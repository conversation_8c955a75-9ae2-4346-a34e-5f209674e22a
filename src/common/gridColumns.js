const getColByKeys = (grdCol, showKeys) => {
  if (showKeys.includes(grdCol.key)) {
    return grdCol
  } else if (Array.isArray(grdCol.children) && grdCol.children.length > 0) {
    let childrenArr = []
    let tmpCol
    for (let sCol of grdCol.children) {
      tmpCol = getColByKeys(sCol, showKeys)
      if (tmpCol !== null) {
        childrenArr.push(tmpCol)
      }
    }
    if (childrenArr.length > 0) {
      grdCol.children = childrenArr
      return grdCol
    }
  }
  return null
}

export const getColumnsByConfig = (gridColumns, config) => {
  let resultArr = []
  if (Array.isArray(config) && config.length > 0 && Array.isArray(gridColumns) && gridColumns.length > 0) {
    let tmpCol
    for (let col of gridColumns) {
      tmpCol = getColByKeys(col, config)
      if (tmpCol !== null) {
        resultArr.push(tmpCol)
      }
    }
  }
  return resultArr
}

const getExcelColByKeys = (grdCol, showKeys, parentTitle) => {
  if (showKeys.includes(grdCol.key)) {
    if (parentTitle && parentTitle.trim().length > 0) {
      return [{'key': grdCol.key, 'value': parentTitle.trim() + '' + grdCol.title}]
    }
    return [{'key': grdCol.key, 'value': grdCol.title}]
  } else if (Array.isArray(grdCol.children) && grdCol.children.length > 0) {
    let childrenArr = []
    let tmpCol
    for (let sCol of grdCol.children) {
      tmpCol = getExcelColByKeys(sCol, showKeys, grdCol.title)
      childrenArr = childrenArr.concat(tmpCol)
    }
    return childrenArr
  }
  return []
}

export const getExcelColumnsByConfig = (gridColumns, excelConfig) => {
  let resultArr = []
  if (Array.isArray(excelConfig) && excelConfig.length > 0 && Array.isArray(gridColumns) && gridColumns.length > 0) {
    let tmpColArr
    for (let col of gridColumns) {
      tmpColArr = getExcelColByKeys(col, excelConfig, '')
      resultArr = resultArr.concat(tmpColArr)
    }
  }
  return resultArr
}
