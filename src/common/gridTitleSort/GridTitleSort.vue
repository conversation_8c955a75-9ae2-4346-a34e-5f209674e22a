<template>
  <div style="color: black;">
    <div style="display: flex; justify-content: left; width: 100%;">
      <div class="colName" @click="handleClickTitleChar">
        {{ colNameP }}
      </div>
      <div class="wrapperA" style="margin-left: 6px;">
        <div class="two" style="margin-top: 2px;" @click="this.handleClickTitleUp">
          <!--  上   -->
          <div class="arrow-up" :style="{ borderBottomColor: this.upColor }"></div>
        </div>
        <div class="three" @click="this.handleClickTitleDown">
          <!--  下   -->
          <div class="arrow-down" :style="{ borderTopColor: this.downColor }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GridTitleSort',
  props: {
    /**
     * 显示 的 文本内容
     */
    colNameP: {
      type: String
    },
    /**
     * 点击列 的 排序信息 默认的可以在这里设置
     */
    sortObjDefP: {
      type: String
    },
    /**
     * 当前 被加载列 id
     */
    colIdP: {
      type: String
    }
  },
  data() {
    return {
      // colId: this.colIdP,    //当前 组件的 id
      upColor: '',
      sortType: '',             //当前字段的 排序类型
      downColor: '',
      sortObjDefArray: [],
      colorSelect: '#2d8cf0',   //颜色管理
      colorCancel: '#c5c8ce'    //颜色管理
    }
  },
  mounted() {
    this.handleLoadSortObjDef()
  },
  methods: {
    /**
     * 根据 sortType 重绘组件
     */
    handleDrawIconBySortType() {
      let me = this
      switch (me.sortType) {
        case 'asc' :      //上
          me.upColor = me.colorSelect
          me.downColor = me.colorCancel
          break
        case 'desc' :     //下
          me.upColor = me.colorCancel
          me.downColor = me.colorSelect
          break
        default:          //取消
          me.upColor = me.colorCancel
          me.downColor = me.colorCancel
          break
      }
    },
    /**
     * 点击文字    正序 倒序  空
     */
    handleClickTitleChar() {
      let me = this

      if (me.sortType === 'asc') {
        me.sortType = 'desc'
      } else if (me.sortType === 'desc') {
        me.sortType = ''
      } else if (me.sortType === '') {
        me.sortType = 'asc'
      }
      me.handleDrawIconBySortType()
      me.handleEmitClick()
    },
    /**
     * 点击升序按钮
     */
    handleClickTitleUp() {
      let me = this
      if (me.sortType === 'asc') {
        me.sortType = ''
      } else {
        me.sortType = 'asc'
      }
      me.handleDrawIconBySortType()
      me.handleEmitClick()
    },
    /**
     * 点击升序按钮
     */
    handleClickTitleDown() {
      let me = this
      if (me.sortType === 'desc') {
        me.sortType = ''
      } else {
        me.sortType = 'desc'
      }
      me.handleDrawIconBySortType()
      me.handleEmitClick()
    },
    /**
     *
     */
    handleEmitClick() {
      let me = this,
        sortObj = {
          sortCol: me.colIdP,
          sortType: me.sortType
        }
      me.$emit('handleEmitClick', sortObj)
    },
    /**
     * 加载默认的排序的 列的信息
     */
    handleLoadSortObjDef() {
      let me = this
      if (!me.sortObjDefP) {
        return
      }
      let sortObjDefArrayMe = me.sortObjDefP.split(',')
      me.sortObjDefArray = sortObjDefArrayMe.map(function(x) {
        let sortCol = x.split(';')[0]
        let sortType = x.split(';')[1]
        return {
          sortCol: sortCol,
          sortType: sortType
        }
      })
      if (!me.sortObjDefArray || !me.sortObjDefArray.length) {
        return
      }
      let isSort = me.sortObjDefArray.filter(x => x.sortCol === me.colIdP)
      if (!isSort || !isSort.length) {
        return
      }
      me.sortType = isSort[0].sortType
      me.handleDrawIconBySortType()
    }
  }
}
</script>

<style scoped>
div.arrow-up {
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-bottom: 5px solid #c5c8ce;
  /* bottom, add background color here */
  border-left: 4px solid transparent;
  /* left arrow slant */
  border-right: 4px solid transparent;
  /* right arrow slant */
}

/* create an arrow that points down */
div.arrow-down {
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-top: 5px solid #c5c8ce;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
}

.wrapperA {
  display: grid;
  grid-gap: 1px;
  /*width: 100%;*/
  grid-template-columns: repeat(1, 1fr);
  /*grid-auto-rows: minmax(5px, auto);*/
}

.colName {
  width: auto;
  display: flex;
  flex: 1 1 auto;
  overflow: hidden;
  align-items: center;
  align-self: stretch;
  text-overflow: ellipsis;
}

.two {
  grid-row: 1;
  grid-column: 1;
  /*height: 9px;*/
  /*text-align: left;*/
  /*line-height: 12px;*/
}

.three {
  grid-row: 2;
  grid-column: 1;
  /*height: 15px;*/
  /*line-height: 9px;*/
}
</style>
