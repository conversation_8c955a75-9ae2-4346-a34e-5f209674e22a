const startsWith = (origin, str) => {
  if (str === null || str === '' || origin.length === 0 || str.length > origin.length)
    return false
  return origin.substr(0, str.length) === str
}

const array_contains = (array, obj) => {
  for (let i = 0; i < array.length; i++) {
    if (array[i] === obj)//如果要求数据类型也一致，这里可使用恒等号===
      return true
  }
  return false
}

/**
 * 使用时，注意，需要添加 area_*的容器  如<div ref="area_head">  ，mount方法中需要添加 this.refreshDynamicHeight()
 */
export default {
  data() {
    return {
      dynamicHeight: 250
    }
  },
  methods: {
    refreshDynamicHeight(salt, hiddenRefs) {
      this.$nextTick(() => {
        // 盐值默认为98 ，实际为外框高度之和 70 + 28
        salt = salt || 98
        const refs = this.$refs
        let totalHeight = 0
        for (let key in refs) {
          let ignore = false
          if (hiddenRefs && hiddenRefs.length) {
            for (let i = 0; i < hiddenRefs.length; i++) {
              if (array_contains(hiddenRefs, key)) {
                ignore = true
                break
              }
            }
          }
          if (ignore)
            continue
          if (startsWith(key, 'area_')) {
            if (refs[key].getBoundingClientRect) {
              totalHeight += refs[key].getBoundingClientRect().height
              if (key === "area_search") {
                totalHeight += 5
              }
            }
          }
        }
        let dHeight = window.innerHeight - totalHeight - salt - 1 + 45
        this.dynamicHeight = dHeight > 200 ? dHeight : 200
      })
    }
  }
}
