/**
 * 判断打开的标签列表里是否已存在这个新添加的路由对象
 */
export const routeHasExist = (tagNavList, routeItem) => {
  let len = tagNavList.length
  let res = false
  doCustomTimes(len, (index) => {
    if (routeEqual(tagNavList[index], routeItem))
      res = true
  })
  return res
}

/**
 * @param {Array} list 标签列表
 * @param {String} route 当前关闭的路由
 */
export const getNextRoute = (list, route) => {
  let res = {}
  if (list.length === 2) {
    res = getHomeRoute(list)
  } else {
    const index = list.findIndex(item => routeEqual(item, route))
    if (index === list.length - 1)
      res = list[list.length - 2]
    else
      res = list[index + 1]
  }
  return res
}

/**
 * @param {Number} times 回调函数需要执行的次数
 * @param {Function} callback 回调函数
 */
export const doCustomTimes = (times, callback) => {
  let i = -1
  while (++i < times) {
    callback(i)
  }
}

/**
 * @param {Array} routers 路由列表数组
 * @param homeName home节点
 * @description 用于找到路由列表中name为home的对象
 */
export const getHomeRoute = (routers, homeName = 'home') => {
  let i = -1
  let len = routers.length
  let homeRoute = {}
  while (++i < len) {
    let item = routers[i]
    if (item.children && item.children.length) {
      let res = getHomeRoute(item.children, homeName)
      if (res.name)
        return res
    } else {
      if (item.name === homeName)
        homeRoute = item
    }
  }
  return homeRoute
}

/**
 * @param {*} list 现有标签导航列表
 * @param {*} newRoute 新添加的路由原信息对象
 * @description 如果该newRoute已经存在则不再添加
 */
export const getNewTagList = (list, newRoute) => {
  const {name, path, meta} = newRoute
  let newList = [...list]
  if (newList.findIndex(item => item.name === name) >= 0)
    return newList
  else
    newList.push({name, path, meta})
  return newList
}

/**
 *
 * @param route
 * @param homeRoute
 * @returns {[(*&{icon: *})]|*[]}
 */
export const getBreadCrumbList = (route, homeRoute) => {
  let homeItem = {
    ...homeRoute,
    icon: homeRoute.meta.icon
  }
  let routeMatched = route.matched
  if (routeMatched.some(item => item.name === homeRoute.name))
    return [homeItem]
  let res = routeMatched.filter(item => {
    return item.meta === undefined || !item.meta.hideInBread
  }).map(item => {
    let meta = {
      ...item.meta
    }
    if (meta.title && typeof meta.title === 'function') {
      meta.__titleIsFunction__ = true
      meta.title = meta.title(route)
    }
    return {
      meta: meta,
      name: item.name,
      icon: (item.meta && item.meta.icon) || ''
    }
  })
  res = res.filter(item => {
    return !item.meta.hideInMenu
  })
  return [...res]
}

/**
 * @description 根据name/params/query判断两个路由对象是否相等
 * @param {*} route1 路由对象
 * @param {*} route2 路由对象
 */
export const routeEqual = (route1, route2) => {
  const params1 = route1.params || {}
  const params2 = route2.params || {}
  const query1 = route1.query || {}
  const query2 = route2.query || {}
  return (route1.name === route2.name) && objEqual(params1, params2) && objEqual(query1, query2)
}

/**
 * @param {*} obj1 对象
 * @param {*} obj2 对象
 * @description 判断两个对象是否相等，这两个对象的值只能是数字或字符串
 */
export const objEqual = (obj1, obj2) => {
  const keysArr1 = Object.keys(obj1)
  const keysArr2 = Object.keys(obj2)
  if (keysArr1.length !== keysArr2.length)
    return false
  else if (keysArr1.length === 0 && keysArr2.length === 0)
    return true
  /* eslint-disable-next-line */
  else
    return !keysArr1.some(key => obj1[key] !== obj2[key])
}

export const hasChild = (item) => {
  return item.children && item.children.length !== 0
}

const showThisMenuEle = (item, access) => {
  if (item.meta && item.meta.access && item.meta.access.length) {
    return !!hasOneOf(item.meta.access, access)
  }
  return true
}

/**
 *
 * @param list 通过路由列表得到菜单列表
 * @param access
 * @returns {[]}
 */
export const getMenuByRouter = (list, access) => {
  let res = []
  if (!list) {
    return res
  }
  list.forEach(item => {
    if (!item.meta || (item.meta && !item.meta.hideInMenu)) {
      let obj = {
        icon: (item.meta && item.meta.icon) || '',
        name: item.name,
        meta: item.meta
      }
      if ((hasChild(item) || (item.meta && item.meta.showAlways)) && showThisMenuEle(item, access)) {
        obj.children = getMenuByRouter(item.children, access)
      }
      if (item.meta && item.meta.href)
        obj.href = item.meta.href
      if (showThisMenuEle(item, access))
        res.push(obj)
    }
  })
  return res
}

export const findNodeUpperByClasses = (ele, classes) => {
  let parentNode = ele.parentNode
  if (parentNode) {
    let classList = parentNode.classList
    if (classList && classes.every(className => classList.contains(className))) {
      return parentNode
    } else {
      return findNodeUpperByClasses(parentNode, classes)
    }
  }
}

export const showTitle = (item) => {
  let {title} = item.meta
  if (!title)
    return
  title = (item.meta && item.meta.title) || item.name
  return title
}

export const getRouteTitleHandled = (route) => {
  let router = {
    ...route
  }
  let meta = {
    ...route.meta
  }
  let title = ''
  if (meta.title) {
    if (typeof meta.title === 'function') {
      meta.__titleIsFunction__ = true
      title = meta.title(router)
    } else title = meta.title
  }
  meta.title = title
  router.meta = meta
  return router
}

/**
 * @description 根据当前跳转的路由设置显示在浏览器标签的title
 * @param {Object} routeItem 路由对象
 * @param {Object} vm Vue实例
 */
export const setTitle = (routeItem, vm) => {
  const handledRoute = getRouteTitleHandled(routeItem)
  const pageTitle = showTitle(handledRoute, vm)
  window.document.title = pageTitle ? `${title} - ${pageTitle}` : title
}

/**
 * @description 本地存储和获取标签导航列表
 */
export const setTagNavListInStorage = list => {
  sessionStorage.tagNaveList = JSON.stringify(list)
}

/**
 * @returns {Array} 其中的每个元素只包含路由原信息中的name, path, meta三项
 */
export const getTagNavListFromStorage = () => {
  const list = sessionStorage.tagNaveList
  return list ? JSON.parse(list) : []
}

export const getHttpHeaderFileName = (headers) => {
  let filename = ''
  if (!headers) {
    return filename
  }
  const disposition = headers['content-disposition']
  if (disposition && disposition.indexOf('attachment') !== -1) {
    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    let matches = filenameRegex.exec(disposition);
    if (matches && matches[1]) {
      filename = matches[1].replace(/['"]/g, '')
      return decodeURIComponent(filename)
    }
  }
  return filename
}

export const blobSaveFile = (blob, fileName) => {
  const objectUrl = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  document.body.appendChild(a)
  a.style = "display: none"
  a.href = objectUrl
  a.download = fileName
  a.click()
  document.body.removeChild(a)
}

/**
 * 自定义非空校验
 * @param rule
 * @param value
 * @param callback
 */
export const nullOrEmptyValid = (rule, value, callback) => {
  if (value === null) {
    callback(new Error(' '))
  } else if (typeof value === "undefined") {
    callback(new Error(' '))
  } else if (value.toString().trim().length === 0) {
    callback(new Error(' '))
  } else {
    callback()
  }
}

/**
 * 判断输入值是否为空
 * @param val
 * @returns {boolean}
 */
export const isNullOrEmpty = (val)=> {
  if (val === null) {
    return true
  }
  if (typeof val === 'undefined') {
    return true
  }
  if (val instanceof Date) {
    return false
  }
  if (typeof val === 'object' && Object.keys(val).length === 0) {
    return true
  }
  return String(val).trim().length === 0
}

/**
 * 判断输入值是否为数字
 * @param val
 * @returns {boolean}
 */
export const isNumber = (val) => {
  if (isNullOrEmpty(val)) {
    return false
  }
  if (typeof val === 'number') {
    return true
  }
  // let re = /^[0-9]+.?[0-9]*$/
  // return re.test(val) && !isNaN(val)
  return !isNaN(val)
}

/**
 * 四舍五入
 * @param originalVal
 * @param precision
 */
export const keepDecimal = (originalVal, precision) => {
  if (isNumber(originalVal) && isNumber(precision)) {
    let tmpPrecision = 1
    for (let i = 0; i < precision; i++) {
      tmpPrecision = tmpPrecision * 10
    }
    return Math.round(Number(originalVal) * tmpPrecision) / tmpPrecision
  }
  return originalVal
}

/**
 * 数字范围校验
 * @param rule
 * @param value
 * @param callback
 */
export const numberRangeValid = (rule, value, callback) => {
  let minVal = rule.min
  let maxVal = rule.max
  if (isNumber(value)) {
    if (isNumber(minVal) && value < minVal) {
      callback(new Error('不得小于' + minVal))
    } else if (isNumber(maxVal) && value > maxVal) {
      callback(new Error('不得大于' + maxVal))
    } else {
      callback()
    }
  } else {
    // callback(new Error('不是数字格式'))
    callback()
  }
}

/**
 * 将数据源的key转为小写
 * @param orgData
 * @returns {*}
 * @constructor
 */
export const ArrayToLocaleLowerCase = (orgData) => {
  if (Array.isArray(orgData)) {
    return orgData.map(item => {
      return {
        label: item['LABEL'],
        value: item['VALUE']
      }
    })
  }
  return []
}

/**
 * 将数据源的key转为小写
 * @param orgData
 * @returns {*}
 * @constructor
 */
export const ArrayToLocaleLowerCaseAll = (orgData) => {
  let result = []
  if (Array.isArray(orgData)) {
    orgData.forEach(item => {
      if (typeof item === 'object') {
        let tmpObj = {}
        Object.keys(item).forEach(key => {
          tmpObj[key.toLocaleLowerCase()] = item[key]
        })
        result.push(tmpObj)
      } else if (!isNullOrEmpty(item)) {
        result.push(item)
      }
    })
  }
  return result
}

/**
 * 根据数据源及key值获取【key value】
 * @param source
 * @param key
 * @param full
 * @returns {*}
 */
export const getKeyValue = (source, key, full)=> {
  if (full !== false) {
    full = true
  }
  if (!isNullOrEmpty(key)) {
    if (Array.isArray(source) && source.length > 0) {
      let theArr = source.filter(item => {
        return item.value === key
      })
      if (Array.isArray(theArr) && theArr.length > 0) {
        if (full) {
          return key + ' ' + theArr[0].label
        } else {
          return theArr[0].label
        }
      } else {
        return key
      }
    } else if (typeof source === 'object') {
      if (source.hasOwnProperty(key)) {
        if (full) {
          return key + ' ' + source[key]
        } else {
          return source[key]
        }
      } else {
        return key
      }
    } else if (typeof source === 'string') {
      if (!isNullOrEmpty(source)) {
        if (full) {
          return key + ' ' + source
        } else {
          return source
        }
      } else {
        return key
      }
    } else {
      return key
    }
  }
  return ''
}

const constantCame = {
  'gname': 'g_name',
  'gmark': 'g_mark',
  'gmodel': 'g_model',
  'iemark': 'i_e_mark',
  'iedate': 'i_e_date'
}

export const snakeCase = (camelCase) => {
  if (!camelCase) return
  if (constantCame.hasOwnProperty(camelCase)) {
    return constantCame[camelCase]
  }
  return camelCase.split(/(?=[A-Z])/).join('_').toUpperCase()
}

export const addEvent = (el, eventName, fn) => {
  if (el.addEventListener) {
    el.addEventListener(eventName, fn, false)
  } else if (el.attachEvent) {
    el.attachEvent('on' + eventName, fn)
  } else {
    el['on' + eventName] = fn
  }
}

/* 代码废弃 2019-4-3 lixy
const rootCls = 'xdo-enter-root'
const formCls = 'xdo-enter-form'
const itemContainerCls = 'ivu-form-item'
const actionContainerCls = 'xdo-enter-action'

const getParentNode = (n,c) =>{
  if (n === document) {
    return null
  }
  let p = n.parentNode
  if (containsClass(p,c)) {
    return p
  }
  return getParentNode(p,c)
}

const findEnableChildNode = (n) =>{
  let inputs = n.getElementsByTagName("input")
  if (inputs.length === 0) {
    return null
  }
  for(let i = 0;i<inputs.length;i++) {
    if ((inputs[i].type != "text" && inputs[i].type != "search" && !containsClass(inputs[i],'ivu-input-number-input'))
      || inputs[i].disabled || inputs[i].readonly || inputs[i].style.display =="none") {
      continue
    }
    return inputs[i]
  }
  return null
}

const containsClass = (elem, className) => {
  if (!elem.className) {
    return false
  }
  let classes = elem.className.split(/\s+/)
  for (let i = 0; i < classes.length; i++) {
    if (classes[i] === className) {
      return true
    }
  }
  return false
}

let getSaveButton = (r) => {
  let dcActionContainer = r.getElementsByClassName(actionContainerCls)
  if (dcActionContainer.length === 0) {
    return null
  }
  let buttons = dcActionContainer[0].getElementsByTagName("button")
  for (let j = 0;j<buttons.length;j++) {
    if (buttons[j].disabled || buttons[j].style.display =="none") {
      continue
    }
    return buttons[j]
  }
  return null
}

export const enterNextFocus = (e) => {
  if (e.keyCode === 13) {
    let eleInput = e.target

    if (eleInput.type != "text" && eleInput.type != "search" && !containsClass(eleInput,'ivu-input-number-input')) {
      return
    }
    let eleParent = getParentNode(eleInput,itemContainerCls)
    if (!eleParent) {
      return
    }
    let eleForm = eleParent.parentNode
    if (!containsClass(eleForm,formCls)) {
      return
    }
    let eleParentNext = eleParent.nextSibling
    let hasNext = false
    while (eleParentNext) {
      let nextInput = findEnableChildNode(eleParentNext)
      if (nextInput) {
        setTimeout(() =>{
          nextInput.focus()
        },200)

        hasNext = true
        break
      }
      eleParentNext = eleParentNext.nextSibling
    }
    if (hasNext) {
      return
    }

    setTimeout(() =>{
      eleInput.blur()
    },200)

    let xdoEnterRoot = getParentNode(eleForm,rootCls)
    if (!xdoEnterRoot) {
      return null
    }

    let eleButton = getSaveButton(xdoEnterRoot)
    if (eleButton) {
      setTimeout(() =>{
        eleButton.click()
      },200)
    }
  }
}
*/
