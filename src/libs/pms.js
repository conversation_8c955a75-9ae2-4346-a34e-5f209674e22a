const { mapActions } = majesty.Vuex

export default {
  data() {
    return {}
  },
  methods: {
    ...mapActions([
      'getFunctions'
    ]),
    async loadFunctions(group = 'default', height = 112) {
      const param = {resId: this.$route.path, group}
      const res = await this.getFunctions(param)
      this.actions = res
      if (this.refreshDynamicHeight) {
        this.refreshDynamicHeight(height, !this.showSearch ? ['area_search'] : null)
      }
      return res
    },
    handleToolbarClick(command) {
      if (this.toolbarEventMap[command] && typeof this.toolbarEventMap[command === 'function']) {
        this.toolbarEventMap[command]()
      } else {
        console.warn(`${command}没有对应的处理方法`)
      }
    },
    setToolbarLoading(command, val = false) {
      const commandAction = this.actions.find(it => it.command === command)
      if (commandAction) {
        commandAction.loading = val
      }
    },
    setToolbarProperty(command, property, val = false) {
      const commandAction = this.actions.find(it => it.command === command)
      if (commandAction && commandAction.hasOwnProperty(property)) {
        commandAction[property] = val
      }
    }
  }
}
