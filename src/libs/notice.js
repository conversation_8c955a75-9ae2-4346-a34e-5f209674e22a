export const singleSelect = (selectData, message) => {
  if (selectData.length > 0) {
    if (selectData.length > 1) {
      message.warning('请选择一条数据操作!')
    } else {
      return true
    }
  } else {
    message.warning('请至少选择一条数据操作!')
  }
  return false
}

export const multiSelect = (selectData, message) => {
  if (selectData.length > 0) {
    return true
  } else {
    message.warning('请至少选择一条数据操作!')
  }
  return false
}
