/**
 *  按键对应的keyCode值
 *  0-9 为（48 - 57）
 *  负号[-] 为 45
 *  点号[.] 为 46
 */

/***
 * 是否是数字键码
 * @param key 键码
 * @param canMinus 能否输入负号，默认不能
 * @returns {boolean} 是否是数字键码
 */
export const isNumberKey = (key, canMinus) => {
  if (!canMinus) {
    return key >= 48 && key <= 57
  }
  return key >= 48 && key <= 57 || key === 45
}

/***
 * 是否是小数键码
 * @param key 键码
 * @param canMinus 能否输入负号，默认不能
 * @returns {boolean} 是否是小数键码
 */
export const isDecimalKey = (key, canMinus) => {
  if (!canMinus) {
    return key >= 48 && key <= 57 || key === 46
  }
  return key >= 48 && key <= 57 || key === 46 || key === 45
}

const getKeyCode = (e) => {
  switch (e.key) {
    case '0':
      return 48;
    case '1':
      return 49;
    case '2':
      return 50;
    case '3':
      return 51;
    case '4':
      return 52;
    case '5':
      return 53;
    case '6':
      return 54;
    case '7':
      return 55;
    case '8':
      return 56;
    case '9':
      return 57;
    case '.':
      return 46;
    case '-':
      return 45;
    default:
      return e.keyCode;
  }
}

export const numberKeypress = (event, canMinus) => {
  let key = getKeyCode(event)
  let currentValue = event.target.value
  let hasMinus = currentValue.indexOf('-') > -1

  if (hasMinus && key === 45) {
    event.returnValue = false
    return
  }
  if (!isNumberKey(key, canMinus)) {
    event.returnValue = false
  }
}

export const numberKeyup = (event, intLength, canMinus) => {
  let key = getKeyCode(event)
  let currentValue = event.target.value
  let hasMinus = currentValue.indexOf('-') > -1

  if (hasMinus && key === 45) {
    event.returnValue = false
    return
  }

  if (!isNumberKey(key, canMinus)) {
    event.returnValue = false
    return
  }

  if (!intLength) {
    intLength = 13
  }

  if (currentValue.startsWith('-')) {
    currentValue = currentValue.substr(1)
  }

  if (currentValue === '') {
    return
  }

  if (currentValue.length > intLength) {
    event.returnValue = false
    event.target.value = currentValue.substr(0, intLength)
  }
}


export const decimalKeypress = (event, canMinus) => {
  let key = getKeyCode(event)
  let currentValue = event.target.value
  let hasDot = currentValue.indexOf('.') > -1
  let hasMinus = currentValue.indexOf('-') > -1

  if (hasDot && key === 46) {
    event.returnValue = false
    return
  }
  if (hasMinus && key === 45) {
    event.returnValue = false
    return
  }
  if (!isDecimalKey(key, canMinus)) {
    event.returnValue = false
  }
}

export const decimalKeyup = (event, precision, intLength, canMinus) => {
  let key = getKeyCode(event)
  let currentValue = event.target.value
  let hasDot = currentValue.indexOf('.') > -1
  let hasMinus = currentValue.indexOf('-') > -1

  if (hasDot && key === 46) {
    event.returnValue = false
    return
  }
  if (hasMinus && key === 45) {
    event.returnValue = false
    return
  }
  if (!isDecimalKey(key, canMinus)) {
    event.returnValue = false
    return
  }
  if (key === 46) {
    return
  }

  if (!precision) {
    precision = 5
  }
  if (!intLength) {
    intLength = 13
  }

  if (currentValue.startsWith('-')) {
    currentValue = currentValue.substr(1)
  }
  if (currentValue === '') {
    return
  }

  let strInt = hasDot ? currentValue.substr(0, currentValue.indexOf('.')) : currentValue
  if (strInt.length > intLength) {
    event.returnValue = false
    event.target.value = strInt.substr(0, intLength) + (hasDot ? currentValue.substr(currentValue.indexOf('.')) : "")
    return
  }

  if (!hasDot && strInt.length <= intLength) {
    return
  }

  let strPrecision = currentValue.substr(currentValue.indexOf('.') + 1)
  if (strPrecision.length > precision) {
    event.returnValue = false
    event.target.value = currentValue.substr(0, currentValue.indexOf('.') + 1) + strPrecision.substr(0, precision)
  }
}

/***
 * 修正js的toFixed方法，解决4舍5入保留n位小数的问题
 * @param num 小数值
 * @param d 小数位数
 * @returns {string}
 */
export const newToFixed = (num, d) => {
  let s = num + ""
  let result = ''
  if (!d) d = 0
  if (s.indexOf(".") === -1) {
    s += "."
  }
  s += new Array(d + 1).join("0")
  if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
    s = "0" + RegExp.$2
    let pm = RegExp.$1, a = RegExp.$3.length, b = true
    if (a === d + 2) {
      a = s.match(/\d/g)
      if (parseInt(a[a.length - 1]) > 4) {
        for (let i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1
          if (a[i] === 10) {
            a[i] = 0
            b = i !== 1
          } else {
            break
          }
        }
      }
      s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2")
    }
    if (b) {
      s = s.substr(1)
    }

    result = (pm + s).replace(/\.$/, "")
    return result
  }
  return num + ""
}

export function round(value, decimals) {
  return Number(Math.round(value + "e" + decimals) + 'e-' + decimals).toFixed(decimals)
}

export function roundUp(value, decimals) {
  return Number(Math.ceil(value + "e" + decimals) + 'e-' + decimals).toFixed(decimals)
}

export function roundDown(value, decimals) {
  return Number(Math.floor(value + "e" + decimals) + 'e-' + decimals).toFixed(decimals)
}
