import { isNullOrEmpty, isNumber } from './util'

/**
 * 获取当前值的实际长度(中文占2个字符、英文数字占1个字符)
 * @param value
 * @returns {number}
 */
export const getRealLength = function (value) {
  if (isNullOrEmpty(value)) {
    return 0
  }
  if (value instanceof Date) {
    return 19
  }
  if (isNumber(value)) {
    let valueStr = String(value).trim()
    return valueStr.length
  }
  let trimValue = String(value).trim(),
    charCode = -1,
    realLength = 0,
    len = trimValue.length
  for (let i = 0; i < len; i++) {
    charCode = trimValue.charCodeAt(i)
    if (charCode >= 0 && charCode <= 128) {
      realLength += 1
    } else {
      realLength += 2
    }
  }
  return realLength
}

export const getValuePxLength = function (value) {
  if (isNullOrEmpty(value)) {
    return 0
  }
  if (value instanceof Date) {
    return 104
  }

  let trimValue = String(value).trim(),
    charCode = -1,
    realLength = 0,
    len = trimValue.length
  for (let i = 0; i < len; i++) {
    charCode = trimValue.charCodeAt(i)
    if (charCode >= 32 && charCode <= 126) {
      if (charCode === 77 || 87 === charCode) {
        realLength += 9
      } else {
        realLength += 4
      }
    } else if (charCode >= 0 && charCode <= 128) {
      realLength += 6
    } else {
      realLength += 12
    }
  }
  return realLength
}
