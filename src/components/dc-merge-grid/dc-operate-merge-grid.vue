<template>
  <section style="white-space: nowrap;">
    <div :style="`width: ${agWidth}; float: left; overflow-x: scroll;`">
      <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" :checkboxSelection="checkboxSelection" :height="operateHeight"
                   :rowSelection="rowSelection" :columns="operateColumn" :data="listConfig.data" :components="components"
                   :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                   @selectionChanged="handleSelectionChange"></xdo-ag-grid>
    </div>
    <div :style="`width: ${dataWidth}; float: left; background-color: white;`">
      <dc-merge-grid :height="mergeHeight" :columns="showColumn" :data="listConfig.data" :select-sids="selSids"
                     :in-source="dynamicSource" rowMerge :spanRows="rowSpanConfig.spanRows"
                     show-operate @doRowSelected="doRowSelected"></dc-merge-grid>
    </div>
  </section>
</template>

<script>
  import { isNumber } from '@/libs/util'
  import { baseAgGrid } from '@/mixin/generic/grid/baseAgGrid'
  import { dcAgRowSpan } from '@/components/dc-ag-grid/js/dc-ag-row-span'

  export default {
    name: 'dcOperateMergeGrid',
    mixins: [baseAgGrid, dcAgRowSpan],
    props: {
      height: {
        type: [String, Number],
        default: () => ('-')
      },
      checkboxSelection: {
        type: Boolean,
        default: () => false
      },
      rowSpanConfig: {
        type: Object,
        default: () => ({
          pks: [],
          spanRows: []
        })
      },
      dynamicSource: {
        type: Object,
        default: () => ({})
      },
      parentListConfig: {
        type: Object,
        default: () => ({
          data: [],                        // grid数据
          columns: [],                     // 最终展示的列字段
          disable: false,                  // grid组件是否可编辑
          selectRows: [],                  // 当前选中的行数组
          checkColumnShow: true,           // 显示选择列
          operationColumnShow: true        // 显示操作列
        })
      }
    },
    data() {
      return {
        tableHeight: 33
      }
    },
    watch: {
      'listConfig.selectRows': {
        immediate: true,
        handler: function(rows) {
          let me = this
          me.$emit('selectionChanged', rows)
        }
      },
      'parentListConfig.data': {
        immediate: true,
        handler: function(data) {
          let me = this
          me.$set(me.listConfig, 'data', me.setMerge(data))
          me.grdHeightReset()
        }
      },
      'parentListConfig.columns': {
        immediate: true,
        handler: function(columns) {
          let me = this
          me.$set(me.listConfig, 'columns', columns)
        }
      },
      'parentListConfig.disable': {
        immediate: true,
        handler: function(disable) {
          let me = this
          me.$set(me.listConfig, 'disable', disable)
        }
      },
      'parentListConfig.selectRows': {
        immediate: true,
        handler: function(selRows) {
          let me = this
          me.$set(me.listConfig, 'selectRows', selRows)
        }
      },
      'parentListConfig.operationColumnShow': {
        immediate: true,
        handler: function(show) {
          let me = this
          me.$set(me.listConfig, 'operationColumnShow', show)
        }
      },
      checkboxSelection: {
        immediate: true,
        handler: function(cbs) {
          let me = this
          me.$set(me.listConfig, 'checkColumnShow', cbs)
        }
      }
    },
    computed: {
      agWidth() {
        let me = this
        if (me.checkboxSelection) {
          return '117px'
        } else {
          return '89px'
        }
      },
      dataWidth() {
        let me = this
        if (me.checkboxSelection) {
          return 'calc(100% - 117px)'
        } else {
          return 'calc(100% - 89px)'
        }
      },
      mergeHeight() {
        let me = this
        return me.operateHeight + 17
      },
      operateHeight() {
        let me = this,
          theHeight = 0
        if (isNumber(me.height)) {
          theHeight = me.height - 17
          if (me.tableHeight > theHeight) {
            return me.tableHeight
          }
          return theHeight
        }
        return '-'
      },
      operateColumn() {
        let me = this,
          opColumns = [],
          opColumn = me.parentListConfig.columns.find(col => col.key === 'operation')
        if (opColumn) {
          opColumns.push(opColumn)
        }
        return opColumns
      },
      showColumn() {
        let me = this
        return me.parentListConfig.columns.filter(col => {
          return col.key !== 'operation'
        })
      },
      selSids() {
        let me = this
        return me.listConfig.selectRows.map(row => row.sid)
      }
    },
    methods: {
      handleEditByRow(row) {
        let me = this
        me.$emit('showEditByRow', row)
      },
      handleViewByRow(row) {
        let me = this
        me.$emit('showViewByRow', row)
      },
      doRowSelected(sid) {
        let me = this
        if (me.$refs['table']) {
          let api = me.$refs['table'].gridOptions.api
          if (api) {
            api.forEachNodeAfterFilter(node => {
              if (node.data.sid === sid) {
                node.setSelected(true)
              } else {
                node.setSelected(false)
              }
            })
          }
        }
      },
      grdHeightReset() {
        let me = this
        me.$nextTick(() => {
          if (me.$refs['table']) {
            let container = me.$refs['table'].$el.querySelector('div[class="ag-pinned-left-cols-container"]')
            if (container) {
              if (isNumber(container.offsetHeight)) {
                me.$set(me, 'tableHeight', 35 + container.offsetHeight)
              }
            }
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
