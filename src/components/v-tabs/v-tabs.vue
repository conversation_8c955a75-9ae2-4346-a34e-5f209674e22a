<template>
  <section class="myTab">
    <XdoCard :bordered="bordered" :title="tabTitle" class="ivu-card-Left">
      <Menu :active-name="publicValue" @on-select="onSelect" style="width: 100%;">
        <template v-for="(tab, index) in navList">
          <MenuItem :name="tab.name" :key="index">
            <XdoIcon v-if="tab.icon !== ''" :type="tab.icon"></XdoIcon>
            {{tab.label}}<span v-if="tab.extend !== ''" class="menuItemSlot">{{tab.extend}}</span>
          </MenuItem>
        </template>
      </Menu>
    </XdoCard>
    <XdoCard :bordered="bordered" :title="detailTitle" ref="panes" class="ivu-card-Right">
      <slot></slot>
    </XdoCard>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'verticalTabs',
    props: {
      value: {
        type: [String, Number]
      },
      bordered: {
        type: Boolean,
        default: true
      },
      tabTitle: {
        type: String,
        default: ''
      },
      detailTitle: {
        type: String,
        default: ''
      }
    },
    watch: {
      value: {
        immediate: true,
        handler: function (val) {
          let me = this
          me.$nextTick(() => {
            me.onSelect(val)
          })
        }
      }
    },
    data() {
      return {
        publicValue: '',
        navList: [],
        activeKey: this.value
      }
    },
    methods: {
      getTabs() {
        let panes = this.$refs.panes
        if (panes) {
          let thePans = panes.$children.filter(item => item.$options.name === 'vTabPane')
          let theParents = []
          for (let child of panes.$children) {
            theParents.push(child)
          }
          let tmpParents = []
          while (Array.isArray(thePans) && thePans.length === 0) {
            for (let parent of theParents) {
              thePans = parent.$children.filter(item => item.$options.name === 'vTabPane')
              for (let tmpParent of parent.$children) {
                tmpParents.push(tmpParent)
              }
            }
            theParents = tmpParents
          }
          return thePans
        }
        return []
      },
      updateStatus() {
        let me = this
        me.getTabs().forEach(tab => tab.show = (tab.currentName === me.activeKey))
      },
      updateNav() {
        let me = this
        me.navList = []
        me.getTabs().forEach((pane, index) => {
          me.navList.push({
            label: pane.label,
            extend: pane.extend,
            icon: pane.icon || '',
            name: pane.currentName || index
          })
          if (!pane.currentName)
            pane.currentName = index
          if (index === 0) {
            if (!me.activeKey) {
              me.activeKey = pane.currentName || index
            }
          }
        })
        me.updateStatus()
      },
      getTabIndex(name) {
        return this.navList.findIndex(nav => nav.name === name);
      },
      onSelect(name) {
        let me = this
        if (isNullOrEmpty(name)) {
          if (Array.isArray(me.navList) && me.navList.length > 0) {
            name = me.navList[0].name
          }
        }
        let index = me.getTabIndex(name)
        if (!isNullOrEmpty(name)) {
          me.getTabs().forEach((el, i) => {
            if (index === i) {
              el.$el.style.display = ''
            } else {
              el.$el.style.display = 'none'
            }
          })
        }
        me.$set(me, 'publicValue', name)
        me.$emit('onTabChange', name)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-card {
    margin: 2px 0;
    overflow: hidden;
  }

  .ivu-card-Left {
    border: 1px solid gray;
    border-right: 0 none;
  }

  .ivu-card-Right {
    border: 1px solid gray;
    border-left: 0 none;
  }

  /deep/ .ivu-card-Right .ivu-card-body {
    border-top: 1px solid #a8adb7;
  }

  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }

  .menuItemSlot {
    float: right;
    padding-right: 20px;
  }

  .myTab {
    display: grid;
    grid-template-columns: 260px auto;
  }

  .ivu-menu-item {
    border-top: 1px solid #a8adb7;
  }

  /deep/ .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
    color: #ffffff;
    background: #fb7360;
    border-right: 1px solid #fb7360;
    /*border-bottom: 1px solid white;*/
    /*box-shadow: 3px 2px 3px #b71b04 inset;*/
  }

  /deep/ li.ivu-menu-item.ivu-menu-item-active.ivu-menu-item-selected::after {
    background: #fb7360;
  }
</style>
