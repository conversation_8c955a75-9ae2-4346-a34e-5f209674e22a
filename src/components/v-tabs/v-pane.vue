<template>
  <div :class="className"><slot></slot></div>
</template>

<script>
  export default {
    name: 'vTabPane',
    props: {
      name: {
        type: String
      },
      icon: {
        type: String
      },
      label: {
        type: String,
        default: ''
      },
      extend: {
        type: String,
        default: ''
      },
      className: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        currentName: this.name
      }
    },
    methods: {
      updateNav() {
        let theParent = this.$parent
        while (theParent && typeof theParent.updateNav !== "function") {
          theParent = theParent.$parent
        }
        if (theParent) {
          theParent.updateNav()
        }
      }
    },
    watch: {
      name(val) {
        this.currentName = val
        this.updateNav()
      },
      icon() {
        this.updateNav()
      },
      label() {
        this.updateNav()
      },
      extend() {
        this.updateNav()
      }
    },
    mounted() {
      this.updateNav()
    }
  }
</script>

<style lang="less" scoped>
</style>
