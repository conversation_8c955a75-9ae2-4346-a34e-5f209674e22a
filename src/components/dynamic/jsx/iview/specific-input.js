import { isNullOrEmpty } from '@/libs/util'
import { getComponentBy<PERSON>ey } from '../utils/element-creater'

const propsInit = (key, item, theKey, vm, frmRef) => {
  let me = getComponentByKey(vm, frmRef, theKey)
  let props = {
    type: 'text',
    disabled: true,
    autosize: true,
    clearable: true,
    value: item.specificValue.call(me)
  }
  if (item.props) {
    if (['large', 'small', 'default'].includes(item.props.size)) {
      props.size = item.props.size
    }
    if (!isNullOrEmpty(item.props.placeholder)) {
      props.placeholder = item.props.placeholder
    }
    if (item.props.readonly === true) {
      props.readonly = true
    }
    if (!isNullOrEmpty(item.props.icon)) {
      props.icon = item.props.icon
    }
    if (!isNullOrEmpty(item.props.prefix)) {
      props.prefix = item.props.prefix
    }
    if (!isNullOrEmpty(item.props.suffix)) {
      props.suffix = item.props.suffix
    }
  }
  return props
}

const slotsInit = (h, item) => {
  let slots = []
  if (item.slot) {
    slots = Object.keys(item.slot).filter(slot => {
      return ['prepend', 'append'].includes(slot)
    }).map(slot => {
      let slotInfo = item.slot[slot]
      if (typeof slotInfo === 'string') {
        return h('span', {
          slot: slot
        }, slotInfo)
      } else if (typeof slotInfo === 'object') {
        if (slotInfo.hasOwnProperty('type')) {
          let theOptions = {
            slot: slot
          }
          if (slotInfo.props) {
            theOptions.props = slotInfo.props
          }
          if (slotInfo.style) {
            theOptions.style = slotInfo.style
          }
          if (slotInfo.on) {
            theOptions.on = slotInfo.on
          }
          return h(slotInfo.type, theOptions, slotInfo.label)
        }
      }
    })
  }
  return slots
}

export const specificInputComponent = (h, item, vm, frmRef) => {
  const key = item.key
  let props = propsInit(key, item, item.theKey, vm, frmRef)
  let style = {}
  if (item.style) {
    style = item.style
  }
  let slots = slotsInit(h, item)
  return h('Input', {
    key: key,
    style: style,
    props: props
  }, slots)
}
