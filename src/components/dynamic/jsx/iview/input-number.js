import { isNumber, isNullOrEmpty } from '@/libs/util'
import { getComponentByKey } from '../utils/element-creater'

const valueValidation = (props, val, vm, key) => {
  let precision = Number(props.precision)
  if (!isNullOrEmpty(val)) {
    val = String(val).trim()
  } else {
    val = ''
  }
  let chars = val.split('')
  let intValStr = ''
  let intContinue = true
  let preValStr = ''
  let preContinue = true

  vm.inputNumberData[key].hasDecimalPoint = false
  for (let i = 0; i < chars.length; i++) {
    let itemChar = chars[i]
    if (i === 0) {
      if (props.negative && itemChar === '-') {
        intValStr = '-'
      } else if (itemChar === '.') {
        intValStr = '0'
        vm.inputNumberData[key].hasDecimalPoint = true
      } else {
        if (isNumber(itemChar)) {
          intValStr = itemChar
        }
      }
    } else {
      if (itemChar === '.') {
        if (!vm.inputNumberData[key].hasDecimalPoint) {
          vm.inputNumberData[key].hasDecimalPoint = true
        }
      } else {
        if (isNumber(itemChar)) {
          if (vm.inputNumberData[key].hasDecimalPoint && precision > 0) {
            if (preContinue) {
              preValStr += itemChar
            }
          } else {
            if (intContinue) {
              intValStr += itemChar
            }
          }
        }
      }
    }
    if (intValStr.replace('-', '').trim().length === Number(props.intDigits)) {
      intContinue = false
    }
    if (precision > 0 && preValStr.trim().length === precision) {
      preContinue = false
    }
  }
  if (precision > 0) {
    if (vm.inputNumberData[key].hasDecimalPoint) {
      if (isNumber(preValStr)) {
        return intValStr + '.' + preValStr
      } else {
        return intValStr + '.'
      }
    }
  }
  return intValStr
}

const canEdit = (props) => {
  if (props.disabled) {
    return false
  }
  return !props.readonly
}

const isValidNumber = (key, val) => {
  if (!isNullOrEmpty(key)) {
    if (isNumber(val)) {
      let valStr = val.trim()
      if (valStr.indexOf('.') === -1) {
        return true
      }
      if (valStr.indexOf('.') + 1 < valStr.length) {
        return true
      }
    }
  }
  return false
}

const changeStep = (type, val, props) => {
  if (!['up', 'down'].includes(type)) {
    return false
  }
  let originalVal = 0
  if (isNumber(val)) {
    originalVal = Number(val)
  }
  let step = props.step
  if (!isNumber(step)) {
    step = 1
  }
  let tmpVal = 0
  if (type === 'up') {
    tmpVal = originalVal + step
  } else {
    tmpVal = originalVal - step
  }
  if (tmpVal <= props.minVal) {
    return props.minVal
  } else if (tmpVal >= props.maxVal) {
    return props.maxVal
  } else {
    let tmpValStr = String(tmpVal)
    let values = tmpValStr.split('.')
    let precision = Number(props.precision)
    if (values.length === 2 && values[1].trim().length > precision) {
      return Number(tmpVal.toFixed(precision))
    } else {
      return tmpVal
    }
  }
}

const propsExtendInit = (props) => {
  let maxLength = Number(props.intDigits)
  let maxIntVal = 0
  for (let i = 0; i < maxLength; i++) {
    maxIntVal += 9 * Math.pow(10, i)
  }
  let maxPrecisionVal = 0
  let precision = Number(props.precision)
  if (precision > 0) {
    maxLength += 1 + precision
    for (let p = 0; p < precision; p++) {
      maxPrecisionVal += 9 * Math.pow(10, p)
    }
    maxPrecisionVal = Number('0.' + maxPrecisionVal)
  }
  props.maxLength = maxLength
  props.maxVal = maxIntVal + maxPrecisionVal
  if (props.negative) {
    props.minVal = (0 - props.maxVal)
  } else {
    props.minVal = 0
  }
  props.hasDecimalPoint = false
  return props
}

const propsInit = (key, item, model) => {
  let props = {
    step: 1,
    precision: 0,
    type: 'text',
    negative: false,
    disabled: false,
    clearable: true,
    intDigits: 100,
    value: isNumber(model[key]) ? Number(model[key]).toString() : ''
  }
  if (item.props) {
    if (['large', 'small', 'default'].includes(item.props.size)) {
      props.size = item.props.size
    }
    if (!isNullOrEmpty(item.props.placeholder)) {
      props.placeholder = item.props.placeholder
    }
    if ([true, false].includes(item.props.clearable)) {
      props.clearable = item.props.clearable
    }
    if (item.props.disabled === true) {
      props.disabled = true
    }
    if (item.props.readonly === true) {
      props.readonly = true
    }
    if (isNumber(item.props.maxlength)) {
      props.maxlength = Number(item.props.maxlength)
    }
    if (!isNullOrEmpty(item.props.icon)) {
      props.icon = item.props.icon
    }
    if (!isNullOrEmpty(item.props.prefix)) {
      props.prefix = item.props.prefix
    }
    if (!isNullOrEmpty(item.props.suffix)) {
      props.suffix = item.props.suffix
    }
    if (!isNullOrEmpty(item.props.search)) {
      props.search = item.props.search
    }
    if (item.props['enter-button'] === true || item.props['enterButton'] === true) {
      props['enter-button'] = true
    }
    if (item.props.autosize === true) {
      props.autosize = true
    }
    if (item.props.autofocus === true) {
      props.autofocus = true
    }
    if (item.props.spellcheck === true) {
      props.spellcheck = true
    }
    if (item.props.autocomplete === 'on') {
      props.autocomplete = 'on'
    }
    if (!isNullOrEmpty(item.props['element-id'])) {
      props['element-id'] = item.props['element-id']
    }
    if (!isNullOrEmpty(item.props['elementId'])) {
      props['element-id'] = item.props['elementId']
    }
    if (item.props.negative === true) {
      props.negative = true
    }
    if (isNumber(item.props.step) && Number(item.props.step) > 0) {
      props.step = Number(item.props.step)
    }
    if (isNumber(item.props.intDigits) && Number(item.props.intDigits) > 0
      && item.props.intDigits.toString().indexOf('.') === -1) {
      props.intDigits = Number(item.props.intDigits)
    }
    if (props.intDigits === 100 && isNumber(item.props.integerDigits) && Number(item.props.integerDigits) > 0
      && item.props.integerDigits.toString().indexOf('.') === -1) {
      props.intDigits = Number(item.props.integerDigits)
    }
    if (isNumber(item.props.precision) && Number(item.props.precision) >= 0
      && item.props.precision.toString().indexOf('.') === -1) {
      props.precision = Number(item.props.precision)
    }
  }
  return propsExtendInit(props)
}

const eventsInit = (key, item, props, model, vm, frmRef) => {
  let events = {
    input: (val) => {
      if (isValidNumber(key, val)) {
        model[key] = Number(val)
        let me = getComponentByKey(vm, frmRef, key)
        if (me && typeof me.dispatch === 'function') {
          me.dispatch('FormItem', 'on-form-change', model[key])
        }
      } else if (isNullOrEmpty(val)) {
        model[key] = null
        let me = getComponentByKey(vm, frmRef, key)
        if (me && typeof me.dispatch === 'function') {
          me.dispatch('FormItem', 'on-form-change', model[key])
        }
      }
    },
    'on-change': (e) => {
      vm.$nextTick(() => {
        e.target.value = valueValidation(props, e.target.value, vm, key)
        if (isValidNumber(key, e.target.value)) {
          model[key] = Number(e.target.value)
        } else if (isNullOrEmpty(e.target.value)) {
          model[key] = ''
        }
      })
    },
    'on-keydown': (e) => {
      try {
        if (canEdit(props) && e.keyCode === 38) {
          e.preventDefault()
          model[key] = changeStep('up', e.target.value, props)
        } else if (canEdit(props) && e.keyCode === 40) {
          e.preventDefault()
          model[key] = changeStep('down', e.target.value, props)
        } else {
          vm.$emit('on-keydown', e)
        }
      } catch (e) {
        console.error(e.message)
      }
    },
    'on-blur': (e) => {
      vm.$nextTick(() => {
        setTimeout(function () {
          e.target.value = valueValidation(props, e.target.value, vm, key)
        }, 280)
      })
    }
  }
  if (item.on) {
    if (typeof item.on.enter === 'function') {
      events['on-enter'] = item.on.enter
    }
    if (typeof item.on.click === 'function') {
      events['on-click'] = item.on.click
    }
    if (typeof item.on.focus === 'function') {
      events['on-focus'] = item.on.focus
    }
    if (typeof item.on.keyup === 'function') {
      events['on-keyup'] = item.on.keyup
    }
    if (typeof item.on.keypress === 'function') {
      events['on-keypress'] = item.on.keypress
    }
    if (typeof item.on.search === 'function') {
      events['on-search'] = item.on.search
    }
    if (typeof item.on.clear === 'function') {
      events['on-clear'] = item.on.clear
    }
    // if (typeof item.on.blur === 'function') {
    //   events['on-blur'] = item.on.blur
    // }
  }
  return events
}

/**
 * 扩展初始化
 * @param h
 * @param item
 * @returns {[]}
 */
const slotsInit = (h, item) => {
  let slots = []
  if (item.slot) {
    slots = Object.keys(item.slot).filter(slot => {
      return ['prepend', 'append'].includes(slot)
    }).map(slot => {
      let slotInfo = item.slot[slot]
      if (typeof slotInfo === 'string') {
        return h('span', {
          slot: slot
        }, slotInfo)
      } else if (typeof slotInfo === 'object') {
        if (slotInfo.hasOwnProperty('type')) {
          let theOptions = {
            slot: slot
          }
          if (slotInfo.props) {
            theOptions.props = slotInfo.props
          }
          if (slotInfo.style) {
            theOptions.style = slotInfo.style
          }
          if (slotInfo.on) {
            theOptions.on = slotInfo.on
          }
          return h(slotInfo.type, theOptions, slotInfo.label)
        }
      }
    })
  }
  return slots
}

export const inputNumberComponent = (h, item, model, vm, frmRef) => {
  const key = item.key
  let props = propsInit(key, item, model)
  if (!vm['inputNumberData']) {
    vm.inputNumberData = {}
  }
  vm.inputNumberData[key] = {
    hasDecimalPoint: false
  }
  let style = {}
  if (item.style) {
    style = item.style
  }
  let events = eventsInit(key, item, props, model, vm, frmRef)
  let slots = slotsInit(h, item)

  return h('Input', {
    key: key,
    on: events,
    style: style,
    props: props
  }, slots)
}
