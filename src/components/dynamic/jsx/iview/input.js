import { isNumber, isNullOrEmpty } from '@/libs/util'
import { getComponentByKey } from '../utils/element-creater'

const propsInit = (key, item, model) => {
  let props = {
    type: 'text',
    disabled: false,
    clearable: true,
    value: model[key]
  }
  if (['text', 'password', 'textarea', 'url', 'email', 'date', 'number', 'tel'].includes(item.itemType)) {
    props.type = item.itemType
  }
  if (item.props) {
    if (props.type === 'textarea') {
      if (isNumber(item.props.rows)) {
        props.rows = Number(item.props.rows)
      }
      if (['hard', 'soft'].includes(item.props.wrap)) {
        props.wrap = item.props.wrap
      }
    }
    if (['large', 'small', 'default'].includes(item.props.size)) {
      props.size = item.props.size
    }
    if (!isNullOrEmpty(item.props.placeholder)) {
      props.placeholder = item.props.placeholder
    }
    if ([true, false].includes(item.props.clearable)) {
      props.clearable = item.props.clearable
    }
    if (item.props.disabled === true) {
      props.disabled = true
    }
    if (item.props.readonly === true) {
      props.readonly = true
    }
    if (isNumber(item.props.maxlength)) {
      props.maxlength = Number(item.props.maxlength)
    }
    if (!isNullOrEmpty(item.props.icon)) {
      props.icon = item.props.icon
    }
    if (!isNullOrEmpty(item.props.prefix)) {
      props.prefix = item.props.prefix
    }
    if (!isNullOrEmpty(item.props.suffix)) {
      props.suffix = item.props.suffix
    }
    if (!isNullOrEmpty(item.props.search)) {
      props.search = item.props.search
    }
    if (props.search === true) {
      props['enter-button'] = true
      let enterButton = ''
      if (typeof item.props['enter-button'] === 'string') {
        enterButton = item.props['enter-button']
      } else if (typeof item.props['enterButton'] === 'string') {
        enterButton = item.props['enterButton']
      }
      if (!isNullOrEmpty(enterButton)) {
        props['enter-button'] = enterButton
      }
    }
    if (item.props.autosize === true) {
      props.autosize = true
    }
    if (item.props.number === true) {
      props.number = true
    }
    if (item.props.autofocus === true) {
      props.autofocus = true
    }
    if (item.props.spellcheck === true) {
      props.spellcheck = true
    }
    if (item.props.autocomplete === 'on') {
      props.autocomplete = 'on'
    }
    if (!isNullOrEmpty(item.props['element-id'])) {
      props['element-id'] = item.props['element-id']
    }
    if (!isNullOrEmpty(item.props['elementId'])) {
      props['element-id'] = item.props['elementId']
    }
  }
  return props
}

const eventsInit = (key, item, model, vm, frmRef) => {
  let events = {
    input: (val) => {
      if (key) {
        model[key] = val
        let me = getComponentByKey(vm, frmRef, key)
        if (me && typeof me.dispatch === 'function') {
          me.dispatch('FormItem', 'on-form-change', model[key])
        }
      }
    }
  }
  if (item.on) {
    if (typeof item.on.enter === 'function') {
      events['on-enter'] = item.on.enter
    }
    if (typeof item.on.click === 'function') {
      events['on-click'] = item.on.click
    }
    if (typeof item.on.change === 'function') {
      events['on-change'] = item.on.change
    }
    if (typeof item.on.focus === 'function') {
      events['on-focus'] = item.on.focus
    }
    if (typeof item.on.blur === 'function') {
      events['on-blur'] = item.on.blur
    }
    if (typeof item.on.keyup === 'function') {
      events['on-keyup'] = item.on.keyup
    }
    if (typeof item.on.keydown === 'function') {
      events['on-keydown'] = item.on.keydown
    }
    if (typeof item.on.keypress === 'function') {
      events['on-keypress'] = item.on.keypress
    }
    if (typeof item.on.search === 'function') {
      events['on-search'] = item.on.search
    }
    if (typeof item.on.clear === 'function') {
      events['on-clear'] = item.on.clear
    }
  }
  return events
}

const slotsInit = (h, item)=> {
  let slots = []
  if (item.slot) {
    slots = Object.keys(item.slot).filter(slot => {
      return ['prepend', 'append'].includes(slot)
    }).map(slot => {
      let slotInfo = item.slot[slot]
      if (typeof slotInfo === 'string') {
        return h('span', {
          slot: slot
        }, slotInfo)
      } else if (typeof slotInfo === 'object') {
        if (slotInfo.hasOwnProperty('type')) {
          let theOptions = {
            slot: slot
          }
          if (slotInfo.props) {
            theOptions.props = slotInfo.props
          }
          if (slotInfo.style) {
            theOptions.style = slotInfo.style
          }
          if (slotInfo.on) {
            theOptions.on = slotInfo.on
          }
          return h(slotInfo.type, theOptions, slotInfo.label)
        }
      }
    })
  }
  return slots
}

export const inputComponent = (h, item, model, vm, frmRef) => {
  const key = item.key
  let props = propsInit(key, item, model)
  let style = {}
  if (item.style) {
    style = item.style
  }
  let events = eventsInit(key, item, model, vm, frmRef)
  let slots = slotsInit(h, item)
  return h('Input', {
    key: key,
    on: events,
    style: style,
    props: props
  }, slots)
}
