import cm from '../utils/component-manage'
import { isNullOrEmpty, isNumber } from '@/libs/util'
import { enterToTabSimulation } from '../utils/element-creater'
import { deepClone, getFormItemVueObj } from '../utils/element-creater'

function renderInnerFormItem(vm, frmRef, h, options, frmOptions, component) {
  let auditMark = false,
    $Vue = vm.parent
  if (frmOptions.auditMark === true) {
    if (options.auditMark !== false) {
      auditMark = true
    }
  } else {
    if (options.auditMark === true) {
      auditMark = true
    }
  }
  $Vue.$nextTick(() => {
    let auditData = '0',
      labelBGColor = ['white', 'greenyellow', 'red']
    if (frmOptions.auditData && frmOptions.auditData.hasOwnProperty(options.key)) {
      if (['0', '1', '2'].includes(frmOptions.auditData[options.key])) {
        auditData = frmOptions.auditData[options.key]
      }
    }
    let frmItem = getFormItemVueObj($Vue, frmRef, options.key)
    if (frmItem) {
      let $labels = frmItem.$el.getElementsByClassName('ivu-form-item-label')

      if (!frmOptions.rules.hasOwnProperty(options.key)) {
        if (frmItem.$el.className.indexOf('ivu-form-item-required') > -1) {
          frmItem.$el.className = frmItem.$el.className.replace('ivu-form-item-required', '')
        }
      }

      if ($labels.length > 0) {
        let $label = $labels.item(0)
        if ($label) {
          $label.style.backgroundColor = labelBGColor[auditData]
          if (auditData === '2') {
            $label.style.color = 'white'
          } else {
            $label.style.color = 'black'
          }
          if (auditMark) {
            $label.style.cursor = 'pointer'
            $label.setAttribute('fiKey', options.key)
            if (typeof frmOptions.onAuditChange === 'function') {
              if ($label.addEventListener) {
                $label.addEventListener('click', frmOptions.onAuditChange, false)
              } else if ($label.attachEvent) {
                $label.attachEvent('onclick', frmOptions.onAuditChange)
              } else {
                $label['onclick'] = frmOptions.onAuditChange
              }
            }
          }
        }
      }
    }
  })
  options.ref = 'fi_' + options.key
  if (Array.isArray(component)) {
    return h('FormItem', options, component)
  } else {
    return h('FormItem', options, [component])
  }
}

function renderPreRequired(vm, frmRef, h, item, options, frmOptions, components) {
  let component = [h('span', {
    slot: 'label',
    class: !isNullOrEmpty(item.labelClass) ? item.labelClass : 'preRequired'
  }, item.title), ...components]
  return renderInnerFormItem(vm, frmRef, h, options, frmOptions, component)
}

function renderSpecificComponents(vm, h, item, frmItemOptions, options, func, frmRef, index) {
  let specificItem = {
    slot: deepClone(item.slot),
    theKey: item.key,
    type: 'specificInput',
    key: item.key + '_specific',
    specificValue: item.specificValue,
    props: deepClone(item.props)
  }
  let funcSpecific = cm.components['specificInput'],
    specificComponent = funcSpecific.call(vm, h, specificItem, vm.parent, frmRef, index)

  if (item.style) {
    item.style.display = 'none'
  } else {
    item.style = {
      display: 'none'
    }
  }
  let simplifyItem = deepClone(item)
  delete simplifyItem.slot
  delete simplifyItem.specificValue
  let component = func.call(vm, h, simplifyItem, options.model, vm.parent, frmRef, index)
  let childrenComponents = [specificComponent, component]

  if (item.hasOwnProperty('itemClass')) {
    frmItemOptions.class = item.itemClass
  }
  if (item.hasOwnProperty('itemStyle')) {
    frmItemOptions.style = item.itemStyle
  }

  if (item.requiredReminder === true || item.preRequired === true) {
    return renderPreRequired(vm, frmRef, h, item, frmItemOptions, options, childrenComponents)
  } else {
    return renderInnerFormItem(vm, frmRef, h, frmItemOptions, options, childrenComponents)
  }
}

function renderEmptyFormItem(h, item) {
  let component = null,
    frmItemOptions = {}
  if (item.isCard === true) {
    frmItemOptions = {
      key: item.key,
      props: {
        'label-width': 0
      },
      style: {
        marginBottom: '2px',
        backgroundColor: '#f7f7f7',
        borderTop: '1px solid #e8eaec'
      }
    }
    if (item.hasOwnProperty('itemClass')) {
      if (item.itemClass.indexOf('ivu-card-head') < 0) {
        item.itemClass = item.itemClass + ' ivu-card-head'
      }
    } else {
      item.itemClass = 'ivu-card-head'
    }
    if (item.collapse === true) {
      frmItemOptions.style.cursor = 'pointer'
      if (item.on && typeof item.on.click === 'function') {
        frmItemOptions.nativeOn = {
          click: item.on.click
        }
      }
      let rotate = 'rotate(0deg)'
      if (item.props && item.props.collapse === false) {
        rotate = 'rotate(90deg)'
      }
      component = h('div', {
        class: 'ivu-collapse-item'
      }, [
        h('div', {
          class: 'ivu-collapse-header'
        }, [h('i', {
          style: {
            top: '5px',
            transform: rotate,
            marginRight: '14px',
            position: 'absolute',
            transition: 'transform .2s ease-in-out'
          },
          class: 'ivu-icon ivu-icon-ios-arrow-forward'
        }, []), h('p', {
          style: {
            paddingLeft: '16px'
          }
        }, item.title)])
      ])
    } else {
      component = h('p', item.title)
    }
  } else {
    component = null  // h('p', '占位栏') //
    frmItemOptions = {
      key: item.key,
      props: {
        label: item.title,
        'label-width': item.labelWidth
      }
    }
  }
  return {
    component: component,
    options: frmItemOptions
  }
}

function renderGroupFormLine(vm, h, item, options, frmRef) {
  let auditData = '0',
    auditMark = false,
    labelBGColor = ['white', 'greenyellow', 'red']
  if (options.auditMark === true) {
    if (item.auditMark !== false) {
      auditMark = true
    }
  } else {
    if (item.auditMark === true) {
      auditMark = true
    }
  }
  if (options.auditData && options.auditData.hasOwnProperty(item.key)) {
    if (['0', '1', '2'].includes(options.auditData[item.key])) {
      auditData = options.auditData[item.key]
    }
  }

  const children = item.fields.map((itemChild, index) => {
    if (!itemChild.hasOwnProperty('labelWidth')) {
      itemChild.labelWidth = options.labelWidth
    }
    if (typeof itemChild.type === 'undefined' || itemChild.type === null) {
      itemChild.type = 'input'
    }
    if (options.disabled === true) {
      if (itemChild.props) {
        itemChild.props.disabled = true
      } else {
        itemChild.props = {
          disabled: true
        }
      }
    }
    return renderFormItem.call(vm, h, itemChild, options, frmRef, index)
  })

  let lineClass = 'ivu-form-item',
    fieldCount = item.fields.length
  if (!isNullOrEmpty(item.class)) {
    lineClass += ' ' + item.class
  }

  let on = {},
    labelStyle = {
      width: item.labelWidth + 'px',
      backgroundColor: labelBGColor[auditData],
      color: (auditData === '2') ? 'white' : 'black'
    }
  if (auditMark) {
    labelStyle.cursor = 'pointer'
    if (typeof options.onAuditChange === 'function') {
      on.click = options.onAuditChange
    }
  }
  let labelComponent = h('label', {
    on: on,
    attrs: {
      fiKey: item.key
    },
    style: labelStyle,
    class: 'ivu-form-item-label'
  }, [item.title])
  if (item.requiredReminder === true || item.preRequired === true) {
    labelComponent = h('label', {
      on: on,
      attrs: {
        fiKey: item.key
      },
      style: labelStyle,
      class: 'ivu-form-item-label'
    }, [h('span', {
      class: !isNullOrEmpty(item.labelClass) ? item.labelClass : 'preRequired'
    }, item.title)])
  } else if (item.required === true) {
    lineClass += ' ivu-form-item-required'
  }
  let grdColumnGap = '0px'
  if (isNumber(item.columnGap)) {
    grdColumnGap = String(item.columnGap).trim() + 'px'
  }
  return h('div', {
    ref: item.key,
    class: lineClass,
    style: {
      marginBottom: '0'
    }
  }, [labelComponent, h('div', {
    class: 'ivu-form-item-content',
    style: {
      display: 'grid',
      'grid-column-gap': grdColumnGap,
      'grid-template-columns': 'repeat(' + fieldCount + ', 1fr)'
    }
  }, children)])
}

function renderFormItem (h, item, options, frmRef, index) {
  let me = this,
    component = null,
    $slots = me.slots() || {},
    frmItemOptions = {
      key: item.key,
      props: {
        prop: item.key,
        label: item.title,
        'label-width': item.labelWidth
      },
      attrs: {},
      style: {
        marginBottom: '0'
      }
    }

  if ($slots[item.key]) {
    component = $slots[item.key]
  } else {
    let func = cm.components[item.type]
    if (typeof func === 'function') {
      if (['input'].includes(item.type) && typeof item.specificValue === 'function') {
        return renderSpecificComponents(me, h, item, frmItemOptions, options, func, frmRef, index)
      } else {
        component = func.call(me, h, item, options.model, me.parent, frmRef, index)
        if (['dateRange'].includes(item.type)) {
          return component
        }
      }
    } else if (item.type === 'empty_formItem') {
      let emptyInfo = renderEmptyFormItem(h, item)
      component = emptyInfo.component
      frmItemOptions = emptyInfo.options
    } else if (item.type === 'group_form_line') {
      return renderGroupFormLine(me, h, item, options, frmRef)
    } else {
      component = h('div', {
        style: {
          paddingTop: '5px'
        }
      }, '类型:【' + item.type + '】暂不支持')
    }
  }

  if (item.hasOwnProperty('itemClass')) {
    frmItemOptions.class = item.itemClass
  }
  if (item.hasOwnProperty('itemStyle')) {
    frmItemOptions.style = item.itemStyle
  }

  if (item.requiredReminder === true || item.preRequired === true) {
    return renderPreRequired(me, frmRef, h, item, frmItemOptions, options, [component])
  } else {
    return renderInnerFormItem(me, frmRef, h, frmItemOptions, options, [component])
  }
}

export const formComponent = (h, context) => {
  let frmClass = context.data.class ? context.data.class : (context.data.staticClass ? context.data.staticClass : '')
  if (isNullOrEmpty(frmClass)) {
    frmClass = 'dc-form xdo-enter-form'
  } else {
    if (frmClass.indexOf('xdo-enter-form') < 0) {
      frmClass += ' xdo-enter-form'
    }
  }
  let realRef = context.data.ref
  if (isNullOrEmpty(realRef)) {
    realRef = 'frm_' + String((new Date()).getTime())
  }
  let onAuditChange = function () {
  }
  if (context.data.on && typeof context.data.on.onAuditChange === 'function') {
    onAuditChange = context.data.on.onAuditChange
  }

  let options = {
    ref: realRef,
    class: frmClass,
    attrs: context.data.attrs,
    model: context.props.model,
    rules: context.props.rules,
    fields: context.props.fields,
    inline: context.props.inline,
    disabled: context.props.disabled,
    labelWidth: context.props.labelWidth,
    showMessage: context.props.showMessage,
    auditMark: context.data.attrs.auditMark,
    auditData: context.data.attrs.auditData,
    autocomplete: context.props.autocomplete,
    labelPosition: context.props.labelPosition,
    onAuditChange: onAuditChange
  }

  const children = options.fields.map((item, index) => {
    if (!item.hasOwnProperty('labelWidth')) {
      item.labelWidth = options.labelWidth
    }
    if (typeof item.type === 'undefined' || item.type === null) {
      item.type = 'input'
    }
    if (options.disabled === true) {
      if (item.props) {
        item.props.disabled = true
      } else {
        item.props = {
          disabled: true
        }
      }
    }
    return renderFormItem.call(context, h, item, options, realRef, index)
  })

  enterToTabSimulation(context.parent, realRef)

  return h('Form', {
    ref: options.ref,
    attrs: options.attrs,
    class: options.class,
    props: {
      model: options.model,
      rules: options.rules,
      inline: options.inline,
      'label-width': options.labelWidth,
      autocomplete: options.autocomplete,
      'show-message': options.showMessage,
      'label-position': options.labelPosition
    }
  }, children)
}
