import { getComponentB<PERSON><PERSON><PERSON> } from '../utils/element-creater'

const propsInit = (key, item, model, vm) => {
  let props = {
    disabled: false,
    value: model[key]
  }
  if (item.props) {
    if (item.props.disabled === true) {
      props.disabled = true
    }
    if (item.props.multiple === true) {
      props.multiple = true
    }
    if (item.props && item.props.hasOwnProperty('optionLabelRender')) {
      props.optionLabelRender = item.props.optionLabelRender
    } else {
      props.optionLabelRender = vm.pcodeRender
    }
    if (item.type === 'pcode') {
      props.asyncOptions = vm.pcodeList
      if (item.props && item.props.hasOwnProperty('meta')) {
        props.meta = item.props.meta
      }
    } else {
      if (item.props && item.props.hasOwnProperty('options')) {
        props.options = item.props.options
      } else {
        props.options = []
      }
    }
  }
  return props
}

/**
 * 事件初始化
 * @param key
 * @param item
 * @param model
 * @param vm
 * @param frmRef
 * @returns {{input: function(*): void}}
 */
const eventsInit = (key, item, model, vm, frmRef) => {
  let events = {
    input: (val) => {
      if (key) {
        model[key] = val
        let me = getComponentByKey(vm, frmRef, key)
        if (me && typeof me.dispatch === 'function') {
          me.dispatch('FormItem', 'on-form-change', model[key])
        }
      }
    }
  }
  if (item.on) {
    if (typeof item.on.change === 'function') {
      events['on-change'] = item.on.change
    }
  }
  return events
}

export const selectComponent = (h, item, model, vm, frmRef) => {
  const key = item.key
  let style = {}
  if (item.style) {
    style = item.style
  }
  let props = propsInit(key, item, model, vm)
  let events = eventsInit(key, item, model, vm, frmRef)

  return h('xdo-select', {
    key: key,
    on: events,
    style: style,
    props: props
  }, model[key])
}
