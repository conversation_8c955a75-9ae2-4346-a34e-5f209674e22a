import { isDate } from '@/libs/datetime'
import { isNullOrEmpty } from '@/libs/util'
import { datePickerComponent } from './date-picker'

const propsInit = (item, model, vm) => {
  const key = item.key
  let startKey = key + 'From'
  let endKey = key + 'To'
  if (Array.isArray(item.fields) && item.fields.length === 2) {
    if (!isNullOrEmpty(item.fields[0].key)) {
      startKey = item.fields[0].key
    }
    if (!isNullOrEmpty(item.fields[1].key)) {
      endKey = item.fields[1].key
    }
  }
  let type = 'date', disabled = false, size = ''
  if (['date', 'datetime', 'month'].includes(item.itemType)) {
    type = item.itemType
  }
  if (item.props) {
    if (item.props.disabled === true) {
      disabled = true
    }
    if (['large', 'small', 'default'].includes(item.props.size)) {
      size = item.props.size
    }
    if (Array.isArray(item.props.values) && item.props.values.length) {
      let startDate = '', endDate = ''
      try {
        if (!isNullOrEmpty(item.props.values[0])) {
          startDate = (new Date(item.props.values[0])).format('yyyy-MM-dd')
        }
      } catch (e) {
        startDate = ''
        console.info('起始时间错误: ' + e.message)
      }
      model[startKey] = startDate
      try {
        if (!isNullOrEmpty(item.props.values[1])) {
          endDate = (new Date(item.props.values[1])).format('yyyy-MM-dd')
        }
      } catch (e) {
        endDate = ''
        console.info('截止时间错误: ' + e.message)
      }
      model[endKey] = endDate
    }
  }
  let shortcuts_end = [],
    shortcuts_start = []
  if (Array.isArray(item['startShortcuts']) && item['startShortcuts'].length > 0) {
    shortcuts_start = item['startShortcuts']
  }
  if (Array.isArray(item['endShortcuts']) && item['endShortcuts'].length > 0) {
    shortcuts_end = item['endShortcuts']
  }
  let rangeProps = {
    startOptions: {
      ref: startKey,
      key: startKey,
      itemType: item.itemType,
      props: {
        options: {
          disabledDate: () => {
            return false
          },
          shortcuts: shortcuts_start
        },
        type: type,
        size: size,
        disabled: disabled,
        placeholder: '请选择起始时间'
      },
      on: {
        change: function (startDate) {
          let eKey = endKey,
            me = vm
          if (me && me.$refs[eKey]) {
            if (isDate(startDate)) {
              me.$refs[eKey].options = {
                disabledDate: date => {
                  let startTime = ''
                  if (model[startKey]) {
                    let tmpDate = new Date(model[startKey])
                    startTime = tmpDate.setDate(tmpDate.getDate()).valueOf()
                  }
                  if (isNullOrEmpty(startTime)) {
                    return ''
                  }
                  return date && (date.valueOf() < startTime)
                }
              }
            } else {
              me.$refs[eKey].options = {
                disabledDate: () => {
                  return false
                }
              }
            }
          }
        }
      }
    },
    endOptions: {
      ref: endKey,
      key: endKey,
      itemType: item.itemType,
      props: {
        options: {
          disabledDate: () => {
            return false
          },
          shortcuts: shortcuts_end
        },
        type: type,
        size: size,
        disabled: disabled,
        placeholder: '请选择截止时间'
      },
      on: {
        change: function (endDate) {
          let sKey = startKey,
            me = vm
          if (me && me.$refs[sKey]) {
            if (isDate(endDate)) {
              me.$refs[sKey].options = {
                disabledDate: date => {
                  let endTime = ''
                  if (model[endKey]) {
                    let tmpDate = new Date(model[endKey])
                    endTime = tmpDate.setDate(tmpDate.getDate()).valueOf()
                  }
                  if (isNullOrEmpty(endTime)) {
                    return ''
                  }
                  return date && date.valueOf() > endTime
                }
              }
            } else {
              me.$refs[sKey].options = {
                disabledDate: () => {
                  return false
                }
              }
            }
          }
        }
      }
    }
  }
  return rangeProps
}

export const dateRangeComponent = (h, item, model, vm, frmRef) => {
  let rangeProps = propsInit(item, model, vm)
  let startDate = h('FormItem', {
    key: rangeProps.startOptions.key,
    props: {
      prop: rangeProps.startOptions.key,
    },
    style: {
      width: '100%',
      marginBottom: '0'
    }
  }, [datePickerComponent(h, rangeProps.startOptions, model, vm, frmRef)])

  let endDate = h('FormItem', {
    key: rangeProps.endOptions.key,
    props: {
      prop: rangeProps.endOptions.key,
    },
    style: {
      width: '100%',
      marginBottom: '0'
    }
  }, [datePickerComponent(h, rangeProps.endOptions, model, vm, frmRef)])

  return h('div', {
    class: 'ivu-form-item',
    style: {
      marginBottom: '0px'
    }
  }, [h('label', {
    class: 'ivu-form-item-label',
    style: {
      width: item.labelWidth + 'px'
    }
  }, [item.title]), h('div', {
    class: 'ivu-form-item-content',
    style: {
      padding: '0',
      display: 'flex',
      margin: '0 0 0 ' + item.labelWidth + 'px'
    }
  }, [startDate, h('div', {
    style: {
      width: '20px',
      paddingTop: '2px',
      textAlign: 'center'
    }
  }, '-'), endDate])])
}
