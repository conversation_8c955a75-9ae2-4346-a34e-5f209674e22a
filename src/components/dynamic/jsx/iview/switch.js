import { isNullOrEmpty } from '@/libs/util'
import { getComponentByKey } from '../utils/element-creater'

const propsInit = (key, item, model) => {
  let props = {
    loading: false,
    disabled: false,
    value: model[key],
    'true-value': '1',
    'false-value': '0'
  }
  if (item.props) {
    if (item.props.disabled === true) {
      props.disabled = true
    }
    if (['large', 'small', 'default'].includes(item.props.size)) {
      props.size = item.props.size
    }
    let trueValue = item.props['true-value']
    if (!isNullOrEmpty(trueValue) || trueValue === '' || [true, false].includes(trueValue)) {
      props['true-value'] = trueValue
    }
    let falseValue = item.props['false-value']
    if (!isNullOrEmpty(falseValue) || falseValue === '' || [true, false].includes(falseValue)) {
      props['false-value'] = falseValue
    }
    if ([true, false].includes(item.props.loading)) {
      props.loading = item.props.loading
    }
  }
  return props
}

const eventsInit = (key, item, model, vm, frmRef) => {
  let events = {
    input: (val) => {
      if (key) {
        model[key] = val
        let me = getComponentByKey(vm, frmRef, key)
        if (me && typeof me.dispatch === 'function') {
          me.dispatch('FormItem', 'on-form-change', model[key])
        }
      }
    }
  }
  if (item.on) {
    if (typeof item.on.change === 'function') {
      events['on-change'] = item.on.change
    }
  }
  return events
}

const slotsInit = (h, item)=> {
  let slots = []
  if (item.slot) {
    slots = Object.keys(item.slot).filter(slot => {
      return ['open', 'close'].includes(slot)
    }).map(slot => {
      return h('span', {
        slot: slot
      }, [item.slot[slot]])
    })
  }
  return slots
}

export const switchComponent = (h, item, model, vm, frmRef) => {
  const key = item.key
  let style = {
    marginTop: '4px'
  }
  if (item.style) {
    style = Object.assign(style, item.style)
  }
  let props = propsInit(key, item, model)
  let events = eventsInit(key, item, model, vm, frmRef)
  let slots = slotsInit(h, item)

  return h('i-switch', {
    key: key,
    on: events,
    props: props,
    style: style
  }, slots)
}
