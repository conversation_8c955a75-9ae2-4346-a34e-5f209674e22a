import cm from '../utils/component-manage'
import { isNullOrEmpty } from '@/libs/util'
import { deepClone } from '../utils/element-creater'

const getPreRequiredRender = (h, item, options, components) => {
  return h('FormItem', options, [h('span', {
    slot: 'label',
    class: !isNullOrEmpty(item.labelClass) ? item.labelClass : 'preRequired'
  }, item.title), ...components])
}

const getSpecificInput = (vm, h, key, item, frmItemOptions, options, frmRef, index) => {
  let specificItem = {
    theKey: key,
    type: 'specificInput',
    key: key + '_specific',
    slot: deepClone(item.slot),
    props: deepClone(item.props),
    specificValue: item.specificValue
  }
  let funcSpecific = cm.components['specificInput'],
    specificComponent = funcSpecific.call(vm, h, specificItem, vm.parent, frmRef, index)

  if (item.style) {
    item.style.display = 'none'
  } else {
    item.style = {
      display: 'none'
    }
  }
  let simplifyItem = deepClone(item)
  delete simplifyItem.slot
  delete simplifyItem.specificValue
  let component = func.call(vm, h, simplifyItem, options.model, vm.parent, frmRef, index)
  let childrenComponents = [specificComponent, component]

  if (item.hasOwnProperty('itemClass')) {
    frmItemOptions.class = item.itemClass
  }
  if (item.hasOwnProperty('itemStyle')) {
    frmItemOptions.style = item.itemStyle
  }

  if (item.requiredReminder === true || item.preRequired === true) {
    return getPreRequiredRender(h, item, frmItemOptions, childrenComponents)
  } else {
    return h('FormItem', frmItemOptions, childrenComponents)
  }
}

const getEmptyFormItem = (h, key, item, frmItemOptions, component) => {
  if (item.isCard === true) {
    frmItemOptions = {
      key: key,
      props: {
        'label-width': 0
      },
      style: {
        marginBottom: '5px',
        backgroundColor: '#f7f7f7',
        borderTop: '1px solid #e8eaec'
      }
    }
    if (item.hasOwnProperty('itemClass')) {
      if (item.itemClass.indexOf('ivu-card-head') < 0) {
        item.itemClass = item.itemClass + ' ivu-card-head'
      }
    } else {
      item.itemClass = 'ivu-card-head'
    }
    if (item.collapse === true) {
      frmItemOptions.style.cursor = 'pointer'
      if (item.on && typeof item.on.click === 'function') {
        frmItemOptions.nativeOn = {
          click: item.on.click
        }
      }
      let rotate = 'rotate(0deg)'
      if (item.props && item.props.collapse === false) {
        rotate = 'rotate(90deg)'
      }
      component = h('div', {
        class: 'ivu-collapse-item'
      }, [
        h('div', {
          class: 'ivu-collapse-header'
        }, [h('i', {
          style: {
            top: '5px',
            transform: rotate,
            marginRight: '14px',
            position: 'absolute',
            transition: 'transform .2s ease-in-out'
          },
          class: 'ivu-icon ivu-icon-ios-arrow-forward'
        }, []), h('p', {
          style: {
            paddingLeft: '16px'
          }
        }, item.title)])
      ])
    } else {
      component = h('p', item.title)
    }
  } else {
    component = null  // h('p', '占位栏') //
    frmItemOptions = {
      key: key,
      props: {
        label: item.title,
        'label-width': item.labelWidth
      }
    }
  }
  return {
    component: component,
    options: frmItemOptions
  }
}

const getGroupFormLine = (vm, h, item, options, frmRef) => {
  const children = item.fields.map((itemChild, index) => {
    if (!itemChild.hasOwnProperty('labelWidth')) {
      itemChild.labelWidth = options.labelWidth
    }
    if (typeof itemChild.type === 'undefined' || itemChild.type === null) {
      itemChild.type = 'input'
    }
    if (options.disabled === true) {
      if (itemChild.props) {
        itemChild.props.disabled = true
      } else {
        itemChild.props = {
          disabled: true
        }
      }
    }
    return formItemComponent(vm, h, item, options, frmRef, index)
  })

  let lineClass = 'ivu-form-item',
    fieldCount = item.fields.length
  if (!isNullOrEmpty(item.class)) {
    lineClass += ' ' + item.class
  }

  return h('div', {
    class: lineClass,
    style: {
      marginBottom: '0'
    }
  }, [h('label', {
    class: 'ivu-form-item-label',
    style: {
      width: item.labelWidth + 'px'
    }
  }, [item.title]), h('div', {
    class: 'ivu-form-item-content',
    style: {
      display: 'grid',
      'grid-column-gap': '0',
      'grid-template-columns': 'repeat(' + fieldCount + ', 1fr)'
    }
  }, children)])
}

export const formItemComponent = (vm, h, item, options, frmRef, index) => {
  let key = item.key,
    component = null,
    $slots = vm.slots() || {},
    frmItemOptions = {
      key: key,
      props: {
        prop: key,
        label: item.title,
        'label-width': item.labelWidth
      },
      attrs: {},
      style: {
        marginBottom: '0'
      }
    }
  if ($slots[key]) {
    component = $slots[key]
  } else {
    let func = cm.components[item.type]
    if (typeof func === 'function') {
      if (['input'].includes(item.type) && typeof item.specificValue === 'function') {
        return getSpecificInput(vm, h, key, item, frmItemOptions, options, frmRef, index)
      } else {
        component = func.call(vm, h, item, options.model, vm.parent, frmRef, index)
        if (['dateRange'].includes(item.type)) {
          return component
        }
      }
    } else if (item.type === 'empty_formItem') {
      let emptyInfo = getEmptyFormItem(h, key, item, frmItemOptions, component)
      frmItemOptions = emptyInfo.options
      component = emptyInfo.component
    } else if (item.type === 'group_form_line') {
      return getGroupFormLine(vm, h, item, options, frmRef)
    } else {
      component = h('div', {
        style: {
          paddingTop: '5px'
        }
      }, '类型:【' + item.type + '】暂不支持')
    }
  }

  if (item.hasOwnProperty('itemClass')) {
    frmItemOptions.class = item.itemClass
  }
  if (item.hasOwnProperty('itemStyle')) {
    frmItemOptions.style = item.itemStyle
  }

  if (item.requiredReminder === true || item.preRequired === true) {
    return getPreRequiredRender(h, item, frmItemOptions, [component])
  } else {
    return h('FormItem', frmItemOptions, [component])
  }
}
