<template>
  <section>
    <div class="left-sider">
      <p class="sider-title">
        配置{{title}}
      </p>
      <XdoButton v-if="showEmptyItem" type="primary" icon="ios-phone-landscape" @click="handleAddEmpty">
        添加占位栏
      </XdoButton>
      <XdoIcon type="md-menu" @click.native="handleRightSliderClick"/>
    </div>
    <div style="margin: 0; padding: 0; display: flex;">
      <div v-show="!collapsed" class="tree-grid">
        <DcAgGrid ref="table" :columns="unSelectedGrdColumns" :data="unSelectedFields" :height="564"
                  focusedRowSelection enableFilter
                  @onCellDoubleClicked="onUnSelectedDoubleClicked"
                  @on-selection-change="handleUnSelectionChange"></DcAgGrid>
      </div>
      <div :class="btnClass">
        <div>
          <XdoButton type="primary" icon="ios-arrow-up" @click="handleUp"></XdoButton>
        </div>
        <div>
          <XdoButton type="primary" icon="ios-arrow-forward" @click="handleForward"></XdoButton>
        </div>
        <div>
          <XdoButton type="primary" icon="ios-arrow-back" @click="handleBack"></XdoButton>
        </div>
        <div>
          <XdoButton type="primary" icon="ios-arrow-down" @click="handleDown"></XdoButton>
        </div>
      </div>
      <div :class="treeGridClass">
        <DcAgGrid ref="grdSel" :columns="selectedGrdColumns" :data="selectedFields" :height="564"
                  focusedRowSelection enableFilter
                  @onCellDoubleClicked="onSelectedDoubleClicked"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
      </div>
    </div>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'fieldsSelect',
    props: {
      title: {
        type: String,
        required: true,
        default: () => ('')
      },
      collapsed: {
        type: Boolean,
        default: () => false
      },
      fields: {
        type: Array,
        default: () => ([])
      },
      selFields: {
        type: Array,
        default: () => ([])
      },
      showChecked: {
        type: Boolean,
        default: () => true
      },
      showEmptyItem: {
        type: Boolean,
        default: () => false
      }
    },
    data() {
      return {
        colSelection: [{
          width: 34,
          align: 'center',
          key: 'selection',
          type: 'selection'
        }],
        selectRows: [],
        unSelectRows: [],
        currSelectedKeys: [],
        selectedEmptyItems: []
      }
    },
    computed: {
      unSelectedGrdColumns() {
        return [
          ...this.colSelection, {
            width: 215,
            key: 'title',
            filter: 'text',
            align: 'center',
            title: '待选' + this.title,
            render: (h, params) => {
              let key = params.row['key'],
                title = params.row[params.column.key]
              return h('span', title + ' (' + key + ')')
            }
          }
        ]
      },
      selectedGrdColumns() {
        return [
          ...this.colSelection, {
            width: 215,
            key: 'title',
            filter: 'text',
            align: 'center',
            title: '有效' + this.title + '(已选)',
            render: (h, params) => {
              let title = params.row[params.column.key],
                key = params.row['key']
              if (params.row.type === 'empty_formItem') {
                if (isNullOrEmpty(title)) {
                  title = '占位栏'
                }
              } else {
                title = title + ' (' + key + ')'
              }
              if (this.showChecked) {
                let show = params.row['defaultShow']
                let iconRender = h('Icon', {
                  style: {
                    color: 'Gray'
                  },
                  props: {
                    size: 17,
                    type: 'ios-close-circle'
                  }
                })
                if (show === true) {
                  iconRender = h('Icon', {
                    style: {
                      color: 'Green'
                    },
                    props: {
                      size: 17,
                      type: 'ios-checkmark-circle'
                    }
                  })
                }
                return h('div', [
                  iconRender,
                  h('span', {
                    style: {
                      paddingLeft: '3px'
                    }
                  }, title)
                ])
              } else {
                return h('span', title)
              }
            }
          }
        ]
      },
      treeGridClass() {
        if (this.collapsed) {
          return 'tree-grid-full'
        }
        return 'tree-grid'
      },
      btnClass() {
        if (this.collapsed) {
          return 'button-div-right'
        }
        return 'button-div'
      },
      unSelectedFields() {
        let me = this
        return me.fields.filter(param => {
          return !me.currSelectedKeys.includes(param.key)
        })
      },
      selectedFields() {
        let me = this
        let selFields = []
        me.currSelectedKeys.forEach(key => {
          let theFields = me.selFields.filter(param => {
            return param.key === key
          })
          let currField = {}
          if (Array.isArray(theFields) && theFields.length === 1) {
            currField = theFields[0]
            currField.index = me.currSelectedKeys.indexOf(key)
            selFields.push(currField)
          } else {
            theFields = me.fields.filter(param => {
              return param.key === key
            })
            if (Array.isArray(theFields) && theFields.length === 1) {
              currField = theFields[0]
              currField.index = me.currSelectedKeys.indexOf(key)
              selFields.push(currField)
            } else {
              theFields = me.selectedEmptyItems.filter(param => {
                return param.key === key
              })
              if (Array.isArray(theFields) && theFields.length === 1) {
                currField = theFields[0]
                currField.index = me.currSelectedKeys.indexOf(key)
                selFields.push(currField)
              }
            }
          }
        })
        return selFields
      }
    },
    watch: {
      selFields: {
        deep: true,
        immediate: true,
        handler: function (selFields) {
          let me = this,
            tmpEmptyItems = selFields.filter(field => {
              return field.type === 'empty_formItem'
            }),
            tmpSelectedKeys = selFields.map(sParam => {
              return sParam.key
            }),
            resultSelKeys = []
          if (me.selectedEmptyItems.length === 0) {
            me.$set(me, 'selectedEmptyItems', tmpEmptyItems)
            resultSelKeys = tmpSelectedKeys
          } else {
            let selEmptyKeys = me.selectedEmptyItems.map(item => {
              return item.key
            })
            me.currSelectedKeys.forEach(key => {
              if (tmpSelectedKeys.includes(key)) {
                resultSelKeys.push(key)
              } else if (selEmptyKeys.includes(key)) {
                resultSelKeys.push(key)
              }
            })
          }
          me.$set(me, 'currSelectedKeys', resultSelKeys)
        }
      },
      currSelectedKeys: {
        deep: true,
        immediate: true,
        handler: function (selectedKeys) {
          let me = this
          me.$emit('set-model-config', selectedKeys)
        }
      },
      selectedEmptyItems: {
        deep: true,
        immediate: true,
        handler: function (selEmptyItems) {
          let me = this
          me.$emit('onEmptyItemsSync', selEmptyItems)
        }
      }
    },
    methods: {
      /**
       * 点击收缩--展开选择内容
       */
      handleRightSliderClick(e) {
        let me = this
        if (me.collapsed) {
          e.target.style.transform = 'rotate(0deg)'
          e.target.style['-webkit-transform'] = 'rotate(0deg)'
          if (me.$refs['attachTitle']) {
            me.$refs['attachTitle'].style.padding = '3px 10px'
          }
        } else {
          e.target.style.transform = 'rotate(90deg)'
          e.target.style['-webkit-transform'] = 'rotate(90deg)'
          if (me.$refs['attachTitle']) {
            me.$refs['attachTitle'].style.padding = '3px 22px'
          }
        }
        me.$emit('collapsedChange', !me.collapsed)
      },
      /**
       * 添加空白格
       */
      handleAddEmpty() {
        let me = this,
          theKey = String((new Date()).getTime())
        me.selectedEmptyItems.push({
          key: theKey,
          title: '',
          index: null,
          isCard: false,
          itemClass: '',
          defaultShow: true,
          type: 'empty_formItem'
        })
        me.currSelectedKeys.push(theKey)
      },
      /**
       * 待选择字段选中
       * @param selRows
       */
      handleUnSelectionChange(selRows) {
        let me = this
        me.$set(me, 'unSelectRows', selRows)
      },
      /**
       * 双击待选择字段--选取之
       */
      onUnSelectedDoubleClicked(row) {
        let me = this
        if (typeof row === 'object' && !isNullOrEmpty(row.key)) {
          me.currSelectedKeys.push(row.key)
          me.$set(me, 'unSelectRows', [])
        }
      },
      /**
       * 根据key值设置选中项
       */
      setRowSelected(key) {
        let me = this
        me.$nextTick(() => {
          if (me.$refs.grdSel && me.$refs.grdSel.api && typeof me.$refs.grdSel.api.forEachNode === 'function') {
            me.$refs.grdSel.api.forEachNode(function (node) {
              if (node.data.key === key) {
                node.setSelected(true, false)
              }
            })
          }
        })
      },
      /**
       * 上移
       */
      handleUp() {
        let me = this
        if (Array.isArray(me.selectRows) && me.selectRows.length === 1) {
          let keyIndex = me.currSelectedKeys.indexOf(me.selectRows[0].key)
          if (keyIndex > 0) {
            me.currSelectedKeys[keyIndex] = me.currSelectedKeys.splice(keyIndex - 1, 1, me.currSelectedKeys[keyIndex])[0]
            me.setRowSelected(me.selectRows[0].key)
          }
        }
      },
      /**
       * 添加
       */
      handleForward() {
        let me = this
        if (Array.isArray(me.unSelectRows) && me.unSelectRows.length > 0) {
          me.unSelectRows.forEach(row => {
            me.currSelectedKeys.push(row.key)
          })
          me.$set(me, 'unSelectRows', [])
        }
      },
      /**
       * 移除
       */
      handleBack() {
        let me = this
        if (Array.isArray(me.selectRows) && me.selectRows.length > 0) {
          me.selectRows.forEach(row => {
            me.currSelectedKeys.splice(me.currSelectedKeys.indexOf(row.key), 1)
            me.removeSelectedEmptyItems(row.key)
          })
          me.$set(me, 'selectRows', [])
        }
      },
      /**
       * 下移
       */
      handleDown() {
        let me = this
        if (Array.isArray(me.selectRows) && me.selectRows.length === 1) {
          let keyIndex = me.currSelectedKeys.indexOf(me.selectRows[0].key)
          if (keyIndex < me.currSelectedKeys.length - 1) {
            me.currSelectedKeys[keyIndex] = me.currSelectedKeys.splice(keyIndex + 1, 1, me.currSelectedKeys[keyIndex])[0]
            me.setRowSelected(me.selectRows[0].key)
          }
        }
      },
      /**
       * 双击已选择字段--删除之
       * @param row
       */
      onSelectedDoubleClicked(row) {
        let me = this
        if (typeof row === 'object' && !isNullOrEmpty(row.key)) {
          me.currSelectedKeys.splice(me.currSelectedKeys.indexOf(row.key), 1)
          me.removeSelectedEmptyItems(row.key)
          me.$set(me, 'selectRows', [])
        }
      },
      /**
       * 移除无用的占位栏
       * @param key
       */
      removeSelectedEmptyItems(key) {
        let me = this,
          currItems = me.selectedEmptyItems.filter(item => {
            return item.key === key
          })
        if (Array.isArray(currItems) && currItems.length > 0) {
          me.selectedEmptyItems.splice(me.selectedEmptyItems.findIndex(item => item.key === key), 1)
        }
      },
      /**
       * 选中已选择字段用于展示需配置内容
       * @param selRows
       */
      handleSelectionChange(selRows) {
        let me = this
        me.$set(me, 'selectRows', selRows)
        let theRow = {}
        if (Array.isArray(selRows) && selRows.length > 0) {
          theRow = selRows[0]
        }
        me.$emit('selectionChange', theRow)
      }
    }
  }
</script>

<style scoped>
  .left-sider {
    height: 36px;
    background: white;
    padding: 6px 0 0 6px;
    border-left: 1px solid rgb(214, 219, 222);
    border-bottom: 1px solid rgb(214, 219, 222);

    .ivu-layout-sider-children {
      overflow-y: hidden;
      margin-right: -18px;
    }
  }

  .left-sider i {
    color: #389de9;
    cursor: pointer;
    font-size: 26px;
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transition: transform .2s linear;
    -webkit-transition: -webkit-transform .2s linear;
  }

  .sider-title {
    float: left;
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    padding: 3px 10px;
  }

  .tree-grid {
    width: calc(100% - 40px);
  }

  .tree-grid-full {
    width: calc(100% - 50px);
  }

  .button-div {
    width: 80px;
    text-align: center;
    vertical-align: middle;
    padding: 237px 10px 0 10px;
  }

  .button-div-right {
    float: right;
    text-align: center;
    vertical-align: middle;
    padding: 237px 10px 0 10px;
  }

  .button-div > div, .button-div-right > div {
    padding: 0 0 10px 0
  }
</style>
