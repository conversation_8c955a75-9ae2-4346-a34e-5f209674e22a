import { isNullOrEmpty } from '@/libs/util'

export const pageConfigFields = {
  data() {
    return {
      params: [],
      selParams: [],
      fields: [],
      selList: [],
      selDetails: [],
      paramName: '',
      methodName: '',
      resultName: '',
      subjectName: '',
      // 标签展示
      mainFields: [{
        title: '所属模块',
        key: 'subjectName'
      }, {
        title: '方法名称',
        key: 'methodName'
      }, {
        title: '参数名称',
        key: 'paramName'
      }, {
        title: '返回实体',
        key: 'resultName'
      }]
    }
  },
  computed: {
    model() {
      return {
        paramName: this.paramName,
        methodName: this.methodName,
        resultName: this.resultName,
        subjectName: this.subjectName
      }
    }
  },
  methods: {
    dataClear() {
      let me = this
      me.$set(me, 'params', [])
      me.$set(me, 'fields', [])
      me.$set(me, 'selParams', [])
      me.$set(me, 'selList', [])
      me.$set(me, 'selDetails', [])
      me.$set(me, 'paramName', '')
      me.$set(me, 'methodName', '')
      me.$set(me, 'resultName', '')
      me.$set(me, 'subjectName', '')
    },
    dataFill(dataObj) {
      let me = this
      me.$set(me, 'params', Array.isArray(dataObj.params) ? dataObj.params : [])
      me.$set(me, 'fields', Array.isArray(dataObj.fields) ? dataObj.fields : [])
      me.$set(me, 'selParams', Array.isArray(dataObj.selParams) ? dataObj.selParams : [])
      me.$set(me, 'selList', Array.isArray(dataObj.selList) ? dataObj.selList : [])
      me.$set(me, 'selDetails', Array.isArray(dataObj.selDetails) ? dataObj.selDetails : [])
      me.$set(me, 'paramName', !isNullOrEmpty(dataObj.paramName) ? dataObj.paramName : '')
      me.$set(me, 'methodName', !isNullOrEmpty(dataObj.methodName) ? dataObj.methodName : '')
      me.$set(me, 'resultName', !isNullOrEmpty(dataObj.resultName) ? dataObj.resultName : '')
      me.$set(me, 'subjectName', !isNullOrEmpty(dataObj.subjectName) ? dataObj.subjectName : '')
    },
    handleClose() {
      let me = this
      me.$emit('update:show', false)
    }
  }
}
