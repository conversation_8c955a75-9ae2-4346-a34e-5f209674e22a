import { commConfig } from '../comm/commConfig'

export const iViews = {
  mixins: [ commConfig ],
  watch: {
    'model.componentsType': {
      immediate: true,
      handler: function (componentsType) {
        let me = this
        let defaultFields = me.getDefaultFields()
        switch (componentsType) {
          case 'input':
            me.$set(me, 'fields', [...defaultFields, ...me.getInputFields()])
            break
          case 'select':
            me.$set(me, 'fields', [...defaultFields, ...me.getSelectFields()])
            break
          case 'pcode':
            me.$set(me, 'fields', [...defaultFields, ...me.getPCodeFields()])
            break
          case 'datePicker':
            me.$set(me, 'fields', [...defaultFields, ...me.getDatePickerFields()])
            break
          case 'dateRange':
            me.$set(me, 'fields', [...defaultFields, ...me.getDateRangeFields()])
            break
          case 'xdoInput':
          case 'numberInput':
            me.$set(me, 'fields', [...defaultFields, ...me.getNumberInputFields()])
            break
          case 'switch':
            me.$set(me, 'fields', [...defaultFields, ...me.getSwitchFields()])
            me.$nextTick(() => {
              me.$set(me.model, 'valueType', 'String')
              me.resetTrueValueType('String')
            })
            break
          case 'checkBox':
            me.$set(me, 'fields', [...defaultFields, ...me.getCheckBoxFields()])
            me.$nextTick(() => {
              me.$set(me.model, 'valueType', 'String')
              me.resetTrueValueType('String')
            })
            break
          case 'checkBoxGroup':
            me.$set(me, 'fields', [...defaultFields, ...me.getCheckBoxGroupFields()])
            break
          case 'radioGroup':
            me.$set(me, 'fields', [...defaultFields, ...me.getRadioGroupFields()])
            break
          default:
            me.$set(me, 'fields', defaultFields)
        }
      }
    },
    'model.itemType': {
      immediate: true,
      handler: function (itemType) {
        let me = this
        let theFields = me.fields.filter(field => {
          return ['rows', 'wrap'].includes(field.key)
        })
        if (itemType === 'textarea') {
          theFields.forEach(field => {
            field.hidden = false
          })
        } else {
          theFields.forEach(field => {
            field.hidden = true
          })
        }
      }
    },
    'model.search': {
      immediate: true,
      handler: function (search) {
        let me = this
        let theFields = me.fields.filter(field => {
          return ['enterButton'].includes(field.key)
        })
        if (search === true) {
          theFields.forEach(field => {
            field.hidden = false
          })
        } else {
          theFields.forEach(field => {
            field.hidden = true
          })
        }
      }
    },
    'model.valueType': {
      immediate: true,
      handler: function (valueType) {
        this.resetTrueValueType(valueType)
      }
    }
  },
  methods: {
    getInputFields() {
      return [{
        title: '类型',
        key: 'itemType',
        type: 'select',
        props: {
          options: ['text', 'password', 'textarea', 'url', 'email', 'date', 'number', 'tel'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'text'
      }, {
        title: '行数(rows)',
        key: 'rows',
        type: 'xdoInput',
        hidden: true,
        props: {
          intDigits: 2
        },
        defaultValue: 2
      }, {
        title: 'wrap属性',
        key: 'wrap',
        type: 'select',
        hidden: true,
        props: {
          options: ['soft', 'hard'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'soft'
      }, {
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '占位文本',
        key: 'placeholder'
      }, {
        title: '最大输入长度',
        key: 'maxlength',
        type: 'xdoInput',
        props: {
          intDigits: 4
        }
      }, {
        title: '尾部图标(仅text类型有效)',
        key: 'icon'
      }, {
        title: '前缀',
        key: 'prefix'
      }, {
        title: '后缀',
        key: 'suffix'
      }, {
        title: '显示为搜索型',
        key: 'search',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }, {
        title: '搜索按钮名称',
        key: 'enterButton',
        hidden: true
      }, {
        title: '自动获得焦点',
        key: 'autofocus',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }]
    },
    getSelectFields() {
      return [{
        title: '显示类型',
        key: 'showType',
        type: 'select',
        props: {
          options: [{
            value: 'value', label: '代码'
          }, {
            value: 'label', label: '名称'
          }, {
            value: 'valueLabel', label: '代码 名称'
          }],
          optionLabelRender: item => item.label
        },
        defaultValue: 'valueLabel'
      }]
    },
    getPCodeFields() {
      return [{
        title: 'PCode关键字',
        key: 'meta',
        required: true
      }]
    },
    getDatePickerFields() {
      return [{
        title: '类型',
        key: 'itemType',
        type: 'select',
        props: {
          options: ['date', 'datetime', 'year', 'month'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'date'
      }, {
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '多选',
        key: 'multiple',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }, {
        title: '占位文本',
        key: 'placeholder'
      }, {
        title: '显示星期数',
        key: 'showWeekNumbers',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }]
    },
    getDateRangeFields() {
      return [{
        title: '类型',
        key: 'itemType',
        type: 'select',
        props: {
          options: ['date', 'datetime'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'date'
      }, {
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '起始字段名',
        key: 'startKey'
      }, {
        title: '截止字段名',
        key: 'endKey'
      }, {
        title: '起始默认值',
        key: 'startValue',
        type: 'datePicker',
      }, {
        title: '截止默认值',
        key: 'endValue',
        type: 'datePicker',
      }]
    },
    getNumberInputFields() {
      return [{
        title: '可输负数',
        key: 'negative',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }, {
        title: '整数位数',
        key: 'intDigits',
        type: 'xdoInput',
        props: {
          intDigits: 3
        },
        defaultValue: 10
      }, {
        title: '小数位数',
        key: 'precision',
        type: 'xdoInput',
        props: {
          intDigits: 1
        },
        defaultValue: 0
      }, {
        title: '增减步幅',
        key: 'step',
        type: 'xdoInput',
        props: {
          intDigits: 4,
          precision: 3
        },
        defaultValue: 1
      }, {
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '占位文本',
        key: 'placeholder'
      }, {
        title: '尾部图标(仅text类型有效)',
        key: 'icon'
      }, {
        title: '前缀',
        key: 'prefix'
      }, {
        title: '后缀',
        key: 'suffix'
      }, {
        title: '显示为搜索型',
        key: 'search',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }, {
        title: '搜索按钮名称',
        key: 'enterButton',
        hidden: true
      }, {
        title: '自动获得焦点',
        key: 'autofocus',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }]
    },
    resetTrueValueType(valueType) {
      let me = this
      let theFields = me.fields.filter(field => {
        return ['trueValue', 'falseValue'].includes(field.key)
      })
      if (valueType === 'String') {
        theFields.forEach(field => {
          field.hidden = false
        })
      } else {
        theFields.forEach(field => {
          field.hidden = true
        })
      }
    },
    getSwitchFields() {
      return [{
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '值类型',
        key: 'valueType',
        type: 'radioGroup',
        props: {
          options: [{
            label: 'String', title: '字符串'
          }, {
            label: 'Boolean', title: '布尔值'
          }]
        },
        defaultValue: 'String'
      }, {
        title: '选中值',
        key: 'trueValue',
        hidden: true,
        defaultValue: '1'
      }, {
        title: '非选中值',
        key: 'falseValue',
        hidden: true,
        defaultValue: '0'
      }]
    },
    getCheckBoxFields() {
      return this.getSwitchFields()
    },
    getCheckBoxGroupFields() {
      return [{
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }]
    },
    getRadioGroupFields() {
      return [{
        title: '尺寸',
        key: 'size',
        type: 'select',
        props: {
          options: ['large', 'small', 'default'],
          optionLabelRender: (item) => item
        },
        defaultValue: 'default'
      }, {
        title: '显示为按钮型',
        key: 'showStyle',
        type: 'switch',
        props: {
          'true-value': 'button',
          'false-value': ''
        },
        defaultValue: ''
      }, {
        title: '垂直排列',
        key: 'vertical',
        type: 'switch',
        props: {
          'true-value': true,
          'false-value': false
        },
        defaultValue: false
      }]
    }
  }
}
