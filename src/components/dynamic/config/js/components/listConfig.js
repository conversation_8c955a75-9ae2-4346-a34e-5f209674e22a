import { commConfig } from '../comm/commConfig'

export const listConfig = {
  mixins: [commConfig],
  data() {
    let defaultData = this.getDefaultData()
    return {
      model: {
        ...defaultData
      },
      rules: {},
      fields: [
        {
          title: '字段名',
          key: 'key',
          props: {
            disabled: true
          }
        }, {
          title: '标题',
          key: 'title',
          props: {
            disabled: true
          }
        }, {
          title: '注释',
          key: 'comment',
          props: {
            disabled: true
          }
        }, {
          title: '索引',
          key: 'index',
          props: {
            disabled: true
          }
        }, {
          title: '宽度',
          key: 'width',
          type: 'xdoInput',
          props: {
            intDigits: 3
          },
          defaultValue: 80
        }, {
          title: '对齐',
          key: 'align',
          type: 'radioGroup',
          props: {
            options: [{
              label: 'left', title: '居左'
            }, {
              label: 'center', title: '居中'
            }, {
              label: 'right', title: '居右'
            }]
          },
          defaultValue: 'center'
        }, {
          title: '提示',
          key: 'tooltip',
          type: 'switch',
          props: {
            'true-value': true,
            'false-value': false
          },
          defaultValue: false
        }, {
          title: '默认选中',
          key: 'defaultShow',
          type: 'switch',
          props: {
            'true-value': true,
            'false-value': false
          },
          defaultValue: false
        }]
    }
  },
  methods: {
    /**
     * 默认值
     * @returns {{}}
     */
    getDefaultData() {
      return {
        key: '',
        title: '',
        comment: '',
        index: null,
        width: 80,
        align: 'center',
        tooltip: false,
        defaultShow: false
      }
    }
  }
}
