import FieldsSelect from '../../components/fields-select'

export const commConfig = {
  components: {
    FieldsSelect
  },
  props: {
    /**
     * 字段数据源(全部)
     */
    sourceFields: {
      type: Array,
      default: () => ([])
    },
    /**
     * 保存按钮loading
     */
    saveLoading: {
      type: <PERSON><PERSON>an,
      default: () => false
    }
  },
  data() {
    return {
      rules: {},
      model: {},
      fields: [],
      currField: {},
      collapsed: false,
      selectedKeys: []
    }
  },
  computed: {
    /**
     * 真正展示的字段
     * @returns {*}
     */
    showFields() {
      return this.fields.filter(field => {
        return field.hidden !== true
      })
    }
  },
  watch: {
    currField: {
      deep: true,
      immediate: true,
      handler: function (field) {
        let me = this
        me.fieldInfoClear()
        let theRules = JSON.stringify(me.rules)
        me.$set(me, 'rules', {})
        Object.keys(field).forEach(key => {
          if (Object.keys(me.model).includes(key)) {
            me.$set(me.model, key, field[key])
          }
        })
        me.$set(me, 'rules', JSON.parse(theRules))
      }
    }
  },
  methods: {
    /**
     *
     * @returns {{}}
     */
    getDefaultData() {
      return {}
    },
    /**
     * 数据清空(恢复默认值)
     */
    fieldInfoClear() {
      let me = this
      me.$set(me, 'model', me.getDefaultData())
    },
    /**
     * 选择组件收缩事件
     * @param collapsed
     */
    onCollapsedChange(collapsed) {
      let me = this
      me.$set(me, 'collapsed', collapsed)
    },
    /**
     * 选中当前行事件
     * @param row
     */
    onSelectionChange(row) {
      let me = this
      me.$set(me, 'currField', row)
    },
    /**
     * 选中行变更事件
     * @param selectedKeys
     */
    onSelectedRowsChanged(selectedKeys) {
      let me = this
      me.$set(me, 'selectedKeys', selectedKeys)
      if (typeof me.setModelConfig === 'function') {
        me.setModelConfig.call(me, selectedKeys)
      }
    },
    /**
     * 获取所有原始字段(如存在默认字段则仅返回默认字段)
     * @param originalFields
     * @param selectedKeys
     * @returns {*|Array|Array|*}
     */
    getSelOrDefaultFields(originalFields, selectedKeys) {
      if (Array.isArray(originalFields) && Array.isArray(selectedKeys)) {
        let filters = originalFields.filter(field => {
          return field.defaultShow === true
        }).map(field => {
          return field.key
        })
        if (Array.isArray(filters) && filters.length > 0) {
          return selectedKeys.filter(key => {
            return filters.includes(key)
          })
        } else {
          return selectedKeys
        }
      }
      return []
    }
  }
}
