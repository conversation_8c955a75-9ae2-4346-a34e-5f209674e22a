import { isNumber, isNullOrEmpty } from '@/libs/util'
import { deepClone } from '../../../jsx/utils/element-creater'

/**
 * 根据所有字段信息及基础查询条件解析出有效的查询条件
 * @param fields
 * @param params
 * @returns {*}
 */
const paramsAnalysis = (fields, params) => {
  // 所有字段
  let fullKeys = fields.map(field => {
    return field.key
  })
  // 所有查询结果中不包含的字段
  let extendKeys = params.filter(param => {
    return !fullKeys.includes(param.key)
  }).map(field => {
    return field.key
  }).sort()

  let rangeFields = {}
  fullKeys.forEach(key => {
    let tmpKeys = extendKeys.filter(eKey => {
      return eKey.indexOf(key) > -1
    })
    if (Array.isArray(tmpKeys) && tmpKeys.length === 2) {
      rangeFields[key] = tmpKeys
    }
  })
  return params.filter(param => {
    return fullKeys.includes(param.key)
  }).map(param => {
    let theFields = fields.filter(field => {
      return field.key === param.key
    })
    let type = 'input'
    if (Array.isArray(theFields) && theFields.length === 1) {
      type = theFields[0].componentsType
    }
    if (Object.keys(rangeFields).includes(param.key)) {
      return {
        range: true,
        key: param.key,
        type: type,
        dataType: theFields[0].type,
        title: param.title,
        comment: param.title,
        startField: rangeFields[param.key][0],
        endField: rangeFields[param.key][1]
      }
    }
    param.type = type
    param.dataType = theFields[0].type
    return param
  })
}

/**
 * 构建查询条件字段信息
 * @param originalFields
 * @param selFields
 * @param selectedKeys
 * @returns {{model, rules, fields: Array}}
 */
const frmParamCreator = (originalFields, selFields, selectedKeys) => {
  let model = {}, rules = {}, fields = []
  if (Array.isArray(selectedKeys)) {
    let baseFields = [], theFields = [], theField = {}
    selectedKeys.forEach(key => {
      baseFields = originalFields.filter(field => {
        return field.key === key
      })
      theFields = selFields.filter(field => {
        return field.key === key
      })
      if (theFields.length > 0) {
        theField = deepClone(theFields[0])
        if (baseFields.length > 0) {
          theField.type = baseFields[0].type
        } else {
          theField.type = 'input'
        }
      } else {
        if (baseFields.length > 0) {
          theField = deepClone(baseFields[0])
        }
      }
      if (!isNullOrEmpty(theField.key)) {
        // model 赋值
        if (typeof theField.defaultValue !== 'undefined') {
          model[key] = theField.defaultValue
        } else if (theField.dataType === 'number') {
          model[key] = null
        } else if (theField.dataType === 'switch') {
          if (theField.props && ([true, false].includes(theField.props['false-value']) || !isNullOrEmpty(theField.props['false-value']))) {
            model[key] = theField.props['false-value']
          } else {
            model[key] = false
          }
        } else {
          model[key] = ''
        }
        // rules 设置
        if (theField.required === true) {
          rules[key] = [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
        // 字段设置
        if (theField.disabled === true) {
          if (theField.props) {
            theField.props.disabled = true
          } else {
            theField.props = {
              disabled: true
            }
          }
        }
        if (theField.range === true) {
          theField.type = 'dateRange'
          theField.fields = [{
            key: theField.startField
          }, {
            key: theField.endField
          }]
        }
        fields.push(theField)
      }
    })
  }
  return {
    model: model,
    rules: rules,
    fields: fields
  }
}

/**
 * 查询结果列字段配置信息构建
 * @param originalFields
 * @param selFields
 * @param selectedKeys
 */
const gridColumnsCreator = (originalFields, selFields, selectedKeys) => {
  let columns = []
  if (Array.isArray(selFields)) {
    let baseFields = [], theFields = [], theField = {}
    selectedKeys.forEach(key => {
      baseFields = originalFields.filter(field => {
        return field.key === key
      })
      if (Array.isArray(baseFields) && baseFields.length === 1) {
        theFields = selFields.filter(field => {
          return field.key === key
        })
        if (Array.isArray(theFields) && theFields.length === 1) {
          theField = {
            key: theFields[0].key,
            title: baseFields[0].title,
          }
          if (isNumber(theFields[0]['width'])) {
            theField['width'] = Number(theFields[0]['width'])
          } else {
            theField['width'] = 80
          }
          if (['left', 'center', 'right'].includes(theFields[0]['align'])) {
            theField['align'] = theFields[0]['align']
          } else {
            theField['align'] = 'center'
          }
          if (theFields[0]['tooltip'] === true) {
            theField['tooltip'] = true
          }
          columns.push(theField)
        } else {
          theField = {
            width: 80,
            tooltip: false,
            align: 'center',
            key: baseFields[0].key,
            title: baseFields[0].title
          }
          columns.push(theField)
        }
      }
    })
  }
  return columns
}

/**
 * 构建详细编辑界面字段信息
 * @param originalFields
 * @param selFields
 * @param selectedKeys
 * @param emptyModels
 * @returns {{model, rules, fields: Array}}
 */
const frmDetailCreator = (originalFields, selFields, selectedKeys, emptyModels) => {
  let model = {}, rules = {}, fields = []
  if (Array.isArray(selectedKeys)) {
    let baseFields = [], theFields = [], theField = {}
    selectedKeys.forEach(key => {
      baseFields = originalFields.filter(field => {
        return field.key === key
      })
      theFields = selFields.filter(field => {
        return field.key === key
      })
      if (theFields.length > 0) {
        theField = deepClone(theFields[0])
        if (baseFields.length > 0) {
          theField.type = baseFields[0].type
        } else if (theField.type !== 'empty_formItem') {
          theField.type = 'input'
        }
      } else {
        if (baseFields.length > 0) {
          theField = deepClone(baseFields[0])
        } else if (Array.isArray(emptyModels) && emptyModels.length > 0) {
          theFields = emptyModels.filter(field => {
            return field.key === key
          })
          if (theFields.length > 0) {
            theField = deepClone(theFields[0])
          }
        }
      }
      if (!isNullOrEmpty(theField.key)) {
        if (theField.type !== 'empty_formItem') {
          // model 赋值
          if (typeof theField.defaultValue !== 'undefined') {
            model[key] = theField.defaultValue
          } else if (theField.dataType === 'number') {
            model[key] = null
          } else if (theField.dataType === 'switch') {
            if (theField.props && ([true, false].includes(theField.props['false-value']) || !isNullOrEmpty(theField.props['false-value']))) {
              model[key] = theField.props['false-value']
            } else {
              model[key] = false
            }
          } else {
            model[key] = ''
          }
          // rules 设置
          if (theField.required === true) {
            rules[key] = [{required: true, message: '不能为空!', trigger: 'blur'}]
          }
          // 字段设置
          if (theField.disabled === true) {
            if (theField.props) {
              theField.props.disabled = true
            } else {
              theField.props = {
                disabled: true
              }
            }
          }
          if (theField.range === true) {
            theField.type = 'dateRange'
            theField.fields = [{
              key: theField.startField
            }, {
              key: theField.endField
            }]
          }
          theField.type = theField.componentsType
          delete theField.componentsType
        }
        fields.push(theField)
      }
    })
  }
  return {
    model: model,
    rules: rules,
    fields: fields
  }
}

export {
  paramsAnalysis,
  frmParamCreator,
  frmDetailCreator,
  gridColumnsCreator
}
