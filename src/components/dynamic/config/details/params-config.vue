<template>
  <section>
    <Layout>
      <Sider hide-trigger collapsible :width="600" v-model="collapsed" :collapsed-width="326" style="overflow: auto; background-color: transparent; height: 600px;">
        <FieldsSelect title="查询条件" :collapsed="collapsed" :fields="sourceParams" :selFields="sortSelParams"
                      @set-model-config="onSelectedRowsChanged" @collapsedChange="onCollapsedChange"
                      @selectionChange="onSelectionChange"></FieldsSelect>
      </Sider>
      <Layout>
        <Content>
          <XdoCard :bordered="false" title="字段信息" class="custom-card" style="text-align: left;">
            <div class="card-bar">
              <XdoButton type="info" icon="ios-copy-outline" key="copy" style="right: 180px; position: relative;"
                         @click="handleCopyClipboard" :loading="reloading">复制到剪贴板</XdoButton>
              <XdoButton type="warning" icon="ios-trash-outline" @click="handleClear" style="right: 20px; position: relative;"
                         :loading="reloading">清空</XdoButton>
              <XdoButton type="primary" icon="md-square" key="saveBill" style="right: 10px; position: relative;"
                         :loading="saveLoading" @click="handleSave">保存</XdoButton>
            </div>
            <div class="card-content xdo-enter-root" v-focus>
              <DynamicForm ref="frmFields" class="dc-form-1" labelWidth="160"
                           :rules="rules" :model="model" :fields="showFields"></DynamicForm>
              <XdoCard :bordered="false" title="预览" class="custom-card-2" style="text-align: left;">
                <div class="card-bar">
                  <div style="width: 80px; padding-top: 3px; background-color: transparent;">每行列数: </div>
                  <div class="ivu-form-item-content" style="padding: 1px 10px 0 0; width: 160px; z-index: 100000;">
                    <xdo-select v-model="modelInfo.columnCount" :options="cmbSource.columnCountData"></xdo-select>
                  </div>
                </div>
                <div class="card-content-preview xdo-enter-root" v-focus>
                  <DynamicForm :class="modelClass" labelWidth="120" :rules="modelInfo.rules"
                               :model="modelInfo.model" :fields="modelInfo.fields"></DynamicForm>
                </div>
              </XdoCard>
            </div>
          </XdoCard>
        </Content>
      </Layout>
    </Layout>
  </section>
</template>

<script>
  import { setCopy } from '../js/comm/utils'
  import { frmParamCreator } from '../js/comm/fieldsCreater'
  import { paramsConfig } from '../js/components/paramsConfig'

  export default {
    name: 'paramsConfig',
    mixins: [paramsConfig],
    props: {
      sourceParams: {
        type: Array,
        default: () => ([])
      },
      selParams: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      return {
        modelInfo: {
          rules: {},
          model: {},
          fields: [],
          columnCount: 3
        },
        cmbSource: {
          columnCountData: [{
            value: 1, label: '1 列'
          }, {
            value: 2, label: '2 列'
          }, {
            value: 3, label: '3 列'
          }, {
            value: 4, label: '4 列'
          }]
        }
      }
    },
    computed: {
      sortSelParams() {
        let me = this
        let selParams = me.selParams.map(selParam => {
          let theFields = me.sourceParams.filter(param => {
            return param.key === selParam.key
          })
          if (Array.isArray(theFields) && theFields.length === 1) {
            selParam.title = theFields[0].title
            selParam.comment = theFields[0].comment
          }
          return selParam
        })
        return selParams.sort(function (p1, p2) {
          return p1.index - p2.index
        })
      },
      modelClass() {
        if (this.modelInfo.columnCount === 1) {
          return 'dc-form-1'
        } else if (this.modelInfo.columnCount === 2) {
          return 'dc-form-2'
        } else if (this.modelInfo.columnCount === 3) {
          return 'dc-form'
        } else if (this.modelInfo.columnCount === 4) {
          return 'dc-form-4'
        } else {
          return 'dc-form'
        }
      }
    },
    methods: {
      /**
       * 清空配置信息
       */
      handleClear() {
        let me = this
        me.$set(me, 'currField', {})
        me.$emit('on-paramConfig-save', {key: 'empty_empty'}, [])
        me.$refs['frmFields'].resetFields()
      },
      /**
       * 配置信息保存
       * @param afterSaveFun
       */
      handleSave(afterSaveFun) {
        let me = this
        me.$refs['frmFields'].validate().then(isValid => {
          if (isValid) {
            me.$emit('on-paramConfig-save', JSON.parse(JSON.stringify(me.model)), me.selectedKeys, afterSaveFun)
          }
        })
      },
      /**
       * 设置预览数据
       * @param selectedKeys
       */
      setModelConfig(selectedKeys) {
        let me = this
        let realObj = frmParamCreator(me.sourceParams, me.selParams, me.getSelOrDefaultFields(me.selParams, selectedKeys))
        me.$set(me.modelInfo, 'model', realObj.model)
        me.$set(me.modelInfo, 'rules', realObj.rules)
        me.$set(me.modelInfo, 'fields', realObj.fields)
      },
      /**
       * 复制到剪贴板
       */
      handleCopyClipboard() {
        let me = this
        me.handleSave(function () {
          let result = {
            sourceParams: me.sourceParams,
            selParams: me.selParams
          }
          setCopy(JSON.stringify(result))
          alert("复制成功!")
        })
      }
    }
  }
</script>

<style scoped>
  /deep/ .custom-card > .ivu-card-body {
    height: 562px;
  }

  /deep/ .custom-card-2 > .ivu-card-body {
    height: 100px;
  }

  /deep/ .ivu-card-head {
    padding: 5px 10px;
  }

  .card-bar {
    top: -30px;
    float: right;
    display: flex;
    position: relative;
  }

  /deep/ .ivu-layout-content {
    background-color: white;
    border-right: 1px solid rgb(214, 219, 222);
    border-bottom: 1px solid rgb(214, 219, 222);
  }

  .card-content {
    top: -24px;
    width: 100%;
    height: 561px;
    overflow: hidden;
    overflow-y: scroll;
    position: relative;
    padding: 4px 10px 4px 0;
  }

  .card-content-preview {
    top: -22px;
    width: 100%;
    padding: 4px 0 0 0;
    position: relative;
  }

  .dc-form-1 {
    width: 100%;
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(1, 1fr);
  }

  .dc-form-1 > div {
    grid-column: 1;
  }

  .dc-form, .dc-form-2, .dc-form-4 {
    width: 100%;
  }

  .dc-merge-1-3-line {
    font-weight: bold;
    grid-column-start: 1;
    grid-column-end: 3;
    margin: 0 0 3px 6px;
    border-bottom: 1px solid #CDCDB4;
  }

  /deep/ .ivu-form-item {
    text-align: left;
  }
</style>
