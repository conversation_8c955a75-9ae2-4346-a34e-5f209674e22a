<template>
  <table class="frmTable">
    <tr v-show="fileShow">
      <td style="width: 40px;">
        <XdoIcon size="16" type="md-close-circle" color="red" @click="removeFile(fileInfo.sid)" v-show="deleteShow"/>
      </td>
      <td style="width: calc(100% - 40px);">
        <a @click="fileDownload(fileInfo.sid, fileInfo.fileName)">{{ fileInfo.fileName }}</a>
      </td>
    </tr>
    <tr v-show="uploadShow">
      <td colspan="2">
        <Upload :action="attachInfo.uploadUrl" :headers="attachInfo.headers" :data="attachInfo.data" :show-upload-list="false"
                :before-upload="handleBeforeUpload" :on-error="handleOnUploadError" :on-success="handleOnUploadSuccess">
          <XdoButton type="primary" icon="md-cloud-upload">
            选择文件
          </XdoButton>
        </Upload>
      </td>
    </tr>
  </table>
</template>

<script>
  import { csAPI } from '@/api'
  import { util } from '@/libs'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'dcSingleAttachment',
    props: {
      /**
       * 业务单证Sid
       */
      businessSid: {
        type: String,
        default: ''
      },
      /**
       * 业务类型标识
       */
      businessType: {
        type: String,
        default: ''
      },
      /**
       * 是否可编辑
       */
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        fileInfo: {
          sid: '',
          fileName: ''
        },
        attachInfo: {
          data: {
            acmpNo: '',
            acmpType: '',
            acmpFormat: '',
            businessSid: this.businessSid,
            businessType: this.businessType
          },
          uploadUrl: csAPI.attachedInfo.insert,
          headers: {
            Authorization: 'Bearer ' + this.$store.state.token
          }
        }
      }
    },
    watch: {
      businessSid: {
        immediate: true,
        handler: function (headId) {
          let me = this
          me.$set(me.attachInfo.data, 'businessSid', headId)
          me.loadFile()
        }
      }
    },
    methods: {
      loadFile() {
        let me = this
        me.$http.post(csAPI.attachedInfo.list, {
          businessSid: me.businessSid,
          businessType: me.businessType
        }).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.fileInfo, 'sid', res.data.data[0].sid)
            me.$set(me.fileInfo, 'fileName', res.data.data[0].originFileName)
          } else {
            me.$set(me.fileInfo, 'sid', '')
            me.$set(me.fileInfo, 'fileName', '')
          }
        }).catch(() => {
        })
      },
      removeFile(fileSid) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            me.$http.delete(`${csAPI.attachedInfo.delete}/${fileSid}`).then(() => {
              me.$Message.success('删除成功!')
              me.$set(me.fileInfo, 'sid', '')
              me.$set(me.fileInfo, 'fileName', '')
            }).catch(() => {
            })
          }
        })
      },
      fileDownload(fileSid, fileName) {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.get}/${fileSid}`, {
          responseType: 'blob'
        }).then(res => {
          const name = fileName
          const blob = new Blob([res.data])
          util.blobSaveFile(blob, name)
        })
      },
      handleBeforeUpload() {
        let me = this
        me.$emit('uploadStatus', {
          uploadFailure: false,
          uploadCompleted: false,
          uploadProgressShow: true
        })
      },
      handleOnUploadError() {
        let me = this
        me.$nextTick(() => {
          me.$emit('uploadStatus', {
            uploadFailure: true,
            uploadCompleted: false,
            uploadProgressShow: true
          })
        })
      },
      handleOnUploadSuccess(response, file) {
        let me = this
        if (response.success) {
          me.$set(me.fileInfo, 'fileName', file.name)
          me.$set(me.fileInfo, 'sid', response.data.sid)
          me.$emit('uploadStatus', {
            uploadFailure: false,
            uploadCompleted: true,
            uploadProgressShow: true
          })
        } else {
          me.$nextTick(() => {
            me.$emit('uploadStatus', {
              uploadFailure: true,
              uploadCompleted: false,
              uploadProgressShow: true
            })
          })
          me.$Message.error(response.message)
        }
      }
    },
    computed: {
      fileShow() {
        let me = this
        return !isNullOrEmpty(me.fileInfo.sid)
      },
      deleteShow() {
        let me = this
        if (me.disabled) {
          return false
        }
        return !isNullOrEmpty(me.fileInfo.sid)
      },
      uploadShow() {
        let me = this
        if (me.disabled) {
          return false
        }
        return isNullOrEmpty(me.fileInfo.sid)
      }
    }
  }
</script>

<style scoped>
  .frmTable {
    margin: 0;
    width: 100%;
    display: table;
    padding: 1px 0;
    border-color: grey;
    border-spacing: 1px;
    border-collapse: separate;
  }

  .frmTable tr, .frmTable td {
    margin: 0;
    padding: 0;
    width: 100%;
  }
</style>
