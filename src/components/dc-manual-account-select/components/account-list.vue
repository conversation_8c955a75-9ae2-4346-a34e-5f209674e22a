<template>
  <XdoCard :bordered="false">
    <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" height="400" :components="components"
              @onAgCellOperation="onAgCellOperation" @onCellDoubleClicked="onDoubleClicked"
              @on-selection-change="handleSelectionChange"></DcAgGrid>
  </XdoCard>
</template>

<script>
  import { csAPI } from '@/api'
  import { manualAccountList } from '../js/manualAccountList'

  export default {
    name: 'accountList',
    mixins: [manualAccountList],
    data() {
      return {
        selectType: 'account',
        ajaxUrl: {
          selectAllPaged: csAPI.manualAccount.account.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
