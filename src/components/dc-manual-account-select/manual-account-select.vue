<template>
  <section>
    <XdoCard :bordered="false">
      <div class="custom-bread-crumb">
        <div class="ivu-breadcrumb" style="font-size: 12px;">
          <span>
            <span class="ivu-breadcrumb-item-link">
              关务
            </span>
            <span class="ivu-breadcrumb-item-separator">/</span>
          </span>
          <span>
            <span class="ivu-breadcrumb-item-link">
              手账册选择
            </span>
          </span>
          <span>
            <span style="color: red; font-weight: bold;">
              {{errMsg}}
            </span>
          </span>
        </div>
        <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
      </div>
      <div class="separateLine"></div>
<!--      <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">-->
<!--      </DynamicForm>-->
    </XdoCard>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="manualTab" label="手册选择" index="'1'">
        <ManualList ref="manualList" :params="params" @doSelect="doSelect"></ManualList>
      </TabPane>
      <TabPane name="accountTab" label="账册选择" index="'2'">
        <AccountList ref="accountList" :params="params" @doSelect="doSelect"></AccountList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty } from '@/libs/util'
  import ManualList from './components/manual-list'
  import AccountList from './components/account-list'
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

  export default {
    name: 'manualAccountSelect',
    mixins: [baseSearchConfig],
    components: {
      ManualList,
      AccountList
    },
    data() {
      let params = this.getParams()
      return {
        tabName: 'manualTab',
        tabs: {
          manualTab: true,
          accountTab: false
        },
        baseParams: [
          ...params
        ],
        errMsg: '',
        ajaxUrl: {
          checkCopEmsNo: csAPI.manualAccount.check.checkCopEmsNo
        }
      }
    },
    watch: {
      tabName(value) {
        let me = this
        me.tabs[value] = true
      }
    },
    computed: {
      params() {
        return this.getSearchParams()
      }
    },
    mounted() {
      let me = this
      me.$nextTick(() => {
        me.handleSearchSubmit()
      })
    },
    methods: {
      getParams() {
        return [{
          key: 'emsNo',
          title: '手账册号',
          itemClass: 'dc-merge-1-3'
        }]
      },
      /**
       * 选中某行
       * @param row
       * @param selectType
       */
      doSelect(row, selectType) {
        let me = this
        me.$http.post(me.ajaxUrl.checkCopEmsNo, {
          copEmsNo: row.copEmsNo
        }, {
          noIntercept: true
        }).then(res => {
          if (res.data.success === true) {
            me.$set(me, 'errMsg', '')

            // console.info('选择类型: ' + selectType + '; 选择行数据为: ' + JSON.stringify(row))
            window['majesty'].store.state[namespace].manualAccount['emsNo'] = row.emsNo
            window['majesty'].store.state[namespace].manualAccount['copEmsNo'] = row.copEmsNo
            window['majesty'].store.state[namespace].manualAccount['selectType'] = selectType
            // /**
            //  * 获取手账册选择的值【暂不执行】 Start
            //  */
            // if (selectType === 'account') {
            //   // 账册
            //   if (window['majesty'].store.state['ems']) {
            //     window['majesty'].store.state['ems']['selectedEms'] = row
            //   }
            // } else if (selectType === 'manual') {
            //   // 手册
            //   if (window['majesty'].store.state['eml']) {
            //     window['majesty'].store.state['eml']['selectedManual'] = row
            //   }
            // }
            // // 清单【此处无需赋值】
            // if (window['majesty'].store.state['bill']) {
            //   window['majesty'].store.state['bill']['selectedManual']['emsNo'] = row.emsNo
            // }
            // /**
            //  * 获取手账册选择的值【暂不执行】 End
            //  */
            if (isNullOrEmpty(row.emsNo)) {
              window.majesty.store.commit('setFooterMark', `当前账(手)册：${row.copEmsNo}`)
            } else {
              window.majesty.store.commit('setFooterMark', `当前账(手)册：${row.copEmsNo + '-' + row.emsNo}`)
            }
            me.$parent.ok()
          } else {
            me.$set(me, 'errMsg', '当前备案号物料中心不存在无法进行操作，请重新选择!')
          }
        }).catch(() => {
        })
      },
      handleSearchSubmit() {
        let me = this
        me.$nextTick(() => {
          if (me.$refs['manualList'] && typeof me.$refs['manualList'].handleSearchSubmit === 'function') {
            me.$refs['manualList'].handleSearchSubmit()
          }
          if (me.$refs['accountList'] && typeof me.$refs['accountList'].handleSearchSubmit === 'function') {
            me.$refs['accountList'].handleSearchSubmit()
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
