import { addEvent } from '@/libs/util'
import { listConfig } from '@/view/cs-manual-account-select/js/ListConfig'

function myOperateCellRenderer() {
}

myOperateCellRenderer.prototype.init = function(params) {
  let vm = undefined,
    vmParent = undefined,
    selectA = document.createElement('a'),
    divContainer = document.createElement('div'),
    fwComWrapper = params['frameworkComponentWrapper']
  if (fwComWrapper) {
    vmParent = fwComWrapper.parent
  }
  if (vmParent) {
    vmParent = vmParent.$parent
  }
  if (vmParent) {
    vm = vmParent.$parent
  }
  divContainer.setAttribute('style', 'padding: 0 0 0 6px')

  selectA.innerHTML = '选择'
  selectA.setAttribute('type', 'primary')
  addEvent(selectA, 'click', function () {
    if (vm && typeof vm.handleEditByRow === 'function') {
      vm.handleEditByRow(params.data)
    }
  })
  divContainer.appendChild(selectA)

  this.eGui = divContainer
}

myOperateCellRenderer.prototype.getGui = function() {
  return this.eGui
}

export const manualAccountList = {
  mixins: [listConfig],
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let fields = this.getFields()
    return {
      baseFields: [
        ...fields
      ],
      initSearch: false,
      components: {
        'myOperateCellRenderer': myOperateCellRenderer
      }
    }
  },
  methods: {
    getSearchParams() {
      return this.params
    },
    getFields() {
      return [{
        width: 60,
        title: '选择',
        fixed: 'left',
        customize: true,
        key: 'operation',
        cellRenderer: 'myOperateCellRenderer'
      }, {
        width: 160,
        key: 'emsNo',
        title: '手账册号'
      }, {
        width: 180,
        key: 'copEmsNo',
        title: '企业内部编号'
      }]
    },
    /**
     *
     * @param data
     */
    onDoubleClicked(data) {
      let me = this
      me.$emit('doSelect', data, me.selectType)
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      me.$emit('doSelect', row, me.selectType)
    }
  }
}
