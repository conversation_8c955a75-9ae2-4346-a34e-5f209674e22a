<template>
  <xdo-ag-grid class="dc-table" ref="agGrid" v-if="grdShow" :enableFilter="enableFilter"
               :checkboxSelection="checkboxSelection" :rowSelection="rowSelection" :suppressRowTransform="suppressRowTransform"
               :columns="grdColumns" :data="data" :height="height" :components="dynamicComponents"
               :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
               @selectionChanged="handleSelectionChange" @cellDoubleClicked="onCellDoubleClicked"
               @gridReady="gridReady" @cellFocused="handleOnCellFocused"
               @rowEditingStarted="onRowEditingStarted" @rowEditingStopped="onRowEditingStopped"
               @cellEditingStarted="onCellEditingStarted" @cellEditingStopped="onCellEditingStopped"
               @cellValueChanged="onCellValueChanged" :getRowStyle="getRowStyle"></xdo-ag-grid>
</template>

<script>
  import { cellRenderer } from './js/dc-ag-cell-render'
  import { isNumber, addEvent, isNullOrEmpty } from '@/libs/util'
  import { deepClone } from '../dynamic/jsx/utils/element-creater'
  import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

  // import { operationRenderer } from './js/dc-ag-cell-operation-render'

  // cell renderer class
  function OpCellRenderer() {
  }

  // init method gets the details of the cell to be renderer
  OpCellRenderer.prototype.init = function(params) {
    let vm = undefined,
      editDisplay = '',
      vmParent = undefined,
      viewMarginLeft = '15px',
      editA = document.createElement('a'),
      viewA = document.createElement('a'),
      divContainer = document.createElement('div'),
      fwComWrapper = params['frameworkComponentWrapper']
    if (fwComWrapper) {
      vmParent = fwComWrapper.parent
    }
    if (vmParent) {
      vmParent = vmParent.$parent
    }
    if (vmParent) {
      vm = vmParent.$parent
    }
    if (vm && typeof vm.cellEditStyle === 'function') {
      editDisplay = vm.cellEditStyle()
      viewMarginLeft = vm.cellEditStyle() === '' ? '15px' : '20px'
    }

    editA.innerHTML = '编辑'
    editA.setAttribute('type', 'primary')
    editA.style.display = editDisplay
    addEvent(editA, 'click', function () {
      if (vm && typeof vm.handleEditByRow === 'function') {
        vm.handleEditByRow(params.data)
      }
    })
    // divContainer.appendChild(editA)

    viewA.innerHTML = '查看'
    viewA.setAttribute('type', 'primary')
    viewA.style.marginLeft = viewMarginLeft
    addEvent(viewA, 'click', function () {
      if (vm && typeof vm.handleViewByRow === 'function') {
        vm.handleViewByRow(params.data)
      }
    })
    divContainer.appendChild(viewA)
    this.eGui = divContainer
  }

  OpCellRenderer.prototype.getGui = function() {
    return this.eGui
  }

  export default {
    name: 'dcAgGrid',
    mixins: [columnRender],
    props: {
      columns: {
        type: Array,
        default: () => ([])
      },
      data: {
        type: Array,
        default: () => ([])
      },
      height: {
        type: [String, Number],
        default: () => ('-')
      },
      rowSelection: {
        type: String,
        validate: function (value) {
          return ['single', 'multiple'].includes(value)
        },
        default: () => ('multiple')
      },
      disable: {
        type: Boolean,
        default: () => false
      },
      resizable: {
        type: Boolean,
        default: () => true
      },
      focusedRowSelection: {
        type: Boolean,
        default: () => false
      },
      components: {
        type: Object,
        default: () => ({})
      },
      enableFilter: {
        type: Boolean,
        default: () => false
      },
      suppressRowTransform: {
        type: Boolean,
        default: () => false
      },
      getRowStyle: {
        type: Function,
        default: ((/*params*/) => {
          // if (parseInt(params.node.id) % 2 == 0) {
          //   return {
          //     'background-color': 'Violet'     //颜色可以用英文、rgb以及十六进制
          //   }
          // }
          // else {
          //   return {
          //     'background-color': '#00BFFF'
          //   }
          // }
        })
      }
    },
    data() {
      return {
        grdShow: true,
        grdColumns: [],
        innerComponents: {},
        checkboxSelection: false,
        // overlayLoadingTemplate: '<i class="ivu-icon ivu-icon-ios-loading" style="font-size: 32px;">正在加载,请稍后......</i>',
        overlayLoadingTemplate: '<i class="ivu-icon ivu-icon-ios-loading" style="font-size: 32px; color: darkgray"></i>',
        overlayNoRowsTemplate: '<span>未查询到数据</span>'//'<span style="padding: 10px; border: 2px solid #444; background: lightgoldenrodyellow;">未查询到数据......</span>'
      }
    },
    watch: {
      columns: {
        deep: true,
        immediate: true,
        handler: function (cols) {
          let me = this
          me.$set(me, 'grdShow', false)
          // 是否有多选框
          let hasCheckCols = cols.filter(item => {
            return item.type === 'selection'
          })
          if (Array.isArray(hasCheckCols) && hasCheckCols.length > 0) {
            me.$set(me, 'checkboxSelection', true)
          } else {
            me.$set(me, 'checkboxSelection', false)
          }
          // 是否有操作列
          let hasOperationCols = cols.filter(item => {
            return item.key === 'operation'
          })
          if (Array.isArray(hasOperationCols) && hasOperationCols.length > 0 && hasOperationCols[0]['customize'] !== true) {
            me.innerComponents['operateCellRenderer'] = OpCellRenderer
          } else {
            delete me.innerComponents['operateCellRenderer']
          }

          let grdColumns = []
          cols.forEach(col => {
            let item = deepClone(col)
            if (item.type === 'selection') {
              // grdColumns.push({
              //   width: 41,
              //   fixed: 'left',
              //   headerName: '',
              //   checkboxSelection: true,
              //   headerCheckboxSelection: true
              // })
              console.info('暂取消单独选择列,用于测试!')
            } else if (item.key === 'operation') {
              if (item['customize'] === true) {
                if (isNullOrEmpty(item['cellRenderer'])) {
                  let opWidth = 88
                  if (me.checkboxSelection) {
                    opWidth = 88 + 28
                  }
                  if (isNumber(item.width)) {
                    opWidth = item.width
                  }
                  let funRender = (h, params) => {
                    console.info(params.index)
                    return h('span', '请设置render方法')
                  }
                  if (typeof item.render === 'function') {
                    funRender = item.render
                    delete item['render']
                  }
                  grdColumns.push({
                    width: opWidth,
                    title: '操作',
                    fixed: 'left',
                    key: 'operation',
                    resizable: false,
                    cellRendererFramework: cellRenderer(this, funRender)
                  })
                } else {
                  grdColumns.push(item)
                }
              } else {
                let opWidth = 88
                if (me.checkboxSelection) {
                  opWidth = 88 + 28
                }
                grdColumns.push({
                  width: opWidth,
                  title: '操作',
                  fixed: 'left',
                  key: 'operation',
                  resizable: false,
                  cellRenderer: 'operateCellRenderer'
                })
              }
            } else {
              if (isNumber(item.minWidth)) {
                item.width = item.minWidth
                delete item['minWidth']
              }
              let funRender = null
              if (typeof item.render === 'function') {
                funRender = item.render
                delete item['render']
              }
              if (item.tooltip === true) {
                if (funRender === null) {
                  funRender = (h, params) => {
                    return this.toolTipRender(h, params.row[params.column.key])
                  }
                }
              } else {
                item.tooltip = false
              }
              if (typeof funRender === 'function') {
                item.cellRendererFramework = cellRenderer(this, funRender, item.tooltip)
              }
              if (me.resizable) {
                grdColumns.push({
                  ...item,
                  resizable: true
                })
              } else {
                grdColumns.push(item)
              }
            }
          })
          me.$set(me, 'grdColumns', grdColumns)
        }
      },
      grdColumns: {
        deep: true,
        immediate: true,
        handler: function () {
          let me = this
          me.$nextTick(() => {
            me.$set(me, 'grdShow', true)
          })
        }
      }
    },
    computed: {
      api() {
        return this.getAgGrdApi()
      },
      dynamicComponents() {
        let me = this
        return {
          ...me.components,
          ...me.innerComponents
        }
      }
    },
    methods: {
      /**
       * 行选中
       * @param agGrd
       */
      handleSelectionChange(agGrd) {
        let me = this
        me.$emit('on-selection-change', agGrd.api.getSelectedRows())
      },
      /**
       * 按钮样式
       */
      cellEditStyle() {
        if (this.disable) {
          return 'none'
        }
        return ''
      },
      /**
       * 编辑行信息
       * @param row
       */
      handleEditByRow(row) {
        let me = this
        me.$emit('onAgCellOperation', {
          params: row,
          methods: 'handleEditByRow'
        })
      },
      /**
       * 查看行信息
       * @param row
       */
      handleViewByRow(row) {
        let me = this
        me.$emit('onAgCellOperation', {
          params: row,
          methods: 'handleViewByRow'
        })
      },
      /**
       * 单元格双击事件
       * @param e
       */
      onCellDoubleClicked: function (e) {
        let me = this
        me.$emit('onCellDoubleClicked', e.data)
      },
      gridReady: function (/*agGrid*/) {
        // agGrid.api.sizeColumnsToFit()
      },
      /**
       * 单元格获取焦点
       * @param e
       */
      handleOnCellFocused: function (e) {
        let me = this,
          rowIndex = e.rowIndex
        me.$emit('onCellCellFocused', e)
        if (me.focusedRowSelection) {
          if (me.api && typeof me.api.forEachNode === 'function') {
            me.api.forEachNode(function (node) {
              if (node.rowIndex === rowIndex) {
                if (me.rowSelection === 'multiple') {
                  node.setSelected(true, false)
                } else {
                  node.setSelected(true, true)
                }
              }
            })
          }
        }
      },
      /**
       * 行编辑开始
       * @param e
       */
      onRowEditingStarted(e) {
        let me = this
        me.$emit('onRowEditingStarted', e)
      },
      /**
       * 行编辑结束
       * @param e
       */
      onRowEditingStopped(e) {
        let me = this
        me.$emit('onRowEditingStopped', e)
      },
      /**
       * 单元格编辑开始
       * @param e
       */
      onCellEditingStarted(e) {
        let me = this
        me.$emit('onCellEditingStarted', e)
      },
      /**
       * 单元格编辑结束
       * @param e
       */
      onCellEditingStopped(e) {
        let me = this
        me.$emit('onCellEditingStopped', e)
      },
      onCellValueChanged(e) {
        let me = this
        me.$emit('onCellValueChanged', e)
      },
      getAgGrdApi() {
        let me = this
        if (me.$refs.agGrid) {
          if (me.$refs.agGrid.gridOptions) {
            return me.$refs.agGrid.gridOptions.api
          }
        }
        return null
      },
      /**
       * 显示正在加载......
       */
      showLoadingOverlay() {
        let me = this,
          api = me.getAgGrdApi()
        if (api && typeof api.showLoadingOverlay === 'function') {
          api.showLoadingOverlay()
        }
      },
      /**
       * 显示没有数据
       */
      showNoRowsOverlay() {
        let me = this,
          api = me.getAgGrdApi()
        if (api && typeof api.showNoRowsOverlay === 'function') {
          api.showNoRowsOverlay()
        }
      },
      /**
       * 隐藏以上两者
       */
      hideOverlay() {
        let me = this,
          api = me.getAgGrdApi()
        if (api && typeof api.hideOverlay === 'function') {
          api.hideOverlay()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  /deep/ .ivu-icon-ios-loading {
    animation: rotating 1.2s linear infinite;
    -webkit-animation: rotating 1.2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
