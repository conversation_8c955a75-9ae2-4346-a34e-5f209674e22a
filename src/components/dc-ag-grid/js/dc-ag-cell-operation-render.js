export const operationRenderer = (vm) => {
  return majesty.Vue.extend({
    render(h) {
      let me = this
      return h('div', [h('a', {
        props: {
          size: 'small',
          type: 'primary'
        },
        style: {
          display: vm.cellEditStyle()
        },
        on: {
          click: () => {
            vm.handleEditByRow(me.params.data)
          }
        }
      }, '编辑'), h('a', {
        props: {
          type: 'primary'
        },
        style: {
          marginLeft: (vm.cellEditStyle() === '' ? '15px' : '20px')
        },
        on: {
          click: () => {
            vm.handleViewByRow(me.params.data)
          }
        }
      }, '查看')])
    }
  })
}
