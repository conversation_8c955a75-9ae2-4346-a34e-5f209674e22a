<template>
  <XdoModal v-model="show" footer-hide mask
            :mask-closable="false" :closable="false">
    <div style="padding: 6px;">
      <table style="width: 100%;">
        <tr>
          <td style="vertical-align: top;">
            <div class="ivu-message-custom-content ivu-message-error">
              <i class="ivu-icon ivu-icon-ios-close-circle"></i>
            </div>
          </td>
          <td style="font-size: 14px; padding: 0 5px;">
            {{message}}
          </td>
          <td>
            <a class="ivu-message-notice-close" style="top: calc(50% - 9px);" @click="handleClose">
              <i class="ivu-icon ivu-icon-ios-close"></i>
            </a>
          </td>
        </tr>
      </table>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'innerMessagePop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      message: {
        type: String,
        default: () => ('')
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style scoped>
  /*/deep/ .ivu-modal-body {*/
  /*  padding: 1px !important;*/
  /*  background-color: #E9EBEE !important;*/
  /*}*/
</style>
