<template>
  <XdoModal width="1024" mask v-model="show" title="数据导入"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header">
      <XdoForm :label-width="100" class="model-form">
        <XdoRow style="overflow: hidden;">
          <XdoCol span="14">
            <XdoFormItem :label="uploadProps.filename">
              <div v-if="uploadProps.file">
                <XdoIInput type="text" readonly v-model="uploadProps.file.name"></XdoIInput>
              </div>
              <div v-else>
                <XdoIInput type="text" readonly></XdoIInput>
              </div>
            </XdoFormItem>
          </XdoCol>
          <XdoCol span="2">
            <Upload ref="upload" style="padding: 2px 0 0 0;" :headers="requestHeader" :show-upload-list="false"
                    :format="['xls','xlsx']" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    :action="uploadProps.uploadUrl" :data="uploadOption"
                    :before-upload="handleBeforeUpload" :on-success="handleSuccess">
              <Button icon="ios-cloud-upload-outline">请选择</Button>
            </Upload>
          </XdoCol>
          <XdoCol span="6" style="padding: 0 0 0 10px;">
            <XdoFormItem label="导入起始行: ">
              <InputNumber :max="10" :min="1" v-model="uploadOption.startRow" size="small" style="width: 60px;"></InputNumber>
            </XdoFormItem>
          </XdoCol>
          <Col span="2" style="padding: 2px 0 0 0;">
            <XdoButton type="primary" @click="upload" :loading="loadingStatus"> 校验 </XdoButton>
          </Col>
        </XdoRow>
      </XdoForm>
    </div>
    <div class="content" style="height: 424px;">
      <Tabs v-model="tabName">
        <TabPane name="tab1" label="正确数据">
          <div v-show="tabName === 'tab1'" class="content">
            <XdoTable :columns="correctData.column" :height="355" :data="correctData.data" stripe :row-class-name="rowClassName"></XdoTable>
            <div class="page">
              <XdoPage :total="correctData.total" show-total @on-change="correctPageChange"/>
            </div>
          </div>
        </TabPane>
        <TabPane name="tab2" label="错误数据">
          <div v-show="tabName === 'tab2'" class="content">
            <XdoTable :columns="errotData.column" :height="355" :data="errotData.data" stripe :row-class-name="rowClassName"></XdoTable>
            <div class="page">
              <XdoPage :total="errotData.total" show-total @on-change="errorPageChange"/>
            </div>
          </div>
        </TabPane>
        <Button type="primary" @click="handleTemp" size="small" slot="extra">模板下载</Button>&nbsp;
        <span>&nbsp;</span>
        <Button type="primary" @click="handleImp" size="small" slot="extra" :disabled="btnImportDisable">正确数据导入</Button>&nbsp;
        <span>&nbsp;</span>
        <Button type="primary" @click="handleExp" size="small" slot="extra">错误数据导出</Button>
      </Tabs>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import excel from '@/libs/excel_old'
  import { isNullOrEmpty } from '@/libs/util'
  import { getBlob, saveAs } from '@/libs/download'

  export default {
    data() {
      return {
        uploadProps: {
          filename: '',
          file: null,
          uploadUrl: '',
          insertUrl: ''
        },
        uploadOption: {
          importType: '',
          startRow: 5,
          headId: '',
          mark: '1',
          emsNo: '',
          copEmsNo: ''
        },
        importOptions: {
          importType: '',
          tempOwnerId: ''
        },
        requestHeader: {
          Authorization: 'Bearer ' + this.$store.state.token
        },
        loadingStatus: false,
        tabName: 'tab1',
        tabs: {
          tab1: true,
          tab2: false
        },
        limit: 10,
        correctData: {
          column: [],
          page: 1,
          total: 0,
          full: [],
          data: []
        },
        errotData: {
          column: [],
          page: 1,
          total: 0,
          full: [],
          data: []
        },
        btnImportDisable: true
      }
    },
    watch: {
      show(value) {
        if (value) {
          this.assignment()
        }
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      uploadConfig: {
        type: Object,
        require: true,
        default: () => ({
          insertUrl: '',
          uploadUrl: '',
          filename: '',
          importType: ''
        })
      },
      headId: {
        type: String,
        require: true
      }
    },
    mounted() {
      this.assignment()
    },
    methods: {
      assignment() {
        let me = this
        me.uploadProps = Object.assign({}, me.uploadConfig)
        me.uploadOption.headId = me.headId
        me.uploadOption.importType = me.uploadConfig.importType
        me.importOptions.importType = me.uploadConfig.importType
      },
      valid() {
        let me = this
        if (me.uploadProps.file) {
          if (me.uploadProps.file.size > 5120000) {
            me.$Message.warning('文件 ' + me.uploadProps.file.name + ' 太大，不能超过 5M.')
            return false
          }
          let fileType = me.uploadProps.file.type
          let typeOne = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          let typeTwo = 'application/vnd.ms-excel'
          if (typeOne !== fileType && typeTwo !== fileType) {
            me.$Message.warning('文件 ' + me.uploadProps.file.name + ' 格式不正确, 请上传 xls 或 xlsx 格式的Excel文件。')
            return false
          }
          return true
        } else {
          me.$Message.warning('校验前请先导入文件')
        }
        return false
      },
      handleBeforeUpload(file) {
        let me = this
        me.$set(me.uploadProps, 'file', file)
        me.valid()
        return false
      },
      upload() {
        let me = this
        if (me.valid()) {
          me.$set(me, 'loadingStatus', true)
          me.$refs.upload.post(me.uploadProps.file)
        }
      },
      dataDealWith(data, type) {
        let me = this
        let pageInfo
        let count = 0
        let pageData = []
        for (let item of data) {
          if (count % me.limit === 0) {
            if (pageInfo !== undefined) {
              pageData.push(pageInfo)
            }
            pageInfo = {
              pageIndex: ((count / me.limit) + 1),
              data: []
            }
          }
          pageInfo.data.push(item)
          count++
        }
        if (count % me.limit > 0 || (pageInfo !== undefined && pageData.length === 0)) {
          pageData.push(pageInfo)
        }
        if (type === 1) {
          me.correctData.total = count
          me.correctData.full = pageData
          me.correctPageChange(1)
        } else {
          me.errotData.total = count
          me.errotData.full = pageData
          me.errorPageChange(1)
        }
      },
      resultAnalysis(result) {
        let me = this,
          headerData = result.data.head
        if (headerData) {
          let headArr = []
          let errHeadArr = []
          for (let head of headerData) {
            if (head.key !== 'TEMP_REMARK' && head.key !== 'tempRemark') {
              headArr.push({
                title: head.value,
                key: head.key,
                tooltip: true,
                width: 120
              })
              errHeadArr.push({
                title: head.value,
                key: head.key,
                tooltip: true,
                width: 120
              })
            } else {
              errHeadArr.push({
                title: head.value,
                key: head.key,
                tooltip: true,
                width: 200
              })
            }
          }
          me.importOptions.tempOwnerId = result.data.tempOwnerId
          me.correctData.column = headArr
          me.errotData.column = errHeadArr
        }
        me.dataDealWith(result.data.correctList, 1)
        me.dataDealWith(result.data.wrongList, 0)
        console.info('校验完成!')
      },
      /**
       * 因为上传过程为实例，这里模拟添加 url
       * @param response
       */
      handleSuccess(response) {
        let me = this
        me.loadingStatus = false
        if (response.success === true) {
          me.$set(me, 'btnImportDisable', false)
          me.resultAnalysis(response)
        } else {
          me.$set(me, 'btnImportDisable', true)
          me.$Message.warning(response.message)
        }
      },
      correctPageChange(page) {
        let me = this
        if (!(me.correctData.full && me.correctData.full.length >= page)) {
          page = 1
        }
        me.correctData.page = page
        if (me.correctData.full.length > 0) {
          me.correctData.data = me.correctData.full[page - 1].data
        } else {
          me.correctData.data = []
        }
      },
      errorPageChange(page) {
        let me = this
        if (!(me.errotData.full && me.errotData.full.length >= page)) {
          page = 1
        }
        me.errotData.page = page
        if (me.errotData.full.length > 0) {
          me.errotData.data = me.errotData.full[page - 1].data
        } else {
          me.errotData.data = []
        }
      },
      handleTemp() {
        let me = this
        if (me.uploadProps.filename === '基础信息') {
          getBlob(csAPI.importFilePath.baseInfoImportTemplate).then(blob => {
            saveAs(blob, '基础信息导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '非保税预归类') {
          getBlob(csAPI.importFilePath.noBondImportTemplate).then(blob => {
            saveAs(blob, '非保税预归类导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '提单表体') {
          getBlob(csAPI.importFilePath.erpBodyImportTemplate).then(blob => {
            saveAs(blob, '提单表体导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '物料涉证维护') {
          getBlob(csAPI.importFilePath.certificateImportTemplate).then(blob => {
            saveAs(blob, '物料涉证维护导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '证件台账') {
          getBlob(csAPI.importFilePath.certificateImportTemplate).then(blob => {
            saveAs(blob, '证件台账导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '证件台账表体') {
          getBlob(csAPI.importFilePath.certificateImportTemplate).then(blob => {
            saveAs(blob, '证件台账表体导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '企业成品修改') {
          getBlob(csAPI.importFilePath.bondUpdateImportTemplate).then(blob => {
            saveAs(blob, '企业成品修改导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '企业料件修改') {
          getBlob(csAPI.importFilePath.bondUpdateImportTemplate).then(blob => {
            saveAs(blob, '企业料件修改导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '企业进口数据') {
          getBlob(csAPI.importFilePath.midImportTemplate).then(blob => {
            saveAs(blob, '企业进口数据导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '企业出口数据') {
          getBlob(csAPI.importFilePath.midImportTemplate).then(blob => {
            saveAs(blob, '企业出口数据导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '保税料件待归类') {
          getBlob(csAPI.importFilePath.MaterialForClassifiedTemplate).then(blob => {
            saveAs(blob, '保税料件待归类导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '保税成品待归类') {
          getBlob(csAPI.importFilePath.MaterialForClassifiedTemplate).then(blob => {
            saveAs(blob, '保税成品待归类导入模板.xlsx')
          })
        } else if (me.uploadProps.filename === '非保税待归类') {
          getBlob(csAPI.importFilePath.MaterialForClassifiedTemplate).then(blob => {
            saveAs(blob, '非保税待归类导入模板.xlsx')
          })
        }
      },
      handleImp() {
        let me = this
        if (me.correctData.data.length > 0 && !isNullOrEmpty(me.importOptions.tempOwnerId)) {
          me.$http.post(me.uploadProps.insertUrl, {
            mark: me.uploadOption.mark,
            ...me.importOptions
          }).then(res => {
            me.correctData.data = []
            me.correctData.total = 0
            me.$Message.success(res.data.message)
            me.$emit('import:success')
          })
        } else {
          me.$Message.warning('没有正确数据可供导入')
        }
      },
      handleExp() {
        let me = this
        if (me.errotData.full.length > 0) {
          let fullData = []
          for (let pageData of me.errotData.full) {
            for (let item of pageData.data) {
              fullData.push(item)
            }
          }
          const params = {
            title: me.errotData.column.map(item => {
              if (item.title) {
                return item.title
              }
            }),
            key: me.errotData.column.map(item => {
              if (item.key) {
                return item.key
              }
            }),
            data: fullData,
            autoWidth: true,
            filename: me.uploadProps.filename + '错误数据'
          }
          excel.export_array_to_excel(params)
        } else {
          me.$Message.warning('没有错误数据可供导出!')
        }
      },
      handleClose() {
        let me = this
        me.correctData = {
          column: [],
          page: 1,
          total: 0,
          full: [],
          data: []
        }
        me.errotData = {
          column: [],
          page: 1,
          total: 0,
          full: [],
          data: []
        }
        me.loadingStatus = false
        me.uploadProps.file = null
        me.$emit('update:show', false)
      },
      rowClassName() {
        return 'myRow'
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }

  .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    border-color: #57a3f3;
    background-color: #57a3f3;
  }
</style>
