<template>
  <XdoFormItem :class="className" :label="label" style="padding-right: 0 !important;">
    <span v-if="slotClass !== ''" slot="label" :class="slotClass">{{label}}</span>
    <div class="rangeContainer">
      <XdoFormItem prop="dateFrom" class="side">
        <XdoDatePicker type="date" :options="fromOptions" :disabled="disabled" @on-change="fromDateChange" placeholder="请选择开始时间" v-model="dateFrom"></XdoDatePicker>
      </XdoFormItem>
      <div class="middleStyle">-</div>
      <XdoFormItem prop="dateTo" class="side">
        <XdoDatePicker type="date" :options="toOptions" :disabled="disabled" @on-change="toDateChange" placeholder="请选择结束时间" v-model="dateTo"></XdoDatePicker>
      </XdoFormItem>
    </div>
  </XdoFormItem>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'dcDateRange',
    data() {
      return {
        dateFrom: '',
        dateTo: '',
        fromOptions: {},  // 开始日期设置
        toOptions: {},    // 结束日期设置
      }
    },
    props: {
      label: {
        type: String,
        default: () => ('日期范围')
      },
      className: {
        type: String,
        default: () => ('')
      },
      fieldNames: {
        type: Array,
        default: () => ([])
      },
      disabled: {
        type: Boolean,
        default: () => false
      },
      slotClass: {
        type: String,
        default: () => ('')
      },
      values: {
        type: Array,
        default: () => (['', ''])
      }
    },
    watch: {
      dateFrom: function (val) {
        let me = this
        me.$emit('onDateRangeChanged', [val, me.dateTo], me.realFields)
      },
      dateTo: function (val) {
        let me = this
        me.$emit('onDateRangeChanged', [me.dateFrom, val], me.realFields)
      },
      values: {
        immediate: true,
        handler: function (values) {
          if (Array.isArray(values)) {
            let me = this,
              startDate = '',
              endDate = ''
            if (values.length > 0) {
              try {
                if (!isNullOrEmpty(values[0])) {
                  startDate = (new Date(values[0])).format('yyyy-MM-dd')
                }
              } catch (e) {
                startDate = ''
                console.info('起始时间错误: ' + e.message)
              }
              me.$set(me, 'dateFrom', startDate)
            }
            if (values.length > 1) {
              try {
                if (!isNullOrEmpty(values[1])) {
                  endDate = (new Date(values[1])).format('yyyy-MM-dd')
                }
              } catch (e) {
                endDate = ''
                console.info('截止时间错误: ' + e.message)
              }
              me.$set(me, 'dateTo', endDate)
            }
          }
        }
      }
    },
    methods: {
      fromDateChange: function (e) { //设置开始时间
        let me = this,
          startTime = ''
        me.dateFrom = e
        if (me.dateFrom) {
          let tmpDate = new Date(me.dateFrom)
          startTime = tmpDate.setDate(tmpDate.getDate() - 1).valueOf()
        }
        me.toOptions = {
          disabledDate: date => {
            if (startTime === '') {
              return ''
            }
            return date && (date.valueOf() < startTime)
          }
        }
      },
      toDateChange: function (e) { //设置结束时间
        let me = this,
          endTime = ''
        me.dateTo = e
        if (me.dateTo) {
          let tmpDate = new Date(me.dateTo)
          endTime = tmpDate.setDate(tmpDate.getDate()).valueOf()
        }
        me.fromOptions = {
          disabledDate: date => {
            if (endTime === '') {
              return ''
            }
            return date && date.valueOf() > endTime
          }
        }
      }
    },
    computed: {
      realFields() {
        let me = this,
          dateFrom = 'dateFrom',
          dateTo = 'dateTo'
        if (Array.isArray(me.fieldNames) && me.fieldNames.length === 2) {
          if (isNullOrEmpty(me.fieldNames[0]) !== true) {
            dateFrom = me.fieldNames[0]
          } else {
            console.error('未设置开始时间字段')
          }
          if (isNullOrEmpty(me.fieldNames[1]) !== true) {
            dateTo = me.fieldNames[1]
          } else {
            console.error('未设置截止时间字段')
          }
        }
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style lang="less" scoped>
  .preRequired:before {
    color: blue;
    content: '*';
    font-size: 12px;
    line-height: 1px;
    margin-right: 4px;
    display: inline-block;
    font-family: 'SimSun';
  }

  .rangeContainer {
    margin: 0;
    padding: 0;
    display: flex;
    display: -o-flex;
    display: -ms-flex;
    display: -moz-flex;
    display: -webkit-flex;
  }

  .side {
    flex: 1;
    margin: 0 !important;
    padding: 0 !important;
  }

  .middleStyle {
    width: 20px;
    padding-top: 2px;
    text-align: center;
  }
</style>
