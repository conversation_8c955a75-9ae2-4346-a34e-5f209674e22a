<template>
  <section>
    <Select ref="cmb" v-model="publicValue" transfer filterable clearable :disabled="disabled" :loading="loading"
            remote :remote-method="handleRemote" @on-query-change="onQueryChange" @on-open-change="onOpenChange" @on-change="handleChange">
      <Option v-for="item in currSource" :value="item.value" :key="item.value">
        <template v-if="onlyShowValue">{{ item.value }}</template><template v-else>{{ item.value }} {{ item.label }}</template>
      </Option>
    </Select>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'remoteSelect',
    props: {
      value: {
        type: [String, Number, Array],
        default: ''
      },                    // 绑定的值
      disabled: {
        type: Boolean,
        default: false
      },                    // 是否可编辑
      doQuery: {
        type: Boolean,
        default: true
      },                    // 是否可远程查询
      options: {
        type: Object,
        default: () => ({
          limit: 20,
          label: 'label',
          value: 'value',
          ajaxUrl: '',
          fieldName: '',
          params: {}
        })
      }
    },
    data () {
      return {
        publicValue: '',
        currSource: [],
        loading: false,
        filter: false,
        keepValue: '',
        currParams: {}
      }
    },
    watch: {
      value: {
        immediate: true,
        handler: function(val) {
          let me = this
          me.$set(me, 'publicValue', val)
          me.$set(me, 'keepValue', val)
          if (me.$refs.cmb) {
            if (isNullOrEmpty(me.$refs.cmb.query)) {
              me.handleRemote('')
            }
          }
        }
      },
      'options.params': {
        deep: true,
        immediate: true,
        handler: function (params) {
          let me = this
          if (!me.objComparison(params, me.currParams)) {
            me.handleRemote(me.publicValue)
          }
        }
      }
    },
    mounted: function () {
      let cmbInput = this.$refs.cmb.$children[0].$refs.input
      if (cmbInput.addEventListener) {
        cmbInput.addEventListener('blur', this.cmdBlur, false)
        cmbInput.addEventListener('focus', this.cmdOnFocus, false)
        cmbInput.addEventListener('keydown', this.handleEnter, false)
      } else if (cmbInput.attachEvent) {
        cmbInput.attachEvent('onblur', this.cmdBlur)
        cmbInput.attachEvent('onfocus', this.cmdOnFocus)
        cmbInput.attachEvent('onkeydown', this.handleEnter)
      } else {
        cmbInput['onblur'] = this.cmdBlur
        cmbInput['onfocus'] = this.cmdOnFocus
        cmbInput['onkeydown'] = this.handleEnter
      }
    },
    methods: {
      /**
       * 模拟远程过滤
       * @param query
       */
      handleRemote (query) {
        let me = this
        if(!me.loading){
          if (!me.doQuery) {
            // console.info('doQuery拦截 ==> query: ' + query + '; value: ' + me.publicValue + ' 参数: ' + JSON.stringify(me.options.params))
            return
          }
          if (isNullOrEmpty(me.options.ajaxUrl)) {
            me.$set(me, 'currSource', [])
            // console.error('未设置远程访问地址【options.ajaxUrl】')
          } else if (isNullOrEmpty(me.options.fieldName)) {
            me.$set(me, 'currSource', [])
            // console.error('未设置当前字段名【options.fieldName】')
          } else {
            // console.info('当前传入值: ' + query)
            let myParams = {
              ...me.options.params
            }
            let realVal = ''
            if (!isNullOrEmpty(query)) {
              realVal = query.trim()
            }
            myParams[me.options.fieldName] = realVal
            // me.$set(me, 'loading', true)
            me.$set(me, 'currParams', JSON.parse(JSON.stringify(me.options.params)))
            me.$http.post(me.options.ajaxUrl, myParams, {
              params: {
                page: 1,
                dataTotal: 1,
                limit: me.options.limit
              }
            }).then(res => {
              let list = res.data.data.map(item => {
                if (typeof item === 'string') {
                  return {
                    value: item,
                    label: item
                  }
                } else {
                  return {
                    value: item[me.options.value],
                    label: item[me.options.label]
                  }
                }
              })
              if (list.length === 0 && !isNullOrEmpty(me.keepValue)) {
                list.push({
                  value: me.keepValue,
                  label: me.keepValue
                })
              }
              me.$set(me, 'currSource', list)
              // console.info('当前数据: ' + JSON.stringify(list))
              let existsVals = list.filter(item => {
                return item.value === realVal
              })

              if (Array.isArray(existsVals) && existsVals.length > 0) {
                me.handleChange(realVal)
              } else {
                me.$set(me, 'publicValue', '')
                me.handleChange('')
              }
            }).catch(() => {
              me.$set(me, 'currSource', [])
            }).finally(() => {
              me.$set(me, 'loading', false)
            })
          }
        }
      },
      onQueryChange (query) {
        this.filter = !isNullOrEmpty(query)
      },
      /**
       * 选中事项
       * @param value
       */
      handleChange (value) {
        let me = this
        me.$emit('input', value)
        me.$emit('on-change', value)
      },
      onOpenChange (open) {
        if (!open) {
          this.filter = false
        }
      },
      /**
       * 焦点离开事件
       */
      cmdBlur () {
        let me = this
        if (isNullOrEmpty(me.publicValue)) {
          if (me.filter) {
            if (Array.isArray(me.currSource) && me.currSource.length > 0) {
              if (me.$refs.cmb.focusIndex > -1 && me.currSource.length > me.$refs.cmb.focusIndex) {
                me.handleChange(me.currSource[me.$refs.cmb.focusIndex].value)
              } else {
                // me.handleChange(me.currSource[0].value)
                if(me.$refs.cmb) {
                  me.$refs.cmb.clearSingleSelect()
                }
                me.$nextTick(() => {
                  me.handleRemote('')
                })
              }
            }
          } else {
            if(me.$refs.cmb) {
              me.$refs.cmb.clearSingleSelect()
            }
            me.$nextTick(() => {
              me.handleRemote('')
            })
          }
        }
      },
      cmdOnFocus () {
        // if (isNullOrEmpty(this.publicValue) && this.currSource.length === 0) {
        //   this.handleRemote('')
        // }
      },
      handleEnter (e) {
        if (e.keyCode === 13) {
          this.cmdBlur()
        }
      },
      /**
       * 对象比较
       * @param objA
       * @param objB
       */
      objComparison (objA, objB) {
        if (typeof objA === 'object' && typeof objB === 'object') {
          // let propsA = Object.keys(objA)
          // let propsB = Object.keys(objB)
          // if (propsA.length === propsB.length) {
          //   let propsLength = propsA.length
          //   let sameLength = 0
          //   let propName
          //   for (let i = 0; i < propsLength; i++) {
          //     propName = propsA[i]
          //     if (objA.hasOwnProperty(propName) && objB.hasOwnProperty(propName)) {
          //       if (objA[propName] === objB[propName]) {
          //         sameLength++
          //       }
          //     }
          //   }
          //   if (propsLength === sameLength) {
          //     return true
          //   }
          // }
          if (JSON.stringify(objA) === JSON.stringify(objB)) {
            return true
          }
        }
        return false
      }
    },
    computed: {
      onlyShowValue () {
        return this.options.label === this.options.value
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
