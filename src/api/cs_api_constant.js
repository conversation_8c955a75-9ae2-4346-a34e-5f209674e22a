/*
* 关务 访问后端接口地址
* */
const baseUri = '/gwstd/api'
//const exportUri = '/gwstd/api/exp'
//const importUri = '/gwstd/api/imp'
//const printUri = '/gwstd/api/print'

const frtBaseUri = process.env.BASE_URL       // '/gw'

import { namespace } from '@/project'

/**
 * swagger
 * @type {{}}
 */
export const swaggerUi = {
  apiDocs: `${baseUri}/v2/api-docs`
}

/**
 * 基础资料接口
 * @type {{enterpriseInfo: {detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, delete: string}}, productionUnit: {selectList: string, selectAllPaged: string, insert: string, update: string, delete: string}, outsourcingContractor: {detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, delete: string}}, clientInfo: {detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, copyprd: string, delete: string}, shipTo: {allShipTo: string, selectAllPaged: string, exportUrl: string, insert: string, update: string, getShipToCodeName: string, delete: string}}, customsBrokerInfo: {detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, delete: string}}, cargoAgentInfo: {detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, delete: string}}, useStatus: string, electronicSeal: {selectAllPaged: string, get: string, insert: string, delete: string}, supplierInfo: {shipFrom: {selectAllPaged: string, exportUrl: string, insert: string, update: string, getShipFromCodeName: string, delete: string, allshipFrom: string}, detail: {insert: string, update: string}, list: {selectAllPaged: string, exportUrl: string, delete: string, copycli: string}}}}
 */
export const csBaseInfo = {
  useStatus: `${baseUri}/v1/biClientInformation/setMatStatus`,
  enterpriseInfo: {
    list: {
      delete: `${baseUri}/v1/biClientInformation`,
      exportUrl: `${baseUri}/v1/biClientInformation/export`,
      selectAllPaged: `${baseUri}/v1/biClientInformation/list`
    },
    detail: {
      insert: `${baseUri}/v1/biClientInformation`,
      update: `${baseUri}/v1/biClientInformation`
    }
  },
  productionUnit: {
    insert: `${baseUri}/v1/biClientDec`,
    update: `${baseUri}/v1/biClientDec`,
    delete: `${baseUri}/v1/biClientDec`,
    selectAllPaged: `${baseUri}/v1/biClientDec/list`,
    selectList: `${baseUri}/v1/biClientDec/selectList`
  },
  // 电子签章
  electronicSeal: {
    get: `${baseUri}/v1/gwstdSeal`,
    insert: `${baseUri}/v1/gwstdSeal`,
    delete: `${baseUri}/v1/gwstdSeal`,
    selectAllPaged: `${baseUri}/v1/gwstdSeal/list`
  },
  cargoAgentInfo: {
    list: {
      delete: `${baseUri}/v1/biClientInformation`,
      exportUrl: `${baseUri}/v1/biClientInformation/export`,
      selectAllPaged: `${baseUri}/v1/biClientInformation/list`
    },
    detail: {
      insert: `${baseUri}/v1/biClientInformation`,
      update: `${baseUri}/v1/biClientInformation`
    }
  },
  customsBrokerInfo: {
    list: {
      delete: `${baseUri}/v1/biClientInformation`,
      exportUrl: `${baseUri}/v1/biClientInformation/export`,
      selectAllPaged: `${baseUri}/v1/biClientInformation/list`,
      selectIfFreeEntrusted: `${baseUri}/v1/biClientInformation/selectIfFreeEntrusted`
    },
    detail: {
      insert: `${baseUri}/v1/biClientInformation`,
      update: `${baseUri}/v1/biClientInformation`
    }
  },
  clientInfo: {
    list: {
      delete: `${baseUri}/v1/biClientInformation`,
      copyprd: `${baseUri}/v1/biClientInformation/copyPrd`,
      exportUrl: `${baseUri}/v1/biClientInformation/export`,
      selectAllPaged: `${baseUri}/v1/biClientInformation/list`
    },
    detail: {
      insert: `${baseUri}/v1/biClientInformation`,
      update: `${baseUri}/v1/biClientInformation`
    },
    shipTo: {
      insert: `${baseUri}/v1/biShipto`,
      update: `${baseUri}/v1/biShipto`,
      delete: `${baseUri}/v1/biShipto`,
      exportUrl: `${baseUri}/v1/biShipto/export`,
      allShipTo: `${baseUri}/v1/biShipto/listAll`,
      selectAllPaged: `${baseUri}/v1/biShipto/list`,
      getShipToCodeName: `${baseUri}/v1/biShipto/selectComboxByCode`
    },
    billTo: {
      insert: `${baseUri}/v1/biBillto`,
      update: `${baseUri}/v1/biBillto`,
      delete: `${baseUri}/v1/biBillto`,
      exportUrl: `${baseUri}/v1/biBillto/export`,
      selectAllPaged: `${baseUri}/v1/biBillto/list`,
      getBillTo: `${baseUri}/v1/biBillto/selectComboxByCode`
    },
    notify: {
      insert: `${baseUri}/v1/biNotify`,
      update: `${baseUri}/v1/biNotify`,
      delete: `${baseUri}/v1/biNotify`,
      exportUrl: `${baseUri}/v1/biNotify/export`,
      selectAllPaged: `${baseUri}/v1/biNotify/list`,
      getNotify: `${baseUri}/v1/biNotify/selectComboxByCode`
    }
  },
  supplierInfo: {
    list: {
      delete: `${baseUri}/v1/biClientInformation`,
      copycli: `${baseUri}/v1/biClientInformation/copyCli`,
      copyFod: `${baseUri}/v1/biClientInformation/copyFod`,
      exportUrl: `${baseUri}/v1/biClientInformation/export`,
      selectAllPaged: `${baseUri}/v1/biClientInformation/list`
    },
    detail: {
      insert: `${baseUri}/v1/biClientInformation`,
      update: `${baseUri}/v1/biClientInformation`
    },
    shipFrom: {
      delete: `${baseUri}/v1/biShipfrom`,
      insert: `${baseUri}/v1/biShipfrom`,
      update: `${baseUri}/v1/biShipfrom`,
      exportUrl: `${baseUri}/v1/biShipfrom/export`,
      allshipFrom: `${baseUri}/v1/biShipfrom/listAll`,
      selectAllPaged: `${baseUri}/v1/biShipfrom/list`,
      getShipFromCodeName: `${baseUri}/v1/biShipfrom/selectComboxByCode`
    }
  },
  outsourcingContractor: {
    list: {
      delete: `${baseUri}/v1/biOutEnterprise`,
      exportUrl: `${baseUri}/v1/biOutEnterprise/export`,
      selectAllPaged: `${baseUri}/v1/biOutEnterprise/list`
    },
    detail: {
      insert: `${baseUri}/v1/biOutEnterprise`,
      update: `${baseUri}/v1/biOutEnterprise`
    }
  }
}

/**
 * 待归类物料
 * @type {{filterMaterial: {restore: string, selectAllPaged: string, exportUrl: string, deleteUrl: string}, nonBonded: {getExtraction: string, selectAllPaged: string, exportUrl: string, bondedConvert: string, chassiFy: string, delete: string, sendToFilter: string}, interfaceData: {selectAllPaged: string, exportUrl: string, sendToFilter: string, sendForClassifiy: string}, bondedMaterials: {getExtraction: string, getMatch: string, selectAllPaged: string, exportUrl: string, bondedConvert: string, delete: string, sendToFilter: string, march: string, addSerialNo: string}, details: {insert: string, update: string}, translateType: string, bondedProducts: {getExtraction: string, getMatch: string, selectAllPaged: string, exportUrl: string, bondedConvert: string, delete: string, sendToFilter: string, march: string, addSerialNo: string}, transLate: string}}
 */
export const csMaterialForClassified = {
  transLate: `${baseUri}/v1/convert/translateToSimpleChinese`,
  translateType: `${baseUri}/v1/convert/selectTranslateConfigByBillType`,
  interfaceData: {
    selectAllPaged: `${baseUri}/v1/matImgexgClassify/list`,
    exportUrl: `${baseUri}/v1/matImgexgClassify/export`,
    sendForClassifiy: `${baseUri}/v1/matImgexgClassify/getFilter/`,
    sendToFilter: `${baseUri}/v1/matImgexgClassify/getFilter/`
  },
  bondedMaterials: {
    delete: `${baseUri}/v1/matImgexgClassify`,
    selectAllPaged: `${baseUri}/v1/matImgexgClassify/list`,
    exportUrl: `${baseUri}/v1/matImgexgClassify/export`,
    sendToFilter: `${baseUri}/v1/matImgexgClassify/getFilter`,
    march: `${baseUri}/v1/matImgexgClassify`,
    addSerialNo: `${baseUri}/v1/matImgexgClassify/insertserialNo`,         // 新增备案序号
    bondedConvert: `${baseUri}/v1/matImgexgClassify/upadateBonmark`,       // 转为非保税料件
    getMatch: `${baseUri}/v1/matImgexgClassify/getMatch`,                  // 直接匹配
    getExtraction: `${baseUri}/v1/matImgexgClassify/getExtraction`         // 直接提取
  },
  bondedProducts: {
    delete: `${baseUri}/v1/matImgexgClassify`,
    selectAllPaged: `${baseUri}/v1/matImgexgClassify/list`,
    exportUrl: `${baseUri}/v1/matImgexgClassify/export`,
    sendToFilter: `${baseUri}/v1/matImgexgClassify/getFilter`,
    march: `${baseUri}/v1/matImgexgClassify`,
    addSerialNo: `${baseUri}/v1/matImgexgClassify/insertserialNo`,         // 新增备案序号
    bondedConvert: `${baseUri}/v1/matImgexgClassify/upadateBonmark`,       // 转为非保税成品
    getMatch: `${baseUri}/v1/matImgexgClassify/getMatch`,                  // 直接匹配
    getExtraction: `${baseUri}/v1/matImgexgClassify/getExtraction`         // 直接提取
  },
  nonBonded: {
    delete: `${baseUri}/v1/matImgexgClassify`,
    selectAllPaged: `${baseUri}/v1/matImgexgClassify/list`,
    exportUrl: `${baseUri}/v1/matImgexgClassify/export`,
    sendToFilter: `${baseUri}/v1/matImgexgClassify/getFilter`,
    bondedConvert: `${baseUri}/v1/matImgexgClassify/upadateBonmark`,        // 转为保税料件/成品
    chassiFy: `${baseUri}/v1/matImgexgClassify/getMatchNoBond`,
    getExtraction: `${baseUri}/v1/matImgexgClassify/getExtraction`         // 直接提取
  },
  filterMaterial: {
    selectAllPaged: `${baseUri}/v1/matImgexgClassify/list`,
    exportUrl: `${baseUri}/v1/matImgexgClassify/export`,
    deleteUrl: `${baseUri}/v1/matImgexgClassify`,
    restore: `${baseUri}/v1/matImgexgClassify/getFilter`
  },
  details: {
    insert: `${baseUri}/v1/matImgexgClassify`,
    update: `${baseUri}/v1/matImgexgClassify`
  }
}

/**
 * 商品归类接口
 * @type {{bonded: {addEmlHead: string, selectAllPaged: string, getEmlHeadList: string, insert: string, update: string, pickUp: string, delete: string, getCopEmsNoOld: string, getZtythEmsListNo: string, setStatus: string, changeSelectAllPaged: string, changeUpdate: string, getEmlHeadInfo: string, getListForMatAuditPaged: string, getEmsNoSelect: string, getFactorRate: string, exportUrl: string, getZtythCopEmsNo: string, copy: string}, nonBonded: {getMatNonCopEmsNo: string, getOrgList: string, selectAllPaged: string, exportUrl: string, insert: string, update: string, copy: string, delete: string, setStatus: string, hsValid: string}, statusLog: {selectAllPaged: string}}}
 */
export const csProductClassify = {
  bonded: {
    insert: `${baseUri}/v1/matImgexg`,
    update: `${baseUri}/v1/matImgexg`,
    delete: `${baseUri}/v1/matImgexg`,
    selectAllPaged: `${baseUri}/v1/matImgexg/list`,
    getListForMatAuditPaged: `${baseUri}/v1/matImgexg/listForMatAudit`,
    exportUrl: `${baseUri}/v1/matImgexg/export`,
    getEmsNoSelect: `${baseUri}/v1/matImgexg/selectEmsNo`,
    getZtythEmsListNo: `${baseUri}/v1/ztythEmsImgexg/getZtythEmsNo`,//
    getZtythCopEmsNo: `${baseUri}/v1/ztythEmsImgexg/getZtythCopEmsNo`,//
    getEmlHeadInfo: `${baseUri}/v1/ztythEmsImgexg/getEmlHeadInfo`,//
    getEmlHeadList: `${baseUri}/v1/ztythEmsImgexg/getEmlHeadList`,//
    addEmlHead: `${baseUri}/v1/gwstdJob/addEmlHead`,//TODO:临时
    /*get: `${baseUriCs}/matImgexg`,
    getExtract: `${baseUriCs}/matImgexg/getExtract`,*/
    changeSelectAllPaged: `${baseUri}/v1/matImgexgChange/list`,
    changeUpdate: `${baseUri}/v1/matImgexgChange`,
    // 设置物料状态 {status} 0-正常 1-停用 2-过滤
    setStatus: `${baseUri}/v1/matImgexg/setStatus`,
    getFactorRate: `${baseUri}/v1/matImgexg/getFactorRate`,
    pickUp: `${baseUri}/v1/matImgexg/pickUp`,
    copy: `${baseUri}/v1/matImgexg/copy`,
    getCopEmsNoOld: `${baseUri}/v1/ztythEmsImgexg/getZtythCopEmsNoOld`,
    getCopEmsNoNew: `${baseUri}/v1/matImgexgOrg/listcopEmsNo`
  },
  nonBonded: {
    insert: `${baseUri}/v1/matNonBonded`,
    update: `${baseUri}/v1/matNonBonded`,
    delete: `${baseUri}/v1/matNonBonded`,
    selectAllPaged: `${baseUri}/v1/matNonBonded/list`,
    exportUrl: `${baseUri}/v1/matNonBonded/export`,
    /*get: `${baseUriCs}/matNonBonded`,
    getExtract: `${baseUriCs}/matNonBonded/getExtract`*/
    // 设置物料状态 {status} 0-正常 1-过滤 2-停用
    setStatus: `${baseUri}/v1/matNonBonded/setStatus`,
    copy: `${baseUri}/v1/matNonBonded/copy`,
    getMatNonCopEmsNo: `${baseUri}/v1/matImgexgOrg/getMatNonCopEmsNo`,
    getOrgList: `${baseUri}/v1/matImgexgOrg/getOrgList`,
    hsValid: `${baseUri}/v1`,
    // 校验商品编码是否相同
    checKCodeTs: `${baseUri}/v1/matNonBonded/checKCodeTs`,
    // 校验商品编码是否相同
    billList: `${baseUri}/v1/matNonBonded/billList`,
    getMatFreeRemark: `${baseUri}/v1/matImgexgOrg/getMatFreeRemark`

  },
  statusLog: {
    selectAllPaged: `${baseUri}/v1/matStatusLog/list`
  },
  // 修改记录查询
  modifyRecordQuery: {
    // 备案物料
    recordedMaterials: {
      exportUrl: `${baseUri}/v1/matImgexgChange/export`,
      selectAllPaged: `${baseUri}/v1/matImgexgChange/list`
    },
    // 企业物料
    enterpriseMaterials: {
      exportUrl: `${baseUri}/v1/matImgexgChange/export`,
      selectAllPaged: `${baseUri}/v1/matImgexgChange/list`
    }
  }
}

/**
 * 物料中心接口
 * @type {{packingConfig: {updateConfig: string, getContainerMd: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, getFacGNo: string, delete: string, getData: string}, turning: {getIeInfoForEmsNo: string, insert: string, update: string, list: string, delete: string, export: string, printPdf: string}, bonded: {getListForMatCenterAuditPaged: string, takePassedImgExgInfo: string, selectAllPaged: string, exportUrl: string, getIeInfo: string, insert: string, update: string, recordPass: string, getIeInfoByCopGNo: string, delete: string}, nonBonded: {rate: {getRate: string}, selectAllPaged: string, exportUrl: string, insert: string, update: string, delete: string}, singleLoss: {head: {sendRecord: string, passSetBySelected: string, sendAudit: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, passSetByParams: string, delete: string}, aeo: {auditDataByParams: string, auditRecordList: string, auditDataM: string, returnDataM: string, auditRecordExport: string, returnDataByParams: string}, body: {rollback: string, distinctExgList: string, exportUrl: string, selectAllPaged: string, pBalance: string, exportBalance: string, setConsumeDclStat: string, insert: string, update: string, getBalanceList: string, delete: string}}, stamping: {insert: string, update: string, list: string, delete: string, export: string, printPdf: string}, kitInfoMaintain: {getCopGNos: string, getNonData: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}}
 */
export const csMaterielCenter = {
  bonded: {
    insert: `${baseUri}/v1/matImgexgCenter`,
    update: `${baseUri}/v1/matImgexgCenter`,
    delete: `${baseUri}/v1/matImgexgCenter`,
    selectAllPaged: `${baseUri}/v1/matImgexgCenter/list`,
    getListForMatCenterAuditPaged: `${baseUri}/v1/matImgexgCenter/listForMatCenterAudit`,
    exportUrl: `${baseUri}/v1/matImgexgCenter/export`,
    getIeInfo: `${baseUri}/v1/matImgexgOrg/getIeInfo`,
    getIeInfoByCopGNo: `${baseUri}/v1/matImgexgOrg/getIeInfoByCopGNo`,
    recordPass: `${baseUri}/v1/matImgexgCenter/recordPass`,
    takePassedImgExgInfo: `${baseUri}/v1/matImgexgOrg/takePassedImgExgInfo`
    /*getRecordStatusSelect: `${baseUriCs}/biCustomerParams/selectComboxByCode/RECORD_TYPE`,
    getEmsNoSelect: `${baseUriCs}/matImgexg/selectEmsNo`,*/
  },
  nonBonded: {
    insert: `${baseUri}/v1/matNonBondedCenter`,
    update: `${baseUri}/v1/matNonBondedCenter`,
    delete: `${baseUri}/v1/matNonBondedCenter`,
    selectAllPaged: `${baseUri}/v1/matNonBondedCenter/list`,
    exportUrl: `${baseUri}/v1/matNonBondedCenter/export`,
    rate: {
      getRate: `${baseUri}/v1/matNonBonded/selectCodeTS`
    }
  },
  // 单损耗信息
  singleLoss: {
    head: {
      insert: `${baseUri}/v1/emsHead`,
      update: `${baseUri}/v1/emsHead`,
      delete: `${baseUri}/v1/emsHead`,
      exportUrl: `${baseUri}/v1/emsHead/export`,
      selectAllPaged: `${baseUri}/v1/emsHead/list`,
      sendRecord: `${baseUri}/v1/emsHead/sendRecord`,                           // 发送备案
      sendAudit: `${baseUri}/v1/emsHead/sendDataByList`,                        // 发送内审
      passSetBySelected: `${baseUri}/v1/emsHead/recordPass`,                    // 备案通过(根据勾选项)
      passSetByParams: `${baseUri}/v1/emsHead/recordPass/recordPassList`        // 备案通过(根据查询条件)
    },
    body: {
      insert: `${baseUri}/v1/emsConsumPre`,
      update: `${baseUri}/v1/emsConsumPre`,
      delete: `${baseUri}/v1/emsConsumPre`,
      exportUrl: `${baseUri}/v1/emsConsumPre/export`,
      selectAllPaged: `${baseUri}/v1/emsConsumPre/list`,
      pBalance: `${baseUri}/v1/emsConsumPre/pBalance`,                           // 平衡计算
      rollback: `${baseUri}/v1/emsConsumPre/rollback`,                           // 单损耗-按钮恢复功能
      getBalanceList: `${baseUri}/v1/emsConsumPre/getBalanceList`,               // 平衡检查列表查询
      exportBalance: `${baseUri}/v1/emsConsumPre/exportBalance`,                 // 平衡检查列表导出
      distinctExgList: `${baseUri}/v1/emsConsumPre/distinctExgList`,             // 单耗申报状态列表
      setConsumeDclStat: `${baseUri}/v1/emsConsumPre/batchSetConsumeDclStat`     // 单耗申报状态设置
    },
    aeo: {
      auditDataByParams: `${baseUri}/v1/emsHead/auditDataByList`,                // 审核通过(根据查询条件)
      auditDataM: `${baseUri}/v1/emsHead/auditDataM`,                            // 审核通过(单个、根据勾选)
      auditRecordList: `${baseUri}/v1/emsHead/auditRecordList`,                  // 审核记录
      auditRecordExport: `${baseUri}/v1/emsHead/exportRecord`,                   // 审核记录(导出)
      returnDataByParams: `${baseUri}/v1/emsHead/returnDataByList`,              // 审核退回(根据查询条件)
      returnDataM: `${baseUri}/v1/emsHead/returnDataM`                           // 审核退回(单个、根据勾选)
    }
  },
  // 装箱配置
  packingConfig: {
    // 表头
    insert: `${baseUri}/v1/matPallet`,
    update: `${baseUri}/v1/matPallet`,
    delete: `${baseUri}/v1/matPallet`,
    exportUrl: `${baseUri}/v1/matPallet/export`,
    getFacGNo: `${baseUri}/v1/matPallet/getFacGNo`,
    selectAllPaged: `${baseUri}/v1/matPallet/list`,
    getPackingData: `${baseUri}/v1/matPallet/getPackingData`,
    // 汇总导入修改调整数量
    updateConfig: `${baseUri}/v1/matPalletUpdate`,
    // 获取根据条件带出的数据
    getData: `${baseUri}/v1/matPallet/getData`,
    // 获取所有企业包装方式
    getContainerMd: `${baseUri}/v1/matPallet/getContainerMd`,
    // 表体
    insertList: `${baseUri}/v1/matPalletList`,
    updateList: `${baseUri}/v1/matPalletList`,
    deleteList: `${baseUri}/v1/matPalletList`,
    exportListUrl: `${baseUri}/v1/matPalletList/export`,
    selectListAllPaged: `${baseUri}/v1/matPalletList/list`
  },
  turning: {
    insert: `${baseUri}/v1/ryTurning`,
    update: `${baseUri}/v1/ryTurning`,
    delete: `${baseUri}/v1/ryTurning`,
    list: `${baseUri}/v1/ryTurning/list`,
    export: `${baseUri}/v1/ryTurning/export`,
    printPdf: `${baseUri}/v1/ryTurning/printPdf`,
    getIeInfoForEmsNo: `${baseUri}/v1/matImgexgOrg/getIeInfoForEmsNo`
  },
  stamping: {
    insert: `${baseUri}/v1/ryStamping`,
    update: `${baseUri}/v1/ryStamping`,
    delete: `${baseUri}/v1/ryStamping`,
    list: `${baseUri}/v1/ryStamping/list`,
    export: `${baseUri}/v1/ryStamping/export`,
    printPdf: `${baseUri}/v1/ryStamping/printPdf`
  },
  // 套件信息维护
  kitInfoMaintain: {
    insert: `${baseUri}/v1/matNonSuitRel`,
    update: `${baseUri}/v1/matNonSuitRel`,
    delete: `${baseUri}/v1/matNonSuitRel`,
    exportUrl: `${baseUri}/v1/matNonSuitRel/export`,
    selectAllPaged: `${baseUri}/v1/matNonSuitRel/list`,
    getCopGNos: `${baseUri}/v1/matNonSuitRel/getCopGNos`,
    getNonData: `${baseUri}/v1/matNonSuitRel/getNonData`
  },
  // 备案通知
  filingNotice: {
    insert: `${baseUri}/v1/matNotice/mudong`,
    update: `${baseUri}/v1/matNotice/mudong`,
    delete: `${baseUri}/v1/matNotice/mudong`,
    statusUpdate: `${baseUri}/v1/matNotice/mudong`,         // 状态更新: /{sids}/{status}
    exportUrl: `${baseUri}/v1/matNotice/mudong/export`,
    selectAllPaged: `${baseUri}/v1/matNotice/mudong/list`
  },
  /**
   * 物料供应商
   */
  materialSupplier: {
    insert: `${baseUri}/v1/matSupplier`,
    update: `${baseUri}/v1/matSupplier`,
    delete: `${baseUri}/v1/matSupplier`,
    selectAllPaged: `${baseUri}/v1/matSupplier/list`
  }
}

/**
 * 物料关系
 * @type {{hsVerification: {comm: {getTime: string, getEmsNo: string, updateUrl: string}, materials: {exportUrl: string, selectAllPaged: string}, nonBonded: {exportUrl: string, selectAllPaged: string}, products: {exportUrl: string, selectAllPaged: string}}, comm: {rollback: string, getOrgList: string, recordPass: string, getFacGNolist: string}, product: {getImgMaxNo: string, selectAllPaged: string, getExgSerialNoList: string, insert: string, update: string, jinErBondedSync: string, delete: string, export: string, getExgMaxNo: string, emsNoSync: string, hsValid: string}, material: {getImgMaxNo: string, extract: string, selectAllPaged: string, insert: string, update: string, jinErBondedSync: string, delete: string, export: string, getImgSerialNoList: string, emsNoSync: string, hsValid: string}, aeoManage: {getListForMatAuditPaged: string}}}
 */
export const materialRelationship = {
  comm: {
    getOrgList: `${baseUri}/v1/matImgexgOrg/getOrgList`,
    recordPass: `${baseUri}/v1/matImgexgOrg/recordPass`,
    rollback: `${baseUri}//v1/matImgexgOrg/rollback`,
    // 模糊查询企业料号
    getFacGNolist: `${baseUri}/v1/matImgexgOrg/getFacGNolist`,
    getAllFacGNo: `${baseUri}/v1/matImgexgOrg/getAllFacGNo`
  },
  material: {
    selectAllPaged: `${baseUri}/v1/matImgexgOrg/list`,
    insert: `${baseUri}/v1/matImgexgOrg`,
    update: `${baseUri}/v1/matImgexgOrg`,
    delete: `${baseUri}/v1/matImgexgOrg`,
    export: `${baseUri}/v1/matImgexgOrg/export`,
    getImgMaxNo: `${baseUri}/v1/matImgexgOrg/getImgMaxNo`,
    getImgSerialNoList: `${baseUri}/v1/matImgexgOrg/getImgSerialNoList`,
    // 金二备案数据同步
    jinErBondedSync: `${baseUri}/v1/`,
    extract: `${baseUri}/v1/matImgexgOrg/getExtraction`,
    // 同步备案号
    emsNoSync: `${baseUri}/v1/matImgexgOrg/getEmsNoAsyc`,
    hsValid: `${baseUri}/v1`
  },
  product: {
    selectAllPaged: `${baseUri}/v1/matImgexgOrg/list`,
    insert: `${baseUri}/v1/matImgexgOrg`,
    update: `${baseUri}/v1/matImgexgOrg`,
    delete: `${baseUri}/v1/matImgexgOrg`,
    export: `${baseUri}/v1/matImgexgOrg/export`,
    getImgMaxNo: `${baseUri}/v1/matImgexgOrg/getImgMaxNo`,
    getExgMaxNo: `${baseUri}/v1/matImgexgOrg/getExgMaxNo`,
    getExgSerialNoList: `${baseUri}/v1/matImgexgOrg/getExgSerialNoList`,
    // 金二备案数据同步
    jinErBondedSync: `${baseUri}/v1/`,
    // 同步备案号
    emsNoSync: `${baseUri}/v1/matImgexgOrg/getEmsNoAsyc`,
    hsValid: `${baseUri}/v1`
  },
  aeoManage: {
    getListForMatAuditPaged: `${baseUri}/v1/matImgexgOrg/listForMatAudit`
  },
  hsVerification: {
    comm: {
      getTime: `${baseUri}/v1/gwstdHscode/getTime`,
      getEmsNo: `${baseUri}/v1/gwstdHscode/getEmsNo`,
      updateUrl: `${baseUri}/v1/gwstdHscode/updateData`
    },
    materials: {
      exportUrl: `${baseUri}/v1/gwstdHscode/export`,
      selectAllPaged: `${baseUri}/v1/gwstdHscode/list`
    },
    products: {
      exportUrl: `${baseUri}/v1/gwstdHscode/export`,
      selectAllPaged: `${baseUri}/v1/gwstdHscode/list`
    },
    nonBonded: {
      exportUrl: `${baseUri}/v1/gwstdHscode/export`,
      selectAllPaged: `${baseUri}/v1/gwstdHscode/list`
    }
  }
}

/**
 * AEO管理
 * @type {{prodClassifyCheck: {selectAllPaged: string}, internalAuditRecordQuery: {getInternalauditList: string, getProductInternalAuditRecord: string, exportUrl: string, exportCommodity: string, getInternalauditrecords: string, exportRecord: string}, errorStatistics: {selectAllPaged: string}, comm: {getApprUser: string}, matCenterCheck: {selectAllPaged: string}, auditTrack: {selectAllPaged: string, clear: string, insert: string}, aeoReview: {actions: {sendData: string, returnDataByListP: string, nonBondedHsCodeCheckByParams: string, bondedHsCodeCheckBySelected: string, selectListBySid: string, recordPassList: string, auditData: string, auditDataM: string, mergingHsCodeCheckByParams: string, v1matImgexg: string, mergingAuditHsCodeCheckBySelected: string, returnData: string, mergingHsCodeCheckBySelected: string, sendBills: string, auditDataAll: string, matNonBonded: string, mixingHsCodeCheckByParams: string, nonBondedHsCodeCheckBySelected: string, sendDataM: string, sendExgImgApiList: string, bondedHsCodeCheckByParams: string, sendDateByList: string, mixingHsCodeCheckBySelected: string, extract: string, sendDataMCheck: string, sendDataCheck: string, returnDataM: string, returnDataByListM: string, auditDateByList: string, matImgexg: string, mergingAuditHsCodeCheckByParams: string}, config: {selectListByUserId: string, stop: string, selectAllPaged: string, insert: string, delete: string}}}}
 */
export const aeoManage = {
  prodClassifyCheck: {
    selectAllPaged: `${baseUri}/v1/matImgexg/selectListForMatAudit`
  },
  matCenterCheck: {
    selectAllPaged: `${baseUri}/v1/matImgexg/selectListForMatCenterAudit`
  },
  aeoReview: {
    actions: {
      auditData: `${baseUri}/v1/aeoAuditInfo/auditData`,
      auditDataM: `${baseUri}/v1/aeoAuditInfo/auditDataM`,
      auditDataAll: `${baseUri}/v1/aeoAuditInfo/auditDataAll`,
      returnData: `${baseUri}/v1/aeoAuditInfo/returnData`,
      returnDataM: `${baseUri}/v1/aeoAuditInfo/returnDataM`,
      sendData: `${baseUri}/v1/aeoAuditInfo/sendData`,
      sendDataM: `${baseUri}/v1/aeoAuditInfo/sendDataM`,
      selectListBySid: `${baseUri}/v1/aeoAuditInfo/selectListBySid`,
      sendBills: `${baseUri}/v1/postApiReturnInfo/sendBillApis`,
      auditDateByList: `${baseUri}/v1/matImgexgOrg/auditDateByList`,
      matImgexg: `${baseUri}/v1/matImgexg/auditDateByList`,
      sendDateByList: `${baseUri}/v1/matImgexgOrg/sendDateByList`,
      recordPassList: `${baseUri}/v1/matImgexgOrg/recordPassList`,
      sendExgImgApiList: `${baseUri}/v1/postApiReturnInfo/sendExgImgApiList`,
      matNonBonded: `${baseUri}/v1/matNonBonded/sendDateByList`,
      v1matImgexg: `${baseUri}/v1/matImgexg/sendDateByList`,
      returnDataByListM: `${baseUri}/v1/matImgexg/returnDataByList`,
      returnDataByListP: `${baseUri}/v1/matImgexgOrg/returnDataByList`,
      // 根据勾选项进行保税(料件、成品)校验HS编码
      bondedHsCodeCheckBySelected: `${baseUri}/v1/aeoAuditInfo/matTickCheckCodeTS`,
      // 根据查询条件进行保税(料件、成品)校验HS编码
      bondedHsCodeCheckByParams: `${baseUri}/v1/aeoAuditInfo/matAllCheckCodeTS`,
      // 根据勾选项进行非保税(料件、成品)校验HS编码
      nonBondedHsCodeCheckBySelected: `${baseUri}/v1/aeoAuditInfo/notTickCheckCodeTS`,
      // 根据查询条件进行非保税(料件、成品)校验HS编码
      nonBondedHsCodeCheckByParams: `${baseUri}/v1/aeoAuditInfo/nonAllCheckCodeTS`,
      // 根据勾选项进行混合(料件、成品)校验HS编码
      mixingHsCodeCheckBySelected: `${baseUri}/v1/aeoAuditInfo/auditTickCheckCodeTS`,
      // 根据查询条件进行混合(料件、成品)校验HS编码
      mixingHsCodeCheckByParams: `${baseUri}/v1/aeoAuditInfo/auditAllCheckCodeTS`,
      // 根据勾选项进行混合(料件、成品)校验HS编码 -- 备案
      mergingHsCodeCheckBySelected: `${baseUri}/v1/aeoAuditInfo/orgTickCheckCodeTS`,
      // 根据查询条件进行混合(料件、成品)校验HS编码 -- 备案
      mergingHsCodeCheckByParams: `${baseUri}/v1/aeoAuditInfo/orgAllCheckCodeTS`,
      // 根据勾选项进行混合(料件、成品)校验HS编码 -- 备案审核勾选
      mergingAuditHsCodeCheckBySelected: `${baseUri}/v1/aeoAuditInfo/orgAuditTickCheckCodeTS`,
      // 根据查询条件进行混合(料件、成品)校验HS编码 -- 备案审核查询条件
      mergingAuditHsCodeCheckByParams: `${baseUri}/v1/aeoAuditInfo/orgAuditAllCheckCodeTS`,
      // 非保税预归类提取功能
      extract: `${baseUri}/v1/matNonBonded/getExtractionNonBonded`,
      // 用户单个发送审核-check
      sendDataCheck: `${baseUri}/v1/aeoAuditInfo/sendDataCheck`,
      // 用户批量发送审核-check
      sendDataMCheck: `${baseUri}/v1/aeoAuditInfo/sendDataMCheck`,
      // 获取种类下拉参数
      categoryParameter: `${baseUri}/v1/biCustomerParams/getCategoryParameter`,
      // 获取种类下拉参数
      bodyTypeParameters: `${baseUri}/v1/biCustomerParams/getBodyTypeParameters`
    },
    config: {
      selectAllPaged: `${baseUri}/v1/aeoauditset/selectAllPaged`,
      insert: `${baseUri}/v1/aeoauditset/insert`,
      delete: `${baseUri}/v1/aeoauditset/delete`,
      stop: `${baseUri}/v1/aeoauditset/stop`,
      selectListByUserId: `${baseUri}/v1/aeoauditset/selectListByUserId`
    }
  },
  internalAuditRecordQuery: {
    getInternalauditList: `${baseUri}/v1/aeoAuditInfo/getInternalauditList`,                    // 进出口
    getInternalauditrecords: `${baseUri}/v1/aeoAuditInfo/getInternalauditrecords`,              // 备案
    getProductInternalAuditRecord: `${baseUri}/v1/aeoAuditInfo/getProductInternalAuditRecord`,  // 商品
    exportUrl: `${baseUri}/v1/aeoAuditInfo/export`,
    exportRecord: `${baseUri}/v1/aeoAuditInfo/exportRecord`,
    exportCommodity: `${baseUri}/v1/aeoAuditInfo/exportCommodity`
  },
  comm: {
    getApprUser: `${baseUri}/v1/aeoAuditInfo/getApprUser`
  },
  /**
   * 审核不通过痕迹
   */
  auditTrack: {
    insert: `${baseUri}/v1/decErpIHeadN/insertApprLog`,
    clear: `${baseUri}/v1/decErpIHeadN/clearApprLog`,
    selectAllPaged: `${baseUri}/v1/decErpIHeadN/selectApprLog`
  },
  /**
   * 进出口内审记录
   */
  errorStatistics: {
    selectAllPaged: `${baseUri}/v1/aeoAuditInfo/audioListByUser`
  }
}

/**
 * 预警管理
 * @type {{manualMargin: {selectAllPaged: string, export: string}, manager: {repair: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, warringSet: {getAll: string, update4Card: string, changeWarnStatus: string, insert: string, update: string, singleSelect: string}, spill: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, warringEmails: {head: {selectWarringId: string, selectByWarringType: string, selectAllPaged: string, insert: string, update: string, getArrivalNoticer: string}, body: {getSelectedWarringTypes: string, delete: string}}, bail: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, decOverdue: {exportUrl: string, selectAllPaged: string, checkData: string}, fileManagement: {getAttachedType: string, exportUrl: string, selectAllPaged: string, checkData: string}, importBillsOverdue: {exportUrl: string, selectAllPaged: string, checkData: string}, equip: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, price: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, expire: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, importsNoArrived: {exportUrl: string, selectAllPaged: string, checkData: string}, clearanceBusiness: {loadConfigUrl: string, insert: string, update: string, getEntryChannelType: string, translate: string}, wt: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, export: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}, card: {logout: string, getMaxNo: string, selectAllPaged: string, insert: string, update: string, delete: string, export: string}}}}
 */
export const earlyWarning = {
  manager: {
    price: {
      insert: `${baseUri}/v1/warringPrice`,
      update: `${baseUri}/v1/warringPrice`,
      delete: `${baseUri}/v1/warringPrice`,
      export: `${baseUri}/v1/warringPrice/export`,
      selectAllPaged: `${baseUri}/v1/warringPrice/list`,
      getMaxNo: `${baseUri}/v1/warringPrice/getMaxNo`,
      logout: `${baseUri}/v1/warringPrice/setStatus`
    },
    bail: {
      insert: `${baseUri}/v1/warringBail`,
      update: `${baseUri}/v1/warringBail`,
      delete: `${baseUri}/v1/warringBail`,
      export: `${baseUri}/v1/warringBail/export`,
      selectAllPaged: `${baseUri}/v1/warringBail/list`,
      getMaxNo: `${baseUri}/v1/warringBail/getMaxNo`,
      logout: `${baseUri}/v1/warringBail/setStatus`
    },
    repair: {
      insert: `${baseUri}/v1/warringRepair`,
      update: `${baseUri}/v1/warringRepair`,
      delete: `${baseUri}/v1/warringRepair`,
      export: `${baseUri}/v1/warringRepair/export`,
      selectAllPaged: `${baseUri}/v1/warringRepair/list`,
      getMaxNo: `${baseUri}/v1/warringRepair/getMaxNo`,
      logout: `${baseUri}/v1/warringRepair/setStatus`
    },
    wt: {
      insert: `${baseUri}/v1/warringWt`,
      update: `${baseUri}/v1/warringWt`,
      delete: `${baseUri}/v1/warringWt`,
      export: `${baseUri}/v1/warringWt/export`,
      selectAllPaged: `${baseUri}/v1/warringWt/list`,
      getMaxNo: `${baseUri}/v1/warringWt/getMaxNo`,
      logout: `${baseUri}/v1/warringWt/setStatus`
    },
    equip: {
      insert: `${baseUri}/v1/warringEquip`,
      update: `${baseUri}/v1/warringEquip`,
      delete: `${baseUri}/v1/warringEquip`,
      export: `${baseUri}/v1/warringEquip/export`,
      selectAllPaged: `${baseUri}/v1/warringEquip/list`,
      getMaxNo: `${baseUri}/v1/warringEquip/getMaxNo`,
      logout: `${baseUri}/v1/warringEquip/setStatus`
    },
    expire: {
      insert: `${baseUri}/v1/warringExpire`,
      update: `${baseUri}/v1/warringExpire`,
      delete: `${baseUri}/v1/warringExpire`,
      export: `${baseUri}/v1/warringExpire/export`,
      selectAllPaged: `${baseUri}/v1/warringExpire/list`,
      getMaxNo: `${baseUri}/v1/warringExpire/getMaxNo`,
      logout: `${baseUri}/v1/warringExpire/setStatus`
    },
    export: {
      insert: `${baseUri}/v1/warringExport`,
      update: `${baseUri}/v1/warringExport`,
      delete: `${baseUri}/v1/warringExport`,
      export: `${baseUri}/v1/warringExport/export`,
      selectAllPaged: `${baseUri}/v1/warringExport/list`,
      getMaxNo: `${baseUri}/v1/warringExport/getMaxNo`,
      logout: `${baseUri}/v1/warringExport/setStatus`
    },
    spill: {
      insert: `${baseUri}/v1/warringSpill`,
      update: `${baseUri}/v1/warringSpill`,
      delete: `${baseUri}/v1/warringSpill`,
      export: `${baseUri}/v1/warringSpill/export`,
      selectAllPaged: `${baseUri}/v1/warringSpill/list`,
      getMaxNo: `${baseUri}/v1/warringSpill/getMaxNo`,
      logout: `${baseUri}/v1/warringSpill/setStatus`
    },
    card: {
      insert: `${baseUri}/v1/warringCard`,
      update: `${baseUri}/v1/warringCard`,
      delete: `${baseUri}/v1/warringCard`,
      export: `${baseUri}/v1/warringCard/export`,
      selectAllPaged: `${baseUri}/v1/warringCard/list`,
      getMaxNo: `${baseUri}/v1/warringCard/getMaxNo`,
      logout: `${baseUri}/v1/warringCard/setStatus`,
      certList: `${baseUri}/v1/warringCard/certList`,
      extractSelect: `${baseUri}/v1/warringCard/extractSelect`,
      extractTerm: `${baseUri}/v1/warringCard/extractTerm`
    },
    warringSet: {
      insert: `${baseUri}/v1/warringSet`,
      update: `${baseUri}/v1/warringSet`,
      getAll: `${baseUri}/v1/warringSet/getAll`,
      update4Card: `${baseUri}/v1/warringCard/update`,
      singleSelect: `${baseUri}/v1/warringSet/singleSelect`,
      changeWarnStatus: `${baseUri}/v1/warringMargin/changeWarnStatus`
    },
    warringEmails: {
      head: {
        insert: `${baseUri}/v1/warringEmailConfig`,
        update: `${baseUri}/v1/warringEmailConfig`,
        selectByWarringType: `${baseUri}/v1/warringEmailConfig/selectByWarringType`,
        selectAllPaged: `${baseUri}/v1/warringEmailConfig/list`,
        selectWarringId: `${baseUri}/v1/warringEmailConfig/selectWarringId`,
        getArrivalNoticer: `${baseUri}/v1/decICustomsTrack/getAlertOthers`
      },
      body: {
        delete: `${baseUri}/v1/warringEmailConfigBody`,
        getSelectedWarringTypes: `${baseUri}/v1/warringEmailConfigBody/getSelectedWarringTypes`
      }
    },
    clearanceBusiness: {
      translate: `${baseUri}/v1/convert/selectTranslateConfig`,
      insert: `${baseUri}/v1/warringPassConfig`,
      update: `${baseUri}/v1/warringPassConfig`,
      loadConfigUrl: `${baseUri}/v1/warringPassConfig/list`,
      getEntryChannelType: `${baseUri}/v1/warringPassConfig/getEntryChannelType`
    },
    // 进口制单超期预警
    importBillsOverdue: {
      exportUrl: `${baseUri}/v1/warringMarkerOverdue/export`,
      selectAllPaged: `${baseUri}/v1/warringMarkerOverdue/list`,
      checkData: `${baseUri}/v1/warringMarkerOverdue/checkData`
    },
    // 报关超期预警
    decOverdue: {
      exportUrl: `${baseUri}/v1/warringDeclareOverdue/export`,
      checkData: `${baseUri}/v1/warringDeclareOverdue/checkData`,
      selectAllPaged: `${baseUri}/v1/warringDeclareOverdue/list`
    },
    // 档案管理预警
    fileManagement: {
      exportUrl: `${baseUri}/v1/warringArchives/export`,
      checkData: `${baseUri}/v1/warringArchives/checkData`,
      selectAllPaged: `${baseUri}/v1/warringArchives/list`,
      getAttachedType: `${baseUri}/v1/warringArchives/getAttachedType`
    },
    // 进口未到货预警
    importsNoArrived: {
      exportUrl: `${baseUri}/v1/warringArrivalOverdue/export`,
      checkData: `${baseUri}/v1/warringArrivalOverdue/checkData`,
      selectAllPaged: `${baseUri}/v1/warringArrivalOverdue/list`
    }
  },
  manualMargin: {
    export: `${baseUri}/v1/warringMargin/export`,
    selectAllPaged: `${baseUri}/v1/warringMargin/list`
    /*getPreCentAge: `${baseUri}/v1/warringMargin/selectQtyRate`,
    setPreCentAge: `${baseUri}/v1/warringMargin/insert`*/
  },
  /*configuration: {
    list: {
      selectAllPaged: `${baseUriCs}/warringSet/selectAllPaged`,
      delete: `${baseUriCs}/warringSet/delete`
    },
    detail: {
      insert: `${baseUriCs}/warringSet/insert`,
      update: `${baseUriCs}/warringSet/update`
    }
  }*/
  /**
   * 修理物品进境预警
   */
  repairItemI: {
    logout: `${baseUri}/v1/repair/ihead/warning/off`,
    restore: `${baseUri}/v1/repair/ihead/warning/on`,
    selectAllPaged: `${baseUri}/v1/repair/ihead/warning`,
    setting: `${baseUri}/v1/repair/ihead/warning/setting`,
    exportUrl: `${baseUri}/v1/repair/ihead/warning/export`
  },
  /**
   * 修理物品出境预警
   */
  repairItemE: {
    logout: `${baseUri}/v1/repair/ehead/warning/off`,
    restore: `${baseUri}/v1/repair/ehead/warning/on`,
    selectAllPaged: `${baseUri}/v1/repair/ehead/warning`,
    setting: `${baseUri}/v1/repair/ehead/warning/setting`,
    exportUrl: `${baseUri}/v1/repair/ehead/warning/export`
  },
  /**
   * 暂时进境预警
   */
  tempEntryExitI: {
    logout: `${baseUri}/v1/temporary/ihead/warning/off`,
    restore: `${baseUri}/v1/temporary/ihead/warning/on`,
    selectAllPaged: `${baseUri}/v1/temporary/ihead/warning`,
    setting: `${baseUri}/v1/temporary/ihead/warning/setting`,
    exportUrl: `${baseUri}/v1/temporary/ihead/warning/export`
  },
  /**
   * 暂时出境预警
   */
  tempEntryExitE: {
    logout: `${baseUri}/v1/temporary/ehead/warning/off`,
    restore: `${baseUri}/v1/temporary/ehead/warning/on`,
    selectAllPaged: `${baseUri}/v1/temporary/ehead/warning`,
    setting: `${baseUri}/v1/temporary/ehead/warning/setting`,
    exportUrl: `${baseUri}/v1/temporary/ehead/warning/export`
  },
  /**
   * 通知人设置
   */
  notifierSettings: {
    insert: `${baseUri}/v1/noticeUser`,
    update: `${baseUri}/v1/noticeUser`,
    delete: `${baseUri}/v1/noticeUser`,
    exportUrl: `${baseUri}/v1/noticeUser/export`,
    selectAllPaged: `${baseUri}/v1/noticeUser/list`
  },
  /**
   * 预警通知设置
   */
  alertNotifySettings: {
    delete: `${baseUri}/v1/noticeInfo`,
    update: `${baseUri}/v1/noticeInfo/save`,
    selectAllPaged: `${baseUri}/v1/noticeInfo/list`,
    selectAllPagedYW: `${baseUri}/v1/noticeInfo/ywlist`,
    checkForMultipleEdit: `${baseUri}/v1/noticeInfo/checkForMultipleEdit`
  }
}

export const ieParams = {
  PRD: `${baseUri}/v1/biClientInformation/selectComboxByCode/PRD`,   // 供应商
  GETHG: `${baseUri}/v1/biClientInformation/getZtythCopEmsNoByTradeCode`,   // 生加工转入，获取海关十位代码
  GETHZC: `${baseUri}/v1/biClientInformation/getZtythCopZCEmsNoByTradeCode`,   // 生加工转出，获取海关十位代码
  GETZRC: `${baseUri}/v1/biClientInformation/getZtythCopEmsNoZc`,   // 生加工，获取转出方转入方手账册号
  GETMD: `${baseUri}/v1/recordHeadFromTo/getZcHFromTo`,   // 生加工，获取转出方转入方手账册号
  GETUSER: `${baseUri}/v1/biClientInformation/getUserName`,   // 生加工，获取当前登录用户
  GETHGSWDM: `${baseUri}/v1/biClientInformation/getHgSwM/PRD`,   // 深加工获取转出海关十位代码
  GETOUTHGSWDM: `${baseUri}/v1/biClientInformation/getOutHgSwM/CLI`,   // 深加工获取转入海关十位代码
  GETDQMD: `${baseUri}/v1/biClientInformation/getdqZcMd/PRD`,   // 深加工获取企业代码
  GETZRMD: `${baseUri}/v1/biClientInformation/getdqZrMd/CLI`,   // 深加工转入方获取企业代码
  CLI: `${baseUri}/v1/biClientInformation/selectComboxByCode/CLI`,   // 客户
  FOD: `${baseUri}/v1/biClientInformation/selectComboxByCode/FOD`,   // 货代
  CUT: `${baseUri}/v1/biClientInformation/selectComboxByCode/CUT`,   // 报关行
  COM: `${baseUri}/v1/biClientInformation/selectComboxByCode/COM`,    // 企业
  selectComboxByCode: `${baseUri}/v1/biClientInformation/selectComboxByCode`,    // 参数可以逗号隔开查询多种类型
  SelectByCustomerTypeAndCode: `${baseUri}/v1/biClientInformation/selectByCustomerTypeAndCode`,   // 根据客户类型、客户编码获取当前企业下的数据
  getListByCustomerType: `${baseUri}/v1/biClientInformation/getListByCustomerType`,   // 根据客户类型获取当前企业下的数据
  getListBySupplierType: `${baseUri}/v1/biClientInformation/getListBySupplierType`,
  getTransformInfo: `${baseUri}/v1/biClientInformation/getTransformInfo`,     //获取转换信息
  //申报单位信用代码
  getCreditCode: `${baseUri}/v1/biClientInformation/getCreditCode`,

  getCompanyName:`${baseUri}/v1/biClientInformation/getCompanyName`
}

/**
 * 进出口预录入单
 * @type {{iBillList: {selectAllPaged: string, getBillTotal: string}, template: {selectAllPaged: string, selectInsertUser: string, insert: string, delete: string}, customsParams: {getParamValues: string}, decErpIListN: {selectAllPaged: string, batchUpdate: string, importExtract: string, listPickUpExport: string, insert: string, update: string, delete: string, extractErpDataByQuery: string, checkDecTotal: string, total: string, exportUrl: string, importLinkedNo: string, extractErpDataByChoose: string}, iLogisticsTracking: {updateMult: string, selectAllPaged: string, exportUrl: string, batchUpdate: string, update: string, selectBySId: string}, eBill: {selectListByHeadId: string, selectAllPaged: string, printBill: string, update: string, selectAllBillNo: string, printCheck: string, costCalculateUrl: string, printEntry: string, sendBillCancel: string, createBillTest: string, createBillsJg: string, createBill: string, getEntryInfoByEmsListNo: string, selectBillHeader: string, printBillList: string, export: string, sendBill: string, sendBills: string, printBillFac: string}, iEntry: {printArrivalNotice: string, selectAllPaged: string, update: string, decIEntryContainer: string, list: string, export: string, exportEntry: string, selectAllListPaged: string, generateArrivalNotification: string}, eCustomsTracking: {updateMult: string, setCompleteSchedule: string, selectAllPaged: string, exportUrl: string, batchUpdate: string, financeCheck: string, update: string, selectBySId: string, finance: string}, decErpIHeadN: {updateBack: string, selectAllPaged: string, insert: string, update: string, getExtract: string, getEntityByKey: string, checkForForceChangeStatus: string, setPreStatus: string, delete: string, aeoReview: {selectAllPaged: string}, forceChangeStatus: string, exportUrl: string, lockDocAccompanying: string, unlockDocAccompanying: string, copy: string, backFillEntryNo: string, getSumListContent: string}, iCustomsTracking: {updateMult: string, purchaseCheck: string, setCompleteSchedule: string, selectAllPaged: string, exportUrl: string, batchUpdate: string, purchase: string, update: string, selectBySId: string, finance: string}, decAttachedDocuments: {imports: {selectAllPaged: string, insert: string, update: string, delete: string}, exports: {selectAllPaged: string, insert: string, update: string, delete: string}}, cert: {rest: string, save: string, getInvolveCerts: string, getSpecifiedCertList: string, extractCertificate: string}, xinYue: {exportUrl: string, selectAllPaged: string}, eEntry: {selectAllPaged: string, update: string, export: string, exportEntry: string, selectAllListPaged: string}, decErpEHeadN: {updateBack: string, selectAllPaged: string, insert: string, update: string, leadHeadList: string, getEntityByKey: string, checkForForceChangeStatus: string, delete: string, aeoReview: {selectAllPaged: string}, forceChangeStatus: string, exportUrl: string, lockDocAccompanying: string, unlockDocAccompanying: string, copy: string, backFillEntryNo: string, getSumListContent: string}, iBill: {selectBillHead: string, selectListByHeadId: string, printBill: string, update: string, selectAllBillNo: string, printCheck: string, costCalculateUrl: string, printEntry: string, sendBillCancel: string, createBillTest: string, createBillsJg: string, createBill: string, getEntryInfoByEmsListNo: string, selectBillHeader: string, printBillList: string, export: string, sendBill: string, sendBills: string, printBillFac: string}, eBillList: {selectAllPaged: string, getBillTotal: string}, eLogisticsTracking: {updateMult: string, selectAllPaged: string, exportUrl: string, update: string, selectBySId: string}, decErpEListN: {selectAllPaged: string, batchUpdate: string, importExtract: string, listPickUpExport: string, insert: string, update: string, delete: string, extractErpDataByQuery: string, checkDecTotal: string, total: string, exportUrl: string, extractErpDataByChoose: string, createPackingInfo: string}, customsDeclaration: {entry: string, sendLjqApis: string, printEEntryList: string, sendLjqApi: string, printIEntryList: string}, qtySpillRemind: {selectAllPaged: string, export: string}, packingInfo: {totalCheck: string, total: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, checkByNo: string, exportPackDetailUrl: string, delete: string}}}
 */
export const csImportExport = {
  decErpComm: {
    uniqueCheck: `${baseUri}/v1/preDecErpHead/uniqueCheck`
  },
  logisticsBoxInfo: {
    insert: `${baseUri}/v1/decLogisticBox`,
    delete: `${baseUri}/v1/decLogisticBox`,
    update: `${baseUri}/v1/decLogisticBox`,
    selectAllPaged: `${baseUri}/v1/decLogisticBox/list`
  },
  decErpIHeadN: {
    insert: `${baseUri}/v1/decErpIHeadN`,
    delete: `${baseUri}/v1/decErpIHeadN`,
    update: `${baseUri}/v1/decErpIHeadN`,
    updateBack: `${baseUri}/v1/decErpIHeadN/updateBack`,
    copy: `${baseUri}/v1/decErpIHeadN/copy`,
    selectAllPaged: `${baseUri}/v1/decErpIHeadN/list`,
    exportUrl: `${baseUri}/v1/decErpIHeadN/export`,
    aeoReview: {
      selectAllPaged: `${baseUri}/v1/decErpIHeadN/selectListForErpAudit`
    },
    backFillEntryNo: `${baseUri}/v1/decErpIHeadN/roback`,
    getSumListContent: `${baseUri}/v1/decErpIHeadN/getSumListContent`,
    getExtract: `${baseUri}/v1/decErpIHeadN/getExtract`,
    getEntityByKey: `${baseUri}/v1/decErpIHeadN/select`,

    //强制修改状态
    checkForForceChangeStatus: `${baseUri}/v1/decErpIHeadN/checkForForceChangeStatus`, // { headId } 强制修改状态校验
    forceChangeStatus: `${baseUri}/v1/decErpIHeadN/forceChangeStatus`,                 // 强制修改状态 /v1/decErpIHeadN/forceChangeStatus/{headId}
    // 锁定随附单据
    lockDocAccompanying: `${baseUri}/v1/decErpIHeadN/lock`,
    // 解锁随附单据
    unlockDocAccompanying: `${baseUri}/v1/decErpIHeadN/lock`,
    // 接受委托
    setPreStatus: `${baseUri}/v1/decErpIHeadN/setPreStatus`,
    //新增校验
    insertCheck: `${baseUri}/v1/decErpIHeadN/insertCheck`,
    getRelateOaNo: `${baseUri}/v1/decErpIHeadN/getRelateOaNo`,
    relateEBill: `${baseUri}/v1/decErpIHeadN/relateEBill`,
  },
  decErpIListN: {
    insert: `${baseUri}/v1/decErpIListN`,
    delete: `${baseUri}/v1/decErpIListN`,
    update: `${baseUri}/v1/decErpIListN`,
    batchUpdate: `${baseUri}/v1/decErpIListUpdate/return`,
    selectAllPaged: `${baseUri}/v1/decErpIListN/list`,
    total: `${baseUri}/v1/decErpIListN/total`,
    exportUrl: `${baseUri}/v1/decErpIListN/export`,
    importLinkedNo: `${baseUri}/v1/decErpIListN/importLinkedNo`,
    // 提取ERP数据-勾选
    extractErpDataByChoose: `${baseUri}/v1/decErpIListN/extractErpDataByChoose`,
    // 提取ERP数据-条件筛选
    extractErpDataByQuery: `${baseUri}/v1/decErpIListN/extractErpDataByQuery`,
    // 导入提取
    importExtract: `${baseUri}/v1/decErpIListPickUp`,
    // 导入提取(导出)
    listPickUpExport: `${baseUri}/v1/decErpIListPickUp`,
    // 校验【数量*单价=/总价】
    checkDecTotal: `${baseUri}/v1/decErpIListN/checkDecTotal`
  },
  iBill: {
    selectBillHead: `${baseUri}/v1/decIBillHead/list`,
    export: `${baseUri}/v1/decIBillHead/export`,
    //生成清单验证
    createBillTest: `${baseUri}/v1/decIBillHead/createBillsCheck`,
    //根据提单表头sid生成清单
    createBill: `${baseUri}/v1/decIBillHead/createBills`,
    //根据提单表头sid生成清单(金关)
    createBillsJg: `${baseUri}/v1/decIBillHead/createBillsJg`,
    //根据提单SID，获取提单下所有清单的EMS_LIST_NO和清单的SID
    selectAllBillNo: `${baseUri}/v1/decIBillHead/selectBillListByHeadId`,
    //根据sid查询清单表头数据
    selectBillHeader: `${baseUri}/v1/decIBillHead/selectBySId`,
    // 打印校验
    printCheck: `${baseUri}/v1/decIBillHead/printCheck`,
    //打印清单
    printBill: `${baseUri}/v1/decIBillHead/printBill`,
    //打印企业料号清单草单
    printBillFac: `${baseUri}/v1/decIBillHead/printBillFac`,
    //打印报关单
    printEntry: `${baseUri}/v1/decIBillHead/printEntry`,
    //根据提单SID,获取所有的报关追踪单SID和对应的清单编号
    selectListByHeadId: `${baseUri}/v1/decIBillHead/selectListByHeadId`,
    //发送清单
    sendBill: `${baseUri}/v1/postApiReturnInfo/sendBillApi`,
    //发送清单(批量)
    sendBills: `${baseUri}/v1/postApiReturnInfo/sendBillApiList`,  //    sendBillApis`,
    // 更新
    update: `${baseUri}/v1/decIBillHead`,
    // 获取报关单信息
    getEntryInfoByEmsListNo: `${baseUri}/v1/decIBillHead/getEntryInfoByEmsListNo`,
    // 清单撤回
    sendBillCancel: `${baseUri}/v1/dataSend/entry/cancelEntry`,
    // 计算运保费
    costCalculateUrl: `${baseUri}/v1/`,
    // 打印清单
    printBillList: `${baseUri}/v1/decIBillHead/printBillList`,
    //同步清单
    billSync: `${baseUri}/v1/decIBillHead/billSync`
  },
  iBillList: {
    getBillTotal: `${baseUri}/v1/decIBillList/total`,
    selectAllPaged: `${baseUri}/v1/decIBillList/list`
  },
  iLogisticsTracking: {
    selectBySId: `${baseUri}/v1/decILogisticsTrack/selectBySId`,
    update: `${baseUri}/v1/decILogisticsTrack`,
    selectAllPaged: `${baseUri}/v1/decILogisticsTrack/list`,
    exportUrl: `${baseUri}/v1/decILogisticsTrack/export`,
    updateMult: `${baseUri}/v1/decILogisticsTrack/updateM`,
    batchUpdate: `${baseUri}/v1/decILogisticsTrackUpdate`
  },
  iCustomsTracking: {
    selectBySId: `${baseUri}/v1/decICustomsTrack/selectBySId`,
    update: `${baseUri}/v1/decICustomsTrack`,
    updateMult: `${baseUri}/v1/decICustomsTrack/updateM`,
    selectAllPaged: `${baseUri}/v1/decICustomsTrack/list`,
    exportUrl: `${baseUri}/v1/decICustomsTrack/export`,
    // 批量导入导出
    batchUpdate: `${baseUri}/v1/decICustomsTrackUpdate`,
    // 设置完成进度
    setCompleteSchedule: `${baseUri}/v1/decICustomsTrack/reach`,
    // 采购确认/取消校验
    purchaseCheck: `${baseUri}/v1/decICustomsTrack/purchaseCheck`,
    // 采购确认/取消
    purchase: `${baseUri}/v1/decICustomsTrack/purchase`,
    // 财务确认
    finance: `${baseUri}/v1/decICustomsTrack/financee`,
    // 确认发送
    sendEmail: `${baseUri}/v1/decICustomsTrack/sendEmail`,
    // 查询所有邮箱
    selectEmails: `${baseUri}/v1/warringEmailConfig/selectEmails`
  },
  decErpEHeadN: {
    insert: `${baseUri}/v1/decErpEHeadN`,
    delete: `${baseUri}/v1/decErpEHeadN`,
    rejected: `${baseUri}/v1/decErpEHeadN/rejected`,
    update: `${baseUri}/v1/decErpEHeadN`,
    updateBack: `${baseUri}/v1/decErpEHeadN/updateBack`,
    copy: `${baseUri}/v1/decErpEHeadN/copy`,
    selectAllPaged: `${baseUri}/v1/decErpEHeadN/list`,
    getSerialNum: `${baseUri}/v1/decErpEHeadN/getSerialNum`,
    exportUrl: `${baseUri}/v1/decErpEHeadN/export`,
    aeoReview: {
      selectAllPaged: `${baseUri}/v1/decErpEHeadN/selectListForErpAudit`
    },
    backFillEntryNo: `${baseUri}/v1/decErpEHeadN/roback`,
    getSumListContent: `${baseUri}/v1/decErpEHeadN/getSumListContent`,
    leadHeadList: `${baseUri}/v1/decErpEHeadN/leadHeadList`,
    getEntityByKey: `${baseUri}/v1/decErpEHeadN/select`,

    //强制修改状态
    checkForForceChangeStatus: `${baseUri}/v1/decErpEHeadN/checkForForceChangeStatus`, // /{headId} 强制修改状态校验
    forceChangeStatus: `${baseUri}/v1/decErpEHeadN/forceChangeStatus`,                 // 强制修改状态 /v1/decErpIHeadN/forceChangeStatus/{headId}
    // 锁定随附单据
    lockDocAccompanying: `${baseUri}/v1/decErpEHeadN/lock`,
    // 解锁随附单据
    unlockDocAccompanying: `${baseUri}/v1/decErpEHeadN/lock`,
    //新增校验
    insertCheck: `${baseUri}/v1/decErpEHeadN/insertCheck`
  },
  decErpEListN: {
    insert: `${baseUri}/v1/decErpEListN`,
    delete: `${baseUri}/v1/decErpEListN`,
    update: `${baseUri}/v1/decErpEListN`,
    batchUpdate: `${baseUri}/v1/decErpEListUpdate/return`,
    selectAllPaged: `${baseUri}/v1/decErpEListN/list`,
    total: `${baseUri}/v1/decErpEListN/total`,
    exportUrl: `${baseUri}/v1/decErpEListN/export`,
    // 提取ERP数据-勾选
    extractErpDataByChoose: `${baseUri}/v1/decErpEListN/extractErpDataByChoose`,
    // 提取ERP数据-条件筛选
    extractErpDataByQuery: `${baseUri}/v1/decErpEListN/extractErpDataByQuery`,
    // 导入提取
    importExtract: `${baseUri}/v1/decErpIListPickUp`,
    // 导入提取(导出)
    listPickUpExport: `${baseUri}/v1/decErpIListPickUp`,
    // 马培德布定制导人模板导出(导出)
    listCustomMadeExport: `${baseUri}/v1/decErpEListCustomMade`,
    // 马培德布定制导人模板导出(导出)
    importCustomMadeExport: `${baseUri}/v1/decErpEListCustomMade`,
    // 校验【数量*单价=/总价】
    checkDecTotal: `${baseUri}/v1/decErpEListN/checkDecTotal`,
    // 生成箱单信息
    createPackingInfo: `${baseUri}/v1/decEPackingList/generatePackinglist`
  },
  decErpListMove: {
    imports: {
      moveUp: `${baseUri}/v1/decErpIListN/moveUp`,            // 上移接口
      moveDown: `${baseUri}/v1/decErpIListN/moveDown`,        // 下移接口
      getMoveFlag: `${baseUri}/v1/decErpIListN/getMoveFlag`   // 修改导入功能前需要调用接口,返回moveFlag为1是提示用户
    },
    exports: {
      moveUp: `${baseUri}/v1/decErpEListN/moveUp`,            // 上移接口
      moveDown: `${baseUri}/v1/decErpEListN/moveDown`,        // 下移接口
      getMoveFlag: `${baseUri}/v1/decErpEListN/getMoveFlag`   // 修改导入功能前需要调用接口,返回moveFlag为1是提示用户
    }
  },
  eBill: {
    selectAllPaged: `${baseUri}/v1/decEBillHead/list`,
    export: `${baseUri}/v1/decEBillHead/export`,
    //生成清单验证
    createBillTest: `${baseUri}/v1/decEBillHead/createBillsCheck`,
    //根据提单表头sid生成清单
    createBill: `${baseUri}/v1/decEBillHead/createBills`,
    //根据提单表头sid生成清单(金关)
    createBillsJg: `${baseUri}/v1/decEBillHead/createBillsJg`,
    //根据提单SID，获取提单下所有清单的EMS_LIST_NO和清单的SID
    selectAllBillNo: `${baseUri}/v1/decEBillHead/selectBillListByHeadId`,
    //根据sid查询清单表头数据
    selectBillHeader: `${baseUri}/v1/decEBillHead/selectBySId`,
    // 打印校验
    printCheck: `${baseUri}/v1/decEBillHead/printCheck`,
    //打印清单
    printBill: `${baseUri}/v1/decEBillHead/printBill`,
    //打印企业料号清单草单
    printBillFac: `${baseUri}/v1/decEBillHead/printBillFac`,
    //打印报关单
    printEntry: `${baseUri}/v1/decEBillHead/printEntry`,
    //根据提单SID,获取所有的报关追踪单SID和对应的清单编号
    selectListByHeadId: `${baseUri}/v1/decEBillHead/selectListByHeadId`,
    //发送清单
    sendBill: `${baseUri}/v1/postApiReturnInfo/sendBillApi`,
    //发送清单(批量)
    sendBills: `${baseUri}/v1/postApiReturnInfo/sendBillApiList`,
    // 更新
    update: `${baseUri}/v1/decEBillHead`,
    // 获取报关单信息
    getEntryInfoByEmsListNo: `${baseUri}/v1/decEBillHead/getEntryInfoByEmsListNo`,
    // 清单撤回
    sendBillCancel: `${baseUri}/v1/dataSend/entry/cancelEntry`,
    // 计算运保费
    costCalculateUrl: `${baseUri}/v1/decEBillHead/getAutomatic`,
    // 进口清单
    printBillList: `${baseUri}/v1/decEBillHead/printBillList`,
    // 同步清单
    billSync: `${baseUri}/v1/decEBillHead/billSync`
  },
  eBillList: {
    getBillTotal: `${baseUri}/v1/decEBillList/total`,
    selectAllPaged: `${baseUri}/v1/decEBillList/list`
  },
  eLogisticsTracking: {
    selectBySId: `${baseUri}/v1/decELogisticsTrack/selectBySId`,
    update: `${baseUri}/v1/decELogisticsTrack`,
    selectAllPaged: `${baseUri}/v1/decELogisticsTrack/list`,
    exportUrl: `${baseUri}/v1/decELogisticsTrack/export`,
    updateMult: `${baseUri}/v1/decELogisticsTrack/updateM`,
    batchUpdate: `${baseUri}/v1/decELogisticsTrackUpdate`
  },
  eCustomsTracking: {
    selectBySId: `${baseUri}/v1/decECustomsTrack/selectBySId`,
    update: `${baseUri}/v1/decECustomsTrack`,
    updateMult: `${baseUri}/v1/decECustomsTrack/updateM`,
    selectAllPaged: `${baseUri}/v1/decECustomsTrack/list`,
    exportUrl: `${baseUri}/v1/decECustomsTrack/export`,
    // 批量导入导出
    batchUpdate: `${baseUri}/v1/decECustomsTrackUpdate`,
    // 设置完成进度
    setCompleteSchedule: `${baseUri}/v1/decECustomsTrack/reach`,
    // 财务确认/取消校验
    financeCheck: `${baseUri}/v1/decECustomsTrack/financeeCheck`,
    // 财务确认/取消
    finance: `${baseUri}/v1/decECustomsTrack/financee`
  },
  template: {
    delete: `${baseUri}/v1/decErpHeadTemplete`,
    insert: `${baseUri}/v1/decErpHeadTemplete`,
    selectAllPaged: `${baseUri}/v1/decErpHeadTemplete/list`,
    selectInsertUser: `${baseUri}/v1/decErpHeadTemplete/selectInsertUser`,
    export: `${baseUri}/v1/decErpHeadTemplete/export`
  },
  qtySpillRemind: {
    selectAllPaged: `${baseUri}/v1/decErpQtyCalcRecord/list`,
    export: `${baseUri}/v1/decErpQtyCalcRecord/export`
  },
  // 账册api请求返回信息
  customsDeclaration: {
    entry: `${baseUri}/v1/dataSend/entry/send`,
    sendLjqApi: `${baseUri}/v1/dataSend/entry/declEntry`,
    sendLjqApis: `${baseUri}/v1/dataSend/entry/declEntryList`,       // sendLjqApis`
    printIEntryList: `${baseUri}/v1/decIBillHead/printEntryList`,    // 打印草单(进口)
    printEEntryList: `${baseUri}/v1/decEBillHead/printEntryList`,     // 打印草单(出口)
    sendComplete:`${baseUri}/v1/dataSend/entry/sendComplete`, //发送完整申报
  },
  // 自定义参数
  customsParams: {
    getParamValues: `${baseUri}/v1/biCustomerParams/getParamValues`
  },
  iEntry: {
    selectAllPaged: `${baseUri}/v1/decIEntryHead/list`,
    export: `${baseUri}/v1/decIEntryHead/export`,
    selectAllListPaged: `${baseUri}/v1/decIEntryList/list`,
    // 生成到货通知
    generateArrivalNotification: `${baseUri}/v1/`,
    // 打印出到货通知信息
    printArrivalNotice: `${baseUri}/v1/decICustomsTrack/arrivalNotice`,
    update: `${baseUri}/v1/decIEntryList`,
    exportEntry: `${baseUri}/v1/decIEntryList/export`,
    // 获取集装箱信息
    list: `${baseUri}/v1/decIEntryContainer/list`,
    // 新增集装箱信息
    decIEntryContainer: `${baseUri}/v1/decIEntryContainer`,
    recallSending: `${baseUri}/v1/decIEntryHead/recallSending`
  },
  eEntry: {
    selectAllPaged: `${baseUri}/v1/decEEntryHead/list`,
    export: `${baseUri}/v1/decEEntryHead/export`,
    selectAllListPaged: `${baseUri}/v1/decEEntryList/list`,
    update: `${baseUri}/v1/decEEntryList`,
    exportEntry: `${baseUri}/v1/decEEntryList/export`
  },
  cert: {
    rest: `${baseUri}/v1/decErpListCertificate`,
    extractCertificate: `${baseUri}/v1/cert/deduct/extractCertificate`,  // 提取证书
    getInvolveCerts: `${baseUri}/v1/cert/deduct/getInvolveCerts`,  // 获取涉证列表
    getSpecifiedCertList: `${baseUri}/v1/cert/deduct/getSpecifiedCertList`,  // 获取指定证书台账表体列表
    save: `${baseUri}/v1/cert/deduct/selectCertificate`  // 选择证书
  },
  xinYue: {
    exportUrl: `${baseUri}/v1/xyszcReport/export`,
    selectAllPaged: `${baseUri}/v1/xyszcReport/list`
  },
  packingInfo: {
    insert: `${baseUri}/v1/decEPackingList`,
    delete: `${baseUri}/v1/decEPackingList`,
    update: `${baseUri}/v1/decEPackingList`,
    total: `${baseUri}/v1/decEPackingList/total`,
    exportUrl: `${baseUri}/v1/decEPackingList/export`,
    selectAllPaged: `${baseUri}/v1/decEPackingList/list`,
    checkByNo: `${baseUri}/v1/decEPackingList/validityByGNo`,
    totalCheck: `${baseUri}/v1/decEPackingList/totalValidity`,
    exportPackDetailUrl: `${baseUri}/v1/decEPackingList/printEntry`
  },
  packingMaintain: {
    insert: `${baseUri}/v1/gwPackingMaintain`,
    delete: `${baseUri}/v1/gwPackingMaintain`,
    update: `${baseUri}/v1/gwPackingMaintain`,
    exportUrl: `${baseUri}/v1/gwPackingMaintain/export`,
    selectAllPaged: `${baseUri}/v1/gwPackingMaintain/list`,
    checkByNo: `${baseUri}/v1/gwPackingMaintain/validityByGNo`,             // 按料号校验
    totalCheck: `${baseUri}/v1/gwPackingMaintain/totalValidity`,            // 总量校验
    exportPackDetailUrl: `${baseUri}/v1/gwPackingMaintain/printEntry`,      // 装箱明细查看
    bodyNetWtFillBack: `${baseUri}/v1/gwPackingMaintain/bodyNetWtFillBack`  // 表体净重回填
  },
  // 随附单证
  decAttachedDocuments: {
    imports: {
      insert: `${baseUri}/v1/decErpIDocu`,
      delete: `${baseUri}/v1/decErpIDocu`,
      update: `${baseUri}/v1/decErpIDocu`,
      selectAllPaged: `${baseUri}/v1/decErpIDocu/list`
    },
    exports: {
      insert: `${baseUri}/v1/decErpEDocu`,
      delete: `${baseUri}/v1/decErpEDocu`,
      update: `${baseUri}/v1/decErpEDocu`,
      selectAllPaged: `${baseUri}/v1/decErpEDocu/list`
    }
  },
  // 平衡表
  balanceSheet: {
    exportUrl: `${baseUri}/v1/optorun/balanceResult/export`,
    selectAllPaged: `${baseUri}/v1/optorun/balanceResult/list`
  },
  // 车柜信息
  carCabinetInfo: {
    // 物流追踪相关
    insert: `${baseUri}/v1/decLogisticsCabinet`,
    update: `${baseUri}/v1/decLogisticsCabinet`,
    delete: `${baseUri}/v1/decLogisticsCabinet`,
    selectAllPaged: `${baseUri}/v1/decLogisticsCabinet/list`,
    // 费用预估相关
    insertFee: `${baseUri}/v1/costCabinetType`,
    updateFee: `${baseUri}/v1/costCabinetType`,
    deleteFee: `${baseUri}/v1/costCabinetType`,
    insertAllFee: `${baseUri}/v1/costCabinetType/insertAll`,
    selectAllPagedFee: `${baseUri}/v1/costCabinetType/list`
  },
  // 舱单信息
  warehouseReceiptInfo: {
    getIInfo: `${baseUri}/v1/manifestInfo/getIInfo`,     // 进口
    getEInfo: `${baseUri}/v1/manifestInfo/getEInfo`      // 出口
  }
}

/**
 * 导入模板路径
 * @type {{bondExgImportTemplate: string, midImportTemplate: string, MaterialForClassifiedTemplate: string, noBondImportTemplate: string, invoiceTemplateConfiguration: string, bondOrgImportTemplate: string, bondImgImportTemplate: string, erpBodyImportTemplate: string, bondUpdateImportTemplate: string, certificateImportTemplate: string, baseInfoImportTemplate: string}}
 */
export const importFilePath = {
  // 备案料件/成品归类导入模板
  bondOrgImportTemplate: `${frtBaseUri}biz/${namespace}/download/bondOrgImportTemplate.xlsx`,
  // 企业料件信息导入模板
  bondImgImportTemplate: `${frtBaseUri}biz/${namespace}/download/bondImportTemplate.xlsx`,
  // 企业成品信息导入模板
  bondExgImportTemplate: `${frtBaseUri}biz/${namespace}/download/bondImportTemplate.xlsx`,
  // 企业(料件/成品)信息修改导入模板
  bondUpdateImportTemplate: `${frtBaseUri}biz/${namespace}/download/bondUpdateImportTemplate.xlsx`,
  // 非保税预归类导入模板
  noBondImportTemplate: `${frtBaseUri}biz/${namespace}/download/noBondImportTemplate.xlsx`,
  // 提单表体导入模板
  erpBodyImportTemplate: `${frtBaseUri}biz/${namespace}/download/erpBodyImportTemplate.xlsx`,
  // 基础资料模板导出地址
  baseInfoImportTemplate: `${frtBaseUri}biz/${namespace}/download/baseInfoImportTemplate.xlsx`,
  // 检验管理导入模板(三合一)
  certificateImportTemplate: `${frtBaseUri}biz/${namespace}/download/certificateImportTemplate.xlsx`,
  // 中期核查==>企业进出口数据(二合一)
  midImportTemplate: `${frtBaseUri}biz/${namespace}/download/midImportTemplate.xlsx`,
  // 待归类物料==>保税料件/成品、非保税(三合一)
  MaterialForClassifiedTemplate: `${frtBaseUri}biz/${namespace}/download/Materialtobeclassified.xlsx`,
  // 进口/出口发票箱单列表页面: 下载配置字段
  invoiceTemplateConfiguration: `${frtBaseUri}biz/${namespace}/download/发票模板配置栏位信息.xlsx`
}

export const importExcel = {
  importExcel: `${baseUri}/v1/importExcel/import`,
  insertData: `${baseUri}/v1/importExcel/insertData`
}

export const attachedInfo = {
  insert: `${baseUri}/v1/attached`,
  insert3M: `${baseUri}/v1/attached/insert3M`,
  insertAll: `${baseUri}/v1/attached/insertAll`,
  list: `${baseUri}/v1/attached/list`,
  delete: `${baseUri}/v1/attached`,
  get: `${baseUri}/v1/attached`,
  getPreAcmpInfo: `${baseUri}/v1/attached/getPreAcmpInfo`,
  business: `${baseUri}/v1/attached/business`,
}

/**
 * 报表中心
 * @type {{freightManagement: {imports: {exportUrl: string, selectAllPaged: string, update: string}, exports: {exportUrl: string, selectAllPaged: string, update: string}}, eBill: {getHeadSum: string, exportAll: string, getListSum: string, exportHead: string, selectAllPaged: string, selectAllHeadPaged: string}, eBillOfLading: {exportHeadUrl: string, exportUrl: string, selectAllPaged: string, selectAllHeadPaged: string}, financeReport: {list1: string, export1: string, list2: string, export2: string}, eEntry: {selectAllPaged: string, exportHead: string, export: string, selectAllPagedhead: string}, iEntry: {selectAllPaged: string, exportHead: string, export: string, selectAllPagedhead: string}, iBill: {getHeadSum: string, exportAll: string, getListSum: string, exportHead: string, selectAllPaged: string, selectAllHeadPaged: string}, iBillOfLading: {exportHeadUrl: string, exportUrl: string, selectAllPaged: string, selectAllHeadPaged: string}, shipmentList: {export1: string, selectAllPaged: string, export2: string, checkData: string}}}
 */
export const reportCenter = {
  iBill: {
    exportHead: `${baseUri}/v1/decIBill/exportHead`,
    getHeadSum: `${baseUri}/v1/decIBill/getHeadSum`,
    selectAllHeadPaged: `${baseUri}/v1/decIBill/listHead`,
    //根据查询条件查询警管理信息表,返回分页数据
    selectAllPaged: `${baseUri}/v1/decIBill/list`,
    exportAll: `${baseUri}/v1/decIBill/export`,
    getListSum: `${baseUri}/v1/decIBill/getListSum`
  },
  iEntry: {
    selectAllPaged: `${baseUri}/v1/decIEntry/list`,
    selectAllPagedhead: `${baseUri}/v1/decIEntry/listHead`,
    export: `${baseUri}/v1/decIEntry/export`,
    exportHead: `${baseUri}/v1/decIEntry/exportHead`
  },
  eBill: {
    getHeadSum: `${baseUri}/v1/decEBill/getHeadSum`,
    exportHead: `${baseUri}/v1/decEBill/exportHead`,
    selectAllHeadPaged: `${baseUri}/v1/decEBill/listHead`,

    //根据查询条件查询警管理信息表,返回分页数据
    selectAllPaged: `${baseUri}/v1/decEBill/list`,
    exportAll: `${baseUri}/v1/decEBill/export`,
    getListSum: `${baseUri}/v1/decEBill/getListSum`
  },
  eEntry: {
    selectAllPaged: `${baseUri}/v1/decEEntry/list`,
    selectAllPagedhead: `${baseUri}/v1/decEEntry/listHead`,
    export: `${baseUri}/v1/decEEntry/export`,
    exportHead: `${baseUri}/v1/decEEntry/exportHead`
  },
  iBillOfLading: {
    // 表头
    exportHeadUrl: `${baseUri}/v1/decErpI/exportHead`,
    selectAllHeadPaged: `${baseUri}/v1/decErpI/listHead`,
    // 综合
    exportUrl: `${baseUri}/v1/decErpI/export`,
    selectAllPaged: `${baseUri}/v1/decErpI/list`,
    // 汇总金额
    getDecTotal: `${baseUri}/v1/decErpI/sumDecTotal`
  },
  eBillOfLading: {
    // 表头
    exportHeadUrl: `${baseUri}/v1/decErpE/exportHead`,
    selectAllHeadPaged: `${baseUri}/v1/decErpE/listHead`,
    // 综合
    exportUrl: `${baseUri}/v1/decErpE/export`,
    selectAllPaged: `${baseUri}/v1/decErpE/list`,
    // 汇总金额
    getDecTotal: `${baseUri}/v1/decErpE/sumDecTotal`
  },
  financeReport: {
    list1: `${baseUri}/v1/ryFinanceReportOne/list`,
    export1: `${baseUri}/v1/ryFinanceReportOne/export`,
    list2: `${baseUri}/v1/ryFinanceReportTwo/list`,
    export2: `${baseUri}/v1/ryFinanceReportTwo/export`
  },
  shipmentList: {
    checkData: `${baseUri}/v1/decInvoiceShipmentList/checkData`,
    export1: `${baseUri}/v1/decInvoiceShipmentList/export1`,
    export2: `${baseUri}/v1/decInvoiceShipmentList/export2`,
    selectAllPaged: `${baseUri}/v1/decInvoiceShipmentList/list`
  },
  freightManagement: {
    imports: {
      update: `${baseUri}/v1/decErpIFreightList`,
      exportUrl: `${baseUri}/v1/decErpIFreightList/export`,
      selectAllPaged: `${baseUri}/v1/decErpIFreightList/list`
    },
    exports: {
      update: `${baseUri}/v1/decErpEFreightList`,
      exportUrl: `${baseUri}/v1/decErpEFreightList/export`,
      selectAllPaged: `${baseUri}/v1/decErpEFreightList/list`
    }
  },
  //丰田定制
  decEReportFt: {
    selectAllHeadPaged: `${baseUri}/v1/decErpEFt/listHead`,
    // 综合
    selectAllPaged: `${baseUri}/v1/decErpEFt/list`,
    // 汇总金额
    getDecTotal: `${baseUri}/v1/decErpEFt/sumDecTotal`
  },
  decIReportFt: {
    selectAllHeadPaged: `${baseUri}/v1/decErpIFt/listHead`,
    // 综合
    selectAllPaged: `${baseUri}/v1/decErpIFt/list`,
    // 汇总金额
    getDecTotal: `${baseUri}/v1/decErpIFt/sumDecTotal`
  },
  /**
   * 统计报表
   */
  statisticalReport: {
    /**
     * 物料号数量
     */
    itemNumber: {
      exportUrl: `${baseUri}/v1/statRpt/mat/facGNoNumber/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/mat/facGNoNumber/list`
    },
    /**
     * 涉及HS数量
     */
    hsInvolvedNo: {
      exportUrl: `${baseUri}/v1/statRpt/mat/hsCodeNumber/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/mat/hsCodeNumber/list`
    },
    /**
     * 总价前10排名
     */
    top10OfTotalPrice: {
      imports: {
        exportUrl: `${baseUri}/v1/statRpt/mat/decTotalTopK/exportI`,
        selectAllPaged: `${baseUri}/v1/statRpt/mat/decTotalTopK/listI`
      },
      exports: {
        exportUrl: `${baseUri}/v1/statRpt/mat/decTotalTopK/exportE`,
        selectAllPaged: `${baseUri}/v1/statRpt/mat/decTotalTopK/listE`
      }
    },
    /**
     * 单价前10排名
     */
    top10UnitPrice: {
      imports: {
        exportUrl: `${baseUri}/v1/statRpt/mat/decPriceTopK/exportI`,
        selectAllPaged: `${baseUri}/v1/statRpt/mat/decPriceTopK/listI`
      },
      exports: {
        exportUrl: `${baseUri}/v1/statRpt/mat/decPriceTopK/exportE`,
        selectAllPaged: `${baseUri}/v1/statRpt/mat/decPriceTopK/listE`
      }
    },
    /**
     * 税金前10统计
     */
    top10Statistics: {
      exportUrl: `${baseUri}/v1/statRpt/mat/taxTopK/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/mat/taxTopK/list`
    },
    /**
     * 货运代理
     */
    shippingAgent: {
      exportUrl: `${baseUri}/v1/statRpt/erp/forwardPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/forwardPercent/list`
    },
    /**
     * 报关行
     */
    customsBroker: {
      exportUrl: `${baseUri}/v1/statRpt/erp/agentPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/agentPercent/list`
    },
    /**
     * 入境口岸
     */
    portOfEntry: {
      exportUrl: `${baseUri}/v1/statRpt/erp/ieportPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/ieportPercent/list`
    },
    /**
     * 监管方式
     */
    supervisionMethod: {
      exportUrl: `${baseUri}/v1/statRpt/erp/tradeModePercent/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/tradeModePercent/list`
    },
    /**
     * 运输方式
     */
    trafMode: {
      exportUrl: `${baseUri}/v1/statRpt/erp/trafModePercent/export`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/trafModePercent/list`
    },
    /**
     * 价格说明 - 进口
     */
    promiseImport: {
      exportUrl: `${baseUri}/v1/statRpt/erp/promiseItemStat/exportI`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/promiseItemStat/listI`
    },
    /**
     * 价格说明 - 出口
     */
    promiseExport: {
      exportUrl: `${baseUri}/v1/statRpt/erp/promiseItemStat/listE`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/promiseItemStat/listI`
    },
    /**
     * 物流异常 - 进口
     */
    logisticsImport: {
      exportUrl: `${baseUri}/v1/statRpt/erp/logisticsAbnormal/exportI`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/logisticsAbnormal/listI`
    },
    /**
     * 物流异常 - 出口
     */
    logisticsExport: {
      exportUrl: `${baseUri}/v1/statRpt/erp/logisticsAbnormal/exportE`,
      selectAllPaged: `${baseUri}/v1/statRpt/erp/logisticsAbnormal/listE`
    },
    /**
     * 核注清单
     */
    checklist: {
      exportUrl: `${baseUri}/v1//export`,
      selectAllPaged: `${baseUri}/v1//list`
    },
    /**
     * 报关单
     */
    customsDeclaration: {
      exportUrl: `${baseUri}/v1//export`,
      selectAllPaged: `${baseUri}/v1//list`
    },
    /**
     * 进口制单
     */
    importMaker: {
      exportUrl: `${baseUri}/v1//export`,
      selectAllPaged: `${baseUri}/v1//list`
    },
    /**
     * 出口制单
     */
    exportMaker: {
      exportUrl: `${baseUri}/v1//export`,
      selectAllPaged: `${baseUri}/v1//list`
    }
  },
  /**
   * 集团统计报表
   */
  statisticalReportGroup: {
    /**
     * 物料号数量
     */
    itemNumber: {
      exportUrl: `${baseUri}/v1/statRptGroup/mat/facGNoNumber/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/mat/facGNoNumber/list`
    },
    /**
     * 涉及HS数量
     */
    hsInvolvedNo: {
      exportUrl: `${baseUri}/v1/statRptGroup/mat/hsCodeNumber/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/mat/hsCodeNumber/list`
    },
    /**
     * 总价前10排名
     */
    top10OfTotalPrice: {
      imports: {
        exportUrl: `${baseUri}/v1/statRptGroup/mat/decTotalTopK/exportI`,
        selectAllPaged: `${baseUri}/v1/statRptGroup/mat/decTotalTopK/listI`
      },
      exports: {
        exportUrl: `${baseUri}/v1/statRptGroup/mat/decTotalTopK/exportE`,
        selectAllPaged: `${baseUri}/v1/statRptGroup/mat/decTotalTopK/listE`
      }
    },

    /**
     * 货运代理
     */
    shippingAgent: {
      exportUrl: `${baseUri}/v1/statRptGroup/erp/forwardPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/erp/forwardPercent/list`
    },
    /**
     * 报关行
     */
    customsBroker: {
      exportUrl: `${baseUri}/v1/statRptGroup/erp/agentPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/erp/agentPercent/list`
    },
    /**
     * 入境口岸
     */
    portOfEntry: {
      exportUrl: `${baseUri}/v1/statRptGroup/erp/ieportPercent/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/erp/ieportPercent/list`
    },
    /**
     * 监管方式
     */
    supervisionMethod: {
      exportUrl: `${baseUri}/v1/statRptGroup/erp/tradeModePercent/export`,
      selectAllPaged: `${baseUri}/v1/statRptGroup/erp/tradeModePercent/list`
    },

  },
  /**
   * 异步任务执行方法
   */
  jobComm: {
    insertJob: `${baseUri}/v1/gwstdJob/insertJob`,
    getLastJob: `${baseUri}/v1/gwstdJob/getLastJobInfo`
  },
  // 出口单证统计表
  exportDocumentStatistics: {
    selectAllPaged: `${baseUri}/v1/technimarkDzReport/list`
  }
}

export const emsApi = {
  // 发送清单数据=====================================
  SendBillApi: `${baseUri}/v1/postApiReturnInfo/sendBillApi`,
  // 发送清单数据(批量)=====================================
  sendBillApis: `${baseUri}/v1/postApiReturnInfo/sendBillApiList`,
  // 获取发送清单返回的错误信息
  getSendErrInfo: `${baseUri}/v1/postApiReturnInfo`,
  //发送料件备案数据
  SendImgApi: `${baseUri}/v1/postApiReturnInfo/sendExgImgApi/I`,
  //发送成品备案数据
  SendExgApi: `${baseUri}/v1/postApiReturnInfo/sendExgImgApi/E`
}

/**
 * 检验管理
 * @type {{comm: {getCopGNo: string}, catalog: {head: {all: string, rest: string, list: string, export: string, delete: string}, body: {rest: string, list: string, export: string, delete: string}}, use: {getQtyUseDetail: string, list: string, getTimesUseDetail: string}, book: {head: {rest: string, enableCert: string, list: string, export: string, delete: string}, body: {warningExport: string, rest: string, warning: string, getCatalogList: string, list: string, export: string, delete: string}}, certificate: {head: {selectDocumentNo: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, body: {exportUrl: string, selectAllPaged: string, insert: string, update: string, getBringOut: string, delete: string}}, certificateMat: {head: {selectAllPaged: string, insert: string, update: string, delete: string}}}}
 */
export const cert = {
  comm: {
    getCopGNo: `${baseUri}/v1/certificateMat/getCopGNo`
  },
  catalog: {
    head: {
      rest: `${baseUri}/v1/cert/catalog`,
      list: `${baseUri}/v1/cert/catalog/list`,
      all: `${baseUri}/v1/cert/catalog/all`,
      export: `${baseUri}/v1/cert/catalog/exports`,
      delete: `${baseUri}/v1/cert/catalog/batch`
    },
    body: {
      rest: `${baseUri}/v1/cert/catalog/body`,
      list: `${baseUri}/v1/cert/catalog/body/list`,
      export: `${baseUri}/v1/cert/catalog/body/export`,
      delete: `${baseUri}/v1/cert/catalog/body/batch`
    }
  },
  book: {
    head: {
      rest: `${baseUri}/v1/cert/book`,
      list: `${baseUri}/v1/cert/book/list`,
      export: `${baseUri}/v1/cert/book/export`,
      delete: `${baseUri}/v1/cert/book/batch`,
      enableCert: `${baseUri}/v1/cert/book/enableCert`
    },
    body: {
      rest: `${baseUri}/v1/cert/book/body`,
      list: `${baseUri}/v1/cert/book/body/list`,
      export: `${baseUri}/v1/cert/book/body/export`,
      delete: `${baseUri}/v1/cert/book/body/batch`,
      warning: `${baseUri}/v1/cert/book/body/warning`,
      warningExport: `${baseUri}/v1/cert/book/body/warning/export`,
      getCatalogList: `${baseUri}/v1/cert/catalog/body/getCatalogList`
    }
  },
  use: {
    list: `${baseUri}/v1/cert/use/getCertificateUseDetails`,
    getQtyUseDetail: `${baseUri}/v1/cert/use/getQtyUseDetail`,  //  已用数量查询
    getTimesUseDetail: `${baseUri}/v1/cert/use/getTimesUseDetail`   //  已用次数查询
  },
  certificate: {
    head: {
      insert: `${baseUri}/v1/certificate`,
      delete: `${baseUri}/v1/certificate`,
      update: `${baseUri}/v1/certificate`,
      exportUrl: `${baseUri}/v1/certificate/export`,
      selectAllPaged: `${baseUri}/v1/certificate/list`,
      selectDocumentNo: `${baseUri}/v1/certificateMat/selectDocumentNo`
    },
    body: {
      insert: `${baseUri}/v1/certificateList`,
      delete: `${baseUri}/v1/certificateList`,
      update: `${baseUri}/v1/certificateList`,
      exportUrl: `${baseUri}/v1/certificateList/export`,
      selectAllPaged: `${baseUri}/v1/certificateList/list`,
      getBringOut: `${baseUri}/v1/certificateMat/getBringOut`
    }
  },
  certificateMat: {
    head: {
      insert: `${baseUri}/v1/certificateMat`,
      delete: `${baseUri}/v1/certificateMat`,
      update: `${baseUri}/v1/certificateMat`,
      // exportUrl: `${baseUri}/v1/certificateMat/export`,
      // selectAllPaged: `${baseUri}/v1/certificateMat/list`,
      selectAllPaged: `${baseUri}/v1/cert/catalog/body/listAll`
    }
  }
}

/**
 * 出口发票箱单
 * @type {{deletebill: {deleteBill: string}, templatelist: {templateList: string}, productbill: {productBill: string}, search: {searchInfor: string}, updatebill: {updateBill: string}, download: {Ddownload: string}, billlist: {billList: string}, invoiceInfoTemplate: {selectAllPaged: string, insert: string, delete: string}, addbill: {addBill: string}, custumer: {custumerInfor: string}, searchno: {searchNo: string}, clockbill: {clockBill: string}, printbill: {printBill: string}}}
 */
export const exportBill = {
  //获取客户信息
  custumer: {
    custumerInfor: `${baseUri}/v1/biClientInformation/selectKeyValueListByCustomerType`
  },
  //查询
  search: {
    searchInfor: `${baseUri}/v1/decInvoiceBoxHeadE/list`
  },
  //单据内部编号获取提单表头信息
  searchno: {
    searchNo: `${baseUri}/v1/decErpEHeadN/getErpInfoByCopEmsNo`
  },
  //发票模板通用接口
  templatelist: {
    templateList: `${baseUri}/v1/receiptTemplate/getTemplateList`
  },
  //新增
  addbill: {
    addBill: `${baseUri}/v1/decInvoiceBoxHeadE`
  },
  //编辑
  updatebill: {
    updateBill: `${baseUri}/v1/decInvoiceBoxHeadE`
  },
  //删除
  deletebill: {
    deleteBill: `${baseUri}/v1/decInvoiceBoxHeadE`
  },
  //锁定+解锁
  clockbill: {
    clockBill: `${baseUri}/v1/decInvoiceBoxHeadE/changeStatus`
  },
  //生成单据
  productbill: {
    productBill: `${baseUri}/v1/decInvoiceBoxHeadE/createBills`
  },
  //单据list
  billlist: {
    billList: `${baseUri}/v1/decInvoiceBoxListE/list`
  },
  //下载文件
  download: {
    Ddownload: `${baseUri}/v1/decInvoiceBoxListE/downloadPdf`,
    DdownloadPdf: `${baseUri}/v1/decInvoiceBoxListE/download`,
    downloadForEntrust: `${baseUri}/v1/decInvoiceBoxListE/downloadForEntrust`
  },
  //打印发票
  printbill: {
    printBill: `${baseUri}/v1/decInvoiceBoxListE/getDownloadListInfoByHeadId`
  },
  invoiceInfoTemplate: {
    insert: `${baseUri}/v1/decInvoiceBoxTemplateE`,
    delete: `${baseUri}/v1/decInvoiceBoxTemplateE`,
    selectAllPaged: `${baseUri}/v1/decInvoiceBoxTemplateE/list`
  }
}

/**
 * 进口发票箱单
 * @type {{deletebill: {deleteBill: string}, downloadPdf: {downloadPdf: string}, templatelist: {templateList: string}, productbill: {productBill: string}, search: {searchInfor: string}, updatebill: {updateBill: string}, download: {Ddownload: string}, billlist: {billList: string}, invoiceInfoTemplate: {selectAllPaged: string, insert: string, delete: string}, addbill: {addBill: string}, custumer: {custumerInfor: string}, searchno: {searchNo: string}, clockbill: {clockBill: string}, printbill: {printBill: string}}}
 */
export const importBill = {
  //获取客户信息
  custumer: {
    custumerInfor: `${baseUri}/v1/biClientInformation/selectKeyValueListByCustomerType`
  },
  //单据内部编号获取提单表头信息
  searchno: {
    searchNo: `${baseUri}/v1/decErpIHeadN/getErpInfoByCopEmsNo`
  },
  //发票模板通用接口
  templatelist: {
    templateList: `${baseUri}/v1/receiptTemplate/getTemplateList`
  },
  //查询
  search: {
    searchInfor: `${baseUri}/v1/decInvoiceBoxHeadI/list`
  },
  //新增
  addbill: {
    addBill: `${baseUri}/v1/decInvoiceBoxHeadI`
  },
  //编辑
  updatebill: {
    updateBill: `${baseUri}/v1/decInvoiceBoxHeadI`
  },
  //删除
  deletebill: {
    deleteBill: `${baseUri}/v1/decInvoiceBoxHeadI`
  },
  //锁定+解锁
  clockbill: {
    clockBill: `${baseUri}/v1/decInvoiceBoxHeadI/changeStatus`
  },
  //生成单据
  productbill: {
    productBill: `${baseUri}/v1/decInvoiceBoxHeadI/createBills`
  },
  //单据list
  billlist: {
    billList: `${baseUri}/v1/decInvoiceBoxListI/list`
  },
  //下载文件
  download: {
    Ddownload: `${baseUri}/v1/decInvoiceBoxListI/downloadPdf`,
    DdownloadPdf: `${baseUri}/v1/decInvoiceBoxListI/download`
  },
  //打印发票
  printbill: {
    printBill: `${baseUri}/v1/decInvoiceBoxListI/getDownloadListInfoByHeadId`
  },
  // 下载箱单信息
  downloadPdf: {
    downloadPdf: `${baseUri}/v1/gwstdSeal/download`
  },
  invoiceInfoTemplate: {
    insert: `${baseUri}/v1/decInvoiceBoxTemplateI`,
    delete: `${baseUri}/v1/decInvoiceBoxTemplateI`,
    selectAllPaged: `${baseUri}/v1/decInvoiceBoxTemplateI/list`
  }
}

/**
 * 发票模版
 * @type {{add: {addConfig: string}, templateDelete: {deleteConfig: string}, search: {getConfig: string}, invoiceInfoITemplate: {search: string}, use: {useConfig: string}, templateInfor: {templateConfig: string}, templatePush: {pushConfig: string}, supplier: {supplierConfig: string}, update: {updateConfig: string}, templateSearch: {searchConfig: string}, custumer: {custumerConfig: string}, delete: {deleteConfig: string}}}
 */
export const invoiceTemplate = {
  //获取供应商代码
  supplier: {
    supplierConfig: `${baseUri}/v1/biClientInformation/selectKeyValueListByCustomerType/PRD`
  },
  //获取客户code
  custumer: {
    custumerConfig: `${baseUri}/v1/biClientInformation/selectKeyValueListByCustomerType/CLI`
  },
  search: {
    getConfig: `${baseUri}/v1/receiptTemplate/list`
  },
  //新增
  add: {
    addConfig: `${baseUri}/v1/receiptTemplate`
  },
  //删除
  delete: {
    deleteConfig: `${baseUri}/v1/receiptTemplate`
  },
  //编辑
  update: {
    updateConfig: `${baseUri}/v1/receiptTemplate`
  },
  //启用
  use: {
    useConfig: `${baseUri}/v1/receiptTemplate/statusswitch`
  },
  //获取发票模版信息
  templateInfor: {
    templateConfig: `${baseUri}/v1/receiptTemplate/getReceiptInfo`
  },
  //发票模版上传
  templatePush: {
    pushConfig: `${baseUri}/v1/receiptTemplate/upload`
  },
  //发票模版下载
  templateSearch: {
    searchConfig: `${baseUri}/v1/receiptTemplate/getReceiptFile`
  },
  //发票模版删除
  templateDelete: {
    deleteConfig: `${baseUri}/v1/receiptTemplate/deletetemplate`
  },
  invoiceInfoITemplate: {
    //查询
    search: `${baseUri}`
    //新增

  }
}

/**
 * 报关单
 * @type {{entryDetail: {head: {selectAllPaged: string, exportUrl: string}}, entryRecord: {head: {add: {intoInfo: string}, select: {selectDelAllPaged: string}, intoJob: string, selectAllPaged: string, deleteFujian: string, getRecordPreview: string, delete: string, getOthersInfo: string, templatePus: {pushUpload: string}, exportsgUrl: string, getFujian: string, exportUrl: string, printUrl: string, intoJobByMdify: string, finalize: string, getRecord: string, updateIn: {updateInfo: string}, exportFin: string, getFileInfo: string, getRecordPreviewAttach: string}}}}
 */
export const entry = {
  entryRecord: {
    head: {
      select: { selectDelAllPaged: `${baseUri}/v1/decEntryDelMod/list` },                     // 报关查询中的删改单记录
      // select:{selectDelAllPaged:`http://localhost:9999/gwstd/api/v1/decEntryDelMod/list`}, // 报关查询中的删改单记录
      exportsgUrl: `${baseUri}/v1/decEntryDelMod/export`,                                     // 报关查询中的删改单记录 数据导出
      add: { intoInfo: `${baseUri}/v1/decEntryDelMod/add` },                                  // 报关查询中的删改单记录 新增
      updateIn: { updateInfo: `${baseUri}/v1/decEntryDelMod` },                               // 报关查询中的删改单记录 编辑
      delete: `${baseUri}/v1/decEntryDelMod`,
      templatePus: { pushUpload: `${baseUri}/v1/decEntryDelModFile/uploader` },               // 报关查询中的删改单记录 附件上传
      getFujian: `${baseUri}/v1/decEntryDelModFile/downloadFile`,                             // 报关查询中的删改单记录 附件下载
      deleteFujian: `${baseUri}/v1/decEntryDelModFile/delFile`,                               // 报关查询中的删改单记录 附件删除
      getFileInfo: `${baseUri}/v1/decEntryDelModFile/getListPagede`,                          // 报关查询中的删改单记录 获取附件详情展示
      getOthersInfo: `${baseUri}/v1/entryDetail/selByEntryNo`,                                // 报关查询中的删改单记录 敲击回车删改单号带出其他数据

      finalize: `${baseUri}/v1/decEntryDelModMistake/list`,                                   // 报关查询中的差错统计分析 查询汇总表数据的接口地址
      exportFin: `${baseUri}/v1/decEntryDelModMistake/export`,                                // 报关查询中的差错统计分析 数据导出
      intoJob: `${baseUri}/v1/gwstdJob/add`,                                                  // 报关查询中的差错统计分析 往任务表中插入数据 02 统计分析
      intoJobByMdify: `${baseUri}/v1/gwstdJob/addByModify`,                                   // 报关查询中的差错统计分析 往任务表中插入数据 01删改单记录查询

      printUrl: `${baseUri}/v1/entryRecord/print`,
      exportUrl: `${baseUri}/v1/entryRecord/export`,
      selectAllPaged: `${baseUri}/v1/entryRecord/list`,

      getRecord: `${baseUri}/v1/entryRecord/listAttach`,
      getRecordPreview: `${baseUri}/v1/entryRecord/preview`,
      getRecordPreviewAttach: `${baseUri}/v1/entryRecord/previewAttach`
    }
  },
  entryDetail: {
    head: {
      exportUrl: `${baseUri}/v1/entryDetail/export`,
      selectAllPaged: `${baseUri}/v1/entryDetail/list`
    }
  }
}

/**
 * 中期核查
 * @type {{customsCheck: {imports: {exportUrl: string, selectAllPaged: string, traceDeclare: {exportUrl: string, selectAllPaged: string}}, exports: {exportUrl: string, selectAllPaged: string, traceDeclare: {exportUrl: string, selectAllPaged: string}}}, enterpriseData: {imports: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, exports: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}, erpDetails: {delivery: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, warehousing: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}, jobTask: {insertJob: string, getLastJob: string}, relatedCheck: {imports: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, traceBackImport: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, exports: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, traceBackExport: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}}}
 */
export const interimVerification = {
  enterpriseData: {
    imports: {
      // 企业进口数据新增接口: post
      insert: `${baseUri}/v1/midI`,
      // 企业进口数据修改接口: put {sid}
      update: `${baseUri}/v1/midI`,
      // 企业进口数据删除接口: delete {sids}
      delete: `${baseUri}/v1/midI`,
      // 企业进口数据Excel数据导出接口: post
      exportUrl: `${baseUri}/v1/midI/export`,
      // 企业进口数据分页查询接口: post
      selectAllPaged: `${baseUri}/v1/midI/list`
    },
    exports: {
      // 企业出口数据接口新增接口: post
      insert: `${baseUri}/v1/midE`,
      // 企业出口数据接口修改接口: put {sid}
      update: `${baseUri}/v1/midE`,
      // 企业出口数据接口删除接口: delete {sids}
      delete: `${baseUri}/v1/midE`,
      // 企业出口数据接口Excel数据导出接口
      exportUrl: `${baseUri}/v1/midE/export`,
      // 企业出口数据接口分页查询接口
      selectAllPaged: `${baseUri}/v1/midE/list`
    }
  },
  customsCheck: {
    imports: {
      // 进口报关核对Excel数据导出接口: post
      exportUrl: `${baseUri}/v1/midDecICheck/export`,
      // 进口报关核对分页查询接口: post
      selectAllPaged: `${baseUri}/v1/midDecICheck/list`,
      // 追溯(申报数量)
      traceDeclare: {
        exportUrl: `${baseUri}/v1/midDecIReview/export`,
        selectAllPaged: `${baseUri}/v1/midDecIReview/list`
      }
    },
    exports: {
      // 出口报关核对Excel数据导出接口
      exportUrl: `${baseUri}/v1/midDecECheck/export`,
      // 出口报关核对分页查询接口
      selectAllPaged: `${baseUri}/v1/midDecECheck/list`,
      // 追溯(申报数量)
      traceDeclare: {
        exportUrl: `${baseUri}/v1/midDecEReview/export`,
        selectAllPaged: `${baseUri}/v1/midDecEReview/list`
      }
    }
  },
  relatedCheck: {
    imports: {
      insert: `${baseUri}/v1/midDecIRelCheck`,
      update: `${baseUri}/v1/midDecIRelCheck`,
      delete: `${baseUri}/v1/midDecIRelCheck`,
      exportUrl: `${baseUri}/v1/midDecIRelCheck/export`,
      selectAllPaged: `${baseUri}/v1/midDecIRelCheck/list`
    },
    exports: {
      insert: `${baseUri}/v1/midDecERelCheck`,
      update: `${baseUri}/v1/midDecERelCheck`,
      delete: `${baseUri}/v1/midDecERelCheck`,
      exportUrl: `${baseUri}/v1/midDecERelCheck/export`,
      selectAllPaged: `${baseUri}/v1/midDecERelCheck/list`
    },
    traceBackImport: {
      insert: `${baseUri}/v1/midDecIRelReview`,
      update: `${baseUri}/v1/midDecIRelReview`,
      delete: `${baseUri}/v1/midDecIRelReview`,
      exportUrl: `${baseUri}/v1/midDecIRelReview/export`,
      selectAllPaged: `${baseUri}/v1/midDecIRelReview/list`
    },
    traceBackExport: {
      insert: `${baseUri}/v1/midDecERelReview`,
      update: `${baseUri}/v1/midDecERelReview`,
      delete: `${baseUri}/v1/midDecERelReview`,
      exportUrl: `${baseUri}/v1/midDecERelReview/export`,
      selectAllPaged: `${baseUri}/v1/midDecERelReview/list`
    }
  },
  // ERP出入库明细信息
  erpDetails: {
    // 入库
    warehousing: {
      insert: `${baseUri}/v1/midImgIList`,
      update: `${baseUri}/v1/midImgIList`,
      delete: `${baseUri}/v1/midImgIList`,
      exportUrl: `${baseUri}/v1/midImgIList/export`,
      selectAllPaged: `${baseUri}/v1/midImgIList/list`
    },
    // 出库
    delivery: {
      insert: `${baseUri}/v1/midExgEList`,
      update: `${baseUri}/v1/midExgEList`,
      delete: `${baseUri}/v1/midExgEList`,
      exportUrl: `${baseUri}/v1/midExgEList/export`,
      selectAllPaged: `${baseUri}/v1/midExgEList/list`
    }
  },
  // 异步任务执行方法
  // MID_I/MID_E/MID_I_CHECK/MID_E_CHECK
  jobTask: {
    insertJob: `${baseUri}/v1/gwstdJob/insertMidJob`,
    getLastJob: `${baseUri}/v1/gwstdJob/getLastJobInfo`
  }
}

/**
 * Erp数据对接接口
 * @type {{domesticSalesDetails: {exportUrl: string, selectAllPaged: string}, bom: {bomList: string, bomExport: string}, materialsIE: {exportUrl: string, selectAllPaged: string}, extract: {imports: {extractConfirm: string, getEmsListNo: string, checkLinkedNoBySid: string, exportUrl: string, selectAllPaged: string}, exports: {extractConfirm: string, getEmsListNo: string, checkLinkedNoBySid: string, exportUrl: string, selectAllPaged: string}}, shippingDetails: {selectAllPaged: string, exportUrl: string}, shippingDeta: {selectAllPaged: string, exportUrl: string, exportPrdSuppUrl: string, exportMaterialUrl: string, exportProductUrl: string}, shippingErpDecIList: {selectAllPaged: string, exportUrl: string}, billOfLadingHeaderE: {extract: string, selectAllPaged: string, exportUrl: string}, productIE: {exportUrl: string, selectAllPaged: string}, billOfLading: {imports: {exportUrl: string, selectAllPaged: string}, exports: {exportUrl: string, selectAllPaged: string}}, billOfLadingHeaderI: {selectAllPaged: string, exportUrl: string}, inventory: {material: {search: string, export: string}, inprocess: {search: string, export: string}, semifinished: {search: string, export: string}, finished: {search: string, export: string}}}}
 */
export const sapErp = {
  // 出货明细
  shippingDetails: {
    selectAllPaged: `${baseUri}/v1/erp/dece/detail/list`,
    exportUrl: `${baseUri}/v1/erp/dece/detail/export`
  },
  // 数据提取
  extract: {
    // 进口
    imports: {
      exportUrl: `${baseUri}/v1/erp/`,
      getEmsListNo: `${baseUri}/v1/decErpIListN/getLinkedNo`,
      selectAllPaged: `${baseUri}/v1/decErpIListN/listDecI`,
      extractConfirm: `${baseUri}/v1/decErpIListN/checkLinkedNo`,
      checkLinkedNoBySid: `${baseUri}/v1/decErpIListN/checkLinkedNoBySid`
    },
    // 出口
    exports: {
      exportUrl: `${baseUri}/v1/erp/`,
      getEmsListNo: `${baseUri}/v1/decErpEListN/getLinkedNo`,
      selectAllPaged: `${baseUri}/v1/decErpEListN/listDecE`,
      extractConfirm: `${baseUri}/v1/decErpEListN/checkLinkedNo`,
      checkLinkedNoBySid: `${baseUri}/v1/decErpEListN/checkLinkedNoBySid`
    }
  },
  // ERP接口数据物料
  shippingDeta: {
    selectAllPaged: `${baseUri}/v1/erp/listMat`,//ERP物料数据
    exportUrl: `${baseUri}/v1/erp/exportMat`,
    exportMaterialUrl: `${baseUri}/v1/matreialErp/export`,
    exportProductUrl: `${baseUri}/v1/productList/export`,
    exportPrdSuppUrl: `${baseUri}/v1/erp/supplier/export`
  },
  // 进货明细
  shippingErpDecIList: {
    selectAllPaged: `${baseUri}/v1/erp/deci/detail/list`,
    exportUrl: `${baseUri}/v1/erp/deci/detail/export`,
    insert: `${baseUri}/v1/erp/deci/detail`,
    update: `${baseUri}/v1/erp/deci/detail`,
    delete: `${baseUri}/v1/erp/deci/detail`,
    entrust: `${baseUri}/v1/erp/deci/detail/entrust`,
    split: `${baseUri}/v1/erp/deci/detail/split`,
    checkGen:`${baseUri}/v1/erp/deci/detail/checkGen`,
    genBatch :`${baseUri}/v1/erp/deci/detail/genBatch`,
    checkBatch :`${baseUri}/v1/erp/deci/detail/checkBatch`,
    back :`${baseUri}/v1/erp/deci/detail/back`,
    downloadFile :`${baseUri}/v1/erp/deci/detail/downloadFile`,
    down :`${baseUri}/v1/erp/deci/detail/down`,
  },
  batch:{
    head:{
      entrust: `${baseUri}/v1/decIBatchHead/entrust`,
      sendForwardOrSupplier: `${baseUri}/v1/decIBatchHead/sendForwardOrSupplier`,
      checkConfirm: `${baseUri}/v1/cost/headi/checkConfirm`,
      rest: `${baseUri}/v1/decIBatchHead`,
      list: `${baseUri}/v1/decIBatchHead/list`,
      genDec: `${baseUri}/v1/decIBatchHead/genDec`,
      genFreeApply: `${baseUri}/v1/decIBatchHead/genFreeApply`,
      checkGen: `${baseUri}/v1/decIBatchHead/checkGen`,
      submitGw: `${baseUri}/v1/decIBatchHead/submitGw`,
      listByUserToken: `${baseUri}/v1/decIBatchHead/listByUserToken`,
      checkGwReturn: `${baseUri}/v1/decIBatchHead/checkGwReturn`,
      gwReturn: `${baseUri}/v1/decIBatchHead/gwReturn`,
      getInfoByBatchNo: `${baseUri}/v1/decIBatchHead/getInfoByBatchNo`,
      dataBackFilling: `${baseUri}/v1/decIBatchHead/dataBackFilling`
    },
    list:{
      rest: `${baseUri}/v1/decIBatchList`,
      list: `${baseUri}/v1/decIBatchList/list`,
      export: `${baseUri}/v1/decIBatchList/export`,
    }
  },
  billOfLadingHeaderE: {
    selectAllPaged: `${baseUri}/v1/erp/dece/head/list`,
    exportUrl: `${baseUri}/v1/erp/dece/head/export`,
    extract: `${baseUri}/v1/decErpEHeadN/getExtract`
  },
  //进口表头
  billOfLadingHeaderI: {
    selectAllPaged: `${baseUri}/v1/erp/deci/head/list`,
    exportUrl: `${baseUri}/v1/erp/deci/head/export`
  },
  //bom信息
  bom: {
    bomList: `${baseUri}/v1/erp/listBom`,
    bomExport: `${baseUri}/v1/erp/exportBom`
  },
  // 原始BOM(得意)
  originalBom: {
    cleaning: `${baseUri}/v1/erp/cleaning`,               // 清洗
    bomList: `${baseUri}/v1/erp/listOriginalBom`,
    bomExport: `${baseUri}/v1/erp/exportOriginalBom`
  },
  // 标准BOM
  standardBom: {
    bomSend: `${baseUri}/v1/erpOriginalBomClean/bomSend`, // 发送
    bomList: `${baseUri}/v1/erpOriginalBomClean/list`,
    bomExport: `${baseUri}/v1/erpOriginalBomClean/export`,
    bomSendCheck: `${baseUri}/v1/erpOriginalBomClean/bomSendCheck`
  },
  //库存
  inventory: {
    material: {
      search: `${baseUri}/v1/erp/stock/listImgStock`,
      export: `${baseUri}/v1/erp/stock/exportImgStock`
    },
    finished: {
      search: `${baseUri}/v1/erp/stock/listExgStock`,
      export: `${baseUri}/v1/erp/stock/exportExgStock`
    },
    semifinished: {
      search: `${baseUri}/v1/erp/stock/listSemiExgStock`,
      export: `${baseUri}/v1/erp/stock/exportSemiExgStock`
    },
    inprocess: {
      search: `${baseUri}/v1/erp/stock/listWipStock`,
      export: `${baseUri}/v1/erp/stock/exportWipStock`
    }
  },
  // 料件出入库
  materialsIE: {
    deleteUrl: `${baseUri}/v1/erp/deleteImg`,
    exportUrl: `${baseUri}/v1/erp/exportImg`,
    selectAllPaged: `${baseUri}/v1/erp/listImg`
  },
  // 成品出入库
  productIE: {
    deleteUrl: `${baseUri}/v1/erp/deleteExg`,
    exportUrl: `${baseUri}/v1/erp/exportExg`,
    selectAllPaged: `${baseUri}/v1/erp/listExg`
  },
  // 提单整单
  billOfLading: {
    imports: {
      exportUrl: `${baseUri}/v1/erp/deci/export`,
      selectAllPaged: `${baseUri}/v1/erp/deci/list`
    },
    exports: {
      exportUrl: `${baseUri}/v1/erp/dece/export`,
      selectAllPaged: `${baseUri}/v1/erp/dece/list`
    }
  },
  // 内销明细
  domesticSalesDetails: {
    exportUrl: `${baseUri}/v1/erp/exportMrpList`,
    selectAllPaged: `${baseUri}/v1/erp/listMrp`
  },
  /**
   * 计划
   */
  plan: {
    /**
     * 出口
     */
    exports: {
      /**
       * 表头
       */
      head: {
        selectAllPaged: `${baseUri}/v1/erp/eplan/head/list`
      },
      /**
       * 表体
       */
      body: {
        selectAllPaged: `${baseUri}/v1/erp/eplan/detail/list`
      },
      /**
       * 整单
       */
      headBody: {
        selectAllPaged: `${baseUri}/v1/erp/eplan/list`
      }
    }
  }
}

export const deep = {
  totalContent: `${baseUri}/v1/sjgInCheckList/getSum`,
  totalExtractet: `${baseUri}/v1/matreialErp/getListTotalForSjg`,
  confirmCheckNo: `${baseUri}/v1/sjgInCheckList/getCountByCheckNo`,
  //深加工出口
  deepImpRecord: {
    head: {
      getAllNo: `${baseUri}/v1/sjgImgErpDetailIn/total`,
      getAllData: `${baseUri}/v1/matreialErp/total`,
      add: { intoInfo: `${baseUri}/v1/recordRelation/add` },//备案关系维护表头 新增
      addOut: { intoOutInfo: `${baseUri}/v1/sjgRecordRelationOut/add` },//深加工转出备案关系维护表头 新增
      getNbbh: { getNbbh: `${baseUri}/v1/sjgImgErpDetailIn/getZrnbbh` },//获取转入内部编号下拉数据源
      getOutNbbh: { getOutNbbh: `${baseUri}/v1/sjgExgErpDetailOut/getZrnbbh` },//深加工转出确认数据的时候获取转出内部编号下拉数据源
      confirmSomeBodyList: { confirmSome: `${baseUri}/v1/sjgImgErpDetailIn/confirmSome` },//深加工确认勾选
      confirmOutSomeBodyList: { outConfirmSome: `${baseUri}/v1/sjgExgErpDetailOut/confirmSome` },//深加转出工确认勾选
      confirmAllBodyList: { confirmAll: `${baseUri}/v1/sjgImgErpDetailIn/confirmAll` },//深加工确认全部
      confirmOutAllBodyList: { outConfirmAll: `${baseUri}/v1/sjgExgErpDetailOut/confirmAll` },//深加转出工确认全部
      updateOutHeadRelation: `${baseUri}/v1/sjgRecordRelationOut/`,//深加工转出转入料件管理表头数据修改
      updateHeadRelation: `${baseUri}/v1/recordRelation/`,//深加工转入料件管理表头数据修改
      selOne: { oneInfo: `${baseUri}/v1/recordRelation/selByszch` },//根据手账册号查询是否存在
      selOneList: { oneInfoList: `${baseUri}/v1/recordRelationList/selByszch` },//s深加工转入根据手账册号查询是否存在
      selOutOne: { oneOutInfo: `${baseUri}/v1/sjgRecordRelationOut/selByszch` },//深加表体工转出根据手账册号查询是否存在
      selHeadOutOne: { oneOutInfo: `${baseUri}/v1/sjgRecordRelationOut/selHeadByszch` },//深加表头工转出根据手账册号查询是否存在
      selOutOneList: { oneOutInfoList: `${baseUri}/v1/recordRelationList/selByszch` },//根据手账册号查询是否存在
      updateBodyList: `${baseUri}/v1/recordRelationList`,//更新表体数据接口地址
      updateOutBodyList: `${baseUri}/v1/sjgRecordRelationListOut`,//深加工转出更新表体数据接口地址
      getBodyList: { selectDelAllPaged: `${baseUri}/v1/recordRelationList/list` },//深加工关系维护表体数据查询
      getOuBodyList: { selectouDelAllPaged: `${baseUri}/v1/sjgRecordRelationListOut/list` },//深加工转出关系维护表体数据查询
      getOutBodyList: { selectOutDelAllPaged: `${baseUri}/v1/sjgRecordRelationListOut/list` },//深加工转出关系维护表体数据查询
      intoBodyList: `${baseUri}/v1/recordRelationList/add`,//插入表体数据库中接口地址
      intoOutBodyList: `${baseUri}/v1/sjgRecordRelationListOut/add`,//插入表体数据库中接口地址
      deleteList: `${baseUri}/v1/recordRelationList/del`,//深加工关系维护表体数据删除
      deleteOutList: `${baseUri}/v1/sjgRecordRelationListOut/del`,//深加工关转出系维护表体数据删除
      exportBodyList: `${baseUri}/v1/recordRelationList/export`,//深加工关系维护表体数据导出
      exportOutBodyList: `${baseUri}/v1/sjgRecordRelationListOut/export`,//深加工转出关系维护表体数据导出


      //深加工转出转出数据提取
      getOutDetailList: { selectOutist: `${baseUri}/v1/sjgExgErpDetailOut/list` },//深加转出转出数据提取数据查询

      //以下为表头数据加载
      getHeadList: { selectHeadList: `${baseUri}/v1/recordRelation/list` },//深加转入工关系维护表头数据查询
      getOutHeadList: { selectOutHeadList: `${baseUri}/v1/sjgRecordRelationOut/list` },//深加工转出关系维护表头数据查询

      getPickUpList: { selectPickUpList: `${baseUri}/v1/sjgImgErpDetailIn/list` },//深加工转入信息提取模块查询接口地址
      delPickUpList: { delPickUpList: `${baseUri}/v1/sjgImgErpDetailIn/del` },//深加工转入信息提取模块删除接口地址
      delOutPickUpList: { delOutPickUpList: `${baseUri}/v1/sjgExgErpDetailOut/del` },//深加工转出信息提取模块删除接口地址

      material: { selectList: `${baseUri}/v1/matreialErp/list` },//深加工转入erp数据查询
      pick: { pickUpList: `${baseUri}/v1/matreialErp/pickUp` },//深加工转入erp数据查询提取接口
      outPick: { outPickUpList: `${baseUri}/v1/productList/pickUp` },//深加工转出erp数据查询提取接口
      prdSuppPick: {
        outPickUpList: `${baseUri}/v1/biClientInformation/pickUp`,
        extractAll: `${baseUri}/v1/biClientInformation/extractAll`
      },//基础资料信息erp提取接口
      product: { selectProdList: `${baseUri}/v1/productList/list` },//深加工转出erp数据查询

      getPrdSupp: { getPrdSupp: `${baseUri}/v1/erp/supplier/list` },//客户供应商信息


      deleteHead: `${baseUri}/v1/recordRelation/del`,//深加工关系维护表头数据删除
      exporheaList: `${baseUri}/v1/recordRelation/export`,//深加工关系维护表头数据导出
      deleteOutHead: `${baseUri}/v1/sjgRecordRelationOut/del`,//深加工关系维护表头数据删除
      exporOutheaList: `${baseUri}/v1/sjgRecordRelationOut/export`,//深加工转出关系维护表头数据导出


      //一下为深加工料件转入接口地址
      getFromToHeadList: { selectHeadList: `${baseUri}/v1/recordHeadFromTo/list` },//深加工料件转入表头列表加载接口地址
      exportFromToUrl: `${baseUri}/v1/recordHeadFromTo/export`,//深加工料件转入表头 数据导出
      deleteFromToHead: `${baseUri}/v1/recordHeadFromTo/del`,//深加工关系维护表头数据删除
      fromToAdd: { intoInfo: `${baseUri}/v1/recordHeadFromTo/add` },//备案关系维护表头 新增
      getReByNblj: { oneInfo: `${baseUri}/v1/recordHeadFromTo/getReByparmFromTo` },//深加工转入料件管理中的表头保存数据校验
      updateHead: `${baseUri}/v1/recordHeadFromTo/`,//深加工转入料件管理表头数据修改
      getZcfhgswm: { getzchg: `${baseUri}/v1/recordHeadFromTo/getZcfhgswm` },//深加工转入料件管理中的表头保存数据校验
      getFromToBodyList: { selectDelAllPaged: `${baseUri}/v1/recordListFromTo/list` },//深加工转入料件管理表体数据查询
      getFromToBillList: { selectBillDelAllPaged: `${baseUri}/v1/sjgImgListInErp/list` },//深加工转入料件管理清单数据查询
      geteXgFromToBillList: { selectExpBillDelAllPaged: `${baseUri}/v1/sjgExgListOutErp/list` },//深加工转出料件管理清单数据查询


      deleteFromToList: `${baseUri}/v1/recordListFromTo/del`,//深加工转入料件管理表体数据删除
      exportFromToBodyList: `${baseUri}/v1/recordListFromTo/export`,//深加工转入料件管理表体数据导出

      exportFromToBillList: `${baseUri}/v1/sjgImgListInErp/export`,//深加工转入料件管理表体数据导出
      exportExgFromToBillList: `${baseUri}/v1/sjgExgListOutErp/export`,//深加工转出料件管理表体数据导出

      selzr: { getZrqyl: `${baseUri}/v1/recordListFromTo/getZrqylh` },//深加工转入料件管理中的表体转入企业料号下拉数据获取
      getOtherFromTo: { getRe: `${baseUri}/v1/recordListFromTo/getOtherFromTo` },//深加工转入料件管理中的表体转入企业料号下拉数据获取
      getGNoOut: { selectGNoOut: `${baseUri}/v1/recordListFromTo/selectGNoOut` },//深加工转入料件管理中的表体获取转出方备案料号

      //先验证的接口
      yanzFromToList: { yanz: `${baseUri}/v1/recordListFromTo/pandan` },//深加工转入料件管理表体数据新增之前的验证

      intoFromToBodyList: `${baseUri}/v1/recordListFromTo/add`,//深加工转入料件管理表体数据新增
      getFromTBodyList: { selectFromToBodyList: `${baseUri}/v1/recordListFromTo/list` },//深加工转入料件管理表体数据查询
      intoFromBillList: { oneInfoList: `${baseUri}/v1/recordBillFromTo/add` },//深加工转入料件管理表体数据新增
      selMaxFromTo: `${baseUri}/v1/recordListFromTo/sel`,//深加工转入料件管理表体数据新增
      updateList: `${baseUri}/v1/recordListFromTo/`,//深加工转入料件管理表体数据修改
      inToBill: { Bill: `${baseUri}/v1/recordBillFromTo/add` },//插入收货单号表
      delBill: { delBi: `${baseUri}/v1/recordBillFromTo/del` },//删除收货单号表
      updBill: { updBi: `${baseUri}/v1/recordBillFromTo/update` },//修改收货单号表
      getBillList: { getBill: `${baseUri}/v1/recordBillFromTo/getBillList` },//加载收货单号所有的数据
      pipeiBaxh: { pi: `${baseUri}/v1/recordHeadFromTo/matchBa` },//匹配备案序号
      getBgxuh: { bg: `${baseUri}/v1/recordHeadFromTo/shengcBgxuh` },//生成报关序号
      exportReceipt: { noReceipt: `${baseUri}/v1/recordHeadFromTo/printBill` },//导出发票清单
      birthBaoGuan: { birthBG: `${baseUri}/v1/recordHeadFromTo/generateDecErpI` },//生成报关信息


      // select:{selectDelAllPaged:`http://localhost:9999/gwstd/api/v1/decEntryDelMod/list`},//报关查询中的删改单记录
      exportsgUrl: `${baseUri}/v1/decEntryDelMod/export`,//报关查询中的删改单记录 数据导出
      updateIn: { updateInfo: `${baseUri}/v1/decEntryDelMod` },//报关查询中的删改单记录 编辑
      templatePus: { pushUpload: `${baseUri}/v1/decEntryDelModFile/uploader` },//报关查询中的删改单记录 附件上传
      getFujian: `${baseUri}/v1/decEntryDelModFile/downloadFile`,//报关查询中的删改单记录 附件下载
      getFileInfo: `${baseUri}/v1/decEntryDelModFile/getListPagede`,//报关查询中的删改单记录 获取附件详情展示
      getOthersInfo: `${baseUri}/v1/entryDetail/selByEntryNo`,//报关查询中的删改单记录 敲击回车删改单号带出其他数据

      finalize: `${baseUri}/v1/decEntryDelModMistake/list`,//报关查询中的差错统计分析 查询汇总表数据的接口地址
      exportFin: `${baseUri}/v1/decEntryDelModMistake/export`,//报关查询中的差错统计分析 数据导出
      intoJobByMdify: `${baseUri}/v1/gwstdJob/addByModify`,//报关查询中的差错统计分析 往任务表中插入数据 01删改单记录查询

      selectAllPaged: `${baseUri}/v1/entryRecord/list`,
      printUrl: `${baseUri}/v1/entryRecord/print`
    }
  },
  deepExgRecord: {
    head: {
      getAllNo: `${baseUri}/v1/sjgExgErpDetailOut/total`,
      getAllData: `${baseUri}/v1/productList/total`,
//一下为深加工料件转出接口地址
      getFromToHeadList: { selectHeadList: `${baseUri}/v1/sjgExgHeadOut/list` },//深加工料件转入表头列表加载接口地址
      exportFromToUrl: `${baseUri}/v1/sjgExgHeadOut/export`,//深加工料件转入表头 数据导出
      deleteFromToHead: `${baseUri}/v1/sjgExgHeadOut/del`,//深加工关系维护表头数据删除
      fromToAdd: { intoInfo: `${baseUri}/v1/sjgExgHeadOut/add` },//备案关系维护表头 新增
      getReByNblj: { oneInfo: `${baseUri}/v1/sjgExgHeadOut/getReByparmFromTo` },//深加工转入料件管理中的表头保存数据校验
      updateHead: `${baseUri}/v1/sjgExgHeadOut/`,//深加工转入料件管理表头数据修改
      getZcfhgswm: { getzchg: `${baseUri}/v1/sjgExgHeadOut/getZcfhgswm` },//深加工转入料件管理中的表头保存数据校验
      pipeiBaxh: { pi: `${baseUri}/v1/sjgExgHeadOut/matchBa` },//匹配备案序号
      getBgxuh: { bg: `${baseUri}/v1/sjgExgHeadOut/shengcBgxuh` },//生成报关序号
      exportReceipt: { noReceipt: `${baseUri}/v1/sjgExgHeadOut/printBill` },//深加工转出导出发票清单
      birthBaoGuan: { birthBG: `${baseUri}/v1/sjgExgHeadOut/generateDecErpE` },//生成报关信息

      getBillList: { getBill: `${baseUri}/v1/sjgExgBillOut/getBillList` },//加载收货单号所有的数据
      inToBill: { Bill: `${baseUri}/v1/sjgExgBillOut/add` },//插入收货单号表
      delBill: { delBi: `${baseUri}/v1/sjgExgBillOut/del` },//删除收货单号表
      updBill: { updBi: `${baseUri}/v1/sjgExgBillOut/update` },//修改收货单号表

      exportFromToBodyList: `${baseUri}/v1/sjgExgListOut/export`,//深加工转入料件管理表体数据导出
      selzr: { getZrqyl: `${baseUri}/v1/sjgExgListOut/getZrqylh` },//深加工转入料件管理中的表体转入企业料号下拉数据获取
      getOtherFromTo: { getRe: `${baseUri}/v1/sjgExgListOut/getOtherFromTo` },//深加工转入料件管理中的表体转入企业料号下拉数据获取
      //先验证的接口
      yanzFromToList: { yanz: `${baseUri}/v1/sjgExgListOut/pandan` },//深加工转入料件管理表体数据新增之前的验证
      intoFromToBodyList: `${baseUri}/v1/sjgExgListOut/add`,//深加工转入料件管理表体数据新增
      getFromTBodyList: { selectFromToBodyList: `${baseUri}/v1/sjgExgListOut/list` },//深加工转入料件管理表体数据查询
      getFromToBodyList: { selectDelAllPaged: `${baseUri}/v1/sjgExgListOut/list` },//深加工转入料件管理表体数据查询
      deleteFromToList: `${baseUri}/v1/sjgExgListOut/del`,//深加工转入料件管理表体数据删除
      selMaxFromTo: `${baseUri}/v1/sjgExgListOut/sel`,//深加工转入料件管理表体数据新增
      updateList: `${baseUri}/v1/sjgExgListOut/`,//深加工转入料件管理表体数据修改

      getFromToBillList: { selectBillDelAllPaged: `${baseUri}/v1/sjgExgListOutErp/list` },//深加工转入料件管理清单数据查询
      exportFromToBillList: `${baseUri}/v1/sjgExgListOutErp/export`,//深加工转入料件管理表体数据导出

      GETZRC: `${baseUri}/v1/biClientInformation/getZtythCopEmsNoZc`,   // 生加工，获取转出方转入方手账册号
      GETMD: `${baseUri}/v1/sjgExgHeadOut/getZcHFromTo`   // 生加工，获取转出方转入方手账册号
    }
  },
  //深加工进口
  deepExportRecord: {
    head: {
      selectAllPaged: `${baseUri}/v1/entryDetail/list`,
      exportUrl: `${baseUri}/v1/entryDetail/export`
    }
  }
}

/**
 * 外发管理
 * @type {{wfapply: {wfapplyexport: string, wfapplysearch: string, wfapplydeletetest: string, wfapplyaddtest: string, wfapplydelete: string, wfapplyput: string, wfapplyadd: string}, getSerialNoList: string, addentrustEmsNo: string, getDataByCode: string, emsCopNoList: string, getcomcode: string, getDataBySerialNo: string, wfsend: {wfsendadd: string, wfsendexport: string, wfsendput: string, wfsendsearch: string, wfsenddelete: string, body: {bodysearch: string, bodyadd: string, bodydelete: string, bodyput: string, bodyexport: string}, wfsendcheck: string}, busParter: string, getDataByEmsCopNo: string, getApplyCompany: string, wfreceive: {wfreceiveadd: string, wfreceivedelete: string, wfreceivecheck: string, wfreceiveput: string, wfreceiveexport: string, body: {bodysearch: string, bodyadd: string, bodydelete: string, bodyput: string, bodyexport: string}, wfreceivesearch: string}, entrustEmsNo: string}}
 */
export const wfManage = {
  //申报单位信用代码
  getcomcode: `${baseUri}/v1/biClientInformation/getCreditCode`,
  //商业伙伴
  busParter: `${baseUri}/v1/biOutEnterprise/getComboxByCode`,
  //查询委托手账册
  entrustEmsNo: `${baseUri}/v1/wfDeclareForm/getEmsNoList`,
  //新增委托手账册
  addentrustEmsNo: `${baseUri}/v1/matImgexg/selectEmsNo`,   // matImgexgOrg/getEmsNoList`,
  // 企业内部编号
  emsCopNoList: `${baseUri}/v1/wfDeclareForm/getEmsCopNoList`,
  //根据企业内部编号获取相关信息
  getDataByEmsCopNo: `${baseUri}/v1/wfDeclareForm/getDataByEmsCopNo`,
  // 根据企业代码自动带出商业伙伴信息
  getDataByCode: `${baseUri}/v1/biOutEnterprise/getDataByCode`,
  // 申报单位信息
  getApplyCompany: `${baseUri}/v1/biClientInformation/selectAllCombox`,
  //获取备案号
  getSerialNoList: `${baseUri}/v1/matImgexgOrg/getSerialNoList`,
  //备案号相关信息
  getDataBySerialNo: `${baseUri}/v1/matImgexgOrg/getDataBySerialNo`,
  //外发申报单
  wfapply: {
    wfapplysearch: `${baseUri}/v1/wfDeclareForm/list`,
    wfapplyaddtest: `${baseUri}/v1/wfDeclareForm/checkEmsCopNo`,
    wfapplyadd: `${baseUri}/v1/wfDeclareForm`,
    wfapplydeletetest: `${baseUri}/v1/wfDeclareForm/checkRecords`,
    wfapplydelete: `${baseUri}/v1/wfDeclareForm`,
    wfapplyput: `${baseUri}/v1/wfDeclareForm`,
    wfapplyexport: `${baseUri}/v1/wfDeclareForm/export`
  },
  //发货单
  wfsend: {
    wfsendadd: `${baseUri}/v1/wfSendHead`,
    wfsendput: `${baseUri}/v1/wfSendHead`,
    wfsenddelete: `${baseUri}/v1/wfSendHead`,
    wfsendsearch: `${baseUri}/v1/wfSendHead/list`,
    wfsendexport: `${baseUri}/v1/wfSendHead/export`,
    wfsendcheck: `${baseUri}/v1/wfSendHead/checkBillNo`, //检查当前企业的发货单编号
    body: {
      bodyadd: `${baseUri}/v1/wfSendList`,
      bodyput: `${baseUri}/v1/wfSendList`,
      bodydelete: `${baseUri}/v1/wfSendList`,
      bodysearch: `${baseUri}/v1/wfSendList/list`,
      bodyexport: `${baseUri}/v1/wfSendList/export`
    }
  },
  //收货单
  wfreceive: {
    wfreceiveadd: `${baseUri}/v1/wfReceiveHead`,
    wfreceiveput: `${baseUri}/v1/wfReceiveHead`,
    wfreceivedelete: `${baseUri}/v1/wfReceiveHead`,
    wfreceivesearch: `${baseUri}/v1/wfReceiveHead/list`,
    wfreceiveexport: `${baseUri}/v1/wfReceiveHead/export`,
    wfreceivecheck: `${baseUri}/v1/wfReceiveHead/checkBillNo`, //检查当前企业的发货单编号
    body: {
      bodyadd: `${baseUri}/v1/wfReceiveList`,
      bodyput: `${baseUri}/v1/wfReceiveList`,
      bodydelete: `${baseUri}/v1/wfReceiveList`,
      bodysearch: `${baseUri}/v1/wfReceiveList/list`,
      bodyexport: `${baseUri}/v1/wfReceiveList/export`
    }
  },
  //发货单报表
  wfsendReport: {
    vWfSendAllList: `${baseUri}/v1/vWfSendHead/headList`,
    vWfSendExport: `${baseUri}/v1/vWfSendHead/headExport`
  },
  //收货单报表
  takedeReport: {
    vWfReceiveAllList: `${baseUri}/v1/vWfReceiveHeadList/headList`,
    vWfReceiveExport: `${baseUri}/v1/vWfReceiveHeadList/headExport`
  }
}

/**
 * 数据分析
 * @type {{priceanalysis: {getComputed: string, getInfo: string, getemsNo: string, getTime: string, getResult: string, getExport: string, getEdit: string}}}
 */
export const dataanalysis = {
  priceanalysis: {
    getEdit: `${baseUri}/v1/analysisPriceConfig`,
    getComputed: `${baseUri}/v1/gwstdJob/addPrice`,
    getemsNo: `${baseUri}/v1/matImgexg/selectEmsNo`,  // matImgexgOrg/getEmsNoListAndNoBond`,
    getInfo: `${baseUri}/v1/analysisPriceResult/list`,
    getExport: `${baseUri}/v1/analysisPriceResult/export`,
    getResult: `${baseUri}/v1/gwstdJob/getPirceLastEntity`,
    getTime: `${baseUri}/v1/analysisPriceConfig/selectByTradeAndEms`
  }
}

/**
 * 企业参数库
 * @type {{}}
 */
export const enterpriseParamsLib = {
  comm: {
    insert: `${baseUri}/v1/biCustomerParams`,
    update: `${baseUri}/v1/biCustomerParams`,
    delete: `${baseUri}/v1/biCustomerParams`,
    exportUrl: `${baseUri}/v1/biCustomerParams/export`,
    selectAllPaged: `${baseUri}/v1/biCustomerParams/list`,
    checkParam: `${baseUri}/v1/biCustomerParams/checkParam`,
    getByParamType: `${baseUri}/v1/biCustomerParams/getParamValues`
  },
  vehicleUsage: {//车辆类型
    exportUrl: `${baseUri}/v1/biCustomerParams/vehicle/export`,
    selectAllPaged: `${baseUri}/v1/biCustomerParams/vehicle/list`
  },
  // 车柜类型
  carCabinetType: {
    insert: `${baseUri}/v1/biCabinetType`,
    update: `${baseUri}/v1/biCabinetType`,
    delete: `${baseUri}/v1/biCabinetType`,
    exportUrl: `${baseUri}/v1/biCabinetType/export`,
    selectAllPaged: `${baseUri}/v1/biCabinetType/list`
  },
  // 车柜信息
  carCabinetInfo: {
    insert: `${baseUri}/v1/decLogisticsCabinet`,
    update: `${baseUri}/v1/decLogisticsCabinet`,
    delete: `${baseUri}/v1/decLogisticsCabinet`,
    selectAllPaged: `${baseUri}/v1/decLogisticsCabinet/list`
  },
  customDocNo: {
    insert: `${baseUri}/v1/biOrdernoGenRule`,
    update: `${baseUri}/v1/biOrdernoGenRule`,
    delete: `${baseUri}/v1/biOrdernoGenRule`,
    selectAllPaged: `${baseUri}/v1/biOrdernoGenRule/list`,
    judgeIsExistsRule: `${baseUri}/v1/biOrdernoGenRule/judgeIsExistsRule`
  },
  royalty: {
    insert: `${baseUri}/v1/gwstdRoyaltyFee`,
    update: `${baseUri}/v1/gwstdRoyaltyFee`,
    delete: `${baseUri}/v1/gwstdRoyaltyFee`,
    exportUrl: `${baseUri}/v1/gwstdRoyaltyFee/export`,
    selectAllPaged: `${baseUri}/v1/gwstdRoyaltyFee/list`,
    checkRoyalityNo: `${baseUri}/v1/gwstdRoyaltyFee/checkRoyalityNo`
  },
  // 企业参数库-物流属性接口
  logisticsAttributes: {
    insert: `${baseUri}/v1/biLogisticsAttribute`,
    update: `${baseUri}/v1/biLogisticsAttribute`,
    delete: `${baseUri}/v1/biLogisticsAttribute`,
    exportUrl: `${baseUri}/v1/biLogisticsAttribute/export`,
    selectAllPaged: `${baseUri}/v1/biLogisticsAttribute/list`
  },
  //物料群组接口
  material: {
    insert: `${baseUri}/v1/biMaterialGroup`,
    update: `${baseUri}/v1/biMaterialGroup`,
    delete: `${baseUri}/v1/biMaterialGroup`,
    selectAllPaged: `${baseUri}/v1/biMaterialGroup/list`
  },
  premiumRate:{
    insert: `${baseUri}/v1/biPremiumRate`,
    update: `${baseUri}/v1/biPremiumRate`,
    delete: `${baseUri}/v1/biPremiumRate`,
    selectAllPaged: `${baseUri}/v1/biPremiumRate/list`,
    changeStatus:`${baseUri}/v1/biPremiumRate/changeStatus`,
  }
}

/**
 * 发票参数库
 */
export const ionvoiceParamsLib = {
  comm: {
    insert: `${baseUri}/v1/biInvoiceParams`,
    update: `${baseUri}/v1/biInvoiceParams`,
    delete: `${baseUri}/v1/biInvoiceParams`,
    exportUrl: `${baseUri}/v1/biInvoiceParams/export`,
    selectAllPaged: `${baseUri}/v1/biInvoiceParams/list`
  }
}

/**
 * 文档中心
 * @type {{docE: {getBill: string, selectAllPaged: string, exportUrl: string}, getPdf: string, fileManage: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, docI: {getBill: string, selectAllPaged: string, exportUrl: string}}}
 */
export const documentCenter = {
  comm: {
    check: `${baseUri}/v1/docCenter/check`,
    getAcmpType: `${baseUri}/v1/docCenter/getAcmpTypeList`,
    // 校验是否可以同步报关单附件 /{ieMark}
    checkSyncEntry: `${baseUri}/v1/docCenter/checkSyncEntry`,
    // 同步报关单附件  /{ieMark}
    syncEntryAttached: `${baseUri}/v1/docCenter/syncEntryAttached`
  },
  docI: {
    exportUrl: `${baseUri}/v1/decErpIHeadN/exportDoc`,
    selectAllPaged: `${baseUri}/v1/decErpIHeadN/docList`,
    getBill: `${baseUri}/v1/decErpIHeadN/getDocAttechedList`
  },
  docE: {
    exportUrl: `${baseUri}/v1/decErpEHeadN/exportDoc`,
    selectAllPaged: `${baseUri}/v1/decErpEHeadN/docList`,
    getBill: `${baseUri}/v1/decErpEHeadN/getDocAttechedList`
  },
  getPdf: `${baseUri}/v1/decErpEHeadN/getAttachFile`,
  fileManage: {
    insert: `${baseUri}/v1/docCenter`,
    update: `${baseUri}/v1/docCenter`,
    delete: `${baseUri}/v1/docCenter`,
    exportUrl: `${baseUri}/v1/docCenter/export`,
    selectAllPaged: `${baseUri}/v1/docCenter/list`,
    lock: `${baseUri}/v1/docCenter/updateStatusList`
  },
  fileType: {
    insert: `${baseUri}/v1/gwstdDocType`,
    update: `${baseUri}/v1/gwstdDocType`,
    delete: `${baseUri}/v1/gwstdDocType`,
    exportUrl: `${baseUri}/v1/gwstdDocType/export`,
    selectAllPaged: `${baseUri}/v1/gwstdDocType/list`,
    selectListType: `${baseUri}/v1/gwstdDocType/selectListType`
  }
}

/**
 * 退换管理
 * @type {{exportInfo: string, getInforDetail: string, getInforList: string, exportInfoDetail: string}}
 */
export const replacementApi = {
  getInforList: `${baseUri}/v1/returnMgt/list`,
  getInforDetail: `${baseUri}/v1/returnMgt/returnDetailList`,
  exportInfo: `${baseUri}/v1/returnMgt/export`,
  exportInfoDetail: `${baseUri}/v1/returnMgt/exportDetail`
}

/**
 * 减免税设备
 * @type {{customsRecord: {saveRecordData: string, selectAllPaged: string, exportUrl: string, insert: string, update: string, delete: string, getEntryRecordData: string}, taxExemptionCertificate: {selectAllPaged: string, exportUrl: string, insert: string, update: string, delete: string}, projectConfirmation: {getItemNoList: string, selectAllPaged: string, exportUrl: string, insert: string, update: string, delete: string, getUsedAmount: string}, usageRecord: {split: string, selectAllPaged: string, exportUrl: string, insert: string, update: string, copy: string, delete: string}, customsAnnualReport: {selectAllPaged: string, exportUrl: string, insert: string, update: string, delete: string}}}
 */
export const taxExemptionEquipment = {
  // 报关记录
  customsRecord: {
    insert: `${baseUri}/v1/devEntryRecord`,
    update: `${baseUri}/v1/devEntryRecord`,
    delete: `${baseUri}/v1/devEntryRecord`,
    selectAllPaged: `${baseUri}/v1/devEntryRecord/list`,
    exportUrl: `${baseUri}/v1/devEntryRecord/export`,
    // 查询需要提取的数据
    getEntryRecordData: `${baseUri}/v1/devEntryRecord/getEntryRecordData`,
    // 提取报关记录数据
    saveRecordData: `${baseUri}/v1/devEntryRecord/saveRecordData`
  },
  // 使用记录
  usageRecord: {
    insert: `${baseUri}/v1/devUseRecord`,
    update: `${baseUri}/v1/devUseRecord`,
    delete: `${baseUri}/v1/devUseRecord`,
    selectAllPaged: `${baseUri}/v1/devUseRecord/list`,
    copy: `${baseUri}/v1/devUseRecord/copy`,
    exportUrl: `${baseUri}/v1/devUseRecord/export`,
    // 拆分报关使用记录
    split: `${baseUri}/v1/devUseRecord/split`
  },
  // 项目确认书
  projectConfirmation: {
    insert: `${baseUri}/v1/devItemConfirmation`,
    update: `${baseUri}/v1/devItemConfirmation`,
    delete: `${baseUri}/v1/devItemConfirmation`,
    selectAllPaged: `${baseUri}/v1/devItemConfirmation/list`,
    exportUrl: `${baseUri}/v1/devItemConfirmation/export`,
    // 获取报关已使用额度
    getUsedAmount: `${baseUri}/v1/devItemConfirmation/getUsedAmount`,
    // 获取项目书编号数据
    getItemNoList: `${baseUri}/v1/devItemConfirmation/getItemNoList`
  },
  // 征免税证明
  taxExemptionCertificate: {
    insert: `${baseUri}/v1/devTaxCertificate`,
    update: `${baseUri}/v1/devTaxCertificate`,
    delete: `${baseUri}/v1/devTaxCertificate`,
    selectAllPaged: `${baseUri}/v1/devTaxCertificate/list`,
    exportUrl: `${baseUri}/v1/devTaxCertificate/export`
  },
  // 海关年报
  customsAnnualReport: {
    insert: `${baseUri}/v1/devCustomsReport`,
    update: `${baseUri}/v1/devCustomsReport`,
    delete: `${baseUri}/v1/devCustomsReport`,
    selectAllPaged: `${baseUri}/v1/devCustomsReport/list`,
    exportUrl: `${baseUri}/v1/devCustomsReport/export`
  }
}

/**
 * 深加工转入/出【新版】 --- 进/出口结转
 * @type {{filingRelationshipDetails: {transferOut: {selectAllPaged: string, exportUrl: string}, transferIn: {selectAllPaged: string, exportUrl: string}}, filingRelationshipMaintenance: {transferOut: {head: {getRelationList: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, getSjgSupplier: string, delete: string}, body: {getListCount: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}, transferIn: {head: {getRelationList: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, getSjgSupplier: string, delete: string}, body: {getListCount: string, exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}}}, informationExtraction: {transferOut: {extract: {extractErpDataByQuery: string, exportUrl: string, selectAllPaged: string, totalContent: string, extractErpDataByChoose: string, totalExtractet: string}, main: {deleteUnComfirmed: string, split: string, exportUrl: string, selectAllPaged: string, getCheckNoList: string, confirmForQuery: string, confirmForChoose: string, deleteComfirmed: string, getMaxMinDate: string}}, transferIn: {extract: {extractErpDataByQuery: string, exportUrl: string, selectAllPaged: string, totalContent: string, extractErpDataByChoose: string, totalExtractet: string}, main: {deleteUnComfirmed: string, split: string, exportUrl: string, selectAllPaged: string, getCheckNoList: string, confirmForQuery: string, confirmForChoose: string, deleteComfirmed: string, getMaxMinDate: string, getFacGNoList: string}}}, dataManagement: {transferOut: {head: {exportUrl: string, selectAllPaged: string, extractDataByChoose: string, insert: string, update: string, delete: string, entryNoFillBackUrl: string, extractDataByQuery: string}, bill: {rowSplit: string, getTotalContent: string, exportUrl: string, selectAllPaged: string, printInvBox: string, update: string, dataClear: string, generateCustomsInfo: string, serialNoChange: string}, body: {matchGNo: string, getRelationByFacGNo: string, exportUrl: string, selectAllPaged: string, extractDataByChoose: string, insert: string, update: string, sum: string, delete: string, createBillGNo: string, extractDataByQuery: string}}, transferIn: {head: {exportUrl: string, selectAllPaged: string, extractDataByChoose: string, insert: string, update: string, delete: string, entryNoFillBackUrl: string, extractDataByQuery: string}, bill: {rowSplit: string, getTotalContent: string, exportUrl: string, selectAllPaged: string, printInvBox: string, update: string, dataClear: string, generateCustomsInfo: string, serialNoChange: string}, body: {matchGNo: string, getRelationByFacGNo: string, exportUrl: string, selectAllPaged: string, extractDataByChoose: string, insert: string, update: string, sum: string, delete: string, createBillGNo: string, extractDataByQuery: string}}}}}
 */
export const deepProcessing = {
  // 备案关系维护
  filingRelationshipMaintenance: {
    transferIn: {
      head: {
        insert: `${baseUri}/v1/sjgInRelation`,
        update: `${baseUri}/v1/sjgInRelation`,
        delete: `${baseUri}/v1/sjgInRelation`,
        exportUrl: `${baseUri}/v1/sjgInRelation/export`,
        selectAllPaged: `${baseUri}/v1/sjgInRelation/list`,
        // 获取供应商列表
        getSjgSupplier: `${baseUri}/v1/sjgInRelation/getSjgSupplier`,
        // 获取备案关系列表
        getRelationList: `${baseUri}/v1/sjgInRelation/getRelationList`
      },
      body: {
        insert: `${baseUri}/v1/sjgInRelationList`,
        update: `${baseUri}/v1/sjgInRelationList`,
        delete: `${baseUri}/v1/sjgInRelationList`,
        exportUrl: `${baseUri}/v1/sjgInRelationList/export`,
        selectAllPaged: `${baseUri}/v1/sjgInRelationList/list`,
        getListCount: `${baseUri}/v1/sjgInRelation/getSelectList`
      }
    },
    transferOut: {
      head: {
        insert: `${baseUri}/v1/sjgOutRelation`,
        update: `${baseUri}/v1/sjgOutRelation`,
        delete: `${baseUri}/v1/sjgOutRelation`,
        exportUrl: `${baseUri}/v1/sjgOutRelation/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutRelation/list`,
        // 获取供应商列表
        getSjgSupplier: `${baseUri}/v1/sjgOutRelation/getSjgSupplier`,
        // 获取备案关系列表
        getRelationList: `${baseUri}/v1/sjgOutRelation/getRelationList`
      },
      body: {
        insert: `${baseUri}/v1/sjgOutRelationList`,
        update: `${baseUri}/v1/sjgOutRelationList`,
        delete: `${baseUri}/v1/sjgOutRelationList`,
        exportUrl: `${baseUri}/v1/sjgOutRelationList/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutRelationList/list`,
        getListCount: `${baseUri}/v1/sjgOutRelation/getSelectList`
      }
    }
  },
  // 备案关系明细
  filingRelationshipDetails: {
    transferIn: {
      selectAllPaged: `${baseUri}/v1/sjgInRelation/headlist`,
      exportUrl: `${baseUri}/v1/sjgInRelation/exportHeadList`
    },
    transferOut: {
      selectAllPaged: `${baseUri}/v1/sjgOutRelation/headlist`,
      exportUrl: `${baseUri}/v1/sjgOutRelation/exportHeadList`
    }
  },
  // 信息提取
  informationExtraction: {
    transferIn: {
      main: {
        exportUrl: `${baseUri}/v1/sjgInCheckList/export`,
        selectAllPaged: `${baseUri}/v1/sjgInCheckList/list`,
        // 获取结转周期范围
        getMaxMinDate: `${baseUri}/v1/sjgInCheckList/getMaxMinDate`,
        // 获取对账单号列表
        getCheckNoList: `${baseUri}/v1/sjgInCheckList/getCheckNoList`,
        confirmForQuery: `${baseUri}/v1/sjgInCheckList/confirmForQuery`,
        confirmForChoose: `${baseUri}/v1/sjgInCheckList/confirmForChoose`,
        // 删除已确认数据
        deleteComfirmed: `${baseUri}/v1/sjgInCheckList/deleteComfirmed`,
        // 删除未确认数据
        deleteUnComfirmed: `${baseUri}/v1/sjgInCheckList/deleteUnComfirmed`,
        split: `${baseUri}/v1/sjgInCheckList/split`,
        getFacGNoList: `${baseUri}/v1/matImgexg/getFacGNoList`
      },
      extract: {
        exportUrl: `${baseUri}/v1/matreialErp/export`,
        selectAllPaged: `${baseUri}/v1/matreialErp/list`,
        // 获取总数量及总金额
        totalContent: `${baseUri}/v1/sjgInCheckList/getSum`,
        // Erp总数量总金额
        totalExtractet: `${baseUri}/v1/matreialErp/getListTotalForSjg`,
        // 提取数据-条件筛选
        extractErpDataByQuery: `${baseUri}/v1/sjgInCheckList/extractErpDataByQuery`,
        // 提取数据-勾选
        extractErpDataByChoose: `${baseUri}/v1/sjgInCheckList/extractErpDataByChoose`
      }
    },
    transferOut: {
      main: {
        exportUrl: `${baseUri}/v1/sjgOutCheckList/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutCheckList/list`,
        // 获取结转周期范围
        getMaxMinDate: `${baseUri}/v1/sjgOutCheckList/getMaxMinDate`,
        // 获取对账单号列表
        getCheckNoList: `${baseUri}/v1/sjgOutCheckList/getCheckNoList`,
        confirmForQuery: `${baseUri}/v1/sjgOutCheckList/confirmForQuery`,
        confirmForChoose: `${baseUri}/v1/sjgOutCheckList/confirmForChoose`,
        // 删除已确认数据
        deleteComfirmed: `${baseUri}/v1/sjgOutCheckList/deleteComfirmed`,
        // 删除未确认数据
        deleteUnComfirmed: `${baseUri}/v1/sjgOutCheckList/deleteUnComfirmed`,
        split: `${baseUri}/v1/sjgOutCheckList/split`
      },
      extract: {
        exportUrl: `${baseUri}/v1/productList/export`,
        selectAllPaged: `${baseUri}/v1/productList/list`,
        // 获取总数量及总金额
        totalContent: `${baseUri}/v1/sjgOutCheckList/getSum`,
        // Erp总数量总金额
        totalExtractet: `${baseUri}/v1/productList/getListTotalForSjg`,
        // 提取数据-条件筛选
        extractErpDataByQuery: `${baseUri}/v1/sjgOutCheckList/extractErpDataByQuery`,
        // 提取数据-勾选
        extractErpDataByChoose: `${baseUri}/v1/sjgOutCheckList/extractErpDataByChoose`
      }
    }
  },
  // 数据管理
  dataManagement: {
    transferIn: {
      head: {
        insert: `${baseUri}/v1/sjgInBillHead`,
        update: `${baseUri}/v1/sjgInBillHead`,
        delete: `${baseUri}/v1/sjgInBillHead`,
        exportUrl: `${baseUri}/v1/sjgInBillHead/export`,
        selectAllPaged: `${baseUri}/v1/sjgInBillHead/list`,
        // 提取数据-条件筛选
        extractDataByQuery: `${baseUri}/v1/sjgInBillList/extractDataByQuery`,
        // 提取数据-勾选
        extractDataByChoose: `${baseUri}/v1/sjgInBillList/extractDataByChoose`,
        // 报关单回填
        entryNoFillBackUrl: `${baseUri}/v1/sjgInBillHead/backFillEntryGNoOut`
      },
      body: {
        insert: `${baseUri}/v1/sjgInBillList`,
        update: `${baseUri}/v1/sjgInBillList`,
        delete: `${baseUri}/v1/sjgInBillList`,
        // 汇总数量，金额接口
        sum: `${baseUri}/v1/sjgInBillList/sum`,
        exportUrl: `${baseUri}/v1/sjgInBillList/export`,
        selectAllPaged: `${baseUri}/v1/sjgInBillList/list`,
        // 匹配备案序号
        matchGNo: `${baseUri}/v1/sjgInBillList/matchOrgSerialNo`,
        // 生成清单序号
        createBillGNo: `${baseUri}/v1/sjgInBillList/generateBillSerialNo`,
        // 提取数据-条件筛选
        extractDataByQuery: `${baseUri}/v1/sjgInBillList/extractDataByQuery`,
        // 提取数据-勾选
        extractDataByChoose: `${baseUri}/v1/sjgInBillList/extractDataByChoose`,
        // 获取指定企业料号的备案关系列表
        getRelationByFacGNo: `${baseUri}/v1/sjgInBillList/getRelationByFacGNo`
      },
      bill: {
        exportUrl: `${baseUri}/v1/sjgInBillTdList/export`,
        selectAllPaged: `${baseUri}/v1/sjgInBillTdList/list`,
        // 导出发票清单
        printInvBox: `${baseUri}/v1/sjgInBillList/printInvBox`,
        // 生成报关信息
        generateCustomsInfo: `${baseUri}/v1/sjgInBillList/generateErpDecIHeadN`,
        // 获取汇总数据
        getTotalContent: `${baseUri}/v1/sjgInBillTdList/sum`,
        // 更新
        update: `${baseUri}/v1/sjgInBillTdList`,
        // 行拆分
        rowSplit: `${baseUri}/v1/sjgInBillTdList/splitTdList`,
        // 序号变更
        serialNoChange: `${baseUri}/v1/sjgInBillTdList/move`,
        // 数据清空
        dataClear: `${baseUri}/v1/sjgInBillTdList/clearAll`
      }
    },
    transferOut: {
      head: {
        insert: `${baseUri}/v1/sjgOutBillHead`,
        update: `${baseUri}/v1/sjgOutBillHead`,
        delete: `${baseUri}/v1/sjgOutBillHead`,
        exportUrl: `${baseUri}/v1/sjgOutBillHead/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutBillHead/list`,
        // 提取数据-条件筛选
        extractDataByQuery: `${baseUri}/v1/sjgOutBillList/extractDataByQuery`,
        // 提取数据-勾选
        extractDataByChoose: `${baseUri}/v1/sjgOutBillList/extractDataByChoose`,
        // 报关单回填
        entryNoFillBackUrl: `${baseUri}/v1/sjgOutBillHead/backFillEntryGNoIn`
      },
      body: {
        insert: `${baseUri}/v1/sjgOutBillList`,
        update: `${baseUri}/v1/sjgOutBillList`,
        delete: `${baseUri}/v1/sjgOutBillList`,
        // 汇总数量，金额接口
        sum: `${baseUri}/v1/sjgOutBillList/sum`,
        exportUrl: `${baseUri}/v1/sjgOutBillList/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutBillList/list`,
        // 匹配备案序号
        matchGNo: `${baseUri}/v1/sjgOutBillList/matchOrgSerialNo`,
        // 生成清单序号
        createBillGNo: `${baseUri}/v1/sjgOutBillList/generateBillSerialNo`,
        // 提取数据-条件筛选
        extractDataByQuery: `${baseUri}/v1/sjgOutBillList/extractDataByQuery`,
        // 提取数据-勾选
        extractDataByChoose: `${baseUri}/v1/sjgOutBillList/extractDataByChoose`,
        // 获取指定企业料号的备案关系列表
        getRelationByFacGNo: `${baseUri}/v1/sjgOutBillList/getRelationByFacGNo`
      },
      bill: {
        exportUrl: `${baseUri}/v1/sjgOutBillTdList/export`,
        selectAllPaged: `${baseUri}/v1/sjgOutBillTdList/list`,
        // 导出发票清单
        printInvBox: `${baseUri}/v1/sjgOutBillList/printInvBox`,
        // 生成报关信息
        generateCustomsInfo: `${baseUri}/v1/sjgOutBillList/generateErpDecEHeadN`,
        // 获取汇总数据
        getTotalContent: `${baseUri}/v1/sjgOutBillTdList/sum`,
        // 更新
        update: `${baseUri}/v1/sjgOutBillTdList`,
        // 行拆分
        rowSplit: `${baseUri}/v1/sjgOutBillTdList/splitTdList`,
        // 序号变更
        serialNoChange: `${baseUri}/v1/sjgOutBillTdList/move`,
        // 数据清空
        dataClear: `${baseUri}/v1/sjgOutBillTdList/clearAll`
      }
    }
  }
}

/**
 * 税金管理
 * @type {{}}
 */
export const taxRateManage = {
  taxRateInquiry: {
    selectAllPaged: `${baseUri}/v1/gwstdTariffGeneral/list`
  },
  agreementTaxRateInquiry: {
    selectAllPaged: `${baseUri}/v1/gwstdTariffAccord/list`
  },
  importDutyRate: {
    selectAllPaged: `${baseUri}/v1/gwstdTariffAntidump/list`
  },
  TariffErvail: {
    selectAllPaged: `${baseUri}/v1/gwstdTariffErvail/list`
  },
  // 税金计算
  taxCalculation: {
    imports: {
      head: {
        update: `${baseUri}/v1/taxHeadI`,
        defendPayment: `${baseUri}/v1/taxHeadI/set-duty-type`,
        exportUrl: `${baseUri}/v1/taxHeadI/export`,
        taxEstimated: `${baseUri}/v1/taxHeadI/estimate-tax`,
        generateActualTaxes: `${baseUri}/v1/taxHeadI/calculate-tax`,
        clearActualTaxes: `${baseUri}/v1/taxHeadI/clear-tax`,
        selectAllPaged: `${baseUri}/v1/taxHeadI/list`
      },
      body: {
        exportUrl: `${baseUri}/v1/taxListI/export`,
        selectAllPaged: `${baseUri}/v1/taxListI/list`
      },
      bill: {
        exportUrl: `${baseUri}/v1/tax-dec-i-list/export`,
        selectAllPaged: `${baseUri}/v1/tax-dec-i-list/list`,
        update: `${baseUri}/v1/tax-dec-i-list`,
        total: `${baseUri}/v1/tax-dec-i-list/total`
      }
    },
    exports: {
      head: {
        update: `${baseUri}/v1/taxHeadE`,
        defendPayment: `${baseUri}/v1/taxHeadE/set-duty-type`,
        exportUrl: `${baseUri}/v1/`,
        taxEstimated: `${baseUri}/v1/`,
        generateActualTaxes: `${baseUri}/v1/`,
        clearActualTaxes: `${baseUri}/v1/taxHeadE/clear-tax`,
        selectAllPaged: `${baseUri}/v1/taxHeadE/list`
      },
      body: {
        exportUrl: `${baseUri}/v1/taxListE/export`,
        selectAllPaged: `${baseUri}/v1/taxListE/list`
      }
    }
  },
  // 实际税金
  actualTax: {
    imports: {
      /**
       * 预览附件 type 0:核对单, 1:缴款书
       * @param taxVouNo
       * @param type
       */
      preview: `${baseUri}/v1/tax/duty-form/preview`,
      getPreview: `${baseUri}/v1/tax/duty-form/getList`,
      dataScraping: `${baseUri}/v1/tax/duty-form/sync`,
      checklistPrintUrl: `${baseUri}/v1/tax/duty-form/`,
      selectAllBodyPaged: `${baseUri}/v1/tax/duty-form`,
      selectAllPaged: `${baseUri}/v1/tax/duty-form/list`,
      /**
       * 下载附件  type 0:核对单, 1:缴款书
       * @param taxVouNo
       * @param type
       */
      downloadAppendix: `${baseUri}/v1/tax/duty-form/downloadAppendix`,
      downloadDutyFormUrl: `${baseUri}/v1/tax/duty-form/downloadDutyForm`
    },
    exports: {
      dataScraping: `${baseUri}/v1/tax/duty-form/sync`,
      checklistPrintUrl: `${baseUri}/v1/tax/duty-form/`,
      selectAllBodyPaged: `${baseUri}/v1/tax/duty-form`,
      selectAllPaged: `${baseUri}/v1/tax/duty-form/list`
    }
  },
  // 实际税金明细
  actualTaxDetail: {
    exportUrl: `${baseUri}/v1/tax/export`,
    selectAllPaged: `${baseUri}/v1/tax/list`
  },
  // 税率排除目录
  taxRateExclusion: {
    insert: `${baseUri}/v1/taxRateExclusionList`,
    update: `${baseUri}/v1/taxRateExclusionList`,
    delete: `${baseUri}/v1/taxRateExclusionList`,
    exportUrl: `${baseUri}/v1/taxRateExclusionList/export`,
    selectAllPaged: `${baseUri}/v1/taxRateExclusionList/list`
  }
}

/**
 * 费用管理
 * @type {{}}
 */
export const expenseManage = {
  expenseAccountSetting: {
    insert: `${baseUri}/v1/costCourse`,
    update: `${baseUri}/v1/costCourse`,
    delete: `${baseUri}/v1/costCourse`,
    exportUrl: `${baseUri}/v1/costCourse/export`,
    selectAllPaged: `${baseUri}/v1/costCourse/list`,
    listJson: `${baseUri}/v1/costCourse/listJson`
  },
  costMaintenance: {
    in: {
      head: {
        insert: `${baseUri}/v1/costHeadI`,
        update: `${baseUri}/v1/costHeadI`,
        delete: `${baseUri}/v1/costHeadI`,
        copy: `${baseUri}/v1/costHeadI/copy`,
        exportUrl: `${baseUri}/v1/costHeadI/export`,
        selectAllPaged: `${baseUri}/v1/costHeadI/list`,
        formWork: `${baseUri}/v1/costHeadI/export-tpl`,
        importUrl: `${baseUri}/v1/costHeadI/import`
      },
      body: {
        insert: `${baseUri}/v1/costListI`,
        update: `${baseUri}/v1/costListI`,
        delete: `${baseUri}/v1/costListI`,
        exportUrl: `${baseUri}/v1/costListI/export`,
        selectAllPaged: `${baseUri}/v1/costListI/list`,
        // 出口费用表体查询所有费用科目
        getCostCourseCode: `${baseUri}/v1/costListI/getCostCourseCode`
      }
    },
    out: {
      head: {
        insert: `${baseUri}/v1/costHeadE`,
        update: `${baseUri}/v1/costHeadE`,
        delete: `${baseUri}/v1/costHeadE`,
        copy: `${baseUri}/v1/costHeadE/copy`,
        exportUrl: `${baseUri}/v1/costHeadE/export`,
        selectAllPaged: `${baseUri}/v1/costHeadE/list`,
        formWork: `${baseUri}/v1/costHeadE/export-tpl`,
        importUrl: `${baseUri}/v1/costHeadE/import`
      },
      body: {
        insert: `${baseUri}/v1/costListE`,
        update: `${baseUri}/v1/costListE`,
        delete: `${baseUri}/v1/costListE`,
        exportUrl: `${baseUri}/v1/costListE/export`,
        selectAllPaged: `${baseUri}/v1/costListE/list`,
        // 出口费用表体查询所有费用科目
        getCostCourseCode: `${baseUri}/v1/costListE/getCostCourseCode`
      }
    }
  },
  expenseAccountSettingNew: {
    insert: `${baseUri}/v1/cost/course`,
    update: `${baseUri}/v1/cost/course`,
    delete: `${baseUri}/v1/cost/course/batch`,
    exportUrl: `${baseUri}/v1/cost/course/export`,
    importUrl: `${baseUri}/v1/cost/course/import`,
    selectAllPaged: `${baseUri}/v1/cost/course/list`,
    exportTpl: `${baseUri}/v1/cost/course/export-tpl`
  },
  costMaintenanceNew: {
    comm: {
      // 获取
      getExchangeRate: `${baseUri}/v1/exchangeRate/list`
    },
    in: {
      head: {
        insert: `${baseUri}/v1/cost/headi`,
        update: `${baseUri}/v1/cost/headi`,
        delete: `${baseUri}/v1/cost/headi/batch`,
        exportUrl: `${baseUri}/v1/cost/headi/export`,
        selectAllPaged: `${baseUri}/v1/cost/headi/list`,
        selectAllErpPaged: `${baseUri}/v1/cost/headi/decerp`,
        locked: `${baseUri}/v1/cost/headi/locked`,
        unlocked: `${baseUri}/v1/cost/headi/unlocked`,
        exportTpl: `${baseUri}/v1/cost/headi/export-tpl`,
        importUrl: `${baseUri}/v1/cost/headi/import`
      },
      body: {
        insert: `${baseUri}/v1/cost/listi`,
        update: `${baseUri}/v1/cost/listi`,
        delete: `${baseUri}/v1/cost/listi/batch`,
        exportUrl: `${baseUri}/v1/cost/listi/export`,
        selectAllPaged: `${baseUri}/v1/cost/listi/list`
      }
    },
    out: {
      head: {
        insert: `${baseUri}/v1/cost/heade`,
        update: `${baseUri}/v1/cost/heade`,
        delete: `${baseUri}/v1/cost/heade/batch`,
        exportUrl: `${baseUri}/v1/cost/heade/export`,
        selectAllPaged: `${baseUri}/v1/cost/heade/list`,
        selectAllErpPaged: `${baseUri}/v1/cost/heade/decerp`,
        locked: `${baseUri}/v1/cost/heade/locked`,
        unlocked: `${baseUri}/v1/cost/heade/unlocked`,
        exportTpl: `${baseUri}/v1/cost/heade/export-tpl`,
        importUrl: `${baseUri}/v1/cost/heade/import`
      },
      body: {
        insert: `${baseUri}/v1/cost/liste`,
        update: `${baseUri}/v1/cost/liste`,
        delete: `${baseUri}/v1/cost/liste/batch`,
        exportUrl: `${baseUri}/v1/cost/liste/export`,
        selectAllPaged: `${baseUri}/v1/cost/liste/list`
      }
    }
  },
  exchange: {
    insert: `${baseUri}/v1/cost/exchange`,
    getByKey: `${baseUri}/v1/cost/exchange`,
    update: `${baseUri}/v1/cost/exchange`,
    batchUpdate: `${baseUri}/v1/cost/exchange/maintain`,
    delete: `${baseUri}/v1/cost/exchange/batch`,
    // 提取
    extraction: `${baseUri}/v1/cost/exchange/extraction`,
    exportUrl: `${baseUri}/v1/cost/exchange/exports`,
    selectAllPaged: `${baseUri}/v1/cost/exchange/list`,
    locked: `${baseUri}/v1/cost/exchange/locked`,
    unlocked: `${baseUri}/v1/cost/exchange/unlocked`
  },
  expenseReports: {
    imports: {
      exportUrl: `${baseUri}/v1/cost/headi/report/export`,
      selectAllPaged: `${baseUri}/v1/cost/headi/report`
    },
    exports: {
      exportUrl: `${baseUri}/v1/cost/heade/report/export`,
      selectAllPaged: `${baseUri}/v1/cost/heade/report`
    }
  },
  // 日月新
  riYueXin: {
    // 费用比对
    costComparison: {
      imports: {
        compare: `${baseUri}/v1/cost/compareI/compare`,                  // 比对
        updateNote: `${baseUri}/v1/cost/compareI/note`,                  // 修改接口
        sendAudit: `${baseUri}/v1/cost/compareI/sendAudit`,              // 发送内审
        sendAudits: `${baseUri}/v1/cost/compareI/sendAudits`,            // 发送内审
        selectAllHeadPaged: `${baseUri}/v1/cost/compareI/compareHead`,   // 表头查询
        selectAllBodyPaged: `${baseUri}/v1/cost/compareI/compareList`    // 表体查询
      },
      exports: {
        compare: `${baseUri}/v1/cost/compareE/compare`,                  // 比对
        updateNote: `${baseUri}/v1/cost/compareE/note`,                  // 修改接口
        sendAudit: `${baseUri}/v1/cost/compareE/sendAudit`,              // 发送内审
        sendAudits: `${baseUri}/v1/cost/compareE/sendAudits`,            // 发送内审
        selectAllHeadPaged: `${baseUri}/v1/cost/compareE/compareHead`,   // 表头查询
        selectAllBodyPaged: `${baseUri}/v1/cost/compareE/compareList`    // 表体查询
      }
    },
    // 费用审核
    costAeo: {
      imports: {
        auditBatch: `${baseUri}/v1/cost/auditI/audit`,
        audit: `${baseUri}/v1/cost/auditI/auditOpinion`,
        selectAllPaged: `${baseUri}/v1/cost/auditI/auditHead`
      },
      exports: {
        auditBatch: `${baseUri}/v1/cost/auditE/audit`,
        audit: `${baseUri}/v1/cost/auditE/auditOpinion`,
        selectAllPaged: `${baseUri}/v1/cost/auditE/auditHead`
      },
      costAudit: {
        // 费用审核记录查询
        getCostAuditList: `${baseUri}/v1/aeoAuditInfo/getCostAuditList`
      }
    }
  },
  //运保费比对报表
  actualCostCompare: {
    exportUrl: `${baseUri}/v1/actualCostCompare/export`,
    selectAllPaged: `${baseUri}/v1/actualCostCompare/list`
  }
}

export const mrp = {
  manage: {
    rest: `${baseUri}/v1/mrp/manage`,
    list: `${baseUri}/v1/mrp/manage/list`,
    batch: `${baseUri}/v1/mrp/manage/batch`,
    generate: `${baseUri}/v1/mrp/manage/generate`,
    fetch: `${baseUri}/v1/mrp/manage/refresh/dcr`
  },
  imgExg: {
    rest: `${baseUri}/v1/mrp/imgexg`,
    list: `${baseUri}/v1/mrp/imgexg/list`,
    stat: `${baseUri}/v1/mrp/imgexg/stat`,
    export: `${baseUri}/v1/mrp/imgexg/export`,
    batch: `${baseUri}/v1/mrp/imgexg/batch`,
    extract: `${baseUri}/v1/mrp/imgexg/extract`,
    extractSave: `${baseUri}/v1/mrp/imgexg/extract/save`
  },
  bom: {
    list: `${baseUri}/v1/mrp/bom/list`,
    export: `${baseUri}/v1/mrp/bom/export`,
    add: `${baseUri}/v1/mrp/bom`,
    addBatch: `${baseUri}/v1/mrp/bom/batch`,
    delete: `${baseUri}/v1/mrp/bom/batch`,
    allDelete: `${baseUri}/v1/mrp/bom/delete/batch`,
    update: `${baseUri}/v1/mrp/bom`,
    getInfo: `${baseUri}/v1/mrp/bom`,
    take: `${baseUri}/v1/mrp/bom/take`,
    getEmsNoSelect: `${baseUri}/v1/matImgexg/selectEmsNo`,
    maintain: `${baseUri}/v1/mrp/bom/maintain`,
    copy: `${baseUri}/v1/mrp/bom/maintain/copy`
  },
  return: {
    rest: `${baseUri}/v1/mrp/return`,
    list: `${baseUri}/v1/mrp/return/list`,
    start: `${baseUri}/v1/mrp/return/start`,
    stat: `${baseUri}/v1/mrp/return/stat`,
    summary: `${baseUri}/v1/mrp/return/summary`,
    summaryStat: `${baseUri}/v1/mrp/return/summary/stat`,
    export: `${baseUri}/v1/mrp/return/export`,
    exportSummary: `${baseUri}/v1/mrp/return/export/summary`,
    clean: `${baseUri}/v1/mrp/return/summary/clean/qty`,
    bom: `${baseUri}/v1/mrp/return/bom`,
    exportBom: `${baseUri}/v1/mrp/return/export/bom`,
    cleanZero: `${baseUri}/v1/mrp/return/summary/cleanZero`      // 清理零值
  },
  price: {
    rest: `${baseUri}/v1/mrp/price`,
    list: `${baseUri}/v1/mrp/price/list`,
    start: `${baseUri}/v1/mrp/price/start`,
    export: `${baseUri}/v1/mrp/price/export`,
    split: `${baseUri}/v1/mrp/price/split`,
    cav: `${baseUri}/v1/mrp/price/cav`,
    cavFetch: `${baseUri}/v1/mrp/price/cav/fetch`,
    exportPrice: `${baseUri}/v1/mrp/price/exportPrice`
  },
  trial: {
    rest: `${baseUri}/v1/mrp/trial`,
    list: `${baseUri}/v1/mrp/trial/list`,
    stat: `${baseUri}/v1/mrp/trial/stat`,
    export: `${baseUri}/v1/mrp/trial/export`,
    start: `${baseUri}/v1/mrp/trial/start`,
    exportBack: `${baseUri}/v1/mrp/trial/exportBack`
  },
  match: {
    rest: `${baseUri}/v1/mrp/match`,
    list: `${baseUri}/v1/mrp/match/list`,
    stat: `${baseUri}/v1/mrp/match/stat`,
    export: `${baseUri}/v1/mrp/match/export`,
    start: `${baseUri}/v1/mrp/match/start`,
    history: `${baseUri}/v1/mrp/match/history`,
    historyExport: `${baseUri}/v1/mrp/match/history/export`
  },
  balance: {
    rest: `${baseUri}/v1/mrp/balance`,
    list: `${baseUri}/v1/mrp/balance/list`,
    export: `${baseUri}/v1/mrp/balance/export`
  },
  history: {
    rest: `${baseUri}/v1/mrp/history`,
    list: `${baseUri}/v1/mrp/history/list`,
    clear: `${baseUri}/v1/mrp/history/clear`,
    batch: `${baseUri}/v1/mrp/history/batch`
  },
  dcr: {
    rest: `${baseUri}/v1/mrp/dcr`,
    prefetch: `${baseUri}/v1/mrp/dcr/prerefresh`,
    fetch: `${baseUri}/v1/mrp/dcr/refresh`,
    list: `${baseUri}/v1/mrp/dcr/list`,
    summaryList: `${baseUri}/v1/mrp/dcr/summary/list`,
    summaryExport: `${baseUri}/v1/mrp/dcr/summary/export`,
    detailList: `${baseUri}/v1/mrp/dcr/detail/list`,
    detailExport: `${baseUri}/v1/mrp/dcr/detail/export`
  }
}

/**
 * 减免税
 * @type {{head: {selectAllPaged: string, selectInsertUser: string, insert: string, delete: string}, list: {exportApply: string, devFreeApplyHead: string, getErpEmsListNo: string, selectAllPaged: string, exportGuarantee: string, checkData: string, freeParams: string, copy: string, exportInstructions: string, getErpTemp: string, export: string, customsGenerated: string}, body: {checkLinkedNoBySid: string, selectAllPaged: string, insert: string, update: string, extractErpData: string, matInfo: string, delete: string, export: string, getFacGNolist: string, getLinkedNo: string}}}
 */
export const tax = {
  list: {
    selectAllPaged: `${baseUri}/v1/devFreeApplyHead/list`,
    selectListForAudit: `${baseUri}/v1/devFreeApplyHead/selectListForAudit`,//减免税审核查询
    devFreeApplyHead: `${baseUri}/v1/devFreeApplyHead`,  //增、删、改
    copy: `${baseUri}/v1/devFreeApplyHead/copy`,   // 复制
    export: `${baseUri}/v1/devFreeApplyHead/export`,   // 导出
    exportApply: `${baseUri}/v1/devFreeApplyHead/exportApply`, // 导出申请表
    exportGuarantee: `${baseUri}/v1/devFreeApplyHead/exportGuarantee`, // 导出担保表
    exportInstructions: `${baseUri}/v1/devFreeApplyHead/exportInstructions`, // 导出用途说明书
    customsGenerated: `${baseUri}/v1/devFreeApplyHead/customsGenerated`, // 生成进口预录入单
    getErpEmsListNo: `${baseUri}/v1/devFreeApplyHead/getErpEmsListNo`, // 生成ERP提单内部编号
    getErpTemp: `${baseUri}/v1/devFreeApplyHead/getErpTemp`, // 提单模板数据源
    freeParams: `${baseUri}/v1/devFreeApplyHead/freeParams`, // 企业自定义参数
    checkData: `${baseUri}/v1/devFreeApplyHead/checkData`, // 判断是否可以导出报表
    sendMsg: `${baseUri}/v1/taxExemption/sendMsg`, // 减免税发送
    buildFiles: `${baseUri}/v1/devFreeApplyHead/buildFiles`, //资料生成
    print: `${baseUri}/v1/devFreeApplyHead/taxApplyForm`, //资料生成
    sendOutline:`${baseUri}/v1/devFreeApplyHead/sendOutline`, //发送概要申报
    applyComplete:`${baseUri}/v1/devFreeApplyHead/applyComplete`, //申请完整申报
    forceChangeStatus:`${baseUri}/v1/devFreeApplyHead/forceChangeStatus`, //强制修改状态
    moveUp:`${baseUri}/v1/devFreeApplyList/moveUp`, //上移
    moveDown:`${baseUri}/v1/devFreeApplyList/moveDown`, //下移
  },
  head: {
    selectAllPaged: `${baseUri}/v1/devFreeApplyTemplate/list`,
    delete: `${baseUri}/v1/devFreeApplyTemplate`,
    insert: `${baseUri}/v1/devFreeApplyTemplate`,
    selectInsertUser: `${baseUri}/v1/devFreeApplyTemplate/selectInsertUser`

  },
  body: {
    selectAllPaged: `${baseUri}/v1/devFreeApplyList/list`,
    insert: `${baseUri}/v1/devFreeApplyList`,
    update: `${baseUri}/v1/devFreeApplyList`,
    delete: `${baseUri}/v1/devFreeApplyList`,
    export: `${baseUri}/v1/devFreeApplyList/export`,
    extractErpData: `${baseUri}/v1/devFreeApplyList/extractErpData`,
    checkLinkedNoBySid: `${baseUri}/v1/devFreeApplyList/checkLinkedNoBySid`,
    getFacGNolist: `${baseUri}/v1/devFreeApplyList/getFacGNolist`,
    matInfo: `${baseUri}/v1/devFreeApplyList/matInfo`,
    getLinkedNo: `${baseUri}/v1/devFreeApplyList/getLinkedNo`
  }
}

/**
 * 首页
 * @type {{decErp: {getDecErpAmount: string, getDecErpQuantity: string}, instructor: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, audit: {selectAllPaged: string}, warning: {selectAllPaged: string}, deleteChange: {getEntryCheckCount: string}}}
 */
export const firstPage = {
  audit: {
    selectAllPaged: `${baseUri}/v1/firstPage/auditList`
  },
  warning: {
    selectAllPaged: `${baseUri}/v1/firstPage/warringList`
  },
  decErp: {
    getDecErpQuantity: `${baseUri}/v1/firstPage/ticketList`,
    getDecErpAmount: `${baseUri}/v1/firstPage/decTotalList`
  },
  deleteChange: {
    getEntryCheckCount: `${baseUri}/v1/firstPage/queryEntryErrorCheckCount`
  },
  // 操作指导
  instructor: {
    insert: `${baseUri}/v1/gwstdPageConfig`,
    update: `${baseUri}/v1/gwstdPageConfig`,
    delete: `${baseUri}/v1/gwstdPageConfig`,
    exportUrl: `${baseUri}/v1/gwstdPageConfig/export`,
    selectAllPaged: `${baseUri}/v1/gwstdPageConfig/list`
  }
}

/**
 * 费用管理 胜利
 * @type {{}}
 */
export const expenseManager = {
  // 快件表头
  quotationFast: {
    insert: `${baseUri}/v1/quotationHead`,
    update: `${baseUri}/v1/quotationHead`,
    delete: `${baseUri}/v1/quotationHead`,
    exportUrl: `${baseUri}/v1/quotationHead/export`,
    selectAllPaged: `${baseUri}/v1/quotationHead/list`,
    getQuoNoList: `${baseUri}/v1/quotationHead/getQuoNoList`,
    calTrafCost: `${baseUri}/v1/quotationHead/calTrafCost`
  },
  // 快件表体
  quotationFastBody: {
    insert: `${baseUri}/v1/quotationList`,
    update: `${baseUri}/v1/quotationList`,
    delete: `${baseUri}/v1/quotationList`,
    exportUrl: `${baseUri}/v1/quotationList/export`,
    selectAllPaged: `${baseUri}/v1/quotationList/list`
  },
  // 报价单科目
  quotationGenericCourse: {
    insert: `${baseUri}/v1/quotationCourse`,
    update: `${baseUri}/v1/quotationCourse`,
    delete: `${baseUri}/v1/quotationCourse`,
    selectAllPaged: `${baseUri}/v1/quotationCourse/list`
  },
  // 供应商比价
  supplierComparePrice: {
    insert: `${baseUri}/v1/goodsInfo`,
    update: `${baseUri}/v1/goodsInfo`,
    delete: `${baseUri}/v1/goodsInfo`,
    selectAllPaged: `${baseUri}/v1/goodsInfo/list`,
    exportUrl: `${baseUri}/v1/goodsInfo/export`,
    conQuotation: `${baseUri}/v1/goodsInfo/conQuotation`,               // 确认供应商 /{sid}/{quoNo}
    getPriceReport: `${baseUri}/v1/goodsInfo/getPriceReport`,           // 获取供应商比价报表 获取匹配单价页面的 列表信息
    getMatchAll: `${baseUri}/v1/goodsInfo/getMatchAll`,                 // v1/goodsInfo/getMatchAll/{sid} 获取所有匹配的报价单列表
    getMatchList: `${baseUri}/v1/goodsInfo/getMatchList`,               // 匹配单价  弹出页面  查询数据源
    calPrice: `${baseUri}/v1/goodsInfo/calPrice`,                       // v1/goodsInfo/calPrice/{sid} 确认计算
    exportPriceReport: `${baseUri}/v1/goodsInfo/exportPriceReport`,     // v1/goodsInfo/exportPriceReport/{sid} 供应商比较报表打印
    getConfList: `${baseUri}/v1/goodsInfo/getConfList`                  // v1/goodsInfo/getConfList/{sid} 获取已经确认计算的报价单列表
  },
  // 出口费用预估
  costEstimateE: {
    head: {
      costCal: `${baseUri}/v1/costEstimateE/costCal`,                     // 费用预估 {headId}
      exportUrl: `${baseUri}/v1/costEstimateE/export`,
      unlockOrlock: `${baseUri}/v1/costEstimateE/lock`,                   // {sid}/{lockStatus}  解锁 锁定
      selectAllPaged: `${baseUri}/v1/costEstimateE/list`,
      saveQuo: `${baseUri}/v1/costEstimateE/createRelation`,              // 保存报价单 {headId}
      getMatchQuo: `${baseUri}/v1/costEstimateE/getMatchQuo`,             // 获取报价单下拉数据源
      matchAndCal: `${baseUri}/v1/costEstimateE/matchAndCal`,             // 自动/手动预估
      getDataBySid: `${baseUri}/v1/costEstimateE/getSumTotal`             // 据Sid获取报价单 确认信息 /getRelation/{sid}
    },
    list: {
      insert: `${baseUri}/v1/costEstimateE/costDetail`,
      update: `${baseUri}/v1/costEstimateE/costDetail`,
      delete: `${baseUri}/v1/costEstimateE/costDetail`,
      export: `${baseUri}/v1/costEstimateE/costDetail/export`,
      selectAllPaged: `${baseUri}/v1/costEstimateE/costDetail/list`       // {headId}
    },
    report: {
      export: `${baseUri}/v1/costEstimateE/report/export`,                // 出口费用预估明细列表
      selectAllPaged: `${baseUri}/v1/costEstimateE/getPriceList`          // 出口费用预估明细报表导出
    }
  },
  // 进口费用预估
  costEstimateI: {
    head: {
      costCal: `${baseUri}/v1/costEstimateI/costCal`,                     // 费用预估 {headId}
      exportUrl: `${baseUri}/v1/costEstimateI/export`,
      unlockOrlock: `${baseUri}/v1/costEstimateI/lock`,                   // {sid}/{lockStatus}  解锁 锁定
      selectAllPaged: `${baseUri}/v1/costEstimateI/list`,
      saveQuo: `${baseUri}/v1/costEstimateI/createRelation`,              // 保存报价单 {headId}
      getMatchQuo: `${baseUri}/v1/costEstimateI/getMatchQuo`,             // 获取报价单下拉数据源
      matchAndCal: `${baseUri}/v1/costEstimateI/matchAndCal`,             // 自动/手动预估
      getDataBySid: `${baseUri}/v1/costEstimateI/getSumTotal`             // 据Sid获取报价单 确认信息  /getRelation/{sid}
    },
    list: {
      insert: `${baseUri}/v1/costEstimateI/costDetail`,
      update: `${baseUri}/v1/costEstimateI/costDetail`,
      delete: `${baseUri}/v1/costEstimateI/costDetail`,
      export: `${baseUri}/v1/costEstimateI/costDetail/export`,
      selectAllPaged: `${baseUri}/v1/costEstimateI/costDetail/list`       // {headId}
    },
    report: {
      export: `${baseUri}/v1/costEstimateI/report/export`,                // 出口费用预估明细列表 /getPriceList
      selectAllPaged: `${baseUri}/v1/costEstimateI/getPriceList`          // 出口费用预估明细报表导出
    }
  },
  // 费用预估基础信息接口
  costBasic: {
    insert: `${baseUri}/v1/costBasicInfo`,
    update: `${baseUri}/v1/costBasicInfo`,
    selectAllPaged: `${baseUri}/v1/costBasicInfo/list`
  },
  // 报价单相关接口
  quoNoAbout: {
    delete: `${baseUri}/v1/quotationHead/relation`,               // 删除
    confirmSelect: `${baseUri}/v1/quotationHead/relation`,        // 选择确认
    UnSelectQuoNoE: `${baseUri}/v1/costEstimateE/getMatchQuo`,    // 未选择列表
    UnSelectQuoNoI: `${baseUri}/v1/costEstimateI/getMatchQuo`,    // 未选择列表
    selectQuoNo: `${baseUri}/v1/quotationHead/getRelationList`    // 已选择列表
  },
  actualMainCost: {
    insert: `${baseUri}/v1/costActualInfo`,
    update: `${baseUri}/v1/costActualInfo`,
    getBean: `${baseUri}/v1/costActualInfo/get`
  },
  //疫情附加费
  costYqAdditional:{
    insert: `${baseUri}/v1/costYqAdditional`,
    update: `${baseUri}/v1/costYqAdditional`,
    delete: `${baseUri}/v1/costYqAdditional`,
    selectAllPaged: `${baseUri}/v1/costYqAdditional/list`,
    export: `${baseUri}/v1/costYqAdditional/export`
  },
  //国别州
  costCountryContinent:{
    insert: `${baseUri}/v1/costCountryContinent`,
    update: `${baseUri}/v1/costCountryContinent`,
    delete: `${baseUri}/v1/costCountryContinent`,
    selectAllPaged: `${baseUri}/v1/costCountryContinent/list`
  },
  //燃油附加费
  costFuelSurcharge:{
    insert: `${baseUri}/v1/costFuelSurcharge`,
    update: `${baseUri}/v1/costFuelSurcharge`,
    delete: `${baseUri}/v1/costFuelSurcharge`,
    selectAllPaged: `${baseUri}/v1/costFuelSurcharge/list`,
    export: `${baseUri}/v1/costFuelSurcharge/export`
  }
}

/**
 * 自定义报表
 * @type {{reportList: {selectAllPaged: string}, details: {exportUrl: string, selectAllPaged: string, getParams: string, getColumns: string}}}
 */
export const dynamicReports = {
  reportList: {
    selectAllPaged: `${baseUri}/v1/dynamic-report/list`
  },
  details: {
    exportUrl: `${baseUri}/v1/dynamic-report/export`,
    getParams: `${baseUri}/v1/dynamic-report/param`,
    getColumns: `${baseUri}/v1/dynamic-report/rtn-value`,
    selectAllPaged: `${baseUri}/v1/dynamic-report/query`
  }
}

/**
 * 报关复核
 * @type {{comm: {update: string}, correct: {exportUrl: string, selectAllPaged: string}, wrong: {exportUrl: string, selectAllPaged: string}}}
 */
export const decReviewData = {
  wrong: {
    exportUrl: `${baseUri}/v1/entryCompareDetail/export`,
    selectAllPaged: `${baseUri}/v1/entryCompareDetail/list`,
    manualCompare:`${baseUri}/v1/entryCompareDetail/manualCompare`,
  },
  correct: {
    exportUrl: `${baseUri}/v1/entCompareRes/export`,
    selectAllPaged: `${baseUri}/v1/entCompareRes/list`
  },
  comm: {
    update: `${baseUri}/v1/entCompareRes/assignTask`
  }
}

/**
 * 手账册选择
 * @type {{manual: {selectAllPaged: string}, account: {selectAllPaged: string}}}
 */
export const manualAccount = {
  manual: {
    selectAllPaged: `${baseUri}/v1/ztythEmsImgexg/getEmlNo`     // 金二手册
  },
  account: {
    selectAllPaged: `${baseUri}/v1/ztythEmsImgexg/getEmsNo`     // 金二账册
  },
  check: {
    checkCopEmsNo: `${baseUri}/v1/matImgexgOrg/checkCopEmsNo`   // 校验【当前备案号物料中心不存在无法进行操作】
  }
}

/**
 * 通关风险设置
 * @type {{}}
 */
export const customsClearanceRiskSetting = {
  /**
   * 代理用户设置
   */
  proxyUserSettings: {
    insert: `${baseUri}/v1/gwstdAgentConfig`,
    update: `${baseUri}/v1/gwstdAgentConfig`,
    delete: `${baseUri}/v1/gwstdAgentConfig`,
    selectAllPaged: `${baseUri}/v1/gwstdAgentConfig/list`,
    getProxy: `${baseUri}/v1/biClientInformation/agent/list`,
    getUserNoByForward:`${baseUri}/v1/gwstdAgentConfig/getUserNoByForward`
  },
  /**
   * 关务分区配置
   */
  partitionConfig: {
    grant: `${baseUri}/v1/gwstdPartitionConfig/grant`,
    selectAllPaged: `${baseUri}/v1/gwstdPartitionConfig/list`
  }
}

/**
 * 退运管理
 * @type {{feedingProducts: {selectAll: string, exportUrl: string, selectAllPaged: string, getIeInfo: string, insert: string, update: string, body: {checkLinkedNo: string, saveMatchCustoms: string, checkLinkedNoBySid: string, selectAllPaged: string, exportUrl: string, extractEQuery: string, insert: string, update: string, matchCustoms: string, checkBySid: string, delete: string, extractIQuery: string}, delete: string, getFacGNolist: string, customsGenerated: string, getLinkedNo: string}, repairProducts: {selectAllPaged: string, exportUrl: string, insert: string, update: string, body: {checkLinkedNo: string, saveMatchCustoms: string, checkLinkedNoBySid: string, selectAllPaged: string, exportUrl: string, extractEQuery: string, insert: string, update: string, matchCustoms: string, checkBySid: string, delete: string, extractIQuery: string}, delete: string, customsGenerated: string}, outboundRepair: {head: {reTrans: string, selectAllPaged: string, fetch: string, insert: string, update: string, delete: string}, body: {unExtraction: string, selectAllPaged: string, insert: string, update: string, delete: string}}, repairAndReShipment: {head: {dec: string, selectAllPaged: string, insert: string, update: string, delete: string}, body: {selectAllPaged: string, insert: string, update: string, extraction: string, delete: string}}, returnExchange: {exportUrl: string, selectAllPaged: string}, nonProducts: {selectAllPaged: string, exportUrl: string, insert: string, update: string, body: {checkLinkedNo: string, saveMatchCustoms: string, checkLinkedNoBySid: string, selectAllPaged: string, exportUrl: string, extractEQuery: string, insert: string, update: string, matchCustoms: string, checkBySid: string, delete: string, extractIQuery: string}, delete: string, customsGenerated: string}, exchangeBalance: {exportUrl: string, selectAllPaged: string}}}
 */
export const returnManagement = {
  //进料成品退换
  feedingProducts: {
    insert: `${baseUri}/v1/gwReturnHeadBonded`,
    update: `${baseUri}/v1/gwReturnHeadBonded`,
    delete: `${baseUri}/v1/gwReturnHeadBonded`,
    exportUrl: `${baseUri}/v1/gwReturnHeadBonded/export`,
    selectAllPaged: `${baseUri}/v1/gwReturnHeadBonded/list`,
    customsGenerated: `${baseUri}/v1/gwReturnHeadBonded/customsGenerated`,
    body: {
      insert: `${baseUri}/v1/gwReturnListBonded`,
      update: `${baseUri}/v1/gwReturnListBonded`,
      delete: `${baseUri}/v1/gwReturnListBonded`,
      selectAllPaged: `${baseUri}/v1/gwReturnListBonded/list`,
      exportUrl: `${baseUri}/v1/gwReturnListBonded/export`,
      // 报关数据
      matchCustoms: `${baseUri}/v1/gwReturnListBonded/matchCustoms`,
      saveMatchCustoms: `${baseUri}/v1/gwReturnListBonded/saveMatchCustoms`,
      extractEQuery: `${baseUri}/v1/gwReturnListBonded/extractEQuery`, // 出口提取
      extractIQuery: `${baseUri}/v1/gwReturnListBonded/extractIQuery`,  // 进口提取
      checkLinkedNoBySid: `${baseUri}/v1/gwReturnListBonded/checkLinkedNoBySid`, // 进口勾选提取
      checkLinkedNo: `${baseUri}/v1/gwReturnListBonded/checkLinkedNo`, // 进口全部提取
      checkBySid: `${baseUri}/v1/gwReturnListBonded/checkBySid` // 出口勾选提取
    },
    selectAll: `${baseUri}/v1/decErpHeadTemplete/selectAll`,
    getIeInfo: `${baseUri}/v1/matImgexgOrg/getIeInfo`,
    getFacGNolist: `${baseUri}/v1/matImgexgOrg/getFacGNolist`,
    getLinkedNo: `${baseUri}/v1/decErpEListN/getLinkedNo`
  },
  // 退换明细
  returnExchange: {
    exportUrl: `${baseUri}/v1/gwReturnReport/export`,
    selectAllPaged: `${baseUri}/v1/gwReturnReport/list`
  },
  // 成品退换平衡
  exchangeBalance: {
    exportUrl: `${baseUri}/v1/gwReturnReport/exportBalance`,
    selectAllPaged: `${baseUri}/v1/gwReturnReport/returnBalance`
  },
  // 非保成品退换
  nonProducts: {
    insert: `${baseUri}/v1/gwReturnHeadNon`,
    update: `${baseUri}/v1/gwReturnHeadNon`,
    delete: `${baseUri}/v1/gwReturnHeadNon`,
    selectAllPaged: `${baseUri}/v1/gwReturnHeadNon/list`,
    exportUrl: `${baseUri}/v1/gwReturnHeadNon/export`,
    customsGenerated: `${baseUri}/v1/gwReturnHeadNon/customsGenerated`,
    body: {
      insert: `${baseUri}/v1/gwReturnListNon`,
      update: `${baseUri}/v1/gwReturnListNon`,
      delete: `${baseUri}/v1/gwReturnListNon`,
      selectAllPaged: `${baseUri}/v1/gwReturnListNon/list`,
      exportUrl: `${baseUri}/v1/gwReturnListNon/export`,
      // 报关数据
      matchCustoms: `${baseUri}/v1/gwReturnListNon/matchCustoms`,
      saveMatchCustoms: `${baseUri}/v1/gwReturnListNon/saveMatchCustoms`,
      extractEQuery: `${baseUri}/v1/gwReturnListNon/extractEQuery`, // 出口提取
      extractIQuery: `${baseUri}/v1/gwReturnListNon/extractIQuery`,  // 进口提取
      checkLinkedNoBySid: `${baseUri}/v1/gwReturnListNon/checkLinkedNoBySid`, // 进口勾选提取
      checkLinkedNo: `${baseUri}/v1/gwReturnListNon/checkLinkedNo`, // 进口全部提取
      checkBySid: `${baseUri}/v1/gwReturnListNon/checkBySid` // 出口勾选提取
    }
  },
  // 修理物品
  repairProducts: {
    insert: `${baseUri}/v1/gwReturnHeadRepair`,
    update: `${baseUri}/v1/gwReturnHeadRepair`,
    delete: `${baseUri}/v1/gwReturnHeadRepair`,
    selectAllPaged: `${baseUri}/v1/gwReturnHeadRepair/list`,
    exportUrl: `${baseUri}/v1/gwReturnHeadRepair/export`,
    customsGenerated: `${baseUri}/v1/gwReturnHeadRepair/customsGenerated`,
    body: {
      insert: `${baseUri}/v1/gwReturnListRepair`,
      update: `${baseUri}/v1/gwReturnListRepair`,
      delete: `${baseUri}/v1/gwReturnListRepair`,
      selectAllPaged: `${baseUri}/v1/gwReturnListRepair/list`,
      exportUrl: `${baseUri}/v1/gwReturnListRepair/export`,
      // 报关数据
      matchCustoms: `${baseUri}/v1/gwReturnListRepair/matchCustoms`,
      saveMatchCustoms: `${baseUri}/v1/gwReturnListRepair/saveMatchCustoms`,
      extractEQuery: `${baseUri}/v1/gwReturnListRepair/extractEQuery`, // 出口提取
      extractIQuery: `${baseUri}/v1/gwReturnListRepair/extractIQuery`,  // 进口提取
      checkLinkedNoBySid: `${baseUri}/v1/gwReturnListRepair/checkLinkedNoBySid`, // 进口勾选提取
      checkLinkedNo: `${baseUri}/v1/gwReturnListRepair/checkLinkedNo`, // 进口全部提取
      checkBySid: `${baseUri}/v1/gwReturnListRepair/checkBySid` // 出口勾选提取
    }
  },
  // 出境修理
  outboundRepair: {
    head: {
      insert: `${baseUri}/v1/repair/ehead`,
      update: `${baseUri}/v1/repair/ehead`,
      delete: `${baseUri}/v1/repair/ehead`,
      // 复运
      reTrans: `${baseUri}/v1/repair/ehead/I`,
      fetch: `${baseUri}/v1/repair/ehead/fetch`,
      selectAllPaged: `${baseUri}/v1/repair/ehead/list`
    },
    body: {
      insert: `${baseUri}/v1/repair/elist`,
      update: `${baseUri}/v1/repair/elist`,
      delete: `${baseUri}/v1/repair/elist`,
      selectAllPaged: `${baseUri}/v1/repair/elist/list`,
      // 待提取的数据
      unExtraction: `${baseUri}/v1/repair/eilist/unextraction`
    }
  },
  // 修理复运进境
  repairAndReShipment: {
    head: {
      insert: `${baseUri}/v1/repair/eihead`,
      update: `${baseUri}/v1/repair/eihead`,
      delete: `${baseUri}/v1/repair/eihead`,
      // 报关生成
      dec: `${baseUri}/v1/repair/eihead/dec`,
      selectAllPaged: `${baseUri}/v1/repair/eihead/list`
    },
    body: {
      insert: `${baseUri}/v1/repair/eilist`,
      update: `${baseUri}/v1/repair/eilist`,
      delete: `${baseUri}/v1/repair/eilist`,
      selectAllPaged: `${baseUri}/v1/repair/eilist/list`,
      // 提取数据
      extraction: `${baseUri}/v1/repair/eilist/extraction`
    }
  },
  // 入境修理
  inboundRepair: {
    head: {
      insert: `${baseUri}/v1/repair/ihead`,
      update: `${baseUri}/v1/repair/ihead`,
      delete: `${baseUri}/v1/repair/ihead`,
      // 复运
      reTrans: `${baseUri}/v1/repair/ihead/E`,
      fetch: `${baseUri}/v1/repair/ihead/fetch`,
      selectAllPaged: `${baseUri}/v1/repair/ihead/list`
    },
    body: {
      insert: `${baseUri}/v1/repair/ilist`,
      update: `${baseUri}/v1/repair/ilist`,
      delete: `${baseUri}/v1/repair/ilist`,
      selectAllPaged: `${baseUri}/v1/repair/ilist/list`,
      // 待提取的数据
      unExtraction: `${baseUri}/v1/repair/ielist/unextraction`
    }
  },
  // 修理复运出境
  repairAndReShipmentE: {
    head: {
      insert: `${baseUri}/v1/repair/iehead`,
      update: `${baseUri}/v1/repair/iehead`,
      delete: `${baseUri}/v1/repair/iehead`,
      // 报关生成
      dec: `${baseUri}/v1/repair/iehead/dec`,
      selectAllPaged: `${baseUri}/v1/repair/iehead/list`
    },
    body: {
      insert: `${baseUri}/v1/repair/ielist`,
      update: `${baseUri}/v1/repair/ielist`,
      delete: `${baseUri}/v1/repair/ielist`,
      selectAllPaged: `${baseUri}/v1/repair/ielist/list`,
      // 提取数据
      extraction: `${baseUri}/v1/repair/ielist/extraction`
    }
  },
  // 退运进口管理
  returnIManagement: {
    head: {
      insert: `${baseUri}/v1/returnHeadNonExg`,
      update: `${baseUri}/v1/returnHeadNonExg`,
      delete: `${baseUri}/v1/returnHeadNonExg`,
      exportUrl: `${baseUri}/v1/returnHeadNonExg/export`,
      selectAllPaged: `${baseUri}/v1/returnHeadNonExg/list`,
      generateDecData: `${baseUri}/v1/returnHeadNonExg/customsGenerated`
    },
    toBeMatched: {
      insert: `${baseUri}/v1/returnListNonExg`,
      update: `${baseUri}/v1/returnListNonExg`,
      delete: `${baseUri}/v1/returnListNonExg`,
      exportUrl: `${baseUri}/v1/returnListNonExg/export`,
      getEmsNo: `${baseUri}/v1/returnListNonExg/getEmsNo`,
      selectAllPaged: `${baseUri}/v1/returnListNonExg/list`,
      matchDecData: `${baseUri}/v1/returnListNonExg/matchCustoms`,
      saveMatchCustoms: `${baseUri}/v1/returnListNonExg/saveMatchCustoms`
    },
    matched: {
      exportUrl: `${baseUri}/v1/returnListNonExg/export`,
      selectAllPaged: `${baseUri}/v1/returnListNonExg/list`
    }
  },
  // 退运出口管理
  returnEManagement: {
    head: {
      insert: `${baseUri}/v1/returnHeadNonExg`,
      update: `${baseUri}/v1/returnHeadNonExg`,
      delete: `${baseUri}/v1/returnHeadNonExg`,
      exportUrl: `${baseUri}/v1/returnHeadNonExg/export`,
      selectAllPaged: `${baseUri}/v1/returnHeadNonExg/list`,
      generateDecData: `${baseUri}/v1/returnHeadNonExg/customsGenerated`
    },
    toBeMatched: {
      insert: `${baseUri}/v1/returnListNonExg`,
      update: `${baseUri}/v1/returnListNonExg`,
      delete: `${baseUri}/v1/returnListNonExg`,
      exportUrl: `${baseUri}/v1/returnListNonExg/export`,
      getEmsNo: `${baseUri}/v1/returnListNonExg/getEmsNo`,
      selectAllPaged: `${baseUri}/v1/returnListNonExg/list`,
      matchDecData: `${baseUri}/v1/returnListNonExg/matchCustoms`,
      saveMatchCustoms: `${baseUri}/v1/returnListNonExg/saveMatchCustoms`
    },
    matched: {
      exportUrl: `${baseUri}/v1/returnListNonExg/export`,
      selectAllPaged: `${baseUri}/v1/returnListNonExg/list`
    }
  },
  // 进(出)口费用比对
  costComparison: {
    imports: {
      exportUrl: `${baseUri}/v1/cost/compareI/export`,
      selectAllPaged: `${baseUri}/v1/cost/compareI/list`
    },
    exports: {
      exportUrl: `${baseUri}/v1/cost/compareE/export`,
      selectAllPaged: `${baseUri}/v1/cost/compareE/list`
    }
  }
}

/**
 * 暂时进出境
 * @type {{reShipmentE: {}, reShipmentI: {}, temporaryERecord: {}, temporaryIRecord: {}}}
 */
export const temporaryIE = {
  /**
   * 复运出境管理
   */
  reShipmentE: {
    head: {
      insert: `${baseUri}/v1/temporary/iehead`,
      update: `${baseUri}/v1/temporary/iehead`,
      delete: `${baseUri}/v1/temporary/iehead`,
      // 报关生成
      dec: `${baseUri}/v1/temporary/iehead/dec`,
      selectAllPaged: `${baseUri}/v1/temporary/iehead/list`
    },
    body: {
      insert: `${baseUri}/v1/temporary/ielist`,
      update: `${baseUri}/v1/temporary/ielist`,
      delete: `${baseUri}/v1/temporary/ielist`,
      selectAllPaged: `${baseUri}/v1/temporary/ielist/list`,
      // 提取数据
      extraction: `${baseUri}/v1/temporary/ielist/extraction`,
      // 待提取的数据
      unExtraction: `${baseUri}/v1/temporary/ielist/unextraction`
    }
  },
  /**
   * 复运进境管理
   */
  reShipmentI: {
    head: {
      insert: `${baseUri}/v1/temporary/eihead`,
      update: `${baseUri}/v1/temporary/eihead`,
      delete: `${baseUri}/v1/temporary/eihead`,
      // 报关生成
      dec: `${baseUri}/v1/temporary/eihead/dec`,
      selectAllPaged: `${baseUri}/v1/temporary/eihead/list`
    },
    body: {
      insert: `${baseUri}/v1/temporary/eilist`,
      update: `${baseUri}/v1/temporary/eilist`,
      delete: `${baseUri}/v1/temporary/eilist`,
      selectAllPaged: `${baseUri}/v1/temporary/eilist/list`,
      // 提取数据
      extraction: `${baseUri}/v1/temporary/eilist/extraction`,
      // 待提取的数据
      unExtraction: `${baseUri}/v1/temporary/eilist/unextraction`
    }
  },
  /**
   * 暂时出境记录
   */
  temporaryERecord: {
    head: {
      insert: `${baseUri}/v1/temporary/ehead`,
      update: `${baseUri}/v1/temporary/ehead`,
      delete: `${baseUri}/v1/temporary/ehead`,
      // 复运
      reTrans: `${baseUri}/v1/temporary/ehead/I`,
      fetch: `${baseUri}/v1/temporary/ehead/fetch`,
      selectAllPaged: `${baseUri}/v1/temporary/ehead/list`
    },
    body: {
      insert: `${baseUri}/v1/temporary/elist`,
      update: `${baseUri}/v1/temporary/elist`,
      delete: `${baseUri}/v1/temporary/elist`,
      selectAllPaged: `${baseUri}/v1/temporary/elist/list`
    }
  },
  /**
   * 暂时进境记录
   */
  temporaryIRecord: {
    head: {
      insert: `${baseUri}/v1/temporary/ihead`,
      update: `${baseUri}/v1/temporary/ihead`,
      delete: `${baseUri}/v1/temporary/ihead`,
      // 复运
      reTrans: `${baseUri}/v1/temporary/ihead/E`,
      fetch: `${baseUri}/v1/temporary/ihead/fetch`,
      selectAllPaged: `${baseUri}/v1/temporary/ihead/list`,
      getEmsListNo:`${baseUri}/v1/decErpEHeadN/getEmsListNo`,
    },
    body: {
      insert: `${baseUri}/v1/temporary/ilist`,
      update: `${baseUri}/v1/temporary/ilist`,
      delete: `${baseUri}/v1/temporary/ilist`,
      selectAllPaged: `${baseUri}/v1/temporary/ilist/list`
    }
  }
}

/**
 * 托书管理
 * @type {{cargoPowerOfAttorney: {head: {selectAllPaged: string, delete: string}, extractPop: {selectAllPaged: string, extraction: string}, body: {selectAllPaged: string}}}}
 */
export const entrustedManage = {
  cargoPowerOfAttorney: {
    // 提取
    extractPop: {
      extraction: `${baseUri}/v1/entrustFileHead/extract`,
      selectAllPaged: `${baseUri}/v1/entrustFileHead/extract/list`
    },
    // 表头
    head: {
      update: `${baseUri}/v1/entrustFileHead`,
      delete: `${baseUri}/v1/entrustFileHead`,
      // 下载
      downLoad: `${baseUri}/v1/`,
      // 批量下载
      downLoadBatch: `${baseUri}/v1/entrustFileHead/download`,
      // 单据生成
      docGeneration: `${baseUri}/v1/entrustFileHead/createBills`,
      selectAllPaged: `${baseUri}/v1/entrustFileHead/list`
    },
    // 表体
    body: {
      selectAllPaged: `${baseUri}/v1/entrustFileList/list`
    }
  }
}

/**
 * 定制项目
 * @type {{}}
 */
export const customMade = {
  /**
   * 大金
   */
  daiKin: {
    exportManagement: {
      /**
       * 出口管理-出口提单表体订单提取接口
       */
      orderWithdrawal: {
        selectAllPaged: `${baseUri}/v1/orderList/list`,
        extractByParams: `${baseUri}/v1/decErpEListN/orderExtract/extract`,       // 根据查询条件提取
        extractByChoose: `${baseUri}/v1/decErpEListN/orderExtract/extractBySid`,   // 根据选择提取
        getAllTypes: `${baseUri}/v1/orderList/getAllTypes`
      }
    }
  },
  /**
   * 克诺尔
   */
  knorr: {
    /**
     * 报表中心
     */
    reportCenter: {
      /**
       * 到货清单
       */
      arrivalList: {
        exportUrl: `${baseUri}/v1/api_cus/knorr/billExport`,
        selectAllPaged: `${baseUri}/v1/api_cus/knorr/billList`
      },
      /**
       * 物流查询
       */
      logisticsQueryList: {
        exportUrl: `${baseUri}/v1/api_cus/knorr/logisticsExport`,
        selectAllPaged: `${baseUri}/v1/api_cus/knorr/logisticsList`
      },
      /**
       * 报关时效追踪
       */
      customsClearanceTrackingList: {
        exportUrl: `${baseUri}/v1/api_cus/knorr/entryExport`,
        selectAllPaged: `${baseUri}/v1/api_cus/knorr/entryList`
      },
      /**
       * 集装箱查询
       */
      containerQueryList: {
        exportUrl: `${baseUri}/v1/preDecErpHead/exportContainerList`,
        selectAllPaged: `${baseUri}/v1/preDecErpHead/getContainerlist`
      }
    }
  },
  /**
   * 牧东
   */
  muDong: {
    preEntryToBeConfirmed: {
      /**
       * 待确认列表
       */
      toBeConfirmedList: {
        filter: `${baseUri}/v1/decTobeConfirmed/filter`,
        exportUrl: `${baseUri}/v1/decTobeConfirmed/export`,
        selectAllPaged: `${baseUri}/v1/decTobeConfirmed/list`,
        financeConfirmedUrl: `${baseUri}/v1/decTobeConfirmed/transferToFinance`,
        purchaseConfirmedUrl: `${baseUri}/v1/decTobeConfirmed/transferToPurchase`
      },
      /**
       * 财务待确认
       */
      financeToBeConfirmed: {
        exportUrl: `${baseUri}/v1/decTobeConfirmed/export`,
        selectAllPaged: `${baseUri}/v1/decTobeConfirmed/list`,
        cancelUrl: `${baseUri}/v1/decTobeConfirmed/financeCancel`,
        returnUrl: `${baseUri}/v1/decTobeConfirmed/financeRefuse`,
        confirmUrl: `${baseUri}/v1/decTobeConfirmed/financeConfirm`,
        checkForFinanceConfirm: `${baseUri}/v1/decTobeConfirmed/checkForFinanceConfirm`,   // 财务-确认校验
        CancelCheckUrl: `${baseUri}/v1/decTobeConfirmed/financeCancelCheck`   // 财务-取消校验
      },
      /**
       * 采购待确认
       */
      purchaseToBeConfirmed: {
        exportUrl: `${baseUri}/v1/decTobeConfirmed/export`,
        selectAllPaged: `${baseUri}/v1/decTobeConfirmed/list`,
        cancelUrl: `${baseUri}/v1/decTobeConfirmed/purchaseCancel`,
        returnUrl: `${baseUri}/v1/decTobeConfirmed/purchaseRefuse`,
        confirmUrl: `${baseUri}/v1/decTobeConfirmed/purchaseConfirm`
      }
    }
  }
}

/**
 * 出货管理
 * @type {{}}
 */
export const shipmentManagement = {
  /**
   * 出口计划
   */
  exportPlan: {
    head: {
      insert: `${baseUri}/v1/decEPlanHead`,
      update: `${baseUri}/v1/decEPlanHead`,
      delete: `${baseUri}/v1/decEPlanHead`,
      exportUrl: `${baseUri}/v1/decEPlanHead/export`,
      checkData: `${baseUri}/v1/decEPlanHead/checkData`,
      extract: `${baseUri}/v1/decEPlanHead/extractData`,
      selectAllPaged: `${baseUri}/v1/decEPlanHead/list`,
      batchExtract: `${baseUri}/v1/decEPlanHead/extractAllData`,
      generateCustomsInfo: `${baseUri}/v1/decEPlanHead/exportInvoiceCustoms`
    },
    body: {
      insert: `${baseUri}/v1/decEPlanList`,
      update: `${baseUri}/v1/decEPlanList`,
      delete: `${baseUri}/v1/decEPlanList`,
      exportUrl: `${baseUri}/v1/decEPlanList/export`,
      selectAllPaged: `${baseUri}/v1/decEPlanList/list`
    }
  }
}

/**
 * 收付汇管理
 * @type {{}}
 */
export const receiptPaymentManagement = {
  // 客户付款条件
  paymentCondition: {
    insert: `${baseUri}/v1/customerPayment`,
    update: `${baseUri}/v1/customerPayment`,
    delete: `${baseUri}/v1/customerPayment`,
    exportUrl: `${baseUri}/v1/customerPayment/export`,
    selectAllPaged: `${baseUri}/v1/customerPayment/list`
  },
  // 收汇管理
  collectionManagement: {
    head: {
      insert: `${baseUri}/v1/collectionManagementHead`,
      update: `${baseUri}/v1/collectionManagementHead`,
      delete: `${baseUri}/v1/collectionManagementHead`,
      exportUrl: `${baseUri}/v1/collectionManagementHead/export`,
      selectAllPaged: `${baseUri}/v1/collectionManagementHead/list`
    },
    body: {
      insert: `${baseUri}/v1/collectionManagementList`,
      update: `${baseUri}/v1/collectionManagementList`,
      delete: `${baseUri}/v1/collectionManagementList`,
      exportUrl: `${baseUri}/v1/collectionManagementList/export`,
      selectAllPaged: `${baseUri}/v1/collectionManagementList/list`
    }
  },
  // 收汇报表
  collectionReport: {
    exportUrl: `${baseUri}/v1/vCollectionManagementHeadlist/export`,
    selectAllPaged: `${baseUri}/v1/vCollectionManagementHeadlist/list`
  }
}

/**
 * 大金订单管理
 * @type {{}}
 */
export const daiKinOrderManagement = {
  // 单价维护
  priceMaintenance: {
    insert: `${baseUri}/v1/orderPrice`,
    update: `${baseUri}/v1/orderPrice`,
    delete: `${baseUri}/v1/orderPrice`,
    exportUrl: `${baseUri}/v1/orderPrice`,
    selectAllPaged: `${baseUri}/v1/orderPrice/list`
  },
  // 订单管理
  orderManagement: {
    jobTask: {
      insertJob: `${baseUri}/v1/gwstdJob/insertJob`
    },
    head: {
      insert: `${baseUri}/v1/orderHead`,
      update: `${baseUri}/v1/orderHead`,
      delete: `${baseUri}/v1/orderHead`,
      // calculate: `${baseUri}/v1/`,
      // exportUrl: `${baseUri}/v1/`,
      selectAllPaged: `${baseUri}/v1/orderHead/list`
    },
    body: {
      update: `${baseUri}/v1/orderList`,
      selectAllPaged: `${baseUri}/v1/orderList/list`
    }
  },
  // 订单报表
  orderReport: {
    selectAllPaged: `${baseUri}/v1/orderList/report`,
    exportUrl: `${baseUri}/v1/orderList/report/export`
  }
}

/**
 * 价格说明管理
 * @type {{}}
 */
export const commitmentManagement = {
  /**
   * 料号级别
   */
  itemLevel: {
    insert: `${baseUri}/v1/commitmentsManager`,
    update: `${baseUri}/v1/commitmentsManager`,
    delete: `${baseUri}/v1/commitmentsManager`,
    selectAllPaged: `${baseUri}/v1/commitmentsManager/list`
  },
  /**
   * 订单级别
   */
  orderLevel: {
    insert: `${baseUri}/v1/commitmentsManager`,
    update: `${baseUri}/v1/commitmentsManager`,
    delete: `${baseUri}/v1/commitmentsManager`,
    selectAllPaged: `${baseUri}/v1/commitmentsManager/list`
  },
  /**
   * 供应商级别
   */
  supplierLevel: {
    insert: `${baseUri}/v1/commitmentsManager`,
    update: `${baseUri}/v1/commitmentsManager`,
    delete: `${baseUri}/v1/commitmentsManager`,
    selectAllPaged: `${baseUri}/v1/commitmentsManager/list`
  }
}

/**
 * 保税退运
 * @type {{materialComeback: {head: {generateCustomsData: string, exportUrl: string, getEmsNo: string, selectAllPaged: string, getTempDatas: string, insert: string, update: string, getErpTemp: string, delete: string}, matched: {exportUrl: string, selectAllPaged: string}, body: {exportUrl: string, selectAllPaged: string, insert: string, update: string, delete: string}, toBeMatched: {selectAllPaged: string, save: string}}}}
 */
export const taxReturn = {
  /**
   * 料件复出
   */
  materialComeback: {
    head: {
      insert: `${baseUri}/v1/returnHeadImg`,
      update: `${baseUri}/v1/returnHeadImg`,
      delete: `${baseUri}/v1/returnHeadImg`,
      exportUrl: `${baseUri}/v1/returnHeadImg/export`,
      // 获取备案号
      getEmsNo: `${baseUri}/v1/returnHeadImg/getEmsNo`,
      selectAllPaged: `${baseUri}/v1/returnHeadImg/list`,
      // 获取保税出口提单模板
      getErpTemp: `${baseUri}/v1/returnHeadImg/getErpTemp`,
      // 获取保税出口提单模板
      getTempDatas: `${baseUri}/v1/returnHeadImg/getTempDatas`,
      // 生成预录入单
      generateCustomsData: `${baseUri}/v1/returnHeadImg/customsGenerated`
    },
    body: {
      insert: `${baseUri}/v1/returnListImg`,
      update: `${baseUri}/v1/returnListImg`,
      delete: `${baseUri}/v1/returnListImg`,
      exportUrl: `${baseUri}/v1/returnListImg/export`,
      selectAllPaged: `${baseUri}/v1/returnListImg/list`
    },
    toBeMatched: {
      autoMatch: `${baseUri}/v1/returnListImg/autoMatch`,
      save: `${baseUri}/v1/returnListImg/saveMatchCustoms`,
      selectAllPaged: `${baseUri}/v1/returnListImg/matchCustoms`
    },
    matched: {
      exportUrl: `${baseUri}/v1/returnMatchImg/export`,
      selectAllPaged: `${baseUri}/v1/returnMatchImg/list`
    }
  }

}
//出口计划
export const plan = {
  export: {
    head:{
      selectAllPaged: `${baseUri}/v1/exportPlanHead/list`,
      insert: `${baseUri}/v1/exportPlanHead`,
      update: `${baseUri}/v1/exportPlanHead`,
      delete: `${baseUri}/v1/exportPlanHead`,
      exportUrl: `${baseUri}/v1/exportPlanHead/export`,
      updatePackingStatus: `${baseUri}/v1/exportPlanHead/updatePackingStatus`,
      customsGenerated: `${baseUri}/v1/exportPlanHead/customsGenerated`,
    },
    body:{
      selectAllPaged: `${baseUri}/v1/exportPlanList/list`,
      insert: `${baseUri}/v1/exportPlanList`,
      update: `${baseUri}/v1/exportPlanList`,
      delete: `${baseUri}/v1/exportPlanList`,
      exportUrl: `${baseUri}/v1/exportPlanList/export`,
      getBodyData: `${baseUri}/v1/exportPlanList/getBodyData`,
      checkLinkedNo: `${baseUri}/v1/exportPlanList/checkLinkedNo`,
      checkLinkedNoBySid: `${baseUri}/v1/exportPlanList/checkLinkedNoBySid`,
    },
    pack:{
      selectAllPaged: `${baseUri}/v1/exportPlanPacking/list`,
      insert: `${baseUri}/v1/exportPlanPacking`,
      update: `${baseUri}/v1/exportPlanPacking`,
      delete: `${baseUri}/v1/exportPlanPacking`,
      exportUrl: `${baseUri}/v1/exportPlanPacking/export`,
      generatePacking: `${baseUri}/v1/exportPlanPacking/generatePacking`,
    }
  }
}

//包材管理
export const packaging={
  packag:{
    selectAllPaged: `${baseUri}/v1/gwstdGzResale/list`,
    insert: `${baseUri}/v1/gwstdGzResale`,
    update: `${baseUri}/v1/gwstdGzResale`,
    delete: `${baseUri}/v1/gwstdGzResale`,
    copGNoList: `${baseUri}/v1/matNonBonded/getCopGNoList`,
    getcopGNo: `${baseUri}/v1/matNonBonded/getCopGNo`,
  },
  requisition:{
    selectAllPaged: `${baseUri}/v1/gwstdRequisitionManagement/list`,
    insert: `${baseUri}/v1/gwstdRequisitionManagement`,
    update: `${baseUri}/v1/gwstdRequisitionManagement`,
    delete: `${baseUri}/v1/gwstdRequisitionManagement`,
  },
  emptyManagement:{
    selectAllPaged: `${baseUri}/v1/gwstdEmptyManagement/list`,
    insert: `${baseUri}/v1/gwstdEmptyManagement`,
    update: `${baseUri}/v1/gwstdEmptyManagement`,
    delete: `${baseUri}/v1/gwstdEmptyManagement`,
  },
  packagingStatistics:{
    selectAllPaged: `${baseUri}/v1/gwstdPackagingStatistics/list`
  },
  /**
   * 异步任务执行方法
   */
  jobComm: {
    insertJob: `${baseUri}/v1/gwstdJob/insertJob`,
    getLastJob: `${baseUri}/v1/gwstdJob/getLastJobInfo`
  },
}
