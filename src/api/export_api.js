let { Vue } = majesty
import { getHttpHeaderFileName, blobSaveFile } from '@/libs/util'

const saveExcelFile = (res, filename) => {
  if (checkError(res)) {
    return
  }
  const headName = getHttpHeaderFileName(res.headers)
  const blob = new Blob([res.data], {
    type: 'application/vnd.ms-excel'
  })
  blobSaveFile(blob, headName ? headName : filename)
}

export const excelExport = (url, params, method='post') => {
  let excelRes
  if (method === 'get') {
    excelRes = Vue.http.get(url, {
      responseType: 'blob'
    })
  }
  if (method === 'post') {
    excelRes = Vue.http.post(url, params, {
      responseType: 'blob'
    })
  }
  return excelRes.then(res => {
    saveExcelFile(res, `${params.name}.xls`)
    if(callback != null) callback()
  })
}

const savePdfFile = (res, filename) => {
  if (checkError(res)) {
    return
  }
  const headName = getHttpHeaderFileName(res.headers)
  const blob = new Blob([res.data], {
    type: 'application/pdf'
  })
  blobSaveFile(blob, headName ? headName : filename)
}

export const pdfExport = (url, params, method='post') => {
  let excelRes
  if (method === 'get') {
    excelRes = Vue.http.get(url, {
      responseType: 'blob'
    })
  }
  if (method === 'post') {
    excelRes = Vue.http.post(url, params, {
      responseType: 'blob'
    })
  }
  return excelRes.then(res => {
    savePdfFile(res, `${params.name}.pdf`)
  })
}

const saveZipFile = (res, filename) => {
  if (checkError(res)) {
    return
  }
  const headName = getHttpHeaderFileName(res.headers)
  const blob = new Blob([res.data], {
    type: 'application/zip'
  })
  blobSaveFile(blob, headName ? headName : filename)
}

export const zipExport = (url, params, method='post') => {
  let excelRes
  if (method === 'get') {
    excelRes = Vue.http.get(url, {
      responseType: 'blob'
    })
  }
  if (method === 'post') {
    excelRes = Vue.http.post(url, params, {
      responseType: 'blob'
    })
  }
  return excelRes.then(res => {
    saveZipFile(res, `${params.name}.zip`)
  })
}

const checkError = (res) => {
  if (res.data.type.includes('application/json')) {
    let reader = new FileReader()
    reader.onload = function () {
      let content = reader.result
      let message = JSON.parse(content).message || '导出错误'
      Vue.prototype.$Message.error(message)
    }
    reader.readAsText(res.data)
    return true
  }
  return false
}
