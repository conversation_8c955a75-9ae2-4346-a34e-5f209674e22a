import store from './store'
import router from './router'
import Xsc from 'vue-draw-xs'
import { namespace } from '@/project'
import 'xdo-report/dist/xdo-report.css'

window.majesty && window.majesty.util.registerApp(namespace, {router, store}, ({ globalStore }) => {
  globalStore.dispatch(`${namespace}/loadClearanceBusinessSetting`)
  window.majesty.Vue.llk.subscribe(namespace, 'setMintenanceCompanySuccess', () => {
    globalStore.dispatch(`${namespace}/loadClearanceBusinessSetting`)
  })
  window.majesty.Vue.use(Xsc)
  // 选择手账册
  window.majesty.Vue.llk.subscribe(namespace, 'icsCsOpenChooseNo', () => {
    window.majesty.Vue.prototype.$Modal.info({
      width: 800,
      okText: '关闭',
      closable: true,
      title: '手账册选择',
      render: (h) => {
        return h('dc-manual-account-select', {}, [])
      }
    })
  })
})

import dcDateRange from './components/dc-date-range'
window.majesty.Vue.use(dcDateRange)

import dcAgGrid from './components/dc-ag-grid'
window.majesty.Vue.use(dcAgGrid)

import dynamic from './components/dynamic'
window.majesty.Vue.use(dynamic)

import numberInput from './components/dc-number-input'
window.majesty.Vue.use(numberInput)

import loadingPop from './components/dc-loading-pop'
window.majesty.Vue.use(loadingPop)

import dcFormItem from './components/dc-form-item'
window.majesty.Vue.use(dcFormItem)

import dcMergeGrid from './components/dc-merge-grid'
window.majesty.Vue.use(dcMergeGrid)

import InnerMessage from './components/inner-message'
window.majesty.Vue.use(InnerMessage)

import ManualAccountSelect from './components/dc-manual-account-select'
window.majesty.Vue.use(ManualAccountSelect)

import DcFilePreview from './components/dc-file-preview'
window.majesty.Vue.use(DcFilePreview)

import dcSingleAttachment from './components/dc-single-attachment'
window.majesty.Vue.use(dcSingleAttachment)

import GridTitleSort  from '@/common/gridTitleSort'
window.majesty.Vue.use(GridTitleSort)
