import { baseConfig } from './baseConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

export const baseSearchConfig = {
  name: 'baseSearchConfig',
  mixins: [baseConfig],
  data() {
    return {
      // 是否显示查询条件
      showSearch: false,
      baseParams: [],
      searchConfig: {
        fields: [],
        model: {},
        rules: {},
        frmColumns: 3
      }
    }
  },
  watch: {
    baseParams: {
      deep: true,
      immediate: true,
      handler: function (params) {
        let me = this,
          fieldsObject = me.fieldsAnalysis(params)
        me.$set(me.searchConfig, 'model', fieldsObject.model)
        me.$set(me.searchConfig, 'rules', fieldsObject.rules)
        me.$set(me.searchConfig, 'fields', fieldsObject.fields)
      }
    }
  },
  methods: {
    /**
     * 显示/隐藏查询条件
     */
    handleShowSearch() {
      this.showSearch = !this.showSearch
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 查询条件数据源重置
     * @param key
     */
    searchFieldsReLoad(key) {
      let me = this,
        field = me.searchConfig.fields.find(p => p.key === key)
      if (field) {
        me.fieldOptimization(field)
      }
    }
  },
  computed: {
    searchLines() {
      return Math.ceil(this.searchConfig.fields.length / this.searchConfig.frmColumns)
    },
    extendParams() {
      return {}
    }
  }
}
