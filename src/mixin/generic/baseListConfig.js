import pms from '@/libs/pms'
import { excelExport } from '@/api'
import { baseConfig } from './baseConfig'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const baseListConfig = {
  name: 'baseListConfig',
  mixins: [pms, baseConfig, baseColumns],
  data() {
    return {
      showList: true,     // 查询界面是否显示(用于切换查询界面与详细界面)
      actions: [],        // 操作按钮
      listLoading: false,
      defaultFields: [],
      listConfig: {
        data: [],
        columns: [],
        selectRows: [],
        selection: false,
        colOptions: false,
        settingColumns: [],
        exportTitle: '导出文件名称'
      },
      autoCreate: true,
      // 分页相关
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1
      },
      pageSizeOpts: [10, 20, 50, 100],
      // 传入编辑界面的信息
      editConfig: {
        headId: '',
        editData: {},
        editStatus: editStatus.SHOW
      },
      // 是否有按钮行
      hasActions: true,
      // 是否有分页行
      hasPager: true,
      // 是否有子Tab组件
      hasChildTabs: false,
      // (列表高度)偏移值
      OffsetHeight: 0,
      // 初始化即查询
      initSearch: true,
      ajaxUrl: {
        selectAllPaged: ''
      },
      listId: '',
      listSetupShow: false,
      pmsLevel: 'default',
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      }
    }
  },
  created: function () {
    let me = this
    if (me.autoCreate) {
      me.loadCmbSource()
      me.loadListConfig()
    }
  },
  mounted: function () {
    let me = this
    if (me.initSearch) {
      me.$nextTick(() => {
        if (typeof me.beforeFirstSearch === 'function') {
          me.beforeFirstSearch.call(me)
        }
        me.handleSearchSubmit()
      })
    }
    me.loadFunctions(me.pmsLevel).then(() => {
      if (typeof me.actionLoaded === 'function') {
        me.actionLoaded()
      }
    })
  },
  watch: {
    baseFields: {
      deep: true,
      immediate: true,
      handler: function (fields) {
        let me = this
        if (Array.isArray(fields)) {
          let columns = []
          fields.forEach(field => {
            columns.push(me.columnOptimization(field))
          })
          me.$set(me.listConfig, 'settingColumns', columns)
          me.handleUpdateColumn(columns)
        }
      }
    },
    listLoading: {
      handler: function (loading) {
        let me = this
        if (loading) {
          if (me.$refs['table'] && typeof me.$refs['table'].showLoadingOverlay === 'function') {
            me.$refs['table'].showLoadingOverlay()
          }
        } else {
          if (me.$refs['table']) {
            if (Array.isArray(me.listConfig.data) && me.listConfig.data.length > 0) {
              if (me.$refs['table'] && typeof me.$refs['table'].hideOverlay === 'function') {
                me.$refs['table'].hideOverlay()
              }
            } else {
              if (me.$refs['table'] && typeof me.$refs['table'].showNoRowsOverlay === 'function') {
                me.$refs['table'].showNoRowsOverlay()
              }
            }
          }
        }
      }
    }
  },
  methods: {
    /**
     * 首次查询之前的事件
     */
    beforeFirstSearch() {
    },
    /**
     * 加载额外的数据源
     */
    loadCmbSource() {
    },
    /**
     * 加载数据列信息
     */
    loadListConfig() {
      let me = this,
        showColumns = [],
        rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/' + me.pmsLevel + '/listId')
      if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
        showColumns = me.$bom3.showTableColumns(me.listId, me.listConfig.settingColumns, me.defaultFields)
      } else {
        showColumns = me.$bom3.showTableColumns(me.listId, me.listConfig.settingColumns)
      }
      me.handleUpdateColumn(showColumns)
    },
    /**
     * 设置显示罗列
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      if (me.listConfig.colOptions) {
        me.$set(me.listConfig, 'columns', [...me.getDefaultColumns(), ...columns])
      } else if (me.listConfig.selection) {
        me.$set(me.listConfig, 'columns', [{
          width: 36,
          fixed: 'left',
          key: 'selection',
          type: 'selection'
        }, ...columns])
      } else {
        me.$set(me.listConfig, 'columns', columns)
      }
      me.listSetupShow = false
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      // console.info('查询成功后执行的操作')
    },
    /**
     * 查询失败后执行的操作
     */
    afterSearchFailure() {
      // console.info('查询失败后执行的操作')
    },
    /**
     * 查询执行完成后的默认操作
     */
    afterSearch() {
      // console.info('查询执行完成后的默认操作')
    },
    /**
     * 执行查询
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.listLoading = true
        let params = {}
        if (typeof me.getSearchParams === 'function') {
          params = me.getSearchParams()
        }
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.listConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.listConfig.selectRows = []
          me.afterSearch()
          me.listLoading = false
        })
      })
    },
    /**
     * 执行数据查询
     */
    getList() {
      let me = this
      if (isNullOrEmpty(me.ajaxUrl.selectAllPaged)) {
        console.error('查询api不能为空!')
        return
      }
      me.doSearch(me.ajaxUrl.selectAllPaged)
    },
    /**
     * 点击查询按钮
     */
    handleSearchSubmit() {
      let me = this
      me.pageParam.page = 1
      me.getList()
    },
    /**
     * 行选中或取消选中
     * @param selectRows
     */
    handleSelectionChange(selectRows) {
      let me = this
      me.listConfig.selectRows = selectRows
    },
    /**
     * 页次切换
     * @param page
     */
    pageChange(page) {
      let me = this
      me.pageParam.page = page
      me.getList()
    },
    /**
     * 切换每页记录数
     * @param pageSize
     */
    pageSizeChange(pageSize) {
      let me = this
      me.pageParam.limit = pageSize
      if (me.pageParam.page === 1) {
        me.getList()
      }
    },
    /**
     * 自定义key-value列展示规则
     * @param h
     * @param params
     * @param keyField
     * @param valueField
     * @returns {*}
     */
    keyValueRender(h, params, keyField, valueField) {
      let me = this,
        showVal = '',
        keyVal = params.row[keyField],
        valueVal = params.row[valueField]

      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return me.toolTipRender(h, showVal.trim())
    },
    /**
     * 获取选中数据的参数组(可外部覆盖)
     */
    getSelectedParams() {
      let me = this
      return me.listConfig.selectRows.map(item => {
        return item.sid
      })
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      me.showList = false
      me.editConfig.editData = {}
      me.listConfig.selectRows = []
      me.editConfig.editStatus = editStatus.ADD
    },
    /**
     * 点击编辑按钮执行
     */
    handleEdit() {
      let me = this
      if (me.checkRowSelected('编辑', true)) {
        me.handleEditByRow(me.listConfig.selectRows[0])
      }
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.customCheck([row], '编辑')) {
        me.showList = false
        me.editConfig.editData = row
        me.editConfig.editStatus = editStatus.EDIT
      }
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      let me = this
      me.showList = false
      me.editConfig.editData = row
      me.editConfig.editStatus = editStatus.SHOW
    },
    /**
     * 設置按鈕是否進入加載狀態
     * @param indexOrKey
     * @param flag
     */
    setButtonLoading(indexOrKey, flag) {
      let me = this
      if (typeof indexOrKey === 'number' && indexOrKey > -1 && indexOrKey < me.actions.length) {
        me.actions[indexOrKey].loading = flag
      } else if (typeof indexOrKey === 'string' && !isNullOrEmpty(indexOrKey)) {
        if (typeof me.setToolbarLoading === 'function') {
          me.setToolbarLoading(indexOrKey, flag)
        }
      }
    },
    /**
     * 执行导出
     * @param exportUrl
     * @param btnIndexOrKey
     * @param finallyFun
     */
    doExport(exportUrl, btnIndexOrKey, finallyFun) {
      let me = this
      me.$nextTick(() => {
        me.setButtonLoading(btnIndexOrKey, true)
        let params = {}
        if (typeof me.getSearchParams === 'function') {
          params = me.getSearchParams()
        }
        excelExport(exportUrl, {
          exportColumns: params,
          header: me.exportHeader,
          name: me.listConfig.exportTitle
        }).finally(() => {
          me.setButtonLoading(btnIndexOrKey, false)
          if (typeof finallyFun === 'function') {
            finallyFun.call(me)
          }
        })
      })
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnIndexOrKey
     */
    doDelete(delUrl, btnIndexOrKey) {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.listConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              me.setButtonLoading(btnIndexOrKey, true)
              let params = me.getSelectedParams()
              me.$http.delete(`${delUrl}/${params}`).then(() => {
                me.$Message.success('删除成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading(btnIndexOrKey, false)
              })
            }
          })
        }
      }
    },
    /**
     * 是否存在被选中的行
     * @param opTitle (操作标签)
     * @param isSingle (是否有且仅有一条)
     */
    checkRowSelected(opTitle, isSingle) {
      let me = this
      if (isSingle !== true) {
        isSingle = false
      }
      if (isNullOrEmpty(opTitle)) {
        opTitle = '操作'
      }
      if (me.listConfig.selectRows.length === 0) {
        me.$Message.warning('未选择数据, 请选择您要' + opTitle + '的数据!')
        return false
      } else if (isSingle === true && me.listConfig.selectRows.length > 1) {
        me.$Message.warning('一次仅能' + opTitle + '一条数据!')
        return false
      } else {
        return true
      }
    },
    /**
     * 自定义编辑、删除检查(可外部覆盖)
     * @param selRows 选中的行数组
     * @param opTitle (操作标签)
     * @returns {boolean}
     */
    customCheck(selRows, opTitle) {
      console.info('执行了自定义【' + opTitle + '】检查')
      return true
    },
    /**
     * 設置列表中編輯按鈕是否顯示
     * @returns {string}
     */
    operationEditShow() {
      return ''
    },
    /**
     * 用于AgGrid单元格
     * @param options
     */
    onAgCellOperation(options) {
      let me = this
      if (options.methods === 'handleEditByRow') {
        me.handleEditByRow(options.params)
      } else if (options.methods === 'handleViewByRow') {
        me.handleViewByRow(options.params)
      }
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.showList = backObj.showList
      if (me.showList) {
        me.getList()
      }
      me.editConfig.editData = backObj.editData
      me.editConfig.editStatus = backObj.editStatus
    },
    /**
     * 返回列表
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    }
  },
  computed: {
    /**
     * 导出列头信息
     * @returns {{value: *, key: *}[]}
     */
    exportHeader() {
      return this.listConfig.columns.filter(column => {
        return !['selection', 'operation'].includes(column.key) && !isNullOrEmpty(column.key)
      }).map(theCol => {
        return {
          key: theCol.key,
          value: theCol.title
        }
      })
    },
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight() {
      let me = this
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 0
      if (me.hasChildTabs) {
        childTabHeight = 38
      }
      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight
      // 去除按鈕行高度: 28px(需要添加2px: 与上下之間的空隙)
      if (me.hasActions) {
        hiddenHeight = hiddenHeight - 30
      }
      // 去除分頁信息行高度: 28px
      if (me.hasPager) {
        hiddenHeight = hiddenHeight - 28
      }
      // 減去偏移量(某些特殊情況使用)
      hiddenHeight = hiddenHeight - this.OffsetHeight - 2
      // 當查詢條件塊打開時候去除其高度
      if (me.showSearch === true) {
        // 與上方間隙高度
        let gapHeight = 6
        // 分行高度
        let lineHeight = 28
        return hiddenHeight - gapHeight - lineHeight * this.searchLines
      }
      return hiddenHeight
    }
  }
}
