import { editStatus } from '@/view/cs-common'

export const baseTabsConfig = {
  name: 'baseTabsConfig',
  props: {
    cmbSource: {
      type: Object,
      default: () => ({})
    },
    editConfig: {
      type: Object,
      default: () => ({
        headId: '',
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (value) {
        let me = this
        me.tabs[value] = true
        if (value === 'headTab') {
          me.$nextTick(() => {
            if (me.$refs.head) {
              console.info(value)
            }
          })
        } else if (value === 'bodyTab') {
          me.$nextTick(() => {
            if (me.$refs.body) {
              console.info(value)
            }
          })
        }
      }
    }
  },
  computed: {
    showBody() {
      return this.editConfig.editStatus !== editStatus.ADD
    },
    parentConfig() {
      let me = this
      return {
        headData: me.editConfig.editData,
        headId: me.editConfig.editData.sid,
        editStatus: me.editConfig.editStatus
      }
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this,
        result = {
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        }
      me.editBack(result)
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  }
}
