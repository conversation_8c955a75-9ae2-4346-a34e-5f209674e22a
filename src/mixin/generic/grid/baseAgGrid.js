import { addEvent, isNullOrEmpty } from '@/libs/util'
import { cell<PERSON>enderer } from '@/components/dc-ag-grid/js/dc-ag-cell-render'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

function OpCellRenderer() {
}

OpCellRenderer.prototype.init = function(params) {
  let vm = undefined,
    editDisplay = '',
    vmParent = undefined,
    viewMarginLeft = '15px',
    divContainer = document.createElement('div'),
    fwComWrapper = params['frameworkComponentWrapper']
  if (fwComWrapper) {
    vmParent = fwComWrapper.parent
  }
  while (vmParent.$parent && typeof vmParent.$parent['cellEditStyle'] !== 'function') {
    vmParent = vmParent.$parent
  }
  if (vmParent.$parent) {
    vm = vmParent.$parent
  }
  if (vm && typeof vm.cellEditStyle === 'function') {
    editDisplay = vm.cellEditStyle()
    viewMarginLeft = vm.cellEditStyle() === '' ? '15px' : '20px'
  }

  let editA = document.createElement('a')
  editA.innerHTML = '编辑'
  if (vm && typeof vm.editCellConfig && !isNullOrEmpty(vm.editCellConfig.editTitle)) {
    editA.innerHTML = vm.getEditTitle('edit', params)
  }

  vm.setEditType(editA, 'edit', params)
  editA.style.display = editDisplay
  addEvent(editA, 'click', function () {
    if (vm && typeof vm.showEditByRow === 'function') {
      vm.showEditByRow(params.data)
    }
  })
  divContainer.appendChild(editA)

  let viewA = document.createElement('a')
  viewA.innerHTML = '查看'
  if (vm && typeof vm.editCellConfig && !isNullOrEmpty(vm.editCellConfig.showTitle)) {
    viewA.innerHTML = vm.getEditTitle('view', params)
  }

  vm.setEditType(viewA, 'view', params)
  viewA.style.marginLeft = viewMarginLeft
  addEvent(viewA, 'click', function () {
    if (vm && typeof vm.showViewByRow === 'function') {
      vm.showViewByRow(params.data)
    }
  })
  divContainer.appendChild(viewA)
  this.eGui = divContainer
}

OpCellRenderer.prototype.getGui = function() {
  return this.eGui
}

export const baseAgGrid = {
  name: 'baseAgGrid',
  mixins: [columnRender],
  data() {
    return {
      listId: '',                        // 用于保存显示列的key字段
      grdShow: true,
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1
      },
      grdName: 'table',                  // grid组件ref名称
      defaultFields: [],                 // 默认显示字段
      listLoading: false,                // 查询时是否显示loading
      pmsLevel: 'default',               // pms抓取按钮权限的key值
      editCellConfig: {                  // 编辑列配置信息
        editTitle: '编辑',
        showTitle: '查看'
      },
      listSetupShow: false,              // 自定义设置列窗口是否显示
      configAutoLoad: true,              // 自动加载配置字段
      rowSelection: 'multiple',          // 'single', 'multiple'
      listConfig: {                      // === grid相关参数 ===
        data: [],                        // grid数据
        columns: [],                     // 最终展示的列字段
        disable: false,                  // grid组件是否可编辑
        selectRows: [],                  // 当前选中的行数组
        settingColumns: [],              // 用于自定义设置的所有字段
        checkColumnShow: true,           // 显示选择列
        exportTitle: '导出文件名称',       // 导出文件名
        operationColumnShow: true,       // 显示操作列
      },
      pageSizeOpts: [10, 20, 50, 100],
      components: {
        operateCellRenderer: OpCellRenderer
      },
      overlayNoRowsTemplate: '<span>未查询到数据</span>',
      overlayLoadingTemplate: '<i class="ivu-icon ivu-icon-ios-loading" style="font-size: 32px; color: darkgray"></i>'
    }
  },
  created: function () {
    let me = this
    if (me.configAutoLoad) {
      me.loadCmbSource()
      me.loadListConfig()
    }
  },
  computed: {
    /**
     * 操作列宽度
     * @returns {number}
     */
    operateColWidth() {
      let me = this,
        opColWidth = 88
      if (me.listConfig.checkColumnShow) {
        opColWidth = 88 + 28
      }
      return opColWidth
    },
    /**
     * 导出列头信息
     * @returns {{value: *, key: *}[]}
     */
    exportHeader() {
      let me = this
      return me.listConfig.columns.filter(column => {
        return !['selection', 'operation'].includes(column.key)
      }).map(theCol => {
        return {
          key: theCol.key,
          value: theCol.title
        }
      })
    }
  },
  watch: {
    listLoading: {
      handler: function (loading) {
        let me = this
        if (loading) {
          me.showLoadingOverlay()
        } else {
          if (Array.isArray(me.listConfig.data) && me.listConfig.data.length > 0) {
            me.hideOverlay()
          } else {
            me.showNoRowsOverlay()
          }
        }
      }
    }
  },
  methods: {
    /**
     * 加载额外的数据源
     */
    loadCmbSource() {
    },
    /**
     * 获取设置key
     */
    getListId() {
      let me = this,
        rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/' + me.pmsLevel + '/listId')
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      // let me = this
      // me.$set(me.listConfig, 'settingColumns', me.totalColumns)
    },
    /**
     * 加载数据列信息
     */
    loadListConfig() {
      let me = this,
        showColumns = []
      me.getListId()
      me.setConfigColumns()
      if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
        showColumns = me.$bom3.showTableColumns(me.listId, me.listConfig.settingColumns, me.defaultFields)
      } else {
        showColumns = me.$bom3.showTableColumns(me.listId, me.listConfig.settingColumns)
      }
      me.handleUpdateColumn(showColumns)
    },
    /**
     * 获取操作列
     */
    getOperationColumn() {
      let me = this
      if (me.listConfig.operationColumnShow) {
        return [{
          title: '操作',
          fixed: 'left',
          key: 'operation',
          resizable: false,
          width: me.operateColWidth,
          cellRenderer: 'operateCellRenderer'
        }]
      }
      return []
    },
    /**
     * 设置显示列
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'grdShow', false)
      me.$set(me.listConfig, 'columns', [...me.getOperationColumn(), ...columns])
      me.$set(me, 'listSetupShow', false)
      me.$nextTick(() => {
        me.$set(me, 'grdShow', true)
      })
    },
    /**
     * 基础渲染方法
     * @param funRender
     * @param tooltip
     */
    baseCellRenderer(funRender, tooltip = false) {
      let me = this
      if (typeof funRender !== 'function') {
        if (tooltip) {
          funRender = (h, params) => {
            return me.toolTipRender(h, params.row[params.column.key])
          }
        } else {
          funRender = (h, params) => {
            return h('span', params.row[params.column.key])
          }
        }
      }
      return cellRenderer(me, funRender, tooltip)
    },
    /**
     * 按钮样式
     */
    cellEditStyle() {
      let me = this
      if (me.listConfig.disable) {
        return 'none'
      }
      return ''
    },
    /**
     * 显示编辑界面
     * @param row
     */
    showEditByRow(row) {
      let me = this
      if (typeof me.handleEditByRow === 'function') {
        me.handleEditByRow.call(me, row)
      }
    },
    /**
     * 显示查看界面
     * @param row
     */
    showViewByRow(row) {
      let me = this
      if (typeof me.handleViewByRow === 'function') {
        me.handleViewByRow.call(me, row)
      }
    },
    /**
     * 单元格双击事件
     * @param e
     */
    cellDoubleClicked: function (e) {
      let me = this
      if (typeof me.onCellDoubleClicked === 'function') {
        return me.onCellDoubleClicked.call(me, e.data)
      }
    },
    /**
     * 获取agGrid的api对象
     * @returns {null|*}
     */
    getAgGrdApi() {
      let me = this
      if (me.$refs[me.grdName]) {
        if (me.$refs[me.grdName].gridOptions) {
          return me.$refs[me.grdName].gridOptions.api
        }
      }
      return null
    },
    /**
     * 显示正在加载
     */
    showLoadingOverlay() {
      let me = this,
        api = me.getAgGrdApi()
      if (api && typeof api.showLoadingOverlay === 'function') {
        api.showLoadingOverlay()
      }
    },
    /**
     * 显示没有数据
     */
    showNoRowsOverlay() {
      let me = this,
        api = me.getAgGrdApi()
      if (api && typeof api.showNoRowsOverlay === 'function') {
        api.showNoRowsOverlay()
      }
    },
    /**
     * 隐藏以上两者
     */
    hideOverlay() {
      let me = this,
        api = me.getAgGrdApi()
      if (api && typeof api.hideOverlay === 'function') {
        api.hideOverlay()
      }
    },
    /**
     * 行选中或取消选中
     * @param agGrd
     */
    handleSelectionChange(agGrd) {
      let me = this
      if (Array.isArray(agGrd)) {
        me.$set(me.listConfig, 'selectRows', agGrd)
      } else {
        me.$set(me.listConfig, 'selectRows', agGrd.api.getSelectedRows())
      }
    },
    /**
     * 页次切换
     * @param page
     */
    pageChange(page) {
      let me = this
      me.$set(me.pageParam, 'page', page)
      me.getList()
    },
    /**
     * 切换每页记录数
     * @param pageSize
     */
    pageSizeChange(pageSize) {
      let me = this
      me.$set(me.pageParam, 'limit', pageSize)
      if (me.pageParam.page === 1) {
        me.getList()
      }
    },
    /**
     * 获取编辑行按钮样式类型
     * @param cellA
     * @param btnType
     * @param params
     */
    setEditType(cellA, btnType, params) {
      console.info(params)
      if (btnType === 'edit') {
        cellA.setAttribute('type', 'primary')
      } else {
        cellA.setAttribute('type', 'primary')
      }
    },
    /**
     * 获取编辑行标签
     * @param btnType
     * @param params
     */
    getEditTitle(btnType, params) {
      let me = this
      console.info(params)
      if (btnType === 'edit') {
        return me.editCellConfig.editTitle
      } else {
        return me.editCellConfig.showTitle
      }
    }
  }
}
