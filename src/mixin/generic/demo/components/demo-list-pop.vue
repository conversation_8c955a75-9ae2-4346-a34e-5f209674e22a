<template>
  <XdoModal width="960" mask v-model="show" :title="title"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
        </DynamicForm>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid :columns="listConfig.columns" :data="listConfig.data" height="400"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

  export default {
    name: 'demoListPop',
    mixins: [baseSearchConfig, baseListConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      title: {
        type: String,
        default: () => ('')
      },
      options: {
        type: Object,
        default: () => ({
          headId: '',
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      let params = this.getParams()
      let fields = this.getFields()
      return {
        baseParams: [
          ...params
        ],
        baseFields: [
          ...fields
        ],
        cmbSource: {},
        pmsLevel: 'pop',
        autoCreate: false,
        initSearch: false,
        toolbarEventMap: {},
        ajaxUrl: {
          selectAllPaged: csAPI.csMaterielCenter.singleLoss.body.distinctExgList,
          setConsumeDclStat: csAPI.csMaterielCenter.singleLoss.body.setConsumeDclStat
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              me.handleSearchSubmit()
            })
          }
        }
      }
    },
    computed: {
      extendParams() {
        return {
          headId: this.options.headId
        }
      }
    },
    methods: {
      actionLoaded() {
        // let me = this
      },
      getParams() {
        return []
      },
      getFields() {
        return []
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-body {
    padding: 0 4px 4px 4px;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
