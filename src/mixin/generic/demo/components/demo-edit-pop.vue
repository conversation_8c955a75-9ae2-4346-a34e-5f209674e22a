<template>
  <XdoModal width="960" mask v-model="show" :title="title"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '../../baseDetailConfig'

  export default {
    name: 'demoEditPop',
    mixins: [baseDetailConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      title: {
        type: String,
        default: () => ('')
      },
      options: {
        type: Object,
        default: () => ({
          headId: '',
          dataSource: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csMaterielCenter.singleLoss.body.insert,
          update: csAPI.csMaterielCenter.singleLoss.body.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '关闭', type: 'primary', command: 'cancel', click: this.handleClose}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function (disable) {
          let me = this
          me.fieldsReset()
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !disable
        }
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {
          ...this.options.dataSource
        }
      }
    },
    methods: {
      getFields() {
        return []
      },
      handleSave() {
        let me = this
        me.doSave(() => {
          me.handleClose()
        })
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
