<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <HeadEdit ref="head" :edit-config="editConfig" :in-source="cmbSource"
                  @onEditBack="editBack"></HeadEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体">
        <BodyList ref="body" :parent-config="parentConfig"></BodyList>
      </TabPane>
      <template v-slot:extra>
        <Button type="text" @click="backToList">
          <XdoIcon type="ios-undo" size="22" style="color: green;" />
        </Button>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import HeadEdit from './demo-head-edit'
  import BodyList from './demo-body-list'
  import { baseTabsConfig } from '../baseTabsConfig'

  export default {
    name: 'demoTabs',
    mixins: [baseTabsConfig],
    components: {
      HeadEdit,
      BodyList
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
