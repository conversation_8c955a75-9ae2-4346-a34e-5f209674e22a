import DemoTabs from '../demo-tabs'
import { baseListConfig } from '../../baseListConfig'
import { baseSearchConfig } from '../../baseSearchConfig'

export const demoHeadList = {
  name: 'demoHeadList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    DemoTabs
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      cmbSource: {},
      listConfig: {
        colOptions: true
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete
      }
    }
  },
  computed: {
    extendParams() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      // let me = this
    },
    loadCmbSource() {
      // let me = this
    },
    getParams() {
      return []
    },
    getFields() {
      return []
    },
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    }
  }
}
