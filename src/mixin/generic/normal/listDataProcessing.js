import pms from '@/libs/pms'
import { excelExport } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { baseAgGrid } from '@/mixin/generic/grid/baseAgGrid'

export const listDataProcessing = {
  name: 'listDataProcessing',
  mixins: [pms, baseAgGrid],
  data() {
    return {
      ajaxUrl: {            // 查询链接
        selectAllPaged: ''
      },
      actions: [],          // 操作按钮
      showList: true,       // 查询界面是否显示(用于切换查询界面与详细界面)
      hasPager: true,       // 是否有分页行
      OffsetHeight: 0,      // (列表高度)偏移值
      initSearch: true,     // 初始化即查询
      hasActions: true,     // 是否有按钮行
      showSearch: false,    // 是否显示查询条件
      hasChildTabs: false,  // 是否有子Tab组件
      pmsLevel: 'default',  // pms抓取按钮权限的key值
      editConfig: {         // 传入编辑界面的信息
        headId: '',
        editData: {},
        editStatus: editStatus.SHOW
      }
    }
  },
  mounted: function () {
    let me = this
    me.$set(me, 'showSearch', false)
    if (me.initSearch === true) {
      me.$nextTick(() => {
        if (typeof me.beforeFirstSearch === 'function') {
          me.beforeFirstSearch.call(me)
        }
        me.handleSearchSubmit()
      })
    }
    me.loadFunctions(me.pmsLevel).then(() => {
      if (typeof me.actionLoaded === 'function') {
        me.actionLoaded()
      }
    })
  },
  methods: {
    /**
     * 显示/隐藏查询条件
     */
    handleShowSearch() {
      let me = this
      me.$set(me, 'showSearch', !me.showSearch)
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this
      if (me.$refs.headSearch) {
        return me.$refs.headSearch.searchParam
      }
      return {}
    },
    /**
     * 首次执行查询之前的操作
     */
    beforeFirstSearch() {
      console.info('首次执行查询之前的操作')
    },
    /**
     * 点击查询按钮
     */
    handleSearchSubmit() {
      let me = this
      me.$set(me.pageParam, 'page', 1)
      me.getList()
    },
    /**
     * 执行数据查询
     */
    getList() {
      let me = this
      if (isNullOrEmpty(me.ajaxUrl.selectAllPaged)) {
        console.error('查询api不能为空!')
        return
      }
      me.doSearch(me.ajaxUrl.selectAllPaged)
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      console.info('查询成功后执行的操作')
    },
    /**
     * 查询失败后执行的操作
     */
    afterSearchFailure() {
      console.info('查询失败后执行的操作')
    },
    /**
     * 查询执行完成后的默认操作
     */
    afterSearch() {
      console.info('查询执行完成后的默认操作')
    },
    /**
     * 执行查询
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.$set(me, 'listLoading', true)
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.$set(me.listConfig, 'data', res.data.data)
          me.$set(me.pageParam, 'page', res.data.pageIndex)
          me.$set(me.pageParam, 'dataTotal', res.data.total)
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.$set(me.listConfig, 'selectRows', [])
          me.afterSearch()
          me.$set(me, 'listLoading', false)
        })
      })
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      me.$set(me.editConfig, 'editData', {})
      me.$set(me.listConfig, 'selectRows', [])
      me.$set(me.editConfig, 'editStatus', editStatus.ADD)
      me.$set(me, 'showList', false)
    },
    /**
     * 是否存在被选中的行
     * @param opTitle (操作标签)
     * @param isSingle (是否有且仅有一条)
     */
    checkRowSelected(opTitle, isSingle) {
      let me = this
      if (isSingle !== true) {
        isSingle = false
      }
      if (isNullOrEmpty(opTitle)) {
        opTitle = '操作'
      }
      if (me.listConfig.selectRows.length === 0) {
        me.$Message.warning('未选择数据, 请选择您要' + opTitle + '的数据!')
        return false
      } else if (isSingle === true && me.listConfig.selectRows.length > 1) {
        me.$Message.warning('一次仅能' + opTitle + '一条数据!')
        return false
      } else {
        return true
      }
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param row
     * @param opTitle
     */
    extendEditCheck(row, opTitle) {
      console.info('执行了自定义【' + opTitle + '】检查')
      return true
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck(row, '编辑')) {
        me.$set(me.editConfig, 'editData', row)
        me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        me.$set(me, 'showList', false)
      }
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      let me = this
      me.$set(me.editConfig, 'editData', row)
      me.$set(me.editConfig, 'editStatus', editStatus.SHOW)
      me.$set(me, 'showList', false)
    },
    /**
     * 点击编辑按钮执行
     */
    handleEdit() {
      let me = this
      if (me.checkRowSelected('编辑', true)) {
        me.handleEditByRow(me.listConfig.selectRows[0])
      }
    },
    /**
     * 执行导出
     * @param exportUrl
     * @param btnKey
     * @param finallyFun
     */
    doExport(exportUrl, btnKey, finallyFun) {
      let me = this
      me.$nextTick(() => {
        me.setToolbarLoading(btnKey, true)
        let params = me.getSearchParams()
        excelExport(exportUrl, {
          exportColumns: params,
          header: me.exportHeader,
          name: me.listConfig.exportTitle
        }).finally(() => {
          me.setToolbarLoading(btnKey)
          if (typeof finallyFun === 'function') {
            finallyFun.call(me)
          }
        })
      })
    },
    /**
     * 获取选中数据的参数组(可外部覆盖)
     */
    getSelectedParams() {
      let me = this
      return me.listConfig.selectRows.map(item => {
        return item.sid
      })
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param rows
     * @param opTitle
     */
    extendDeleteCheck(rows, opTitle) {
      console.info('执行了自定义【' + opTitle + '】检查')
      return true
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnKey
     * @param delSuccessFun
     */
    doDelete(delUrl, btnKey, delSuccessFun) {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.extendDeleteCheck(me.listConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              me.setToolbarLoading(btnKey, true)
              let params = me.getSelectedParams()
              me.$http.delete(`${delUrl}/${params}`).then(() => {
                if (typeof delSuccessFun === 'function') {
                  delSuccessFun.call()
                } else {
                  me.handleSearchSubmit()
                }
                me.$Message.success('删除成功!')
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading(btnKey)
              })
            }
          })
        }
      }
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$set(me, 'showList', backObj.showList)
      if (me.showList) {
        me.getList()
      }
      me.$set(me.editConfig, 'editData', backObj.editData)
      me.$set(me.editConfig, 'editStatus', backObj.editStatus)
    },
    /**
     * 返回列表
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    }
  },
  computed: {
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight() {
      let me = this
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 0
      if (me.hasChildTabs) {
        childTabHeight = 38
      }
      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight
      // 去除按鈕行高度: 28px(需要添加2px: 与上下之間的空隙)
      if (me.hasActions) {
        hiddenHeight = hiddenHeight - 30
      }
      // 去除分頁信息行高度: 28px
      if (me.hasPager) {
        hiddenHeight = hiddenHeight - 28
      }
      // 減去偏移量(某些特殊情況使用)
      hiddenHeight = hiddenHeight - me.OffsetHeight - 2
      // 當查詢條件塊打開時候去除其高度
      if (me.showSearch === true) {
        // 與上方間隙高度
        let gapHeight = 6
        // 分行高度
        let lineHeight = 28
        return hiddenHeight - gapHeight - lineHeight * me.searchLines
      }
      return hiddenHeight
    }
  }
}
