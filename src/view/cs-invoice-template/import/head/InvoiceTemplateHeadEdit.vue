<template>
  <section>
    <XdoCard :bordered="false">
      <div class="xdo-enter-root" v-focus>
        <XdoForm ref="headParam" :model="addParams.headParam" class="dc-form dc-form-3" :rules="rulesHeader" label-position="right" :label-width="100" inline>
          <XdoFormItem prop="status" label="状态">
            <xdo-select :options="inDataSource.invoiceStatus" v-model="addParams.headParam.status"
                        :optionLabelRender="pcodeRender" :disabled="isStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="documentType" label="单证属性">
            <xdo-select :options="inDataSource.documnetType" v-model="addParams.headParam.documentType"
                        :optionLabelRender="pcodeRender" :disabled="isText" @on-change="dynamicStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="templateCode" label="模板编号">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.templateCode" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="custumerCode" label="供应商代码">
            <xdo-select v-model="addParams.headParam.customerCode" clearable :options="this.compData" :optionLabelRender="pcodeRender" :disabled="isSelect"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="运输方式显示">
            <XdoIInput type="text" v-model="addParams.headParam.trafModeName" :disabled="isText" :maxlength="15"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="币制显示">
            <XdoIInput type="text" v-model="addParams.headParam.currName" :disabled="isText" :maxlength="10"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="inserttime" label="录入时间">
            <XdoIInput type="text" v-model="addParams.headParam.insertTime" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="linkname" label="联络人">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.linkmanName" :maxlength="30"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="arrival" label="目的地">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.destination" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="destPort" label="目的港">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.destPort" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.note" :maxlength="100"></XdoIInput>
          </XdoFormItem>
        </XdoForm>
      </div>
    </XdoCard>
    <XdoCard :bordered="false">
      <span>模版:</span>
      <XdoTable class="dc-table" v-if="gridConfig.show" ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" stripe border>
        <template slot-scope="{index}" slot="templateCode">
          <XdoIInput :id="[index]" type="text" v-model="gridConfig.data[index].templateCode" :disabled="check" :maxlength="50"></XdoIInput>
        </template>
        <template slot-scope="{index}"  slot="note">
          <XdoIInput type="text" v-model="gridConfig.data[index].note" :disabled="check" :maxlength="200"></XdoIInput>
        </template>
        <template slot-scope="{index}" slot="documentType">
          <Select v-model="gridConfig.data[index].documentType" :disabled="check">
            <Option v-for="item in inDataSource.templateType" :value="item.value" :key="item.value">{{item.label}}</Option>
          </Select>
        </template>
        <template slot-scope="{index}" slot="pagingConfiguration">
          <dc-numberInput v-if="pageConfigure" v-model="gridConfig.data[index].pagingConfiguration"
                          integerDigits="3" precision="0" :disabled="check"></dc-numberInput>
          <span v-else>否</span>
        </template>
        <template slot-scope="{index}" slot="templateStyle">
          <ul>
            <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                    :show-upload-list="false" :format="['xls','xlsx']" :on-format-error="handleFormatError" :on-success="handleOnSuccess" :disabled="check">
              <strong>
                <a :class="typeNoInfor === 2 ? 'upLoad':''" icon="md-cloud-upload" long @click.prevent="upLoad(index)">
                  <XdoIcon type="ios-loop-strong"></XdoIcon>
                  [上传]
                </a>
              </strong>
            </Upload>
            <li v-for="(item,num) in gridConfig.data[index].templateStyle" :key="num">
              <span>
                <XdoIcon title="删除" type="md-close" @click.prevent="deleteTemplate(item.sid)" v-show="buttonShow"/>
                <a @click.prevent="downloadFile(item.sid)">{{item.fileNameOrigin}}</a>
              </span>
            </li>
          </ul>
        </template>
      </XdoTable>
    </XdoCard>
    <div class="dc-container-center">
      <XdoButton class="btntool" type="warning" v-if="btnShow" @click="templateSave()">保存</XdoButton>&nbsp;
      <XdoButton class="btntool" type="warning" @click="saveClose" v-if="btnShow">保存并关闭</XdoButton>&nbsp;
      <XdoButton class="btntool"  type="primary" @click="closeAdd">关闭</XdoButton>
    </div>
  </section>
</template>

<script>
  import { invoiceManage } from '@/view/cs-common'
  import { invoiceTemplateEdit } from '@/view/cs-invoice-template/js/invoiceTemplateEdit'

  export default {
    name: 'InvoiceTemplateHeadEdit',
    mixins: [invoiceTemplateEdit],
    data() {
      return {
        addParams: {
          listParam: [],
          headParam: {
            iemark: 'I',
            customerCode: '',   // 适用客户代码
            destination: '',    // 目的地
            documentType: '',   // 单证属性
            insertTime: '',     // 录入时间
            linkmanName: '',    // 联络人
            note: '',           // 备注
            sid: '',            // 主键
            status: '',         // 状态
            templateCode: '',   // 模版编号
            templateType: '2',  // 模版类型
            trafModeName: '',   // 运输方式
            currName: '',       // 币制显示
            destPort: ''        // 目的港
          }
        },
        inDataSource: {
          templateType: invoiceManage.templateType.filter(it => it.value !== '4')
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  ul li {
    color: #999;
    list-style: none;
    margin-left: 15px;
    display: inline-block;
  }

  .upLoad {
    color: #dcdee2;
  }
</style>
