<template>
    <section>
      <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
        <XdoFormItem prop="status" label="状态">
          <xdo-select :options="inDataSource.invoiceStatus" v-model="searchParam.status"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="status" label="单证属性">
          <xdo-select :options="inDataSource.documnetType"  v-model="searchParam.documentType"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="templateNo" label="模板编号">
          <XdoIInput type="text" v-model="searchParam.templateCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="适用客户代码">
          <xdo-select v-model="searchParam.customerCode" clearable :options="inDataSource.custumerType"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="status" label="运输方式显示">
          <XdoIInput type="text" v-model="searchParam.trafModeName"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="币制显示">
          <XdoIInput type="text" v-model="searchParam.currName"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { invoiceManage } from '../../cs-common'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'InvoiceTemplateListSearch',
    data() {
      return {
        searchParam: {
          customerCode: "",//适用客户代码
          destination: "",
          documentType: "",//单证属性
          insertTime: "",
          linkmanName: "",
          note: "",
          sid: "",
          status: "",//状态
          templateCode: "",//模版编号
          templateType: "1",
          trafModeName: '',
          currName: ''//币制显示
          //trafMode: ""// 运输方式
        },
        inDataSource: {
          documnetType: invoiceManage.documnetType,
          invoiceStatus: invoiceManage.invoiceStatus
        }
      }
    },
    mounted() {
      this.custumerInfor()
    },
    methods: {
      //获取客户code
      custumerInfor() {
        this.$http.post(csAPI.invoiceTemplate.custumer.custumerConfig).then(res => {
          this.$set(this.inDataSource, 'custumerType', ArrayToLocaleLowerCase(res.data.data))
        }, () => {
        })
      }
    }
  }
</script>

<style scoped>
</style>
