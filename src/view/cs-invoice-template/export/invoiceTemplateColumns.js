import {  isNullOrEmpty } from '@/libs/util'

const columnsConfig = [
  'selection','operation','documentType','customerCode','templateCode','trafModeName','currName','status','insertTime','linkmanName','destination','note','destPort'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          key: 'operation'
        },
        {
          title: '单证属性',
          minWidth: 120,
          align: 'center',
          key: 'documentType'
        },
        {
          title: '适用客户名称',
          minWidth: 120,
          align: 'center',
          key: 'customerCode',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'customerCode', 'customerName')
          }
        },
        {
          title: '模板编号',
          minWidth: 120,
          align: 'center',
          key: 'templateCode',
        },
        {
          title: '运输方式显示',
          minWidth: 120,
          align: 'center',
          key: 'trafModeName',
        },
        {
          title: '币制显示',
          minWidth: 100,
          align: 'center',
          key: 'currName'
        },
        {
          title: '状态',
          minWidth: 120,
          align: 'center',
          key: 'status',
        },
        {
          title: '录入时间',
          minWidth: 120,
          align: 'center',
          key: 'insertTime',
        },
        {
          title: '联络人',
          minWidth: 120,
          align: 'center',
          key: 'linkmanName',
        },
        {
          title: '目的地',
          minWidth: 120,
          align: 'center',
          key: 'destination',
        },
        {
          title: '目的港',
          minWidth: 160,
          align: 'center',
          key: 'destPort'
        },
        {
          title: '备注',
          minWidth: 120,
          align: 'center',
          key: 'note',
        }
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return h("span", showVal.trim())
    }
  }
}

export {
  columnsConfig,
  columns
}
