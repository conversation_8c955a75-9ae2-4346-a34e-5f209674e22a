<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InvoiceTemplateListSearch ref="headSearch"></InvoiceTemplateListSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard>
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <InvoiceTemplateTabs v-if="!showList" :typeNo="typeNo" :compData="dataList" :textData="textData" :searchData="searchData"
                         @oneditback="backToList"></InvoiceTemplateTabs>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import InvoiceTemplateTabs from './InvoiceTemplateTabs'
  import { columns, columnsConfig } from './invoiceTemplateColumns'
  import InvoiceTemplateListSearch from './InvoiceTemplateListSearch'
  import { invoiceTemplateList } from '@/view/cs-invoice-template/js/invoiceTemplateList'

  export default {
    name: 'InvoiceTemplateEList',
    components: {
      InvoiceTemplateTabs,
      InvoiceTemplateListSearch
    },
    mixins: [invoiceTemplateList, columns],
    data() {
      return {
        ajaxUrl: {
          enableUrl: csAPI.invoiceTemplate.use.useConfig,
          delete: csAPI.invoiceTemplate.delete.deleteConfig,
          selectAllPaged: csAPI.invoiceTemplate.search.getConfig,
          getDataListUrl: csAPI.invoiceTemplate.custumer.custumerConfig,
          invoiceTemplateConfiguration: csAPI.importFilePath.invoiceTemplateConfiguration
        }
      }
    },
    mounted: function () {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
