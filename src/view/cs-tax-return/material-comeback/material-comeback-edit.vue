<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 4px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { editStatus, returnManagement, productClassify } from '@/view/cs-common'

  export default {
    name: 'materialComebackEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        firstLoading: true,
        emsNoLoading: true,
        formName: 'frmData',
        cmbSource: {
          emsNo: [],
          templateHeadId: [],
          ieMark: returnManagement.I_E_MARK,
          gmark: productClassify.GMARK_SELECT
        },
        ajaxUrl: {
          insert: csAPI.taxReturn.materialComeback.head.insert,
          update: csAPI.taxReturn.materialComeback.head.update,
          getEmsNo: csAPI.taxReturn.materialComeback.head.getEmsNo,
          getErpTemp: csAPI.taxReturn.materialComeback.head.getErpTemp,
          getTempDatas: csAPI.taxReturn.materialComeback.head.getTempDatas
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this,
            gMarkDisable = false
          me.fieldsReset()
          if (me.buttons[me.buttons.findIndex(btn => btn.key === 'save')]) {
            me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
          }
          if (me.showDisable) {
            gMarkDisable = true
          } else {
            gMarkDisable = (me.editConfig.editStatus !== editStatus.ADD)
          }
          me.$nextTick(() => {
            let field = me.detailConfig.fields.find(p => p.key === 'gmark')
            if (field) {
              me.setDisable('gmark', gMarkDisable)
            }
          })
        }
      }
    },
    created() {
      let me = this
      // 选择模板
      me.$http.post(me.ajaxUrl.getErpTemp).then(res => {
        me.$set(me.cmbSource, 'templateHeadId', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.KEY
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'templateHeadId', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'templateHeadId')
        if (field) {
          me.fieldOptimization(field)
        }
      })
      // 备案号
      me.$http.post(me.ajaxUrl.getEmsNo).then(res => {
        me.$set(me.cmbSource, 'emsNo', res.data.data.map(item => {
          return {
            label: item,
            value: item
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'emsNo', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'emsNo')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    methods: {
      /**
       * 数据加载完成后执行
       * @param flag
       */
      afterModelLoaded(flag) {
        let me = this
        me.$nextTick(() => {
          me.setDisable('gmark', true)
          me.setDisable('ieMark', true)
        })
        if (!flag) {
          me.$set(me.detailConfig, 'model', me.editConfig.editData)
        }
      },
      /**
       * 输入框
       * @returns {({itemClass: string, isCard: boolean, title: string, type: string, key: string}|{title: string, key: string}|{defaultValue: string, type: string, title: string, key: string, props: {disabled: boolean}}|{title: string, key: string, props: {maxlength: number}}|{title: string, key: string, props: {maxlength: number}})[]}
       */
      getFields() {
        let me = this
        return [{
          isCard: true,
          title: '表头信息',
          key: '1212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          required: true,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          type: 'select',
          title: '选择模板',
          key: 'templateHeadId',
          on: {
            change: me.templateIdChange
          },
          props: {
            options: [],
            optionLabelRender: opt => opt.label
          }
        }, {
          key: 'emsNo',
          required: true,
          type: 'select',
          title: '备案号',
          on: {
            change: me.emsNoChange
          },
          props: {
            optionLabelRender: opt => opt.label
          }
        }, {
          type: 'select',
          required: true,
          props: {
            // meta: 'TRADE'
            options: [{
              value: '0664', label: '进料料件复出'
            }, {
              value: '0265', label: '来料料件复出'
            }]
          },
          title: '监管方式',
          key: 'tradeMode'
        }, {
          type: 'select',
          key: 'agentCode',
          title: '报关单申报单位'
        }, {
          key: 'ieMark',
          type: 'select',
          title: '进出口标志',
          props: {
            disabled: true
          },
          defaultValue: 'E'
        }, {
          key: 'gmark',
          type: 'select',
          title: '物料类型',
          props: {
            disabled: true
          },
          defaultValue: 'I'
        }, {
          key: 'note1',
          title: '备注'
        }]
      },
      templateIdChange(value) {
        let me = this
        if (me.firstLoading) {
          me.$set(me, 'firstLoading', false)
        } else {
          me.$set(me.detailConfig.model, 'emsNo', '')
          me.$set(me.detailConfig.model, 'tradeMode', '')
          me.$set(me.detailConfig.model, 'agentCode', '')
          if (!isNullOrEmpty(value)) {
            me.$http.post(me.ajaxUrl.getTempDatas + '/' + value).then(res => {
              me.$set(me.detailConfig.model, 'emsNo', res.data.data['emsNo'])
              me.$set(me.detailConfig.model, 'tradeMode', res.data.data['tradeMode'])
              me.$set(me.detailConfig.model, 'agentCode', res.data.data['declareCode'])
            }).catch(() => {
            })
          }
        }
      },
      emsNoChange(emsNo) {
        let me = this,
          tradeMode = ''
        if (me.emsNoLoading) {
          me.$set(me, 'emsNoLoading', false)
        } else {
          if (isNullOrEmpty(me.detailConfig.model.templateHeadId) && !isNullOrEmpty(emsNo)) {
            let bookHead = emsNo.substring(0, 1).toUpperCase()
            if (['E', 'C'].includes(bookHead)) {
              tradeMode = '0664'
            } else if (['B'].includes(bookHead)) {
              tradeMode = '0265'
            }
            if (!isNullOrEmpty(tradeMode)) {
              me.$set(me.detailConfig.model, 'tradeMode', tradeMode)
            }
          }
        }
      },
      /**
       * 返回主界面
       */
      handleBack() {
        let me = this
        me.$emit('backToList')
      },
      handleSave() {
        let me = this,
          currData = Object.assign({}, me.editConfig.editData)
        if (!isNullOrEmpty(me.detailConfig.model['agentCode'])) {
          let agentItem = me.inSource.agentCode.find(p => p.value === me.detailConfig.model['agentCode'])
          if (agentItem) {
            me.$set(me.detailConfig.model, 'agentName', agentItem.label)
          } else {
            me.$set(me.detailConfig.model, 'agentName', '')
          }
        } else {
          me.$set(me.detailConfig.model, 'agentName', '')
        }
        me.doSave(res => {
          me.$set(me, 'firstLoading', true)
          me.$set(me, 'emsNoLoading', true)
          currData = Object.assign({}, res.data.data)
          me.$emit('onHeadSaved', {
            editData: res.data.data,
            editStatus: editStatus.EDIT
          })
        }, () => {
          me.$set(me, 'firstLoading', true)
          me.$set(me, 'emsNoLoading', true)
          me.$emit('onHeadSaved', {
            editData: currData,
            editStatus: editStatus.EDIT
          })
        })
      }
    }
  }
</script>

<style scoped>
  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
