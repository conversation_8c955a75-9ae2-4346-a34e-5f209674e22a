<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <materialComebackEdit :edit-config="currConfig" :in-source="inSource"
                              @onHeadSaved="onHeadSaved" @backToList="backToList"></materialComebackEdit>
      </TabPane>
      <TabPane name="detailTab" v-if="showBody" label="复出明细" index="'2'">
        <materialComebackBodyList ref="detailTab" :parent-config="currConfig"></materialComebackBodyList>
      </TabPane>
      <TabPane name="matchedTab" v-if="showBody" label="匹配明细" index="'3'">
        <matchedDataList ref="matchedTab" :head-id="currConfig.editData.sid"></matchedDataList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import matchedDataList from './matched-data-list'
  import materialComebackEdit from './material-comeback-edit'
  import { materialComebackTabs } from './js/materialComebackTabs'
  import materialComebackBodyList from './material-comeback-body-list'

  export default {
    name: 'materialComebackTabs',
    components: {
      matchedDataList,
      materialComebackEdit,
      materialComebackBodyList
    },
    mixins: [materialComebackTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
