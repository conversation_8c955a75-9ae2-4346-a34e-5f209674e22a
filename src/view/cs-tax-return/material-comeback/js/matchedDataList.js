import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const matchedDataList = {
  name: 'matchedDataList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    headId: {
      type: String,
      default: () => ('')
    }
  },
  data() {
    return {
      hasChildTabs: true,
      pmsLevel: 'matched',
      listConfig: {
        checkColumnShow: false,
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  watch: {
    headId: {
      immediate: true,
      handler: function () {
        this.handleSearchSubmit()
      }
    }
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.headId
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 150,
        key: 'gname',
        title: '商品名称'
      }, {
        key: 'qty',
        width: 150,
        title: '数量'
      }, {
        width: 150,
        key: 'unit',
        title: '申报单位'
      }, {
        width: 150,
        key: 'decPrice',
        title: '申报单价'
      }, {
        width: 150,
        key: 'decTotal',
        title: '申报总价'
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 150,
        title: '原产国',
        key: 'originCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        })
      }, {
        width: 120,
        key: 'documentNo',
        title: '原产地证编号'
      }, {
        width: 150,
        key: 'entryNo',
        title: '原报关单号'
      }, {
        width: 120,
        key: 'emsListNo',
        title: '原清单内部编号'
      }, {
        width: 150,
        key: 'invoiceNo',
        title: '原发票号',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'netWt',
        title: '净重'
      }, {
        width: 136,
        key: 'qty1',
        title: '法一数量'
      }, {
        width: 120,
        key: 'unit1',
        title: '法一单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'UNIT')
        })
      }, {
        width: 136,
        key: 'qty2',
        title: '法二数量'
      }, {
        width: 120,
        key: 'unit2',
        title: '法二单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'UNIT')
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    }
  }
}
