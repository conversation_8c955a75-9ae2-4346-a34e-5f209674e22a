<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <materialComebackTabs v-if="!showList" :edit-config="editConfig" :in-source="cmbSource"
                          @onEditBack="editBack"></materialComebackTabs>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <createCustomsInfoPop :show.sync="createCustomsInfo.show" :ems-list-no="createCustomsInfo.emsListNo"
                          @doGenerate="doGenerateCustomsData"></createCustomsInfoPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { materialComebackList } from './js/materialComebackList'

  export default {
    name: 'materialComebackList',
    mixins: [materialComebackList],
    data() {
      return {
        listConfig: {
          exportTitle: '料件复出'
        },
        cmbSource: {
          agentCode: []
        },
        ajaxUrl: {
          deleteUrl: csAPI.taxReturn.materialComeback.head.delete,
          exportUrl: csAPI.taxReturn.materialComeback.head.exportUrl,
          selectAllPaged: csAPI.taxReturn.materialComeback.head.selectAllPaged,
          generateCustomsData: csAPI.taxReturn.materialComeback.head.generateCustomsData
        }
      }
    },
    created() {
      let me = this
      // 报关行
      me.$http.post(csAPI.ieParams.selectComboxByCode + '/CUT').then(res => {
        let exists = [],
          validData = []
        res.data.data.filter(item => !isNullOrEmpty(item['CODE'])).forEach(it => {
          if (!exists.includes(it['CODE'])) {
            exists.push(it['CODE'])
            validData.push({
              value: it['CODE'],
              label: it['LABEL']
            })
          }
        })
        me.$set(me.cmbSource, 'agentCode', validData)
      }).catch(() => {
        me.$set(me.cmbSource, 'agentCode', [])
      }).finally(() => {
        me.searchFieldsReLoad('agentCode')
      })
    }
  }
</script>

<style scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
