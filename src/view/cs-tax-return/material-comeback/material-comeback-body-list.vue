<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <materialComebackBodyEdit v-if="!showList" :edit-config="editConfig" :parent-config="parentConfig"
                              @onEditBack="editBack"></materialComebackBodyEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <matchableInfoPop :show.sync="matchDecData.show" :configData="matchDecData.data" ie-mark="E"
                      @matchSuccess="handleSearchSubmit"></matchableInfoPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { materialComebackBodyList } from './js/materialComebackBodyList'

  export default {
    name: 'materialComebackBodyList',
    mixins: [materialComebackBodyList],
    data() {
      return {
        listConfig: {
          exportTitle: '复出明细'
        },
        ajaxUrl: {
          deleteUrl: csAPI.taxReturn.materialComeback.body.delete,
          exportUrl: csAPI.taxReturn.materialComeback.body.exportUrl,
          autoMatch: csAPI.taxReturn.materialComeback.toBeMatched.autoMatch,
          selectAllPaged: csAPI.taxReturn.materialComeback.body.selectAllPaged
        }
      }
    }
  }
</script>

<style scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
