<template>
  <XdoModal width="1280" mask v-model="show" title="匹配原报关数据"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <matchableBodyShow :curr-data="configData"></matchableBodyShow>
    <Card :bordered="false">
      <p slot="title">可匹配信息</p>
    </Card>
    <matchableInfoList ref="grdMatchList" :head-id="configData.sid" :ems-no="configData.emsNo"
                       :fac-g-no="configData.facGNo" :trade-mode="configData.tradeMode"
                       @doSelectedRowChange="doSelectedRowChange"></matchableInfoList>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 0;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import matchableBodyShow from './matchable-body-show'
  import matchableInfoList from './matchable-info-list'

  export default {
    name: 'matchableInfoPop',
    mixins: [],
    components: {
      matchableBodyShow,
      matchableInfoList
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      /**
       * 传入的查看信息
       */
      configData: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        selectedRows: [],
        ajaxUrl: {
          saveMatchCustoms: csAPI.taxReturn.materialComeback.toBeMatched.save
        },
        buttons: [
          {...btnComm, label: '保存', icon: 'ios-bookmarks-outline', key: 'save', click: this.handleSave},
          {...btnComm, label: '返回', icon: 'ios-undo-outline', key: 'cancel', click: this.handleClose}
        ]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            if (me.$refs['grdMatchList'] && typeof me.$refs['grdMatchList'].doRealSearch === 'function') {
              me.$refs['grdMatchList'].doRealSearch()
            }
          }
        }
      }
    },
    methods: {
      /**
       * 选中数据
       * @param rows
       */
      doSelectedRowChange(rows) {
        let me = this
        me.$set(me, 'selectedRows', rows)
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        if (me.selectedRows.length > 0) {
          me.$http.post(me.ajaxUrl.saveMatchCustoms + '/' + me.configData.sid, me.selectedRows).then(() => {
            me.$Message.success('数据匹配完成!')
            me.$emit('matchSuccess')
            me.$emit('update:show', false)
          }).catch(() => {
          })
        } else {
          me.$Message.warning('你还未选择任何数据!')
        }
      },
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-card-head {
    padding: 3px 16px 1px 16px !important;
  }
</style>
