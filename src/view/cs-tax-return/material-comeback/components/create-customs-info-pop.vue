<template>
  <XdoModal width="600" mask v-model="show" title="生成报关数据"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm ref="popForm" class="xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="emsListNo" label="单据内部编号">
          <XdoIInput type="text" v-model="frmData.emsListNo" clearable></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  /**
   * 生成报关数据
   */
  export default {
    name: 'createCustomsInfoPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      emsListNo: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {
          emsListNo: ''
        },
        rulesHeader: {
          emsListNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, label: '生成', icon: 'dc-btn-save', click: this.handleConfirm
        }, {
          ...btnComm, label: '关闭', icon: 'dc-btn-cancel', click: this.handleClose
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.$set(me.frmData, 'emsListNo', me.emsListNo)
          } else {
            me.$nextTick(() => {
              if (me.$refs['popForm']) {
                me.$refs['popForm']['resetFields'].call()
              }
            })
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$refs['popForm'].validate().then(isValid => {
          if (isValid) {
            me.$emit('doGenerate', me.frmData.emsListNo)
          }
        })
      }
    }
  }
</script>

<style scoped>
</style>
