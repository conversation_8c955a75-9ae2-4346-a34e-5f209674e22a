import { keepDecimal } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const matchableInfoList = {
  name: 'matchableInfoList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        emsNo: []
      }
    }
  },
  computed: {
    gridApi() {
      let me = this
      if (me.$refs['table']) {
        if (me.$refs['table'].gridOptions) {
          return me.$refs['table'].gridOptions.api
        }
      }
      return null
    }
  },
  methods: {
    /**
     * 执行手动查询
     */
    doRealSearch() {
      let me = this
      me.$nextTick(() => {
        me.$set(me.searchConfig.model, 'emsNo', me.emsNo)
        if (me.tradeMode === '0265') {
          me.$set(me.searchConfig.model, 'tradeMode', '0214')
        } else if (me.tradeMode === '0664') {
          me.$set(me.searchConfig.model, 'tradeMode', '0615')
        }
        me.handleSearchSubmit()
      })
    },
    /**
     * 查询条件
     * @returns {({type: string, title: string, key: string, props: {optionLabelRender: (function(*): *)}}|{type: string, title: string, key: string, props: {meta: string}}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'emsNo',
        type: 'select',
        title: '备案号',
        props: {
          optionLabelRender: opt => opt.label
        }
      }, {
        type: 'pcode',
        props: {
          meta: 'TRADE'
        },
        title: '监管方式',
        key: 'tradeMode'
      }, {
        range: true,
        key: 'ddate',
        title: '报关单申报日期'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['sid'] = me.headId
      paramObj['facGNo'] = me.facGNo
      return paramObj
    },
    /**
     * 显示列表
     * @returns {({width: number, title: string, key: string}|{onCellValueChanged: (function(*): undefined), editable: boolean, width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'kppQty',
        title: '可匹配数量'
      }, {
        width: 150,
        key: 'qty',
        editable: true,
        title: '本次匹配数量',
        onCellValueChanged: me.onCellValueChanged
      }, {
        width: 150,
        key: 'syQty',
        title: '剩余数量'
      }, {
        width: 150,
        key: 'listNo',
        title: '核注清单编号'
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 150,
        key: 'dDate',
        title: '报关单申报日期'
      }, {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 120,
        key: 'unit',
        title: '单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.unit)
        }, true)
      }, {
        width: 150,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 150,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        title: '原产国',         // '目的国',
        key: 'originCountry',   // 'destinationCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.country_outdated)
        })
      }, {
        width: 120,
        key: 'documentNo',
        title: '产地证编号'
      }]
    },
    /**
     * 行内编辑 本次匹配数量
     * @param event
     */
    onCellValueChanged(event) {
      let me = this,
        oldQty = Number(event.oldValue),
        // 当前行的下标
        tempIndex = me.listConfig.data.findIndex(item => {
          return item.sid === event.data.sid
        })
      if (!event.data.qty || !/^\d*\.?\d*$/.test(event.data.qty.toString())) {
        me.$Message.warning('请输入有效的数据!')
        // 把值重置到正确的值
        me.listConfig.data[tempIndex].qty = String(oldQty)
      } else {
        let syQty = Number(event.data.syQty),
          newQty = Number(keepDecimal(event.newValue, 5))
        if (newQty > syQty + oldQty) {
          me.$Message.warning('【本次匹配数量】不得大于【剩余数量】')
          // 把值重置到正确的值
          me.listConfig.data[tempIndex].qty = String(oldQty)
        } else {
          me.listConfig.data[tempIndex].qty = keepDecimal(event.newValue, 5)
          // 计算 剩余
          me.listConfig.data[tempIndex].syQty = keepDecimal(syQty + oldQty - newQty, 5)
        }
      }

      // 处理 重绘问题
      const obj = me.listConfig.data
      me.listConfig.data = []
      me.$nextTick(() => {
        me.listConfig.data = obj
        //页面刷新勾选框
        me.$nextTick(() => {
          if (me.gridApi) {
            me.gridApi.forEachNode(node => {
              let isSelect = me.listConfig.selectRows.filter(x => x.sid && x.sid === node.data.sid)
              if (isSelect && isSelect.length > 0) {
                node.setSelected(true, false)
              }
            })
          }
        })
      })
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 行选中或取消选中
     * @param agGrd
     */
    handleSelectionChange(agGrd) {
      let me = this
      if (Array.isArray(agGrd)) {
        me.$set(me.listConfig, 'selectRows', agGrd)
      } else {
        me.$set(me.listConfig, 'selectRows', agGrd.api.getSelectedRows())
      }
      me.$emit('doSelectedRowChange', me.listConfig.selectRows)
    },
    /**
     * 查询执行完成后的默认操作
     */
    afterSearch() {
      let me = this
      me.$emit('doSelectedRowChange', [])
    }
  }
}
