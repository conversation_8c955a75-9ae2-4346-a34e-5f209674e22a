<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getGwShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="分配指派">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="userName" label="关务账号" class="dc-merge-1-4">
        <xdo-select v-model="userName" :options="this.cmbSource.userNameList" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="submit">提交</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"

  export default {
    name: 'permission',
    props: {
      gwShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: String
      },
      type: {
        type: String
      },
    },
    data() {
      return {
        entrustForward: '',
        userName: '',
        entrustSupplier: '',
        entrustCut: '',
        cmbSource:{
          forwardCmb:[],
          userNameList:[],
          cutCmb:[],
          supplierCmb:[]
        },
        ajaxUrl:{
          entrust: csAPI.sapErp.batch.head.entrust,
        }
      }
    },
    computed: {
      getGwShow() {
        return this.gwShow
      }
    },
    mounted() {
      let me = this;
      let baseUrl = window.location.origin + '/pms/api/v1/user/list'
      this.$http.post(baseUrl,{
        headers: {
          Authorization: 'Bearer ' + me.$store.state.token
        }
      }).then(res => {
        console.log( res.data.data.map((item => item.userName)))
        me.cmbSource.userNameList = res.data.data
          .filter(item => item.userName.startsWith('GW'))
          .map((item => item.userName))
      })
    },
    methods: {
      handleVisible(val) {
        if (!val) {
          this.$emit('update:gwShow', false)
        }
      },
      submit(){
        let me = this
        console.log(me.userName)






        this.$emit('onAfterGwEntrust',null)
      }
    }
  }
</script>

<style scoped>

</style>

