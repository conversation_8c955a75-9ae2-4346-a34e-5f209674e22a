<template>
  <XdoModal width='1400' mask v-model='show' :title="xdoi18nGet(batchNo)"
            :closable='false' footer-hide :mask-closable='false'>
    <a class='ivu-modal-close' @click='handleClose'>
      <XdoIcon type='ios-close' style='font-size: 21px; color: #389DE9; line-height: 1px;'></XdoIcon>
    </a>
    <XdoForm ref='headerEditFrom' class='dc-form xdo-enter-form ' label-position='right' :label-width='80'
             :model='configData' style='padding: 0; grid-column-gap: 0;'>

    </XdoForm>
<!--    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>-->
    <Card :bordered='false'>
      <DcAgGrid class='dc-table' ref='table' :height='300'
                   :columns='gridConfig.gridColumns' :data='gridConfig.data' suppressRowClickSelection
                   rowSelection='multiple' @on-selection-change='handleSelectionChange' @grid-ready='onGridReady'></DcAgGrid>

      <div ref='area_page'>
        <XdoPage class='dc-page' show-total show-sizer :page-size-opts='pageSizeOpts'
                 :current='pageParam.page' :page-size='pageParam.limit' :total='pageParam.dataTotal'
                 @on-change='pageChange' @on-page-size-change='pageSizeChange' />
      </div>
    </Card>
    <div class='xdo-enter-action action' style='text-align: center; margin-top: 10px;'>
      <template v-for='item in buttons'>
        <Button v-if='item.needed' :type='item.type' :disabled='item.disabled' :loading='item.loading'
                @click='item.click' :icon='item.icon' :key='item.icon'>{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
    <matPopNote ref='matPopNote' :show.sync='matPopNoteShow' :edit-config='editConfig' @onReload="afterSetting"  ></matPopNote>
  </XdoModal>
</template>

<script>
import { csAPI } from '@/api'
import { matPopColumns } from './matPopColumns'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'
import matPopNote from './matPopNote.vue'
import {isNullOrEmpty} from "@/libs/util";


export default {
  name: 'matPop',
  components: { matPopNote },
  mixins: [matPopColumns, commColumnsCustom],
  props: {
    show: {
      type: Boolean,
      require: true
    },
    batchNo: {
      type: String,
      require: true
    },
    /**
     * 传入的查看信息
     */
    configData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      isShowOp: false,
      matching: false,
      searchParam: {
        companyNameCn: '',
        countryName: '',
        tsCode: '',
      },
      p_group: 'noAction',
      checkboxSelection: true,
      ajaxUrl: {
        selectAllPaged:csAPI.sapErp.batch.head.getInfoByBatchNo
      },
      buttons: [
        {
          ...btnComm,
          label: majesty.Vue.xdoi18nGet('关闭'),
          type: 'primary',
          command: 'cancel',
          click: this.handleClose
        }
      ],
      pageParam: {
        page: 1,
        limit: 10,
        dataTotal: 0
      },
      facGNo: '',
      gMark: '',
      iEMark: '',
      decPrice: '',
      curr:'',
      forwardCode:'',
      transMode:'',
      pmsLevel: 'body',
      toolbarEventMap: {
        'setting': this.handleSetting,
      },
      matPopNoteShow: false,
      paramsToNote:[],
    }

  },

  watch: {
    show: function(val) {
      if (val) {
        this.getList()
      }
    },

  },
  mounted() {
    //this.loadFunctions().then()
  },
  created(){

  },
  methods: {
    //跳转页面
    jumpTo(){
      let me = this
      let emsListNo = me.gridConfig.selectData.emsListNo
      let documentsType = me.gridConfig.selectData.documentsType//单据类型
      if (documentsType==='预录入单') {//非保税进口
        if (me.$store.state.tag.tagNavList) {
          let index = me.$store.state.tag.tagNavList.findIndex(item => item.name === 'nonBondedIHeadList');
          if (index != -1) {
            me.$store.state.tag.tagNavList.splice(index, 1);
          }
        }
        me.$router.push({ name:'nonBondedIHeadList', params:{emsListNo:emsListNo}})
      } else if(documentsType==='免表'){//免表
        if (me.$store.state.tag.tagNavList) {
          let index = me.$store.state.tag.tagNavList.findIndex(item => item.name === '"taxPreferenceInformationList"');
          if (index != -1) {
            me.$store.state.tag.tagNavList.splice(index, 1);
          }
        }
        me.$router.push({ name:'taxPreferenceInformationList', params:{emsListNo:emsListNo}})
      }
      me.$emit('update:show', false)
    },
    jumpTo1(){
      let me = this
      let erpEmsListNo = me.gridConfig.selectData.erpEmsListNo
      //非保税进口
      if (me.$store.state.tag.tagNavList) {
        let index = me.$store.state.tag.tagNavList.findIndex(item => item.name === 'nonBondedIHeadList');
        if (index != -1) {
          me.$store.state.tag.tagNavList.splice(index, 1);
        }
      }
      me.$router.push({ name:'nonBondedIHeadList', params:{emsListNo:erpEmsListNo}})
      me.$emit('update:show', false)
    },
    jumpTo2(){
      let me = this
      let entryNo = me.gridConfig.selectData.entryNo//报关单号
      //报关单
      if (me.$store.state.tag.tagNavList) {
        let index = me.$store.state.tag.tagNavList.findIndex(item => item.name === 'entryIList');
        if (index != -1) {
          me.$store.state.tag.tagNavList.splice(index, 1);
        }
      }
        me.$router.push({ name:'entryIList', params:{entryNo:entryNo}})
      me.$emit('update:show', false)
    },
    /**
     * 执行数据查询
     */
    getList() {
      if (isNullOrEmpty(this.ajaxUrl.selectAllPaged)) {
        console.error('查询api不能为空!')
        return
      }
      this.doSearch(this.ajaxUrl.selectAllPaged)
    },
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.tableloading = true
        let params = {batchNo:me.batchNo}
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
          me.tableloading = false
        })
      })
    },
    /**
     * ag-grid创建完成后执行的事件
     */
    onGridReady(params) {
      let me = this
      // 获取gridApi
      me.gridApi = params.api
      me.columnApi = params.columnApi
      // 这时就可以通过gridApi调用ag-grid的传统方法了
      me.gridApi.sizeColumnsToFit()
    },
    /**
     * 关闭
     */
    handleClose() {
      let me = this
      me.$emit('update:show', false)
      me.$emit('import:success', false)
      me.$set(me.gridConfig,'date',null)
    },
    getData(gMark,copGNo,iEMark,decPrice,curr,transMode,forwardCode){
      let me = this
        me.facGNo = copGNo
        me.gMark = gMark
        me.iEMark = iEMark
        me.decPrice = decPrice
        me.curr = curr
        me.forwardCode = forwardCode
        me.transMode = transMode
      me.$http.post(me.ajaxUrl.getMatInfo, {
        facGNo: copGNo,
        gMark: gMark,
        iEMark: iEMark,
        decPrice: decPrice,
        curr:curr,
        forwardCode:forwardCode,
        transMode:transMode,
      }, {
        params: {
          ...me.pageParam
        }
      }).then(res => {
        let data = res.data.data
        me.gridConfig.data = data.data
        me.pageParam.page = data.pageIndex
        me.pageParam.dataTotal = data.total
      }).catch(() => {
        me.afterSearchFailure()
      }).finally(() => {
        me.gridConfig.selectRows = []
        me.afterSearch()
        me.tableloading = false
        me.pageParam.page = 1
      })
    },
    pageChange(page) {
      let me = this
      me.pageParam.page = page
      me.getData(me.gMark,me.facGNo,me.iEMark,me.decPrice,me.curr,me.transMode,me.forwardCode)
    },
    /**
     * 切换每页记录数
     * @param pageSize
     */
    pageSizeChange(pageSize) {
      let me = this
      me.pageParam.limit = pageSize
      if (me.pageParam.page === 1) {
        me.getData(me.gMark,me.facGNo,me.iEMark,me.decPrice,me.curr,me.transMode,me.forwardCode)
      }
    },
    handleSetting(){
      let me = this
      if (!me.gridConfig.selectRows || me.gridConfig.selectRows.length <= 0) {
        me.setToolbarLoading('setStatus')
        me.setStatusModelShow=false
        me.$Message.warning(majesty.Vue.xdoi18nGet('请勾选至少一条数据'))
        return
      }
      me.matPopNoteShow = true

      let params = []
      me.gridConfig.selectRows.forEach(row =>{
        params.push({
          emsListNo: row.emsListNo,
          facGNo: me.facGNo,
          gMark: me.gMark,
          iEMark: me.iEMark,
          decPrice: me.decPrice,
          curr:me.curr,
          forwardCode:me.forwardCode,
          transMode:me.transMode
        })
      })
      me.$set(me,'paramsToNote',params)
      me.paramsToNote = params

    },
    async loadFunctions(group = 'body', height = 112) {
      const param = { resId: this.$route.path, group }
      const res = await this.getFunctions(param)
      this.actions = res
      if (this.refreshDynamicHeight) {
        this.refreshDynamicHeight(height, !this.showSearch ? ['area_search'] : null)
      }
      return res
    },

    afterSetting(data) {
      let me = this
      if(data.noteReason){
        me.$http.post(me.ajaxUrl.setNote+'/'+data.noteReason, me.paramsToNote
        ).then(() => {

        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.getData(me.gMark,me.facGNo,me.iEMark,me.decPrice,me.curr,me.transMode,me.forwardCode)
        })
      }else{
        me.$Message.warning(majesty.Vue.xdoi18nGet('备注说明不能为空'))
      }

    },
  }
}
</script>

<style lang='less' scoped>
/deep/ .ivu-card-head {
  padding: 3px 16px 1px 16px !important;
}

/deep/ .ivu-card-body {
  padding: 8px 8px 2px 8px !important;
}

/deep/ .data-split {
  background-color: #ff9900 !important;
}

.dc-form-5 {
  display: grid;
  grid-column-gap: 10px;
  grid-template-columns: repeat(5, 1fr);
}
</style>
