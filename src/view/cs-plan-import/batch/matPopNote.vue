<template>
  <XdoModal v-model="show" mask width="560" :title="xdoi18nGet('维护备注说明')"
            :mask-closable="false" footer-hide :closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header" style="margin: 30px;">
      <XdoForm class="dc-form dc-form-2 xdo-enter-form" ref="form"  :label-width="80">
        <XdoFormItem :label="xdoi18nGet('备注说明')" class="dc-merge-1-5">
          <XdoIInput type="textarea" v-model="frmData.noteReason" :autosize="{minRows: 3,maxRows: 6}" ></XdoIInput>
        </XdoFormItem>
          </XdoForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: right; margin: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
import { csAPI } from '@/api'
import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

export default {
  name: 'matPopNote',
  mixins: [baseDetailConfig],
  props: {
    show: {
      type: Boolean,
      require: true
    }
  },
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      frmData: {
        status:'',
        noteReason:''
      },

      ajaxUrl: {
        insert: csAPI.earlyWarning.manager.warringSet.insert,
        update: csAPI.earlyWarning.manager.warringSet.update
      },
      buttons: [
        { ...btnComm, label: '取消', type: 'default', icon: 'dc-btn-cancel', click: this.handleClose },
        { ...btnComm, label: '确定', type: 'primary', icon: 'dc-btn-save', click: this.handleSave }
      ]
    }
  },
  created: function() {
    // this.initialData()
  },
  watch: {
    show: {
      handler: function(show) {
        let me = this
        if (!show) {
          if (me.$refs['frmData'] && typeof me.$refs['frmData']['resetFields'] === 'function') {
            me.$refs['frmData']['resetFields'].call()
          }
        }
      }
    }
  },
  methods: {
    handleClose() {
      let me = this
      me.$emit('update:show', false)
    },
    handleSave() {
      let me = this
      me.$emit('onReload',me.frmData)
      me.frmData.noteReason = ''
      me.handleClose()

    },
    handleSelectionChange(selectRows) {
      let me = this
      me.warringEmailsGridConfig.selectRows = selectRows
    },

  }
}
</script>

<style scoped>
/deep/ .ivu-modal-body {
  padding: 1px !important;
}

/deep/ .ivu-card-head {
  padding: 5px 10px !important;
}

/deep/ .dc-form {
  grid-column-gap: 0;
  grid-template-columns: repeat(3, minmax(100px, 1fr));
}
/deep/ .dc-merge-new {
  width: 90%;
  //margin: 0 auto;
  //padding-left: 1px;
  //padding-right: 1px;
}
.firstZhi {
  top: 40px;
  left: 303px;
  position: absolute;
}
.add-gap {
  margin-right: 15px  !important;
}
.xxx {
  width: 100%;
  margin-left: 5px  !important;
}
.firstZhi {
  top: 72px;
  left: 373px;
  position: absolute;
}
.filesList strong a {
  font-size: 14px;
}
.ieLogisticsTrackingCard .ivu-card-head {
  padding: 5px 10px !important;
}

.ieLogisticsTrackingCard .ivu-card-body {
  padding: 8px 8px 2px 8px;
}
</style>
