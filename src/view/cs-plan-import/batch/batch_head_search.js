import {importExportManage} from '@/view/cs-common'
import {ArrayToLocaleLowerCase} from "@/libs/util";
import {csAPI} from "@/api";
import { roleCodes as ROLE_CODES} from '@/view/cs-common/constant'

export default {
  data() {
    return {
      searchSchema: {
        titleWidth: 80
      },
      overseasShipperList:[],
      importExportManage: importExportManage,
      showSearch: false,
      searchParam: {
        batchNo: '',
        batchStatus: '',
        sendGwList: ['0', '2']
      }
    }
  },
  created() {
    // 收/发货人
    let me = this
    me.$http.post(csAPI.ieParams.PRD).then(res => {
      me.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
    }).catch(() => {
      me.overseasShipperList = []
    })
  },
  methods: {
    handleShowSearch() {
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(112, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit() {
      this.pageParam.page = 1
      this.loadData()
    },

  },
  computed: {
    isGwUser(){
      const me = this

      const roleCodes = me.$store.state.user['roleCodes']
      if (!roleCodes){
        return false
      }
      return roleCodes.split(',').map(s => s.trim()).includes(ROLE_CODES.GW)
    },
    searchElements() {
      const elements = [
        {
          key: 'batchNo', title: '批次号'
        },
        // {
        //   type: "select", title: '批次状态', key: 'batchStatus',
        //   props: {options: this.importExportManage.batchStatusHead}
        // },
        {
          key: 'ieport', title: '进境关别', type: "pcode",
          props: {meta: 'CUSTOMS_REL'}
        },
        {
          key: 'overseasShipper', title: '境外发货人代码',type: "select",
          props: {options: this.overseasShipperList}
        },
        {
          key: 'overseasShipperName', title: '境外发货人名称'
        },
        {
          key: 'trafName', title: '运输工具名称'
        },
        {
          key: 'cutMode', title: '征免性质', type: "pcode",
          props: {meta: 'LEVYTYPE'}
        },
        {
          key: 'wrapType', title: '包装种类', type: "pcode",
          props: {meta: 'WRAP'}
        },
        {
          key: 'transMode', title: '成交方式', type: "pcode",
          props: {meta: 'TRANSAC'}
        },
        // {
        //   key: 'exemptsNo', title: '免表编号'
        // },
        {
          key: 'note', title: '备注'
        },
        {
          type: 'dateRange', title: '进口日期', key: 'ieDate', fields: [{key: 'ieDateFrom'}, {key: 'ieDateTo'}]
        },
        {
          key: 'mawb', title: '主提运单号'
        },{
          key: 'poItem', title: 'PO号'
        },{
          key: 'invNo', title: '发票号'
        }
      ]
      if (!this.isGwUser) {
        elements.push(
          {
            type: 'select', title: '是否提交关务', key: 'sendGwList', class: 'dc-multiple-select',
            props: { options: this.importExportManage.isSendGw, multiple: true }
          }
        )
      }
      return elements
    }
  }
}
