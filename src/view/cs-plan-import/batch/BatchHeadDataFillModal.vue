<template>
  <XdoModal v-model="show" ref="theModel" width="550" :title="title"
            :mask-closable="false" footer-hide :closable="false" @on-visible-change="visibleChange">
    <XdoForm ref="frmFillData" class="dc-form no-gap" :model="formData" :label-width="100" inline :rules="formRules">
      <XdoFormItem prop="mawb" class="dc-merge-1-4" label="主提运单号">
        <XdoIInput type="text" v-model="formData.mawb" clearable :maxlength="50" />
      </XdoFormItem>
      <XdoFormItem prop="hawb" class="dc-merge-1-4" label="分提运单号">
        <XdoIInput type="text" v-model="formData.hawb" clearable :maxlength="50" />
      </XdoFormItem>
      <XdoFormItem prop="note" class="dc-merge-1-4" label="备注">
        <XdoIInput type="text" v-model="formData.note" clearable :maxlength="255" />
      </XdoFormItem>
    </XdoForm>
    <div class="action-buttons" style="text-align: center; padding-top: 15px;">
      <div style="display: flex; justify-content: space-around;">
        <template v-for="item in buttons">
          <Button v-if="item.needed" :size="item.size" :type="item.type" :disabled="item.disabled"
                  :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
          </Button>
        </template>
      </div>
    </div>
  </XdoModal>
</template>

<script>

import { csAPI } from '@/api'

export default {
  name: 'BatchHeadDataFillModal',
  props: {
    show: {
      type: Boolean,
      default: () => true
    },
    title: {
      type: String,
      default: () => ''
    },
    fillData: {
      type: Object,
      default: () => {
      }
    }
  },
  created() {
    const me = this
    me.formData = { ...me.fillData }
  },
  data() {
    let btnComm = {
      icon: '',
      size: 'default',
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      formData: {
        sid: '',
        mawb: '',
        hawb: '',
        note: ''
      },
      buttons: [{
        ...btnComm, label: majesty.Vue.xdoi18nGet('取消'), type: '', click: this.handleCancel
      }, {
        ...btnComm, label: majesty.Vue.xdoi18nGet('确认'), type: 'primary', click: this.handleOk
      }],
      ajaxUrl: {
        dataBackFillingUrl: csAPI.sapErp.batch.head.dataBackFilling
      },
      formRules: {}
    }
  },
  methods: {
    async handleOk() {
      const me = this
      me.$set(me.buttons[0], 'loading', true)
      try {
        const isValidate = await me.$refs['frmFillData'].validate()
        if (!isValidate) {
          return
        }
        if (!me.formData.mawb && !me.formData.hawb && !me.formData.note) {
          me.$Message.warning('请填写信息!')
          return
        }
        await me.$http.post(me.ajaxUrl.dataBackFillingUrl, { ...me.formData })
        me.$Message.success(majesty.Vue.xdoi18nGet('数据回填成功!'))
        me.close(true)
      } finally {
        me.$set(me.buttons[0], 'loading', false)
      }
    },
    handleCancel() {
      const me = this
      me.close(false)
    },
    close(reload) {
      const me = this
      me.formData = { sid: '', mawb: '', hawb: '', note: '' }
      me.$emit('close', reload)
    },
    visibleChange(val) {
      const me = this
      if (val) {
        me.formData = { ...me.fillData }
      }
    }
  }
}
</script>

<style scoped>
/deep/ .no-gap .ivu-form-item {
  margin-right: 0;
}

.action-buttons button:nth-child(1) {
  margin-left: 80px;
}

.action-buttons button:nth-child(2) {
  margin-right: 80px;
}
</style>
