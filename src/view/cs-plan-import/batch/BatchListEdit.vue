<template>
  <section v-focus>
    <FormBuilder class="form-builder" ref="editForm" :disabled="editDisabled" :schema="editSchema" :rules="formRules" :items="editElements" :model="editModel">
    </FormBuilder>
    <div class="xdo-enter-action" style="text-align: center;margin-top:10px">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :key="item.label">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>
<script>
import formMixin from './batch_list_edit_form'
import { editStatus } from '@/view/cs-common'
import { csAPI } from '@/api'
// import { isNullOrEmpty } from '@/libs/util'

export default {
  name: 'BatchListEdit',
  mixins: [formMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      editBackOption: {
        showList: true,
        updated: false
      }
    }
  },
  mounted () {
    this.editModel.decPrice += ''
    if (this.editModel.decPrice === 'null') {
      this.editModel.decPrice = '';
    }
    this.editModel.decTotal += ''
    if (this.editModel.decTotal === 'null') {
      this.editModel.decTotal = '';
    }
    this.editModel.splitNum += ''
    if (this.editModel.splitNum === 'null') {
      this.editModel.splitNum = '';
    }
    this.editModel.netWt += ''
    if (this.editModel.netWt === 'null') {
      this.editModel.netWt = '';
    }
    // this.editModel.grossWt += ''
    // if (this.editModel.grossWt === 'null') {
    //   this.editModel.grossWt = '';
    // }
  },
  watch:{
    // 'editModel.facGNo': {
    //   immediate: true,
    //   handler: function () {
    //     this.facGNoEnter()
    //   }
    // },
  },
  methods: {
    handleSaveContinue () {
      if (parseFloat(this.editModel.netWt)>parseFloat(this.editModel.grossWt)) {
        this.$Message.error("净重不可大于毛重数量!")
        return
      }
      this.doSave(() => {
        if (this.editOption.status !== editStatus.SHOW) {
          this.editBackOption.updated = true
          this.editModel = {
            sid: '',
            headId: this.editOption.parent.data.sid,
            facGNo: '',
            purchaseNum: '',
            splitNum: '',
            decPrice: '',
            decTotal: '',
            netWt: '',
            grossWt: '',
            cylinderNo: '',
            originCountry:'',
            decSid: '',
            lineNo: '',
            taxation: '',
            status: '0',
            linkedNo: '',
            curr:'',
            poItem:'',
            poDes:'',
            invNo:'',
            boxNo:'',
          }
        }
      })
    },
    handleSave () {
      if (parseFloat(this.editModel.netWt)>parseFloat(this.editModel.grossWt)) {
        this.$Message.error("净重不可大于毛重数量!")
        return
      }
      this.doSave(() => {
        this.editBackOption.updated = true
      });
    },
    handleSaveClose (){
      if (parseFloat(this.editModel.netWt)>parseFloat(this.editModel.grossWt)) {
        this.$Message.error("净重不可大于毛重数量!")
        return
      }
      this.doSave(() => {
        this.editBackOption.updated = true
        this.editBack()
      })
    },
    handleBack () {
      this.editBack()
    },
    editBack() {
      this.editBackOption.showList = true
      this.$emit('onEditBack', this.editBackOption)
    },
    // facGNoEnter() {
    //   let me = this
    //   me.$http.post(csAPI.packaging.packag.getcopGNo, {
    //     copGNo: me.editModel.facGNo                                // 企业料号
    //   }).then(res => {
    //     if(isNullOrEmpty(me.editModel.taxation)) {
    //       me.$set(me.editModel, 'taxation', res.data.taxation)
    //     }
    //   }).catch(() => {
    //   })
    // },
    doSave(callback) {
      this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          let http = null
          if (this.editModel.sid) {
            http = this.$http.put(`${csAPI.sapErp.batch.list.rest}/${this.editModel.sid}`, this.editModel)
          } else {
            http = this.$http.post(csAPI.sapErp.batch.list.rest, this.editModel)
          }
          http.then(res => {
            this.editModel.sid = res.data.data.sid
            this.$Message.success("保存成功")
            if (callback) {
              callback(res)
            }
          }, () => {})
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .form-builder {
    background: white;
    padding-top: 10px;
  }
</style>
