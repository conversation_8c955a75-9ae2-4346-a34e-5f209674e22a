import { editStatus } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'
import { productClassify,importExportManage } from '@/view/cs-common'
import {getKeyValue, isNullOrEmpty} from "@/libs/util"
import {columnRender} from "@/view/cs-interim-verification/comm/columnRender";
import {commList} from "@/view/cs-interim-verification/comm/commList";
import {baseColumns} from "@/view/cs-interim-verification/comm/baseColumns";


export default {
  mixins: [commList,columnRender,baseColumns],
  data () {
    return {
      basicColumn: [
        {
          width: 36,
          fixed: 'left',
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          key: 'opera',
          cellRendererFramework: operationRenderer(this)
        },
        {
          title: '采购订单号',
          minWidth: 120,
          align: 'center',
          key: 'linkedNo',
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
        },
        {
          title: '采购订单行号',
          minWidth: 120,
          align: 'center',
          key: 'lineNo',
        },
        {
          title: 'POitem',
          width: 120,
          align: 'center',
          key: 'poItem',
        },
        {
          title: 'EQ ID',
          width: 120,
          align: 'center',
          key: 'eqId',
        },
        {
          title: '货物描述',
          width: 120,
          align: 'center',
          key: 'poDes',
        },

        {
          title: '采购数量',
          minWidth: 120,
          align: 'center',
          key: 'purchaseNum',
        },
        {
          title: '拆分数量',
          minWidth: 120,
          align: 'center',
          key: 'splitNum',
        },
        {
          title: '单价',
          minWidth: 120,
          align: 'center',
          key: 'decPrice',
        },
        {
          title: '金额',
          minWidth: 120,
          align: 'center',
          key: 'decTotal',
        },
        {
          title: '币制',
          minWidth: 120,
          align: 'center',
          key: 'curr',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('CURR_OUTDATED', value)}` : '' }
        },
        {
          title: '净重',
          minWidth: 120,
          align: 'center',
          key: 'netWt',
        },
        {
          title: '毛重',
          width: 120,
          align: 'center',
          key: 'grossWt',
        },
        {
          title: '单据内部编号',
          width: 120,
          align: 'center',
          key: 'emsListNo',
        },
        {
          title: '征免税',
          width: 120,
          align: 'center',
          key: 'taxation',
          valueFormatter: ({value}) => { return getKeyValue(productClassify.TAXATION_SELECT, value) }
        },
        {
          title: "批次状态",
          width: 180,
          key: "generateStatus",
          valueFormatter: ({value}) => {
            return getKeyValue(importExportManage.erpFreeBatchStatus, value)
          }
        },
        {
          title: '生成状态',
          width: 120,
          align: 'center',
          key: 'status',
          valueFormatter: ({value}) => { return getKeyValue(importExportManage.batchStatus, value) }
        },
        {
          title: '发票号',
          width: 120,
          align: 'center',
          key: 'invNo',
        },
        {
          title: '箱单号',
          width: 120,
          align: 'center',
          key: 'boxNo',
        },
        {
          title: '原产国',
          width: 120,
          align: 'center',
          key: 'originCountry',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('COUNTRY_OUTDATED', value)}` : '' }
        },
        {
          width: 120,
          key: 'filePath',
          title: '附件',
          render: (h, params) => {
            let value = params.row[params.column.key]
            if (isNullOrEmpty(value)) {
              return h('div', '')
            } else {
              return h('div', [
                h('a', {
                  props: {
                    type: 'primary'
                  },
                  style: {
                    marginLeft: '15px'
                  },
                  on: {
                    click: () => {
                      this.downloadOrderPdf(params.row.filePath)
                    }
                  }
                }, 'Y')
              ])
            }
          }
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableColumns: [],
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
