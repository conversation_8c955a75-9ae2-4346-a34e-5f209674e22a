<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getEnShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="分配指派">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="entrustForward" label="委托货代" class="dc-merge-1-4">
        <xdo-select v-model="entrustForward" :options="this.cmbSource.forwardCmb" :optionLabelRender="pcodeRender" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entrustSupplier" label="委托报关行" class="dc-merge-1-4">
        <xdo-select v-model="entrustCut" :options="this.cmbSource.cutCmb" :optionLabelRender="pcodeRender" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleEntrust">授权</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'permission',
    props: {
      enShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: String
      },
      type: {
        type: String
      },
    },
    data() {
      return {
        entrustForward: '',
        entrustSupplier: '',
        entrustCut: '',
        cmbSource:{
          forwardCmb:[],
          cutCmb:[],
          supplierCmb:[]
        },
        ajaxUrl:{
          entrust: csAPI.sapErp.batch.head.entrust,
        }
      }
    },
    computed: {
      getEnShow() {
        return this.enShow
      }
    },
    mounted() {
      let me = this;
      me.$http.post(csAPI.ieParams.FOD).then(res => {
        me.cmbSource.forwardCmb = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.forwardCmb = []
      })
      me.$http.post(csAPI.ieParams.PRD).then(res => {
        me.cmbSource.supplierCmb = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.supplierCmb = []
      })
      me.$http.post(csAPI.ieParams.CUT).then(res => {
        me.cmbSource.cutCmb = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.cutCmb = []
      })
    },
    methods: {
      handleVisible(val) {
        if (!val) {
          this.$emit('update:enShow', false)
        }
      },
      handleEntrust(){
        let me = this
        if (!me.entrustForward && !me.entrustCut){
          me.$Message.warning("请先选择指派的货代或者报关行！")
          return;
        }
        // if (!me.entrustCut){
        //   me.$Message.warning("请先选择指派的报关行！")
        //   return;
        // }
        me.$http.post(me.ajaxUrl.entrust,{
          sids:me.medata,
          forwardPermission:me.entrustForward,
          cutPermission:me.entrustCut,
          supplierPermission:me.entrustSupplier
        }).then( ()=> {
          me.$Message.success("保存成功")
        }).finally(()=>{
          me.entrustForward = ''
          this.$emit('onAfterEntrust',null)
        })
      }
    }
  }
</script>

<style scoped>

</style>

