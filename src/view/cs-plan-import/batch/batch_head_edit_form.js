import { editStatus } from '@/view/cs-common'
import { csAPI } from '@/api'
import {ArrayToLocaleLowerCase, isNullOrEmpty} from '@/libs/util'
import { productClassify,importExportManage } from '@/view/cs-common'

export default {
  data () {
    return {
      editSchema: {
        titleWidth: 120,
        class: 'dc-form dc-form-3'
      },
      overseasShipperList:[],
      declareCodeCustomsList:[],
      cutDataValueCode: {},
      trafModeList:[],
      editModel: {
        sid: '',
        purchaseNo: '',
        billNo: '',
        batchNo: '',
        forwardCode: '',
        supplierCode: '',
        invoiceNo: '',
        tradeTerms: '',
        bondMark: '',
        emsListNo: '',
        // originCountry: '',
        batchStatus: '0',
        eta:'',
        etd:'',
        trafMode:'0110',
        tradeCountry:'',
        despPort:'',
        overseasShipper:'',
        declareCodeCustoms:'',
        declareName:'',
        declareCode:'',
        overseasShipperName:'',
      },
      formRules: {
        batchNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
        tradeCountry:[{required: true, message: '不能为空！', trigger: 'blur'}],
        despPort:[{required: true, message: '不能为空！', trigger: 'blur'}],
        ieport:[{required: true, message: '不能为空！', trigger: 'blur'}],
        wrapType:[{required: true, message: '不能为空！', trigger: 'blur'}],
        trafMode:[{required: true, message: '不能为空！', trigger: 'blur'}],
        cutMode:[{required: true, message: '不能为空！', trigger: 'blur'}],
        declareCode: [
          {
            required: false,
            message: '当贸易条款为 DAP 时必填',
            trigger: 'blur'
          }
        ]
      },
      productClassify:productClassify,
      importExportManage:importExportManage,
      cmbSource: {
        forwardCode: [],
        supplierCode : []
      }
    }
  },
  mounted() {
    if (this.editOption.status !== editStatus.ADD) {
      this.editModel = Object.assign({}, this.editOption.data)
      this.editModel.dcrTimes = this.editOption.data.dcrTimes ? this.editOption.data.dcrTimes + '' : ''
      this.editModel.gmark = this.editOption.data.gmark && this.editOption.data.gmark.split(',')
    }

    this.$http.post(csAPI.ieParams.selectComboxByCode + '/PRD').then(res => {
      this.$set(this.cmbSource, 'supplierCode', ArrayToLocaleLowerCase(res.data.data))
    })
    this.$http.post(csAPI.ieParams.selectComboxByCode + '/FOD').then(res => {
      this.$set(this.cmbSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
    })
    // 收/发货人
    let me = this
    me.$http.post(csAPI.ieParams.PRD).then(res => {
      me.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
    }).catch(() => {
      me.overseasShipperList = []
    })
    me.$http.get('/api/pcode?type=TRANSF').then(res => {
      me.trafModeList = res.data.data['TRANSF'].filter((item) => {
        return !['3', '4', '6', 'G', 'H', 'L'].includes(item.CODE)
      }).map((item) => {
        return {
          value: item.CODE,
          label: item.NAME
        }
      })

    })
    // 报关行
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.declareCodeCustomsList = ArrayToLocaleLowerCase(res.data.data)
      let valueCodeObj = {}
      for (let item of res.data.data) {
        valueCodeObj[item.VALUE] = item['CODE']
      }
      me.$set(me, 'cutDataValueCode', valueCodeObj)
    }).catch(() => {
      me.declareCodeCustomsList = []
      me.$set(me, 'cutDataValueCode', {})
    })

  },
  watch: {
    'editModel.overseasShipper': {
      immediate: true,
      handler: function () {
        let me = this
        let theItems = me.overseasShipperList.filter(item => {
          return item.value === me.editModel.overseasShipper
        })
        if (Array.isArray(theItems) && theItems.length > 0) {
          me.$set(me.editModel, 'overseasShipperName', theItems[0].label)
        }
      }
    },
    'editModel.declareCodeCustoms': {
      immediate: true,
      handler: function () {
        let me = this
        let theItems = me.declareCodeCustomsList.filter(item => {
          return item.value === me.editModel.declareCodeCustoms
        })

        if (Array.isArray(theItems) && theItems.length > 0) {
          let declareCode = me.cutDataValueCode[theItems[0].value]
          me.$set(me.editModel, 'declareName', theItems[0].label)
          me.$set(me.editModel, 'declareCode', declareCode)
        }
      }
    },
  },
  computed: {
    declareCodeEnter() {
      let me = this
      let queryCode = me.editModel.declareCode
      if (isNullOrEmpty(queryCode) || queryCode.trim().length !== 10) {
        me.$set(me.editModel, 'declareName', '')
      } else {
        me.pcodeRemote(me.pcode.company, queryCode.trim()).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            let re_name = res[0].NAME
            me.$set(me.editModel, 'declareName', re_name)
          } else {
            me.$set(me.editModel, 'declareName', '')
          }
        })
      }
    },
    editDisabled () {
      return this.editOption.status === editStatus.SHOW
    },
    buttons () {
      return [
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSave, label: '保存' },
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSaveClose, label: '保存关闭' },
        { type: 'primary', disabled: false, loading: false, needed: true, click: this.handleBack, label: '返回' }
      ]
    },
    editElements () {
      return [
        // {
        //   title: '采购订单号', key: 'purchaseNo',
        //   attrs: {
        //     maxlength: 50
        //   }
        // },
        // {
        //   title: '填制要求备注', key: 'requiredRemarks',
        //   attrs: {
        //     maxlength: 255,
        //     disabled: true
        //   }
        // },
        // {
        //   title: '提运单号', key: 'billNo',
        //   attrs: {
        //     maxlength: 50
        //   }
        // },
        {
          title: '批次号', key: 'batchNo',
          attrs: {
            maxlength: 50,
            disabled: true
          }
        },
        {
          type:"select", key:"forwardCode", title: '货代',
          props: { options: this.cmbSource.forwardCode },
          attrs: {
            disabled: true
          }
        },
        {
          type:"select", key:"supplierCode", title: '供应商',
          props: { options: this.cmbSource.supplierCode },
          attrs: {
            disabled: true
          }
        },
        // {
        //   title: '发票号', key: 'invoiceNo',
        //   attrs: {
        //     maxlength: 50
        //   }
        // },
        {

          type:"select",title: '贸易条款', key: 'tradeTerms',
          props:{options : this.importExportManage.tradeTermList},
          attrs: {
            disabled: true
          },
          events: {  'on-change': this.handleChangeTradeTerms}
        },
        // {
        //   type:"select", key:"bondMark", title: '征免税',
        //   props: { options: this.productClassify.BONDED_FLAG_SELECT }
        // },
        // {
        //   title: '预录入单内部编号', key: 'emsListNo',
        //   attrs: {
        //     maxlength: 50
        //   }
        // },
        // {
        //   type: 'pcode', title: '原产国', key: 'originCountry',
        //   props: { meta: 'COUNTRY_OUTDATED' }
        // },
        // {
        //   type:"select",title: '批次状态', key: 'batchStatus',
        //   props:{options : this.importExportManage.batchStatusHead},
        //   attrs: {
        //     disabled: true
        //   }
        // },
        // {
        //   type:'datePicker',title: 'ETA', key: 'eta',
        // },
        // {
        //   type:'datePicker',title: 'ETD', key: 'etd',
        // },
        {
          type: 'select', title: '运输方式', key: 'trafMode',
          props: { options: this.trafModeList }
        },
        {
          type: 'pcode', title: '启运国（地区）', key: 'tradeCountry',
          props: { meta: 'COUNTRY_OUTDATED' }
        },
        {
          type: 'pcode', title: '启运港', key: 'despPort',
          props: { meta: 'PORT_LIN' }
        },
        {
          title: '报关单号', key: 'entryNo',
          attrs: {
            maxlength: 255,
            disabled: true
          }
        },
        {
          type: 'pcode', title: '监管方式', key: 'tradeMode',
          props: { meta: 'TRADE' }
        },
        {
          key: 'ieport', title: '进境关别', type: "pcode",
          props: {meta: 'CUSTOMS_REL'}
        },
        /*{
          key: 'overseasShipper', title: '境外发货人代码',type: "select",
          props: {options: this.overseasShipperList}

        },
        {
          key: 'overseasShipperName', title: '境外发货人名称',
          attrs: {
            disabled: true
          }
        },*/
        /*{
          key: 'trafName', title: '运输工具名称'
        },*/
        {
          key: 'cutMode', title: '征免性质', type: "pcode",
          props: {meta: 'LEVYTYPE'}
        },
        {
          key: 'wrapType', title: '包装种类', type: "pcode",
          props: {meta: 'WRAP'}
        },
        {
          key: 'transMode', title: '成交方式', type: "pcode",
          props: {meta: 'TRANSAC'}
        },
        // {
        //   key: 'exemptsNo', title: '免表编号'
        // },
        {
          key: 'note', title: '备注'
        },
        {
          type: 'datePicker', title: '进口日期', key: 'ieDate'
        },
        {
          key: 'mawb', title: '主提运单号'
        },
        {
          key: 'hawb', title: '分提运单号'
        }/*,{
          type: 'select',
          title: '报关行简码及名称',
          key: 'declareCodeCustoms',
          props: {options: this.declareCodeCustomsList},
        }*/, {
          key: 'declareCode',
          title: '报关行十位编码',
          slot: {
            append: ''
          },
          props: {
            maxlength: 10
          },
        }, {
          key: 'declareName',
          title: '报关行名称',
          props: {
            disabled: true
          },
          on: {
            enter: this.declareCodeEnter
          }
        },
      ]
    }
  }
}
