<template>
  <XdoModal width="600" mask v-model="show" title="分配指派"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm class="xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="logistics" label="物流公司">
          <xdo-select v-model="frmData.logistics" :options="this.cmbSource.logistics"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="payNo" label="送货地点">
          <XdoIInput type="text" v-model="frmData.payNo" :maxlength="50"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="payDate" label="送达时间">
          <XdoDatePicker type="date" v-model="frmData.payDate" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'deliverySchedulePop',
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnCom = {
        type: 'primary',
        disabled: false,
        loading: false,
        needed: true
      }
      return {
        rulesHeader: {},
        frmData: {
          logistics: '',
          payNo: '',
          payDate: ''
        },
        buttons: [
          {...btnCom, click: this.handleConfirm, icon: 'dc-btn-save', label: '保存'},
          {...btnCom, click: this.handleClose, icon: 'dc-btn-cancel', label: '关闭'}
        ],
        cmbSource: {
          logistics: [{
            value: 'FS', label: '顺丰'
          }, {
            value: 'JD', label: '京东'
          }, {
            value: 'YT', label: '圆通'
          }, {
            value: 'ST', label: '申通'
          }, {
            value: 'UPS', label: '美国联合包裹运送服务公司'
          }, {
            value: 'FedEX', label: '联邦快递公司'
          }, {
            value: 'Deutsche Post World Net', label: '德国邮政世界网'
          }, {
            value: 'A.P. Moller-Maersk Group', label: '马士基集团'
          }, {
            value: 'Nippon Express', label: '日本运通公司'
          }, {
            value: 'Ryder System', label: '莱德系统'
          }, {
            value: 'TNT Post Group', label: 'TNT快递公司'
          }, {
            value: 'Expeditors International', label: '康捷国际公司'
          }, {
            value: 'Panalpina', label: '泛亚班拿'
          }, {
            value: 'Exel', label: '英运物流'
          }]
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            this.$set(this.frmData, 'logistics', '')
            this.$set(this.frmData, 'payNo', '')
            this.$set(this.frmData, 'payDate', '')
          }
        }
      }
    },
    methods: {
      handleClose() {
        this.$emit('update:show', false)
      },
      handleConfirm() {
        this.$emit('doBackFill', {
          payNo: this.frmData.payNo,
          payDate: this.frmData.payDate,
          logistics: this.frmData.logistics
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
