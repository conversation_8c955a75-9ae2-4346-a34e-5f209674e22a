<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getEnShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="分配指派">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="entrustForward" label="委托货代" class="dc-merge-1-4">
        <xdo-select v-model="entrustForward" :options="this.cmbSource.forwardCmb" :optionLabelRender="pcodeRender" @on-change="forwardChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entrustUserSid" label="用户名" class="dc-merge-1-4">
        <xdo-select v-model="entrustUserSid" :options="this.cmbSource.userCmb" :optionLabelRender="(item) => item.label" :disabled="entrustUserEnable"></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleEntrust">授权货代</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"
  import {ArrayToLocaleLowerCase, isNullOrEmpty} from '@/libs/util'

  export default {
    name: 'PurchaseOrderEntrust',
    props: {
      enShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: String
      },
      linkedNos:{
        type: Array,
        default: () => ([])
      }
    },
    data() {
      return {
        entrustForward: '',
        entrustUserSid:'',
        entrustUserEnable:true,
        cmbSource:{
          forwardCmb:[],
          userCmb:[]
        },
        ajaxUrl:{
          entrust: csAPI.sapErp.shippingErpDecIList.entrust,
        }
      }
    },
    watch: {
      'entrustForward': {
        immediate: true,
        handler: function(val) {
          if (!val) {
            this.entrustUserSid = ''
            this.entrustUserEnable = true
          }
        }
      }
    },
    computed: {
      getEnShow() {
        return this.enShow
      }
    },
    mounted() {
      let me = this;
      me.$http.post(csAPI.ieParams.FOD).then(res => {
        me.cmbSource.forwardCmb = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.forwardCmb = []
      })
    },
    methods: {
      handleVisible(val) {
        const me = this
        if (!val) {
          me.$emit('update:enShow', false)
          return
        }
        // 带出货代信息：相同则带出其值，否则初始为空
        const data = me.$parent.gridConfig.data
        if (!data || !data.length || data.length < 1) {
          return
        }
        const selectedData = data.filter(item => me.medata.includes(item.sid))
        if (selectedData.length < 1) {
          return
        }
        if (selectedData.findIndex(item => item.isAssign !== '1') !== -1) {
          me.entrustForward = ''
          me.entrustUserSid = ''
          return
        }
        const anyData = selectedData[0]
        if (selectedData.filter(item => item['forwardUserSid'] === anyData['forwardUserSid']).length < selectedData.length) {
          me.entrustForward = ''
          me.entrustUserSid = ''
          return
        }
        me.entrustForward = anyData['forwardCode']
        me.entrustUserSid = anyData['forwardUserSid']
      },
      handleEntrust(){
        let me = this
        if (!me.entrustForward){
          me.$Message.warning("请先选择指派的货代！")
          return;
        }
        if (!me.entrustUserSid){
          me.$Message.warning("请先选择指派的用户！")
          return;
        }
        //指派用户信息
        let entrustUserNo = ''
        let entrustUser = me.cmbSource.userCmb.find(item => item.value === me.entrustUserSid)
        if(!isNullOrEmpty(entrustUser)){
          entrustUserNo = entrustUser.label
        }
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          content: '是否确认授权给货代：'+ entrustUserNo,
          cancelText: '取消',
          onOk: () => {
            me.$http.post(me.ajaxUrl.entrust + '/' + me.medata,
              { forwardCode:me.entrustForward,
                forwardUserNo:entrustUserNo,
                forwardUserSid:me.entrustUserSid,
                linkedNoList:me.linkedNos
              }
            ).then( ()=> {
              me.$Message.success("保存成功")
            }).finally(()=>{
              me.entrustForward = ''
              me.entrustUserSid = ''
              me.entrustUserEnable = true
              this.$emit('onAfterEntrust',null)
            })
          }
        })
      },
      //选择了委托货代
      forwardChange(){
        let me = this
        //委托货代不是空
        if(!isNullOrEmpty(me.entrustForward)){
          me.entrustUserEnable = false
          me.$http.post(csAPI.customsClearanceRiskSetting.proxyUserSettings.getUserNoByForward,
            {forwardAlias:me.entrustForward,}).then(res => {
            if(res.data.success){
              me.cmbSource.userCmb = res.data.data.map(item => {
                return {
                    value: item.sid,
                    label: item.userNo
                  }
              })
            }

          }).catch(() => {
            me.cmbSource.userCmb = []
          })
        }
      }
    }
  }
</script>

<style scoped>

</style>

