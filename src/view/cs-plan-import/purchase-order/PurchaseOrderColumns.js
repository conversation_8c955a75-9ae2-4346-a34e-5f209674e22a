import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'
import {isNullOrEmpty, isNumber} from '@/libs/util'
import {csAPI} from "@/api"
import { importPlan } from '@/view/cs-common/constant'


const commColumns = [
   'sid',
   'linkedNo',
   'orderCheckTime',
   'lineNo',
   'hawb',
   'batchNo',
   'facGNo',
   'purchaseNum',
   'purchaseCount',
   'splitNum',
   'decTotal',
   'totalPrice',
   'splitTotal',
   'curr',
   'newWt',
   'grossWt',
   'originCountry',
   'batchStatus',
   'isOrigin',
   'splitTime',
   'forwardCode',
   'supplierCode',
   'cylinderNo',
   'invoiceNo',
   'tradeTerms',
  'decPrice',
  'billType',
  'modifyMark',
  'filePath',
  'poStatus',
  'isAssign',
  'forwardUserNo'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          title: '订单状态',
          width: 120,
          align: 'center',
          key: 'poStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.poStatus)
          }
        },{
          title: '采购订单号',
          width: 120,
          align: 'center',
          key: 'linkedNo'
        },
        {
          title: '订单确认时间',
          width: 120,
          align: 'center',
          key: 'orderCheckTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '采购订单序号',
          width: 120,
          align: 'center',
          key: 'lineNo'
        }/*,{
          title: '提运单号',
          width: 120,
          align: 'center',
          key: 'hawb'
        }*/, {
          title: '批次号',
          width: 120,
          align: 'center',
          key: 'batchNo',
          render: (h,params) => {
            return h('div', [
              //添加查看按钮
              h('a', {
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.jumpTo()
                  },
                },
              }, params.row.batchNo)
            ])
          },
        }, {
          title: '企业料号',
          width: 200,
          align: 'center',
          key: 'facGNo'
        }, {
          title: '剩余数量',
          width: 120,
          align: 'center',
          key: 'purchaseNum'
        },  {
          title: '采购数量',
          width: 120,
          align: 'center',
          key: 'purchaseCount'
        }, {
          title: '拆分数量',
          width: 120,
          align: 'center',
          key: 'splitNum',
          editable: true,
          onCellValueChanged: this.onCellValueChanged,
          headerClass: 'split-title'
        },
        {
          title: '单价',
          width: 120,
          align: 'center',
          key: 'decPrice'
        },
        {
          title: '剩余金额',
          width: 120,
          align: 'center',
          key: 'decTotal'
        },
        {
          title: '金额',
          width: 120,
          align: 'center',
          key: 'totalPrice'
        },
        {
          title: '拆分金额',
          width: 120,
          align: 'center',
          key: 'splitTotal',
          editable: true,
          onCellValueChanged: this.onCellValueChanged2
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '净重',
          width: 120,
          align: 'center',
          key: 'netWt'
        }, {
          title: '毛重',
          width: 120,
          align: 'center',
          key: 'grossWt'
        }/*, {
          title: '原产国',
          width: 120,
          align: 'center',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }*/, {
          title: '生成批次状态',
          width: 120,
          align: 'center',
          key: 'batchStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.batchStatus)
          }
        }, {
          title: '是否原始订单数据',
          width: 200,
          align: 'center',
          key: 'isOrigin',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.isOrigin)
          }
        }, {
          title: '拆分时间',
          width: 120,
          align: 'center',
          key: 'splitTime'
        }, {
          title: '供应商代码',
          width: 120,
          align: 'center',
          key: 'supplierCode'
        }, {
          title: '供应商名称',
          width: 120,
          align: 'center',
          key: 'supplierName'
        }/*, {
          title: '发票号',
          width: 120,
          align: 'center',
          key: 'invoiceNo'
        }*/, {
          title: '钢瓶号',
          width: 120,
          align: 'center',
          key: 'cylinderNo'
        }, {
          title: '贸易条款',
          width: 150,
          align: 'center',
          key: 'tradeTerms',
          // render: (h, params) => {
          //   return this.cmbShowRender(h, params, this.importExportManage.tradeTermList)
          // }
        }, {
          title: '数据状态',
          width: 120,
          align: 'center',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.MODIFY_MARK_SELECT)
          }
        }
        , {
          title: '订单类型',
          width: 120,
          align: 'center',
          key: 'billType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.erpBillType)
          }
        }
        , {
          title: '标准/寄售',
          width: 120,
          align: 'center',
          key: 'standardOrConsignment',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.standardOrConsignmentType)
          }
        }
        , {
          title: '采购类型',
          width: 120,
          align: 'center',
          key: 'proCategory',
        },
        {
          title: '是否已指派',
          width: 110,
          align: 'center',
          key: 'isAssign',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.isAssign)
          }
        }
        , {
          title: '委托指派用户',
          width: 120,
          align: 'center',
          key: 'forwardUserNo',
        },
        {
          width: 200,
          key: 'filePath',
          title: '附件',
          render: (h, params) => {
            let value = params.row[params.column.key]
            if (isNullOrEmpty(value)) {
              return h('span', '')
            } else {
              return h('div', [
                h('a', {
                  props: {
                    type: 'primary'
                  },
                  style: {
                    marginLeft: '15px'
                  },
                  on: {
                    click: () => {
                      this.downloadOrderPdf(params.row.filePath)
                    }
                  }
                }, 'Y')
              ])
            }
          }
        }
      ]
    }
  },

  methods: {
    getPoStatusDesc(poStatus) {
      const { orderStatus } = importPlan
      for (const key of Object.keys(orderStatus)) {
        if (orderStatus[key].value === poStatus) {
          return orderStatus[key].label
        }
      }
      return ''
    },
    onCellValueChanged(event) {
      let me = this
      if (event.data.billType !== 'NB') {
        me.$Message.warning("该类型订单不能拆分数量")
        me.getList()
        return
      }
      const { orderStatus } = importPlan
      const poStatus = event.data.poStatus
      if (poStatus === orderStatus.cancelled.value || poStatus === orderStatus.closed.value
        || poStatus === orderStatus.timeoutClosed.value) {
        me.$Message.error(`当前订单状态是${me.getPoStatusDesc(poStatus)}，不支持拆单!`)
        me.getList()
        return
      }
      if (event.data.batchStatus !== '1' && !isNullOrEmpty(event.oldValue) && event.oldValue !== 0) {
        me.$Message.warning('当前状态不允许拆分!')
        me.getList()
        return
      }
      const splitFunction = () => {
        if (isNumber(event.newValue)) {
          let num = Number(event.newValue)
          if (isNullOrEmpty(event.oldValue) || event.oldValue === 0 || event.data.batchStatus === '1') {
            if (num > 0) {
              if (num > event.data.purchaseNum) {
                me.$Message.warning('拆分数量不能大于剩余数量')
              } else {
                me.$http.post(csAPI.sapErp.shippingErpDecIList.split, event.data).then(() => {
                  me.$Message.success('拆分成功')
                }).finally(() => {
                  me.getList()
                })
              }
            } else {
              me.$Message.warning('拆分数量不能为负数')
              me.getList()
            }
          } else {
            me.$Message.warning('当前状态不允许拆分')
            me.getList()
          }
        } else {
          me.$Message.warning('只能输入数字')
          me.getList()
        }
      }
      if (poStatus === orderStatus.pass.value) {
        splitFunction()
      } else if (poStatus === orderStatus.done.value) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '当前采购订单已收货完成，请确认是否进行拆单？',
          onOk: splitFunction,
          onCancel: () => {
            me.getList()
          }
        })
      }
    },
    onCellValueChanged2(event) {
      let me = this
      if (event.data.billType !== 'ZCT') {
        me.$Message.warning("该类型订单不能拆分金额")
        me.getList()
        return
      }
      const { orderStatus } = importPlan
      const poStatus = event.data.poStatus
      if (poStatus === orderStatus.cancelled.value || poStatus === orderStatus.closed.value
        || poStatus === orderStatus.timeoutClosed.value) {
        me.$Message.error(`当前订单状态是${me.getPoStatusDesc(poStatus)}，不支持拆单!`)
        me.getList()
        return
      }
      if (event.data.batchStatus !== '1' && !isNullOrEmpty(event.oldValue) && event.oldValue !== 0) {
        me.$Message.warning('当前状态不允许拆分!')
        me.getList()
        return
      }
      const splitFunction = () => {
        if (isNumber(event.newValue)) {
          let num = Number(event.newValue)
          if (isNullOrEmpty(event.oldValue) || event.oldValue === 0 || event.data.batchStatus === '1') {
            if (num > 0) {
              if (num > event.data.decTotal) {
                me.$Message.warning("拆分金额不能大于剩余金额")
              } else {
                me.$http.post(csAPI.sapErp.shippingErpDecIList.split, event.data).then(() => {
                  me.$Message.success("拆分成功")
                }).finally(() => {
                  me.getList()
                })
              }
            } else {
              me.$Message.warning("拆分金额不能为负数")
              me.getList()
            }
          } else {
            me.$Message.warning("当前状态不允许拆分")
            me.getList()
          }
        } else {
          me.$Message.warning("只能输入数字")
          me.getList()
        }
      }
      if (poStatus === orderStatus.pass.value) {
        splitFunction()
      } else if (poStatus === orderStatus.done.value) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '当前采购订单已收货完成，请确认是否进行拆单？',
          onOk: splitFunction,
          onCancel: () => {
            me.getList()
          }
        })
      }
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
