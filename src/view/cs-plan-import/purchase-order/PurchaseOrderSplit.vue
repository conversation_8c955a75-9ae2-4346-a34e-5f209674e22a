<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getSpShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="拆分">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="canSplitNum" label="可拆分数量" class="dc-merge-1-4">
        <dc-numberInput v-model="canSplitNum" integerDigits="13" precision="8" :disabled="true" ></dc-numberInput>
      </XdoFormItem>
      <XdoFormItem prop="splitNum" label="拆分数量" class="dc-merge-1-4">
        <dc-numberInput v-model="splitNum" integerDigits="13" precision="8" :disabled="false" ></dc-numberInput>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleSplit">确认拆分</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"


  export default {
    name: 'PurchaseOrderSplit',
    props: {
      spShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: Object
      },
    },
    data() {
      return {

        canSplitNum:'',
        splitNum:'',
        ajaxUrl:{
          split: csAPI.sapErp.shippingErpDecIList.split,
        }
      }
    },
    computed: {
      getSpShow() {
        return this.spShow
      }
    },

    watch: {
      spShow: {
          deep: true,
          immediate:true,
          handler:function() {
            let me = this
            if(me.spShow){
              if(!me.medata.splitNum || me.medata.splitNum == 0){
                me.canSplitNum = me.medata.purchaseNum
              }else {
                me.canSplitNum = me.medata.splitNum
              }
            }
        }
      }
    },
    methods: {
      handleVisible(val) {
        if (!val) {
          this.$emit('update:spShow', false)
        }
      },
      handleSplit(){
        let me = this
        if (!me.splitNum){
          me.$Message.warning("请先填写拆分数量！")
          return;
        }
        if(me.splitNum > me.canSplitNum){
          me.$Message.warning("拆分数量余量不足！")
          return;
        }

        me.$http.post(me.ajaxUrl.split ,{sid:me.medata.sid,splitNum:me.splitNum}).then( ()=> {
          me.$Message.success("拆分成功")
        }).finally(()=>{
          me.splitNum = ''
          this.$emit('onAfterSplit',null)
        })
      }
    }
  }
</script>

<style scoped>

</style>

