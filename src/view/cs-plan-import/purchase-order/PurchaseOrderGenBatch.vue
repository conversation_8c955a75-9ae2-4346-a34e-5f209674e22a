<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getGenShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="生成批次">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="entrustForward" label="征免税" class="dc-merge-1-4">
        <xdo-select v-model="bondMark" :options="this.productClassify.BONDED_FLAG_SELECT" :optionLabelRender="pcodeRender" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleComfirm">生成批次</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'PurchaseOrderGenBatch',
    props: {
      genShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: String
      },
    },
    data() {
      return {
        productClassify:productClassify,
        bondMark: '',
        ajaxUrl:{
          genBatch: csAPI.sapErp.shippingErpDecIList.genBatch,
        }
      }
    },
    computed: {
      getGenShow() {
        return this.genShow
      }
    },
    methods: {
      handleVisible(val) {
        if (!val) {
          this.$emit('update:genShow', false)
        }
      },
      handleComfirm(){
        let me = this
        //console.log(me.medata[0])
        if (!me.bondMark){
          me.$Message.warning("请先选择征免税！")
          return;
        }
        me.$http.post(me.ajaxUrl.genBatch + '/' + me.medata,{ bondMark:me.bondMark }).then( () => {
            me.$Message.success("生成成功")
        }).finally(()=>{
          me.bondMark = ''
          this.$emit('onAfterGenBatch',null)
        })
      }
    }
  }
</script>

<style scoped>

</style>

