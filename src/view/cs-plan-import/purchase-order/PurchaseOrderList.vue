<template>
  <section>
    <purchaseOrderEntrust :enShow.sync="enShow" :medata="enSids"  :linkedNos="linkedNoList" @onAfterEntrust="onAfterEntrust">
    </purchaseOrderEntrust>
    <!--    <purchaseOrderGenBatch :genShow.sync="genShow" :medata="genSids" @onAfterGenBatch="onAfterGenBatch">-->
    <!--    </purchaseOrderGenBatch>-->
    <purchaseOrderSplit :spShow.sync="spShow" :medata="spData" @onAfterSplit="onAfterSplit">
    </purchaseOrderSplit>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <purchaseOrderSearch ref="headSearch"></purchaseOrderSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <dcAgGridNoEdit ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                        @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"
                        :getRowStyle="getRowStyle" />
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="totalColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
<!--    <gwPermission :gwShow.sync="gwShow" :gwSids="this.gwsids"  @onAfterGwEntrust="onAfterGwEntrust"></gwPermission>-->
    <purchaseOrderEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></purchaseOrderEdit>
  </section>
</template>


<script>
import {csAPI} from '@/api'
import pms from '@/libs/pms'
import {csBaseInfoList} from '../../cs-base-info/base/js/csBaseInfoList'
import {getColumnsByConfig, getExcelColumnsByConfig} from '@/common'
import {columnsConfig, excelColumnsConfig, columns} from './PurchaseOrderColumns'
import purchaseOrderEdit from './PurchaseOrderEdit'
import purchaseOrderSearch from './PurchaseOrderSearch'
import purchaseOrderEntrust from './PurchaseOrderEntrust'
import purchaseOrderSplit from './PurchaseOrderSplit'
// import gwPermission from "@/view/cs-plan-import/purchase-order/gwPermission";
import {importExportManage, productClassify} from '@/view/cs-common'
import {editStatus} from '@/view/cs-common'
import {isNullOrEmpty} from "@/libs/util";
import dcAgGridNoEdit from "@/components/dc-ag-grid/dc-ag-grid-no-edit.vue";
export default {
  name: 'purchaseOrderList',
  components: {
    purchaseOrderEdit,
    purchaseOrderSearch,
    purchaseOrderEntrust,
    purchaseOrderSplit,
    dcAgGridNoEdit,
    // gwPermission
    // purchaseOrderGenBatch
  },
  mixins: [csBaseInfoList, columns, pms, productClassify],
  data() {
    return {
      gridConfig: {
        exportTitle: '采购订单'
      },
      listSetupShow: false,
      importExportManage: importExportManage,
      productClassify: productClassify,
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'split': this.handleSplit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup,
        'gen': this.handleGen,
        'entrust': this.handleEntrust,
        'back': this.handleBack,
      },
      ajaxUrl: {
        selectAllPaged: csAPI.sapErp.shippingErpDecIList.selectAllPaged,
        exportUrl: csAPI.sapErp.shippingErpDecIList.exportUrl,
        checkGen: csAPI.sapErp.shippingErpDecIList.checkGen,
        deleteUrl: csAPI.sapErp.shippingErpDecIList.delete,
        genBatch: csAPI.sapErp.shippingErpDecIList.genBatch,
        checkBatch: csAPI.sapErp.shippingErpDecIList.checkBatch,
        back: csAPI.sapErp.shippingErpDecIList.back,
        downloadFile: csAPI.sapErp.shippingErpDecIList.downloadFile,
        down: csAPI.sapErp.shippingErpDecIList.down,
      },
      enShow: false,
      enSids: [],
      linkedNoList: [],
      spShow: false,
      spData: {},
      gwsids:[],
      gwShow:false,
      // genShow:false,
      // genSids:[],
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
    me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)

    me.listId = me.$route.path + '/' + me.$options.name
    let columns = me.$bom3.showTableColumns(me.listId, me.totalColumns)
    me.handleUpdateColumn(columns)
  },
  methods: {
    //跳转页面
    jumpTo(){
      let me = this
      let batchNo = me.gridConfig.selectData.batchNo
      if (me.$store.state.tag.tagNavList) {
        let index = me.$store.state.tag.tagNavList.findIndex(item => item.name === 'BatchHead');
        if (index != -1) {
          me.$store.state.tag.tagNavList.splice(index, 1);
        }
      }
      debugger
      me.$router.push({ name:'BatchHead', params:{batchNo:batchNo}})
    },
    /**
     * 附件下载
     * @param row
     *
     */
    downloadOrderPdf(filePath){
      let me = this
      console.log(filePath)
      me.$http.post(me.ajaxUrl.down+ '/' + filePath).then(res => {
        const resultMap = new Map(Object.entries(res.data));

        if (resultMap.size !== 0) {
          resultMap.forEach((value, key) => {
            window.open(key)
            // let link = document.createElement('a');
            // fetch(key)
            //   .then(res => {
            //     if (!res.ok) {
            //       throw new Error('下载文件失败');
            //     }
            //     return res.blob();
            //   })
            //   .then(blob => {
            //     link.href = URL.createObjectURL(blob);
            //     link.download = value; //文件名
            //     document.body.appendChild(link);
            //     link.click();
            //     document.body.removeChild(link);
            //     URL.revokeObjectURL(link.href);
            //   })
            //   .catch(error => {
            //     console.error('下载文件失败:', error);
            //   });
          });
        }else {
          me.$Message.error('未查询到相应文件')
        }
      })
    },

    onAfterGwEntrust() {
      this.gwShow = false
      this.getList()
    },
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.gridConfig.gridColumns = [...columns]
      me.$set(me, 'exportColumns', columns.map(column => {
        return {
          key: column.key,
          value: column.title
        }
      }))
    },
    handleEditByRow(row) {
      let me = this
      if (row.splitNum !== 0 && !isNullOrEmpty(row.splitNum)) {
        me.$Message.warning("已拆分数据不能编辑")
      } else {
        if (me.customCheck([row], '编辑')) {
          me.editConfig.editStatus = editStatus.EDIT
          me.editConfig.editData = row
          me.showList = false
        }
      }
    },
    handleDelete() {
      let me = this
      // let canNotDelete = me.gridConfig.selectRows.filter(x => {
      //   return x.isOrigin == '1' || x.originSid == null
      // })
      // if(canNotDelete.length > 0){
      //   me.$Message.warning("订单原始数据不能删除")
      //   return;
      // }
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleBack() {
      let me = this
      if (me.gridConfig.selectRows.length != 1) {
        me.$Message.warning("请选择一条数据")
        return;
      }
      if (me.gridConfig.selectRows[0].batchStatus == '1') {
        me.$Message.warning("已生成批次数据不能恢复")
        return;
      }
      let sid = me.gridConfig.selectRows[0].sid

      me.$http.post(me.ajaxUrl.back + '/' + sid).then(() => {
        me.$Message.success("恢复成功")
      }).finally(() => {
        me.getList()
      })
    },
    handleGen() {
      let me = this
      if (me.gridConfig.selectRows.length === 0) {
        me.$Message.warning("请先选择数据")
        return;
      }

      me.gwsids = me.gridConfig.selectRows.map(row => {
        return row.sid
      })
      me.setToolbarLoading('gen', true)
      me.$http.post(me.ajaxUrl.checkBatch + '/' + me.gwsids).then(res => {
        if (res.data.success) {
          if (!isNullOrEmpty(res.data.message)){
            me.$Message.warning({
              content:res.data.message,
              duration:5
            })
          }
          me.$http.post(me.ajaxUrl.genBatch + '/' + me.gwsids).then(() => {
            me.$Message.success({
              content:'生成成功',
              duration:5
            })
            // this.$emit('onAfterGwEntrust',null)
          }).finally(() => {
            //me.getList()
            me.setToolbarLoading('gen')
            me.getList()
          })
        }
        // this.getList()
      })
      // me.gwShow = true
    },
    // onAfterGenBatch() {
    //   this.genShow = false
    //   this.getList()
    // },
    handleEntrust() {
      let me = this
      if (me.gridConfig.selectRows.length == 0) {
        me.$Message.warning("请先选择数据")
        return;
      }
      me.enShow = true
      console.log(me.gridConfig.selectRows)
      me.enSids = me.gridConfig.selectRows.map(row => {
        return row.sid
      })
      me.linkedNoList = me.gridConfig.selectRows.map(row => {
        return row.linkedNo
      })
    },
    onAfterEntrust() {
      this.enShow = false
      this.getList()
    },
    handleSplit() {
      let me = this
      if (me.gridConfig.selectRows.length != 1) {
        me.$Message.warning("请选择一条数据")
        return;
      }
      if (me.gridConfig.selectRows[0].isOrigin != '1') {
        me.$Message.warning("非订单原始数据不能拆分")
        return;
      }
      me.spShow = true
      me.spData = me.gridConfig.selectRows[0]
    },
    onAfterSplit() {
      this.spShow = false
      this.getList()
    },
    getRowStyle(row) {
      if (row.data.isAssign === '0') {
        return {
          'background-color': '#E3F0E1'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}

.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}

/deep/ .split-title{
  background-color: #8DBED5;
}
</style>
