/***
 * 关务-进口计划-路由
 */
import { namespace } from '@/project'
import PurchaseOrderList from './purchase-order/PurchaseOrderList'
import DeliveryScheduleList from './delivery-schedule/delivery-schedule-list'
import ConfirmationOfReceiptList from './confirmation-of-receipt/confirmation-of-receipt-list'
import WarehouseUnreceivedDetailsList from './warehouse-unreceived-details/warehouse-unreceived-details-list'
import BatchHead from './batch/BatchHead'

export default [
  {
    path: '/' + namespace + '/planImport/purchaseOrderList',
    name: 'purchaseOrderList',
    meta: {
      title: '采购订单管理'
    },
    component: PurchaseOrderList
  },
  {
    path: '/' + namespace + '/planImport/deliveryScheduleList',
    name: 'deliveryScheduleList',
    meta: {
      title: '进口送货排程'
    },
    component: DeliveryScheduleList
  },
  {
    path: '/' + namespace + '/planImport/confirmationOfReceiptList',
    name: 'confirmationOfReceiptList',
    meta: {
      title: '进口收货确认'
    },
    component: ConfirmationOfReceiptList
  },
  {
    path: '/' + namespace + '/planImport/warehouseUnreceivedDetailsList',
    name: 'warehouseUnreceivedDetailsList',
    meta: {
      title: '外仓未收货明细'
    },
    component: WarehouseUnreceivedDetailsList
  },
  {
    path: '/' + namespace + '/planImport/batch',
    name: 'BatchHead',
    meta: {
      title: '订单批次管理'
    },
    component: BatchHead
  }
]
