import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { erpInterfaceData, importExportManage, productClassify, interimVerification } from '@/view/cs-common'

export const deliverySchedule = {
  name: 'deliverySchedule',
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      cmbSource: {
        inWay: erpInterfaceData.IMPORT_METHOD_MAP,
        gmarkConvert: productClassify.GMARK_SELECT,
        status: erpInterfaceData.EXTRACT_STATUS_MAP,
        modifyMark: erpInterfaceData.DATA_STATUS_MAP,
        bondMarkConvert: importExportManage.bondedFlagMap,
        logistics: [{
          value: 'FS', label: '顺丰'
        }, {
          value: 'JD', label: '京东'
        }, {
          value: 'YT', label: '圆通'
        }, {
          value: 'ST', label: '申通'
        }, {
          value: 'UPS', label: '美国联合包裹运送服务公司'
        }, {
          value: 'FedEX', label: '联邦快递公司'
        }, {
          value: 'Deutsche Post World Net', label: '德国邮政世界网'
        }, {
          value: 'A.P. Moller-Maersk Group', label: '马士基集团'
        }, {
          value: 'Nippon Express', label: '日本运通公司'
        }, {
          value: 'Ryder System', label: '莱德系统'
        }, {
          value: 'TNT Post Group', label: 'TNT快递公司'
        }, {
          value: 'Expeditors International', label: '康捷国际公司'
        }, {
          value: 'Panalpina', label: '泛亚班拿'
        }, {
          value: 'Exel', label: '英运物流'
        }]
      },
      toolbarEventMap: {
        'distribution': this.handleDistribution,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      },
      productClassify: productClassify,
      erpInterfaceData: erpInterfaceData,
      importExportManage: importExportManage,
      interimVerification: interimVerification
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = []
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '分配指派',
        key: 'xdo-btn-delete',
        icon: 'ios-git-branch',
        command: 'distribution'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      })
    },
    getCommParams() {
      return [{
        key: 'linkedNo',
        title: '采购订单号'
      }, {
        key: 'status',
        title: '提取状态',
        type: 'select'
      }, {
        key: 'modifyMark',
        title: '数据状态',
        type: 'select'
      }, {
        key: 'bondMarkConvert',
        title: '转换后保完税标记',
        type: 'select'
      }, {
        key: 'gmarkConvert',
        title: '转换后物料类型',
        type: 'select'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGNo',
        title: '备案料号'
      }, {
        key: 'invoiceNo',
        title: '发票号'
      }, {
        key: 'purchaseNo',
        title: '采购订单号'
      }, {
        key: 'supplierName',
        title: '供应商名称'
      }, {
        key: 'inWay',
        title: '进口方式',
        type: 'select'
      }, {
        range: true,
        title: '接收时间',
        key: 'insertTime'
      }, {
        range: true,
        title: '订单日期',
        key: 'orderDate'
      }, {
        title: '单价',
        key: 'decPrice',
        type: 'checkBox',
        props: {
          label: '0或空',
          'true-value': 1,
          'false-value': null
        },
        defaultValue: null
      }, {
        key: 'netWt',
        title: '净重',
        type: 'checkBox',
        props: {
          label: '0或空',
          'true-value': 1,
          'false-value': null
        },
        defaultValue: null
      }, {
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        key: 'logistics',
        title: '运输公司',
        type: 'select'
      }]
    },
    getCommFields() {
      return [{
        title: '采购订单号',
        width: 120,
        align: 'center',
        key: 'linkedNo'
      }, {
        title: '订单序号',
        width: 120,
        align: 'center',
        key: 'lineNo'
      }, {
        title: '提取状态',
        width: 90,
        align: 'center',
        key: 'status',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
        }
      }, {
        title: '数据状态',
        width: 120,
        align: 'center',
        key: 'modifyMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
        }
      }, {
        title: '运输公司',
        width: 90,
        align: 'center',
        key: 'logistics',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.logistics)
        }
      }, {
        title: '保完税标记',
        width: 100,
        align: 'center',
        key: 'bondMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
        }
      }, {
        title: '转换后保完税标记',
        width: 160,
        align: 'center',
        key: 'bondMarkConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.bondedFlagMap)
        }
      }, {
        title: '物料类型标志',
        width: 120,
        align: 'center',
        key: 'gmark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        title: '转换后物料类型标志',
        width: 160,
        align: 'center',
        key: 'gmarkConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        title: '企业料号',
        width: 200,
        align: 'center',
        key: 'facGNo'
      }, {
        title: '备案料号',
        width: 200,
        align: 'center',
        key: 'copGNo'
      }, {
        title: '数量',
        width: 120,
        align: 'center',
        key: 'qty'
      }, {
        title: '单价',
        width: 120,
        align: 'center',
        key: 'decPrice'
      }, {
        title: '总价',
        width: 120,
        align: 'center',
        key: 'decTotal'
      }, {
        title: 'ERP交易单位',
        width: 120,
        align: 'center',
        key: 'unitErp'
      }, {
        title: '币制',
        width: 120,
        align: 'center',
        key: 'curr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        title: '转换后币制',
        width: 120,
        align: 'center',
        key: 'currConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        title: '净重',
        width: 120,
        align: 'center',
        key: 'netWt'
      }, {
        title: '毛重',
        width: 120,
        align: 'center',
        key: 'grossWt'
      }, {
        title: '原产国',
        width: 120,
        align: 'center',
        key: 'originCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        title: '转换后原产国',
        width: 120,
        align: 'center',
        key: 'originCountryConvert'
      }, {
        title: '发票号',
        width: 120,
        align: 'center',
        key: 'invoiceNo'
      }, {
        title: '采购单号',
        width: 120,
        align: 'center',
        key: 'purchaseNo'
      }, {
        title: '采购单行号',
        width: 120,
        align: 'center',
        key: 'purchaseLineNo'
      }, {
        title: '采购订单日期',
        width: 120,
        align: 'center',
        key: 'orderDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        title: '采购数量',
        width: 120,
        align: 'center',
        key: 'purchaseNum'
      }, {
        title: '供应商代码',
        width: 120,
        align: 'center',
        key: 'supplierCode'
      }, {
        title: '供应商名称',
        width: 120,
        align: 'center',
        key: 'supplierName'
      }, {
        title: '进口方式',
        width: 120,
        align: 'center',
        key: 'inWay',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.IMPORT_METHOD_MAP)
        }
      }, {
        title: '原始物料',
        width: 180,
        align: 'center',
        key: 'originalGNo'
      }, {
        title: '英文名称',
        width: 180,
        align: 'center',
        key: 'copGNameEn'
      }, {
        title: '厂商编号',
        width: 120,
        align: 'center',
        key: 'factory'
      }, {
        title: '法定数量',
        width: 120,
        align: 'center',
        key: 'qty1'
      }, {
        title: '法二数量',
        width: 120,
        align: 'center',
        key: 'qty2'
      }, {
        title: '征免方式',
        key: 'dutyMode',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.levymode)
        }
      }, {
        title: '境内目的地',
        key: 'districtCode',
        align: 'center',
        width: 160,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.area)
        }
      }, {
        title: '境内目的地(行政区划)',
        key: 'districtPostCode',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'POST_AREA')
        }
      }, {
        title: '最终目的国',
        key: 'destinationCountry',
        align: 'center',
        width: 120,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        title: '转换后最终目的国',
        key: 'destinationCountryConvert',
        align: 'center',
        width: 120,
        ellipsis: true,
        tooltip: true
      }, {
        title: '报关单归并序号',
        key: 'entryGNo',
        align: 'center',
        width: 120,
        ellipsis: true,
        tooltip: true
      }, {
        title: '备注',
        width: 250,
        align: 'left',
        key: 'note'
      }, {
        title: 'remark1',
        minWidth: 120,
        align: 'center',
        key: 'remark1',
        ellipsis: true,
        tooltip: true
      }, {
        title: 'remark2',
        minWidth: 120,
        align: 'center',
        key: 'remark2',
        ellipsis: true,
        tooltip: true
      }, {
        title: 'remark3',
        minWidth: 120,
        align: 'center',
        key: 'remark3',
        ellipsis: true,
        tooltip: true
      }, {
        title: '传输批次号',
        width: 120,
        align: 'center',
        key: 'tempOwner'
      }, {
        title: 'ERP创建时间',
        width: 150,
        align: 'center',
        key: 'lastModifyDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        title: '提取时间',
        width: 150,
        align: 'center',
        key: 'extractTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        title: '接收时间',
        width: 150,
        align: 'center',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        title: '更新时间',
        width: 150,
        align: 'center',
        key: 'updateTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.columns = [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, ...columns]
      me.listSetupShow = false
    },
    /**
     * 分配指派
     */
    handleDistribution() {
      let me = this
      if (me.checkRowSelected('分配指派', true)) {
        me.$set(me, 'confirmShow', true)
      }
    },
    doBackFill(result) {
      let me = this
      console.info(result)
      me.$set(me, 'confirmShow', false)
    },
    /**
     * 导出
     */
    handleDownload() {
      // this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
