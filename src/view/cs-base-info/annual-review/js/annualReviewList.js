import AnnualReviewEdit from '../annual-review-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const annualReviewList = {
  name: 'annualReviewList',
  components: {
    AnnualReviewEdit
  },
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      listConfig: {
        colOptions: true
      },
      cmbSource: {
        reviewType: [{
          value: 'cargo', label: '货代'
        }, {
          value: 'customs', label: '报关行'
        }, {
          value: 'convoy', label: '车队'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this,
      showColumns = [],
      rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
    me.afterSearchSuccess()
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '新增',
        command: 'add',
        icon: 'ios-add',
        key: 'xdo-btn-add'
      }, {
        ...me.actionsComm,
        label: '编辑',
        command: 'edit',
        key: 'xdo-btn-edit',
        icon: 'ios-create-outline'
      }, {
        ...me.actionsComm,
        label: '删除',
        command: 'delete',
        key: 'xdo-btn-delete',
        icon: 'ios-trash-outline'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      })
    },
    getCommParams() {
      return [{
        type: 'select',
        key: 'reviewType',
        title: '年度评审类别'
      }, {
        title: '代码',
        key: 'customerCode'
      }, {
        title: '名称',
        key: 'companyName'
      }, {
        range: true,
        key: 'reviewDate',
        title: '评审时间段'
      }]
    },
    getCommFields() {
      return [{
        width: 120,
        align: 'center',
        key: 'reviewType',
        title: '年度评审类别',
        render: (h, param) => {
          return this.cmbShowRender(h, param, this.cmbSource.reviewType)
        }
      }, {
        width: 120,
        title: '代码',
        tooltip: true,
        align: 'center',
        key: 'customerCode'
      }, {
        width: 220,
        title: '名称',
        tooltip: true,
        align: 'center',
        key: 'companyName'
      }, {
        width: 168,
        align: 'center',
        key: 'reviewDate',
        title: '评审时间段',
        render: (h, params) => {
          let reviewDateFrom = params.row['reviewDateFrom'],
            reviewDateTo = params.row['reviewDateTo']
          return h('span', {}, reviewDateFrom + ' ~ ' + reviewDateTo)
        }
      }, {
        width: 100,
        key: 'votes',
        title: '票数',
        tooltip: true,
        align: 'center'
      }, {
        width: 120,
        tooltip: true,
        align: 'center',
        title: '异常统计',
        key: 'abnormalStatistics'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleDelete() {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.listConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
            }
          })
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      // this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      me.listConfig.data = [{
        sid: '234646356556',
        reviewType: 'cargo',
        customerCode: 'cargoXXX',
        companyName: 'XXX货代有限公司',
        reviewDateFrom: '2020-03-01',
        reviewDateTo: '2020-03-31',
        votes: 200,
        abnormalStatistics: 33
      }, {
        sid: '246246246256',
        reviewType: 'customs',
        customerCode: 'customsXXX',
        companyName: 'XXX报关行',
        reviewDateFrom: '2020-04-01',
        reviewDateTo: '2020-05-10',
        votes: 198,
        abnormalStatistics: 25
      }, {
        sid: '346243624624',
        reviewType: 'convoy',
        customerCode: 'convoyXXX',
        companyName: 'XXX车队',
        reviewDateFrom: '2020-05-01',
        reviewDateTo: '2020-06-01',
        votes: 336,
        abnormalStatistics: 18
      }]
      me.pageParam.dataTotal = 3
    }
  }
}
