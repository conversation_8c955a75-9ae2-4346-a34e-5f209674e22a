<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'annualReviewEdit',
    mixins: [baseDetailConfig],
    props: {
      inSource: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        ajaxUrl: {
          insert: '',
          update: '',
        },
        formName: 'frmData',
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {
          ...this.inSource,
          ...this.cmbSource
        }
      }
    },
    methods: {
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
      },
      getFields() {
        return [{
          isCard: true,
          title: '年度评审信息',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'reviewType',
          title: '年度评审类别',
          type: 'select'
        }, {
          key: 'customerCode',
          title: '代码'
        }, {
          key: 'companyName',
          title: '名称'
        }, {
          range: true,
          key: 'reviewDate',
          title: '评审时间段'
        }, {
          key: 'votes',
          title: '票数',
          type: 'xdoInput',
          props: {
            intDigits: 5
          }
        }, {
          key: 'abnormalStatistics',
          title: '异常统计',
          type: 'xdoInput',
          props: {
            intDigits: 3
          }
        }]
      },
      handleSave() {
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
