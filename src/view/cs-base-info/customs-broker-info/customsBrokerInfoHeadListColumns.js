import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'customerType'
  , 'customerCode'
  , 'companyName'
  , 'inspectionCode'
  , 'creditCode'
  , 'declareCode'
  , 'telephoneNo'
  , 'linkmanName'
  , 'mobilePhone'
  , 'email'
  , 'address'
  , 'companyNameEn'
  , 'countryEn'
  , 'areaEn'
  , 'cityEn'
  , 'addressEn'
  , 'telephoneNoEn'
  , 'linkmanNameEn'
  , 'mobilePhoneEn'
  , 'emailEn'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'linkmanDutyEn'
  , 'aeoCode'
  , 'authorize'
  , 'authorizeDeadline'
  , 'ifFreeEntrusted'
]

const columnsConfig = [
  ...baseColumnsShow
  , 'status'
  , ...commColumns
  , 'customsCreditRating'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
  , 'customsCreditRatingName'
]

const columns = {
  mixins: [baseColumns, columnRender],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      ifFreeEntrustedList:[
        { value: '0', label: '否' },
        { value: '1', label: '是' }
      ],
      totalColumns: [
        ...baseFields,
        {
          width: 80,
          key: 'status',
          tooltip: true,
          title: '使用状态',
          render: (h, param) => {
            return h('span', this.useStatus(param.row.status))
          }
        },
        {
          width: 220,
          tooltip: true,
          key: 'companyName',
          title: '报关行中文名称'
        },
        {
          width: 120,
          tooltip: true,
          key: 'declareCode',
          title: '海关注册编码'
        },
        {
          width: 120,
          tooltip: true,
          title: '海关信用等级',
          key: 'customsCreditRating',
          render: (h, params) => {
            return h('span', this.customsCreditRating[params.row.customsCreditRating])
          }
        },
        {
          width: 120,
          title: '海关信用等级',
          key: 'customsCreditRatingName'
        },
        {
          width: 80,
          title: '是否授权',
          key: 'authorize',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.authorize)
          }
        },
        {
          width: 120,
          title: '授权截止日期',
          key: 'authorizeDeadline',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '报关行代码',
          key: 'customerCode'
        },
        {
          width: 120,
          tooltip: true,
          title: '报关行名称缩写',
          key: 'companyNameShort'
        },
        {
          width: 120,
          tooltip: true,
          key: 'creditCode',
          title: '社会信用代码'
        },
        {
          width: 120,
          tooltip: true,
          key: 'linkmanName',
          title: '报关行联系人'
        },
        {
          width: 120,
          tooltip: true,
          key: 'linkmanDuty',
          title: '联系人职务'
        },
        {
          width: 120,
          tooltip: true,
          title: '联系人手机',
          key: 'mobilePhone'
        },
        {
          width: 120,
          tooltip: true,
          title: '电话',
          key: 'telephoneNo'
        },
        {
          width: 120,
          key: 'email',
          tooltip: true,
          title: '联系人邮箱'
        },
        {
          width: 220,
          tooltip: true,
          key: 'address',
          title: '报关行地址'
        },
        {
          width: 200,
          tooltip: true,
          key: 'aeoCode',
          title: 'AEO代码'
        },
        {
          width: 200,
          tooltip: true,
          key: 'ifFreeEntrusted',
          title: '是否免表受托单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.ifFreeEntrustedList)
          }
        }
      ]
    }
  },
  methods: {
    useStatus(e) {
      if (e === '0') {
        return '启用'
      } else {
        return '停用'
      }
    }
  }
}

export {
  columns,
  columnsConfig,
  excelColumnsConfig
}
