<template>
  <section>
    <div v-show="showList">
      <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                  @onImportSuccess="onAfterImport"></ImportPage>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <customsBrokerInfoHeadSearch ref="headSearch"></customsBrokerInfoHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CustomsBrokerInfoHeadEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></CustomsBrokerInfoHeadEdit>
    <!-- 该模块为erp提取模块  -->
    <ErpBasicInfoPop :show.sync="showErp" customer-type="CUT" @closePickUp="closePickUp"></ErpBasicInfoPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { csBaseInfoList } from '../base/js/csBaseInfoList'
  import ErpBasicInfoPop from '../components/erp-basic-info-pop'
  import CustomsBrokerInfoHeadEdit from './customsBrokerInfoHeadEdit'
  import { customsCreditRatingKeys, baseInfo } from '@/view/cs-common'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import customsBrokerInfoHeadSearch from './customsBrokerInfoHeadSearch'
  import { columnsConfig, excelColumnsConfig, columns } from './customsBrokerInfoHeadListColumns'

  export default {
    name: 'customsBrokerInfoHeadList',
    components: {
      ErpBasicInfoPop,
      CustomsBrokerInfoHeadEdit,
      customsBrokerInfoHeadSearch
    },
    mixins: [csBaseInfoList, columns],
    data() {
      let importConfig = this.getCommImportConfig('BI_CUT')
      return {
        typeNo: '',
        showErp: false,
        gridConfig: {
          exportTitle: '报关行基础信息'
        },
        toolbarEventMap: {
          'add': this.handleAdd,          // 新增
          'edit': this.handleEdit,        // 编辑
          'delete': this.handleDelete,    // 删除
          'import': this.handleImport,    // 导入
          'extract': this.handlePickUp,   // 提取数据
          'enable': this.handleUseopen,   // 启用
          'export': this.handleDownload,  // 导出
          'disable': this.handleUseclose  // 停用
        },
        importKey: 'BI_CUT',
        importConfig: importConfig,
        // 导入参数
        impData: {
          importType: 'cut',
          filename: '基础信息',
          insertUrl: csAPI.importExcel.insertData,
          uploadUrl: csAPI.importExcel.importExcel
        },
        customsCreditRating: customsCreditRatingKeys,
        cmbSource: {
          authorize: baseInfo.customsBroker.authorize
        },
        ajaxUrl: {
          delete: csAPI.csBaseInfo.customsBrokerInfo.list.delete,
          exportUrl: csAPI.csBaseInfo.customsBrokerInfo.list.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.customsBrokerInfo.list.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      // 点击页面提取ERP数据按钮的方法
      handlePickUp() {
        let me = this
        me.$set(me, 'showErp', true)
      },
      // 提取页面自动关闭
      closePickUp() {
        let me = this
        me.$set(me, 'showErp', false)
        me.handleSearchSubmit() // 走一下后台的初始化方法
      },
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 下载
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
