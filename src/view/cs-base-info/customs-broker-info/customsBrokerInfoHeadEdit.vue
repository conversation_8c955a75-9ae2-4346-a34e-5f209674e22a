<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="customerCode" label="代码">
            <XdoIInput type="text" v-model="frmData.customerCode" :disabled="codeDisable" :clearable="!codeDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="companyName" label="中文名称">
            <XdoIInput type="text" v-model="frmData.companyName" :disabled="true" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="companyNameEn" label="英文名称">
            <XdoIInput type="text" v-model="frmData.companyNameEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="declareCode" label="海关注册编码">
            <XdoIInput type="text" v-model="frmData.declareCode" :disabled="showDisable" :clearable="!showDisable" :maxlength="10" @on-enter="declareCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="creditCode" label="社会信用代码">
            <XdoIInput type="text" v-model="frmData.creditCode" disabled :clearable="!showDisable" :maxlength="18" @on-enter="onCreditCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="aeoCode" label="AEO代码">
            <XdoIInput type="text" v-model="frmData.aeoCode" :disabled="showDisable" :clearable="!showDisable" :maxlength="30"></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop="customsCreditRating" label="海关信用等级">
            <xdo-select v-model="frmData.customsCreditRating" :disabled="showDisable" :options="customsCreditRatingSource" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="authorize" label="是否授权">
            <xdo-select v-model="frmData.authorize" :disabled="showDisable" :options="cmbSource.authorize" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="authorizeDeadline" label="授权截止日期">
            <DatePicker type="date" :disabled="showDisable" v-model="frmData.authorizeDeadline" placeholder="请选择授权截止日期" style="width: 100%" transfer></DatePicker>
          </XdoFormItem>

          <XdoFormItem prop="linkmanName" label="联系人">
            <XdoIInput type="text" v-model="frmData.linkmanName" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="mobilePhone" label="联系人手机">
            <XdoIInput type="text" v-model="frmData.mobilePhone" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="email" label="联系人邮箱">
            <XdoIInput type="text" v-model="frmData.email" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="telephoneNo" label="电话">
            <XdoIInput type="text" v-model="frmData.telephoneNo" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="fax" label="传真">
            <XdoIInput type="text" v-model="frmData.fax" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="postal" label="邮编">
            <XdoIInput type="text" v-model="frmData.postal" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="ifFreeEntrusted" label="是否免表受托单位" :label-width="270">
            <i-switch v-model="frmData.ifFreeEntrusted" :true-value="'1'" :false-value="'0'" :disabled="showDisable" />
          </XdoFormItem>
          <XdoFormItem prop="countryEn" label="英文国家/地区" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.countryEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="address" label="中文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.address" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="addressEn" label="英文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.addressEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="frmData.sid" :showAction="showAction" :just-view="!showAction" business-type="B" title="合同协议"></AcmpInfoListCustom>
    </div>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="annualSid" :showAction="showAction" :just-view="!showAction" business-type="B" title="年度评审报告"></AcmpInfoListCustom>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { csBaseInfoEdit } from '../base/js/csBaseInfoEdit'
  import { editStatus, AcmpInfoListCustom, baseInfo } from '@/view/cs-common'

  export default {
    name: 'customsBrokerInfoHeadEdit',
    mixins: [csBaseInfoEdit],
    components: {
      AcmpInfoListCustom
    },
    data() {
      return {
        formName: 'dataForm',
        cmbSource: {
          authorize: baseInfo.customsBroker.authorize
        },
        ajaxUrl: {
          insert: csAPI.csBaseInfo.customsBrokerInfo.detail.insert,
          update: csAPI.csBaseInfo.customsBrokerInfo.detail.update
        },
        rulesHeader: {
          creditCode: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          declareCode: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          companyName: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          customerCode: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          email: [{ type: 'email', message: '不是正确的电子邮箱格式!', trigger: 'blur' }]
        }
      }
    },
    watch:{
      'frmData.ifFreeEntrusted': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val==='1') {
            me.$http.get(csAPI.csBaseInfo.customsBrokerInfo.list.selectIfFreeEntrusted+ '/' + me.editConfig.editData.sid).then(
              () => {}
            ).catch(() => {
              me.frmData.ifFreeEntrusted='0'
            }).finally(() => {
            })
          }
        }
      }
    },
    computed: {
      showAcmpInfo() {
        return this.editConfig.editStatus !== editStatus.ADD
      },
      showAction() {
        return (this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      },
      annualSid() {
        if (!isNullOrEmpty(this.frmData.sid)) {
          return this.frmData.sid + '-al'
        }
        return ''
      }
    },
    methods: {
      /**
       * 获取默认值
       * @returns {{}}
       */
      getDefaultData() {
        return {
          declareCode: '',
          companyName: '',
          companyNameEn: '',
          countryEn: '',
          customsCreditRating: '',
          customerCode: '',
          companyNameShort: '',
          creditCode: '',
          linkmanName: '',
          linkmanDuty: '',
          mobilePhone: '',
          email: '',
          aeoCode: '',
          telephoneNo: '',
          fax: '',
          postal: '',
          ifFreeEntrusted: '',
          address: '',
          addressEn: '',
          customerType: 'CUT',
          authorize: '',
          authorizeDeadline: ''
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
