<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="供应商基础信息">
        <Head @onEditBack="editBack" :edit-config="editConfig"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="Ship From">
        <ShipFromList v-if="tabs.bodyTab" :parent-config="parentConfig"></ShipFromList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './supplierInfoHeadEdit'
  import { editStatus } from '@/view/cs-common'
  import ShipFromList from './ship-from/ship-from-list'

  export default {
    name: 'supplierInfoTabs',
    components: {
      Head,
      ShipFromList
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        tabName: 'headTab',
        tabs: {
          headTab: true,
          bodyTab: false
        }
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      }
    },
    computed: {
      showBody() {
        let me = this
        if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT) {
          return true
        } else return me.editConfig && me.editConfig.editStatus === editStatus.SHOW
      },
      parentConfig() {
        return {
          editStatus: this.editConfig.editStatus,
          editData: {
            sid: this.editConfig.editData.sid,
            customerCode: this.editConfig.editData.customerCode
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
