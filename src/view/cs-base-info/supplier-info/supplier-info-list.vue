<template>
  <section>
    <div v-show="showList">
      <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                  @onImportSuccess="onAfterImport"></ImportPage>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <supplierInfoTabs v-if="!showList" :editConfig="editConfig" :in-source="cmbSource"
                      @onEditBack="editBack"></supplierInfoTabs>
    <!-- 该模块为erp提取模块  -->
    <ErpBasicInfoPop :show.sync="showErp" customer-type="PRD"
                     @closePickUp="closePickUp"></ErpBasicInfoPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import supplierInfoTabs from './supplier-info-tabs'
  import { supplierInfoList } from './js/supplierInfoList'
  import ErpBasicInfoPop from '../components/erp-basic-info-pop'

  export default {
    name: 'supplierInfoList',
    mixins: [supplierInfoList],
    components: {
      ErpBasicInfoPop,
      supplierInfoTabs
    },
    data() {
      let importConfig = this.getCommImportConfig('BI_PRD')
      return {
        showErp: false,
        importKey: 'BI_PRD',
        // 是否显示导入页面
        modelImportShow: false,
        importConfig: importConfig,
        listConfig: {
          exportTitle: '供应商基础信息'
        },
        ajaxUrl: {
          copy: csAPI.csBaseInfo.supplierInfo.list.copycli,
          delete: csAPI.csBaseInfo.supplierInfo.list.delete,
          exportUrl: csAPI.csBaseInfo.supplierInfo.list.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.supplierInfo.list.selectAllPaged
        }
      }
    },
    methods: {
      /**
       * 弹出导入窗体
       */
      handleImport() {
        let me = this
        me.modelImportShow = true
      },
      /**
       * 导入成功后事件
       */
      onAfterImport() {
        let me = this
        me.modelImportShow = false
        me.getList()
      },
      /**
       * 点击页面提取ERP数据按钮的方法
       */
      handlePickUp() {
        let me = this
        me.$set(me, 'showErp', true)
      },
      /**
       * 提取页面自动关闭
       */
      closePickUp() {
        let me = this
        me.$set(me, 'showErp', false)
        me.handleSearchSubmit()
      },
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, 'delete')
      },
      /**
       * 下载
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, 'export')
      },
      /**
       * 数据复制
       * @param btnIndex
       */
      doCopy(btnIndex) {
        let me = this
        if (me.checkRowSelected('复制')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '复制',
            cancelText: '取消',
            content: '确认复制所选项吗',
            onOk: () => {
              me.actions[btnIndex].loading = true
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.copy}/${params}`).then(() => {
                me.$Message.success('复制成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.actions[btnIndex].loading = false
              })
            }
          })
        }
      },
      /**
       * 复制
       */
      handleCopy() {
        let me = this
        me.doCopy(me.actions.findIndex(it => it.command === 'copy'))
      },
      /**
       * 启用
       */
      handleUseOpen() {
        let me = this
        if (me.listConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '是否启用',
            onOk: () => {
              const emsCopNos = me.listConfig.selectRows.map(item => {
                return item.sid
              })
              me.$http.post(csAPI.csBaseInfo.useStatus + `/0/${emsCopNos}`).then(() => {
                me.$Message.success('启用成功')
                me.listConfig.selectRows = []
                me.handleSearchSubmit()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }
      },
      /**
       * 停用
       */
      handleUseClose() {
        let me = this
        if (me.listConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '是否停用',
            onOk: () => {
              const emsCopNos = me.listConfig.selectRows.map(item => {
                return item.sid
              })
              me.$http.post(csAPI.csBaseInfo.useStatus + `/1/${emsCopNos}`).then(() => {
                me.$Message.success('停用成功')
                me.listConfig.selectRows = []
                me.handleSearchSubmit()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }
      }
    }
  }
</script>

<style scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
