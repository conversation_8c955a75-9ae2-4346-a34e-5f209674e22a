<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="customerCode" label="代码">
            <XdoIInput type="text" v-model="frmData.customerCode" :disabled="codeDisable" :clearable="!codeDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="companyName" label="中文名称">
            <XdoIInput type="text" v-model="frmData.companyName" :disabled="showDisable" :clearable="!showDisable" :maxlength="200"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="customsCreditRating" label="海关信用等级">
            <xdo-select v-model="frmData.customsCreditRating" :disabled="showDisable" :options="this.customsCreditRatingSource" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="declareCode" label="海关注册编码">
            <XdoIInput type="text" v-model="frmData.declareCode" :disabled="showDisable" :clearable="!showDisable" :maxlength="10" @on-enter="declareCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="creditCode" label="社会信用代码">
            <XdoIInput type="text" v-model="frmData.creditCode" :disabled="showDisable" :clearable="!showDisable" :maxlength="18" @on-enter="onCreditCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="aeoCode" label="AEO代码">
            <XdoIInput type="text" v-model="frmData.aeoCode" :disabled="showDisable" :clearable="!showDisable" :maxlength="30"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="linkmanName" label="联系人">
            <XdoIInput type="text" v-model="frmData.linkmanName" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="mobilePhone" label="联系人手机">
            <XdoIInput type="text" v-model="frmData.mobilePhone" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="email" label="联系人邮箱">
            <XdoIInput type="text" v-model="frmData.email" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="country" label="中文国家">
            <XdoIInput type="text" v-model="frmData.country" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="city" label="中文城市">
            <XdoIInput type="text" v-model="frmData.city" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="area" label="中文地区">
            <XdoIInput type="text" v-model="frmData.area" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="telephoneNo" label="电话">
            <XdoIInput type="text" v-model="frmData.telephoneNo" :disabled="showDisable" :clearable="!showDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="fax" label="传真">
            <XdoIInput type="text" v-model="frmData.fax" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="postal" label="邮编">
            <XdoIInput type="text" v-model="frmData.postal" :disabled="showDisable" :clearable="!showDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="correspondGw" label="对应关务">
            <xdo-select v-model="frmData.correspondGw" :disabled="showDisable" :options="this.correspondGwList" ></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="address" label="中文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.address" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="invoiceAddress" label="发票中文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.invoiceAddress" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="deliverAddress" label="发货中文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.deliverAddress" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="costCenter" label="成本中心" >
            <XdoIInput type="text" v-model="frmData.costCenter" :disabled="showDisable" :clearable="!showDisable" :maxlength="150"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-2-4">
            <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="英文信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="companyNameEn" label="英文名称">
            <XdoIInput type="text" v-model="frmData.companyNameEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="countryEn" label="英文国家">
            <XdoIInput type="text" v-model="frmData.countryEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="cityEn" label="英文城市">
            <XdoIInput type="text" v-model="frmData.cityEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="areaEn" label="英文地区">
            <XdoIInput type="text" v-model="frmData.areaEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="addressEn" label="英文地址" class="dc-merge-2-4">
            <XdoIInput type="text" v-model="frmData.addressEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="invoiceAddressEn" label="发票英文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.invoiceAddressEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="deliverAddressEn" label="发货英文地址" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.deliverAddressEn" :disabled="showDisable" :clearable="!showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="frmData.sid" :showAction="showAction" :just-view="!showAction" business-type="B" title="附件信息"></AcmpInfoListCustom>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { csBaseInfoEdit } from '../base/js/csBaseInfoEdit'
  import { editStatus, AcmpInfoListCustom } from '@/view/cs-common'
  import {isNullOrEmpty} from "@/libs/util";

  export default {
    name: 'supplierInfoHeadEdit',
    mixins: [csBaseInfoEdit],
    components: {
      AcmpInfoListCustom
    },
    data() {
      return {
        formName: 'dataForm',
        correspondGwList: [],
        ajaxUrl: {
          insert: csAPI.csBaseInfo.supplierInfo.detail.insert,
          update: csAPI.csBaseInfo.supplierInfo.detail.update
        },
        rulesHeader: {
          companyName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          customerCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          email: [{type: 'email', message: '不是正确的电子邮箱格式!', trigger: 'blur'}]
        }
      }
    },
    computed: {
      showAcmpInfo() {
        return this.editConfig.editStatus !== editStatus.ADD
      },
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      }
    },


    mounted() {
      let me = this;
      let baseUrl = window.location.origin + '/pms/api/v1/user/list'
      this.$http.post(baseUrl,{
        headers: {
          Authorization: 'Bearer ' + me.$store.state.token
        }
      }).then(res => {
        // console.log( res.data.data.map((item => item.userName)))
        console.log( res.data.data.map((item => item.userRoles)))
        console.log( res.data.data.map((item => item.loginName)))
        me.correspondGwList = res.data.data
          .filter(item => !isNullOrEmpty(item.userRoles) && item.userRoles.indexOf('关务') !== -1)
          .map((item => item.loginName))
      })

    },
    methods: {
      /**
       * 获取默认值
       * @returns {{}}
       */
      getDefaultData() {
        return {
          customerCode: '',
          companyName: '',
          customsCreditRating: '',
          declareCode: '',
          creditCode: '',
          companyNameShort: '',
          telephoneNo: '',
          linkmanName: '',
          linkmanDuty: '',
          mobilePhone: '',
          country: '',
          city: '',
          area: '',
          email: '',
          aeoCode: '',
          fax: '',
          postal: '',
          address: '',
          invoiceAddress: '',
          deliverAddress: '',
          costCenter: '',
          note: '',
          companyNameEn: '',
          countryEn: '',
          areaEn: '',
          cityEn: '',
          addressEn: '',
          invoiceAddressEn: '',
          deliverAddressEn: '',
          telephoneNoEn: '',
          linkmanNameEn: '',
          mobilePhoneEn: '',
          emailEn: '',
          customerType: 'PRD'
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
