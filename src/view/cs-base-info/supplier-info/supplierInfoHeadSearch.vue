<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="customerCode" label="供应商代码">
        <XdoIInput type="text" v-model="searchParam.customerCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyName" label="供应商中文名称">
        <XdoIInput type="text" v-model="searchParam.companyName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'supplierInfoHeadSearch',
    data() {
      return {
        searchParam: {
          customerCode: '',
          companyName: '',
          companyNameShort: '',
          customerType: 'PRD'
        }
      }
    }
  }
</script>

<style scoped>
</style>
