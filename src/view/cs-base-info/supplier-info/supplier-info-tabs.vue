<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="供应商基础信息">
        <supplierInfoEdit :edit-config="currConfig" :in-source="inSource"
                          @onEditBack="editBack"></supplierInfoEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="Ship From">
        <ShipFromList :parent-config="currConfig"></ShipFromList>
      </TabPane>
      <template v-slot:extra>
        <Button type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></Button>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import supplierInfoEdit from './supplier-info-edit'
  import ShipFromList from './ship-from/ship-from-list'

  export default {
    name: 'supplierInfoTabs',
    components: {
      supplierInfoEdit,
      ShipFromList
    },
    props: {
      /**
       * 传入的数据源
       */
      inSource: {
        type: Object,
        default: () => ({})
      },
      editConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        tabName: 'headTab',
        tabs: {
          headTab: true,
          bodyTab: false
        },
        currConfig: {
          editData: {},
          editStatus: editStatus.SHOW
        }
      }
    },
    computed: {
      showBody() {
        let me = this
        return me.currConfig.editData && !isNullOrEmpty(me.currConfig.editData.sid)
      }
    },
    watch: {
      tabName: {
        immediate: true,
        handler: function (tabName) {
          let me = this
          me.tabs[tabName] = true
        }
      },
      editConfig: {
        deep: true,
        immediate: true,
        handler: function (config) {
          let me = this
          me.$set(me.currConfig, 'editData', config.editData)
          me.$set(me.currConfig, 'editStatus', config.editStatus)
        }
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.$emit('onEditBack', {
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      }
    }
  }
</script>

<style scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
