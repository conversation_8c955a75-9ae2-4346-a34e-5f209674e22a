import ImportPage from 'xdo-import'
import { customsCreditRatingSource } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const supplierInfoList = {
  name: 'supplierInfoList',
  mixins: [columnRender, baseSearchConfig, dynamicImport, listDataProcessing],
  components: {
    ImportPage
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'add': this.handleAdd,          // 新增
        'edit': this.handleEdit,        // 编辑
        'copy': this.handleCopy,        // 复制为客户信息
        'copyFod': this.handleCopyFod,        // 复制为客户信息
        'import': this.handleImport,    // 导入
        'delete': this.handleDelete,    // 删除
        'extract': this.handlePickUp,   // 提取数据
        'export': this.handleDownload   // 导出
      },
      cmbSource: {
        customsCreditRating: customsCreditRatingSource
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        title: '供应商代码',
        key: 'customerCode'
      }, {
        key: 'companyName',
        title: '供应商中文名称'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['customerType'] = 'PRD'
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, tooltip: boolean, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '代码',
        key: 'customerCode',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '中文名称',
        key: 'companyName',
        cellRendererFramework: me.baseCellRenderer(null, true)
        // }, {
        //   width: 120,
        //   tooltip: true,
        //   title: '海关信用等级',
        //   key: 'customsCreditRating',
        //   cellRendererFramework: me.baseCellRenderer(function (h, params) {
        //     return me.cmbShowRender(h, params, customsCreditRatingSource)
        //   })
      }, {
        width: 120,
        title: '海关信用等级',
        key: 'customsCreditRatingName'
      }, {
        width: 120,
        key: 'declareCode',
        title: '海关注册编码'
      }, {
        width: 120,
        key: 'creditCode',
        title: '社会信用代码'
      }, {
        width: 200,
        key: 'aeoCode',
        title: 'AEO代码'
      }, {
        width: 200,
        key: 'country',
        title: '中文国家',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 200,
        key: 'city',
        title: '中文城市',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 200,
        key: 'area',
        title: '中文地区',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '供应商名称缩写',
        key: 'companyNameShort',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'linkmanName',
        title: '供应商联系人',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '联系人职务',
        key: 'linkmanDuty',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '联系人电话',
        key: 'mobilePhone',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'email',
        title: '联系人邮箱',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '电话',
        key: 'telephoneNo',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        key: 'fax',
        width: 120,
        title: '传真'
      }, {
        width: 120,
        title: '邮编',
        key: 'postal',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        key: 'address',
        title: '中文地址',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发票中文地址',
        key: 'invoiceAddress',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发货中文地址',
        key: 'deliverAddress',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'countryEn',
        title: '英文国家',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'cityEn',
        title: '英文城市',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'areaEn',
        title: '英文地区',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        key: 'addressEn',
        title: '英文地址',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发票英文地址',
        key: 'invoiceAddressEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发货英文地址',
        key: 'deliverAddressEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    }
  }
}
