<template>
  <section>
    <div v-show="showList">
      <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                  @onImportSuccess="onAfterImport"></ImportPage>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <supplierInfoHeadSearch ref="headSearch"></supplierInfoHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <SupplierInfoTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></SupplierInfoTabs>
    <!-- 该模块为erp提取模块  -->
    <ErpBasicInfoPop :show.sync="showErp" customer-type="PRD" @closePickUp="closePickUp"></ErpBasicInfoPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import SupplierInfoTabs from './supplierInfoTabs'
  import { csBaseInfoList } from '../base/js/csBaseInfoList'
  import { customsCreditRatingKeys } from '@/view/cs-common'
  import supplierInfoHeadSearch from './supplierInfoHeadSearch'
  import ErpBasicInfoPop from '../components/erp-basic-info-pop'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './supplierInfoHeadListColumns'

  export default {
    name: 'supplierInfoHeadList',
    components: {
      ErpBasicInfoPop,
      SupplierInfoTabs,
      supplierInfoHeadSearch
    },
    mixins: [csBaseInfoList, columns],
    data() {
      let importConfig = this.getCommImportConfig('BI_PRD')
      return {
        showErp: false,
        gridConfig: {
          exportTitle: '供应商基础信息'
        },
        toolbarEventMap: {
          'add': this.handleAdd,          // '新增'
          'edit': this.handleEdit,        // '编辑'
          'copy': this.handleCopy,        // '复制为客户信息'
          'copyFod': this.handleCopyFod,        // '复制为货代信息'
          'delete': this.handleDelete,    // '删除'
          'import': this.handleImport,    // '导入'
          'extract': this.handlePickUp,   // '提取数据'
          'export': this.handleDownload   // '导出'
        },
        importKey: 'BI_PRD',
        importConfig: importConfig,
        //导入参数
        impData: {
          importType: 'prd',
          filename: '基础信息',
          insertUrl: csAPI.importExcel.insertData,
          uploadUrl: csAPI.importExcel.importExcel
        },
        customsCreditRating: customsCreditRatingKeys,
        ajaxUrl: {
          copy: csAPI.csBaseInfo.supplierInfo.list.copycli,
          copyFod: csAPI.csBaseInfo.supplierInfo.list.copyFod,
          delete: csAPI.csBaseInfo.supplierInfo.list.delete,
          exportUrl: csAPI.csBaseInfo.supplierInfo.list.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.supplierInfo.list.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      // 点击页面提取ERP数据按钮的方法
      handlePickUp() {
        let me = this
        me.$set(me, 'showErp', true)
      },
      // 提取页面自动关闭
      closePickUp() {
        let me = this
        me.$set(me, 'showErp', false)
        me.handleSearchSubmit() // 走一下后台的初始化方法
      },
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 下载
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      },
      /**
       * 复制
       */
      handleCopy() {
        let me = this
        me.doCopy(me.actions.findIndex(it => it.command === 'copy'))
      },
      /**
       * 复制
       */
      handleCopyFod() {
        let me = this
        console.log("1111")
        me.doCopyFod(me.actions.findIndex(it => it.command === 'copyFod'))
      },
      /**
       * 数据复制
       * @param btnIndex
       */
      doCopyFod(btnIndex) {
        let me = this
        console.log("22222")
        if (me.checkRowSelected('复制')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '复制',
            cancelText: '取消',
            content: '确认复制所选项吗',
            onOk: () => {
              me.actions[btnIndex].loading = true
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.copyFod}/${params}`).then(() => {
                me.$Message.success('复制成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.actions[btnIndex].loading = false
              })
            }
          })
        }
      },
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
