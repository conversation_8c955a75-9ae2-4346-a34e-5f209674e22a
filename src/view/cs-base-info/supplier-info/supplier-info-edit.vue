<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="editConfig.editData.sid" :showAction="showAction" :just-view="!showAction" business-type="B" title="附件信息"></AcmpInfoListCustom>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus, AcmpInfoListCustom } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'supplierInfoEdit',
    components: {
      AcmpInfoListCustom
    },
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csBaseInfo.supplierInfo.detail.insert,
          update: csAPI.csBaseInfo.supplierInfo.detail.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    computed: {
      showAcmpInfo() {
        return this.editConfig.editStatus !== editStatus.ADD
      },
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      },
      codeDisable() {
        if (this.editConfig.editStatus !== editStatus.ADD) {
          return true
        }
        return this.showDisable
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      /**
       * 扩展规则
       */
      extendRules() {
        return {
          email: [{type: 'email', message: '不是正确的电子邮箱格式!', trigger: 'blur'}]
        }
      },
      /**
       * 界面字段、规则重置后执行的事件
       */
      afterFieldsReset() {
        let me = this
        me.$nextTick(() => {
          me.setDisable('customerCode', me.codeDisable)
        })
      },
      getFields() {
        let me = this
        return [{
          isCard: true,
          title: '基础信息',
          key: '12121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '代码',
          required: true,
          key: 'customerCode'
        }, {
          required: true,
          title: '中文名称',
          key: 'companyName'
        }, {
          type: 'select',
          title: '海关信用等级',
          key: 'customsCreditRating'
        }, {
          key: 'declareCode',
          title: '海关注册编码',
          on: {
            enter: me.onDeclareCodeEnter
          }
        }, {
          key: 'creditCode',
          title: '社会信用代码',
          on: {
            enter: me.onCreditCodeEnter
          }
        }, {
          key: 'aeoCode',
          title: 'AEO代码'
        }, {
          title: '联系人',
          key: 'linkmanName'
        }, {
          title: '联系人手机',
          key: 'mobilePhone'
        }, {
          key: 'email',
          title: '联系人邮箱'
        }, {
          key: 'country',
          title: '中文国家'
        }, {
          key: 'city',
          title: '中文城市'
        }, {
          key: 'area',
          title: '中文地区'
        }, {
          title: '电话',
          key: 'telephoneNo'
        }, {
          key: 'fax',
          title: '传真'
        }, {
          title: '邮编',
          key: 'postal'
        }, {
          key: 'address',
          title: '中文地址',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '发票中文地址',
          key: 'invoiceAddress',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '发货中文地址',
          key: 'deliverAddress',
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'note',
          title: '备注',
          itemClass: 'dc-merge-1-4'
        }, {
          isCard: true,
          title: '英文信息',
          key: '*************',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '英文名称',
          key: 'companyNameEn'
        }, {
          key: 'countryEn',
          title: '英文国家'
        }, {
          key: 'cityEn',
          title: '英文城市'
        }, {
          key: 'areaEn',
          title: '英文地区'
        }, {
          key: 'addressEn',
          title: '英文地址',
          itemClass: 'dc-merge-2-4'
        }, {
          title: '发票英文地址',
          key: 'invoiceAddressEn',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '发货英文地址',
          key: 'deliverAddressEn',
          itemClass: 'dc-merge-1-4'
        }]
      },
      /**
       * code、name赋值
       * @param codeField
       * @param nameField
       * @param codeValue
       * @param nameValue
       */
      codeNameSet(codeField, nameField, codeValue = '', nameValue = '') {
        let me = this
        me.$set(me.detailConfig.model, nameField, nameValue)
        me.$set(me.detailConfig.model, codeField, codeValue)
      },
      /**
       * 代码回车事件
       * @param paramName 参数名
       * @param paramNameLength 参数名规定长度
       * @param code code字段
       * @param name name字段
       * @param url 访问链接字段
       * @param warningMsg 警告信息
       * @param codeL codeL字段
       * @param nameL nameL字段
       */
      onCodeEnter(paramName, paramNameLength, code, name, url, warningMsg, codeL, nameL) {
        let me = this
        if (isNullOrEmpty(paramName)) {
          me.codeNameSet(code, name)
        } else {
          let paramNameTrim = paramName.trim()
          if (paramNameTrim.length !== paramNameLength) {
            me.codeNameSet(code, name)
            me.$Message.warning(warningMsg + '!')
          } else {
            me.pcodeRemote(url, paramNameTrim).then(res => {
              if (Array.isArray(res) && res.length > 0) {
                me.codeNameSet(code, name, res[0][codeL], res[0][nameL])
              } else {
                me.codeNameSet(code, name)
              }
            }).catch(() => {
              me.codeNameSet(code, name)
            })
          }
        }
      },
      /**
       * 根据海关10位编码获取【承揽者名称】和【承揽者社会信用代码】
       */
      onDeclareCodeEnter() {
        let me = this
        me.onCodeEnter(me.detailConfig.model.declareCode, 10, 'creditCode', 'companyName',
          me.pcode.company, '海关代码必须是10位', 'CREDIT_CODE', 'NAME')
      },
      /**
       * 根据承揽者社会信用代码18位获取【承揽者海关代码】和【承揽者名称】
       */
      onCreditCodeEnter() {
        let me = this
        me.onCodeEnter(me.detailConfig.model.creditCode, 18, 'declareCode', 'companyName',
          'COMPANY_BY_CREDITCODE', '承揽者社会信用代码必须是18位', 'CODE', 'NAME')
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'customerType', 'PRD')
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
