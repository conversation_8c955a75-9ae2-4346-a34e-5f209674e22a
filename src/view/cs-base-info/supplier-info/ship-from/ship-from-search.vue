<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm class="dc-form" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="shipFromCode" label="shipFrom代码">
        <XdoIInput type="text" v-model="searchParam.shipFromCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="shipFromName" label="shipFrom名称">
        <XdoIInput type="text" v-model="searchParam.shipFromName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'shipFromSearch',
    props: {
      headId: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchParam: {
          headId: '',
          shipFromCode: '',
          shipFromName: ''
        }
      }
    },
    mounted() {
      let me = this
      me.$set(me.searchParam, 'headId', me.headId)
    }
  }
</script>

<style scoped>
</style>
