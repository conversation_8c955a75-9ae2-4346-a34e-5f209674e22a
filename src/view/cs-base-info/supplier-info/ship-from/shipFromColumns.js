import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'shipFromCode'
  , 'shipFromName'
  , 'shipFromAddress'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]
const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 200,
          tooltip: true,
          key: 'shipFromCode',
          title: 'Ship From 代码'
        },
        {
          width: 200,
          tooltip: true,
          key: 'shipFromName',
          title: 'Ship From 名称'
        },
        {
          width: 350,
          tooltip: true,
          key: 'shipFromAddress',
          title: 'Ship From 详细信息'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
