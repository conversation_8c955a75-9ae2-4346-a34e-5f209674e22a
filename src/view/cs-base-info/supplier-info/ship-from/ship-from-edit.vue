<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form dc-form-2" style="padding-right: 10px;">
          <XdoFormItem prop="shipFromCode" label="shipFrom代码">
            <XdoIInput type="text" v-model="frmData.shipFromCode" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="shipFromName" label="shipFrom名称">
            <XdoIInput type="text" v-model="frmData.shipFromName" :disabled="showDisable" :maxlength="250"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="shipFromAddress" label="shipFrom详细信息" class="dc-merge-1-3">
            <XdoIInput v-model="frmData.shipFromAddress" type="textarea" :disabled="showDisable" :rows="5" :maxlength="500"/>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

  export default {
    name: 'shipFromEdit',
    mixins: [commEdit],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'dataForm',
        ajaxUrl: {
          insert: csAPI.csBaseInfo.supplierInfo.shipFrom.insert,
          update: csAPI.csBaseInfo.supplierInfo.shipFrom.update
        },
        rulesHeader: {
          shipFromCode: [{required: true, message: '不能为空', trigger: 'blur'}],
          shipFromName: [{required: true, message: '不能为空', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', icon: 'dc-btn-save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', icon: 'dc-btn-cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function (val) {
          this.buttons[0].needed = val !== editStatus.SHOW
        }
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          shipFromCode: '',
          shipFromName: '',
          shipFromAddress: '',
          headId: this.editConfig.headId,
          supplierCode: this.editConfig.customerCode
        }
      },
      /**
       * 设置保存按钮加载样式
       * @param loading
       */
      setBtnSaveLoading(loading) {
        this.buttons[0].loading = loading
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        me.doSave((res) => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      },
      /**
       * 保存继续
       */
      handleSaveContinue() {
        let me = this
        me.doSave(() => {
          me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
