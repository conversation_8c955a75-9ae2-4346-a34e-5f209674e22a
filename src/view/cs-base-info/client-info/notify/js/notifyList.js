import NotifyEdit from '../notify-edit'
import { editStatus } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const notifyList = {
  name: 'notifyList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    NotifyEdit
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {
          sid: '',
          customerCode: ''
        },
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      pmsLevel: 'body',
      hasChildTabs: true,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload
      }
    }
  },
  watch: {
    'parentConfig.editStatus': {
      immediate: true,
      handler: function (status) {
        let me = this
        me.$set(me.listConfig, 'disable', status !== editStatus.EDIT)
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'notifyCode',
        title: 'notify 代码'
      }, {
        key: 'notifyName',
        title: 'notify 名称'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.parentConfig.editData.sid
      return paramObj
    },
    /**
     * 展示列字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{flex: number, title: string, key: string})[]}
     */
    getFields() {
      return [{
        width: 160,
        key: 'notifyCode',
        title: 'notify 代码'
      }, {
        width: 160,
        key: 'notifyName',
        title: 'notify 名称'
      }, {
        flex: 1,
        key: 'notifyAddress',
        title: 'notify 详细信息'
      }]
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      me.$set(me.editConfig, 'editData', {})
      me.$set(me.listConfig, 'selectRows', [])
      me.$set(me.editConfig, 'headId', me.parentConfig.editData.sid)
      me.$set(me.editConfig, 'editStatus', editStatus.ADD)
      me.$set(me, 'showList', false)
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck(row, '编辑')) {
        me.$set(me.editConfig, 'editData', row)
        me.$set(me.editConfig, 'headId', me.parentConfig.editData.sid)
        me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        me.$set(me, 'showList', false)
      }
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
