<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="客户基础信息">
        <Head @onEditBack="editBack" :edit-config="editConfig"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="Ship To">
        <ShipToList v-if="tabs.bodyTab" :parent-config="parentConfig"></ShipToList>
      </TabPane>
      <TabPane name="bollToTab" v-if="showBody" label="Bill To">
        <BillToList v-if="tabs.bollToTab" :parent-config="parentConfig"></BillToList>
      </TabPane>
      <TabPane name="notifyTab" v-if="showBody" label="Notify">
        <NotifyList v-if="tabs.notifyTab" :parent-config="parentConfig"></NotifyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './clientInfoHeadEdit'
  import { editStatus } from '@/view/cs-common'
  import NotifyList from './notify/notify-list'
  import ShipToList from './ship-to/ship-to-list'
  import BillToList from './bill-to/bill-to-list'

  export default {
    name: 'clientInfoTabs',
    components: {
      Head,
      NotifyList,
      ShipToList,
      BillToList
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        tabName: 'headTab',
        tabs: {
          headTab: true,
          bodyTab: false,
          bollToTab: false,
          notifyTab: false
        }
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      }
    },
    computed: {
      showBody() {
        let me = this
        if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT) {
          return true
        } else return me.editConfig && me.editConfig.editStatus === editStatus.SHOW
      },
      parentConfig() {
        let me = this
        return {
          editStatus: me.editConfig.editStatus,
          editData: {
            sid: me.editConfig.editData.sid,
            customerCode: me.editConfig.editData.customerCode
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
