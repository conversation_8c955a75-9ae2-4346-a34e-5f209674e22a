<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="customerCode" label="客户代码">
        <XdoIInput type="text" v-model="searchParam.customerCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyName" label="客户中文名称">
        <XdoIInput type="text" v-model="searchParam.companyName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'clientInfoHeadSearch',
    data() {
      return {
        searchParam: {
          customerCode: '',
          companyName: '',
          companyNameShort: '',
          customerType: 'CLI'
        }
      }
    }
  }
</script>

<style scoped>
</style>
