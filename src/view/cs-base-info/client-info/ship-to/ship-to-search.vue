<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm class="dc-form" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="shipToCode" label="ship To 代码">
        <XdoIInput type="text" v-model="searchParam.shipToCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="shipToName" label="ship To 名称">
        <XdoIInput type="text" v-model="searchParam.shipToName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'shipToSearch',
    props: {
      headId: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchParam: {
          headId: '',
          shipToCode: '',
          shipToName: ''
        }
      }
    },
    mounted() {
      let me = this
      me.$set(me.searchParam, 'headId', me.headId)
    }
  }
</script>

<style scoped>
</style>
