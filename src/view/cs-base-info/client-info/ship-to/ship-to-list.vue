<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ShipToSearch ref="headSearch" :head-id="editConfig.headId"></ShipToSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 38px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <ShipToEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></ShipToEdit>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import ShipToEdit from './ship-to-edit'
  import ShipToSearch from './ship-to-search'
  import { editStatus } from '@/view/cs-common'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { columnsConfig, excelColumnsConfig, columns } from './shipToColumns'

  export default {
    name: 'shipToList',
    components: {
      ShipToEdit,
      ShipToSearch
    },
    mixins: [commList, columns, pms],
    props: {
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        actions: [],
        searchLines: 1,
        OffsetHeight: 12,
        hasChildTabs: true,
        gridConfig: {
          exportTitle: 'Ship To'
        },
        toolbarEventMap: {
          'add': this.handleAdd,         // 新增
          'edit': this.handleEdit,       // 编辑
          'delete': this.handleDelete,   // 删除
          'export': this.handleDownload  // 导出
        },
        ajaxUrl: {
          delete: csAPI.csBaseInfo.clientInfo.shipTo.delete,
          exportUrl: csAPI.csBaseInfo.clientInfo.shipTo.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.clientInfo.shipTo.selectAllPaged
        }
      }
    },
    watch: {
      parentConfig: {
        deep: true,
        immediate: true,
        handler: function (config) {
          let me = this
          me.$set(me.editConfig, 'headId', config.editData.sid)
          me.$set(me.editConfig, 'customerCode', config.editData.customerCode)
          me.$set(me, 'showList', true)
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
      me.loadFunctions('body').then(() => {
        if (me.parentConfig.editStatus === editStatus.SHOW) {
          me.setToolbarProperty('add', 'needed', false)
          me.setToolbarProperty('edit', 'needed', false)
          me.setToolbarProperty('delete', 'needed', false)
        } else {
          me.setToolbarProperty('add', 'needed', true)
          me.setToolbarProperty('edit', 'needed', true)
          me.setToolbarProperty('delete', 'needed', true)
        }
      })
    },
    methods: {
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 导出
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      },
      operationEditShow() {
        return this.editDisplay
      }
    },
    computed: {
      editDisplay() {
        if (this.parentConfig.editStatus === editStatus.EDIT) {
          return ''
        }
        return 'none'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
