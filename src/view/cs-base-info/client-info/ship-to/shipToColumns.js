import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'shipToCode'
  , 'clientCode'
  , 'shipToName'
  , 'shipToAddress'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]
const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [...baseFields, {
        width: 200,
        tooltip: true,
        key: 'shipToCode',
        title: 'Ship To 代码'
      }, {
        width: 200,
        tooltip: true,
        key: 'shipToName',
        title: 'Ship To 名称'
      }, {
        width: 350,
        tooltip: true,
        key: 'shipToAddress',
        title: 'Ship To 详细信息'
      }]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
