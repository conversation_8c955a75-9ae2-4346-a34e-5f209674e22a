<template>
  <section>
    <div style="padding: 2px 6px 2px 2px; width: 100%; background-color: white;">
      <XdoCard :bordered="false" class="ieLogisticsTrackingCard" title="基础信息">
        <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="120" class="dc-form-2"
                     :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        </DynamicForm>
      </XdoCard>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'billToEdit',
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csBaseInfo.clientInfo.billTo.insert,
          update: csAPI.csBaseInfo.clientInfo.billTo.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '保存继续', type: 'warning', command: 'save-continue', click: this.handleSaveContinue},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      getFields() {
        return [{
          required: true,
          key: 'billToCode',
          title: 'bill To 代码',
          props: {
            maxlength: 50
          }
        }, {
          required: true,
          key: 'billToName',
          title: 'bill To 名称',
          props: {
            maxlength: 250
          }
        }, {
          key: 'billToAddress',
          itemType: 'textarea',
          title: 'bill To 详细信息',
          props: {
            rows: 5,
            maxlength: 500
          },
          itemClass: 'dc-merge-1-3'
        }]
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'headId', me.editConfig.headId)
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      },
      handleSaveContinue() {
        let me = this
        me.$set(me.detailConfig.model, 'headId', me.editConfig.headId)
        me.doSave(() => {
          me.refreshIncomingData(false, editStatus.ADD, {
            sid: '',
            billToCode: '',
            billToName: '',
            billToAddress: '',
            headId: me.editConfig.headId
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
