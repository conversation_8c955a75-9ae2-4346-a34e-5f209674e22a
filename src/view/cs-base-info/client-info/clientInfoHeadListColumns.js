import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'customerType'
  , 'customerCode'
  , 'companyName'
  , 'inspectionCode'
  , 'creditCode'
  , 'declareCode'
  , 'aeoCode'
  , 'country'
  , 'city'
  , 'area'
  , 'linkmanName'
  , 'mobilePhone'
  , 'email'
  , 'telephoneNo'
  , 'fax'
  , 'postal'
  , 'address'
  , 'invoiceAddress'
  , 'deliverAddress'
  , 'companyNameEn'
  , 'countryEn'
  , 'areaEn'
  , 'cityEn'
  , 'addressEn'
  , 'invoiceAddressEn'
  , 'deliverAddressEn'
  , 'telephoneNoEn'
  , 'linkmanNameEn'
  , 'mobilePhoneEn'
  , 'emailEn'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'linkmanDutyEn'
  , 'costCenter'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
  , 'customsCreditRating'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
  , 'customsCreditRatingName'
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          title: '代码',
          tooltip: true,
          key: 'customerCode'
        },
        {
          width: 220,
          tooltip: true,
          title: '中文名称',
          key: 'companyName'
        },
        {
          width: 120,
          tooltip: true,
          title: '海关信用等级',
          key: 'customsCreditRating',
          render: (h, params) => {
            return h('span', this.customsCreditRating[params.row.customsCreditRating])
          }
        },
        {
          width: 120,
          title: '海关信用等级',
          key: 'customsCreditRatingName'
        },
        {
          width: 120,
          tooltip: true,
          key: 'declareCode',
          title: '海关注册编码'
        },
        {
          width: 120,
          tooltip: true,
          key: 'creditCode',
          title: '社会信用代码'
        },
        {
          width: 200,
          tooltip: true,
          key: 'aeoCode',
          title: 'AEO代码'
        },
        {
          width: 200,
          tooltip: true,
          key: 'country',
          title: '中文国家'
        },
        {
          width: 200,
          key: 'city',
          tooltip: true,
          title: '中文城市'
        },
        {
          width: 200,
          key: 'area',
          tooltip: true,
          title: '中文地区'
        },
        {
          width: 120,
          tooltip: true,
          title: '客户名称缩写',
          key: 'companyNameShort'
        },
        {
          width: 120,
          tooltip: true,
          title: '客户联系人',
          key: 'linkmanName'
        },
        {
          width: 120,
          tooltip: true,
          key: 'linkmanDuty',
          title: '联系人职务'
        },
        {
          width: 120,
          tooltip: true,
          key: 'mobilePhone',
          title: '联系人手机'
        },
        {
          width: 120,
          key: 'email',
          tooltip: true,
          title: '联系人邮箱'
        },
        {
          width: 120,
          title: '电话',
          tooltip: true,
          key: 'telephoneNo'
        },
        {
          width: 120,
          key: 'fax',
          title: '传真',
          tooltip: true
        },
        {
          width: 120,
          title: '邮编',
          key: 'postal',
          tooltip: true
        },
        {
          width: 220,
          tooltip: true,
          key: 'address',
          title: '中文地址'
        },
        {
          width: 220,
          tooltip: true,
          title: '发票中文地址',
          key: 'invoiceAddress'
        },
        {
          width: 220,
          tooltip: true,
          title: '发货中文地址',
          key: 'deliverAddress'
        },
        {
          width: 120,
          tooltip: true,
          title: '英文国家',
          key: 'countryEn'
        },
        {
          width: 120,
          key: 'cityEn',
          tooltip: true,
          title: '英文城市'
        },
        {
          width: 120,
          key: 'areaEn',
          tooltip: true,
          title: '英文地区'
        },
        {
          width: 220,
          tooltip: true,
          title: '英文地址',
          key: 'addressEn'
        },
        {
          width: 220,
          tooltip: true,
          title: '发票英文地址',
          key: 'invoiceAddressEn'
        },
        {
          width: 220,
          tooltip: true,
          title: '发货英文地址',
          key: 'deliverAddressEn'
        },
        {
          width: 220,
          tooltip: true,
          title: '成本中心',
          key: 'costCenter'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
