<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="detailConfig.model.sid" :showAction="showAction" :just-view="!showAction" business-type="B" title="合同协议"></AcmpInfoListCustom>
    </div>
    <div v-if="showAcmpInfo" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="detailConfig.model.sid" :showAction="showAction" :just-view="!showAction" business-type="B" title="年度评审报告"></AcmpInfoListCustom>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
  import { editStatus, AcmpInfoListCustom, customsCreditRatingSource } from '@/view/cs-common'

  export default {
    name: 'convoyBasicInfoEdit',
    mixins: [baseDetailConfig],
    components: {
      AcmpInfoListCustom
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        ajaxUrl: {
          insert: '',
          update: '',
        },
        formName: 'frmData',
        cmbSource: {
          customsCreditRating: customsCreditRatingSource
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      }
    },
    computed: {
      showAcmpInfo() {
        return this.editConfig.editStatus !== editStatus.ADD
      },
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      }
    },
    methods: {
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
      },
      getFields() {
        return [{
          isCard: true,
          title: '车队基础信息',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '车队代码',
          key: 'customerCode'
        }, {
          title: '中文名称',
          key: 'companyName'
        }, {
          title: '英文名称',
          key: 'companyNameEn'
        }, {
          title: '海关注册编码',
          key: 'declareCode'
        }, {
          title: '社会信用代码',
          key: 'creditCode'
        }, {
          title: '英文国家/地区',
          key: 'countryEn'
        }, {
          type: 'select',
          title: '海关信用等级',
          key: 'customsCreditRating'
        }, {
          title: 'AEO代码',
          key: 'aeoCode'
        }, {
          title: '联系人',
          key: 'linkmanName'
        }, {
          title: '联系人手机',
          key: 'mobilePhone'
        }, {
          title: '联系人邮箱',
          key: 'email'
        }, {
          title: '电话',
          key: 'telephoneNoEn'
        }, {
          title: '传真',
          key: 'fax'
        }, {
          title: '邮编',
          key: 'postal'
        }, {
          title: '中文地址',
          key: 'address'
        }, {
          title: '英文地址',
          key: 'addressEn'
        }]
      },
      handleSave() {
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
