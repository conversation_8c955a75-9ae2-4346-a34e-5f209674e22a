import ConvoyBasicInfoEdit from '../convoy-basic-info-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const convoyBasicInfoList = {
  name: 'convoyBasicInfoList',
  components: {
    ConvoyBasicInfoEdit
  },
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      listConfig: {
        colOptions: true
      },
      cmbSource: {
        status: [{
          value: '0', label: '启用'
        }, {
          value: '1', label: '停用'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this,
      showColumns = [],
      rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
    me.afterSearchSuccess()
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '新增',
        command: 'add',
        icon: 'ios-add',
        key: 'xdo-btn-add'
      }, {
        ...me.actionsComm,
        label: '编辑',
        command: 'edit',
        key: 'xdo-btn-edit',
        icon: 'ios-create-outline'
      }, {
        ...me.actionsComm,
        label: '删除',
        command: 'delete',
        key: 'xdo-btn-delete',
        icon: 'ios-trash-outline'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      })
    },
    getCommParams() {
      return [{
        title: '车队代码',
        key: 'customerCode'
      }, {
        title: '车队名称',
        key: 'companyName'
      }]
    },
    getCommFields() {
      return [{
        width: 90,
        title: '状态',
        key: 'status',
        render: (h, param) => {
          return this.cmbShowRender(h, param, this.cmbSource.status)
        }
      }, {
        width: 220,
        tooltip: true,
        title: '车队名称',
        key: 'companyName'
      }, {
        width: 120,
        tooltip: true,
        title: '车队代码',
        key: 'customerCode'
      }, {
        width: 120,
        tooltip: true,
        key: 'declareCode',
        title: '海关注册编码'
      }, {
        width: 120,
        title: '海关信用等级',
        key: 'customsCreditRatingName'
      }, {
        width: 120,
        tooltip: true,
        title: '车队名称缩写',
        key: 'companyNameShort'
      }, {
        width: 120,
        tooltip: true,
        key: 'creditCode',
        title: '社会信用代码'
      }, {
        width: 120,
        tooltip: true,
        title: '车队联系人',
        key: 'linkmanName'
      }, {
        width: 120,
        tooltip: true,
        title: '联系人职务',
        key: 'linkmanDuty'
      }, {
        width: 120,
        tooltip: true,
        title: '联系人电话',
        key: 'mobilePhone'
      }, {
        width: 120,
        key: 'email',
        tooltip: true,
        title: '联系人邮箱'
      }, {
        width: 220,
        tooltip: true,
        key: 'address',
        title: '车队地址'
      }, {
        width: 200,
        tooltip: true,
        key: 'aeoCode',
        title: 'AEO代码'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleDelete() {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.listConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
            }
          })
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      // this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      me.listConfig.data = [{
        sid: '23462462546',
        status: '0',
        companyName: 'XXX车行',
        customerCode: 'xxxCarCod',
        declareCode: '23462462',
        customsCreditRating: '1',
        customsCreditRatingName: '高级认证企业',
        companyNameShort: 'XXX车行',
        creditCode: '1325135245',
        linkmanName: '张军',
        linkmanDuty: '队长',
        mobilePhone: '13004587586',
        email: '<EMAIL>',
        address: '车队地址',
        aeoCode: 'AEO Code'
      }, {
        sid: '3543547357',
        status: '1',
        companyName: 'XXL车行',
        customerCode: 'xxlCarCod',
        declareCode: '*********',
        customsCreditRating: '2',
        customsCreditRatingName: '一般认证企业',
        companyNameShort: 'XXL车行',
        creditCode: '1325135685',
        linkmanName: '王军',
        linkmanDuty: '幅队长',
        mobilePhone: '13964587586',
        email: '<EMAIL>',
        address: '车队地址AAA',
        aeoCode: 'AEO Code 001'
      }, {
        sid: '56356363',
        status: '0',
        companyName: 'XML车行',
        customerCode: 'xmlCarCod',
        declareCode: '4654218',
        customsCreditRating: '1',
        customsCreditRatingName: '高级认证企业',
        companyNameShort: 'XML车行',
        creditCode: '1328645685',
        linkmanName: '李敏',
        linkmanDuty: '幅队长',
        mobilePhone: '13864582286',
        email: '<EMAIL>',
        address: '车队地址AAA-DSA-085',
        aeoCode: 'AEO Code 005'
      }]
      me.pageParam.dataTotal = 3
    }
  }
}
