/***
 * 关务-基础资料-路由
 */
import { namespace } from '@/project'
import ClientInfoHeadList from './client-info/clientInfoHeadList'
import AnnualReviewList from './annual-review/annual-review-list'
import enterpriseInfoTab from './enterprise-info/enterpriseInfoTab'
import SupplierInfoHeadList from './supplier-info/supplierInfoHeadList'
// import SupplierInfoHeadList from './supplier-info/supplier-info-list'
import ConvoyBasicInfoList from './convoy-basic-info/convoy-basic-info-list'
import CargoAgentInfoHeadList from './cargo-agent-info/cargoAgentInfoHeadList'
import CustomsBrokerInfoHeadList from './customs-broker-info/customsBrokerInfoHeadList'
import OutsourcingContractorList from './outsourcing-contractor-info/outsourcingContractorList'

export {
  enterpriseInfoTab,
  ClientInfoHeadList,
  SupplierInfoHeadList,
  CargoAgentInfoHeadList,
  CustomsBrokerInfoHeadList,
  OutsourcingContractorList
}

export default [
  {
    path: '/' + namespace + '/baseinfo/enterpriseInfo',
    name: 'enterpriseInfoTab',
    meta: {
      title: '企业基础信息'
    },
    component: enterpriseInfoTab
  },
  {
    path: '/' + namespace + '/baseinfo/cargoAgentInfo',
    name: 'cargoAgentInfoHeadList',
    meta: {
      title: '货代基础信息'
    },
    component: CargoAgentInfoHeadList
  },
  {
    path: '/' + namespace + '/baseinfo/customsBrokerInfo',
    name: 'customsBrokerInfoHeadList',
    meta: {
      title: '报关行基础信息'
    },
    component: CustomsBrokerInfoHeadList
  },
  {
    path: '/' + namespace + '/baseinfo/clientInfo',
    name: 'clientInfoHeadList',
    meta: {
      title: '客户基础信息'
    },
    component: ClientInfoHeadList
  },
  {
    path: '/' + namespace + '/baseinfo/supplierInfo',
    name: 'supplierInfoHeadList',
    meta: {
      title: '供应商基础信息'
    },
    component: SupplierInfoHeadList
  },
  {
    path: '/' + namespace + '/baseinfo/OutEnterpriseInfo',
    name: 'OutsourcingContractorList',
    meta: {
      title: '外发承揽者信息'
    },
    component: OutsourcingContractorList
  },
  {
    path: '/' + namespace + '/baseinfo/convoyBasicInfoList',
    name: 'convoyBasicInfoList',
    meta: {
      title: '车队基础信息'
    },
    component: ConvoyBasicInfoList
  },
  {
    path: '/' + namespace + '/baseinfo/annualReviewList',
    name: 'annualReviewList',
    meta: {
      title: '年度评审'
    },
    component: AnnualReviewList
  }
]
