import { baseColumnsShow, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'ownerCode'
  , 'ownerCreditCode'
  , 'ownerName'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const columns = {
  mixins: [baseEditColumns],
  data() {
    let me = this
    let baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 150,
          align: 'center',
          key: 'ownerCode',
          title: '海关十位代码',
          render: (h, params) => {
            let row = params.row
            let index = params.index
            let field = params.column.key
            let scope = this
            let inputVal = row[field]
            if (this.openEditMode(params.row)) {
              let props = {
                clearable: true,
                value: inputVal
              }
              return h('div', [h('Input', {
                props: props,
                on: {
                  input: (val) => {
                    scope.$set(scope.gridConfig.data[index], field, val)
                  },
                  'on-enter': () => {
                    //  根据海关十位编码获取社会信用代码以及企业中文名称
                    if (inputVal.trim().length !== 10) {
                      this.$Message.warning('请输入10位海关编码!')
                      this.$set(this.gridConfig.data[index], 'ownerName', '')
                      this.$set(this.gridConfig.data[index], 'ownerCreditCode', '')
                    } else {
                      let queryCode = inputVal.trim()
                      this.pcodeRemote(this.pcode.company, queryCode).then(res => {
                        if (Array.isArray(res) && res.length > 0) {
                          let theData = res[0]
                          this.$set(this.gridConfig.data[index], 'ownerName', theData.NAME)
                          this.$set(this.gridConfig.data[index], 'ownerCreditCode', theData['CREDIT_CODE'])
                        } else {
                          this.$set(this.gridConfig.data[index], 'ownerName', '')
                          this.$set(this.gridConfig.data[index], 'ownerCreditCode', '')
                        }
                      })
                    }
                  }
                }
              })])
            } else {
              if (params.column.tooltip === true) {
                return scope.toolTipRender(h, inputVal, true)
              } else {
                return h('span', inputVal)
              }
            }
          }
        },
        {
          width: 200,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          title: '社会信用代码',
          key: 'ownerCreditCode',
          render: (h, params) => {
            return me.inputRender(h, params)
          }
        },
        {
          minWidth: 150,
          align: 'center',
          key: 'ownerName',
          title: '企业中文名称',
          render: (h, params) => {
            return me.inputRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
