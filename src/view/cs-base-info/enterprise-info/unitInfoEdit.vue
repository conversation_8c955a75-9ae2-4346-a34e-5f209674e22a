<template>
  <section>
    <XdoCard :bordered="false">
      <template v-for="item in actions">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
          <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
          {{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </XdoCard>
    <XdoCard :bordered="false">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { baseMethods } from './baseMethods'
  import { getColumnsByConfig } from '@/common'
  import { columnsConfig, columns } from './unit-columns'

  export default {
    mixins: [columns, baseMethods],
    props: {
      customerType: {
        type: Object,
        default: () => ''
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    }
  }
</script>
