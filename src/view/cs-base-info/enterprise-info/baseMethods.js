import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const baseMethods = {
  mixins: [commList],
  data() {
    let btnComm = {
      type: 'text',
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      searchLines: 0,
      OffsetHeight: -28,
      hasChildTabs: true,
      ajaxUrl: {
        insert: csAPI.csBaseInfo.productionUnit.insert,
        update: csAPI.csBaseInfo.productionUnit.update,
        delete: csAPI.csBaseInfo.productionUnit.delete,
        selectAllPaged: csAPI.csBaseInfo.productionUnit.selectAllPaged
      },
      actions: [
        { ...btnComm, label: '新增', command: 'add', icon: 'ios-add', key: 'xdo-btn-add', click: this.handleAdd },
        { ...btnComm, label: '删除', command: 'delete', icon: 'ios-trash-outline', key: 'xdo-btn-delete', click: this.handleDelete }
      ]
    }
  },
  methods: {
    /**
     * 新增
     */
    handleAdd() {
      let me = this
      me.addEmptyRow({
        moduleName: '',
        tradeMode: '',
        fixedPrefix: ''
      })
    },
    /**
     * 编辑
     */
    rowEdit() {
      let me = this
      if (me.checkRowSelected('编辑', true)) {
        let index = -1,
          theInd = -1,
          theRow = me.gridConfig.selectRows[0]
        for (let row of me.gridConfig.data) {
          theInd++
          if (row.sid === theRow.sid) {
            index = theInd
          }
        }
        if (index > -1) {
          me.setRowEdit(index)
        } else {
          console.error('当前选中的数据存在问题!')
        }
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 数据保存
     * @param row
     * @param index
     */
    saveEditRow(row, index) {
      let me = this,
        submitRow = JSON.parse(JSON.stringify(row))
      submitRow.customerType = me.customerType
      delete submitRow.$rowStatus
      delete submitRow.$originData
      if (row.ownerCode && row.ownerCode.length !== 10) {
        me.$Message.warning('请输入10位海关编码!')
        me.$set(me.gridConfig.data[index], 'ownerName', '')
        me.$set(me.gridConfig.data[index], 'ownerCreditCode', '')
      } else if (!row.ownerCreditCode) {
        me.$Message.warning('社会信用代码不可为空!')
      } else if (row.ownerCreditCode && row.ownerCreditCode.length !== 18) {
        me.$Message.warning('请输入18位社会信用代码!')
      } else if (row.$rowStatus === editStatus.ADD) {
        if (isNullOrEmpty(me.ajaxUrl.insert)) {
          console.error('未设置新增接口: this.ajaxUrl.insert')
          me.$Message.warning('未设置新增接口: this.ajaxUrl.insert!')
        } else {
          me.$set(me, 'rowSaveLoading', true)
          me.$http.post(me.ajaxUrl.insert, submitRow).then(res => {
            me.$set(me.gridConfig.data[index], 'sid', res.data.data.sid)
            me.$set(me.gridConfig.data[index], '$rowStatus', editStatus.SHOW)
            me.$set(me.gridConfig.data[index], '$originData', {})
            me.baseAfterRowSave(me)
            me.$Message.success(me.successMsg.insert + '!')
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'rowSaveLoading', false)
            try {
              let fn = eval(me.handleSearchSubmit)
              if (typeof (fn) === 'function') {
                fn()
              }
            } catch (e) {
              console.error(e)
            }
          })
        }
      } else if (row.$rowStatus === editStatus.EDIT) {
        if (isNullOrEmpty(me.ajaxUrl.update)) {
          console.error('未设置更新接口: this.ajaxUrl.update')
          me.$Message.warning('未设置更新接口: this.ajaxUrl.update!')
        } else {
          me.$set(me, 'rowSaveLoading', true)
          me.$http.put(`${me.ajaxUrl.update}/${row.sid}`, submitRow).then(() => {
            me.$set(me.gridConfig.data[index], '$rowStatus', editStatus.SHOW)
            me.$set(me.gridConfig.data[index], '$originData', {})
            me.baseAfterRowSave(me)
            me.$Message.success(me.successMsg.update + '!')
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'rowSaveLoading', false)
          })
        }
      } else {
        console.error('需保存的编辑信息有误!')
        me.$Message.warning('需保存的编辑信息有误!')
      }
    },
    /**
     * 获取查询条件
     * @returns {any}
     */
    getSearchParams() {
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {
        customerType: this.customerType
      }))
    }
  }
}
