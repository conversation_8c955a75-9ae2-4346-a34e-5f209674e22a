import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'customerType'
  , 'customerCode'
  , 'companyName'
  , 'inspectionCode'
  , 'creditCode'
  , 'declareCode'
  , 'companyNameShort'
  , 'telephoneNo'
  , 'linkmanName'
  , 'linkmanDuty'
  , 'mobilePhone'
  , 'email'
  , 'address'
  , 'companyNameEn'
  , 'countryEn'
  , 'areaEn'
  , 'cityEn'
  , 'addressEn'
  , 'telephoneNoEn'
  , 'linkmanNameEn'
  , 'mobilePhoneEn'
  , 'emailEn'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'linkmanDutyEn'
  , 'aeoCode'
  , 'masterCustoms'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
  , 'customsCreditRating'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
  , 'customsCreditRatingName'
]

const columns = {
  mixins: [ baseColumns ],
  data () {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          title: '海关注册编码',
          minWidth: 120,
          align: 'center',
          key: 'declareCode',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '企业中文名称',
          minWidth: 220,
          align: 'center',
          key: 'companyName',
          ellipsis: true,
          tooltip: true,
        },
        {
          width: 100,
          tooltip: true,
          ellipsis: true,
          align:'center',
          title: '主管海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          tooltip: true,
          minWidth: 120,
          ellipsis: true,
          align: 'center',
          title: '海关信用等级',
          key: 'customsCreditRating',
          render: (h, params) => {
            return h('span', this.customsCreditRating[params.row.customsCreditRating])
          }
        },
        {
          minWidth: 120,
          align: 'center',
          title: '海关信用等级',
          key: 'customsCreditRatingName'
        },
        {
          title: '社会信用代码',
          minWidth: 120,
          align: 'center',
          key: 'creditCode',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '商检代码',
          minWidth: 120,
          align: 'center',
          key: 'inspectionCode',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '企业电话',
          minWidth: 120,
          align: 'center',
          key: 'telephoneNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '联系人职务',
          minWidth: 120,
          align: 'center',
          key: 'linkmanDuty',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '联系人电话',
          minWidth: 120,
          align: 'center',
          key: 'mobilePhone',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '联系人邮箱',
          minWidth: 120,
          align: 'center',
          key: 'email',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '企业中文地址',
          minWidth: 220,
          align: 'center',
          key: 'address',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: 'AEO代码',
          minWidth: 200,
          align: 'center',
          key: 'aeoCode',
          ellipsis: true,
          tooltip: true
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
