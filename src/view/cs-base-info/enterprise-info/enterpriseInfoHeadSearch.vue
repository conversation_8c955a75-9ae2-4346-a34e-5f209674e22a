<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="declareCode" label="海关注册编码">
        <XdoIInput type="text" v-model="searchParam.declareCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyName" label="中文名称">
        <XdoIInput type="text" v-model="searchParam.companyName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'enterpriseInfoHeadSearch',
    data() {
      return {
        searchParam: {
          declareCode: '',
          companyName: '',
          customerType: 'COM'
        }
      }
    }
  }
</script>

<style scoped>
</style>
