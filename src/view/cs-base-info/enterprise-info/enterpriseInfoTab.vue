<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="bodyTab1" label="企业基础信息">
        <enterpriseInfoEdit v-if="tabs.bodyTab1"></enterpriseInfoEdit>
      </TabPane>
      <TabPane name="bodyTab2" label="消费使用单位">
        <consumptionUnitEdit v-if="tabs.bodyTab2" customerType="I"></consumptionUnitEdit>
      </TabPane>
      <TabPane name="bodyTab3" label="生产销售单位">
        <productionUnitEdit v-if="tabs.bodyTab3" customerType="E"></productionUnitEdit>
      </TabPane>
      <TabPane name="bodyTab4" v-if="certTabShow" label="电子签章">
        <electronicSealList v-if="tabs.bodyTab4"></electronicSealList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import productionUnitEdit from './unitInfoEdit'
  import consumptionUnitEdit from './unitInfoEdit'
  import enterpriseInfoEdit from './enterpriseInfoEdit'
  import electronicSealList from '../electronic-seal/electronicSealList'

  export default {
    name: 'enterpriseInfoTab',
    mixins: [pms],
    components: {
      enterpriseInfoEdit,
      consumptionUnitEdit,
      productionUnitEdit,
      electronicSealList
    },
    data() {
      return {
        actions: [],
        tabName: 'bodyTab1',
        tabs: {
          bodyTab1: true,
          bodyTab2: false,
          bodyTab3: false,
          bodyTab4: false
        }
      }
    },
    created: function () {
      let me = this
      me.loadFunctions('tabs').then()
    },
    computed: {
      certTabShow() {
        let me = this
        let certTabs = me.actions.filter(action => {
          return action.command === 'certElectronic'
        })
        return Array.isArray(certTabs) && certTabs.length > 0
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
