<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <enterpriseInfoHeadSearch ref="headSearch"></enterpriseInfoHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <EnterpriseInfoHeadEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></EnterpriseInfoHeadEdit>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { customsCreditRatingKeys } from '@/view/cs-common'
  import { csBaseInfoList } from '../base/js/csBaseInfoList'
  import EnterpriseInfoHeadEdit from './enterpriseInfoHeadEdit'
  import enterpriseInfoHeadSearch from './enterpriseInfoHeadSearch'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './enterpriseInfoHeadListColumns'

  export default {
    name: 'enterpriseInfoHeadList',
    components: {
      EnterpriseInfoHeadEdit,
      enterpriseInfoHeadSearch
    },
    mixins: [csBaseInfoList, columns],
    data() {
      return {
        actions: [],
        gridConfig: {
          exportTitle: '企业基础信息'
        },
        toolbarEventMap: {
          'add': this.handleAdd,         // 新增
          'edit': this.handleEdit,       // 编辑
          'delete': this.handleDelete,   // 删除
          'export': this.handleDownload  // 导出
        },
        customsCreditRating: customsCreditRatingKeys,
        ajaxUrl: {
          delete: csAPI.csBaseInfo.enterpriseInfo.list.delete,
          exportUrl: csAPI.csBaseInfo.enterpriseInfo.list.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.enterpriseInfo.list.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 下载
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
