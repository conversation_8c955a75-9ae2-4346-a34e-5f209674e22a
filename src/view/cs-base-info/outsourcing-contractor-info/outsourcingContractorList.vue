<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <OutsourcingContractorSearch ref="headSearch"></OutsourcingContractorSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <OutsourcingContractorEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></OutsourcingContractorEdit>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { csBaseInfoList } from '../base/js/csBaseInfoList'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './outsourcingContractorListColumns'
  import OutsourcingContractorEdit from '@/view/cs-base-info/outsourcing-contractor-info/outsourcingContractorEdit'
  import OutsourcingContractorSearch from '@/view/cs-base-info/outsourcing-contractor-info/outsourcingContractorSearch'

  export default {
    name: 'OutsourcingContractorList',
    components: {
      OutsourcingContractorEdit,
      OutsourcingContractorSearch
    },
    mixins: [csBaseInfoList, columns],
    data() {
      return {
        gridConfig: {
          exportTitle: '外发承揽者信息'
        },
        toolbarEventMap: {
          'add': this.handleAdd,          // 新增
          'edit': this.handleEdit,        // 编辑
          'delete': this.handleDelete,    // 删除
          'export': this.handleDownload   // 导出
        },
        ajaxUrl: {
          delete: csAPI.csBaseInfo.outsourcingContractor.list.delete,
          exportUrl: csAPI.csBaseInfo.outsourcingContractor.list.exportUrl,
          selectAllPaged: csAPI.csBaseInfo.outsourcingContractor.list.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
