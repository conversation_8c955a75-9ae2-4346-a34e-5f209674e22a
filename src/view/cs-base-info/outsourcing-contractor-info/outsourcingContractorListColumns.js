import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'companyCode'
  , 'declareCode'
  , 'companyName'
  , 'creditCode'
  , 'masterCustoms'
  , 'districtCode'
  , 'telephoneNo'
  , 'mobilePhone'
  , 'insertTime'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          tooltip: true,
          key: 'companyCode',
          title: '承揽者代码'
        },
        {
          width: 120,
          tooltip: true,
          key: 'declareCode',
          title: '承揽者海关代码'
        },
        {
          width: 220,
          tooltip: true,
          title: '承揽者名称',
          key: 'companyName'
        },
        {
          width: 160,
          tooltip: true,
          key: 'creditCode',
          title: '承揽者社会信用代码'
        },
        {
          width: 150,
          tooltip: true,
          key: 'masterCustoms',
          title: '承揽者所在地主管海关',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 290,
          tooltip: true,
          key: 'districtCode',
          title: '承揽者所在地地区代码',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'telephoneNo',
          title: '承揽者法人/电话'
        },
        {
          width: 160,
          tooltip: true,
          key: 'mobilePhone',
          title: '承揽者联系人/电话'
        },
        {
          width: 100,
          title: '录入日期',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
