<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="companyName" label="承揽者名称">
        <XdoIInput type="text" v-model="searchParam.companyName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'outsourcingContractorSearch',
    data() {
      return {
        searchParam: {
          companyName: ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
