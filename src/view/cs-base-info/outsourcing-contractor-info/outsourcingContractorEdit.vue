<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="companyCode" label="承揽者代码">
            <XdoIInput type="text" v-model="frmData.companyCode" :disabled="codeDisable" :clearable="!codeDisable" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="declareCode" label="承揽者海关代码">
            <XdoIInput type="text" v-model="frmData.declareCode" :disabled="showDisable" :maxlength="10" @on-enter="declareCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="companyName" label="承揽者名称">
            <XdoIInput type="text" v-model="frmData.companyName" :maxlength="70" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="creditCode" label="承揽者社会信用代码">
            <XdoIInput type="text" v-model="frmData.creditCode" :disabled="showDisable" :maxlength="18" @on-enter="onCreditCodeEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="masterCustoms" label="承揽者所在地主管海关">
            <xdo-select v-model="frmData.masterCustoms" :asyncOptions="pcodeList" transfer
                        :meta="pcode.customs_rel" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="districtCode" label="承揽者所在地地区代码">
            <xdo-select v-model="frmData.districtCode" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.area" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="telephoneNo" label="承揽者法人/电话">
            <XdoIInput type="text" v-model="frmData.telephoneNo" :disabled="showDisable" :clearable="!showDisable" :maxlength="127"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="mobilePhone" label="承揽者联系人/电话">
            <XdoIInput type="text" v-model="frmData.mobilePhone" :disabled="showDisable" :clearable="!showDisable" :maxlength="127"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { csBaseInfoEdit } from '../base/js/csBaseInfoEdit'

  export default {
    name: 'outsourcingContractorEdit',
    mixins: [csBaseInfoEdit],
    data() {
      return {
        formName: 'dataForm',
        ajaxUrl: {
          insert: csAPI.csBaseInfo.outsourcingContractor.detail.insert,
          update: csAPI.csBaseInfo.outsourcingContractor.detail.update
        },
        rulesHeader: {
          companyCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          companyName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          telephoneNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          mobilePhone: [{required: true, message: '不能为空!', trigger: 'blur'}],
          districtCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          masterCustoms: [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          companyCode: '',
          declareCode: '',
          companyName: '',
          creditCode: '',
          masterCustoms: '',
          districtCode: '',
          telephoneNo: '',
          mobilePhone: ''
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
