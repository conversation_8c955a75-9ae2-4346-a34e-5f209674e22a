import { delList, erpInterfaceData } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const erpBasicInfoPop = {
  name: 'erpBasicInfoPop',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    customerType: {
      type: String,
      required: true,
      validate: function (value) {
        return ['FOD', 'CUT', 'CLI', 'PRD'].includes(value)
      }
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      pmsLevel: 'extract',
      listConfig: {
        operationColumnShow: false
      },
      cmbSource: {
        customerType: delList.customerType,
        status: erpInterfaceData.EXTRACT_STATUS_MAP,
        modifyMark: erpInterfaceData.DATA_STATUS_MAP
      },
      toolbarEventMap: {
        'ExtractAll': this.handleExtractAll,
        'ExtractSelected': this.handleExtractChoose
      }
    }
  },
  created: function () {
    let me = this
    me.$set(me.listConfig, 'settingColumns', me.getFields())
    me.loadListConfig()
  },
  computed: {
    /**
     * 动态数据源
     * @returns {*}
     */
    dynamicSource() {
      let me = this
      return {
        ...me.cmbSource
      }
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions = [{
        ...me.actionsComm,
        label: '勾选提取',
        key: 'xdo-btn-edit',
        icon: 'ios-checkmark',
        command: 'ExtractSelected'
      }, {
        ...me.actionsComm,
        label: '全部提取',
        icon: 'md-done-all',
        command: 'ExtractAll',
        key: 'xdo-btn-upload'
      }]
    },
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        type: 'select',
        title: '企业类型',
        key: 'customerType',
        props: {
          disabled: true
        }
      }, {
        title: '企业代码',
        key: 'customerCode'
      }, {
        key: 'companyName',
        title: '企业中文名称'
      }, {
        title: '企业英文名称',
        key: 'companyNameEn'
      }, {
        type: 'select',
        title: '数据状态',
        key: 'modifyMark'
      }, {
        range: true,
        title: '接收时间',
        key: 'insertTime'
      }, {
        key: 'tempOwner',
        title: '传输批次号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['status'] = '0'
      paramObj['pickUp'] = '0'
      paramObj['customerType'] = me.customerType
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '企业代码',
        key: 'customerCode'
      }, {
        width: 120,
        title: '企业类型',
        key: 'customerType',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.customerType)
        })
      }, {
        width: 160,
        key: 'companyName',
        title: '企业中文名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 160,
        title: '企业英文名称',
        key: 'companyNameEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'status',
        title: '提取状态',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        title: '数据状态',
        key: 'modifyMark',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.modifyMark)
        })
      }, {
        width: 120,
        key: 'declareCode',
        title: '海关注册编码'
      }, {
        width: 120,
        key: 'creditCode',
        title: '社会信用代码'
      }, {
        width: 160,
        key: 'country',
        title: '中文国家',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'city',
        title: '中文城市',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'area',
        title: '中文地区',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 160,
        title: '英文国家',
        key: 'countryEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'cityEn',
        title: '英文城市',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'areaEn',
        title: '英文地区',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '海关信用等级',
        key: 'customsCreditRating'
      }, {
        width: 120,
        key: 'aeoCode',
        title: 'AEO代码'
      }, {
        width: 120,
        title: '中文联系人',
        key: 'linkmanName'
      }, {
        width: 120,
        title: '联系人手机',
        key: 'mobilePhone'
      }, {
        width: 160,
        key: 'email',
        title: '联系人邮箱',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '联系人电话',
        key: 'linkManPhone'
      }, {
        key: 'fax',
        width: 120,
        title: '传真'
      }, {
        width: 120,
        title: '邮编',
        key: 'postal'
      }, {
        width: 220,
        key: 'address',
        title: '中文地址',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '英文地址',
        key: 'addressEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发票中文地址',
        key: 'invoiceAddress',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '发票英文地址',
        key: 'invoiceAddressEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '送货中文地址',
        key: 'deliverAddress',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        title: '送货英文地址',
        key: 'deliverAddressEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 220,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        width: 150,
        title: 'ERP创建时间',
        key: 'lastModifyDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 150,
        title: '提取时间',
        key: 'extractTime',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 150,
        title: '接收时间',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 150,
        title: '更新时间',
        key: 'updateTime',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }]
    },
    /**
     * 提取勾选
     */
    handleExtractChoose() {
      let me = this
      if (me.checkRowSelected('提取勾选', true)) {
        me.setToolbarLoading('ExtractSelected', true)
        let params = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.extractByChoose + '/' + params).then(res => {
          me.$Message.success('提取成功:' + res.data.data + '条')
          me.$emit('closePickUp')
        }).catch(() => {
          me.$Message.warning('部分数据已提取请确认!')
        }).finally(() => {
          me.setToolbarLoading('ExtractSelected')
        })
      }
    },
    /**
     * 提取全部
     */
    handleExtractAll() {
      let me = this,
        searchParam = me.getSearchParams()
      me.setToolbarLoading('ExtractAll', true)
      me.$http.post(me.ajaxUrl.extractAll + '/' + me.customerType, searchParam).then(res => {
        me.$Message.success(res.data.message)
        me.$emit('closePickUp')
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('ExtractAll')
      })
    }
  }
}
