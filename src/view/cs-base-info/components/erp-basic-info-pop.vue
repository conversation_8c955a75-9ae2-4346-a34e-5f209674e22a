<template>
  <XdoModal width="98%" mask v-model="show" title="提取数据"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </div>
    </XdoCard>
    <hr class="dc-merge-1-3" style="border: 0 none; border-top: 1px solid #DDDDDD; width: 100%; height: 5px;"/>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
      </xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection height="500"
                   :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                   :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                   @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { erpBasicInfoPop } from './js/erpBasicInfoPop'

  export default {
    name: 'erpBasicInfoPop',
    mixins: [erpBasicInfoPop],
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      return {
        initSearch: false,
        ajaxUrl: {
          exportUrl: csAPI.sapErp.shippingDeta.exportPrdSuppUrl,
          extractAll: csAPI.deep.deepImpRecord.head.prdSuppPick.extractAll,
          selectAllPaged: csAPI.deep.deepImpRecord.head.getPrdSupp.getPrdSupp,
          extractByChoose: csAPI.deep.deepImpRecord.head.prdSuppPick.outPickUpList
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.$set(me, 'showSearch', true)
            me.$set(me.searchConfig.model, 'customerType', me.customerType)
            me.handleSearchSubmit()
          }
        }
      }
    },
    methods: {
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .action {
    padding: 0 !important;
  }
</style>
