<template>
  <section>
    <XdoCard :bordered="false">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns"
                :data="gridConfig.data" stripe border>
        <template slot-scope="{ row }" slot="fdfsId">
          <!--标准格式-->
          <template>
            <div class="filesList">
              <span>附件</span>
              <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                      accept=".png"
                      :format="['png']"
                      :show-upload-list="false"
                      :before-upload="handleBeforeUpload"
                      :on-error="handleOnError"
                      :on-success="handleOnSuccess"
                      :on-format-error="handleFormatError"
                      :on-exceeded-size="handleExceededSize">
                <strong>
                  <a icon="md-cloud-upload" long @click.prevent="onUpAttach(row.type)">
                    <XdoIcon type="ios-loop-strong"></XdoIcon>
                    [上传]
                  </a>
                </strong>
              </Upload>
              <ul>
                <li v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
                  <span :title="attachNoListItem.fileName">
                    <XdoIcon title="删除" type="md-close" @click="onDeleteFile(attachNoListItem.fileSid, row.type)"/>
                    <a @click.prevent="downloadFile(attachNoListItem.fileSid)">{{attachNoListItem.fileName}}</a>
                  </span>
                </li>
              </ul>
            </div>
          </template>
        </template>
      </XdoTable>
    </XdoCard>
    <UniversalProgress :show.sync="uploadProgressShow" :completed="uploadCompleted" :failure="uploadFailure"></UniversalProgress>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getHttpHeaderFileName, blobSaveFile } from '@/libs/util'
  import UniversalProgress from '@/components/universal-progress/universal-progress'

  export default {
    name: 'electronicSealList',
    components: {
      UniversalProgress
    },
    data() {
      return {
        timer: null,
        attachTypeEnum: {
          emsListNo: 'emsListNo',
          attachType: 'pactCode',
          invoiceNo: 'invoiceNo',
          boxNo: 'boxNo',
          entryNo: 'entryNo',
          other: 'other',

          invoiceBox: 'invoiceBox',
          review: 'review',
          invBoxBill: 'invBoxBill',
          tax: 'tax',
          statement: 'statement',
          situation: 'situation',
          origin: 'origin',
          orderNo: 'orderNo'
        },
        gridConfig: {
          gridColumns: [{
            width: 120,
            key: 'type',
            align: 'center',
            title: '电子章类型',
            render: (h, params) => {
              if (params.row.type === '1') {
                return h('span', '公章')
              } else if (params.row.type === '2') {
                return h('span', '报关专用章')
              } else if (params.row.type === '3') {
                return h('span', '合同专用章')
              }
            }
          }, {
            align: 'left',
            slot: 'fdfsId',
            title: '印章上传'
          }, {
            width: 168,
            align: 'center',
            title: '上传时间',
            key: 'insertTime'
          }],
          data: [
            {type: '1', fdfsId: '', insertTime: '', attachNoList: []},
            {type: '2', fdfsId: '', insertTime: '', attachNoList: []},
            {type: '3', fdfsId: '', insertTime: '', attachNoList: []}
          ]
        },
        ajaxUrl: {
          getAttachList: csAPI.attachedInfo.list,
          download: csAPI.csBaseInfo.electronicSeal.get,
          upload: csAPI.csBaseInfo.electronicSeal.insert,
          delete: csAPI.csBaseInfo.electronicSeal.delete
        },
        uploadInfo: {
          type: ''
        },
        tmpAttachData: [],
        uploadFailure: false,
        uploadCompleted: false,
        uploadProgressShow: false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      getList() {
        let me = this
        me.$http.post(csAPI.csBaseInfo.electronicSeal.selectAllPaged).then(res => {
          if (res.data.data.length > 0) {
            me.gridConfig.data.map(item => {
              res.data.data.map(data => {
                if (item.type === data.type) {
                  item.insertTime = data.insertTime
                  item.fdfsId = data.fdfsId
                  item.attachNoList.push({
                    fileSid: data.sid,
                    fileName: data.originFileName
                  })
                }
              })
            })
          }
        })
      },
      /**
       * 文件删除
       */
      onDeleteFile(fileSid, type) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            me.$http.delete(`${me.ajaxUrl.delete}/${fileSid}`).then(() => {
              me.$Message.success('删除成功!')
              me.gridConfig.data.map(item => {
                if (item.type === type) {
                  item.attachNoList.splice(0, 1)
                }
              })
            }).catch(() => {
            })
          }
        })
      },
      /**
       * 文件上传
       */
      onUpAttach(type) {
        this.uploadInfo.type = type
      },
      /**
       * 上传之前回调方法
       */
      handleBeforeUpload(file) {
        let me = this
        let exceededNum = file.name.lastIndexOf(".");
        let exceededStr = file.name.substring(exceededNum + 1, file.name.length).trim();
        if (exceededStr !== "png") {
          me.$Message.warning(file.name + '文件格式不正确, 请选择 png')
          me.$set(me, 'uploadCompleted', true)
          return false
        } else {
          me.$set(me, 'uploadFailure', false)
          me.$set(me, 'uploadCompleted', false)
          me.$set(me, 'uploadProgressShow', true)
          return true
        }
      },
      /**
       * 上传错误回调方法
       */
      handleOnError() {
        let me = this
        me.$nextTick(() => {
          console.log('1111')
          me.$Message.error('仅可上传png类型的文件!')
          me.$set(me, 'uploadCompleted', true)
        })
      },
      /**
       * 上传附件成功后回调
       */
      handleOnSuccess(response, file) {
        let me = this
        if (response.success) {
          let attachDataList = {
            fileName: file.name,
            fileSid: response.data.sid
          }
          me.gridConfig.data.filter(item => item.type === response.data.type).map(item => {
            item.attachNoList.splice(0, 1, attachDataList)
            item.insertTime = response.data.insertTime
          })
          me.$set(me, 'uploadCompleted', true)
        } else {
          me.$nextTick(() => {
            me.$set(me, 'uploadFailure', true)
          })
          me.$Message.error(response.message)
        }
      },
      /**
       * 文件格式验证失败时的钩子
       */
      handleFormatError() {
        // let me = this
        // me.$nextTick(() => {
        //   me.$set(me, 'uploadFailure', true)
        // })
      },
      /**
       * 文件超出指定大小限制时的钩子
       */
      handleExceededSize() {
        // let me = this
        // me.$nextTick(() => {
        //   me.$set(me, 'uploadFailure', true)
        // })
      },
      /**
       * 附件下载
       * @param fn
       * @param delay
       * @param sysId
       * @returns {function(): void}
       */
      debounce(fn, delay, sysId) {
        let me = this
        return function () {
          clearTimeout(me.timer)
          let call = !me.timer
          call && fn.call(me, sysId)
          me.timer = setTimeout(function () {
            me.timer = false
          }, delay)
        }
      },
      downloadFile(sysId) {
        let me = this
        me.debounce(me.downFun, 2000, sysId)()
      },
      downFun(sysId) {
        let me = this
        me.$http.get(`${me.ajaxUrl.download}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          const name = getHttpHeaderFileName(res.headers)
          const blob = new Blob([res.data], {type: 'application/octet-stream'})
          blobSaveFile(blob, name)
        }).catch(() => {
        })
      }
    },
    computed: {
      uploadFileConfig() {
        return {
          action: this.ajaxUrl.upload,
          data: {
            type: this.uploadInfo.type
          },
          headers: {
            Authorization: 'Bearer ' + this.$store.state.token
          }
        }
      }
    }
  }
</script>

<style scoped>
  tr.ivu-table-row-hover td {
    background-color: snow;
  }

  .ivu-icon {
    cursor: pointer;
  }

  .ivu-upload {
    width: 80px;
    display: inline-block;
  }

  .filesList {
    margin-top: 2px;
    text-align: left;
    margin-bottom: 2px;
  }

  .filesList strong a {
    font-size: 14px;
  }

  ul li {
    color: #999;
    list-style: none;
    margin-left: 15px;
    display: inline-block;
  }

  .form-title-wrapper {
    line-height: 1px;
    padding: 5px 10px !important;
    background-color: #dcdee2 !important;
  }

  .form-title-wrapper p {
    width: 100%;
    height: 20px;
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
  }

  .form-title-wrapper p span {
    vertical-align: middle;
  }

  .form-title-body-wrapper {
    padding: 8px 8px 2px 8px;
  }

  .form-title-body-wrapper .ivu-form-item {
    margin-bottom: 3px;
  }
</style>
