<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="customerCode" label="货代代码">
        <XdoIInput type="text" v-model="searchParam.customerCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyName" label="货代中文名称">
        <XdoIInput type="text" v-model="searchParam.companyName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'cargoAgentInfoHeadSearch',
    data() {
      return {
        searchParam: {
          companyName: '',
          customerCode: '',
          customerType: 'FOD',
          companyNameShort: ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
