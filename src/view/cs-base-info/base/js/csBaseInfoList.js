import pms from '@/libs/pms'
import { csAPI } from '@/api'
import ImportPage from 'xdo-import'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

export const csBaseInfoList = {
  components: {
    ImportPage
  },
  mixins: [commList, pms, dynamicImport],
  data() {
    return {
      // 查询条件行数
      searchLines: 1,
      // 是否显示导入页面
      modelImportShow: false
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
  },
  methods: {
    /**
     * 数据复制
     * @param btnIndex
     */
    doCopy(btnIndex) {
      let me = this
      if (me.checkRowSelected('复制')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '复制',
          cancelText: '取消',
          content: '确认复制所选项吗',
          onOk: () => {
            me.actions[btnIndex].loading = true
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.copy}/${params}`).then(() => {
              me.$Message.success('复制成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.actions[btnIndex].loading = false
            })
          }
        })
      }
    },
    /**
     * 弹出导入窗体
     */
    handleImport() {
      let me = this
      me.modelImportShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.modelImportShow = false
      me.getList()
    },
    /**
     * 启用
     */
    handleUseopen() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '是否启用',
          onOk: () => {
            const emsCopNos = me.gridConfig.selectRows.map(item => {
              return item.sid
            })
            me.$http.post(csAPI.csBaseInfo.useStatus + `/0` + `/${emsCopNos}`).then(() => {
              me.$Message.success('启用成功')
              me.gridConfig.selectRows = []
              me.handleSearchSubmit()
            }).catch()
          }
        })
      } else {
        me.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
      }
    },
    handleUseclose() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '是否停用',
          onOk: () => {
            const emsCopNos = me.gridConfig.selectRows.map(item => {
              return item.sid
            })
            me.$http.post(csAPI.csBaseInfo.useStatus + `/1` + `/${emsCopNos}`).then(() => {
              me.$Message.success('停用成功')
              me.gridConfig.selectRows = []
              me.handleSearchSubmit()
            }).catch(() => {
            })
          }
        })
      } else {
        me.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
      }
    }
  }
}
