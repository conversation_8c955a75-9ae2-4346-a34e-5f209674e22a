import { isNullOrEmpty } from '@/libs/util'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
import { editStatus, customsCreditRatingSource } from '@/view/cs-common'

export const csBaseInfoEdit = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      customsCreditRatingSource: customsCreditRatingSource,
      buttons: [
        {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
        {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * @param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave((res) => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存继续
     */
    handleSaveContinue() {
      let me = this
      me.doSave(() => {
        me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
      })
    },
    /**
     * 根据海关10位编码获取【承揽者名称】和【承揽者社会信用代码】
     */
    declareCodeEnter() {
      let me = this
      if (isNullOrEmpty(me.frmData.declareCode)) {
        me.$set(me.frmData, 'companyName', '')
        me.$set(me.frmData, 'creditCode', '')
      } else {
        if (me.frmData.declareCode.trim().length !== 10) {
          me.$set(me.frmData, 'companyName', '')
          me.$set(me.frmData, 'creditCode', '')
          me.$Message.warning('海关代码必须是10位!')
        } else {
          me.pcodeRemote(me.pcode.company, me.frmData.declareCode).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.frmData, 'companyName', res[0]['NAME'])
              me.$set(me.frmData, 'creditCode', res[0]['CREDIT_CODE'])
            } else {
              me.$set(me.frmData, 'companyName', '')
              me.$set(me.frmData, 'creditCode', '')
            }
          }).catch(() => {
            me.$set(me.frmData, 'companyName', '')
            me.$set(me.frmData, 'creditCode', '')
          })
        }
      }
    },
    /**
     * 根据承揽者社会信用代码获取【承揽者海关代码】和【承揽者名称】
     */
    onCreditCodeEnter() {
      let me = this
      if (isNullOrEmpty(me.frmData.creditCode)) {
        me.$set(me.frmData, 'declareCode', '')
        me.$set(me.frmData, 'companyName', '')
      } else {
        let creditCode = me.frmData.creditCode.trim()
        if (creditCode.length !== 18) {
          me.$set(me.frmData, 'declareCode', '')
          me.$set(me.frmData, 'companyName', '')
          me.$Message.warning('承揽者社会信用代码必须是18位!')
        } else {
          me.pcodeRemote('COMPANY_BY_CREDITCODE', creditCode).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.frmData, 'declareCode', res[0]['CODE'])
              me.$set(me.frmData, 'companyName', res[0]['NAME'])
            } else {
              me.$set(me.frmData, 'declareCode', '')
              me.$set(me.frmData, 'companyName', '')
            }
          }).catch(() => {
            me.$set(me.frmData, 'declareCode', '')
            me.$set(me.frmData, 'companyName', '')
          })
        }
      }
    }
  },
  computed: {
    codeDisable() {
      if (this.editConfig.editStatus !== editStatus.ADD) {
        return true
      }
      return this.showDisable
    }
  }
}
