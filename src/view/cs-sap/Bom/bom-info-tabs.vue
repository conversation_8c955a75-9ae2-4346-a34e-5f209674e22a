<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="originalTab" label="原始BOM">
        <bomOriginalList></bomOriginalList>
      </TabPane>
      <TabPane name="standardTab" label="标准BOM">
        <bomStandardList ref="standardList"></bomStandardList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import bomOriginalList from './bom-original-list'
  import bomStandardList from './bom-standard-list'

  export default {
    name: 'bomInfoTabs',
    components: {
      bomOriginalList,
      bomStandardList
    },
    data() {
      return {
        tabName: 'originalTab',
        tabs: {
          originalTab: true,
          standardTab: false
        }
      }
    },
    watch: {
      tabName: {
        handler: function(value) {
          let me = this
          me.tabs[value] = true
          if (value === 'standardTab') {
            if (me.$refs['standardList']) {
              if (typeof me.$refs['standardList'].handleSearchSubmit === 'function') {
                me.$refs['standardList'].handleSearchSubmit()
              }
            }
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
