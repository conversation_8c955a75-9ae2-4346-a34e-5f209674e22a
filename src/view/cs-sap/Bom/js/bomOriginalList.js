import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const bomOriginalList = {
  name: 'bomOriginalList',
  mixins: [baseSearchConfig, columnRender, listDataProcessing],
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      hasChildTabs: true,
      toolbarEventMap: {
        'clean': this.handleClean,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'copExgNo',
        title: '成品料号'
      }, {
        key: 'exgVersion',
        title: '成品版本号'
      }, {
        key: 'copImgNo',
        title: '料件料号'
      }, {
        key: 'matGroup',
        title: '物料群组'
      }, {
        key: 'facGNo',
        title: '原料关务料号'
      }, {
        range: true,
        title: '接收时间',
        key: 'insertTime'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      return [{
        width: 150,
        key: 'copExgNo',
        title: '成品料号'
      }, {
        width: 120,
        title: '版本号',
        key: 'exgVersion'
      }, {
        width: 120,
        key: 'exgGroup',
        title: '成品物料群组'
      }, {
        width: 120,
        key: 'exgUnit',
        title: '成品单位'
      }, {
        width: 150,
        key: 'copImgNo',
        title: '料件料号'
      }, {
        width: 180,
        key: 'imgNote',
        title: '料件料号说明'
      }, {
        width: 120,
        key: 'unit',
        title: '基础计量单位'
      }, {
        width: 150,
        key: 'matGroup',
        title: '物料群组'
      }, {
        width: 180,
        key: 'matGroupName',
        title: '物料群组说明'
      }, {
        width: 150,
        key: 'matGroupOut',
        title: '外部物料群组'
      }, {
        width: 180,
        key: 'matGroupNameOut',
        title: '外部物料群组说明'
      }, {
        width: 180,
        key: 'consumptionTotal',
        title: '备案理论总耗用(多阶)'
      }, {
        width: 150,
        key: 'facGNo',
        title: '原料关务料号'
      }, {
        width: 120,
        key: 'factor',
        title: '计量比例因子'
      }, {
        width: 150,
        key: 'imgUnit',
        title: '元件计量单位'
      }, {
        width: 150,
        key: 'gname',
        title: '海关商品名称'
      }, {
        width: 100,
        title: '数据状态',
        key: 'modifyMark'
      }, {
        width: 138,
        key: 'lastUpdateTime',
        title: 'ERP最后变更日期'
      }, {
        width: 138,
        key: 'insertTime',
        title: '关务接收日期'
      }]
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * bom清洗
     */
    handleClean() {
      let me = this
      me.$http.post(me.ajaxUrl.bomClean).then(() => {
        me.handleSearchSubmit()
      }).catch(() => {
      })
    }
  }
}
