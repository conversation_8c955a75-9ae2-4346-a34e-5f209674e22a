import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { productClassify } from '@/view/cs-common'

export const bomStandardList = {
  name: 'bomStandardList',
  mixins: [baseSearchConfig, columnRender, listDataProcessing,productClassify],
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      hasChildTabs: true,
      toolbarEventMap: {
        'send': this.handleBomSend,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'gmark',
        title: '物料标识'
      }, {
        key: 'copExgNo',
        title: '成品半成品料号'
      }, {
        key: 'BOM版本号',
        title: '成品版本号'
      }, {
        key: 'copImgNo',
        title: '企业料件料号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      return [{
        width: 150,
        key: 'gmark',
        title: '物料标识',
        render: (h, params) => {
          return this.cmbShowRender(h, params, productClassify.GMARK_SELECT_BOM)
        }
      }, {
        width: 150,
        key: 'copExgNo',
        title: '成品半成品料号'
      }, {
        width: 120,
        title: 'BOM版本号',
        key: 'exgVersion'
      }, {
        width: 150,
        key: 'copImgNo',
        title: '企业料件料号'
      }, {
        width: 120,
        key: 'decCm',
        title: '净耗'
      }, {
        width: 180,
        key: 'decDm',
        title: '有形损耗%'
      }, {
        width: 180,
        key: 'decDmInvisiable',
        title: '无形损耗'
      }, {
        width: 180,
        key: 'bondmarkProportion',
        title: '保税料件比例'
      }]
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * bom发送
     */
    handleBomSend() {
      let me = this
      me.$http.post(me.ajaxUrl.bomSend).then(() => {
        me.handleSearchSubmit()
        me.$Message.success('发送成功')
      }).catch(() => {
      })
    },
    /**
     * Excel导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
