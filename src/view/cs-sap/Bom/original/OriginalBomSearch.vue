<template>
    <section class="xdo-enter-root" v-focus>
      <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="120" inline>
        <XdoFormItem prop="copExgNo" label="成品料号">
          <XdoIInput type="text" v-model="searchParam.copExgNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="成品版本号">
          <XdoIInput v-model="searchParam.exgVersion"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="料件料号">
          <XdoIInput v-model="searchParam.copImgNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="物料群组">
          <XdoIInput v-model="searchParam.matGroup"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="原料关务料号">
          <XdoIInput v-model="searchParam.facGNo"></XdoIInput>
        </XdoFormItem>
        <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
      </XdoForm>
    </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'BomSearch',
    data() {
      return {
        searchParam: {
          copExgNo: '',
          copImgNo: '',
          exgVersion: '',
          matGroup: '',
          facGNo: '',
          insertTimeFrom: '',
          insertTimeTo: ''
        },
        productClassify: productClassify
      }
    },
    methods: {
      /**
       * ERP数据抛转日期范围
       * @param values
       */
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
