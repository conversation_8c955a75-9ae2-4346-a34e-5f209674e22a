import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 150,
          key: 'copExgNo',
          title: '成品料号'
        },
        {
          width: 120,
          key: 'exgVersion',
          title: '版本号'
        },
        {
          width: 120,
          key: 'exgUnit',
          title: '成品单位'
        },
        {
          width: 150,
          key: 'copImgNo',
          title: '料件料号'
        },
        {
          width: 180,
          key: 'imgNote',
          title: '料件料号说明'
        },
        {
          width: 120,
          key: 'unit',
          title: '基础计量单位'
        },
        {
          width: 150,
          key: 'matGroup',
          title: '物料群组'
        },
        {
          width: 180,
          key: 'matGroupName',
          title: '物料群组说明'
        },
        {
          width: 180,
          key: 'consumptionTotal',
          title: '备案理论总耗用（多阶）'
        },
        {
          width: 150,
          key: 'facGNo',
          title: '原料关务料号'
        },
        {
          width: 120,
          key: 'factor',
          title: '计量比例因子'
        },
        {
          width: 150,
          key: 'imgUnit',
          title: '元件计量单位'
        },
        {
          width: 150,
          key: 'gname',
          title: '海关商品名称'
        },
        {
          width: 100,
          title: '数据状态',
          key: 'modifyMark'
        },
        {
          width: 138,
          key: 'lastUpdateTime',
          title: 'ERP最后变更日期'
        },
        {
          width: 138,
          title: '关务接收日期',
          key: 'insertTime'
        },
        {
          width: 138,
          title: '关务更新日期',
          key: 'updateTime'
        }
      ]
    }
  }
}
