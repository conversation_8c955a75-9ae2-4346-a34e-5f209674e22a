<template>
  <XdoModal width="1024" mask v-model="show" title="预警提示"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="300"></DcAgGrid>
      <div ref="area_page" style="text-align: right; padding-right: 20px;">
        共 {{pageParam.dataTotal}} 条
      </div>
      <div style="text-align: center;">
        <XdoButton type="primary" class="dc-margin-right" :loading="loading" @click="continueProduct" :disabled="isContinue">继续</XdoButton>
        <XdoButton type="primary" class="dc-margin-right" @click="handleClose">取消</XdoButton>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
import { commList } from '@/view/cs-interim-verification/comm/commList'

export default {
  mixins: [commList],
  props: {
    show: {
      type: Boolean,
      require: true
    },
    isContinue: {
      type: Boolean,
      required: true
    },
    errData: {
      type: Boolean,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      initSearch: false,
      gridConfig: {
        gridColumns: [{
          width: 120,
          key: 'copImgNo',
          title: '企业料号',
          render: (h, params) => {
            if (params.row.warningMark === '2') {
              return h('span', {
                style: {
                  color: 'red'
                }
              }, params.row.copImgNo)
            } else {
              return h('span', {}, params.row.copImgNo)
            }
          }
        }, {
          width: 350,
          title: '警告信息',
          key: 'warningMsg',
          render: (h, params) => {
            if (params.row.warningMark === '2') {
              return h('span', {
                style: {
                  color: 'red'
                }
              }, params.row.warningMsg)
            } else {
              return h('span', {}, params.row.warningMsg)
            }
          }
        }]
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        let me = this,
          theData = []
        if (Array.isArray(me.errData)) {
          theData = me.errData
        }
        me.$set(me, 'loading', false)
        me.gridConfig.data = theData
        me.pageParam.dataTotal = theData.length
      }
    }
  },
  methods: {
    handleClose() {
      let me = this
      me.$emit('onContinue', false)
    },
    continueProduct() {
      let me = this
      me.loading = true
      me.$emit('onContinue', true)
    },
    rowClassName(row) {
      if (row.warningMark === '2') {
        return 'demo-table-info-row'
      }
      return ''
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ivu-modal-body {
  padding: 1px !important;
  background-color: #E9EBEE !important;
}

.ivu-form-item {
  margin-bottom: 5px;
}

.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}

/deep/ .ivu-table .demo-table-info-row td {
  color: red;
  font-weight: bold;
}
</style>
