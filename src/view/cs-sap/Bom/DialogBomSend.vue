<template>
  <XdoModal v-model="show" :mask-closable="false" :closable="false" :footer-hide="true" :mask="true" width="500" title="发送标准BOM">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px;color: #389DE9;line-height: 1;"></XdoIcon>
    </a>
    <XdoForm class="dc-form dc-form-1 " ref="form" :show-message="false" :model="selectForm" :label-width="100" inline>
      <XdoFormItem prop="contrNo" label="备案号">
        <xdo-select v-model="selectForm.emsNo" :options="this.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-merge-1-3" style="text-align: right">
        <XdoButton type="success" icon="ios-cloud-upload" @click="handleConfirm">确定</XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>

import { csAPI } from '@/api'

export default {
  name: 'DialogBomSend',
  props:{
    show: { type: Boolean, required: true },
    editConfig: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      selectForm: {
        emsNo:''
      },
      emsNoList: [],
      /**
       * 表单校验
       */
      ruleValidate: {
        emsNo: [
          { required: true, message: '备案号不能为空！', trigger: 'blur' },
        ]
      },
    }
  },
  created() {
    let me = this
    // 备案号
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.emsNoList = res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE
        }
      })
    }).catch(() => {
      me.cmbSource.emsNoData = []
    })
  },
  watch:{
    show:{
      immediate:true,
      handler:function(show){
        if(show)
        {
          // this.selectForm= this.editConfig.editData
        }
      }
    }
  },
  methods: {
    /**
     * 确定
     */
    handleConfirm() {
      this.$emit('onConfirm', this.selectForm)
      this.loadingTrue = true

    },
    /**
     * 关闭
     */
    handleClose() {
      this.$emit('update:show', false)
    }
  }

}
</script>
<style lang="less" scoped>
.dc-form-1 {
  display: grid;
  grid-column-gap: 10px;
  grid-template-columns: repeat(1, 1fr);
}

.dc-form-1 > div {
  grid-column: 1/2;
}
</style>

