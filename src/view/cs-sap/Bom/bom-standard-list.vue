<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </XdoCard>
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <DialogBomSend :show.sync='isShowBomSend' @onConfirm='handleDialogBomSendConfirm'></DialogBomSend>
    <BomSubmitPopUps :show.sync="bomSubmitConfig.show" :err-data="bomSubmitConfig.data"
                            @onContinue="onSubmitContinue"  :isContinue="isContinue"></BomSubmitPopUps>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { bomStandardList } from '@/view/cs-sap/Bom/js/bomStandardList'
  import DialogBomSend from '@/view/cs-sap/Bom/DialogBomSend'
  import BomSubmitPopUps from '@/view/cs-sap/Bom/Bom-submit-pop-ups'
  import {isNullOrEmpty} from "@/libs/util";

  export default {
    name: 'bomStandardList',
    components: { BomSubmitPopUps, DialogBomSend },
    mixins: [bomStandardList],
    data() {
      return {
        isShowBomSend:false,
        pmsLevel: 'standard',
        listConfig: {
          exportTitle: '标准BOM信息',
          operationColumnShow: false
        },
        toolbarEventMap: {
          'bomSend': this.handleBomSend, //新增
        },
        ajaxUrl: {
          bomSend: csAPI.sapErp.standardBom.bomSend,
          exportUrl: csAPI.sapErp.standardBom.bomExport,
          selectAllPaged: csAPI.sapErp.standardBom.bomList,
          bomSendCheck: csAPI.sapErp.standardBom.bomSendCheck
        },
        financeConfig: {
          source: '',
          show: false,
          financeConfirm: ''
        },
        bomSubmitConfig: {
          data: [],
          show: false,
          emsNo: '',
          loadingKey: '',
          confirmValue: ''
        },
        isContinue: true
      }
    },
    methods: {
      handleBomSend(){
        this.isShowBomSend = true
      },
      /**
       * 弹出框确定事件
       * @param selectData  弹出框 回传的数据
       */
     /* handleDialogBomSendConfirm(selectForm) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认发送所有BOM信息？',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            this.$http.post(this.ajaxUrl.bomSend + '/'+selectForm.emsNo).then((res) => {
              this.$Message.success(res.data.message)
              if (res.data.success) {
                this.isShowBomSend = false
                this.handleSearchSubmit()
              }
            }).catch(() => {
            }).finally(
            )
          }
        })
      },*/

      doFinance( emsNo, btnKey) {
        let me = this
        me.$http.post(me.ajaxUrl.bomSend + '/' + emsNo).then(() => {
          me.$Message.success('发送成功!')
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading(btnKey)
        })
      },
      handleDialogBomSendConfirm(selectForm) {
        let me = this,
          btnKey = 'finance-affirm'
        // me.$set(me.isShowBomSend, false)
          let confirmCheckUrl = me.ajaxUrl.bomSendCheck
          confirmCheckUrl += '/' + selectForm.emsNo

        me.setToolbarLoading(btnKey, true)
        me.$http.post(confirmCheckUrl).then(()=>{
          me.doFinance( selectForm.emsNo, btnKey)
        }).catch((err) => {
          if (Array.isArray(err.data.data) && err.data.data.length > 0) {
            me.$set(me.bomSubmitConfig, 'loadingKey', btnKey)
            me.$set(me.bomSubmitConfig, 'data', err.data.data)
            me.$set(me.bomSubmitConfig, 'emsNo', selectForm.emsNo)
            me.$set(me.bomSubmitConfig, 'show', true)
            me.isContinue = !isNullOrEmpty(err.data['errorField'])
          }
          me.setToolbarLoading(btnKey)
        })

      },

      onSubmitContinue(isContinue) {
        let me = this
        me.$set(me.bomSubmitConfig, 'show', false)
        if (isContinue) {
           // 采购
            me.doFinance(me.bomSubmitConfig.emsNo, me.bomSubmitConfig.loadingKey)
        } else {
          me.setToolbarLoading(me.bomSubmitConfig.loadingKey)
        }
      },

    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
