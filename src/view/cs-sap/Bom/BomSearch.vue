<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="copExgNo" label="成品料号">
        <XdoIInput type="text" v-model="searchParam.copExgNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="料件料号">
        <XdoIInput v-model="searchParam.copImgNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="料件单位">
        <XdoIInput v-model="searchParam.imgUnit"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="成品单位">
        <XdoIInput v-model="searchParam.exgUnit"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="有效日期" @onDateRangeChanged="handleValidDateChange"></dc-dateRange>
      <XdoFormItem label="料件保完税标志">
        <xdo-select v-model="searchParam.imgBondMark" :options="this.productClassify.BONDED_FLAG_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="成品保完税标志" >
        <xdo-select v-model="searchParam.exgBondMark" :options="this.productClassify.BONDED_FLAG_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="工厂">
        <XdoIInput v-model="searchParam.factory"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="ERP变更日期" @onDateRangeChanged="lastModifyDateChange"></dc-dateRange>
      <XdoFormItem label="成品版本号">
        <XdoIInput v-model="searchParam.exgVersion"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'BomSearch',
    data() {
      return {
        searchParam: {
          copExgNo: '',
          copImgNo: '',
          imgUnit: '',
          exgUnit: '',
          imgBondMark: '',
          exgBondMark: '',
          factory: '',
          lastModifyDate: '',
          exgVersion: ''
        },
        productClassify: productClassify
      }
    },
    methods: {
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "validBeginDateFrom", values[0])
          this.$set(this.searchParam, "validEndDateTo", values[1])
        } else {
          this.$set(this.searchParam, "validBeginDateFrom", '')
          this.$set(this.searchParam, "validEndDateTo", '')
        }
      },
      lastModifyDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "lastModifyDateFrom", values[0])
          this.$set(this.searchParam, "lastModifyDateTo", values[1])
        } else {
          this.$set(this.searchParam, "lastModifyDateFrom", '')
          this.$set(this.searchParam, "lastModifyDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
