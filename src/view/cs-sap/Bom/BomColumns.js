import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          key: 'copExgNo',
          title: '成品料号'
        },
        {
          width: 120,
          title: '项次',
          key: 'itemNo'
        },
        {
          width: 120,
          key: 'exgVersion',
          title: '成品版本号'
        },
        {
          width: 130,
          key: 'copImgNo',
          title: '料件料号'
        },
        {
          width: 130,
          key: 'decCm',
          title: '净耗'
        },
        {
          width: 130,
          title: '单耗',
          key: 'decAllConmuse'
        },
        {
          width: 130,
          key: 'baseNum',
          title: '主件底数'
        },
        {
          width: 120,
          key: 'imgUnit',
          title: '料件单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'exgUnit',
          title: '成品单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'decDm',
          title: '有形损耗率'
        },
        {
          width: 120,
          title: '无形损耗率',
          key: 'decDmInvisiable'
        },
        {
          width: 120,
          title: '保税料件比例',
          key: 'bondMtpckPrpr'
        },
        {
          width: 138,
          title: '有效起始日期',
          key: 'validBeginDate'
        },
        {
          width: 138,
          key: 'validEndDate',
          title: '有效结束日期'
        },
        {
          width: 120,
          key: 'imgBondMark',
          title: '料件保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.BONDED_FLAG)
          }
        },
        {
          width: 120,
          key: 'exgBondMark',
          title: '成品保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.BONDED_FLAG)
          }
        },
        {
          width: 120,
          title: '工厂',
          key: 'factory'
        },
        {
          width: 120,
          key: 'remark1',
          title: 'remark1'
        },
        {
          width: 120,
          key: 'remark2',
          title: 'remark2'
        },
        {
          width: 140,
          key: 'tempOwner',
          title: '导入批次号'
        },
        {
          width: 138,
          key: 'lastModifyDate',
          title: 'ERP最后变更日期'
        },
        {
          width: 140,
          title: '数据状态',
          key: 'modifyMark'
        },
        {
          width: 138,
          title: '接收日期',
          key: 'insertTime'
        },
        {
          width: 138,
          title: '更新日期',
          key: 'updateTime'
        }
      ]
    }
  }
}
