import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { erpInterfaceData, productClassify } from '@/view/cs-common'

export const domesticSalesDetailsList = {
  name: 'domesticSalesDetailsList',
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      autoCreate: false,
      productClassify: productClassify,
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        gmark: erpInterfaceData.MAT_FLAG_MAP,
        status: erpInterfaceData.EXTRACT_STATUS_MAP,
        modifyMark: erpInterfaceData.DATA_STATUS_MAP
      }
    }
  },
  /**
   * 新建
   */
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = []
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
  },
  methods: {
    getParams() {
      return [{
        title: '批号',
        key: 'batchNo'
      }, {
        key: 'billNo',
        title: '单据编号'
      }, {
        title: '客户代码',
        key: 'customerNo'
      }, {
        key: 'exgVersion',
        title: '成品版本号'
      }, {
        title: '料号',
        key: 'facGNo'
      }, {
        title: '工厂',
        key: 'factory'
      }, {
        key: 'gmark',
        type: 'select',
        title: '物料类型标志'
      }, {
        title: '项次',
        key: 'itemNo'
      }, {
        range: true,
        key: 'lastModifyDate',
        title: 'ERP最后变更日期'
      }, {
        range: true,
        key: 'soldDate',
        title: '销售日期'
      }, {
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        key: 'unit',
        title: 'ERP单位'
      }]
    },
    getFields() {
      return [{
        width: 160,
        title: '批号',
        key: 'batchNo',
        align: 'center'
      }, {
        width: 120,
        key: 'billNo',
        align: 'center',
        title: '单据编号'
      }, {
        width: 120,
        align: 'center',
        title: '客户代码',
        key: 'customerNo'
      }, {
        width: 100,
        align: 'center',
        key: 'exgVersion',
        title: '成品版本号'
      }, {
        width: 180,
        title: '料号',
        key: 'facGNo',
        align: 'center'
      }, {
        width: 180,
        title: '工厂',
        key: 'factory',
        align: 'center'
      }, {
        width: 130,
        key: 'gmark',
        align: 'center',
        title: '物料类型标志',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.gmark)
        }
      }, {
        width: 130,
        align: 'center',
        key: 'gmarkConvert',
        title: '转换后物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.gmark)
        }
      }, {
        width: 90,
        align: 'center',
        title: '创建时间',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        tooltip: true,
        title: '创建人',
        align: 'center',
        key: 'insertUser'
      }, {
        width: 120,
        title: '项次',
        key: 'itemNo',
        tooltip: true,
        align: 'center'
      }, {
        width: 150,
        align: 'center',
        key: 'lastModifyDate',
        title: 'ERP最后变更日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        width: 100,
        type: 'select',
        align: 'center',
        title: '数据状态',
        key: 'modifyMark'
      }, {
        width: 180,
        key: 'note',
        title: '备注',
        tooltip: true,
        align: 'center'
      }, {
        key: 'qty',
        width: 180,
        title: '数量',
        tooltip: true,
        align: 'center'
      }, {
        width: 180,
        tooltip: true,
        key: 'remark1',
        align: 'center',
        title: 'remark1'
      }, {
        width: 180,
        tooltip: true,
        key: 'remark2',
        align: 'center',
        title: 'remark2'
      }, {
        width: 180,
        tooltip: true,
        key: 'remark3',
        align: 'center',
        title: 'remark3'
      }, {
        width: 90,
        align: 'center',
        key: 'soldDate',
        title: '销售日期'
      }, {
        width: 90,
        key: 'status',
        type: 'select',
        align: 'center',
        title: '提取状态'
      }, {
        width: 120,
        align: 'center',
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        width: 120,
        key: 'unit',
        align: 'center',
        title: '申报单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 120,
        align: 'center',
        key: 'unitConvert',
        title: '转换后申报单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 90,
        align: 'center',
        title: '更新时间',
        key: 'updateTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        tooltip: true,
        title: '更新人',
        align: 'center',
        key: 'updateUser'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.columns = columns
      me.listSetupShow = false
    },
    /**
     * 导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
