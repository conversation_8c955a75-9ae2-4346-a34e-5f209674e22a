<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" labelWidth="110">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { wholeOrderList } from './js/wholeOrderList'
  import { importExportManage, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'wholeOrderIList',
    mixins: [wholeOrderList],
    data() {
      return {
        iEMark: 'I',
        defaultFields: [],
        listConfig: {
          exportTitle: '进口提单整单'
        },
        erpInterfaceData: erpInterfaceData,
        taskInfo: {
          taskCode: 'DEC_I'     // 添加任务使用的taskCode
        },
        cmbSource: {
          inWay: erpInterfaceData.IMPORT_METHOD_MAP,
          gmarkConvert: erpInterfaceData.MAT_FLAG_MAP,
          inWayConvert: erpInterfaceData.IMPORT_METHOD_MAP,
          bondMarkConvert: importExportManage.bondedFlagMap
        },
        ajaxUrl: {
          exportUrl: csAPI.sapErp.billOfLading.imports.exportUrl,
          selectAllPaged: csAPI.sapErp.billOfLading.imports.selectAllPaged
        }
      }
    },
    methods: {
      getUniqueParams() {
        return [{
          title: '提运单号',
          key: 'mawb'
        }, {
          title: '运输方式',
          key: 'trafMode',
          type: 'pcode',
          props: {
            meta: 'TRANSF' // this.pcode.transf
          }
        }, {
          title: '监管方式',
          key: 'tradeMode',
          type: 'pcode',
          props: {
            meta: 'TRADE' // this.pcode.trade
          }
        }, {
          range: true,
          title: '接收时间',
          key: 'insertTime'
        }, {
          title: '转换后保完税标记',
          key: 'bondMarkConvert',
          type: 'select'
        }, {
          title: '转换后物料类型',
          key: 'gmarkConvert',
          type: 'select'
        }, {
          title: '企业料号',
          key: 'facGNo'
        }, {
          title: '备案料号',
          key: 'emsNo'
        }, {
          title: '发票号',
          key: 'invoiceNo'
        }, {
          title: '采购单号',
          key: 'purchaseNo'
        }, {
          title: '供应商名称',
          key: 'supplierName'
        }, {
          title: '进口方式',
          key: 'inWayConvert',
          type: 'select'
        }, {
          range: true,
          title: '订单日期',
          key: 'poDate'
        }, {
          title: '单价',
          key: 'decPrice',
          type: 'checkBox',
          'true-value': 1,
          'false-value': null
        }, {
          title: '净重',
          key: 'netWt',
          type: 'checkBox',
          'true-value': 1,
          'false-value': null
        }, {
          title: '传输批次号',
          key: 'tempOwner'
        }]
      },
      getUniqueFields() {
        return [{
          title: '启运港',
          key: 'despPort',
          align: 'center',
          width: 220,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.port_lin)
          }
        }, {
          title: '货物存放地点',
          width: 150,
          align: 'center',
          key: 'goodsStroePlace',
          ellipsis: true,
          tooltip: true
        }, {
          title: '采购订单号',
          width: 120,
          align: 'center',
          key: 'purchaseNo'
        }, {
          title: '采购订单行号',
          width: 120,
          align: 'center',
          key: 'purchaseLineNo'
        }, {
          title: '采购订单日期',
          width: 120,
          align: 'center',
          key: 'orderDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }, {
          title: '厂商编号',
          width: 150,
          align: 'center',
          key: 'factory'
        }, {
          title: '采购数量',
          width: 120,
          align: 'center',
          key: 'purchaseNum'
        }, {
          title: '供应商代码',
          width: 120,
          align: 'center',
          key: 'supplierCode'
        }, {
          title: '供应商名称',
          width: 120,
          align: 'center',
          key: 'supplierName'
        }, {
          title: '进口方式',
          width: 120,
          align: 'center',
          key: 'inWay',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.IMPORT_METHOD_MAP)
          }
        }, {
          width: 120,
          key: 'inOutRelNo',
          title: '入库关联单号'
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
