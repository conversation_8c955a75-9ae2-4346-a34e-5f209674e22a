import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { erpInterfaceData, importExportManage } from '@/view/cs-common'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'

export const wholeOrderList = {
  name: 'wholeOrderList',
  mixins: [baseSearchConfig, baseListConfig, dynamicExport],
  data() {
    let commParams = this.getCommParams()
    let uniqueParams = this.getUniqueParams()
    let commFields = this.getCommFields()
    let uniqueFields = this.getUniqueFields()
    return {
      autoCreate: false,
      baseParams: [
        ...commParams,
        ...uniqueParams
      ],
      baseFields: [
        ...commFields,
        ...uniqueFields
      ],
      cmbSource: {
        gmark: erpInterfaceData.MAT_FLAG_MAP,
        gmarkConvert: erpInterfaceData.MAT_FLAG_MAP,
        status: erpInterfaceData.EXTRACT_STATUS_MAP,
        modifyMark: erpInterfaceData.DATA_STATUS_MAP,
        mergeType: importExportManage.mergeTypeMapFull,
        containerLcl: [{
          value: '0', label: '否'
        }, {
          value: '1', label: '是'
        }]
      },
      erpInterfaceData: erpInterfaceData,
      importExportManage: importExportManage,
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = []
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
  },
  computed: {
    districtCodeLabel() {
      if (this.iEMark === 'I') {
        return '表头境内目的地(国内地区)'
      } else if (this.iEMark === 'E') {
        return '表头境内货源地(国内地区)'
      } else {
        return '表头境内(货源/目的)地(国内地区)'
      }
    },
    districtPostCodeLabel() {
      if (this.iEMark === 'I') {
        return '表头境内目的地(行政区划)'
      } else if (this.iEMark === 'E') {
        return '表头境内货源地(行政区划)'
      } else {
        return '表头境内(货源/目的)地(行政区划)'
      }
    },
    listDistrictCodeLabel() {
      if (this.iEMark === 'I') {
        return '境内目的地(国内地区)'
      } else if (this.iEMark === 'E') {
        return '境内货源地(国内地区)'
      } else {
        return '境内(货源/目的)地(国内地区)'
      }
    },
    listDistrictPostCodeLabel() {
      if (this.iEMark === 'I') {
        return '境内目的地(国内地区)'
      } else if (this.iEMark === 'E') {
        return '境内货源地(国内地区)'
      } else {
        return '境内(货源/目的)地(国内地区)'
      }
    },
    tradeCountryCodeLabel() {
      if (this.iEMark === 'I') {
        return '启运国(地区)'
      } else if (this.iEMark === 'E') {
        return '运抵国(地区)'
      } else {
        return '启运/运抵国(地区)'
      }
    },
    destPortLabel() {
      if (this.iEMark === 'I') {
        return '经停港'
      } else if (this.iEMark === 'E') {
        return '指运港'
      } else {
        return '经停/指运港'
      }
    },
    iePortLabel() {
      if (this.iEMark === 'I') {
        return '进境关别'
      } else if (this.iEMark === 'E') {
        return '出境关别'
      } else {
        return '进/出境关别'
      }
    },
    entryPortLabel() {
      if (this.iEMark === 'I') {
        return '入境口岸'
      } else if (this.iEMark === 'E') {
        return '离境口岸'
      } else {
        return '入/离境口岸'
      }
    },
    receiveCodeLabel() {
      if (this.iEMark === 'I') {
        return '境内收货人代码'
      } else if (this.iEMark === 'E') {
        return '境内发货人代码'
      } else {
        return '境内收/发货人代码'
      }
    },
    receiveNameLabel() {
      if (this.iEMark === 'I') {
        return '境内收货人名称'
      } else if (this.iEMark === 'E') {
        return '境内发货人名称'
      } else {
        return '境内收/发货人名称'
      }
    },
    overseasShipperAeoLabel() {
      if (this.iEMark === 'I') {
        return '境外发货人代码'
      } else if (this.iEMark === 'E') {
        return '境外收货人代码'
      } else {
        return '境外发/收货人代码'
      }
    },
    overseasShipperEnameLabel() {
      if (this.iEMark === 'I') {
        return '境外发货人名称'
      } else if (this.iEMark === 'E') {
        return '境外收货人名称'
      } else {
        return '境外发/收货人名称'
      }
    },
    ieDateLabel() {
      if (this.iEMark === 'I') {
        return '进口日期'
      } else if (this.iEMark === 'E') {
        return '出口日期'
      } else {
        return '进/出口日期'
      }
    },
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {
        ieDate: this.ieDateLabel,
        iePort: this.iePortLabel,
        destPort: this.destPortLabel,
        entryPort: this.entryPortLabel,
        receiveCode: this.receiveCodeLabel,
        receiveName: this.receiveNameLabel,
        districtCode: this.districtCodeLabel,
        tradeCountry: this.tradeCountryCodeLabel,
        districtPostCode: this.districtPostCodeLabel,
        overseasShipperAeo: this.overseasShipperAeoLabel,
        listDistrictCode: this.listDistrictCodeLabel,
        listDistrictPostCode: this.listDistrictPostCodeLabel,
        overseasShipperEname: this.overseasShipperEnameLabel
      }
    }
  },
  methods: {
    getCommParams() {
      return [{
        key: 'linkedNo',
        title: '关联提单编号'
      }, {
        key: 'status',
        type: 'select',
        title: '提取状态'
      }, {
        type: 'select',
        title: '数据状态',
        key: 'modifyMark'
      }, {
        title: '发票号',
        key: 'invoiceNo'
      }, {
        key: 'contrNo',
        title: '合同协议号'
      }]
    },
    getUniqueParams() {
      return []
    },
    getCommFields() {
      return [{
        width: 120,
        key: 'linkedNo',
        title: '关联提单编号'
      }, {
        width: 90,
        key: 'status',
        type: 'select',
        title: '提取状态',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.status)
        }
      }, {
        width: 100,
        type: 'select',
        title: '数据状态',
        key: 'modifyMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
        }
      }, {
        width: 180,
        tooltip: true,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 130,
        key: 'gmark',
        type: 'select',
        title: '表头物料类型标志',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        width: 150,
        type: 'select',
        key: 'gmarkConvert',
        title: '表头转换后物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        width: 120,
        key: 'forwardCode',
        title: '货运代理代码'
      }, {
        width: 120,
        key: 'forwardName',
        title: '货运代理名称'
      }, {
        width: 150,
        tooltip: true,
        key: 'invoiceNo',
        title: '表头发票号'
      }, {
        width: 150,
        tooltip: true,
        key: 'contrNo',
        title: '合同协议号'
      }, {
        width: 180,
        key: 'wrapType',
        title: '包装种类',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.wrap)
        }
      }, {
        width: 150,
        title: '表头件数',
        key: 'packNum'
      }, {
        width: 150,
        key: 'netWt',
        title: '总净重'
      }, {
        width: 150,
        key: 'grossWt',
        title: '总毛重'
      }, {
        width: 150,
        title: '体积',
        key: 'volume'
      }, {
        width: 120,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 250,
        key: 'districtCode',
        title: '表头境内目的地',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.area)
        }
      }, {
        width: 200,
        tooltip: true,
        key: 'districtPostCode',
        title: '表头境内目的地(行政区划)',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'POST_AREA')
        }
      }, {
        width: 120,
        key: 'tradeNation',
        title: '贸易国(地区)',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        key: 'tradeCountry',
        title: '运抵国(地区)',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 220,
        title: '经停港',
        key: 'destPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.port_lin)
        }
      }, {
        width: 150,
        key: 'iePort',
        title: '进境关别',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 150,
        title: '入境口岸',
        key: 'entryPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
        }
      }, {
        width: 120,
        title: '表头最终目的国',
        key: 'destinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        title: '转换后表头最终目的国',
        key: 'destinationCountryConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        key: 'declareCode',
        title: '申报单位代码'
      }, {
        width: 160,
        key: 'declareName',
        title: '申报单位名称'
      }, {
        width: 130,
        title: '社会信用代码',
        key: 'tradeCreditCode'
      }, {
        width: 120,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 120,
        title: '监管方式',
        key: 'tradeMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      }, {
        width: 120,
        key: 'cutMode',
        title: '征免性质',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.levytype)
        }
      }, {
        width: 120,
        key: 'licenseNo',
        title: '许可证号'
      }, {
        width: 150,
        type: 'select',
        key: 'mergeType',
        title: '报关单归并类型'
      }, {
        width: 150,
        title: '航班日期',
        key: 'voyageDate'
      }, {
        width: 150,
        key: 'hawb',
        tooltip: true,
        title: '提运单号'
      }, {
        width: 150,
        tooltip: true,
        key: 'trafName',
        title: '运输工具名称'
      }, {
        width: 150,
        tooltip: true,
        title: '航次号',
        key: 'voyageNo'
      }, {
        width: 150,
        tooltip: true,
        key: 'receiveCode',
        title: '境内收货人编码'
      }, {
        width: 150,
        tooltip: true,
        key: 'receiveName',
        title: '境内收货人名称'
      }, {
        width: 150,
        tooltip: true,
        title: '境外发货人代码',
        key: 'overseasShipperAeo'
      }, {
        width: 150,
        tooltip: true,
        title: '境外发货人名称',
        key: 'overseasShipperEname'
      }, {
        width: 120,
        title: '申报地海关',
        key: 'masterCustoms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 150,
        key: 'ieDate',
        title: '进口日期'
      }, {
        width: 120,
        title: '成交方式',
        key: 'transMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transac)
        }
      }, {
        width: 150,
        title: '运费',
        key: 'feeRate'
      }, {
        width: 150,
        key: 'feeMark',
        title: '运费类型代码'
      }, {
        width: 150,
        key: 'feeCurr',
        title: '运费币制代码'
      }, {
        width: 150,
        title: '保费',
        key: 'insurRate'
      }, {
        width: 150,
        key: 'insurMark',
        title: '保费类型代码'
      }, {
        width: 150,
        key: 'insurCurr',
        title: '保费币制代码'
      }, {
        width: 150,
        title: '杂费',
        key: 'otherRate'
      }, {
        width: 150,
        key: 'otherMark',
        title: '杂费类型代码'
      }, {
        width: 150,
        key: 'otherCurr',
        title: '杂费币制代码'
      }, {
        width: 150,
        title: '报关标志',
        key: 'dclcusMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.dclcusMark)
        }
      }, {
        width: 150,
        title: '报关类型',
        key: 'dclcusType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.dclcusType)
        }
      }, {
        width: 150,
        key: 'entryMark',
        title: '核注清单报关单类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.entryType)
        }
      }, {
        width: 150,
        key: 'entryType',
        title: '报关单类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.entryType)
        }
      }, {
        width: 120,
        tooltip: true,
        key: 'relEmsNo',
        title: '关联手账册号'
      }, {
        width: 120,
        tooltip: true,
        title: '集装箱号',
        key: 'containerMd'
      }, {
        width: 150,
        title: '集装箱规格',
        key: 'containerType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.containerType)
        }
      }, {
        width: 120,
        title: '自重',
        tooltip: true,
        key: 'containerWt'
      }, {
        width: 120,
        type: 'select',
        title: '拼箱标识',
        key: 'containerLcl'
      }, {
        width: 120,
        tooltip: true,
        key: 'containerGNo',
        title: '商品项号关系'
      }, {
        width: 120,
        tooltip: true,
        key: 'entryNote',
        title: '报关单备注'
      }, {
        width: 180,
        tooltip: true,
        key: 'headNote',
        title: '表头备注'
      }, {
        width: 120,
        key: 'lineNo',
        title: '单据序号'
      }, {
        width: 100,
        key: 'bondMark',
        title: '保完税标记',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.bondedFlagMap4Erp)
        }
      }, {
        width: 160,
        key: 'bondMarkConvert',
        title: '转换后保完税标记',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.bondedFlagMap4Erp)
        }
      }, {
        width: 120,
        key: 'listgMark',
        title: '物料类型标志',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        width: 160,
        key: 'listgMarkConvert',
        title: '转换后物料类型标志',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        width: 200,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 150,
        key: 'qty',
        title: '数量'
      }, {
        width: 150,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 150,
        title: '总价',
        key: 'decTotal'
      }, {
        width: 120,
        key: 'unitErp',
        title: 'ERP交易单位'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 120,
        title: '转换后币制',
        key: 'currConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 150,
        title: '表体净重',
        key: 'listNetWt'
      }, {
        width: 150,
        title: '表体毛重',
        key: 'listGrossWt'
      }, {
        width: 120,
        title: '表体原产国',
        key: 'originCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        title: '转换后表体原产国',
        key: 'originCountryConvert'
      }, {
        width: 120,
        title: '表体发票号',
        key: 'listInvoiceNo'
      }, {
        width: 120,
        title: '表体发票日期',
        key: 'listInvoiceDate'
      }, {
        width: 120,
        key: 'qty1',
        title: '法定数量'
      }, {
        width: 120,
        key: 'qty2',
        title: '法定第二数量'
      }, {
        width: 120,
        key: 'dutyMode',
        title: '征免方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.levymode)
        }
      }, {
        width: 120,
        title: '表体最终目的国',
        key: 'listDestinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        title: '转换后表体最终目的国',
        key: 'listDestinationCountryConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 220,
        title: '境内目的地',
        key: 'listDistrictCode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.area)
        }
      }, {
        width: 220,
        tooltip: true,
        title: '境内目的地(行政区划)',
        key: 'listDistrictPostCode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'POST_AREA')
        }
      }, {
        width: 120,
        tooltip: true,
        key: 'entryGNo',
        title: '报关单归并序号'
      }, {
        width: 250,
        key: 'note',
        title: '备注',
        tooltip: true
      }, {
        width: 180,
        tooltip: true,
        key: 'remark1',
        title: 'remark1'
      }, {
        width: 180,
        tooltip: true,
        key: 'remark2',
        title: 'remark2'
      }, {
        width: 180,
        tooltip: true,
        key: 'remark3',
        title: 'remark3'
      }, {
        width: 150,
        tooltip: true,
        title: '原始物料',
        key: 'originalGNo'
      }, {
        width: 150,
        tooltip: true,
        title: '英文名称',
        key: 'copGNameEn'
      }, {
        width: 120,
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        width: 150,
        title: 'ERP创建时间',
        key: 'lastModifyDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        width: 150,
        title: '提取时间',
        key: 'extractTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        width: 150,
        title: '接收时间',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        width: 150,
        title: '更新时间',
        key: 'updateTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }, {
        width: 160,
        key: 'royalityNo',
        title: '特许权关联号'
      }, {
        width: 150,
        title: '表体件数',
        key: 'listPackNum'
      }]
    },
    getUniqueFields() {
      return []
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.columns = columns
      me.listSetupShow = false
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
