<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { erpInterfaceData } from '@/view/cs-common'
  import { wholeOrderList } from './js/wholeOrderList'

  export default {
    name: 'wholeOrderEList',
    mixins: [wholeOrderList],
    data() {
      return {
        iEMark: 'E',
        listConfig: {
          exportTitle: '出口提单整单'
        },
        erpInterfaceData: erpInterfaceData,
        taskInfo: {
          taskCode: 'DEC_E'     // 添加任务使用的taskCode
        },
        cmbSource: {
          outWay: erpInterfaceData.EXPORT_METHOD_MAP,
          outWayConvert: erpInterfaceData.EXPORT_METHOD_MAP,
          orderType: [{
            value: '0',
            label: 'PO退运'
          }, {
            value: '1',
            label: 'PO换货'
          }, {
            value: '2',
            label: '修理物品'
          }, {
            value: '3',
            label: '出口加工'
          }, {
            value: '4',
            label: 'Resale'
          }],
        },
        defaultFields: [],// 'linkedNo','status','modifyMark','emsListNo',
        ajaxUrl: {
          exportUrl: csAPI.sapErp.billOfLading.exports.exportUrl,
          selectAllPaged: csAPI.sapErp.billOfLading.exports.selectAllPaged
        }
      }
    },
    methods: {
      getUniqueParams() {
        return [{
          range: true,
          title: '订单日期',
          key: 'shipDate'
        }, {
          title: '运输方式',
          key: 'trafMode',
          type: 'pcode',
          props: {
            meta: 'TRANSF' // this.pcode.transf
          }
        }, {
          title: '监管方式',
          key: 'tradeMode',
          type: 'pcode',
          props: {
            meta: 'TRADE' // this.pcode.trade
          }
        }, {
          range: true,
          title: '接收时间',
          key: 'insertTime'
        }, {
          title: '企业料号',
          key: 'facGNo'
        }, {
          title: '销售订单号',
          key: 'soNo'
        }, {
          title: '客户名称',
          key: 'customerName'
        }, {
          title: '出口方式',
          key: 'outWayConvert',
          type: 'select'
        }, {
          range: true,
          title: '订单日期',
          key: 'soDate'
        }, {
          title: '单价',
          key: 'decPrice',
          type: 'checkBox',
          'true-value': 1,
          'false-value': null
        }, {
          title: '净重',
          key: 'netWt',
          type: 'checkBox',
          'true-value': 1,
          'false-value': null
        }, {
          title: '传输批次号',
          key: 'tempOwner'
        } ,{
          width: 120,
          key: 'orderType',
          title: '订单类型',
          type: 'select',
        }]
      },
      getUniqueFields() {
        return [{
          title: '出货日期',
          key: 'shipDate',
          align: 'center',
          width: 148
        }, {
          title: '账款客户',
          width: 150,
          align: 'center',
          key: 'accountCustomer'
        }, {
          title: 'BOM版本号',
          width: 120,
          align: 'center',
          key: 'exgVersion'
        }, {
          title: '发票日期',
          width: 120,
          align: 'center',
          key: 'invoiceDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }, {
          title: '销售订单号',
          width: 120,
          align: 'center',
          key: 'soNo'
        }, {
          title: '销售订单行号',
          width: 120,
          align: 'center',
          key: 'soLineNo'
        }, {
          title: '客户订单日期',
          width: 150,
          align: 'center',
          key: 'soDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }, {
          title: '客户料号',
          width: 120,
          align: 'center',
          key: 'customerGNo'
        }, {
          title: '客户订单行号',
          width: 120,
          align: 'center',
          key: 'customerLineNo'
        }, {
          title: '客户订单号',
          width: 120,
          align: 'center',
          key: 'customerPoNo'
        }, {
          title: '客户编码',
          width: 120,
          align: 'center',
          key: 'clientCode'
        }, {
          title: '客户名称',
          width: 120,
          align: 'center',
          key: 'clientName'
        }, {
          title: '出口方式',
          width: 120,
          align: 'center',
          key: 'outWay',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXPORT_METHOD_MAP)
          }
        }, {
          title: '加工费单价',
          width: 160,
          align: 'center',
          key: 'decPriceProcess'
        }, {
          title: '加工费总价',
          width: 160,
          align: 'center',
          key: 'decTotalProcess'
        }, {
          width: 120,
          key: 'inOutRelNo',
          title: '出库关联单号'
        }, {
          width: 120,
          key: 'orderType',
          title: '订单类型',
          type: 'select',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.orderType)
          }
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
