<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InventorySearch ref="headSearch" :showHeadSearch="showHeadSearch" :showFactory="showFactory" :showexgVersion="showexgVersion" :showCurr="showCurr"></InventorySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { sapCommList } from '../common/sapCommList'
  import { columns } from './../common/inventoryColumns'
  import InventorySearch from '../common/inventorySearch'
  import { dynamicExport } from '../../cs-common/dynamic-export/dynamicExport'

  export default {
    name: 'semi-finished-inventory',
    components: {
      InventorySearch
    },
    mixins: [columns, sapCommList, dynamicExport],
    data() {
      return {
        showCurr: true,
        showFactory: false,
        ajaxUrl: {
          exportUrl: csAPI.sapErp.inventory.semifinished.export,
          selectAllPaged: csAPI.sapErp.inventory.semifinished.search
        },
        gridConfig: {
          exportTitle: '半成品库存'
        },
        searchLines: 3,
        taskInfo: {
          taskCode: 'SEMI_EXG_STOCK'     // 添加任务使用的taskCode
        }
      }
    },
    created: function () {
      this.totalColumns = [...this.commonColumns, ...this.extrColumns, ...this.noteColumns]
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
