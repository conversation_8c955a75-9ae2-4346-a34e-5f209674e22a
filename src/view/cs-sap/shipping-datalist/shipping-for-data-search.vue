<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="150" inline>
      <XdoFormItem v-show="showFields.bondMark" prop="bondMarkConvert" label="转换后保完税标记">
        <xdo-select v-model="searchParam.bondMarkConvert" :options="this.importExportManage.bondedFlagMap"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="提取状态">
        <xdo-select v-model="searchParam.status" :options="this.erpInterfaceData.EXTRACT_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gmarkConvert" label="转换后物料类型">
        <xdo-select v-model="searchParam.gmarkConvert" :options="this.cmbSource.gMarkConvertData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="copGName" label="中文名称">
        <XdoIInput type="text" v-model="searchParam.copGName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGNameEn" label="英文品名">
        <XdoIInput type="text" v-model="searchParam.copGNameEn"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="unitErp" label="ERP计量单位">
        <XdoIInput type="text" v-model="searchParam.unitErp" :maxlength="10"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tempOwner" label="传输批次号">
        <XdoIInput type="text" v-model="searchParam.tempOwner"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
      <XdoFormItem prop="supplierName" label="供应商/客户名称">
        <div>
          <XdoCheckbox v-model="supplierName" style="padding-top: 4px;">空</XdoCheckbox>
          <XdoIInput v-model="searchParam.supplierName" :disabled="isSuppliername" style="width: 100px;"></XdoIInput>
        </div>
      </XdoFormItem>
      <XdoFormItem prop="decPriceFlag" label="单价">
        <XdoCheckbox v-model="decPriceFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
      </XdoFormItem>
      <XdoFormItem prop="netWtFlag" label="净重">
        <XdoCheckbox v-model="netWtFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { importExportManage, productClassify, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'shippingForDataSearch',
    props: {
      fixedValues: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        netWtFlag: false,
        decPriceFlag: false,
        isSuppliername: false,
        isUnit: false,
        isapplyUnit: false,
        isCurr: false,
        supplierName: '',
        unit: '',
        applyunit: '',
        currConvert: '',
        searchParam: {
          bondMarkConvert: '',
          status: '',
          modifyMark: '',
          facGNo: '',
          copGNo: '',
          gmarkConvert: '',
          copGName: '',
          copGNameEn: '',
          unitErp: '',
          tempOwner: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          supplierName: '',
          decPrice: null,
          netWt: null
        },
        cmbSource: {
          gMarkConvertData: [
            ...importExportManage.supplierName,
            ...productClassify.GMARK_SELECT
          ],
          statusData: erpInterfaceData.EXTRACT_STATUS_MAP
        },
        showFields: {
          gmark: true,
          bondMark: true
        },
        productClassify: productClassify,
        erpInterfaceData: erpInterfaceData,
        importExportManage: importExportManage
      }
    },
    watch: {
      fixedValues: {
        deep: true,
        immediate: true,
        handler: function (values) {
          this.$set(this.showFields, 'gmark', true)
          if (values.hasOwnProperty('gmark') && !isNullOrEmpty(values['gmark'])) {
            this.$set(this.showFields, 'gmark', false)
            this.$set(this.searchParam, 'gmark', values['gmark'])
          }
          this.$set(this.showFields, 'bondMark', true)
          if (values.hasOwnProperty('bondMark') && !isNullOrEmpty(values['bondMark'])) {
            this.$set(this.showFields, 'bondMark', false)
            this.$set(this.searchParam, 'bondMark', values['bondMark'])
          }
        }
      },
      netWtFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'netWt', 1)
          } else {
            this.$set(this.searchParam, 'netWt', null)
          }
        }
      },
      decPriceFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'decPrice', 1)
          } else {
            this.$set(this.searchParam, 'decPrice', null)
          }
        }
      },
      supplierName: {
        immediate: true,
        handler: function (val) {
          this.setIsNull('supplierName', 'isSuppliername', val)
        }
      },
      unit: {
        immediate: true,
        handler: function (val) {
          this.setIsNull('unitErpConvert', 'isUnit', val)
        }
      },
      applyunit: {
        immediate: true,
        handler: function (val) {
          this.setIsNull('unitConvert', 'isapplyUnit', val)
        }
      },
      currConvert: {
        immediate: true,
        handler: function (val) {
          this.setIsNull('currConvert', 'isCurr', val)
        }
      }
    },
    methods: {
      /**
       * ERP数据抛转日期范围
       * @param values
       */
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      setIsNull(searchField, boolField, flag) {
        if (flag) {
          this.$set(this.searchParam, searchField, 'N')
          this.$set(this, boolField, true)
        } else {
          this.$set(this.searchParam, searchField, '')
          this.$set(this, boolField, false)
        }
      }
    }
  }
</script>

<style scoped>
</style>
