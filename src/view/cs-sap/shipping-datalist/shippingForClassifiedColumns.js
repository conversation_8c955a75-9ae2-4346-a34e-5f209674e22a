import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 90,
          key: 'status',
          title: '提取状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
          }
        },
        {
          width: 120,
          title: '数据状态',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          width: 110,
          key: 'bondMark',
          title: '保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          width: 120,
          key: 'bondMarkConvert',
          title: '转换后保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 120,
          key: 'gmarkConvert',
          title: '转换后物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 150,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          tooltip: true,
          title: '备案料号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'contritem',
          title: '备案序号'
        },
        {
          width: 120,
          key: 'codeTS',
          tooltip: true,
          title: '商品编码'
        },
        {
          width: 160,
          tooltip: true,
          key: 'copGName',
          title: '中文名称'
        },
        {
          width: 120,
          tooltip: true,
          title: '英文品名',
          key: 'copGNameEn'
        },
        {
          width: 120,
          tooltip: true,
          key: 'copGModel',
          title: '料号申报要素'
        },
        {
          width: 120,
          tooltip: true,
          key: 'copGModelEn',
          title: '英文规格型号'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 150,
          key: 'unitConvert',
          title: ' 转换后申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'unitErp',
          title: 'ERP计量单位'
        },
        {
          width: 120,
          title: '单价',
          key: 'decPrice'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          title: '转换后币制',
          key: 'currConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'netWt',
          title: '净重'
        },
        {
          width: 120,
          key: 'supplierCode',
          title: '供应商/客户代码'
        },
        {
          width: 120,
          key: 'supplierName',
          title: '供应商/客户名称'
        },
        {
          width: 120,
          title: '工厂',
          key: 'factory'
        },
        {
          width: 100,
          tooltip: true,
          title: '法定比例因子',
          key: 'firstqtyFactor'
        },
        {
          width: 100,
          tooltip: true,
          title: '法二比例因子',
          key: 'secondqtyFactor'
        },
        {
          width: 120,
          key: 'note',
          title: '备注'
        },
        {
          width: 136,
          title: 'ERP创建时间',
          key: 'lastModifyDate'
        },
        {
          width: 120,
          key: 'tempOwner',
          title: '传输批次号'
        },
        {
          width: 136,
          title: '提取时间',
          key: 'extractTime'
        },
        {
          width: 136,
          title: '接收时间',
          key: 'insertTime'
        },
        {
          width: 136,
          title: '更新时间',
          key: 'updateTime'
        },
        {
          width: 136,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 136,
          key: 'gname',
          title: '商品名称'
        }
      ]
    }
  }
}
