import { exportPlanCommList } from '../../comm/js/exportPlanCommList'

export const exportPlanHeadBodyList = {
  name: 'exportPlanHeadBodyList',
  mixins: [exportPlanCommList],
  /**
   * 方法
   */
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'linkedNo',
        title: '出货ID'
      }, {
        key: 'status',
        type: 'select',
        title: '提取状态'
      }, {
        type: 'select',
        title: '数据状态',
        key: 'modifyMark'
      }, {
        key: 'invoiceNo',
        title: '发票号码'
      }, {
        title: '客户名称',
        key: 'clientName'
      }, {
        range: true,
        title: '出货日期',
        key: 'shipmentDate'
      }, {
        range: true,
        title: '接收时间',
        key: 'insertTime'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGName',
        title: '商品名称'
      }, {
        key: 'soNo',
        title: '销售订单号'
        // }, {
        //   range: true,
        //   title: '更新时间',
        //   key: 'updateTime'
        // }, {
        //   range: true,
        //   title: '提取时间',
        //   key: 'extractTime'
      }]
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        key: 'linkedNo',
        title: '出货ID'
      }, {
        width: 120,
        key: 'invoiceNo',
        title: '发票号码'
      }, {
        width: 120,
        title: '出货编号',
        key: 'shipmentNo'
      }, {
        width: 120,
        title: '客户代码',
        key: 'clientCode'
      }, {
        width: 120,
        title: '客户名称',
        key: 'clientName'
      }, {
        width: 120,
        title: '出货日期',
        key: 'shipmentDate',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '收货地址',
        key: 'shippingAddress',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'marks',
        title: '标记唛头'
      }, {
        width: 120,
        title: '交货单号',
        key: 'deliveryOrderNo'
      }, {
        width: 156,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 120,
        title: '付款方式',
        key: 'paymentMode'
      }, {
        width: 120,
        key: 'shipTo',
        title: 'SHIP TO'
      }, {
        width: 120,
        title: '收货方',
        key: 'receivingParty'
      }, {
        width: 120,
        title: '通知方',
        key: 'notifyParty'
      }, {
        width: 120,
        key: 'despPort',
        title: '启运港',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.port_lin)
        })
      }, {
        width: 120,
        key: 'destPort',
        title: '目的港',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.port_lin)
        })
      }, {
        width: 136,
        key: 'packingQty',
        title: '外包装数量'
      }, {
        width: 120,
        key: 'copGName',
        title: '主要品名',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        title: '总毛重',
        key: 'grossWtTotal'
      }, {
        width: 136,
        title: '总体积',
        key: 'volumeTotal'
      }, {
        width: 136,
        title: '备注1',
        key: 'remark1',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        title: '备注2',
        key: 'remark2',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '序号',
        key: 'lineNo'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'bondMark',
        title: '保完税标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 120,
        key: 'gMark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gMark)
        })
      }, {
        width: 120,
        title: '商品名称',
        key: 'copGNameBody'
      }, {
        width: 120,
        key: 'gModel',
        title: '规格型号'
      }, {
        width: 120,
        key: 'qty',
        title: '交易数量'
      }, {
        width: 120,
        key: 'unitErp',
        title: 'ERP计量单位'
      }, {
        width: 120,
        title: '材料费',
        key: 'decPriceMaterials'
      }, {
        width: 136,
        title: '加工费',
        key: 'decPriceProcess'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        title: '箱号',
        key: 'cartonNo'
      }, {
        width: 120,
        key: 'soNo',
        title: '销售订单号码'
      }, {
        width: 120,
        key: 'shopOrder',
        title: '车间订单'
      }, {
        width: 120,
        title: '客户PO',
        key: 'customerPoNo'
      }, {
        width: 136,
        key: 'netWt',
        title: '净重'
      }, {
        width: 136,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 136,
        title: '体积',
        key: 'volume'
      }, {
        width: 120,
        title: 'remark1',
        key: 'remark1Body',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: 'remark2',
        key: 'remark2Body',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        title: 'ERP创建时间',
        key: 'lastUpdateTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 120,
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        width: 120,
        key: 'status',
        title: '提取状态',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        title: '数据状态',
        key: 'modifyMark',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.modifyMark)
        })
      }, {
        width: 136,
        key: 'bondMarkConvert',
        title: '转换后保完税标记',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMarkConvert)
        })
      }, {
        width: 136,
        key: 'gmarkConvert',
        title: '转换后物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmarkConvert)
        })
      }, {
        width: 136,
        title: '接收时间',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 136,
        title: '更新时间',
        key: 'updateTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 136,
        title: '提取时间',
        key: 'extractTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }]
    }
  }
}
