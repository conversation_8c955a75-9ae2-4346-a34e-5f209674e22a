import { exportPlanCommList } from '../../comm/js/exportPlanCommList'

export const exportPlanBodyList = {
  name: 'exportPlanBodyList',
  mixins: [exportPlanCommList],
  /**
   * 方法
   */
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        title: '出货ID',
        key: 'linkedNo'
      }, {
        title: '序号',
        key: 'lineNo'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'status',
        type: 'select',
        title: '提取状态'
      }, {
        type: 'select',
        title: '数据状态',
        key: 'modifyMark'
      }, {
        key: 'copGName',
        title: '商品名称'
      }, {
        key: 'soNo',
        title: '销售订单号'
      }, {
        range: true,
        title: '接收时间',
        key: 'insertTime'
      }, {
        type: 'select',
        key: 'bondMarkConvert',
        title: '转换后保完税标记'
      }, {
        type: 'select',
        key: 'gmarkConvert',
        title: '转换后物料类型'
        // }, {
        //   range: true,
        //   title: '更新时间',
        //   key: 'updateTime'
        // }, {
        //   range: true,
        //   title: '提取时间',
        //   key: 'extractTime'
      }]
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '出货ID',
        key: 'linkedNo'
      }, {
        width: 120,
        title: '序号',
        key: 'lineNo'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'bondMark',
        title: '保完税标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 120,
        key: 'gMark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gMark)
        })
      }, {
        width: 120,
        key: 'copGName',
        title: '商品名称'
      }, {
        width: 120,
        key: 'gModel',
        title: '规格型号'
      }, {
        width: 120,
        key: 'qty',
        title: '交易数量'
      }, {
        width: 120,
        key: 'unitErp',
        title: 'ERP计量单位'
      }, {
        width: 120,
        title: '材料费',
        key: 'decPriceMaterials'
      }, {
        width: 136,
        title: '加工费',
        key: 'decPriceProcess'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        title: '箱号',
        key: 'cartonNo'
      }, {
        width: 120,
        key: 'soNo',
        title: '销售订单号码'
      }, {
        width: 120,
        key: 'shopOrder',
        title: '车间订单'
      }, {
        width: 120,
        title: '客户PO',
        key: 'customerPoNo'
      }, {
        width: 136,
        key: 'netWt',
        title: '净重'
      }, {
        width: 136,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 136,
        title: '体积',
        key: 'volume'
      }, {
        width: 136,
        key: 'remark1',
        title: '备注１',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'remark2',
        title: '备注２',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        title: 'ERP创建时间',
        key: 'lastUpdateTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 120,
        key: 'tempOwner',
        title: '传输批次号'
      }, {
        width: 120,
        key: 'status',
        title: '提取状态',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        title: '数据状态',
        key: 'modifyMark',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.modifyMark)
        })
      }, {
        width: 136,
        key: 'bondMarkConvert',
        title: '转换后保完税标记',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMarkConvert)
        })
      }, {
        width: 136,
        key: 'gmarkConvert',
        title: '转换后物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmarkConvert)
        })
      }, {
        width: 136,
        title: '接收时间',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 136,
        title: '更新时间',
        key: 'updateTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 136,
        title: '提取时间',
        key: 'extractTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }]
    }
  }
}
