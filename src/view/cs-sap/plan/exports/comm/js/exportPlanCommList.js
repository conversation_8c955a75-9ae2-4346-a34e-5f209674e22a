import { importExportManage } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const exportPlanCommList = {
  name: 'exportPlanCommList',
  mixins: [columnRender, baseSearchConfig, dynamicExport, listDataProcessing],
  data() {
    let me = this,
      params = me.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        status: [{
          value: '0', label: '未提取'
        }, {
          value: '1', label: '已提取'
        }],
        modifyMark: [{
          value: '1', label: '修改'
        }, {
          value: '2', label: '删除'
        }, {
          value: '3', label: '新增'
        }],
        gMark: importExportManage.gmarkMap,
        gmarkConvert: importExportManage.gmarkMap,
        bondMark: importExportManage.bondedFlagMap,
        bondMarkConvert: importExportManage.bondedFlagMap
      }
    }
  },
  /**
   * 创建
   */
  created: function () {
    // let me = this
    // // 收款单位
    // me.$http.post(csAPI.ieParams.selectComboxByCode + '/PRD,FOD,CUT').then(res => {
    //   me.$set(me.cmbSource, 'remitteeCode', ArrayToLocaleLowerCase(res.data.data))
    // }).catch(() => {
    //   me.$set(me.cmbSource, 'remitteeCode', [])
    // }).finally(() => {
    //   me.searchFieldsReLoad('remitteeCode')
    // })
  },
  /**
   * 方法
   */
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
