<template>
  <XdoModal v-model="show" mask width="1024" title="进口提单表头数据提取"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <BillOfLadingHeaderIList :extract="extract" :show="show" @onConfirm="reloadParent" :grd-height="500"></BillOfLadingHeaderIList>
  </XdoModal>
</template>

<script>
  import BillOfLadingHeaderIList from './bill-of-lading-header-I-list'

  export default {
    name: 'billOfLadingHeaderIPop',
    components: {
      BillOfLadingHeaderIList
    },
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      return {
        extract: true
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      reloadParent(rowData) {
        let me = this
        me.handleClose()
        me.$emit('onReload', rowData)
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }
</style>
