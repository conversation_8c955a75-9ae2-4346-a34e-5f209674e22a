<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <BillOfLadingHeaderSearch ref="headSearch"></BillOfLadingHeaderSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="theGridHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { sapCommList } from '../common/sapCommList'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { columns } from './billOfLadingHeaderIColumns'
  import { erpInterfaceData, importExportManage } from '@/view/cs-common'
  import BillOfLadingHeaderSearch from './bill-of-lading-header-I-search'
  import { dynamicExport } from '../../cs-common/dynamic-export/dynamicExport'

  export default {
    name: 'billOfLadingHeaderIList',
    components: {
      BillOfLadingHeaderSearch
    },
    mixins: [sapCommList, columns, dynamicExport],
    props: {
      show: {
        type: Boolean,
        default: () => ({})
      },
      extract: {
        type: Boolean,
        default: false
      },
      grdHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        gridConfig: {
          exportTitle: '进口提单表头'
        },
        // 查询条件行数
        searchLines: 3,
        ajaxUrl: {
          extract: csAPI.sapErp.billOfLadingHeaderI.extract,
          exportUrl: csAPI.sapErp.billOfLadingHeaderI.exportUrl,
          selectAllPaged: csAPI.sapErp.billOfLadingHeaderI.selectAllPaged
        },
        toolbarEventMap: {
          'extract': this.handleExtract
        },
        cmbSource: {
          supplierCodeList: [],
          containerLclList: [
            {value: '0', label: '否'},
            {value: '1', label: '是'}
          ]
        },
        erpInterfaceData: erpInterfaceData,
        importExportManage: importExportManage,
        taskInfo: {
          taskCode: 'I_DEC_HEAD'     // 添加任务使用的taskCode
        }
      }
    },
    created: function () {
      let me = this
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.cmbSource.supplierCodeList = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.supplierCodeList = []
      })
    },
    watch: {
      extract: {
        immediate: true,
        handler: function (extract) {
          let me = this
          me.$set(me, 'checkboxSelection', extract)
        }
      }
    },
    methods: {
      loadOtherActions() {
        let me = this
        if (me.checkboxSelection) {
          me.actions.splice(me.actions.findIndex(it => it.command === 'setting'), 1)
          let settings = me.actions.filter(item => {
            return item.command === 'extract'
          })
          if (Array.isArray(settings) && settings.length === 0) {
            me.actions.push({
              ...me.actionsComm,
              label: "数据提取",
              command: 'extract',
              key: 'xdo-btn-take',
              icon: 'ios-checkmark'
            })
          }
        }
      },
      handleExtract() {
        let me = this
        if (me.checkRowSelected('提取', true)) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '提取',
            cancelText: '取消',
            content: '确认提取所选项数据吗',
            onOk: () => {
              me.$emit('onConfirm', me.gridConfig.selectRows[0])
            }
          })
        }
      }
    },
    computed: {
      theGridHeight() {
        if (this.grdHeight > 0) {
          return this.grdHeight
        }
        return this.dynamicHeight
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
