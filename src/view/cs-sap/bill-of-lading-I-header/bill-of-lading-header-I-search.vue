<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="linkedNo" label="提取单号">
        <XdoIInput type="text" v-model="searchParam.linkedNo" :maxlength="32"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="提取状态">
        <xdo-select v-model="searchParam.status" :options="this.erpInterfaceData.EXTRACT_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo" :maxlength="50"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo" :maxlength="50"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="hawb"  label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb" :maxlength="30"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'billOfLadingHeaderISearch',
    data () {
      return {
        searchParam: {
          linkedNo: '',
          status: '',
          modifyMark: '',
          invoiceNo: '',
          contrNo: '',
          hawb: '',
          trafMode: '',
          tradeMode: '',
          insertTimeFrom: '',
          insertTimeTo: ''
        },
        erpInterfaceData: erpInterfaceData
      }
    },
    methods: {
      handleInsertTimeChange (values) {
        let me = this
        if (values instanceof Array && values.length === 2) {
          me.$set(me.searchParam, "insertTimeFrom", values[0])
          me.$set(me.searchParam, "insertTimeTo", values[1])
        } else {
          me.$set(me.searchParam, "insertTimeFrom", '')
          me.$set(me.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
