import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          title: '单据号',
          key: 'linkedNo'
        },
        {
          width: 120,
          key: 'lineNo',
          title: '单据序号'
        },
        {
          width: 90,
          key: 'status',
          title: '提取状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
          }
        },
        {
          width: 120,
          title: '数据状态',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          width: 100,
          key: 'bondMark',
          title: '保完税标记',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          width: 160,
          key: 'bondMarkConvert',
          title: '转换后保完税标记',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.bondedFlagMap)
          }
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 160,
          key: 'gmarkConvert',
          title: '转换后物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 200,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          key: 'qty',
          width: 120,
          title: '数量'
        },
        {
          width: 120,
          title: '单价',
          key: 'decPrice'
        },
        {
          width: 120,
          title: '总价',
          key: 'decTotal'
        },
        {
          width: 120,
          key: 'unitErp',
          title: 'ERP交易单位'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          title: '转换后币制',
          key: 'currConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'netWt',
          title: '净重'
        },
        {
          width: 120,
          title: '毛重',
          key: 'grossWt'
        },
        {
          width: 120,
          title: '最终目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 160,
          title: '转换后最终目的国',
          key: 'destinationCountryConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          key: 'exgVersion',
          title: 'BOM版本号'
        },
        {
          width: 160,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 120,
          title: '发票日期',
          key: 'invoiceDate'
        },
        {
          width: 120,
          key: 'soNo',
          title: '销售订单号'
        },
        {
          width: 120,
          key: 'soLineNo',
          title: '销售订单行号'
        },
        {
          width: 150,
          key: 'soDate',
          title: '客户订单日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 120,
          title: '客户料号',
          key: 'customerGNo'
        },
        {
          width: 120,
          title: '客户订单行号',
          key: 'customerLineNo'
        },
        {
          width: 120,
          title: '客户订单号',
          key: 'customerPoNo'
        },
        {
          width: 120,
          title: '客户编码',
          key: 'clientCode'
        },
        {
          width: 120,
          title: '客户名称',
          key: 'clientName'
        },
        {
          width: 120,
          key: 'outWay',
          title: '出口方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXPORT_METHOD_MAP)
          }
        },
        {
          width: 120,
          key: 'qty1',
          title: '法定数量'
        },
        {
          width: 120,
          key: 'qty2',
          title: '法二数量'
        },
        {
          width: 160,
          title: '加工费单价',
          key: 'decPriceProcess'
        },
        {
          width: 160,
          title: '加工费总价',
          key: 'decTotalProcess'
        },
        {
          width: 120,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 160,
          title: '境内货源地',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'districtPostCode',
          title: '境内货源地(行政区划)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 120,
          title: '原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          title: '转换后原产国',
          key: 'originCountryConvert'
        },
        {
          width: 130,
          tooltip: true,
          key: 'entryGNo',
          title: '报关单归并序号'
        },
        {
          width: 120,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        {
          width: 120,
          tooltip: true,
          key: 'remark1',
          title: 'remark1'
        },
        {
          width: 120,
          tooltip: true,
          key: 'remark2',
          title: 'remark2'
        },
        {
          width: 120,
          tooltip: true,
          key: 'remark3',
          title: 'remark3'
        },
        {
          width: 150,
          title: '原始物料',
          key: 'originalGNo'
        },
        {
          width: 150,
          title: '英文名称',
          key: 'copGNameEn'
        },
        {
          width: 150,
          title: '账款客户',
          key: 'accountCustomer'
        },
        {
          width: 120,
          key: 'tempOwner',
          title: '传输批次号'
        },
        {
          width: 150,
          title: 'ERP创建时间',
          key: 'lastModifyDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          title: '提取时间',
          key: 'extractTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          title: '接收时间',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 160,
          key: 'royalityNo',
          title: '特许权关联号'
        },
        {
          width: 120,
          key: 'inOutRelNo',
          title: '出库关联单号'
        },
        {
          width: 120,
          key: 'gmodel',
          align: 'center',
          title: '申报规格型号'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报单位'
        },
        {
          width: 120,
          key: 'packNum',
          title: '件数'
        }
      ]
    }
  }
}
