<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-if="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <materialSearch ref="headSearch"></materialSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange">
            <span><span style="font-weight: bolder; margin-right: 10px;">{{this.allPrice}}</span>  共{{pageParam.dataTotal}}条</span>
          </XdoPage>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
    <ImportPage :importKey="importKey" :importShow.sync="importShow" :importConfig="importConfig"
                @onImportSuccess="afterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { sapCommList } from '../common/sapCommList'
  import { columns } from './shippingMaterialColumns'
  import materialSearch from './shipping-for-material-search'
  import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { interimVerification, productClassify, erpInterfaceData, deepProcessing } from '@/view/cs-common'

  export default {
    name: 'ShippingImgIeList',
    props: {
      typeNoInfor: {
        type: String,
        default: () => ({})
      }     // 传值List页面中的 typeNo
    },
    components: {
      materialSearch
    },
    mixins: [sapCommList, columns, dynamicExport, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('IMPORT-IMG-IE-LIST')
      return {
        allPrice: '',
        ajaxUrl: {
          exportUrl: csAPI.sapErp.materialsIE.exportUrl,
          deleteUrl: csAPI.sapErp.materialsIE.deleteUrl,
          selectAllPaged: csAPI.sapErp.materialsIE.selectAllPaged
        },
        // 查询条件行数
        searchLines: 5,
        gridConfig: {
          exportTitle: '料件出入库'
        },
        checkboxSelection: true,
        deepProcessing: deepProcessing,
        productClassify: productClassify,
        erpInterfaceData: erpInterfaceData,
        interimVerification: interimVerification,
        taskInfo: {
          taskCode: 'IMG_DETAIL'     // 添加任务使用的taskCode
        },
        importConfig: importConfig,
        importKey: 'IMPORT-IMG-IE-LIST'
      }
    },
    methods: {
      actionLoad() {
        let me = this
        if (typeof me.loadOtherActions === 'function') {
          me.loadOtherActions()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
