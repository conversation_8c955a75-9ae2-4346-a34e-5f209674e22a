<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm :label-width="120" :model="searchParam" class="dc-form dc-form-3" inline label-position="right" ref="formInline">
      <XdoFormItem label="出入库单据号" prop="billNo">
        <XdoIInput type="text" v-model="searchParam.billNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="提取状态">
        <xdo-select v-model="searchParam.status" :options="this.erpInterfaceData.DATA_GET_STATUS"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="移动类型">
        <xdo-select v-model="searchParam.ieType" :options="this.deepProcessing.IE_TYPE_IN_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="采购订单号" prop="poNo">
        <XdoIInput type="text" v-model="searchParam.poNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="出入库关联单号" prop="linkedNo">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="发票号" prop="invoiceNo">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="企业料号" prop="facGNo">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="供应商名称" prop="supplierName">
        <XdoIInput type="text" v-model="searchParam.supplierName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="进口方式" prop="inWayConvert">
        <xdo-select v-model="searchParam.inWayConvert" :options="this.erpInterfaceData.IMPORT_METHOD_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="交易日期" @onDateRangeChanged="handleDeliveryDateChanges"></dc-dateRange>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChanges"></dc-dateRange>
      <dc-dateRange label="更新时间" @onDateRangeChanged="handleUpdateTimeChanges"></dc-dateRange>
      <XdoFormItem prop="tempOwner" label="传输批次号">
        <XdoIInput type="text" v-model="searchParam.tempOwner"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessing, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'materialStockSearch',
    data() {
      return {
        searchParam: {
          billNo: '',
          status: '',
          modifyMark: '',
          ieType: '',
          poNo: '',
          linkedNo: '',
          invoiceNo: '',
          facGNo: '',
          supplierName: '',
          inWayConvert: '',
          deliveryDateFrom: '',
          deliveryDateTo: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          updateTimeFrom: '',
          updateTimeTo: '',
          tempOwner: ''
        },
        deepProcessing: deepProcessing,
        erpInterfaceData: erpInterfaceData
      }
    },
    methods: {
      handleDeliveryDateChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "deliveryDateFrom", values[0])
          this.$set(this.searchParam, "deliveryDateTo", values[1])
        } else {
          this.$set(this.searchParam, "deliveryDateFrom", '')
          this.$set(this.searchParam, "deliveryDateTo", '')
        }
      },
      handleInsertTimeChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      handleUpdateTimeChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "updateTimeFrom", values[0])
          this.$set(this.searchParam, "updateTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "updateTimeFrom", '')
          this.$set(this.searchParam, "updateTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
