import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '出入库单据号',
          width: 150,
          align: 'center',
          key: 'billNo',
          tooltip: true,
          ellipsis: true
        },
        {
          title: '单据序号',
          width: 120,
          align: 'center',
          key: 'lineNo'
        },
        {
          title: '提取状态',
          width: 90,
          align: 'center',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_GET_STATUS)
          }
        },
        {
          title: '数据状态',
          width: 120,
          align: 'center',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          title: '保完税标记',
          width: 100,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_SOURCE)
          }
        },
        {
          title: '转换后保完税标记',
          width: 160,
          align: 'center',
          key: 'bondMarkConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_SOURCE)
          }
        },
        {
          title: '库位别',
          width: 120,
          align: 'center',
          key: 'warehouseNo'
        },
        {
          title: '移动类型',
          width: 120,
          align: 'center',
          key: 'ieType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.deepProcessing.IE_TYPE_IN_MAP)
          }
        },
        {
          title: '出入库关联单号',
          width: 150,
          align: 'center',
          key: 'linkedNo'
        },
        {
          title: '采购订单号',
          width: 120,
          align: 'center',
          key: 'poNo'
        },
        {
          title: '发票号',
          width: 120,
          align: 'center',
          key: 'invoiceNo'
        },
        {
          title: '交易日期',
          width: 120,
          align: 'center',
          key: 'deliveryDate'
        },
        {
          title: '企业料号',
          width: 200,
          align: 'center',
          key: 'facGNo'
        },
        {
          title: '数量',
          width: 120,
          align: 'center',
          key: 'qty'
        },
        {
          title: 'ERP交易单位',
          width: 120,
          align: 'center',
          key: 'unit'
        },
        {
          title: '单价',
          width: 120,
          align: 'center',
          key: 'decPrice'
        },
        {
          title: '总价',
          width: 120,
          align: 'center',
          key: 'decTotal'
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'curr'
        },
        {
          title: '转换后币制',
          width: 120,
          align: 'center',
          key: 'currConvert'
        },
        {
          title: '供应商代码',
          width: 130,
          align: 'center',
          key: 'supplierCode',
          tooltip: true,
          ellipsis: true
        },
        {
          title: '供应商名称',
          width: 180,
          align: 'center',
          key: 'supplierName',
          tooltip: true,
          ellipsis: true
        },
        {
          title: '进口方式',
          width: 120,
          align: 'center',
          key: 'inWay',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.IMPORT_METHOD_MAP)
          }
        },
        {
          title: '成本中心',
          width: 120,
          align: 'center',
          key: 'costCenter'
        },
        {
          title: '备注',
          width: 120,
          align: 'center',
          key: 'note',
          tooltip: true,
          ellipsis: true
        },
        {
          title: '传输批次号',
          width: 120,
          align: 'center',
          key: 'tempOwner'
        },
        {
          title: 'ERP创建时间',
          width: 150,
          align: 'center',
          key: 'lastModifyDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '提取时间',
          width: 150,
          align: 'center',
          key: 'extractTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '接收时间',
          width: 150,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '更新时间',
          width: 150,
          align: 'center',
          key: 'updateTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }
      ]
    }
  },
  /**
   * 方法
   */
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
