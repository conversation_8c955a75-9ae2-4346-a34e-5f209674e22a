<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-4" :model="searchParam" label-position="right" :label-width="110" inline>
      <XdoFormItem label="出入库单据号" prop="billNo">
        <XdoIInput type="text" v-model="searchParam.billNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="提取状态">
        <xdo-select v-model="searchParam.status" :options="this.erpInterfaceData.DATA_GET_STATUS"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="移动类型">
        <xdo-select v-model="searchParam.ieType" :options="this.deepProcessing.IE_TYPE_OUT_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="销售订单号" prop="poNo">
        <XdoIInput type="text" v-model="searchParam.poNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="出入库关联单号" prop="linkedNo">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="发票号" prop="invoiceNo">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="企业料号" prop="facGNo">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="客户名称" prop="customerName">
        <XdoIInput type="text" v-model="searchParam.customerName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="出口方式" prop="outWayConvert">
        <xdo-select v-model="searchParam.outWayConvert" :options="this.erpInterfaceData.EXPORT_METHOD_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="交易日期" @onDateRangeChanged="handleDeliveryDateChanges"></dc-dateRange>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChanges"></dc-dateRange>
      <dc-dateRange label="更新时间" @onDateRangeChanged="handleUpdateTimeChanges"></dc-dateRange>
      <XdoFormItem prop="tempOwner" label="传输批次号">
        <XdoIInput type="text" v-model="searchParam.tempOwner"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessing, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'productSearch',
    data() {
      return {
        searchParam: {
          billNo: '',//单证编号
          status: '',
          modifyMark: '',
          ieType: '',//移动类型
          soNo: '',
          linkedNo: '',//关联编号
          invoiceNo: '',//发票号
          facGNo: '',//料号
          customerName: '',
          outWayConvert: '',
          deliveryDateFrom: '',
          deliveryDateTo: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          updateTimeFrom: '',
          updateTimeTo: '',
          tempOwner: ''
        },
        deepProcessing: deepProcessing,
        erpInterfaceData: erpInterfaceData
      }
    },
    methods: {
      handleDeliveryDateChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "deliveryDateFrom", values[0])
          this.$set(this.searchParam, "deliveryDateTo", values[1])
        } else {
          this.$set(this.searchParam, "deliveryDateFrom", '')
          this.$set(this.searchParam, "deliveryDateTo", '')
        }
      },
      handleInsertTimeChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      handleUpdateTimeChanges(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "updateTimeFrom", values[0])
          this.$set(this.searchParam, "updateTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "updateTimeFrom", '')
          this.$set(this.searchParam, "updateTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
