<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-if="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <productSearch ref="headSearch"></productSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange">
            <span><span style="font-weight: bolder; margin-right: 10px;">{{this.allPrice}}</span>  共{{pageParam.dataTotal}}条</span>
          </XdoPage>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
    <ImportPage :importKey="importKey" :importShow.sync="importShow" :importConfig="importConfig"
                @onImportSuccess="afterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { columns } from './shippingProductColumns'
  import { sapCommList } from '../common/sapCommList'
  import productSearch from './shipping-for-product-search'
  import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { interimVerification, erpInterfaceData, deepProcessing } from '@/view/cs-common'

  export default {
    name: 'ShippingExgIeList',
    props: {
      /**
       * 传值List页面中的 typeNo
       */
      typeNoInfor: {
        type: String,
        default: () => ({})
      }
    },
    components: {
      productSearch
    },
    mixins: [sapCommList, columns, dynamicExport, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('IMPORT-EXG-IE-LIST')
      return {
        allPrice: '',
        ajaxUrl: {
          exportUrl: csAPI.sapErp.productIE.exportUrl,
          deleteUrl: csAPI.sapErp.productIE.deleteUrl,
          selectAllPaged: csAPI.sapErp.productIE.selectAllPaged
        },
        // 查询条件行数
        searchLines: 4,
        gridConfig: {
          exportTitle: '成品出入库'
        },
        checkboxSelection: true,
        deepProcessing: deepProcessing,
        erpInterfaceData: erpInterfaceData,
        interimVerification: interimVerification,
        taskInfo: {
          taskCode: 'EXG_DETAIL'     // 添加任务使用的taskCode
        },
        importConfig: importConfig,
        importKey: 'IMPORT-EXG-IE-LIST'
      }
    },
    methods: {
      actionLoad() {
        let me = this
        if (typeof me.loadOtherActions === 'function') {
          me.loadOtherActions()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
