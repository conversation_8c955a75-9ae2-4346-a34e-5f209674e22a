import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 150,
          key: 'linkedNo',
          title: '提取单号'
        },
        {
          width: 90,
          key: 'status',
          title: '提取状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
          }
        },
        {
          width: 100,
          title: '数据状态',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          width: 180,
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 130,
          key: 'gmark',
          title: '物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 130,
          key: 'gmarkConvert',
          title: '转换后物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 150,
          key: 'shipDate',
          title: '出货日期'
        },
        {
          width: 120,
          key: 'forwardCode',
          title: '货运代理代码'
        },
        {
          width: 120,
          key: 'forwardName',
          title: '货运代理名称'
        },
        {
          width: 150,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 150,
          tooltip: true,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 180,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 150,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 150,
          key: 'netWt',
          title: '总净重'
        },
        {
          width: 150,
          key: 'grossWt',
          title: '总毛重'
        },
        {
          width: 150,
          title: '体积',
          key: 'volume'
        },
        {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 180,
          title: '境内货源地',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 200,
          tooltip: true,
          key: 'districtPostCode',
          title: '境内货源地(行政区划)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 120,
          key: 'tradeNation',
          title: '贸易国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 180,
          key: 'tradeCountry',
          title: '运抵国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 180,
          title: '指运港',
          key: 'destPort',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.port_lin)
          }
        },
        {
          width: 180,
          key: 'ieport',
          title: '出境关别',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 180,
          title: '离境口岸',
          key: 'entryPort',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
          }
        },
        {
          width: 180,
          title: '最终目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 180,
          tooltip: true,
          title: '转换后最终目的国',
          key: 'destinationCountryConvert'
        },
        {
          width: 120,
          key: 'declareCode',
          title: '申报单位代码'
        },
        {
          width: 120,
          key: 'declareName',
          title: '申报单位名称'
        },
        {
          width: 120,
          title: '社会信用代码',
          key: 'tradeCreditCode'
        },
        {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 120,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 120,
          key: 'cutMode',
          title: '征免性质',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levytype)
          }
        },
        {
          width: 120,
          title: '许可证号',
          key: 'licenseNo'
        },
        {
          width: 150,
          tooltip: true,
          type: 'select',
          key: 'mergeType',
          title: '报关单归并类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.mergeTypeMapFull)
          }
        },
        {
          width: 150,
          title: '航班日期',
          key: 'voyageDate'
        },
        {
          width: 150,
          key: 'hawb',
          tooltip: true,
          title: '提运单号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'trafName',
          title: '运输工具名称'
        },
        {
          width: 150,
          tooltip: true,
          title: '航次号',
          key: 'voyageNo'
        },
        {
          width: 150,
          tooltip: true,
          key: 'receiveCode',
          title: '境内发货人代码'
        },
        {
          width: 150,
          tooltip: true,
          key: 'receiveName',
          title: '境内发货人名称'
        },
        {
          width: 150,
          tooltip: true,
          title: '境外收货人代码',
          key: 'overseasShipperAeo'
        },
        {
          width: 150,
          tooltip: true,
          title: '境外收货人名称',
          key: 'overseasShipperName'
        },
        {
          width: 120,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 150,
          key: 'iedate',
          title: '出口日期'
        },
        {
          width: 120,
          key: 'transMode',
          title: '成交方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        },
        {
          width: 150,
          title: '运费',
          key: 'feeRate'
        },
        {
          width: 150,
          key: 'feeMark',
          title: '运费类型代码'
        },
        {
          width: 150,
          key: 'feeCurr',
          title: '运费币制代码'
        },
        {
          width: 150,
          title: '保费',
          key: 'insurRate'
        },
        {
          width: 150,
          key: 'insurMark',
          title: '保费类型代码'
        },
        {
          width: 150,
          key: 'insurCurr',
          title: '保费币制代码'
        },
        {
          width: 150,
          title: '杂费',
          key: 'otherRate'
        },
        {
          width: 150,
          key: 'otherMark',
          title: '杂费类型代码'
        },
        {
          width: 150,
          key: 'otherCurr',
          title: '杂费币制代码'
        },
        {
          width: 150,
          title: '报关标志',
          key: 'dclcusMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.dclcusMark)
          }
        },
        {
          width: 150,
          title: '报关类型',
          key: 'dclcusType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.dclcusType)
          }
        },
        {
          width: 150,
          key: 'entryMark',
          title: '核注清单报关单类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.entryType)
          }
        },
        {
          width: 150,
          key: 'entryType',
          title: '报关单类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.entryType)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'relEmsNo',
          title: '关联手账册号'
        },
        {
          width: 120,
          tooltip: true,
          title: '集装箱号',
          key: 'containerMd'
        },
        {
          width: 150,
          title: '集装箱规格',
          key: 'containerType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.containerType)
          }
        },
        {
          width: 120,
          title: '自重',
          tooltip: true,
          key: 'containerWt'
        },
        {
          width: 120,
          title: '拼箱标识',
          key: 'containerLcl',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.containerLclList)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'containerGNo',
          title: '商品项号关系'
        },
        {
          width: 120,
          tooltip: true,
          key: 'entryNote',
          title: '报关单备注'
        },
        {
          width: 180,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        {
          width: 150,
          title: 'ERP创建时间',
          key: 'lastModifyDate'
        },
        {
          width: 150,
          key: 'tempOwner',
          title: '传输批次号'
        },
        {
          width: 150,
          title: '提取时间',
          key: 'extractTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          title: '接收时间',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 90,
          tooltip: true,
          key: 'cweight',
          title: '计费重量'
        },
        {
          width: 120,
          tooltip: true,
          key: 'inviteDate',
          title: '申请出口日期'
        },
        {
          width: 90,
          tooltip: true,
          title: '贸易条款',
          key: 'tradeTerms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.tradeTermList)
          }
        }
      ]
    }
  }
}
