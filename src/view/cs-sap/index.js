import { namespace } from '@/project'
import BomInformation from './Bom/BomList'
import bomInfoTabs from './Bom/bom-info-tabs'
import MaterialStock from './material-stock/material-stock'
// import OriginalBomList from './Bom/original/OriginalBomList'
import WholeOrderIList from './bill-of-lading/whole-order-i-list'
import WholeOrderEList from './bill-of-lading/whole-order-e-list'
import ShippingDataList from './shipping-datalist/shipping-data-list'
import FinshedInventory from './finished-inventory/finished-inventory'
import InprocessInventory from './Inprocess-inventory/inprocess-inventory'
import ShippingDetailsList from './shipping-details/shipping-details-list'
import ShippingPrdSuppList from './ShippingPrdSuppList/ShippingPrdSuppList'
import ShippingExgIeList from './shipping-erpProductIList/shipping-product-list'
import ShippingImgIeList from './shipping-erpMaterialIList/shipping-material-list'
import ShippingEroDecIList from './shipping-erpDecIList/shipping-erpDecIList-list'
import SemiFinishedInventory from './semi-finished-inventory/semi-finished-inventory'
import exportPlanHeadList from './plan/exports/export-plan-head/export-plan-head-list'
import exportPlanBodyList from './plan/exports/export-plan-body/export-plan-body-list'
import BillOfLadingHeaderEList from './bill-of-lading-header/bill-of-lading-header-e-list'
import DomesticSalesDetailsList from './domestic-sales-details/domestic-sales-details-list'
import BillOfLadingHeaderIList from './bill-of-lading-I-header/bill-of-lading-header-I-list'
import exportPlanHeadBodyList from './plan/exports/export-plan-head-body/export-plan-head-body-list'

export default [
  {
    path: '/' + namespace + '/sap/ShippingDataList',
    name: 'ShippingDetaList',
    meta: {
      icon: 'ios-document',
      title: '物料信息'
    },
    component: ShippingDataList
  },
  {
    path: '/' + namespace + '/sap/shipping-erpDecIList-list',
    name: 'ShippingEroDecIList',
    meta: {
      icon: 'ios-document',
      title: '进口明细'
    },
    component: ShippingEroDecIList
  },
  {
    path: '/' + namespace + '/sap/ShippingDetailsList',
    name: 'ShippingDetailsList',
    meta: {
      icon: 'ios-document',
      title: '出口明细'
    },
    component: ShippingDetailsList
  },
  {
    path: '/' + namespace + '/sap/ShippingImgIeList',
    name: 'ShippingImgIeList',
    meta: {
      icon: 'ios-document',
      title: '料件出入库'
    },
    component: ShippingImgIeList
  },
  {
    path: '/' + namespace + '/sap/ShippingExgIeList',
    name: 'ShippingExgIeList',
    meta: {
      icon: 'ios-document',
      title: '成品出入库'
    },
    component: ShippingExgIeList
  },
  {
    path: '/' + namespace + '/sap/bill-of-lading-header-e-list',
    name: 'billOfLadingHeaderEList',
    meta: {
      icon: 'ios-document',
      title: '出口提单表头'
    },
    component: BillOfLadingHeaderEList
  },
  {
    path: '/' + namespace + '/sap/bill-of-lading-header-I-list',
    name: 'billOfLadingHeaderIList',
    meta: {
      icon: 'ios-document',
      title: '进口提单表头'
    },
    component: BillOfLadingHeaderIList
  },
  {
    path: '/' + namespace + '/sap/ShippingPrdSuppList',
    name: 'ShippingPrdSuppList',
    meta: {
      icon: 'ios-document',
      title: '客户供应商信息'
    },
    component: ShippingPrdSuppList
  },
  {
    path: '/' + namespace + '/sap/material-stock',
    name: 'material-stock',
    meta: {
      icon: 'ios-document',
      title: '料件库存'
    },
    component: MaterialStock
  },
  {
    path: '/' + namespace + '/sap/finished-inventory',
    name: 'finished-inventory',
    meta: {
      icon: 'ios-document',
      title: '成品库存'
    },
    component: FinshedInventory
  },
  {
    path: '/' + namespace + '/sap/semi-finished-inventory',
    name: 'semi-finished-inventory',
    meta: {
      icon: 'ios-document',
      title: '半成品库存'
    },
    component: SemiFinishedInventory
  },
  {
    path: '/' + namespace + '/sap/inprocess-inventory',
    name: 'inprocess-inventory',
    meta: {
      icon: 'ios-document',
      title: '在制品库存'
    },
    component: InprocessInventory
  },
  {
    path: '/' + namespace + '/sap/bomInformation',
    name: 'BomList',
    meta: {
      icon: 'ios-document',
      title: 'BOM信息'
    },
    component: BomInformation
  },
  {
    path: '/' + namespace + '/sap/wholeOrderIList',
    name: 'wholeOrderIList',
    meta: {
      icon: 'ios-document',
      title: '进口提单整单'
    },
    component: WholeOrderIList
  },
  {
    path: '/' + namespace + '/sap/wholeOrderEList',
    name: 'wholeOrderEList',
    meta: {
      icon: 'ios-document',
      title: '出口提单整单'
    },
    component: WholeOrderEList
  },
  {
    path: '/' + namespace + '/sap/domesticSalesDetailsList',
    name: 'domesticSalesDetailsList',
    meta: {
      icon: 'ios-document',
      title: '内销明细'
    },
    component: DomesticSalesDetailsList
  },
  {
    path: '/' + namespace + '/sap/exportPlanHeadList',
    name: 'exportPlanHeadList',
    meta: {
      icon: 'ios-document',
      title: '出口计划表头'
    },
    component: exportPlanHeadList
  },
  {
    path: '/' + namespace + '/sap/exportPlanBodyList',
    name: 'exportPlanBodyList',
    meta: {
      icon: 'ios-document',
      title: '出口计划表体'
    },
    component: exportPlanBodyList
  },
  {
    path: '/' + namespace + '/sap/exportPlanHeadBodyList',
    name: 'exportPlanHeadBodyList',
    meta: {
      icon: 'ios-document',
      title: '出口计划整单'
    },
    component: exportPlanHeadBodyList
  },
  {
    path: '/' + namespace + '/sap/OriginalBomList',
    name: 'bomInfoTabs',
    meta: {
      icon: 'ios-document',
      title: 'BOM信息'
    },
    component: bomInfoTabs
  }
]
