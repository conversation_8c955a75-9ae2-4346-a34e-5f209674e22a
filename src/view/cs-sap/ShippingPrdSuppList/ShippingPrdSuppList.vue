<template>
  <section>
    <div v-if="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <prdSuppListSearch ref="headSearch" :typeNoInforSearch="typeNo1"></prdSuppListSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { sapCommList } from '../common/sapCommList'
  import prdSuppListSearch from './shippingPrdSuppList-search'
  import { interimVerification, erpInterfaceData } from '@/view/cs-common'
  import { columns } from '../ShippingPrdSuppList/shippingPrdSuppListColumns'

  export default {
    name: 'ShippingPrdSuppList',
    props: {
      /**
       * 0 表示货代基础信息进入
       * 1 表示报关行基础信息进入
       * 2 表示客户基础信息进入
       * 3 表示供应商基础信息进入
       * 否则是其模块进来的
       */
      typeNoInfor: {
        type: String,
        default: () => ('')
      }
    },
    components: {
      prdSuppListSearch
    },
    mixins: [sapCommList, columns],
    data() {
      return {
        typeNo1: '',
        gridConfig: {
          exportTitle: '客户供应商信息'
        },
        // 查询条件行数
        searchLines: 2,
        erpInterfaceData: erpInterfaceData,
        interimVerification: interimVerification,
        toolbarEventMap: {
          'ExtractAll': this.handleExtractAll,
          'ExtractSelected': this.handleExtractSelected
        },
        ajaxUrl: {
          exportUrl: csAPI.sapErp.shippingDeta.exportPrdSuppUrl,
          selectAllPaged: csAPI.deep.deepImpRecord.head.getPrdSupp.getPrdSupp
        }
      }
    },
    watch: {
      typeNoInfor: {
        immediate: true,
        handler: function (typeNo) {
          let me = this
          if ([0, 1, 2, 3].includes(typeNo)) {
            //传值给查询页面
            me.typeNo1 = typeNo
            //控制查询条件默认展开
            me.$set(me, 'checkboxSelection', true)
            me.$nextTick(() => {
              me.$set(me, 'showSearch', true)
              me.loadOtherActions()
            })
          } else {
            me.$set(me, 'checkboxSelection', false)
            me.$nextTick(() => {
              me.$set(me, 'showSearch', false)
            })
          }
          me.setShowFields(me.totalColumns)
          me.actionLoad()
        }
      }
    },
    methods: {
      loadOtherActions() {
        let me = this
        if (me.checkboxSelection) {
          me.actions = [{
            ...me.actionsComm,
            label: '勾选提取',
            key: 'xdo-btn-edit',
            icon: 'ios-checkmark',
            command: 'ExtractSelected'
          }, {
            ...me.actionsComm,
            label: '全部提取',
            icon: 'md-done-all',
            command: 'ExtractAll',
            key: 'xdo-btn-upload'
          }]
        }
      },
      // 点击提取的js方法
      handleExtractSelected() {
        let me = this
        if (me.gridConfig.selectRows.length > 0) {
          const sids = me.gridConfig.selectRows.map(item => {
            return item.sid
          })
          // 带着该条件数据去erp料件表面查询出相关数据，并且提取到转入信息提取表中
          me.$http.post(csAPI.deep.deepImpRecord.head.prdSuppPick.outPickUpList + `/${sids}`).then(res => {
            me.$Message.success('提取成功:' + res.data.data + '条')
            me.$emit('closePickUp', false) //页面自动关闭
          }).catch(() => {
            me.$Message.warning('部分数据已提取请确认!')
          })
        } else {
          me.$Message.warning('请勾选数据')
        }
      },
      /**
       * 提取所有(符合查询条件)
       */
      handleExtractAll() {
        let me = this
        let params = me.getSearchParams()
        let customerType = ''
        if ([0, 1, 2, 3].includes(me.typeNo1)) {
          if (me.typeNo1 === 0) {
            customerType = 'FOD'
          } else if (me.typeNo1 === 1) {
            customerType = 'CUT'
          } else if (me.typeNo1 === 2) {
            customerType = 'CLI'
          } else if (me.typeNo1 === 3) {
            customerType = 'PRD'
          } else {
            customerType = 'COM'
          }
        } else {
          me.$Message.success('提取类型不正确!')
          return
        }
        me.$http.post(csAPI.deep.deepImpRecord.head.prdSuppPick.extractAll + `/${customerType}`, params).then(res => {
          me.$Message.success(res.data.message)
          me.$emit('closePickUp', false) //页面自动关闭
        }).catch(() => {
        })
      },
      /**
       * 获取查询条件
       */
      getSearchParams() {
        let me = this
        if (me.checkboxSelection) {
          return Object.assign({
            status: '0',
            pickUp: '0'
          }, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}))
        } else {
          return Object.assign({
            status: '0',
            pickUp: '1'
          }, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}))
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
