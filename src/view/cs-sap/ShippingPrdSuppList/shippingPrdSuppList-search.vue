<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-4" :model="searchParam" :rules="rulesHeader" label-position="right" :label-width="110" inline>
      <XdoFormItem prop="customerType" label="企业类型">
        <xdo-select v-model="searchParam.customerType" :disabled="codeDisable" :options="this.erpDataSource.customerType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="customerCode" label="企业代码">
        <XdoIInput type="text" v-model="searchParam.customerCode" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyName" label="企业中文名称">
        <XdoIInput type="text" v-model="searchParam.companyName" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="companyNameEn" label="企业英文名称">
        <XdoIInput type="text" v-model="searchParam.companyNameEn" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
      <XdoFormItem prop="tempOwner" label="传输批次号">
        <XdoIInput type="text" v-model="searchParam.tempOwner" clearable></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { delList, erpInterfaceData } from '../../cs-common'

  export default {
    name: 'materialSearch',
    props: {
      typeNoInforSearch: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        codeDisable: false,
        rulesHeader: {
          // customerCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          // customerType: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        searchParam: {
          customerType: '',     // 单证编号
          customerCode: '',     // 移动类型
          companyName: '',      // 关联编号
          companyNameEn: '',    // 物料类型标识
          modifyMark: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          tempOwner: '',
          status: ''            // 提取状态
        },
        erpDataSource: {
          customerType: delList.customerType,//企业类型
          getStatuz: delList.getStatuz//企业类型
        },
        erpInterfaceData: erpInterfaceData
      }
    },
    mounted: function () {
      //this.typeNoInforMethods(this.typeNoInforSearch) //编辑的方法，一面一加载就走js方法
    },
    watch: {
      typeNoInforSearch: {
        immediate: true,
        handler: function (val) {
          //0表示货代基础信息进入 1 表示报关行基础信息进入 2表示客户基础信息进入 3 表示供应商基础信息进入  否则是其模块进来的
          if (val === 0) {
            this.searchParam.customerType = 'FOD'
            this.searchParam.status = '0'
            this.codeDisable = true
            this.$parent.getPickUpList//子页面调用父页面的方法
          } else if (val === 1) {
            this.searchParam.customerType = 'CUT'
            this.searchParam.status = '0'
            this.codeDisable = true
            this.$parent.getPickUpList
          } else if (val === 2) {
            this.searchParam.customerType = 'CLI'
            this.searchParam.status = '0'
            this.codeDisable = true
            this.$parent.getPickUpList
          } else if (val === 3) {
            this.searchParam.customerType = 'PRD'
            this.searchParam.status = '0'
            this.codeDisable = true
            this.$parent.getPickUpList
          } else {
            this.searchParam.customerType = ''
            this.searchParam.status = ''
          }
        }
      }
    },
    methods: {
      /**
       * 接收时间
       * @param values
       */
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
