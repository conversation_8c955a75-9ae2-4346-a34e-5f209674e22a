import { delList } from '../../cs-common'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '企业代码',
          width: 120,
          align: 'center',
          key: 'customerCode'
        },
        {
          title: '企业类型',
          width: 120,
          align: 'center',
          key: 'customerType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, delList.customerType)
          }
        },
        {
          title: '企业中文名称',
          width: 160,
          align: 'center',
          key: 'companyName',
          tooltip: true
        },
        {
          title: '企业英文名称',
          width: 160,
          align: 'center',
          key: 'companyNameEn',
          tooltip: true
        },
        {
          title: '提取状态',
          width: 120,
          align: 'center',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
          }
        },
        {
          title: '数据状态',
          width: 120,
          align: 'center',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          title: '海关注册编码',
          width: 120,
          align: 'center',
          key: 'declareCode'
        },
        {
          title: '社会信用代码',
          width: 120,
          align: 'center',
          key: 'creditCode'
        },
        {
          title: '中文国家',
          width: 160,
          align: 'center',
          key: 'country',
          tooltip: true
        },
        {
          title: '中文城市',
          width: 120,
          align: 'center',
          key: 'city',
          tooltip: true
        },
        {
          title: '中文地区',
          width: 120,
          align: 'center',
          key: 'area',
          tooltip: true
        },
        {
          title: '英文国家',
          width: 160,
          align: 'center',
          key: 'countryEn',
          tooltip: true
        },
        {
          title: '英文城市',
          width: 120,
          align: 'center',
          key: 'cityEn',
          tooltip: true
        },
        {
          title: '英文地区',
          width: 120,
          align: 'center',
          key: 'areaEn',
          tooltip: true
        },
        {
          title: '海关信用等级',
          width: 120,
          align: 'center',
          key: 'customsCreditRating'
        },
        {
          title: 'AEO代码',
          width: 120,
          align: 'center',
          key: 'aeoCode'
        },
        {
          title: '中文联系人',
          width: 120,
          align: 'center',
          key: 'linkmanName'
        },
        {
          title: '联系人手机',
          width: 120,
          align: 'center',
          key: 'mobilePhone'
        },
        {
          title: '联系人邮箱',
          width: 160,
          align: 'center',
          key: 'email',
          tooltip: true
        },
        {
          title: '联系人电话',
          width: 120,
          align: 'center',
          key: 'linkManPhone'
        },
        {
          title: '传真',
          width: 120,
          align: 'center',
          key: 'fax'
        },
        {
          title: '邮编',
          width: 120,
          align: 'center',
          key: 'postal'
        },
        {
          title: '中文地址',
          width: 220,
          align: 'center',
          key: 'address',
          tooltip: true
        },
        {
          title: '英文地址',
          width: 220,
          align: 'center',
          key: 'addressEn',
          tooltip: true
        },
        {
          title: '发票中文地址',
          width: 220,
          align: 'center',
          key: 'invoiceAddress',
          tooltip: true
        },
        {
          title: '发票英文地址',
          width: 220,
          align: 'center',
          key: 'invoiceAddressEn',
          tooltip: true
        },
        {
          title: '送货中文地址',
          width: 220,
          align: 'center',
          key: 'deliverAddress',
          tooltip: true
        },
        {
          title: '送货英文地址',
          width: 220,
          align: 'center',
          key: 'deliverAddressEn',
          tooltip: true
        },
        {
          title: '备注',
          width: 220,
          align: 'center',
          key: 'note',
          tooltip: true
        },
        {
          title: '传输批次号',
          width: 120,
          align: 'center',
          key: 'tempOwner'
        },
        {
          title: 'ERP创建时间',
          width: 150,
          align: 'center',
          key: 'lastModifyDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '提取时间',
          width: 150,
          align: 'center',
          key: 'extractTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '接收时间',
          width: 150,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '更新时间',
          width: 150,
          align: 'center',
          key: 'updateTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }
        // {
        //   title: '英文联系人',
        //   width: 120,
        //   align: 'center',
        //   key: 'linkmanNameEn'
        // }
      ]
    }
  }
}
