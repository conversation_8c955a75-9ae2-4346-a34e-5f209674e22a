export const commonHeight = {
  data(){
    return{
      // 查询条件行数
      searchLines: 3,
      // 是否有按钮行
      hasActions: true,
      // 是否有分页行
      hasPager: true,
      // 是否有子Tab组件
      hasChildTabs: false,
      // (列表高度)偏移值
      OffsetHeight: 0,
    }
  },
  computed: {
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight () {
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 0
      if (this.hasChildTabs) {
        childTabHeight = 38
      }
      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight
      // 去除按鈕行高度: 28px(需要添加2px: 与上下之間的空隙)
      if (this.hasActions) {
        hiddenHeight = hiddenHeight - 30
      }
      // 去除分頁信息行高度: 28px
      if (this.hasPager) {
        hiddenHeight = hiddenHeight - 28
      }
      // 減去偏移量(某些特殊情況使用)
      hiddenHeight = hiddenHeight - this.OffsetHeight
      // 當查詢條件塊打開時候去除其高度
      if (this.showSearch === true) {
        // 與上方間隙高度
        let gapHeight = 6
        // 分行高度
        let lineHeight = 28
        return hiddenHeight - gapHeight - lineHeight * this.searchLines
      }
      return hiddenHeight
    }
  }
}
