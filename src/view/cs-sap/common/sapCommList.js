import pms from '@/libs/pms'
import ImportPage from 'xdo-import'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const sapCommList = {
  mixins: [pms, commList],
  components: {
    ImportPage
  },
  data() {
    return {
      tableId: '',
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      importShow: false,
      checkboxSelection: false,
      showtableColumnSetup: false,
      toolbarEventMap: {
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'import': this.handleImportShow,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  mounted: function () {
    let me = this
    me.setShowFields(me.totalColumns)
    me.loadFunctions().then(() => {
      if (typeof me.actionLoad === 'function') {
        me.actionLoad()
      }
    })
  },
  methods: {
    actionLoad() {
      let me = this
      me.actions = [{
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      }, {
        ...me.actionsComm,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting',
        key: 'xdo-btn-setting'
      }]
      if (typeof me.loadOtherActions === 'function') {
        me.loadOtherActions()
      }
    },
    /**
     * 设置列表显示字段
     * @param totalColumns
     * @param defaultColumns
     */
    setShowFields(totalColumns, defaultColumns) {
      let me = this
      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = []
      if (Array.isArray(defaultColumns) && defaultColumns.length > 0) {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns, me.defaultColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns)
      }
      me.handleUpdateColumn(columns)
    },
    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showtableColumnSetup = true
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      if (me.checkboxSelection) {
        me.gridConfig.gridColumns = [{
          width: 36,
          fixed: 'left',
          align: 'center',
          key: 'selection',
          type: 'selection'
        }, ...columns]
      } else {
        me.gridConfig.gridColumns = columns
      }
      me.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleImportShow() {
      let me = this
      me.importShow = true
    },
    afterImport() {
      let me = this
      me.importShow = false
      me.handleSearchSubmit()
    }
  }
}
