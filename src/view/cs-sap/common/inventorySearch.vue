<template>
  <section>
    <div v-if="showHeadSearch" class="xdo-enter-root" v-focus>
      <XdoForm :label-width="120" :model="searchParam" class="dc-form dc-form-3" inline label-position="right" ref="formInline">
        <XdoFormItem label="仓库编号" prop="emsListNo">
          <XdoIInput type="text" v-model="searchParam.whNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="库位标识">
          <XdoIInput v-model="searchParam.whMark"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="保完税标志">
          <xdo-select :optionLabelRender="pcodeRender" :options="this.productClassify.BONDED_FLAG_SELECT"
                      v-model="searchParam.bondMark"></xdo-select>
        </XdoFormItem>
        <dc-dateRange @onDateRangeChanged="handleValidDateChange" label="日期"></dc-dateRange>
        <XdoFormItem label="料号">
          <XdoIInput v-model="searchParam.facGNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="单耗版本号" prop='exgVersion' v-if="showexgVersion">
          <XdoIInput v-model="searchParam.exgVersion"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="工厂" v-if="showFactory">
          <XdoIInput v-model="searchParam.factory"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="单位" v-if="showCurr">
          <xdo-select :asyncOptions="pcodeList" :meta="pcode.unit" :optionLabelRender="pcodeRender"
                      v-model="searchParam.unit"></xdo-select>
        </XdoFormItem>
        <XdoFormItem label="币制" prop="curr" v-if="showCurr">
          <xdo-select :asyncOptions="pcodeList" :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"
                      v-model="searchParam.curr"></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </div>
    <div v-else class="xdo-enter-root" v-focus>
      <XdoForm :label-width="120" :model="searchParam" class="dc-form dc-form-3" inline label-position="right" ref="formInline">
        <XdoFormItem label="WIP工单号">
          <XdoIInput type="text" v-model="searchParam.woNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="WIP料号">
          <XdoIInput v-model="searchParam.facGNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem label="单耗版本号" prop='exgVersion'>
          <XdoIInput v-model="searchParam.exgVersion"></XdoIInput>
        </XdoFormItem>
        <dc-dateRange @onDateRangeChanged="handleProcessDateChange" label="日期"></dc-dateRange>
        <XdoFormItem label="物料类型" prop='gmark'>
          <XdoIInput v-model="searchParam.gmark"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </div>
  </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'inventorySearch',
    props: {
      showHeadSearch: {
        type: Boolean,
        default: true
      },
      showFactory: {
        type: Boolean,
        default: true
      },
      showexgVersion: {
        type: Boolean,
        default: true
      },
      showCurr: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        productClassify: productClassify,
        searchParam: {
          whNo: '',
          whMark: '',
          bondMark: '',
          facGNo: '',
          exgVersion: '',
          factory: '',
          unit: '',
          curr: '',
          whDateFrom: '',
          whDateTo: '',

          woNo: '',
          // facGNo: '',
          // exgVersion: '',
          gmark: '',
          wipDateFrom: '',
          wipDateTo: ''
        }
      }
    },
    methods: {
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'whDateFrom', (values[0].toString().replace('-', '')).replace('-', ''))
          this.$set(this.searchParam, 'whDateTo', (values[1].toString().replace('-', '')).replace('-', ''))
        } else {
          this.$set(this.searchParam, 'whDateFrom', '')
          this.$set(this.searchParam, 'whDateTo', '')
        }
      },
      handleProcessDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'wipDateFrom', (values[0].toString().replace('-', '')).replace('-', ''))
          this.$set(this.searchParam, 'wipDateTo', (values[1].toString().replace('-', '')).replace('-', ''))
        } else {
          this.$set(this.searchParam, 'wipDateFrom', '')
          this.$set(this.searchParam, 'wipDateTo', '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
