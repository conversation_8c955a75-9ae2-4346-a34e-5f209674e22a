import { getKeyValue } from '@/libs/util'
import { productClassify } from '@/view/cs-common'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columns = {
  mixins:[baseColumns],
  data() {
    return {
      commonColumns : [
        {
          title: '仓库编号',
          minWidth: 120,
          align: 'center',
          key: 'whNo',
        },
        {
          title: '库位标识',
          minWidth: 120,
          align: 'center',
          key: 'whMark',
        },
        {
          title: '日期',
          minWidth: 120,
          align: 'center',
          key: 'whDate',
        },
        {
          title: '料号',
          key: 'facGNo',
          width: 130,
          align: 'center',
        },
        {
          title: '数量',
          minWidth: 120,
          align: 'center',
          key: 'qty'
        },
        {
          title: '单位',
          minWidth: 120,
          align: 'center',
          key: 'unit',
          render:(h,params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '单价',
          minWidth: 120,
          align: 'center',
          key: 'decPrice',
        },
        {
          title: '币制',
          minWidth: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
          }
        },
        {
          title: '保完税标志',
          minWidth: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return h('span', getKeyValue(productClassify.BONDED_FLAG, params.row.bondMark))
          }
        },
        {
          title: '工厂',
          minWidth: 120,
          align: 'center',
          key: 'factory',
        },
        {
          title: '数据状态',
          key: 'modifyMark',
          width: 140,
          align: 'center'
        },
        {
          title: '接收日期',
          key: 'insertTime',
          width: 140,
          align: 'center'
        },
        {
          title: '更新日期',
          key: 'updateTime',
          width: 140,
          align: 'center'
        }
      ],
      inprocessColumns:[
        {
          title: 'WIP工单号',
          key: 'woNo',
          width: 130,
          align: 'center',
        },
        {
          title: 'WIP料号',
          key: 'facGNo',
          width: 130,
          align: 'center',
        },
        {
          title: '物料类型',
          key: 'gmark',
          width: 130,
          align: 'center',
          // render: (h, params) => {
          //   return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          // }
        },
        {
          title: '日期',
          minWidth: 120,
          align: 'center',
          key: 'wipDate',
        },
        {
          title: '数量',
          minWidth: 120,
          align: 'center',
          key: 'qty'
        },
        {
          title: 'ERP单位',
          minWidth: 120,
          align: 'center',
          key: 'unit',
          render:(h,params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '数据状态',
          key: 'modifyMark',
          width: 140,
          align: 'center'
        },
        {
          title: '接收日期',
          key: 'insertTime',
          width: 140,
          align: 'center'
        },
        {
          title: '更新日期',
          key: 'updateTime',
          width: 140,
          align: 'center'
        }
      ],
      extrColumns:[
        {
          title: '单耗版本号',
          minWidth: 120,
          align: 'center',
          key: 'exgVersion',
        }
      ],
      noteColumns:[
        {
          title: '备注1',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'remark1',
        },
        {
          title: '备注2',
          key: 'remark2',
          width: 140,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
      ]
      // ,
      // gridConfig: {
      //   data: [],
      //   selectRows: [],
      //   gridColumns: [],
      //   selectData: []
      // },
      // pageParam: {
      //   page: 1,
      //   limit: 20,
      //   dataTotal: -1,
      //   pageSizeOpts: [10, 20, 50, 100]
      // }
    }
  }
}
export {
  columns
}
