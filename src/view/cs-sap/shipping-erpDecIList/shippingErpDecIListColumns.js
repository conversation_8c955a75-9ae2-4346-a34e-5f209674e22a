import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'
import {isNullOrEmpty} from "@/libs/util";

export const columns = {
  mixins: [baseColumns],
  data() {
    let me = this
    return {
      totalColumns: [
        {
          width: 120,
          title: '单据号',
          align: 'center',
          key: 'linkedNo'
        },
        {
          width: 120,
          key: 'lineNo',
          align: 'center',
          title: '单据序号'
        },
        {
          width: 90,
          key: 'status',
          align: 'center',
          title: '提取状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.EXTRACT_STATUS_MAP)
          }
        },
        {
          width: 120,
          align: 'center',
          title: '数据状态',
          key: 'modifyMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.DATA_STATUS_MAP)
          }
        },
        {
          width: 100,
          align: 'center',
          key: 'bondMark',
          title: '保完税标记',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          width: 160,
          align: 'center',
          key: 'bondMarkConvert',
          title: '转换后保完税标记',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.bondedFlagMap)
          }
        },
        {
          width: 120,
          key: 'gmark',
          align: 'center',
          title: '物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 160,
          align: 'center',
          key: 'gmarkConvert',
          title: '转换后物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 200,
          key: 'facGNo',
          align: 'center',
          title: '企业料号'
        },
        {
          width: 200,
          key: 'copGNo',
          align: 'center',
          title: '备案料号'
        },
        {
          key: 'qty',
          width: 120,
          title: '数量',
          align: 'center'
        },
        {
          width: 120,
          title: '单价',
          align: 'center',
          key: 'decPrice'
        },
        {
          width: 120,
          title: '总价',
          align: 'center',
          key: 'decTotal'
        },
        {
          width: 120,
          key: 'unitErp',
          align: 'center',
          title: 'ERP交易单位'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          align: 'center',
          title: '转换后币制',
          key: 'currConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'netWt',
          title: '净重',
          align: 'center'
        },
        {
          width: 120,
          title: '毛重',
          key: 'grossWt',
          align: 'center'
        },
        {
          width: 120,
          title: '原产国',
          align: 'center',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          align: 'center',
          title: '转换后原产国',
          key: 'originCountryConvert'
        },
        {
          width: 120,
          title: '发票号',
          align: 'center',
          key: 'invoiceNo'
        },
        {
          width: 120,
          title: '发票日期',
          align: 'center',
          key: 'invoiceDate'
        },
        {
          width: 120,
          align: 'center',
          title: '采购单号',
          key: 'purchaseNo'
        },
        {
          width: 120,
          align: 'center',
          title: '采购单行号',
          key: 'purchaseLineNo'
        },
        {
          width: 120,
          align: 'center',
          key: 'orderDate',
          title: '采购订单日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          align: 'center',
          title: '采购数量',
          key: 'purchaseNum'
        },
        {
          width: 120,
          align: 'center',
          title: '供应商代码',
          key: 'supplierCode'
        },
        {
          width: 120,
          align: 'center',
          title: '供应商名称',
          key: 'supplierName'
        },
        {
          width: 120,
          key: 'inWay',
          align: 'center',
          title: '进口方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.IMPORT_METHOD_MAP)
          }
        },
        {
          width: 180,
          align: 'center',
          title: '原始物料',
          key: 'originalGNo'
        },
        {
          width: 180,
          align: 'center',
          title: '英文名称',
          key: 'copGNameEn'
        },
        {
          width: 120,
          key: 'factory',
          align: 'center',
          title: '厂商编号'
        },
        {
          width: 120,
          key: 'qty1',
          align: 'center',
          title: '法定数量'
        },
        {
          width: 120,
          key: 'qty2',
          align: 'center',
          title: '法二数量'
        },
        {
          width: 90,
          tooltip: true,
          ellipsis: true,
          key: 'dutyMode',
          align: 'center',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 160,
          align: 'center',
          title: '境内目的地',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 160,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'districtPostCode',
          title: '境内目的地(行政区划)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 120,
          align: 'center',
          title: '最终目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          title: '转换后最终目的国',
          key: 'destinationCountryConvert'
        },
        {
          width: 120,
          tooltip: true,
          ellipsis: true,
          key: 'entryGNo',
          align: 'center',
          title: '报关单归并序号'
        },
        {
          width: 250,
          key: 'note',
          title: '备注',
          align: 'left'
        },
        {
          minWidth: 120,
          tooltip: true,
          key: 'remark1',
          ellipsis: true,
          align: 'center',
          title: 'remark1'
        },
        {
          minWidth: 120,
          tooltip: true,
          key: 'remark2',
          ellipsis: true,
          align: 'center',
          title: 'remark2'
        },
        {
          tooltip: true,
          minWidth: 120,
          key: 'remark3',
          ellipsis: true,
          align: 'center',
          title: 'remark3'
        },
        {
          width: 120,
          align: 'center',
          key: 'tempOwner',
          title: '传输批次号'
        },
        {
          width: 150,
          align: 'center',
          title: 'ERP创建时间',
          key: 'lastModifyDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          align: 'center',
          title: '提取时间',
          key: 'extractTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          align: 'center',
          title: '接收时间',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 150,
          align: 'center',
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 160,
          align: 'center',
          key: 'royalityNo',
          title: '特许权关联号'
        },
        {
          width: 120,
          key: 'inOutRelNo',
          title: '入库关联单号'
        },
        {
          width: 120,
          key: 'gmodel',
          align: 'center',
          title: '申报规格型号'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报单位'
        },
        {
          width: 120,
          key: 'packNum',
          title: '件数'
        },
        {
          width: 120,
          key: 'proCategory',
          title: '采购类型'
        },
        {
          title: '订单类型',
          width: 120,
          align: 'center',
          key: 'billType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.erpBillType)
          }
        },
        {
          width: 200,
          key: 'filePath',
          title: '附件',
          render: (h, params) => {
            let value = params.row[params.column.key]
            if (isNullOrEmpty(value)) {
              return h('span', '')
            } else {
              return h('div', [
                h('a', {
                  props: {
                    type: 'primary'
                  },
                  style: {
                    marginLeft: '15px'
                  },
                  on: {
                    click: () => {
                        // me.fileDownload(params.row.filePath)
                        me.downloadOrderPdf(params.row.filePath)
                    }
                  }
                }, 'Y')
              ])
            }
          }
        }
      ]
    }
  }
}
