<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ShippingErpDecISearch ref="headSearch"></ShippingErpDecISearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { sapCommList } from '../common/sapCommList'
  import { columns } from './shippingErpDecIListColumns'
  import ShippingErpDecISearch from './shipping-erpDecIList-search'
  import { dynamicExport } from '../../cs-common/dynamic-export/dynamicExport'
  import { interimVerification, erpInterfaceData, importExportManage } from '@/view/cs-common'

  export default {
    name: 'ShippingEroDecIList',
    components: {
      ShippingErpDecISearch
    },
    mixins: [sapCommList, columns, dynamicExport],
    data() {
      return {
        // 查询条件行数
        searchLines: 6,
        gridConfig: {
          exportTitle: '进口明细'
        },
        cmbSource: {
          tradeCodeData: []
        },
        erpInterfaceData: erpInterfaceData,
        importExportManage: importExportManage,
        interimVerification: interimVerification,
        taskInfo: {
          taskCode: 'I_ERP_DETAIL'     // 添加任务使用的taskCode
        },
        ajaxUrl: {
          downloadFile: csAPI.sapErp.shippingErpDecIList.downloadFile,
          down: csAPI.sapErp.shippingErpDecIList.down,
          exportUrl: csAPI.sapErp.shippingErpDecIList.exportUrl,
          selectAllPaged: csAPI.sapErp.shippingErpDecIList.selectAllPaged
        }
      }
    },
    created: function () {
      let me = this
      me.$http.post(csAPI.ieParams.PRD).then(res => {
        me.cmbSource.tradeCodeData = res.data.data.map(item => {
          return {
            label: item['LABEL'],
            value: item['VALUE']
          }
        })
      }).catch(() => {
        me.cmbSource.tradeCodeData = []
      })
    },
    methods:{
      /**
       * 附件下载
       * @param row
       *
       */
      downloadOrderPdf(filePath){
        let me = this
        console.log(filePath)
        me.$http.post(me.ajaxUrl.down+ '/' + filePath).then(res => {
          const resultMap = new Map(Object.entries(res.data));

          if (resultMap.size !== 0) {
            resultMap.forEach((value, key) => {
              window.open(key)
              // let link = document.createElement('a');
              // fetch(key)
              //   .then(res => {
              //     if (!res.ok) {
              //       throw new Error('下载文件失败');
              //     }
              //     return res.blob();
              //   })
              //   .then(blob => {
              //     link.href = URL.createObjectURL(blob);
              //     link.download = value; //文件名
              //     document.body.appendChild(link);
              //     link.click();
              //     document.body.removeChild(link);
              //     URL.revokeObjectURL(link.href);
              //   })
              //   .catch(error => {
              //     console.error('下载文件失败:', error);
              //   });
            });
          }else {
            me.$Message.error('未查询到相应文件')
          }
        })
      },


    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
