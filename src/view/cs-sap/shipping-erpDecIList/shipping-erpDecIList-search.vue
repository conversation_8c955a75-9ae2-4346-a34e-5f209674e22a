<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="110" inline>
      <XdoFormItem prop="linkedNo" label="单据号">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="提取状态">
        <xdo-select v-model="searchParam.status" :options="this.erpInterfaceData.EXTRACT_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="modifyMark" label="数据状态">
        <xdo-select v-model="searchParam.modifyMark" :options="this.erpInterfaceData.DATA_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="bondMarkConvert" label="转换后保完税标记">
        <xdo-select v-model="searchParam.bondMarkConvert" :options="this.importExportManage.bondedFlagMap"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gmarkConvert" label="转换后物料类型">
        <xdo-select v-model="searchParam.gmarkConvert" :options="this.productClassify.GMARK_SELECT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="purchaseNo" label="采购订单号">
        <XdoIInput type="text" v-model="searchParam.purchaseNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="supplierName" label="供应商名称">
        <XdoIInput type="text" v-model="searchParam.supplierName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="inWayConvert" label="进口方式">
        <xdo-select v-model="searchParam.inWayConvert" :options="this.erpInterfaceData.IMPORT_METHOD_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="接收时间" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
      <dc-dateRange label="订单日期" @onDateRangeChanged="handleOrderDateChange"></dc-dateRange>
      <XdoFormItem prop="decPriceFlag" label="单价">
        <XdoCheckbox v-model="decPriceFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
      </XdoFormItem>
      <XdoFormItem prop="netWtFlag" label="净重">
        <XdoCheckbox v-model="netWtFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
      </XdoFormItem>
      <XdoFormItem prop="tempOwner" label="传输批次号">
        <XdoIInput type="text" v-model="searchParam.tempOwner"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { erpInterfaceData, importExportManage, productClassify } from '../../cs-common'

  export default {
    name: 'shippingErpDecIListSearch',
    data() {
      return {
        netWtFlag: false,
        decPriceFlag: false,
        searchParam: {
          linkedNo: '',
          status: '',
          modifyMark: '',
          bondMarkConvert: '',
          gmarkConvert: '',
          facGNo: '',
          copGNo: '',
          invoiceNo: '',
          purchaseNo: '',
          supplierName: '',
          inWayConvert: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          poDateFrom: '',
          poDateTo: '',
          decPrice: null,
          netWt: null,
          tempOwner: ''
        },
        productClassify: productClassify,
        erpInterfaceData: erpInterfaceData,
        importExportManage: importExportManage
      }
    },
    watch: {
      netWtFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'netWt', 1)
          } else {
            this.$set(this.searchParam, 'netWt', null)
          }
        }
      },
      decPriceFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'decPrice', 1)
          } else {
            this.$set(this.searchParam, 'decPrice', null)
          }
        }
      }
    },
    methods: {
      /**
       * 更新日期范围
       * @param values
       */
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      /**
       * 订单日期范围
       * @param values
       */
      handleOrderDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "poDateFrom", values[0])
          this.$set(this.searchParam, "poDateTo", values[1])
        } else {
          this.$set(this.searchParam, "poDateFrom", '')
          this.$set(this.searchParam, "poDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
