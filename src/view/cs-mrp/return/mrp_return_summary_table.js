import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import DcImport from '@/components/dc-import'
import searchMixin from './mrp_return_summary_search'
import { getGridExportColumns } from '../../cs-common/function'

export default {
  name: 'MrpBackSummaryTable',
  mixins: [dynamicHeight, searchMixin],
  components: {
    DcImport
  },
  props: {
    editOption: {
      type: Object,
      default: () => ({})
    },
    refresh: {
      type: Number
    }
  },
  data() {
    return {
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10, 20, 30, 40, 50]
      },
      tableColumns: [
        {
          title: '企业料件料号',
          width: 120,
          key: 'facGNo',
        },
        {
          title: '备案料件料号',
          width: 120,
          key: 'copGNo'
        },
        {
          title: '备案料件序号',
          width: 120,
          key: 'gno'
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS'
        },
        {
          title: '料件商品名称',
          width: 120,
          key: 'gname'
        },
        {
          title: '原材料数量',
          width: 120,
          key: 'imgQty',
        },
        {
          title: '半成品折料数量',
          width: 120,
          key: 'semiExgQty',
        },
        {
          title: '成品折料数量',
          width: 120,
          key: 'exgQty',
        },

        {
          title: '汇总数量',
          width: 120,
          key: 'sumQty',
        },
        {
          title: '调整后数量',
          width: 120,
          key: 'adjustmentQty',
          editable: this.editOption.data.status === '0' || this.editOption.data.status === '1',
          onCellValueChanged: this.onCellValueChanged
        }
      ],
      tableData: [],
      totalContent: '',
      importShow: false,
      importConfig: {
        tplUrl: '',
        errorUrl: csAPI.mrp.return.rest + '/export/summary',
        correctUrl: csAPI.mrp.return.rest + '/import/summary',
        url: csAPI.mrp.return.rest + '/import/summary/adjustmentqty'
      },
      importParam: {
        headId: ''
      }
    }
  },
  mounted() {
    this.refreshDynamicHeight(280, !this.showSearch ? ['area_search'] : null)
    this.searchParam.headId = this.editOption.data.sid
    this.importParam.headId = this.editOption.data.sid
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },
    pageChange(val) {
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val) {
      this.pageParam.limit = val
      if (this.pageParam.page === 1) {
        this.loadData()
      }
    },
    loadStat() {
      this.$http.post(csAPI.mrp.return.summaryStat, this.searchParam).then(res => {
        const data = res.data.data
        if (data) {
          this.totalContent = `内销总数量：${data.total}`
        }
      }, () => {
      })
    },
    onCellValueChanged(event) {
      if (!event.data.adjustmentQty || !/^\d*\.?\d*$/.test(event.data.adjustmentQty.toString())) {
        this.$Message.warning('请输入有效的数据')
        return
      }
      this.$http.put(`${csAPI.mrp.return.summary}/${event.data.sid}/adjustmentQty`, event.data).then(() => {
      }, () => {
      })
    },
    loadData() {
      this.$http.post(csAPI.mrp.return.summary, this.searchParam, {
        params: {
          page: this.pageParam.page,
          limit: this.pageParam.limit
        }
      }).then(res => {
        this.tableData = res.data.data
        this.pageParam.dataTotal = res.data.total
      }, () => {
      })
    },
    exportExcel() {
      const param = {
        name: '折料汇总',
        exportColumns: this.searchParam,
        header: getGridExportColumns(this.tableColumns)
      }
      excelExport(csAPI.mrp.return.exportSummary, param)
    },
    importExcel() {
      this.importShow = true
    },
    importSuccess() {
      this.doQuery()
    }
  },
  watch: {
    refresh() {
      this.doQuery()
    }
  }
}
