import pmsMixin from '@/libs/pms'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import MrpBackDetailTable from './MrpReturnDetailTable'
import MrpBackSummaryTable from './MrpReturnSummaryTable'
import MrpReturnConfirm from './components/MrpReturnConfirm.vue'

export default {
  name: 'MrpBack',
  mixins: [dynamicHeight, pmsMixin],
  components: {MrpBackDetailTable, MrpBackSummaryTable, MrpReturnConfirm},
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      actions: [],
      refresh: 0,
      toolbarEventMap: {
        'bom': this.handleBOM,
        'back': this.handleReturn,
        'export': this.handleExport,
        'batch-edit': this.handleImport,
        'clean': this.handleClean,
        'clean-zeros': this.handleCleanZeros
      },
      tabName: 'detailTab',
      detailActions: [],
      summaryActions: [],
      errorBomData: [],
      showConfirm: false,
      returnParam: {
        confirmKey: null
      }
    }
  },
  mounted() {
    this.loadFunctions('back', 120).then(() => {
      this.detailActions = this.actions
      const param = {resId: this.$route.path, group: 'summary'}
      this.getFunctions(param).then(res => {
        this.summaryActions = [...this.actions, ...res]
      })
    })
  },
  methods: {
    loadData() {
    },
    onReturnConfirm() {
      this.handleReturn(true)
    },
    handleBOM() {
      this.setToolbarLoading('bom', true)
      this.$http.get(`${csAPI.mrp.return.bom}/${this.editOption.data.sid}`)
        .then(res => {
          if (parseInt(res.data.data) > 0) {
            this.$Message.warning('开始导出没有匹配到版本号的成品或半成品')
            excelExport(`${csAPI.mrp.return.exportBom}/${this.editOption.data.sid}`, {name: '缺失BOM的数据'}, 'get')
          } else {
            this.$Message.success('检查通过')
          }
        }, () => {
        })
        .finally(() => {
          this.setToolbarLoading('bom')
        })
    },
    handleReturn(isConfirm) {
      this.setToolbarLoading('back', true)
      const param = {}
      if (isConfirm) {
        Object.assign(param, this.returnParam)
      }
      this.$http.post(`${csAPI.mrp.return.start}/${this.editOption.data.sid}`, param, {noIntercept: true})
        .then(res => {
          if (res.data && res.data.success) {
            this.refresh = this.refresh + 1
          } else {
            if (!res.data.data) {
              this.$Message.warning(res.data.message)
            } else {
              this.errorBomData = res.data.data
              this.returnParam.confirmKey = res.data.message
              this.showConfirm = true
            }
          }
        }, () => {
        })
        .finally(() => {
          this.setToolbarLoading('back')
        })
      this.returnParam.confirmKey = null
    },
    handleImport() {
      if (this.tabName !== 'detailTab') {
        this.$refs['summaryTable'].importExcel()
      }
    },
    handleExport() {
      if (this.tabName === 'detailTab') {
        this.$refs['detailTable'].exportExcel()
      } else {
        this.$refs['summaryTable'].exportExcel()
      }
    },
    handleClean() {
      this.$Modal.confirm({
        title: '提醒',
        okText: '删除',
        cancelText: '取消',
        content: '将删除所有企业料号汇总数量小于等于0的数据，并需重新进行“BOM完整性检查及折料”',
        onOk: () => {
          const commandAction = this.summaryActions.find(it => it.command === 'clean')
          if (commandAction) {
            commandAction.loading = true
          }
          this.$http.delete(`${csAPI.mrp.return.clean}/${this.editOption.data.sid}`).then((res) => {
            if (res.data.data > 0) {
              this.$Message.success('清理成功')
            } else {
              this.$Message.warning('没有企业料号汇总后的数据没有小于等于0的数据')
            }
          }, () => {
          }).finally(() => {
            this.$refs['detailTable'].doQuery()
            this.$refs['summaryTable'].doQuery()
            if (commandAction) {
              commandAction.loading = false
            }
          })
        }
      })
    },
    /**
     * 清理零值
     */
    handleCleanZeros() {
      let me = this
      me.$Modal.confirm({
        title: '提醒',
        okText: '清理零值',
        cancelText: '取消',
        content: '将清理所有企业料号汇总数量等于0的数据',
        onOk: () => {
          me.setToolbarLoading('clean-zeros', true)
          me.$http.delete(`${csAPI.mrp.return.cleanZero}/${me.editOption.data.sid}`).then(res => {
            if (res.data.data > 0) {
              me.$Message.success('清理成功')
            } else {
              me.$Message.warning('没有企业料号汇总后的数据没有等于0的数据')
            }
          }, () => {
          }).finally(() => {
            me.$refs['detailTable'].doQuery()
            me.$refs['summaryTable'].doQuery()
            me.setToolbarLoading('clean-zeros')
          })
        }
      })
    }
  },
  computed: {
    /**
     * 通关业务设置
     */
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    }
  },
  watch: {
    tabName: {
      handler() {
        if (this.tabName === 'detailTab') {
          this.actions = this.detailActions
        } else {
          this.actions = this.summaryActions
        }
      }
    }
  }
}
