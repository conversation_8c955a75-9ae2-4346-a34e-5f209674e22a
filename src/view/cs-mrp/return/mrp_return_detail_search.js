import { productClassify } from '@/view/cs-common'
export default {
  data () {
    return {
      searchSchema: {
        titleWidth: 130
      },
      showSearch: false,
      searchParam: {
        headId: '',
        gmark: '',
        facExgNo: '',
        exgGNo: '',
        exgCodeTS: '',
        exgGName: '',
        exgVersion: '',
        facGNo: '',
        copImgNo: '',
        imgGNo: '',
        codeTS: '',
        gname: ''
      }
    }
  },
  methods: {
    handleSearchSubmit () {
      this.doQuery()
    },
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(230, !this.showSearch ? ["area_search"] : null)
    }
  },
  computed: {
    searchElements () {
      return [
        {
          title: '成品/半成品企业料号', key: 'facExgNo',
          attrs: {
            maxlength: 30
          }
        },
        {
          type:"select", key: 'gmark', title: '物料类型',
          props: { options: productClassify.GMARK_MRP_SELECT }
        },
        {
          title: '备案序号', key: 'exgGNo'
        },
        {
          title: '成品商品编码', key: 'exgCodeTS'
        },
        {
          title: '成品商品名称', key: 'exgGName'
        },
        {
          title: 'BOM版本号', key: 'exgVersion'
        },
        {
          title: '企业料件料号', key: 'facGNo',
        },
        {
          title: '备案料件料号', key: 'copImgNo',
        },
        {
          title: '备案料件序号', key: 'imgGNo',
        },
        {
          title: '料件商品编码', key: 'codeTS'
        },
        {
          title: '料件商品名称', key: 'gname'
        }
      ]
    }
  }
}
