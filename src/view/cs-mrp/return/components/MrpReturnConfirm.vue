<template>
  <Modal v-model="value" width="920" class-name="vertical-center-modal" title="以下料号缺少BOM,是否继续" :closable="false" :mask-closable="false">
    <xdo-ag-grid class="dc-table" ref="table" :columns="columns" :data="dataSource" :options="gridOptions" :height="480"></xdo-ag-grid>
    <div slot="footer">
      <Button type="primary" @click="handleExportExcel">导出错误数据</Button>
      <Button type="primary" @click="handleConfirm">继续</Button>
      <Button type="text" @click="handleClose">关闭</Button>
    </div>
  </Modal>
</template>
<script>
import { export_array_to_excel } from '@/libs/excel'

export default {
  name: 'MrpPriceCalcRange',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    dataSource: {
      type: Array
    }
  },
  data () {
    return {
      columns: [
        {
          title: '企业料号',
          width: 160,
          key: 'facGNo',
        },
        {
          title: 'BOM版本号',
          width: 160,
          key: 'exgVersion',
        }
      ],
      gridOptions: {
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    handleConfirm () {
      this.$emit('returnConfirm')
      this.handleClose()
    },
    handleExportExcel() {
      export_array_to_excel({key: this.columns.map(it => it.key), data: this.dataSource, title: this.columns.map(it => it.title), filename: "缺失BOM的数据", autoWidth: true })
      this.$Message.success('导出成功')
    }
  }
}
</script>
<style scoped>
</style>
