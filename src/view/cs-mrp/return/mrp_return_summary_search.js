export default {
  data () {
    return {
      searchSchema: {
        titleWidth: 130
      },
      showSearch: false,
      searchParam: {
        headId: '',
        facGNo: '',
        copGNo: '',
        imgGNo: '',
        codeTS: '',
        gname: ''
      }
    }
  },
  methods: {
    handleSearchSubmit () {
      this.doQuery()
    },
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(230, !this.showSearch ? ["area_search"] : null)
    }
  },
  computed: {
    searchElements () {
      return [
        {
          title: '企业料件料号', key: 'facGNo',
        },
        {
          title: '备案料件料号', key: 'copGNo',
        },
        {
          title: '备案料件序号', key: 'gno',
        },
        {
          title: '料件商品编码', key: 'codeTS'
        },
        {
          title: '料件商品名称', key: 'gname'
        }
      ]
    }
  }
}
