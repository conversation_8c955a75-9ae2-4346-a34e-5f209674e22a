import { csAPI, excelExport } from '@/api'
import { productClassify } from '@/view/cs-common'
import { getKeyValue } from "@/libs/util"
import { dynamicHeight } from '@/common'
import {getGridExportColumns} from "../../cs-common/function"
import searchMixin from './mrp_return_detail_search'

export default {
  name: 'MrpBackDetailTable',
  mixins: [ dynamicHeight, searchMixin ],
  props: {
    editOption: {type: Object, default: () => ({})},
    refresh: { type: Number }
  },
  data () {
    return {
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableColumns: [
        {
          title: '成品/半成品企业料号',
          width: 210,
          key: 'facExgNo',
        },
        {
          title: '物料类型',
          width: 120,
          key: 'gmark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify.GMARK_MRP_SELECT, value) }
        },
        {
          title: '备案序号',
          width: 120,
          key: 'exgGNo'
        },
        {
          title: '成品商品编码',
          width: 120,
          key: 'exgCodeTS'
        },
        {
          title: '成品商品名称',
          width: 120,
          key: 'exgGName'
        },
        {
          title: 'BOM版本号',
          width: 120,
          key: 'exgVersion'
        },
        {
          title: '企业料件料号',
          width: 120,
          key: 'facGNo',
        },
        {
          title: '备案料件料号',
          width: 120,
          key: 'copImgNo',
        },
        {
          title: '备案料件序号',
          width: 80,
          key: 'imgGNo',
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS'
        },
        {
          title: '料件商品名称',
          width: 120,
          key: 'gname'
        },
        {
          title: '内销数量',
          width: 120,
          key: 'qty',
        },
        {
          title: '单耗',
          width: 80,
          key: 'decAllConsume',
        },
        {
          title: '损耗率',
          width: 80,
          key: 'decDm',
        },
        {
          title: '净耗',
          width: 80,
          key: 'decCm',
        },
        {
          title: '折算原料数量',
          width: 120,
          key: 'imgQty',
        }
      ],
      tableData: [],
      totalContent: '',
    }
  },
  mounted () {
    this.refreshDynamicHeight(230, !this.showSearch ? ['area_search'] : null)
    this.searchParam.headId = this.editOption.data.sid
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    },
    loadStat () {
      this.$http.post(csAPI.mrp.return.stat, this.searchParam)
        .then(res => {
          const data = res.data.data
          if (data) {
            this.totalContent = `料件内销数量：${data.imgTotal} 成品折料数量：${data.exgTotal} 半成品折料数量：${data.semiTotal} 内销总数量：${data.total}`
          }
        }, () => {})
    },
    loadData () {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.return.list, this.searchParam, { params: pageParam })
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {})
    },
    exportExcel () {
      const param = {
        exportColumns: this.searchParam,
        name: '折料明细',
        header: getGridExportColumns(this.tableColumns)
      }

      excelExport(csAPI.mrp.return.export, param)
    }
  },
  watch: {
    refresh () {
      this.doQuery();
    }
  }
}
