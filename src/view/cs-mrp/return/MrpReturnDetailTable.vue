<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head" style="text-align: right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
        <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
      </div>
      <div ref="area_search">
        <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
          <template v-slot:startDate>
            <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
          </template>
        </FormBuilder>
      </div>
    </XdoCard>
    <XdoCard>
      <xdo-ag-grid class="dc-table" ref="table" :columns="tableColumns" :data="tableData" :height="dynamicHeight"></xdo-ag-grid>
      <div ref="area_page" style="height: 26px; overflow: hidden">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
              @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        <span style="font-size:12px; position: relative; top: -22px; float: right; margin-right: 90px">{{totalContent}}</span>
      </div>
    </XdoCard>
  </section>
</template>
<script src="./mrp_return_detail_table.js"></script>
