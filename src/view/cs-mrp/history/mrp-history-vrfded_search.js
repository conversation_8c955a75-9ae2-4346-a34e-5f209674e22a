export default {
  data () {
    return {
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      searchParam: {
        headId: '',
        facGNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        tradeMode: ''
      }
    }
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'copGNo', title: '备案料号'
        },
        {
          key: 'gno', title: '备案序号'
        },
        {
          key: 'tradeMode', type: 'pcode', title: '监管方式',
          props: {
            meta: 'TRADE'
          }
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        }
      ]
    }
  }
}
