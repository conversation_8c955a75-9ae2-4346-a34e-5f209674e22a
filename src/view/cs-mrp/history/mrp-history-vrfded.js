
import searchMixin from './mrp-history-vrfded_search'
import tableMixin from './mrp-history-vrfded-table'
import { dynamicHeight } from '@/common'
import pmsMixin from '@/libs/pms'
import { csAPI } from '@/api'
import dcImportMixin from '@/mixin/dc_import_mixin'

export default {
  name: 'MrpHistoryVrfded',
  mixins:[dynamicHeight, pmsMixin, searchMixin, tableMixin, dcImportMixin],
  data () {
    return {
      toolbarEventMap: {
        import: this.handleImport,
        delete: this.handleDelete,
        'all-delete': this.handleDeleteAll
      },
      importConfig: {
        tplName: '内销历史核扣明细导入',
        url: csAPI.mrp.history.rest + "/import",
        tplUrl: csAPI.mrp.history.rest + "/export/tpl",
        correctUrl: csAPI.mrp.history.rest + "/import",
        errorUrl: csAPI.mrp.history.rest + "/export"
      },
    }
  },
  mounted () {
    this.loadFunctions('default', 112)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.getList()
    },
    getList() {
      this.$http.post(csAPI.mrp.history.list, this.searchParam)
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {})
    },
    handleDelete () {
      if (this.tableSelectedRows.length > 0) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.setToolbarLoading('delete', true)
            const sids = this.tableSelectedRows.map(item => item.sid).join(',')
            this.$http.delete(`${csAPI.mrp.history.batch}/${sids}`).then(() => {
              this.$Message.success('删除成功！')
              this.selectRows = []
              this.getList()
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('delete')
            })
          },
          onCancel: () => {
          }
        })
      } else {
        this.$Message.warning('请选择要删除的数据！')
      }
    },
    handleDeleteAll () {
      this.$Modal.confirm({
        title: '提醒',
        content: '要删除所有数据吗',
        okText: '删除',
        cancelText: '取消',
        onOk: () => {
          this.setToolbarLoading('all-delete', true)
          this.$http.delete(csAPI.mrp.history.clear).then(() => {
            this.$Message.success('删除所有数据成功！')
            this.selectRows = []
            this.getList()
          }).catch(() => {
          }).finally(() => {
            this.setToolbarLoading('all-delete')
          })
        },
        onCancel: () => {
        }
      })
    },
    importSuccess () {
      this.doQuery()
    }
  }
}
