<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
        </FormBuilder>
      </div>
    </XdoCard>
    <div class="action mrp-toolbar" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard>
      <xdo-ag-grid class="dc-table" ref="table" :columns="tableColumns" :data="tableData" checkboxSelection rowSelection='multiple'
                   :options="gridOptions" :height="dynamicHeight" @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div ref="area_page" style="height: 26px; overflow: hidden">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
    <DcImport v-model="importShow" :config="importConfig" :startRow="4" @importSuccess="importSuccess">
      <template v-slot:note>
        <div style="margin-top: 15px; text-align: center">内销数量为0的数据会被忽略</div>
      </template>
    </DcImport>
  </section>
</template>
<script src="./mrp-history-vrfded.js"></script>

