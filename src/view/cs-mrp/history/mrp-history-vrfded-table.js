
export default {
  data () {
    return {
      tableColumns: [
        {
          title: '备案号',
          width: 120,
          key: 'emsNo',
        },
        {
          title: '核注清单编号',
          width: 120,
          key: 'listNo',
        },
        {
          title: '核注清单申报日期',
          width: 160,
          key: 'declareDate',
        },
        {
          title: '报关单号',
          width: 160,
          key: 'entryNo',
        },
        {
          title: '企业料号',
          width: 160,
          key: 'facGNo',
        },
        {
          title: '备案料号',
          width: 160,
          key: 'copGNo',
        },
        {
          title: '备案序号',
          width: 80,
          key: 'gno',
        },
        {
          title: '监管方式',
          width: 120,
          key: 'tradeMode',
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS',
        },
        {
          title: '料件商品名称',
          width: 220,
          key: 'gname',
        },
        {
          title: '内销数量',
          width: 120,
          key: 'qty',
        },
        {
          title: '备注',
          width: 220,
          key: 'note',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      gridOptions: {},
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    handleSelectionChange (param) {
      this.tableSelectedRows = param.api.getSelectedRows()
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
