import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_trial_search'
import tableMixin from './mrp_trial_table'
import updateEditDataMixin from '../common/update_edit_data_mixin'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'

export default {
  name: 'MrpTrial',
  mixins:[dynamicHeight, pmsMixin, searchMixin, tableMixin, updateEditDataMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      toolbarEventMap: {
        start: this.handleStart,
        export: this.handleExport,
        exportBack: this.handleExportBack
      },
      trialParam: {
        headId: ''
      }
    }
  },
  mounted () {
    this.trialParam.headId = this.editOption.data.sid
    this.searchParam.headId = this.editOption.data.sid
    this.loadFunctions('trial', 160)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },

    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '补税金额试算',
        header: getGridExportColumns(this.tableColumns)
      }
      excelExport(csAPI.mrp.trial.export, param)
    },
    handleExportBack() {
      const param = {
        exportColumns: this.searchParam,
        name: '补税金额试算(备案料号级)',
        header: getGridExportColumns(this.tableColumns)
      }
      excelExport(csAPI.mrp.trial.exportBack, param)
    },
    handleStart () {
      this.start()
    },
    loadStat () {
      this.$http.post(csAPI.mrp.trial.stat, this.searchParam)
        .then(res => {
          const data = res.data.data
          if (data) {
            this.totalContent = `HS编码无对应税率的料号：${data.noTaxCount || 0 }个 预估关税总额：${data.totalTaxPredict || '无'} 预估增值税总额：${data.totalAddTaxPredict || '无'}`
          }
        }, () => {})
    },
    loadData () {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.trial.list, this.searchParam, { params: pageParam })
        .then(res => {
          this.tableData = res.data.data || []
          this.pageParam.dataTotal = res.data.total
        }, () => {})
    },
    start () {
      this.setToolbarLoading('start', true)
      this.$http.post(csAPI.mrp.trial.start, this.trialParam)
        .then(() => {
          this.doQuery()
          this.updateEditOption()
        }, () => {})
        .finally(() => {
          this.setToolbarLoading('start')
        })
    }
  },
  computed: {
  }
}
