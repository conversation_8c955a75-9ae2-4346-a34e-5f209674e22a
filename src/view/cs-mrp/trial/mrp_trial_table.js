import { roundUp } from '@/libs/num'

export default {
  data () {
    return {
      tableColumns: [
        {
          title: '企业料件料号',
          width: 120,
          key: 'facGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料号',
          width: 120,
          key: 'copGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案序号',
          width: 120,
          key: 'gno',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '料件商品名称',
          width: 220,
          key: 'gname',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '调整后数量',
          width: 120,
          key: 'qty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '匹配数量',
          width: 120,
          key: 'matchQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '原产国',
          width: 100,
          key: 'originCountry',
          sortable: true,
          filter: "agTextColumnFilter",
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('COUNTRY_OUTDATED', value)}` : '' }
        },
        {
          title: '原产地证号',
          width: 120,
          key: 'certificateNo'
        },
        {
          title: '最终审核单价（USD）',
          width: 140,
          key: 'decPrice',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 4); }
        },
        {
          title: '总价（USD）',
          width: 160,
          key: 'usdDecTotal',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 2); }
        },
        {
          title: '总价（RMB）',
          width: 160,
          key: 'rmbDecTotal',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 2); }
        },
        {
          title: '关税类型',
          width: 140,
          key: 'taxType',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '关税率%',
          width: 120,
          key: 'taxRate',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '预估关税（RMB）',
          width: 160,
          key: 'taxPredict',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 2); }
        },
        {
          title: '增值税率%',
          width: 100,
          key: 'addTaxRate',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '预估增值税（RMB）',
          width: 160,
          key: 'addTaxPredict',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 2); }
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      gridOptions: {
      },
      tableData: [],
      totalContent: ''
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}

