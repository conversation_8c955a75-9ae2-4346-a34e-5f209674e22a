export default {
  data () {
    return {
      taxOptions: [{ value: '1', label: "有" }, { value: '0', label: '无' }],
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      searchParam: {
        headId: '',
        facGNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        originCountry: '',
        tax: ''
      }
    }
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'copGNo', title: '备案料号'
        },
        {
          key: 'gno', title: '备案序号'
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        },
        {
          key: 'originCountry', type: 'pcode', title: '原产国',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        },
        {
          type:"select", key: 'tax', title: '税率取值',
          props: { options: this.taxOptions, optionLabelRender: (it) => it.label }
        }
      ]
    }
  }
}
