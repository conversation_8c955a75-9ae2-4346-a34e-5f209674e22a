export default {
  data () {
    return {
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      searchParam: {
        headId: '',
        facGNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        originCountry: ''
      }
    }
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'copGNo', title: '备案料件料号'
        },
        {
          key: 'gno', title: '备案料件序号'
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        }
      ]
    }
  }
}
