<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
        </FormBuilder>
      </div>
    </XdoCard>
    <div class="action mrp-toolbar" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        <template v-slot:import>
          <Dropdown trigger="click">
            <XdoButton type="text" style="font-size: 12px; width: 95px;">
              <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
            </XdoButton>
            <DropdownMenu slot="list">
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                  <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                </XdoButton>
              </DropdownItem>
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                  <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                </XdoButton>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </template>
      </xdo-toolbar>
      <div style="font-size: 12px">{{editOption.data.priceMark}}</div>
    </div>
    <XdoCard>
      <xdo-ag-grid class="dc-table" ref="table" checkboxSelection :columns="tableColumns" :data="tableData" :rowClassRules="rowClassRules"
        :options="gridOptions" rowSelection='single' @selectionChanged="handleSelectionChange" :height="dynamicHeight"></xdo-ag-grid>
      <div ref="area_page" style="height: 26px; overflow: hidden">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        <span style="font-size:12px; position: relative; top: -22px; float: right; margin-right: 90px">{{totalContent}}</span>
      </div>
    </XdoCard>
    <Modal v-model="showPrice" width="420" class-name="vertical-center-modal" title="试算" :closable="false" :mask-closable="false">
      <FormBuilder ref="headSearch" v-show="showPrice" :schema="priceSchema" :items="priceElements" :model="priceParam">
        <template v-slot:declareDateFrom>
          <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
        </template>
      </FormBuilder>
      <div slot="footer">
        <Button type="text"  @click="showPrice = false">取消</Button>
        <Button type="primary" @click="handleConfirm">确定</Button>
      </div>
    </Modal>
    <Modal v-model="showFetchPrice" width="420" class-name="vertical-center-modal" title="获取单价" :closable="false" :mask-closable="false">
      <mrp-price-fetch ref="fetchPrice" v-if="showFetchPrice" v-model="fetchPriceParam"></mrp-price-fetch>
      <div slot="footer">
        <Button type="text"  @click="showFetchPrice = false">取消</Button>
        <Button type="primary" :loading="fetchPriceLoading" @click="handleFetchConfirm">确定</Button>
      </div>
    </Modal>
    <Modal v-model="showSplit" width="420" class-name="vertical-center-modal" title="拆分" :closable="false" :mask-closable="false">
      <MrpPriceSplit v-if="showSplit" ref="split" :qty="selectedRow.adjustmentQty"></MrpPriceSplit>
      <div slot="footer">
        <Button type="text"  @click="showSplit = false">取消</Button>
        <Button type="primary" @click="handleConfirmSplit">确定</Button>
      </div>
    </Modal>
    <Modal v-model="showCalcWarning" width="920" class-name="vertical-center-modal" title="校验核注清单运保费异常提醒" :closable="false" :mask-closable="false">
      <MrpPriceCalcWarning v-if="showCalcWarning" ref="calcWarning" :tableColumns="calcWarningColumns" :calcWarningData="calcWarningData"></MrpPriceCalcWarning>
      <div slot="footer">
        <Button type="primary" @click="handleExportCalcWarning">导出清单运保费数据</Button>
        <Button type="text" @click="showCalcWarning = false">关闭</Button>
      </div>
    </Modal>
    <MrpPriceCalcRange v-model="showCalcRange" ref="calcWarning" :dataSource="calcRangeData"></MrpPriceCalcRange>
    <DcImport v-model="importShow" :config="importConfig" :bizParam="importParam" :startRow="5" @importSuccess="importSuccess"></DcImport>
  </section>
</template>
<script src="./mrp_price.js"></script>
<style scoped>
  .mrp-toolbar {
    display: flex;
    justify-content:space-between;
    align-items: center;
    background-color: #fff;
    padding-right: 20px;
  }

  /deep/ .data-split {
    background-color: #ff9900 !important;
  }
</style>
