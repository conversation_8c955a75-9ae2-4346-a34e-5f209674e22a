import { csAPI } from '@/api'
import { roundUp } from '@/libs/num'
import { copDecPriceComponent } from '@/view/cs-mrp/common/operation_renderer'

export default {
  data () {
    return {
      tableColumns: [
        {
          title: '企业料件料号',
          width: 160,
          key: 'facGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料件料号',
          width: 160,
          key: 'copGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料件序号',
          width: 120,
          key: 'gno',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '料件商品名称',
          width: 220,
          key: 'gname',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '汇总数量',
          width: 120,
          key: 'qty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '调整后数量',
          width: 120,
          key: 'adjustmentQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '企业料号单价（USD）',
          width: 190,
          key: 'usdFacDecPrice',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 5); }
        },
        {
          title: '备案料号单价（USD）',
          width: 190,
          key: 'usdDecPrice',
          sortable: true,
          cellRendererFramework: copDecPriceComponent(this.openCalcDetail),
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 5); }
        },
        {
          title: '企业料号单价（RMB）',
          width: 190,
          key: 'rmbFacDecPrice',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 6); }
        },
        {
          title: '备案料号单价（RMB）',
          width: 190,
          key: 'rmbDecPrice',
          sortable: true,
          cellRendererFramework: copDecPriceComponent(this.openCalcDetail),
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 6); }
        },
        {
          title: '最终审核单价（USD）',
          width: 180,
          key: 'usdApprDecPrice',
          sortable: true,
          editable: this.editOption.data.status === '0' || this.editOption.data.status === '1',
          filter: "agNumberColumnFilter",
          onCellValueChanged: this.onCellValueChanged,
          valueFormatter: ({value}) => {
            if (!value) {
              return value
            }
            let val = value;
            if (typeof value !== 'number') {
              val = Number(value)
            }
            return val && roundUp(val, 5);
          }
        },
        {
          title: '原产地证号',
          width: 120,
          key: 'certificateNo',
          editable: this.editOption.data.status === '0' || this.editOption.data.status === '1',
          onCellValueChanged: this.onCertificateNoChanged
        },
        {
          title: '总价（USD）',
          width: 120,
          key: 'usdDecTotal',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 2); }
        },
        {
          title: '总价（RMB）',
          width: 160,
          key: 'rmbDecTotal',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 6); }
        },
        {
          title: '原产国(地区)',
          width: 100,
          key: 'originCountry',
          sortable: true,
          filter: "agTextColumnFilter",
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('COUNTRY_OUTDATED', value)}` : '' }
        },
      ],
      modifyExportColumns: [
        {
          title: '企业料件料号',
          width: 160,
          key: 'facGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料件料号',
          width: 160,
          key: 'copGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料件序号',
          width: 120,
          key: 'gno',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '料件商品名称',
          width: 220,
          key: 'gname',
          sortable: true,
          filter: "agTextColumnFilter"
        }, {
          title: '企业料号单价（USD）',
          width: 190,
          key: 'usdFacDecPrice',
          sortable: true,
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 5); }
        },
        {
          title: '备案料号单价（USD）',
          width: 140,
          key: 'usdDecPrice',
          sortable: true,
          cellRendererFramework: copDecPriceComponent(this.openCalcDetail),
          filter: "agNumberColumnFilter",
          valueFormatter: ({value}) => { return value && roundUp(value, 5); }
        },
         {
          title: '最终审核单价（USD）',
          width: 180,
          key: 'usdApprDecPrice',
          sortable: true,
          editable: this.editOption.data.status === '0' || this.editOption.data.status === '1',
          filter: "agNumberColumnFilter",
          onCellValueChanged: this.onCellValueChanged,
          valueFormatter: ({value}) => {
            if (!value) {
              return value
            }
            let val = value;
            if (typeof value !== 'number') {
              val = Number(value)
            }
            return val && roundUp(val, 5);
          }
        },
        {
          title: '原产地证号',
          width: 120,
          key: 'certificateNo',
          editable: this.editOption.data.status === '0' || this.editOption.data.status === '1',
          onCellValueChanged: this.onCertificateNoChanged
        }, {
          title: '原产国(地区)',
          width: 100,
          key: 'originCountry',
          sortable: true,
          filter: "agTextColumnFilter",
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('COUNTRY_OUTDATED', value)}` : '' }
        }],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      gridOptions: {
      },
      tableData: [],
      tableSelectedRows: [],
      totalContent: '',
      calcRangeData: []
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    },
    handleSelectionChange (param) {
      this.tableSelectedRows = param.api.getSelectedRows()
    },
    onCellValueChanged (event) {
      if (!event.data.usdApprDecPrice || !/^\d*\.?\d*$/.test(event.data.usdApprDecPrice.toString())) {
        this.$Message.warning('请输入有效的数据')
        return
      }
      const url = `${csAPI.mrp.price.rest}/${event.data.sid}/usdApprDecPrice`
      this.$http.put(url, event.data)
        .then(() => {
          this.doQuery()
        }, () => {})
    },
    onCertificateNoChanged (event) {
      const url = `${csAPI.mrp.price.rest}/${event.data.sid}/certificateNo`
      this.$http.put(url, event.data)
        .then(() => {}, () => {})
    },
    openCalcDetail (params) {
      const url = `${csAPI.mrp.price.rest}/calc/range/${params.data.headId}/${params.data.copGNo}`
      this.$http.get(url)
        .then(res => {
          this.calcRangeData = res.data.data
          console.log(res)
          this.showCalcRange = true
        }, () => {})
    }
  }
}

