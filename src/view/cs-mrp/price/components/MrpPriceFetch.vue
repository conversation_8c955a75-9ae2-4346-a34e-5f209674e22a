<template>
  <section>
    <XdoForm ref="editForm" :model="formModel" :label-width="120" :rules="formRules">
      <XdoFormItem label="备案号" prop="emsNo">
        <XdoIInput type="text" v-model="formModel.emsNo" disabled></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="剩余数量">
        <XdoRadio v-model="formModel.emlEmsStage">手账册级</XdoRadio>
        <XdoCheckbox v-model="formModel.dcrStage" @on-change="dcrStageChange" :disabled="disableEml">报核级</XdoCheckbox>
      </XdoFormItem>
      <XdoFormItem prop="cavMark" label="核销标志">
        <xdo-select v-model="formModel.cavMark" :disabled="!formModel.dcrStage" :options="cavMarks" dataValue="cavMark" :optionLabelRender="(it) => it.cavMark"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="priceType" label="补税单价类型">
        <xdo-select v-model="formModel.priceType" :options="priceTypeOptions" :optionLabelRender="(it) => it.label"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>
<script>
import { priceTypeOptions } from '../../common/mrp_constant'
import { csAPI } from '@/api'

export default {
  name: 'MrpPriceFetch',
  props: {
    value: {
      type: Object, required: true
    }
  },
  mounted () {
    Object.assign(this.formModel, this.value)
    if (!this.disableEml) {
      this.loadCavMark()
    }
  },
  data () {
    return {
      priceTypeOptions: priceTypeOptions,
      cavMarks: [],
      formModel: {
        headId: '',
        emsNo: '',
        emlEmsStage: true,
        dcrStage: false,
        cavMark: '',
        priceType: 'min'
      }
    }
  },
  methods: {
    loadCavMark () {
      this.$http.get(csAPI.mrp.price.cav + '/' + this.formModel.headId).then(res => {
        this.cavMarks = res.data.data
      }, () => {});
    },
    dcrStageChange (val) {
      if (!val) {
        this.formModel.cavMark = ''
      }
    }
  },
  computed: {
    disableEml () {
      return this.formModel.emsNo && (this.formModel.emsNo.startsWith("B") || this.formModel.emsNo.startsWith("C"))
    }
  }
}
</script>
