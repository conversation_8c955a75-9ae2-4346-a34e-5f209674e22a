<template>
  <Modal v-model="value" width="920" class-name="vertical-center-modal" title="单价计算核注清单范围" :closable="false" :mask-closable="false">
    <xdo-ag-grid class="dc-table" ref="table" :columns="columns" :data="dataSource" :options="gridOptions" :height="480"></xdo-ag-grid>
    <div slot="footer">
      <Button type="primary" @click="handleExportExcel">导出</Button>
      <Button type="text" @click="handleClose">关闭</Button>
    </div>
  </Modal>
</template>
<script>
import { export_array_to_excel } from '@/libs/excel'

export default {
  name: 'MrpPriceCalcRange',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    dataSource: {
      type: Array
    }
  },
  data () {
    return {
      columns: [
        {
          title: '核注清单编号',
          width: 160,
          key: 'listNo'
        },
        {
          title: '报关单号',
          width: 160,
          key: 'entryNo'
        },
        {
          title: '清单申报日期',
          width: 160,
          key: 'declareDate'
        },
        {
          title: '监管方式',
          width: 160,
          key: 'tradeMode',
        },
        {
          title: '成交方式',
          width: 160,
          key: 'transMode',
        },
        {
          title: '运费',
          width: 160,
          key: 'fee',
        },
        {
          title: '保费',
          width: 160,
          key: 'insur',
        },
        {
          title: '杂费',
          width: 160,
          key: 'other',
        },
        {
          title: '备案料号',
          width: 160,
          key: 'copGNo',
        },
        {
          title: '商品编码',
          width: 160,
          key: 'codeTS',
        },
        {
          title: '商品名称',
          width: 160,
          key: 'gname',
        },
        {
          title: '申报数量',
          width: 160,
          key: 'qty',
        },
        {
          title: '计量单位',
          width: 160,
          key: 'unit',
        },
        {
          title: '原产国',
          width: 160,
          key: 'originCountry',
        },
        {
          title: '申报单价',
          width: 160,
          key: 'decPrice',
        },
        {
          title: '申报总价',
          width: 160,
          key: 'decTotal',
        },
        {
          title: '币制',
          width: 160,
          key: 'curr',
        },
        {
          title: '汇率',
          width: 160,
          key: 'exchangeRate',
        },
      ],
      gridOptions: {
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    handleExportExcel() {
      export_array_to_excel({key: this.columns.map(it => it.key), data: this.dataSource, title: this.columns.map(it => it.title), filename: "单价计算范围", autoWidth: true })
      this.$Message.success('导出成功')
    }
  }
}
</script>
<style scoped>
</style>
