<template>
  <section>
    <XdoForm ref="editForm" :model="formModel" :label-width="80" :rules="formRules" class="mrp-price-split-form">
      <XdoFormItem label="总数量">
        <XdoIInput type="text" v-model="qty" disabled></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="剩余数量">
        <XdoIInput type="text" v-model="remainingQty" disabled></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="拆分数量" prop="splitQty">
        <xdo-input type="text" v-model="formModel.splitQty" decimal int-length="10" precision="5" notConvertNumber></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="originCountry" label="原产国">
        <xdo-select v-model="formModel.originCountry" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="certificateNo" label="原产地证号">
        <XdoIInput type="text" v-model="formModel.certificateNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { keepDecimal } from '@/libs/util'

  export default {
    name: 'MrpPriceSplit',
    props: {
      qty: {
        type: Number
      }
    },
    data() {
      return {
        formModel: {
          splitQty: '',
          originCountry: '',
          certificateNo: ''
        },
        formRules: {
          splitQty: [{required: true, message: '不能为空!', trigger: 'blur'}],
          originCountry: [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    methods: {
      getSplitParam() {
        return this.$refs['editForm'].validate().then(isValid => {
          if (isValid) {
            if (this.formModel.splitQty <= 0) {
              return Promise.reject({message: '拆分数量必须大于0'})
            }
            if (this.formModel.splitQty >= this.qty) {
              return Promise.reject({message: '拆分数量要小于被拆分的数量'})
            }

            return Promise.resolve({
              qty: this.formModel.splitQty,
              originCountry: this.formModel.originCountry,
              certificateNo: this.formModel.certificateNo
            })
          } else {
            return Promise.reject()
          }
        })
      }
    },
    computed: {
      remainingQty() {
        let me = this
        return keepDecimal(me.qty - me.formModel.splitQty, 5)
      }
    }
  }
</script>

<style scoped>
  .mrp-price-split-form {
    display: grid;
    grid-column-gap: 2px;
    grid-template-columns: repeat(2, 1fr);
  }
</style>
