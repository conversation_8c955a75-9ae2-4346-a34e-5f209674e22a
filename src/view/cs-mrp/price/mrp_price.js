import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_price_search'
import tableMixin from './mrp_price_table'
import updateEditDataMixin from '../common/update_edit_data_mixin'
import dcImportMixin from '@/mixin/dc_import_mixin'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'
import MrpPriceSplit from './components/MrpPriceSplit.vue'
import MrpPriceCalcWarning from './components/MrpPriceCalcWarning.vue'
import MrpPriceCalcRange from '@/view/cs-mrp/price/components/MrpPriceCalcRange'
import MrpPriceFetch from './components/MrpPriceFetch.vue'

import excel from '@/libs/excel'
import { priceTypeOptions, exchangeRateTypeOptions } from '../common/mrp_constant'

export default {
  name: 'Mrp<PERSON>rice',
  mixins:[dynamicHeight, pmsMixin, searchMixin, tableMixin, updateEditDataMixin, dcImportMixin],
  components: { MrpPriceSplit, MrpPriceCalcWarning, MrpPriceFetch, MrpPriceCalcRange },
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      ajaxUrl: {
        exportPrice: csAPI.mrp.price.exportPrice,
      },
      toolbarEventMap: {
        start: this.handleStart,
        'fetch': this.handleFetch,
        export: this.handleExport,
        import: this.handleImport,
        split: this.handleSplit,
        'split-restore': this.handleSplitRestore
      },
      priceSchema: {
        class: ' ',
        titleWidth: 120
      },
      scopeOptions: [
        { label: '按申报日期', value: 0 }
      ],
      priceParam: {
        emsNo: '',
        headId: '',
        scope: 0,
        declareDateFrom: '',
        declareDateTo: '',
        priceType: 'max',
        exchangeRateType: 'declareDate'
      },
      showPrice: false,
      fetchPriceParam: {
        emsNo: '',
        headId: '',
        emlEmsStage: true,
        dcrStage: false,
        cavMark: '',
        priceType: 'max'
      },
      showFetchPrice: false,
      fetchPriceLoading: false,
      showSplit: false,
      importConfig: {
        url: csAPI.mrp.price.rest + "/import",
        tplUrl: '',
        correctUrl: csAPI.mrp.price.rest + "/import",
        errorUrl: csAPI.mrp.price.rest + "/export"
      },
      importParam: {
        headId: ''
      },
      rowClassRules: {
        'data-split': 'data.splitId && data.splitSerialNo === 1'
      },
      showCalcWarning: false,
      showCalcRange: false,
      calcWarningData: [],
      calcWarningColumns: [
        {
          title: '备案号',
          width: 160,
          key: 'emsNo'
        },
        {
          title: '清单内部编号',
          width: 160,
          key: 'emsListNo'
        },
        {
          title: '核注清单编号',
          width: 160,
          key: 'listNo'
        },
        {
          title: '报关单统一编号',
          width: 160,
          key: 'seqNo'
        },
        {
          title: '报关单号',
          width: 160,
          key: 'entryNo'
        },
        {
          title: '清单申报日期',
          width: 160,
          key: 'declareDate'
        },
        {
          title: '提运单号',
          width: 160,
          key: 'hawb'
        },
        {
          title: '成交方式',
          key: 'transMode',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('TRANSAC', value)}` : '' }
        },
        {
          title: '运费',
          width: 160,
          key: 'feeMark',
          valueFormatter: ({data}) => { return `${data.feeCurr || '空'}/${data.feeRate || '空'}/${data.feeMark || '空'}` }
        },
        {
          title: '保费',
          width: 160,
          key: 'insurMark',
          valueFormatter: ({data}) => { return `${data.insurCurr || '空'}/${data.insurRate || '空'}/${data.insurMark || '空'}` }
        },
        {
          title: '杂费',
          width: 160,
          key: 'otherMark',
          valueFormatter: ({data}) => { return `${data.otherCurr || '空'}/${data.otherRate || '空'}/${data.otherMark || '空'}` }
        }
      ]
    }
  },
  mounted () {
    this.priceParam.emsNo = this.editOption.data.emsNo
    this.priceParam.headId = this.editOption.data.sid
    this.fetchPriceParam.emsNo = this.editOption.data.emsNo
    this.fetchPriceParam.headId = this.editOption.data.sid
    this.searchParam.headId = this.editOption.data.sid
    this.importParam.headId = this.editOption.data.sid
    this.loadFunctions('price', 160)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },
    handleStart () {
      this.showPrice = true
    },
    handleConfirm () {
      this.showPrice = false
      this.start()
    },
    handleConfirmSplit () {
      if (this.tableSelectedRows.length !== 1) {
        this.$Message.warning('选择的数据发生变化，请关闭拆分页面重新选择数据!')
        return
      }
      this.$refs['split'].getSplitParam().then(res => {
        const param = Object.assign({}, res)
        param.sid = this.tableSelectedRows[0].sid
        this.setToolbarLoading('split', true)
        this.$http.post(csAPI.mrp.price.split, param).then(() => {
          this.$Message.success('拆分成功')
          this.showSplit = false
          this.doQuery()
        }, () => {}).finally(() => {
          this.setToolbarLoading('split', false)
        })
      }, (err) => {
        if (err && err.message) {
          this.$Message.error(err.message)
        }

      })
    },
    handleExportCalcWarning () {
      const params = {
        title: this.calcWarningColumns.map(item => {
          if (item.title) {
            return item.title
          }
        }),
        key: this.calcWarningColumns.map(item => {
          if (item.key) {
            return item.key
          }
        }),
        data: this.calcWarningData.map(it => {
          return {...it,
            transMode: it.transMode ? `${it.transMode} ${this.pcodeGet('TRANSAC', it.transMode)}` : '',
            feeMark: `${it.feeCurr || '空'}/${it.feeRate || '空'}/${it.feeMark || '空'}`,
            insurMark: `${it.insurCurr || '空'}/${it.insurRate || '空'}/${it.insurMark || '空'}`,
            otherMark: `${it.otherCurr || '空'}/${it.otherRate || '空'}/${it.otherMark || '空'}`
          }
        }),

        autoWidth: true,
        filename: '校验核注清单运保费校验异常数据'
      }
      excel.export_array_to_excel(params)
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '单价计算',
        header: getGridExportColumns([{key: 'sid', title: 'sid'},...this.tableColumns])
      }

      excelExport(csAPI.mrp.price.export, param)
    },
    loadStat () {
    },
    loadData () {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.price.list, this.searchParam, { params: pageParam })
        .then(res => {
          this.tableData = res.data.data || []
          this.pageParam.dataTotal = res.data.total
        }, () => {})
        .finally(() => {
          this.tableSelectedRows = []
        })
    },
    start () {
      this.setToolbarLoading('start', true)
      this.$http.post(csAPI.mrp.price.start, this.priceParam)
        .then(res => {
          this.calcWarningData = res.data.data
          this.showCalcWarning = this.calcWarningData && this.calcWarningData.length > 0
          this.doQuery()
          this.updateEditOption()
        }, () => {})
        .finally(() => {
          this.setToolbarLoading('start')
        })
    },
    handleDateChange (values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.priceParam, "declareDateFrom", values[0])
        this.$set(this.priceParam, "declareDateTo", values[1])
      } else {
        this.$set(this.priceParam, "declareDateFrom", '')
        this.$set(this.priceParam, "declareDateTo", '')
      }
    },
    handleSplit () {
      if (this.tableSelectedRows.length !== 1) {
        this.$Message.warning('请选择一行数据进行拆分!')
        return
      }
      this.showSplit = true
    },
    handleSplitRestore () {
      let sid = ''
      if (this.tableSelectedRows.length > 0) {
        const item = this.tableSelectedRows[0]
        if (!item.splitId) {
          this.$Message.warning('非拆分数据，无需还原')
          return;
        }
        sid = item.sid
      }

      this.$Modal.confirm({
        title: '提醒',
        content: (sid) ? '将还原本料号已拆分的数据' : '将还原所有拆分的数据',
        okText: '还原',
        cancelText: '取消',
        onOk: () => {
          this.setToolbarLoading('split-restore', true)
          this.$http.delete(`${csAPI.mrp.price.split}/${this.editOption.data.sid}/${sid}`).then(() => {
            this.$Message.success('还原成功')
            this.doQuery()
          }, () => {})
          .finally(() => {
            this.setToolbarLoading('split-restore', false)
          })
        },
      })
    },
    importSuccess () {
      this.doQuery()
    },
    handleFetch () {
      this.showFetchPrice = true
    },
    handleFetchConfirm () {
      const param = Object.assign({}, this.$refs.fetchPrice.formModel)
      this.fetchPriceLoading = true
      this.$http.post(csAPI.mrp.price.cavFetch, param)
        .then(() => {
          this.showFetchPrice = false
          this.doQuery()
          this.updateEditOption()
        }, () => {})
        .finally(() => {
          this.fetchPriceLoading = false
        })
    },
    /**
     * 下载修改数据
     */
    downLoadModify() {
      let me = this
      me.beforeDownload()
      me.doExport(me.ajaxUrl.exportPrice, me.actions.findIndex(it => it.command === 'import'))
    },
    /**
     * 上传批量修改数据
     */
    uploadModify() {
      let me = this
      me.$set(me, 'importShow', true)
    },
    afterImport() {
      let me = this
      me.$set(me, 'importShow', false)
      me.getList()
    },
    /**
     * 下载前操作
     * @param type
     */
    beforeDownload() {
      // let me = this
      // me.$set(me.gridConfig, 'exportColumns', me.modifyExportColumns)
      // me.$set( getGridExportColumns(me.modifyExportColumns))
      const param = {
        exportColumns: this.searchParam,
        name: '单价计算',
        header: getGridExportColumns([{key: 'sid', title: 'sid'},...this.modifyExportColumns])
      }
      excelExport(csAPI.mrp.price.exportPrice, param)
    }
  },
  computed: {
    selectedRow () {
      if (this.tableSelectedRows && this.tableSelectedRows.length === 1) {
        return this.tableSelectedRows[0]
      }
      return {}
    },
    priceElements () {
      return [
        {
          key: 'emsNo', title: '备案号',
          props: {
            disabled: true
          }
        },
        {
          type:"select", key:"scope", title: '单价计算范围',
          props: { options: this.scopeOptions }
        },
        {
          type:'dateRange',title: '申报日期', key: 'declareDateFrom',
          fields: [{key: 'declareDateFrom'}, {key: 'declareDateTo'}]
        },
        {
          type:"select", key: 'priceType', title: '补税单价类型',
          props: { options: priceTypeOptions,  optionLabelRender: (it) => it.label }
        },
        {
          type:"select", key: 'exchangeRateType', title: '备案单价汇率取值',
          props: { options: exchangeRateTypeOptions,  optionLabelRender: (it) => it.label }
        }
      ]
    }
  }

}
