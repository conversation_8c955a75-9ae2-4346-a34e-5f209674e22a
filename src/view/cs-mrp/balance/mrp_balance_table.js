export default {
  data () {
    return {
      tableColumns: [
        {
          title: '备案号',
          width: 160,
          key: 'emsNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案料号',
          width: 160,
          key: 'copGNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '备案序号',
          width: 120,
          key: 'gno',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '料件商品名称',
          width: 220,
          key: 'gname',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '余料结转数量',
          width: 160,
          key: 'ylImpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '直接进口数量',
          width: 160,
          key: 'impQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '深加工进口数量',
          width: 160,
          key: 'deepImpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件退换(进口)数量',
          width: 160,
          key: 'ljthImpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件内销',
          width: 160,
          key: 'mrpImpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件复出数量',
          width: 160,
          key: 'ljfcExpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '料件退换(出口)数量',
          width: 160,
          key: 'ljthExpQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        },
        {
          title: '可内销数量',
          width: 160,
          key: 'sumQty',
          sortable: true,
          filter: "agNumberColumnFilter"
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      gridOptions: {}
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}

