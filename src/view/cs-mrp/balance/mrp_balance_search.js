export default {
  data () {
    return {
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      emsNoList: [],
      searchParam: {
        headId: '',
        emsNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        originCountry: ''
      }
    }
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.loadData()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          type:"select", key:"emsNo", title: '备案号',
          props: { options: this.emsNoList, optionLabelRender: (it) => it  }
        },
        {
          key: 'copGNo', title: '备案料号'
        },
        {
          key: 'gno', title: '备案序号'
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        }
      ]
    }
  }
}
