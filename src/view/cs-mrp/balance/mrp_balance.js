import searchMixin from './mrp_balance_search'
import tableMixin from './mrp_balance_table'
import { dynamicHeight } from '@/common'
import { commonAPI, csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'

export default {
  name: 'Mrp<PERSON><PERSON><PERSON>',
  mixins:[dynamicHeight, searchMixin, tableMixin],
  data () {
    return {
      actions: [
        { type: 'text', disabled: false, command: "export", icon: 'ios-cloud-download-outline', label: '导出', key:'xdo-btn-download', loading: false, needed: true }
      ],
      toolbarEventMap: {
        export: this.handleExport
      }
    }
  },
  mounted () {
    this.refreshDynamicHeight(126, !this.showSearch ? ['area_search'] : null)
    this.loadData()
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.map(it => it.VALUE)
        }
      )
  },
  methods: {
    handleToolbarClick(command) {
      if (this.toolbarEventMap[command] && typeof this.toolbarEventMap[command === 'function']) {
        this.toolbarEventMap[command]()
      } else {
        console.warn(`${command}没有对应的处理方法`)
      }
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.balance.list, this.searchParam, { params: pageParam })
        .then(res => {
          this.tableData = res.data.data || []
          this.pageParam.dataTotal = res.data.total
        }, () => {})
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '内销平衡检查',
        header: getGridExportColumns(this.tableColumns)
      }

      excelExport(csAPI.mrp.balance.export, param)
    }
  }
}
