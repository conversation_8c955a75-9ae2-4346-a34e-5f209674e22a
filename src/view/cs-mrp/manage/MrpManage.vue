<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
            <template v-slot:startDate>
              <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
            </template>
          </FormBuilder>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard>
        <xdo-ag-grid class="dc-table" ref="table" checkboxSelection rowSelection='multiple' :columns="tableColumns" :data="tableData"
                     :height="dynamicHeight"  @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
                @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>

    <MrpManageTab @onEditBack="editBack" v-if="!showList" :editOption="editConfig"></MrpManageTab>
    <Modal v-model="showGenerateEntry" width="620" class-name="vertical-center-modal" title="报关生成" :closable="false" :mask-closable="false">
      <MrpGenerateEntryPop v-if="showGenerateEntry" ref="generateEntry"></MrpGenerateEntryPop>
      <div v-if="!matchDone" style="color: #ed4014; font-size: 12px; margin-top: 16px">存在未匹配或未完全匹配到核注清单的料号，无法报关生成，请返回内销物料维护修改</div>
      <div slot="footer">
        <Button type="text" @click="showGenerateEntry = false">取消</Button>
        <Button type="primary" :loading="generateLoading" :disabled="!matchDone" @click="handleConfirmGenerateEntry">确定</Button>
      </div>
    </Modal>
    <MrpBillDcrFetchPop v-model="showSyncDcrTimes" :emsNoList="emsNoList"></MrpBillDcrFetchPop>

  </section>
</template>
<script src="./mrp_manage.js"></script>

