import { productClassify } from '@/view/cs-common'

export default {
  data () {
    return {
      searchSchema: {
        titleWidth: 80
      },
      statusList: [
        { label: '暂存', value: '0' },
        { label: '试算完成', value: '1' },
        { label: '报关生成', value: '2' },
        { label: '报关完成', value: '3' }
      ],
      showSearch: false,
      emsNoList: [],
      searchParam: {
        emsNo: '',
        status: '',
        batchNo: '',
        startDate: '',
        endDate: '',
        gmark: ''
      }
    }
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(112, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.pageParam.page = 1
      this.loadData()
    },
    handleDateChange (values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.searchParam, "startDate", values[0])
        this.$set(this.searchParam, "endDate", values[1])
      } else {
        this.$set(this.searchParam, "startDate", '')
        this.$set(this.searchParam, "endDate", '')
      }
    }
  },
  computed: {
    searchElements () {
      return [
        {
          type:"select", key:"emsNo", title: '备案号',
          props: { options: this.emsNoList, optionLabelRender: (it) => it  }
        },
        {
          type:"select", key:"status", title: '状态',
          props: { options: this.statusList }
        },
        {
          key: 'batchNo', title: '批次号'
        },
        {
          type: 'pcode', title: '监管方式', key: 'tradeMode',
          props: {
            meta: 'TRADE_OUTDATED'
          }
        },
        { type:'dateRange',title: '内销周期', key: 'startDate',
          fields: [
            {
              key: 'startDate',
              options: {
                disabledDate: date => {
                  if (this.searchParam.endDate === '') {
                    return ''
                  }
                  return date && (date.valueOf() < this.searchParam.endDate)
                }
              }
            },
            {
              key: 'endDate',
              options: {
                disabledDate: date => {
                  if (this.searchParam.startDate === '') {
                    return ''
                  }
                  return date && (date.valueOf() < this.searchParam.startDate)
                }
              }
            }
          ]
        },
        {
          type:"select", key: 'gmark', title: '物料类型',
          props: { options: productClassify.GMARK_MRP_SELECT }
        }
      ]
    }
  }
}
