<template>
  <section v-focus>
    <FormBuilder class="form-builder" ref="editForm" :disabled="editDisabled" :schema="editSchema" :rules="formRules" :items="editElements" :model="editModel"></FormBuilder>
    <div class="xdo-enter-action" style="text-align: center;margin-top:10px">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :key="item.label">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>
<script>
import formMixin from './mrp_manage_edit_form'
import { csAPI } from '@/api'
import { commonAPI } from '@/api'
export default {
  name: 'MrpManageEdit',
  mixins: [formMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      editBackOption: {
        showList: true,
        updated: false
      }
    }
  },
  mounted () {

  },
  methods: {
    handleSave () {
      this.doSave(() => {
        this.editBackOption.showList = false
        this.editBackOption.updated = true
        this.$emit('onEditBack', this.editBackOption, this.editOption)
      })
    },
    handleSaveClose (){
      this.doSave(() => {
        this.editBackOption.updated = true
        this.editBack()
      })
    },
    handleBack () {
      this.editBack()
    },
    editBack() {
      this.editBackOption.showList = true
      this.$emit('onEditBack', this.editBackOption)
    },
    doSave(callback) {
      this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          let http = null
          const param = Object.assign({}, this.editModel)
          param.gmark = Array.isArray(this.editModel.gmark) ? this.editModel.gmark.join(',') : this.editModel.gmark
          if (this.editModel.sid) {
            http = this.$http.put(`${csAPI.mrp.manage.rest}/${this.editModel.sid}`, param)
          } else {
            http = this.$http.post(csAPI.mrp.manage.rest, param)
          }
          http.then(res => {
            this.editModel.sid = res.data.data.sid
            this.editOption.data = res.data.data
            this.editOption.status = commonAPI.EDIT
            this.$Message.success("保存成功")
            if (callback) {
              callback(res)
            }
          }, () => {})
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .form-builder {
    background: white;
    padding-top: 10px;
  }

  /deep/ .dc-select-multiple .dc-select-input {
    height: 22px;
  }
</style>
