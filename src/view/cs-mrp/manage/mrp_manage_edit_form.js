import { editStatus } from '@/view/cs-common'
import { commonAPI } from '@/api'
import { isEml } from '../common/mrp_constant'

export default {
  data () {
    return {
      editSchema: {
        titleWidth: 80,
        class: 'dc-form dc-form-3'
      },
      editModel: {
        sid: '',
        batchNo: '',
        startDate: '',
        endDate: '',
        emsNo: '',
        dcrTimes: '',
        gmark: '',
        note: ''
      },
      formRules: {
        batchNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
        emsNo:[{required: true, message: '不能为空！', trigger: 'blur'}]
      },
      emsNoList: []
    }
  },
  mounted() {
    if (this.editOption.status !== editStatus.ADD) {
      this.editModel = Object.assign({}, this.editOption.data)
      this.editModel.dcrTimes = this.editOption.data.dcrTimes ? this.editOption.data.dcrTimes + '' : ''
      this.editModel.gmark = this.editOption.data.gmark && this.editOption.data.gmark.split(',')
    }

    commonAPI.getEmsNoList().then(
      (data) => {
        this.emsNoList = data.map(it => it.VALUE)
      }
    )
  },
  watch: {
    'editModel.emsNo': {
      deep: true,
      handler: function () {
        if(isEml(this.editModel.emsNo)) {
          this.editModel.dcrTimes = ''
        }

        this.formRules = {
          batchNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
          emsNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
          dcrTimes: [{required: !(!this.editModel.emsNo || isEml(this.editModel.emsNo)), message: '不能为空！', trigger: 'blur'}]
        }
      }
    }
  },
  computed: {
    editDisabled () {
      return this.editOption.status === editStatus.SHOW
    },
    buttons () {
      return [
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSave, label: '保存' },
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSaveClose, label: '保存关闭' },
        { type: 'primary', disabled: false, loading: false, needed: true, click: this.handleBack, label: '返回' }
      ]
    },
    editElements () {
      return [
        {
          title: '批次号', key: 'batchNo',
          attrs: {
            maxlength: 30
          }
        },
        {
          type:'datePicker',title: '开始时间', key: 'startDate',
          props: {
            options: {
              disabledDate: date => {
                if (!this.editModel.endDate) {
                  return
                }
                return date && date.valueOf() > new Date(this.editModel.endDate).valueOf()
              }
            }
          }
        },
        {
          type:'datePicker',title: '结束时间', key: 'endDate',
          props: {
            options: {
              disabledDate: date => {
                if (!this.editModel.startDate) {
                  return
                }
                return date && date.valueOf() < new Date(this.editModel.startDate).valueOf()
              }
            }
          }
        },
        {
          type:"select", key:"emsNo", title: '备案号',
          props: { options: this.emsNoList, optionLabelRender: (it) => it, disabled: this.editOption.status !== editStatus.ADD }
        },
        {
          type: 'xdoInput', key: 'dcrTimes', title: '报核次数',
          props:{
            decimal: false, intLength: "5", precision: "0", notConvertNumber: true, disabled: !this.editModel.emsNo || isEml(this.editModel.emsNo)
          }
        },
        {
          title: '备注', key: 'note',
          props: {
            type: 'text'
          },
          attrs: {
            maxlength: 100,
          }
        }
      ]
    }
  }
}
