<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="mrpTab">
      <TabPane name="headTab" label="内销表头管理" tab="mrpTab">
        <MrpManageEdit :editOption="editOption" @onEditBack="editBack"></MrpManageEdit>
      </TabPane>
      <TabPane name="bodyTab" label="内销物料维护" v-if="showBody" tab="mrpTab">
        <MrpImgExg v-if="tabs.bodyTab" :editOption="editOption" @onEditBack="editBack"></MrpImgExg>
      </TabPane>
      <TabPane name="backTab" label="内销折料管理" v-if="showBody" tab="mrpTab">
        <MrpBack v-if="tabs.backTab" :editOption="editOption" @onEditBack="editBack"></MrpBack>
      </TabPane>
      <TabPane name="priceTab" label="单价计算" v-if="showBody" tab="mrpTab">
        <MrpPrice v-if="tabs.priceTab" :editOption="editOption"></MrpPrice>
      </TabPane>
      <TabPane name="matchingTab" label="匹配核注清单" v-if="showBody"  tab="mrpTab">
        <MrpMatch v-if="tabs.matchingTab"  :editOption="editOption" @onEditBack="editBack"></MrpMatch>
      </TabPane>
      <TabPane name="trialTab" label="内销税金试算" v-if="showBody"  tab="mrpTab">
        <MrpTrial v-if="tabs.trialTab"  :editOption="editOption" @onEditBack="editBack"></MrpTrial>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>
<script>

import { editStatus } from '@/view/cs-common'
import MrpManageEdit from './MrpManageEdit'
import MrpImgExg from '../imgexg/MrpImgExg'
import MrpPrice from  '../price/MrpPrice'
import MrpBack from '../return/MrpReturn'
import MrpMatch from '../match/MrpMatch'
import MrpTrial from '../trial/MrpTrial'

export default {
  name: 'MrpManageTag',
  components: { MrpManageEdit, MrpImgExg, MrpBack, MrpPrice, MrpMatch, MrpTrial },
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        backTab: false,
        priceTab: false,
        matchingTab: false,
        trialTab: false
      },
      showBody: false,
      editBackOption: {
        showList: true,
        updated: true
      }
    }
  },
  mounted () {
    this.showBody = this.editOption.status !== editStatus.ADD
  },
  watch: {
    tabName (value) {
      this.tabs[value] = true
    }
  },
  methods: {
    editBack(option, data) {
      this.$emit('onEditBack', option)
      if (data && !this.showBody) {
        this.editOption = data
        this.showBody = true
      }
    },
    backToList () {
      this.editBack(this.editBackOption)
    }
  }
}
</script>
