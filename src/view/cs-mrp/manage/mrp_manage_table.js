import { editStatus } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-mrp/common/operation_renderer'
import { productClassify } from '@/view/cs-common'
import { getKeyValue } from "@/libs/util"

export default {
  data () {
    return {
      basicColumn: [
        {
          title: '操作',
          width: 120,
          key: 'operation',
          cellRendererFramework: operationRenderer(this)
        },
        {
          title: '状态',
          width: 100,
          key: 'status',
          sortable: true,
          valueFormatter: ({value}) => {
            if (value) {
              return getKeyValue(this.statusList, value)
            }
            return ''
          }
        },
        {
          title: '批次号',
          minWidth: 120,
          key: 'batchNo',
          sortable: true,
          filter: "agTextColumnFilter"
        },
        {
          title: '开始时间',
          minWidth: 120,
          key: 'startDate',
          valueFormatter: ({value}) => {
            return value && value.substring(0, 10)
          }
        },
        {
          title: '结束时间',
          minWidth: 120,
          key: 'endDate',
          valueFormatter: ({value}) => {
            return value && value.substring(0, 10)
          }
        },
        {
          title: '物料类型',
          minWidth: 120,
          key: 'gmark',
          valueFormatter: ({value}) => {
            const list = value && value.split(',')
            return Array.isArray(list) ? list.map(it => getKeyValue(productClassify.GMARK_MRP_SELECT, it)).join(',') : ''
          }
        },
        {
          title: '备案号',
          width: 120,
          key: 'emsNo',
        },
        {
          title: "清单编号",
          width: 180,
          key: "listNo"
        },
        {
          title: "监管方式",
          width: 160,
          key: "tradeMode",
          valueFormatter: ({value}) => { return value ? `${value} ${ this.pcodeGet('TRADE_OUTDATED', value)} ` : '' }
        },
        {
          title: "报核次数",
          width: 80,
          key: "dcrTimes"
        },
        {
          title: '备注',
          width: 120,
          key: 'note',
        },
        {
          title: '制单人',
          width: 160,
          key: 'userName',
        },
        {
          title: '制单日期',
          width: 120,
          key: 'insertTime',
        }

      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        sort: [],
        pageSizeOpts: [10,20,30,40,50]
      },
      tableColumns: [],
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
