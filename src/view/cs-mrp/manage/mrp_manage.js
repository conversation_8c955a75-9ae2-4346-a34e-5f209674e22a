import tableMixin from './mrp_manage_table'
import searchMixin from './mrp_manage_search'
import pmsMixin from '@/libs/pms'
import debounceMixin from '@/mixin/debounce'
import { dynamicHeight } from '@/common'
import { csAPI, commonAPI } from '@/api'
import { editStatus } from '@/view/cs-common'
import { matchStat } from '@/view/cs-mrp/common/mrp_api'

import MrpManageTab from './MrpManageTab'
import MrpGenerateEntryPop from '../component/MrpGenerateEntryPop'
import MrpBillDcrFetchPop from '../component/MrpBillDcrFetchPop.vue'

export default {
  name: 'MrpManage',
  mixins:[dynamicHeight, searchMixin, tableMixin, pmsMixin, debounceMixin],
  components: { MrpManageTab, MrpGenerateEntryPop, MrpBillDcrFetchPop },
  data () {
    return {
      showList: true,
      showGenerateEntry: false,
      matchDone: true,
      generateLoading: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'generate-entry': this.handleGenerateEntry,
        'fetch-dcr': this.handleFetchDcr,
      },
      editConfig: {
        status: editStatus.ADD,
        data: null,
      },
      showSyncDcrTimes: false
    }
  },
  mounted () {
    this.tableColumns = this.basicColumn
    this.loadFunctions('default', 125)
    this.loadData()
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.map(it => it.VALUE)
        }
      )
  },
  beforeDestroy () {
  },
  methods: {
    handleSelectionChange (param) {
      this.tableSelectedRows = param.api.getSelectedRows()
    },
    editBack (option) {
      this.showList = option.showList
      if (option.updated) {
        this.loadData()
      }
    },
    handleAdd () {
      this.editConfig.data = null
      this.editConfig.status = editStatus.ADD
      this.showList = !this.showList
    },
    handleEdit () {
      if (this.tableSelectedRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要编辑的数据!')
        return
      } else if (this.tableSelectedRows.length > 1) {
        this.$Message.warning('一次仅能编辑一条数据！')
        return
      }
      this.handleEditByRow(this.tableSelectedRows[0])
    },
    handleDelete () {
      if (this.tableSelectedRows.length > 0) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.setToolbarLoading('delete', true)
            const sids = this.tableSelectedRows.map(item => item.sid).join(',')
            this.$http.delete(`${csAPI.mrp.manage.batch}/${sids}`).then(() => {
              this.$Message.success('删除成功！')
              this.tableSelectedRows = []
              this.loadData()
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('delete')
            })
          },
          onCancel: () => {
          }
        })
      } else {
        this.$Message.warning('请选择要删除的数据！')
      }
    },
    handleGenerateEntry () {
      if (this.tableSelectedRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要生成报关单的数据!')
        return
      }
      if (this.tableSelectedRows.length > 1) {
        this.$Message.warning('一次仅能选择一条数据生成报关单！')
        return
      }
      if (this.tableSelectedRows[0].status !== '1') {
        this.$Message.warning('此数据状态不允许进行此操作')
        return
      }

      matchStat(this, {headId: this.tableSelectedRows[0].sid}).then(res => {
        const data = res.data.data
        if (data) {
          this.matchDone = data.remainCount === 0
          this.showGenerateEntry = true
        }
      }, () => {})
    },
    handleFetchDcr () {
      this.showSyncDcrTimes = true
    },
    handleConfirmGenerateEntry() {
      this.debounce(this.confirmGenerateEntry)()
    },
    confirmGenerateEntry() {
      this.$refs['generateEntry'].getGenerateParam().then(param => {
        this.setToolbarLoading('generate-entry', true)
        this.generateLoading = true
        const generateEntryParam = Object.assign({sid: this.tableSelectedRows[0].sid}, param)
        this.$http.post(csAPI.mrp.manage.generate, generateEntryParam).then(() => {
          this.showGenerateEntry = false
          this.selectRows = []
          this.loadData()
          this.$Message.success('生成成功！')
        }).catch(() => {
        }).finally(() => {
          this.generateLoading = false
          this.setToolbarLoading('generate-entry')
        })
      })
    },
    loadData () {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page,
        sort: this.pageParam.sort.join(',')
      }
      this.$http.post(csAPI.mrp.manage.list, this.searchParam, { params: pageParam})
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {})
        .finally(() => {
        })
    }
  }
}
