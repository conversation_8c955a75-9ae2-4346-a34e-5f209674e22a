import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_match_search'
import tableMixin from './mrp_match_table'
import updateEditDataMixin from '../common/update_edit_data_mixin'
import { isNumber } from '@/libs/util'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'
import { matchStat } from '@/view/cs-mrp/common/mrp_api'
import { isEml } from '../common/mrp_constant'

export default {
  name: 'MrpMatching',
  mixins: [dynamicHeight, pmsMixin, searchMixin, tableMixin, updateEditDataMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      toolbarEventMap: {
        start: this.handleStart,
        export: this.handleExport
      },
      matchSchema: {
        class: ' ',
        titleWidth: 120
      },
      matchParam: {
        emsNo: '',
        headId: '',
        matchMethod: '2',
        declareDateFrom: '',
        declareDateTo: '',
        originCountryType: '0',
        originCountry: '',
        dcrTimesType: '0'
      },
      matchOptions: [
        {label: '先进先销', value: '0'},
        {label: '后进先销', value: '1'},
        {label: '按单价匹配', value: '2'}
      ],
      originCountryOptions: [
        {label: '不限', value: '0'},
        {label: '优先匹配', value: '1'},
        {label: '不优先匹配', value: '2'},
        {label: '指定', value: '3'},
      ],
      dcrTimesTypeOptions: [
        {label: '不限', value: '0'},
        {label: '优先当前', value: '1'},
        {label: '当前周期', value: '2'}
      ],
      showMatch: false,
      totalContent: ''
    }
  },
  mounted() {
    this.matchParam.emsNo = this.editOption.data.emsNo
    this.matchParam.headId = this.editOption.data.sid
    this.searchParam.headId = this.editOption.data.sid
    if (isEml(this.matchParam.emsNo)) {
      this.matchParam.dcrTimesType = ''
    }
    this.loadFunctions('match', 160)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },
    handleDateChange(values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.matchParam, "declareDateFrom", values[0])
        this.$set(this.matchParam, "declareDateTo", values[1])
      } else {
        this.$set(this.matchParam, "declareDateFrom", '')
        this.$set(this.matchParam, "declareDateTo", '')
      }
    },
    handleStart() {
      this.showMatch = true
    },
    handleOriginCountryTypeChange() {
      this.matchParam.originCountry = ''
    },
    handleConfirm() {
      if (!isNumber(this.matchParam.matchMethod)) {
        this.$Message.warning('请选择匹配方式！')
        return
      }
      if (this.matchParam.originCountryType
        && this.matchParam.originCountryType !== '0' ) {
        if (!this.matchParam.originCountry) {
          this.$Message.warning('请选择原产国！')
          return
        }
      }
      this.showMatch = false
      this.setToolbarLoading('start', true)
      this.$http.post(csAPI.mrp.match.start, this.matchParam).then(() => {
        this.doQuery()
        this.updateEditOption()
      }, () => {
      }).finally(() => {
        this.setToolbarLoading('start')
      })
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '匹配原始核注清单',
        header: getGridExportColumns(this.tableColumns)
      }
      excelExport(csAPI.mrp.match.export, param)
    },
    loadStat() {
      matchStat(this, this.searchParam).then(res => {
        const data = res.data.data
        if (data) {
          this.totalContent = `未完全匹配料号个数：${data.remainCount} 完全匹配料号个数：${data.matchCount}`
        }
      }, () => {})
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.match.list, this.searchParam, {params: pageParam}).then(res => {
        this.tableData = res.data.data || []
        this.pageParam.dataTotal = res.data.total
      }, () => {
      })
    }
  },
  computed: {
    disableOriginCountry() {
      return this.matchParam.originCountryType === '0'
    },
    matchElements() {
      return [
        {
          type: "select", key: "matchMethod", title: '选择核注清单',
          props: {options: this.matchOptions, optionLabelRender: (it) => it.label}
        },
        {
          type: 'dateRange', title: '申报日期', key: 'declareDateFrom',
          fields: [
            {
              key: 'declareDateFrom'
            },
            {
              key: 'declareDateTo',
            }
          ]
        },
        {
          type: 'select', title: '原产国', key: 'originCountryType',
          props: {options: this.originCountryOptions}
        },
        {
          type: 'select', title: '报核次数', key: 'dcrTimesType',
          props: {options: this.dcrTimesTypeOptions, disabled: isEml(this.matchParam.emsNo)}
        }
      ]
    }
  }
}
