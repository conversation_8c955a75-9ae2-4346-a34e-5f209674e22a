export default {
  data () {
    return {
      qtyOptions: [{ value: '0', label: "等于0" }, { value: '1', label: '大于0' }],
      matchDoneOptions: [{ value: '0', label: "是" }, { value: '1', label: '否' }],
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      searchParam: {
        headId: '',
        facGNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        emsListNo: '',
        entryNo: '',
        declareDateFrom: '',
        declareDateTo: '',
        originCountry: '',
        matchQty: null,
        remainQty: null,
        decBillRemainQty: null,
        done: null
      }
    }
  },
  methods: {
    handleDateChange (values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.searchParam, "declareDateFrom", values[0])
        this.$set(this.searchParam, "declareDateTo", values[1])
      } else {
        this.$set(this.searchParam, "declareDateFrom", '')
        this.$set(this.searchParam, "declareDateTo", '')
      }
    },
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'copGNo', title: '备案料号'
        },
        {
          key: 'gno', title: '备案序号'
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        },
        {
          key: 'emsListNo', title: '清单内部编号'
        },
        {
          key: 'entryNo', title: '报关单号'
        },
        {
          type:'dateRange',title: '申报日期', key: 'declareDateFrom',
          fields: [
            {
              key: 'declareDateFrom'
            },
            {
              key: 'declareDateTo',
            }
          ]
        },
        {
          key: 'originCountry', type: 'pcode', title: '原产国（地区）',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        },
        {
          type:"select", key: 'done', title: '匹配完成',
          props: { options: this.matchDoneOptions, optionLabelRender: (it) => it.label }
        }
      ]
    }
  }
}
