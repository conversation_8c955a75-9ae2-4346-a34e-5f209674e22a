
export default {
  data () {
    return {
      tableColumns: [
        {
          title: '企业料件料号',
          width: 120,
          key: 'facGNo',
        },
        {
          title: '备案料号',
          width: 120,
          key: 'copGNo'
        },
        {
          title: '备案序号',
          width:  80,
          key: 'gno'
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS'
        },
        {
          title: '料件商品名称',
          width: 160,
          key: 'gname'
        },
        {
          title: '料件待补税总量',
          width: 120,
          key: 'qty',
        },
        {
          title: '清单内部编号',
          width: 160,
          key: 'emsListNo',
        },
        {
          title: '核注清单编号',
          width: 160,
          key: 'listNo',
        },
        {
          title: '报核次数',
          width: 80,
          key: 'dcrTimes'
        },
        {
          title: '报关单号',
          width: 120,
          key: 'entryNo',
        },
        {
          title: '申报日期',
          width: 120,
          key: 'declareDate',
          valueFormatter: ({value}) => { return value && value.slice(0, 10) }
        },
        {
          title: '原产国（地区）',
          width: 120,
          key: 'originCountry',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('COUNTRY_OUTDATED', value)}` : '' }
        },
        {
          title: '原申报数量',
          width: 120,
          key: 'originQty',
        },
        {
          title: '已匹配数量',
          width: 120,
          key: 'originMrpQty',
        },
        {
          title: '原剩余数量',
          width: 120,
          key: 'originRemainQty',
        },
        {
          title: '本次匹配数量',
          width: 120,
          key: 'matchQty',
        },
        {
          title: '本次未匹配数量',
          width: 120,
          key: 'remainQty',
        },
        {
          title: '核注清单剩余数量',
          width: 120,
          key: 'decBillRemainQty',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
