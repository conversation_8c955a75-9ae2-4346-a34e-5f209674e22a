<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
        </FormBuilder>
      </div>
    </XdoCard>
    <div class="action mrp-toolbar" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      <div style="font-size: 12px">{{editOption.data.matchMark}}</div>
    </div>
    <XdoCard>
      <xdo-ag-grid class="dc-table" ref="table" :columns="tableColumns" :data="tableData"
                   :height="dynamicHeight"></xdo-ag-grid>
      <div ref="area_page" style="height: 26px; overflow: hidden">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
              @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        <span style="font-size:12px; position: relative; top: -22px; float: right; margin-right: 90px">{{totalContent}}</span>
      </div>
    </XdoCard>
    <Modal v-model="showMatch" width="420" class-name="vertical-center-modal" title="匹配核注清单" :closable="false" :mask-closable="false">
      <FormBuilder ref="headSearch" v-show="showMatch" :schema="matchSchema" :items="matchElements" :model="matchParam">
        <template v-slot:declareDateFrom>
          <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
        </template>
        <template v-slot:originCountryType>
          <div style="display: flex; justify-content: space-between">
            <xdo-select v-model="matchParam.originCountryType" @on-change="handleOriginCountryTypeChange" :options="originCountryOptions" style="margin-right: 20px"></xdo-select>
            <xdo-select v-model="matchParam.originCountry" :disabled="disableOriginCountry" :asyncOptions="pcodeList" meta="COUNTRY_OUTDATED" :optionLabelRender="pcodeRender"></xdo-select>
          </div>
        </template>
      </FormBuilder>
      <div slot="footer">
        <Button type="text"  @click="showMatch = false">取消</Button>
        <Button type="primary" @click="handleConfirm">确定</Button>
      </div>
    </Modal>
  </section>
</template>
<script src="./mrp_match.js"></script>
<style scoped>
.mrp-toolbar {
  display: flex;
  justify-content:space-between;
  align-items: center;
  background-color: #fff;
  padding-right: 20px;
}
</style>
