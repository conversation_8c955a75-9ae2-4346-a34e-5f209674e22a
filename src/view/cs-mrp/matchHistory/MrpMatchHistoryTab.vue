<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="mrpMatchTab">
      <TabPane name="historyTab" label="已匹配明细" tab="mrpMatchTab">
        <MrpMatchHistory></MrpMatchHistory>
      </TabPane>
    </XdoTabs>
  </section>
</template>
<script>

import MrpMatchHistory from './MrpMatchHistory'

export default {
  name: 'MrpMatchTab',
  components: { MrpMatchHistory },
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      tabName: 'historyTab',
      tabs: {
        historyTab: true,
      },
    }
  },
  watch: {
    tabName (value) {
      this.tabs[value] = true
    }
  },
  methods: {
  }
}
</script>
