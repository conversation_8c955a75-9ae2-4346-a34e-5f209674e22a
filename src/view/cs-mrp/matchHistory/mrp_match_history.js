import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_match_history_search'
import tableMixin from './mrp_match_history_table'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'

export default {
  name: 'MrpMatching',
  mixins: [dynamicHeight, pmsMixin, searchMixin, tableMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      toolbarEventMap: {
        export: this.handleExport
      }
    }
  },
  mounted() {
    this.loadFunctions('default', 164)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '内销已匹配明细',
        header: getGridExportColumns(this.tableColumns)
      }
      excelExport(csAPI.mrp.match.historyExport, param)
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.match.history, this.searchParam, {params: pageParam}).then(res => {
        this.tableData = res.data.data || []
        this.pageParam.dataTotal = res.data.total
      }, () => {})
    }
  }
}
