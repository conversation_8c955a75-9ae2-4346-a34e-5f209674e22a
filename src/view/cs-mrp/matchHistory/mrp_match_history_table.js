
export default {
  data () {
    return {
      tableColumns: [
        {
          title: '企业料件料号',
          width: 160,
          key: 'facGNo',
        },
        {
          title: '备案料件料号',
          width: 160,
          key: 'copGNo'
        },
        {
          title: '备案料件序号',
          width:  120,
          key: 'gno'
        },
        {
          title: '料件商品编码',
          width: 120,
          key: 'codeTS'
        },
        {
          title: '料件商品名称',
          width: 160,
          key: 'gname'
        },
        {
          title: '原核注清单编号',
          with: 160,
          key: 'listNo'
        },
        {
          title: '原报关单号',
          with: 160,
          key: 'entryNo'
        },
        {
          title: '原核注清单数量',
          width: 120,
          key: 'billQty',
        },
        {
          title: '内销单据内部编号',
          width: 120,
          key: 'batchNo',
        },
        {
          title: '内销核注清单编号',
          with: 160,
          key: 'mrpListNo'
        },
        {
          title: '内销报关单号',
          with: 160,
          key: 'mrpEntryNo'
        },
        {
          title: '内销申报日期',
          width: 120,
          key: 'mrpDeclareDate',
          valueFormatter: ({value}) => { return value && value.slice(0, 10) }
        },
        {
          title: '内销匹配数量',
          with: 160,
          key: 'matchQty'
        },
        {
          title: '申报单位',
          with: 160,
          key: 'mrpDeclareName'
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
