export default {
  data () {
    return {
      qtyOptions: [{ value: '0', label: "等于0" }, { value: '1', label: '大于0' }],
      matchDoneOptions: [{ value: '0', label: "是" }, { value: '1', label: '否' }],
      showSearch: false,
      searchSchema: {
        titleWidth: 120
      },
      searchParam: {
        facGNo: '',
        copGNo: '',
        gno: '',
        codeTS: '',
        gname: '',
        listNo: '',
        entryNo: '',
        batchNo: '',
        mrpListNo: '',
        mrpEntryNo: '',
        mrpDeclareDateFrom: '',
        mrpDeclareDateTo: '',
      }
    }
  },
  methods: {
    handleDateChange(values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.matchParam, "mrpDeclareDateFrom", values[0])
        this.$set(this.matchParam, "mrpDeclareDateTo", values[1])
      } else {
        this.$set(this.matchParam, "mrpDeclareDateFrom", '')
        this.$set(this.matchParam, "mrpDeclareDateTo", '')
      }
    },
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(160, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.pageParam.page = 1
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'copGNo', title: '备案料件料号'
        },
        {
          key: 'gno', title: '备案料件序号'
        },
        {
          key: 'codeTS', title: '料件商品编码'
        },
        {
          key: 'gname', title: '料件商品名称'
        },
        {
          key: 'listNo', title: '原核注清单编号'
        },
        {
          key: 'entryNo', title: '原报关单号'
        },
        {
          key: 'batchNo', title: '内销单据内部编号'
        },
        {
          key: 'mrpListNo', title: '内销核注清单编号'
        },
        {
          key: 'mrpEntryNo', title: '内销报关单号'
        },
        {
          type:'dateRange',title: '内销申报日期', key: 'mrpDeclareDateFrom',
          fields: [
            {
              key: 'mrpDeclareDateFrom'
            },
            {
              key: 'mrpDeclareDateTo',
            }
          ]
        },
      ]
    }
  }
}
