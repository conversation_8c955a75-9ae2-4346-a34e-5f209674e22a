import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_dcr_detail_search'
import tableMixin from './mrp_dcr_detail_table'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'

export default {
  name: 'MrpDcrSummary',
  mixins: [dynamicHeight, pmsMixin, searchMixin, tableMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      toolbarEventMap: {
        export: this.handleExport
      }
    }
  },
  mounted() {
    this.loadFunctions('detail', 240)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '期初匹配明细',
        header: getGridExportColumns(this.tableColumns)
      }
      this.setToolbarLoading('export', true)
      excelExport(csAPI.mrp.dcr.detailExport, param).finally(() => {
        this.setToolbarLoading('export', false)
      })
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.dcr.detailList, this.searchParam, {params: pageParam}).then(res => {
        this.tableData = res.data.data || []
        this.pageParam.dataTotal = res.data.total
      }, () => {})
    }
  }
}
