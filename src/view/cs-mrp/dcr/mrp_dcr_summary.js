import pmsMixin from '@/libs/pms'
import searchMixin from './mrp_dcr_summary_search'
import tableMixin from './mrp_dcr_summary_table'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '../../cs-common/function'
import MrpDcrFetchPop from '../component/MrpDcrFetchPop.vue'
import DcImport from '@/components/dc-import'

export default {
  name: 'MrpDcrSummary',
  mixins: [dynamicHeight, pmsMixin, searchMixin, tableMixin],
  components: {MrpDcrFetchPop, DcImport},
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      toolbarEventMap: {
        extract: this.handleExtract,
        import: this.importExcel,
        export: this.handleExport,
        delete: this.handleDelete
      },
      showFetch: false,
      importShow: false,
      importConfig: {
        tplName: '期初匹配明细汇总导入',
        url: csAPI.mrp.dcr.rest + '/import',
        tplUrl: csAPI.mrp.dcr.rest + '/export/tpl',
        correctUrl: csAPI.mrp.dcr.rest + '/import',
        errorUrl:  csAPI.mrp.dcr.rest + '/export'
      },
      importParam: {
      }
    }
  },
  mounted() {
    this.loadFunctions('default', 164)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
    },
    handleExtract () {
      this.showFetch = true
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '期初匹配明细-汇总',
        header: getGridExportColumns(this.tableColumns)
      }
      this.setToolbarLoading('export', true)
      excelExport(csAPI.mrp.dcr.summaryExport, param).finally(() => {
        this.setToolbarLoading('export', false)
      })
    },
    importExcel () {
      this.importShow = true
    },
    importSuccess () {
      this.doQuery()
    },
    handleDelete () {
      if (this.tableSelectedRows.length > 0) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.setToolbarLoading('delete', true)
            const sids = this.tableSelectedRows.map(item => item.sid).join(',')
            this.$http.delete(`${csAPI.mrp.dcr.rest}/${sids}`).then(() => {
              this.$Message.success('删除成功！')
              this.tableSelectedRows = []
              this.loadData()
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('delete')
            })
          },
          onCancel: () => {
          }
        })
      } else {
        this.$Message.warning('请选择要删除的数据！')
      }
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.dcr.summaryList, this.searchParam, {params: pageParam}).then(res => {
        this.tableData = res.data.data || []
        this.pageParam.dataTotal = res.data.total
      }, () => {})
    }
  }
}
