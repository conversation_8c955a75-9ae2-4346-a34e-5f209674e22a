<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="mrpDcrTab">
      <TabPane name="summaryTab" label="匹配汇总" tab="mrpDcrTab">
        <MrpDcrSummary></MrpDcrSummary>
      </TabPane>
      <TabPane name="detailTab" label="匹配明细" tab="mrpDcrTab">
        <MrpDcrDetail></MrpDcrDetail>
      </TabPane>
    </XdoTabs>
  </section>
</template>
<script>

import MrpDcrDetail from './MrpDcrDetail.vue'
import MrpDcrSummary from './MrpDcrSummary.vue'
export default {
  name: 'MrpDcrTab',
  components: { MrpDcrDetail, MrpDcrSummary },
  data () {
    return {
      tabName: 'summaryTab',
      tabs: {
        summaryTab: true,
        detailTab: false
      },
    }
  },
  watch: {
    tabName (value) {
      this.tabs[value] = true
    }
  },
  methods: {
  }
}
</script>
<style>
</style>
