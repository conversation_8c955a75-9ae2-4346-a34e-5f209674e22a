
export default {
  data () {
    return {
      tableColumns: [
        {
          title: "报核次数",
          with: 100,
          key: 'dcrTimes'
        },
        {
          title: '备案号',
          width: 120,
          key: 'emsNo',
        },
        {
          title: '企业料件料号',
          width: 160,
          key: 'facGNo',
        },
        {
          title: '备案料件料号',
          width: 160,
          key: 'copGNo'
        },
        {
          title: '期初数量',
          width: 160,
          key: 'qty'
        },
        {
          title: '已匹配数量',
          width: 160,
          key: 'matchQty'
        },
        {
          title: '报关单号',
          width: 120,
          key: 'entryNo',
        },
        {
          title: '核注清单编号',
          width: 160,
          key: 'listNo',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
