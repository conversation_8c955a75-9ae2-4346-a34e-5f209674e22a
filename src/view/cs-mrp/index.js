import { namespace } from '@/project'
import MrpB<PERSON> from  './bom/MrpBomTab'
import MrpManage from './manage/MrpManage'
import MrpDcrTab from './dcr/MrpDcrTab.vue'
import MrpBalance from './balance/MrpBalance.vue'
import MrpHistoryVrfded from './history/MrpHistoryVrfded'
import MrpMatchHistoryTab from './matchHistory/MrpMatchHistoryTab.vue'

export default [
  {
    path: '/' + namespace + '/mrp/manage',
    name: 'MrpManage',
    meta: {
      icon: 'ios-document',
      title: '内销试算管理'
    },
    component: MrpManage
  },
  {
    path: '/' + namespace + '/mrp/bom',
    name: 'MrpBom',
    meta: {
      icon: 'ios-document',
      title: '内销BOM维护'
    },
    component: MrpBom
  },
  {
    path: '/' + namespace + '/mrp/balance',
    name: 'Mr<PERSON><PERSON><PERSON><PERSON>',
    meta: {
      icon: 'ios-document',
      title: '内销BOM维护'
    },
    component: Mr<PERSON><PERSON><PERSON><PERSON>
  },
  {
    path: '/' + namespace + '/mrp/history',
    name: 'MrpHistoryVrfded',
    meta: {
      icon: 'ios-document',
      title: '内销BOM维护'
    },
    component: MrpHistoryVrfded
  },
  {
    path: '/' + namespace + '/mrp/match/history',
    name: 'MrpMatchTab',
    meta: {
      icon: 'ios-document',
      title: '历史匹配查询'
    },
    component: MrpMatchHistoryTab
  },
  {
    path: '/' + namespace + '/mrp/dcr',
    name: 'MrpDcrTab',
    meta: {
      icon: 'ios-document',
      title: '期初匹配明细'
    },
    component: MrpDcrTab
  }
]
