
export const imgExgDataSourceList = [
  {value: 'ADD', label: '新增'},
  {value: 'IMPORT', label: '导入'},
  {value: 'ERP_EXG_IE', label: '成品出入库'},
  {value: 'ERP_IMG_IE', label: '料件出入库'},
  {value: 'ERP_MRP', label: '内销明细'}
]

export const tradeModeList = [
  { value: '0644', label: '进料料件内销' },
  { value: '0245', label: '来料料件内销' },
  { value: '0444', label: '保区进料成品' },
  { value: '0445', label: '保区来料成品' },
  { value: '0446', label: '加工设备内销' },
  { value: '0544', label: '保区进料料件' },
  { value: '0545', label: '保区来料料件' },
  { value: '0844', label: '进料边角料内销' },
  { value: '0845', label: '来料边角料内销' }
]

export const bomDataSource = [
  {value: 'ADD', label: '新增'},
  {value: 'IMPORT', label: '导入'},
  {value: 'COPY', label: '复制'},
  {value: 'SWO', label: '核销'},
  {value: 'BOM', label: '原始BOM接口'},
]

export const priceTypeOptions = [
  { label: '加权平均', value: 'wei_avg' },
  { label: '最高价', value: 'max' },
  { label: '最低价', value: 'min' }
]

export const exchangeRateTypeOptions = [
  { label: '取最新汇率', value: 'latest' },
  { label: '按清单申报日期取汇率', value: 'declareDate' }
]

export function isEml(emsNo = '') {
  if (emsNo.startsWith('B') || emsNo.startsWith('C')) {
    return true;
  }
  return false;
}
