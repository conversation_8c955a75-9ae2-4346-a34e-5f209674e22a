const defaultOperation = [
  {title: '编辑', handle: 'handleEditByRow'},
  {title: '查看', handle: 'handleViewByRow', marginRight: '0'}
]

const createChildComponent = (h, item, vm, params) => {
  return h('a', {
    style: {
      marginRight: item.marginRight ? item.marginRight : '15px'
    },
    on: {
      click: () => {
        if (vm && vm.hasOwnProperty(item.handle)) {
          vm[item.handle](params.data)
        }
      },
    },
  }, item.title)
}

export const operationRenderer = (vm, meta = defaultOperation) => {
  return majesty.Vue.extend({
    render(h) {
      const children = meta.map(it => createChildComponent(h, it, vm, this.params))
      return h('div', children)
    }
  })
}


export const copDecPriceComponent = (callback) => majesty.Vue.extend({
  render(h) {
    return h('a', {
      on: {
        click: () => {
          if (callback) {
            callback(this.params)
          }
        },
      },
    }, this.params.value)
  }
});
