import { editStatus } from '@/view/cs-common'
import { getKeyValue } from "@/libs/util"

const productClassify =  [{ value: "E", label: "成品" }, { value: '4', label: '成品&半成品' }]

export default {
  data () {
    return {
      tableColumns: [
        {
          title: '成品半成品料号',
          minWidth: 120,
          key: 'facExgNo',
        },
        {
          title: '物料标识',
          minWidth: 120,
          key: 'gmark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify, value) }
        },
        {
          title: '是否使用',
          minWidth: 120,
          key: 'hasUse',
          valueFormatter: ({value}) => { return value === '1' ? '已使用' : '未使用' }
        },
        {
          title: '备案号',
          minWidth: 120,
          key: 'emsNo'
        },
        {
          title: '成品备案料号',
          minWidth: 120,
          key: 'copExgNo'
        },
        {
          title: '成品备案序号',
          minWidth: 120,
          key: 'exgGNo',
        },
        {
          title: '成品商品名称',
          minWidth: 120,
          key: 'exgGName',
        },
        {
          title: '成品商品编码',
          minWidth: 120,
          key: 'exgCodeTS',
        },
        {
          title: 'BOM版本号',
          minWidth: 120,
          key: 'exgVersion',
        },
        {
          title: '数据来源',
          width: 80,
          key: 'dataSource',
        }
      ],
      popTableColumns: [
        {
          title: '成品料号',
          minWidth: 120,
          key: 'facExgNo',
        },
        {
          title: '成品备案料号',
          minWidth: 120,
          key: 'copExgNo'
        },
        {
          title: '成品备案序号',
          minWidth: 120,
          key: 'exgGNo',
        },
        {
          title: '成品商品名称',
          minWidth: 120,
          key: 'exgGName',
        },
        {
          title: '成品商品编码',
          minWidth: 120,
          key: 'exgCodeTS',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      copyPageParam: {
        page: 1,
        limit: 200,
        dataTotal: 0,
        pageSizeOpts: [100, 200, 500, 1000]
      },
      tableData: [],
      selectRows:[],
      copyDataSource: [],
      copySelectRows: []
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      if (data.hasUse === 1) {
        this.$Message.warning('已使用的数据无法进行编辑')
        return;
      }
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.doQuery()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.doQuery()
      }
    },
    handleSelectionChange (param) {
      this.selectRows = param.api.getSelectedRows()
    },
    copyPageChange(val){
      this.copyPageParam.page = val
      this.loadCopyAppointData()
    },
    copyPageSizeChange(val){
      this.copyPageParam.limit = val
      if(this.copyPageParam.page === 1) {
        this.loadCopyAppointData()
      }
    },
    handleSelectionCopyChange (param) {
      this.copySelectRows = param.api.getSelectedRows()
    }
  }
}
