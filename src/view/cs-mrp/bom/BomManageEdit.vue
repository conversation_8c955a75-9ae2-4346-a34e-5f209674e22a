<template>
  <section v-focus>
      <XdoCard :bordered="false" class="dc-merge-1-4">
        <p>成品半成品信息</p>
        <FormBuilder class="form-builder" ref="editForm" :schema="editSchema" :rules="formRules" :items="editElements" :model="editModel"></FormBuilder>
      </XdoCard>
      <XdoCard :bordered="false" class="dc-merge-1-4" style="margin-top: 0">
        <p>料件信息</p>
        <FormBuilder class="form-builder" ref="copForm" :disabled="disabled2" :schema="editSchema"
                     :rules="formRules" :items="copElements" :model="editModel" @updateModel="onUpdateModel"></FormBuilder>
      </XdoCard>
    <div class="xdo-enter-action" style="text-align: center;margin-top:10px">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :key="item.label">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import formMixin from './bom_manage_edit_form'
  import {round} from '@/libs/num'
  import {csAPI} from "../../../api";
    export default {
      name: "BomManageEdit",
      mixins:[formMixin],
      props:{
          editConfig:{type:Object, default: () => ({})}
        },
      data(){
        return{
          buttons:[
            { type: 'primary', disabled: false, loading: false, needed:true, click: this.handleSave, label: '保存' },
            { type: 'primary', disabled: false, loading: false, needed:true, click: this.handleAdd, label: '新增' },
            { type: 'primary', disabled: false, loading: false, needed:true, click: this.handleSaveContinue, label: '保存继续' },
            { type: 'primary', disabled: false, loading: false, needed:true, click: this.handleBack, label: '返回' }
          ],
        }
      },
      methods:{
        getDefault(){
            this.editModel.emsNo= '',
            this.editModel.gmark= '',
            this.editModel.facExgNo ='',
            this.editModel.exgGNo='',
            this.editModel.exgGName='',
            this.editModel.exgVersion=''
        },
        getCopDefault(){
            this.editModel.facGNo='',
            this.editModel.imgGNo='',
            this.editModel.gname='',
            this.editModel.decCm='',
            this.editModel.decDmVisiable='',
            this.editModel.decDmInvisiable='',
            this.editModel.bondMtpckPrpr='',
            this.editModel.decAllConsume='',
            this.editModel.note=''
        },
        handleSave () {
          this.doSave(null)
        },
        doSave(callback){
          this.$refs['editForm'].validate().then(isValid => {
            if(isValid){
              this.$refs['copForm'].validate().then(item => {
                if(item){
                  let http = null
                  const param = Object.assign({}, this.editModel)
                  if(this.editModel.sid){
                    http = this.$http.put(`${csAPI.mrp.bom.update}/${this.editModel.sid}`,param)
                  }
                  else {
                    http = this.$http.post(csAPI.mrp.bom.add, param)
                  }
                  http.then(res=>{
                    if(this.editModel.sid){
                      this.$Message.success('编辑成功')
                    }else{
                      this.$Message.success('新增成功')
                    }
                    this.editModel = Object.assign({}, res.data.data)
                    this.editModel.decAllConsume = round(this.editModel.decAllConsume, 9)
                    this.editModel.decCm += ''
                    if(callback){
                      callback(res)
                    }

                  },()=>{})
                }
              })
            }
          })
        },
        handleAdd(){
          this.getDefault()
          this.getCopDefault()
          this.disabled1 = false
          this.disabled2 = false
          this.editModel.sid = ''
        },
        handleSaveContinue(){
           this.doSave(()=>{
             this.disabled1 = true
             this.getCopDefault()
             this.editModel.sid = ''
           })

        },
        handleBack () {
          this.$emit('onEditBack', {})
        },
      }
    }
</script>

<style lang="less" scoped>
  .form-builder {
    background: white;
    padding-top: 10px;
  }
</style>
