import { editStatus } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-mrp/common/operation_renderer'
import { getKeyValue } from "@/libs/util"

const productClassify =  [{ value: "E", label: "成品" }, { value: '4', label: '成品&半成品' }]

export default {
  data () {
    return {
      basicColumn: [
        {
          title: '操作',
          width: 120,
          key: 'operation',
          cellRendererFramework: operationRenderer(this)
        },
        {
          title: '备案号',
          width: 120,
          key: 'emsNo',
        },
        {
          title: '是否使用',
          width: 80,
          key: 'hasUse',
          valueFormatter: ({value}) => { return value === '1' ? '已使用' : '未使用' }
        },
        {
          title: '物料标识',
          width: 80,
          key: 'gmark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify, value) }
        },
        {
          title: '成品半成品料号',
          width: 180,
          key: 'facExgNo',
        },
        {
          title: '成品备案序号',
          width: 100,
          key: 'exgGNo',
        },
        {
          title: '成品商品名称',
          minWidth: 200,
          key: 'exgGName',
        },
        {
          title: 'BOM版本号',
          width: 100,
          key: 'exgVersion',
        },
        {
          title: '企业料件料号',
          width: 180,
          key: 'facGNo',
        },
        {
          title: '料件备案序号',
          width: 100,
          key: 'imgGNo',
        },
        {
          title: '料件商品名称',
          width: 200,
          key: 'gname',
        },
        {
          title: '净耗',
          width: 80,
          key: 'decCm',
        },
        {
          title: '有形损耗率%',
          width: 100,
          key: 'decDmVisiable',
        },
        {
          title: '无形损耗率%',
          width: 100,
          key: 'decDmInvisiable',
        },
        {
          title: '保税料件比例%',
          width: 120,
          key: 'bondMtpckPrpr',
        },
        {
          title: '保税料件单耗',
          width: 100,
          key: 'decAllConsume',
        },
        {
          title: '数据来源',
          width: 80,
          key: 'dataSource',
        },
        {
          title: '备注',
          width: 200,
          key: 'note',
        },
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableColumns: [],
      tableData: [],
      selectRows:[]
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      if (data.hasUse === 1) {
        this.$Message.warning('已使用的数据无法进行编辑')
        return;
      }
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      // 修复值转string
      this.editConfig.data.decCm = this.editConfig.data.decCm + ''
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
