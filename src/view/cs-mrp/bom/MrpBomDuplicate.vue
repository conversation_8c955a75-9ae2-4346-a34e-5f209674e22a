<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam"></FormBuilder>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard>
      <xdo-ag-grid class="dc-table" ref="table" checkboxSelection rowSelection='multiple'  :columns="tableColumns" :data="tableData"
                   :height="dynamicHeight" @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div ref="area_page">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
    <Modal v-model="showCopyDataSource" width="620"  title="选择要复制的数据" :closable="false" :mask-closable="false">
      <xdo-ag-grid class="dc-table" ref="table" checkboxSelection rowSelection='multiple'  :columns="popTableColumns" :data="copyDataSource"
                   :height="260" @selectionChanged="handleSelectionCopyChange"></xdo-ag-grid>
      <div ref="area_page">
        <XdoPage class="dc-page" :current="copyPageParam.page" :page-size='copyPageParam.limit' :total="copyPageParam.dataTotal" show-total show-sizer :page-size-opts='copyPageParam.pageSizeOpts'
                 @on-change="copyPageChange" @on-page-size-change="copyPageSizeChange"/>
      </div>
      <div slot="footer">
        <Button type="text"  @click="showCopyDataSource = false">取消</Button>
        <Button type="primary" :loading="copyLoading" @click="handleSelectedCopy">确定</Button>
      </div>
    </Modal>
  </section>
</template>
<script src="./mrp_bom_duplicate.js"></script>
