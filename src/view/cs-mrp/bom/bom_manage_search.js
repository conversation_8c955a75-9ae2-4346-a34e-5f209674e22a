import { commonAPI } from '@/api'
import {bomDataSource} from '../common/mrp_constant'

export default {
  data () {
    return {
      searchSchema: {
        titleWidth: 120
      },
      emsNoList: [],
      showSearch: false,
      //emsNoList: [],
      searchParam: {
        emsNo: '',
        facExgNo: '',
        facGNo: '',
        exgVersion: '',
        gmark:'',
        hasUse: '',
        dataSource: '',
        note: ''
      }
    }
  },
  mounted() {
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.map(it => it.VALUE)
        }
      )
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(154, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.pageParam.page = 1
      this.loadData()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          type:"select", key:"emsNo", title: '备案号',
          props: { options: this.emsNoList, optionLabelRender: (it) => it  }
        },
        {
          key: 'facExgNo', title: '成品/半成品料号'
        },
        {
          key: 'facGNo', title: '企业料件料号'
        },
        {
          key: 'exgVersion', title: 'Bom版本号'
        },
        {
          type:"select", key: 'gmark', title: '物料标识',
          props: { options: [{ value: "E", label: "成品" }, { value: '4', label: '成品&半成品' }]}
        },
        {
          type:"select", key: 'hasUse', title: '是否使用',
          props: { options: [{ value: '0', label: "未使用" }, { value: '1', label: '已使用' }]}
        },
        {
          type:"select", key: 'dataSource', title: '数据来源',
          props: { options: bomDataSource, optionLabelRender: (it) => it.label }
        },
        {
          key: 'note', title: '备注'
        },
      ]
    }
  }
}
