import { csAPI, commonAPI } from '@/api'
import { editStatus } from '@/view/cs-common'

export default {
  data () {
    return {
      disabled1:false,
      disabled2:false,
      gmarkOptions: [{ value: "E", label: "成品" }, { value: '4', label: '成品&半成品' }],
      editSchema: {
        titleWidth: 120,
        class: 'dc-form dc-form-3aa'
      },
      editModel: {
        sid:'',
        emsNo: '',
        gmark: '',
        facExgNo: '',
        exgGNo:'',
        exgGName:'',
        exgVersion:'',
        copImgNo: '',
        facGNo:'',
        gname:'',
        decCm:'',
        decDmVisiable:'',
        decDmInvisiable:'',
        bondMtpckPrpr:'',
        decAllConsume:'',
        note: ''
      },
      formRules:{
        emsNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
        gmark:[{required: true, message: '不能为空！', trigger: 'blur'}],
        facExgNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
        exgVersion:[{required: true, message: '不能为空！', trigger: 'blur'}],
        facGNo:[{required: true, message: '不能为空！', trigger: 'blur'}],
        decCm:[{required: true, message: '不能为空！', trigger: 'blur'}]
      },
      emsNoList: [],
      firstInit: 0,
      facExgNoList: [],
      facGNoList: []
    }
  },
  mounted(){
    let me = this
    me.buttons.forEach(item => {
      if(item.label !== '返回'){
        item.needed = !this.showBtn
      }
    })

    Object.assign(this.editModel, this.editConfig.data)
    if(this.showBtn){
      me.disabled1 = true
      me.disabled2 = true
    } else {
      commonAPI.getEmsNoList().then((data) => {
        this.emsNoList = data.map(it => it.VALUE)
      })
      this.handleFacGNoChange()
      if (this.editModel.gmark === '4') {
          this.facExgNoList = [this.editModel.facExgNo]
      } else {
        this.handleFacExgNoChange()
      }
    }
  },
  methods: {
    handleEmsNoChange () {
      // 解决赋值时xdoSelect触发change的问题
      if (this.firstInit < 2) {
        this.firstInit += 1;
        return;
      }
      this.facExgNoList = []
      this.editModel.facGNo = ''
      this.editModel.facExgNo = ''
      this.editModel.codeTS = ''
      this.editModel.gname = ''
      this.editModel.imgGNo = ''
      this.editModel.copImgNo = ''
      this.editModel.exgGNo = ''
      this.editModel.exgGName = ''
      this.editModel.exgCodeTS = ''
      this.editModel.copExgNo = ''
      this.handleFacExgNoChange()
      this.handleFacGNoChange()
    },
    handleGMarkChange () {
      // 解决赋值时xdoSelect触发change的问题
      if (this.firstInit < 2) {
        this.firstInit += 1;
        return;
      }
      this.facExgNoList = []
      this.editModel.facExgNo = ''
      this.editModel.exgGNo = ''
      this.editModel.exgGName = ''
      this.editModel.exgCodeTS = ''
      this.handleFacExgNoChange()
    },
    /**
     * 企业料件料号
     */
    handleFacGNoChange () {
      if(this.showBtn) {
        return
      }

      if (!this.editModel.emsNo) {
        return
      }
      const param = {
        bondedFlag: '0',
        emsNo: this.editModel.emsNo,
        gmark: 'I',
        facGNo: this.editModel.facGNo
      }
      this.$http.post(csAPI.materialRelationship.comm.getFacGNolist, param)
        .then(res => {
          this.facGNoList = res.data.data
        }, () => {})
    },
    loadSearchByFacGNo() {
      if(this.showBtn) {
        return
      }

      if (!this.editModel.emsNo) {
        return
      }
      const param = {
        bondedFlag: '0',
        emsNo: this.editModel.emsNo,
        gmark: 'I',
        facGNo: this.editModel.facGNo
      }
      this.$http.post(csAPI.csMaterielCenter.bonded.getIeInfo, param).then(res => {
        if (res.data.success) {
          const data = res.data.data
          this.editModel.codeTS = data.codeTS
          this.editModel.gname = data.gname
          this.editModel.imgGNo = data.serialNo
          this.editModel.copImgNo = data.copGNo
        }
      }).catch(() => {
      })
    },
    /***
     * 企业成品料号
     */
    handleFacExgNoChange () {
      if(this.showBtn) {
        return
      }

      if (!this.editModel.gmark || this.editModel.gmark === '4') {
        return;
      }

      if (!this.editModel.emsNo || !this.editModel.gmark) {
        return;
      }

      const param = {
        bondedFlag: '0',
        emsNo: this.editModel.emsNo,
        gmark: this.editModel.gmark,
        facGNo: this.editModel.facExgNo
      }
      this.$http.post(csAPI.materialRelationship.comm.getFacGNolist, param)
        .then(res => {
          this.facExgNoList = res.data.data
        }, () => {})
    },
    loadSearchByFacExgGNo() {
      if(this.showBtn) {
        return
      }

      if (!this.editModel.gmark || this.editModel.gmark === '4') {
        return;
      }

      if (!this.editModel.emsNo || !this.editModel.gmark) {
        return;
      }

      const param = {
        bondedFlag: '0',
        emsNo: this.editModel.emsNo,
        gmark: this.editModel.gmark,
        facGNo: this.editModel.facExgNo
      }
      this.$http.post(csAPI.csMaterielCenter.bonded.getIeInfo, param).then(res => {
        if (res.data.success) {
          const data = res.data.data
          this.editModel.exgGNo = data.serialNo
          this.editModel.exgGName = data.gname
          this.editModel.exgCodeTS = data.codeTS
          this.editModel.copExgNo = data.copGNo
        }
      }).catch(() => {
      })
    },
    onUpdateModel (formData, key) {
      if (key === 'decCm' || key === 'decDmVisiable' || key === 'decDmInvisiable' || key === 'bondMtpckPrpr') {
        const value = (1 - (Number.parseFloat(formData.decDmVisiable) + Number.parseFloat(formData.decDmInvisiable )) / 100.0);

        if (value !== 0) {
          const decAllConsume = Number.parseFloat(formData.decCm) / value * Number.parseFloat(formData.bondMtpckPrpr) / 100;
          if (!Number.isNaN(decAllConsume)) {
            this.editModel.decAllConsume = decAllConsume.toFixed(9) + ''
          }
        }
      }
    }
  },
  watch: {
    'editModel.facGNo': {
      handler: function () {
        this.loadSearchByFacGNo()
      }
    },
    'editModel.facExgNo': {
      handler: function () {
        this.loadSearchByFacExgGNo()
      }
    }
  },
  computed: {
    disabledFetchFacGNo() {
      return this.editModel.gmark !== '4'
    },
    editElements () {
      if (this.disabled1) {
        return [
          {
            key:"emsNo", title: '备案号',
            props: { disabled: true }
          },
          {
            title: '物料标识', key: 'gmark',
            props: { disabled: true }
          },
          {
            title: '成品半成品料号', key: 'facExgNo',
            props: { disabled: true }
          },
          {
            title: '成品备案序号', key: 'exgGNo',
            props: { disabled: true }
          },
          {
            title: '成品商品名称', key: 'exgGName',
            props: { disabled: true }
          },
          {
            title: 'BOM版本号', key: 'exgVersion',
            props: { disabled: true }
          },
        ]
      } else {
        return [
          {
            type:"select", key:"emsNo", title: '备案号',
            props: { options: this.emsNoList, optionLabelRender: (it) => it },
            events: {  'on-change': this.handleEmsNoChange }
          },
          {
            type:'select',title: '物料标识', key: 'gmark',
            props: { options: this.gmarkOptions },
            events: {  'on-change': this.handleGMarkChange }
          },
          {
            type:"select", title: '成品半成品料号', key: 'facExgNo',
            attrs: { maxlength: 50 },
            props: { mixer: this.editModel && this.editModel.gmark === '4', options: this.facExgNoList, optionLabelRender: (it) => it }
          },
          {
            title: '成品备案序号', key: 'exgGNo',
            props: { disabled: this.disabledFetchFacGNo }
          },
          {
            title: '成品商品名称', key: 'exgGName',
            props: { disabled: this.disabledFetchFacGNo }
          },
          {
            title: 'BOM版本号', key: 'exgVersion',
            attrs:{
              maxLength:30
            }
          },
        ]
      }

    },
    copElements () {
      if (this.disabled2) {
        return [
          {
            title: '企业料件料号', key: 'facGNo'
          },
          {
            title: '料件备案序号', key: 'imgGNo'
          },
          {
            title: '料件商品名称', key: 'gname'
          },
          {
            title: '净耗', key: 'decCm'
          },
          {
            title: '有形损耗%', key: 'decDmVisiable'
          },
          {
            title: '无形损耗%', key: 'decDmInvisiable'
          },
          {
            title: '保税料件比例%', key: 'bondMtpckPrpr'
          },
          {
            title: '单耗', key: 'decAllConsume'
          },
          {
            title: '备注', key: 'note'
          }
        ]
      } else {
        return [
          {
            type:"select", title: '企业料件料号', key: 'facGNo',
            attrs: { maxlength: 50 },
            props: { options: this.facGNoList, optionLabelRender: (it) => it }
          },
          {
            title: '料件备案序号', key: 'imgGNo',
            props: { disabled: true }
          },
          {
            title: '料件商品名称', key: 'gname',
            props: { disabled: true }
          },
          {
            title: '净耗', key: 'decCm',type:'xdoInput',
            props:{
              decimal:true, intLength:"9", precision:"9", notConvertNumber: true
            }
          },
          {
            title: '有形损耗%', key: 'decDmVisiable',
            type:'xdoInput',
            props:{
              decimal:true, intLength:"2", precision:"9", notConvertNumber: true
            }
          },
          {
            title: '无形损耗%', key: 'decDmInvisiable',
            type:'xdoInput',
            props:{
              decimal:true, intLength:"2", precision:"9", notConvertNumber: true
            }
          },
          {
            title: '保税料件比例%', key: 'bondMtpckPrpr',
            type:'xdoInput',
            props:{
              decimal:true, intLength:"3", precision:"9", notConvertNumber: true
            }
          },
          {
            title: '单耗', key: 'decAllConsume',
            props:{
              disabled: true, notConvertNumber: true
            }
          },
          {
            title: '备注', key: 'note',
            attrs:{
              maxLength:50
            }
          },
        ]
      }
    },
    showBtn(){
      return this.editConfig.status === editStatus.SHOW
    }
  }
}
