import tableMixin from './mrp_bom_duplicate_table'
import searchMixin from './mrp_bom_duplicate_search'
import pmsMixin from '@/libs/pms'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { getGridExportColumns } from '@/view/cs-common/function'

export default {
  name: 'MrpBomDuplicate',
  mixins:[dynamicHeight, searchMixin, tableMixin, pmsMixin],
  data () {
    return {
      copyLoading: false,
      showCopyDataSource: false,
      toolbarEventMap: {
        'export': this.handleExport,
        'select-copy': this.handleAppointCopy,
        'all-copy': this.handleAllCopy
      },
    }
  },
  mounted() {
    this.loadFunctions('maintain', 190)
    this.doQuery()
  },
  methods: {
    doQuery() {
      this.loadData()
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '内销BOM',
        header: getGridExportColumns(this.tableColumns)
      }

      excelExport(csAPI.mrp.bom.maintain + '/export', param)
    },
    handleAppointCopy () {
      if (this.selectRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要复制的数据!')
      } else if (this.selectRows.length > 1) {
        this.$Message.warning('指定复制一次只能选择一条数据！')
      } else {
        this.loadCopyAppointData()
      }
    },
    handleSelectedCopy () {
      if (this.copySelectRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要复制的数据!')
        return
      }

      const param = this.selectRows.map(it => { return {emsNo: it.emsNo, facExgNo: it.facExgNo, exgVersion: it.exgVersion} })[0]
      param.copyList = this.copySelectRows.map(it => it.facExgNo);
      this.copyLoading = true
      this.$http.post(csAPI.mrp.bom.copy + '/appoint', param)
        .then(res => {
          if (res.data.data.length > 0) {
            this.$Message.success('复制成功')
            this.showCopyDataSource = false
            this.doQuery()
          } else {
            this.$Message.warning('物料中心没有找到可以复制的数据')
          }
        }, () => {})
        .finally(() => {
          this.copyLoading = false
        })
    },
    handleAllCopy() {
      if (this.selectRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要复制的数据!')
        return;
      }


      const params = []
      const duplicateMap = {}
      for (const item of this.selectRows) {
        const key = `${item.emsNo}/${item.facExgNo}`
        if (duplicateMap.hasOwnProperty(key)) {
          this.$Message.warning(`${item.facExgNo}成品料号存在多个版本号请重新选择`)
          return;
        }
        duplicateMap[key] = true
        params.push({emsNo: item.emsNo, facExgNo: item.facExgNo, exgVersion: item.exgVersion})
      }

      this.$Modal.confirm({
        title: '提醒',
        content: '确定将选中的BOM复制给同序号下其他成品料号',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.setToolbarLoading('all-copy', true)
          this.$http.post(csAPI.mrp.bom.copy + '/all', params)
            .then(res => {
              if (res.data.data.length > 0) {
                this.$Message.success('复制成功')
                this.doQuery()
              } else {
                this.$Message.warning('物料中心没有找到可以复制的数据')
              }
            }, () => {})
            .finally(() => {
              this.setToolbarLoading('all-copy', false)
            })
        }
      });
    },
    loadCopyAppointData() {
      const params = this.selectRows.map(it => { return {emsNo: it.emsNo, facExgNo: it.facExgNo, exgVersion: it.exgVersion} })[0]
      const pageParam = {
        limit: this.copyPageParam.limit,
        page: this.copyPageParam.page
      }
      this.$http.post(csAPI.mrp.bom.maintain + '/exg', params, { params: pageParam})
        .then(res => {
          this.copyDataSource = res.data.data
          this.copyPageParam.dataTotal = res.data.total
          if (this.copyDataSource.length > 0) {
            this.showCopyDataSource = true
          } else {
            this.$Message.warning('物料中心没有找到可以复制的数据')
          }
        }, () => {})
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.bom.maintain, this.searchParam, { params: pageParam})
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {})
    }
  }
}
