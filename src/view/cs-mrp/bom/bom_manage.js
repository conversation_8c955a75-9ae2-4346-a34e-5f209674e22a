import tableMixin from './bom_manage_table'
import searchMixin from './bom_manage_search'
import pmsMixin from '@/libs/pms'
import { dynamicHeight } from '@/common'
import { editStatus } from '@/view/cs-common'
import { csAPI, excelExport } from '@/api'
import BomManageEdit from "./BomManageEdit";
import ImportPage from 'xdo-import'
import { getGridExportColumns } from '../../cs-common/function'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

export default {
  name: "BomManage",
  components: {BomManageEdit, ImportPage},
  mixins: [dynamicHeight, searchMixin, tableMixin, pmsMixin, dynamicImport],
  data() {
    let importConfig = this.getCommImportConfig('MRP_BOM_IMPORT')
    return {
      importKey: 'MRP_BOM_IMPORT',
      importConfig: importConfig,
      showList: true,
      modelImportShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.rowEdit,
        'delete': this.handleDelete,
        'all-delete': this.handleAllDelete,
        'import': this.handleImport,
        'export': this.handleExport,
        'take': this.handleTake
      },
      editConfig: {
        status: editStatus.ADD,
        data: null,
      }
    }
  },
  mounted() {
    this.tableColumns = this.basicColumn
    this.loadFunctions('default', 164)
    this.loadData()
  },
  methods: {
    handleSelectionChange(param) {
      this.selectRows = param.api.getSelectedRows()
    },
    handleAdd() {
      this.showList = !this.showList
      this.editConfig.status = editStatus.ADD
    },
    onEditBack() {
      this.showList = true
      this.editConfig.data = {}
      this.loadData()
    },
    rowEdit() {
      if (this.selectRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要编辑的数据!')
      } else if (this.selectRows.length > 1) {
        this.$Message.warning('一次仅能编辑一条数据！')
      } else {
        this.handleEditByRow(this.selectRows[0])
      }
    },
    handleDelete() {
      if (this.selectRows.length > 0) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.setToolbarLoading('delete', true)
            const sids = this.selectRows.map(item => item.sid).join(',')
            this.$http.delete(`${csAPI.mrp.bom.delete}/${sids}`).then(() => {
              this.$Message.success('删除成功！')
              this.selectRows = []
              this.loadData()
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('delete')
            })
          },
          onCancel: () => {
          }
        })
      } else {
        this.$Message.warning('请选择要删除的数据！')
      }
    },
    handleAllDelete() {
      this.$Modal.confirm({
        title: '提醒',
        content: '全部删除将跳过已经使用的BOM数据！',
        okText: '删除',
        cancelText: '取消',
        onOk: () => {
          this.setToolbarLoading('all-delete', true)
          this.$http.post(`${csAPI.mrp.bom.allDelete}`, this.searchParam).then(() => {
            this.$Message.success('删除成功！')
            this.selectRows = []
            this.loadData()
          }).catch(() => {
          }).finally(() => {
            this.setToolbarLoading('all-delete')
          })
        },
        onCancel: () => {
        }
      })
    },
    handleImport() {
      this.modelImportShow = true
    },
    afterImport() {
      this.modelImportShow = false
      this.loadData()
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '内销BOM',
        header: getGridExportColumns(this.tableColumns.slice(1))
      }

      this.setToolbarLoading('export', true)
      excelExport(csAPI.mrp.bom.export, param).finally(() =>
        this.setToolbarLoading('export')
      )
    },
    handleTake() {
      this.setToolbarLoading('take', true)
      this.loadMrpManage((success, data) => {
        if (!success) {
          this.setToolbarLoading('take')
          this.$Message.warning('获取内销批次失败')
          return
        }

        const options = data.map(it => {
          return {label: it.batchNo, value: it.sid}
        })
        const param = {
          headId: '',
          dcrTimes: 1
        }
        this.$Modal.confirm({
          title: '提醒',
          okText: '提取BOM',
          cancelText: '取消',
          render: (h) => {
            return h('section', {}, [
              h('XdoSelect', {
                style: {
                  'margin-bottom': '15px',
                },
                props: {
                  autofocus: true,
                  options: options,
                  placeholder: '请选择监管方式'
                },
                on: {
                  input: (val) => {
                    param.headId = val
                  }
                }
              }),
              h('XdoInput', {
                props: {
                  value: param.dcrTimes,
                  placeholder: '请输入报核次数'
                },
                on: {
                  input: (val) => {
                    param.dcrTimes = val
                  }
                }
              }),
            ])
          },
          onCancel: () => {
            this.setToolbarLoading('take')
          },
          onOk: () => {
            this.setToolbarLoading('generate-entry', true)
            this.$http.post(csAPI.mrp.bom.take, param).then(() => {
              this.$Message.success('提取成功！')
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('take')
            })
          },
        })
      })
    },
    loadMrpManage(callback) {
      const pageParam = {
        limit: 100,
        page: 1
      }
      this.$http.post(csAPI.mrp.manage.list, this.searchParam, {params: pageParam})
        .then(res => {
          callback && callback(true, res.data.data)
        }, () => {
          callback && callback(false)
        })
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.bom.list, this.searchParam, {params: pageParam})
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {
        })
    }
  }
}
