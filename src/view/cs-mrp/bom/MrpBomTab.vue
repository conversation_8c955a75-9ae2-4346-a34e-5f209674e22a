<template>
  <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="returnTab">
    <TabPane name="detailTab" label="BOM明细维护">
      <MrpBomManage v-if="tabs.detailTab"></MrpBomManage>
    </TabPane>
    <TabPane name="duplicateTab" label="已维护BOM物料">
      <MrpBomDuplicate v-if="tabs.duplicateTab"></MrpBomDuplicate>
    </TabPane>
  </XdoTabs>
</template>
<script>
import MrpBomDuplicate from './MrpBomDuplicate.vue'
import MrpBomManage from './BomManage'

export default {
  name: 'MrpBom',
  components: { MrpBomDuplicate, MrpBomManage },
  data () {
    return {
      tabName: 'detailTab',
      tabs: {
        detailTab: true,
        duplicateTab: false
      },
    }
  },
  mounted() {

  },
  watch: {
    tabName (value) {
      this.tabs[value] = true
    }
  }
}
</script>
