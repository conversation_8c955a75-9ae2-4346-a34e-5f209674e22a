<template>
  <Modal v-model="value" width="420" class-name="vertical-center-modal" title="更新报核期初" :closable="false" :mask-closable="false">
    <XdoForm ref="editForm" :model="formModel" :label-width="100" :rules="formRules">
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="formModel.emsNo" :options="emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="dcrTimes" label="报核次数">
        <xdo-input v-model="formModel.dcrTimes" int-length="5" notConvertNumber></xdo-input>
      </XdoFormItem>
    </XdoForm>
    <div slot="footer">
      <Button type="primary"  :loading='refreshLoading' @click="handleRefresh">更新</Button>
      <Button type="text" @click="handleClose">关闭</Button>
    </div>
  </Modal>
</template>
<script>

import { csAPI, commonAPI } from '@/api'

export default {
  name: 'MrpPriceCalcRange',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      emsNoList: [],
      formModel: {
        emsNo: '',
        dcrTimes: ''
      },
      formRules: {
        emsNo: [{required: true, message: '不能为空！', trigger: 'blur'}],
        dcrTimes: [{required: true, message: '不能为空！', trigger: 'blur'}]
      },
      refreshLoading: false
    }
  },
  mounted () {
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.filter(it => it.VALUE.startsWith('E')).map(it => it.VALUE)
        }
      )
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    handleRefresh () {
      this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          this.prefetch()
        }
      })
    },
    prefetch() {
      const param = Object.assign({}, this.formModel)
      this.$http.post(csAPI.mrp.dcr.prefetch, param)
        .then((res) => {
          if (res.data.data === 0) {
            this.fetch()
          } else {
            this.$Modal.confirm({
            title: '提醒',
            content: '存在报核次数相同的数据，是否重新提取',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
              this.fetch()
            },
            onCancel: () => {
            }
          })
          }
        }, () => {})
    },
    fetch () {
      const param = Object.assign({}, this.formModel)
      this.$http.post(csAPI.mrp.dcr.fetch, param)
        .then(() => {
          this.$Message.success('同步成功')
          this.$emit('fetchSuccess')
          this.handleClose()
        }, () => {})
    }
  }
}
</script>
<style scoped>
</style>

