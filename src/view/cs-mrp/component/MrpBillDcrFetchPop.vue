<template>
  <Modal v-model="value" width="420" class-name="vertical-center-modal" title="更新清单报核次数" :closable="false" :mask-closable="false">
    <XdoForm ref="editForm" :model="formModel" :label-width="100" :rules="formRules">
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="formModel.emsNo" :options="emsNoList"></xdo-select>
      </XdoFormItem>
    </XdoForm>
    <div slot="footer">
      <XdoButton type="primary" :loading='refreshLoading' @click="handleRefresh">更新</XdoButton>
      <XdoButton type="text" @click="handleClose">关闭</XdoButton>
    </div>
  </Modal>
</template>
<script>

import { csAPI } from '@/api'

export default {
  name: 'MrpPriceCalcRange',
  props: {
    value: {
      type: <PERSON>olean,
      default: false
    },
    emsNoList: {
      type: Array,
    }
  },
  data () {
    return {
      formModel: {
        emsNo: ''
      },
      formRules: {
        emsNo:[{required: true, message: '不能为空！', trigger: 'blur'}]
      },
      refreshLoading: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    handleRefresh () {
      this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          const param = Object.assign({}, this.formModel)
          this.refreshLoading = true
          this.$http.post(csAPI.mrp.manage.fetch, param)
            .then(() => {
              this.$Message.success('同步成功')
              this.handleClose()
            }, () => {})
            .finally(() => {
              this.refreshLoading = false
            })
        }
      })
    }
  }
}
</script>
<style scoped>
</style>
