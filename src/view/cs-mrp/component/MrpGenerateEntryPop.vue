<template>
  <section>
    <XdoForm ref="editForm" :model="formModel" :label-width="100" :rules="formRules">
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="formModel.tradeMode" :options="tradeModeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareCodeCustoms" label="报关行">
        <xdo-select v-model="formModel.declareCodeCustoms" :options="cutData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="districtCode" label="境内目的地" style="width:100%">
        <section class="dc-form dc-form-2 xdo-enter-form">
          <xdo-select v-model="formModel.districtCode" meta="AREA" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          <xdo-select v-model="formModel.districtPostCode" :asyncOptions="pcodeList" :meta="'POST_AREA'" :optionLabelRender="pcodeRender"></xdo-select>
        </section>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>
<script>
import { tradeModeList } from '../common/mrp_constant'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { csAPI } from '../../../api'

export default {
  name: 'MrpGenerateEntryPop',
  data () {
    return {
      tradeModeList,
      formModel: {
        tradeMode: '',
        declareCodeCustoms: '',
        districtCode: '',
        districtPostCode: ''
      },
      formRules:{
        tradeMode:[{ required: true, message: '不能为空！', trigger: 'blur'}],
        declareCodeCustoms:[{ required: true, message: '不能为空！', trigger: 'blur'}]
      },
      cutData: []
    }
  },
  mounted () {
    this.$http.post(csAPI.ieParams.CUT).then(res => {
      this.cutData = ArrayToLocaleLowerCase(res.data.data)
    })
  },
  methods: {
    getGenerateParam () {
      return this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          return Promise.resolve(this.formModel)
        } else {
          return Promise.reject()
        }
      })
    }
  }
}
</script>
