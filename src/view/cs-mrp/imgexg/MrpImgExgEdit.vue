<template>
  <section v-focus>
    <FormBuilder class="form-builder" ref="editForm" :disabled="editDisabled" :schema="editSchema" :rules="formRules" :items="editElements" :model="editModel">
    </FormBuilder>
    <div class="xdo-enter-action" style="text-align: center;margin-top:10px">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :key="item.label">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>
<script>
import formMixin from './mrp_img_exg_edit_form'
import { editStatus } from '@/view/cs-common'
import { csAPI } from '@/api'
export default {
  name: 'MrpImgExgEdit',
  mixins: [formMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      editBackOption: {
        showList: true,
        updated: false
      }
    }
  },
  mounted () {
    this.editModel.qty += ''
  },
  methods: {
    handleSaveContinue () {
      this.doSave(() => {
        if (this.editOption.status !== editStatus.SHOW) {
          this.editBackOption.updated = true
          this.editModel = {
            sid: '',
            headId: this.editOption.parent.data.sid,
            gmark: '',
            facGNo: '',
            gno: '',
            qty: '',
            codeTS: '',
            gname: '',
            gmodel: '',
            unit: '',
            decPrice: '',
            curr: '',
            exgVersion: '',
            note: ''
          }
        }
      })
    },
    handleSave () {
      this.doSave(() => {
        this.editBackOption.updated = true
      })
    },
    handleSaveClose (){
      this.doSave(() => {
        this.editBackOption.updated = true
        this.editBack()
      })
    },
    handleBack () {
      this.editBack()
    },
    editBack() {
      this.editBackOption.showList = true
      this.$emit('onEditBack', this.editBackOption)
    },
    handleGModelChange (val) {
      this.editModel.gmodel = val
    },
    doSave(callback) {
      this.$refs['editForm'].validate().then(isValid => {
        if (isValid) {
          let http = null
          if (this.editModel.sid) {
            http = this.$http.put(`${csAPI.mrp.imgExg.rest}/${this.editModel.sid}`, this.editModel)
          } else {
            http = this.$http.post(csAPI.mrp.imgExg.rest, this.editModel)
          }
          http.then(res => {
            this.editModel.sid = res.data.data.sid
            this.$Message.success("保存成功")
            if (callback) {
              callback(res)
            }
          }, () => {})
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .form-builder {
    background: white;
    padding-top: 10px;
  }
</style>
