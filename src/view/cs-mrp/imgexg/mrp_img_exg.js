import tableMixin from './mrp_img_exg_table'
import searchMixin from './mrp_img_exg_search'
import pmsMixin from '@/libs/pms'
import { dynamicHeight } from '@/common'
import { csAPI, excelExport } from '@/api'
import { editStatus } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-mrp/common/operation_renderer'
import ImportPage from 'xdo-import'

import MrpImgExgEdit from './MrpImgExgEdit'
import MrpImgExgExtract from './extract/MrpImgExgExtract'
import { getGridExportColumns } from '../../cs-common/function'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

export default {
  name: 'MrpImgExg',
  mixins: [dynamicHeight, searchMixin, tableMixin, pmsMixin, dynamicImport],
  components: {MrpImgExgEdit, ImportPage, MrpImgExgExtract},
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data() {
    return {
      showList: true,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'take': this.handleTake,
        'export': this.handleExport,
        'all-delete': this.handleAllDelete
      },
      editConfig: {
        parent: null,
        status: editStatus.ADD,
        data: null,
      },
      totalContent: '',
      modelImportShow: false,
      showTake: false
    }
  },
  mounted() {
    this.loadFunctions('body', 165).then(() => {
      if (this.editDisabled) {
        this.actions = []
      }
    })

    if (this.editDisabled) {
      this.tableColumns[0].cellRendererFramework = operationRenderer(this, [{
        title: '查看',
        handle: 'handleViewByRow',
        marginRight: '0'
      }])
    }

    this.doQuery()
    this.searchParam.headId = this.editOption.data.sid
    this.editConfig.parent = this.editOption
  },
  methods: {
    doQuery() {
      this.loadData()
      this.loadStat()
    },
    handleSelectionChange(param) {
      this.tableSelectedRows = param.api.getSelectedRows()
    },
    onEditBack(option) {
      this.showList = option.showList
      if (option.updated) {
        this.doQuery()
      }
    },
    handleAdd() {
      this.editConfig.parent = this.editOption
      this.editConfig.status = editStatus.ADD
      this.editConfig.data = null
      this.showList = !this.showList
    },
    handleEdit() {
      if (this.tableSelectedRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要编辑的数据!')
        return
      } else if (this.tableSelectedRows.length > 1) {
        this.$Message.warning('一次仅能编辑一条数据！')
        return
      }
      this.handleEditByRow(this.tableSelectedRows[0])
    },
    handleImport() {
      this.modelImportShow = true
    },
    afterImport() {
      this.modelImportShow = false
      this.doQuery()
    },
    handleExport() {
      const param = {
        exportColumns: this.searchParam,
        name: '内销物料维护',
        header: getGridExportColumns([...this.tableColumns].splice(1))
      }

      this.setToolbarLoading('export', true)
      excelExport(csAPI.mrp.imgExg.export, param).finally(() => {
        this.setToolbarLoading('export', false)
      })
    },
    handleTake() {
      this.showTake = true
    },
    handleCloseTake() {
      this.showTake = false
      this.handleSearchSubmit();
    },
    handleDelete() {
      if (this.tableSelectedRows.length > 0) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.setToolbarLoading('delete', true)
            const sids = this.tableSelectedRows.map(item => item.sid).join(',')
            this.$http.delete(`${csAPI.mrp.imgExg.batch}/${sids}`).then(() => {
              this.$Message.success('删除成功！')
              this.selectRows = []
              this.doQuery()
            }).catch(() => {
            }).finally(() => {
              this.setToolbarLoading('delete')
            })
          },
          onCancel: () => {
          }
        })
      } else {
        this.$Message.warning('请选择要删除的数据！')
      }
    },
    handleAllDelete() {
      this.$Modal.confirm({
        title: '提醒',
        content: '确认删除所有数据吗',
        okText: '删除',
        cancelText: '取消',
        onOk: () => {
          this.setToolbarLoading('all-delete', true)
          this.$http.delete(`${csAPI.mrp.imgExg.rest}/manage/${this.editOption.data.sid}`).then(() => {
            this.$Message.success('删除成功！')
            this.selectRows = []
            this.doQuery()
          }).catch(() => {
          }).finally(() => {
            this.setToolbarLoading('all-delete')
          })
        },
        onCancel: () => {
        }
      })
    },
    loadStat() {
      this.$http.post(csAPI.mrp.imgExg.stat, this.searchParam)
        .then(res => {
          const data = res.data.data
          if (data) {
            this.totalContent = `料件内销：${data.imgCount}/${data.imgSum} 成品内销：${data.exgCount}/${data.exgSum} 成品&半成品内销：${data.semiCount}/${data.semiSum}`
          }
        }, () => {
        })
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.imgExg.list, this.searchParam, {params: pageParam})
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {
        })
    }
  },
  computed: {
    editDisabled() {
      return this.editOption.status === editStatus.SHOW
    },
    importConfig() {
      let importConfig = this.getCommImportConfig('MRP_IMG_EXG_IMPORT', {
        headId: this.editOption.data.sid,
        emsNo: this.editOption.data.emsNo,
        accessToken: this.$store.state.token
      }, this.editOption.sid)
      return {
        config: importConfig,
        importKey: 'MRP_IMG_EXG_IMPORT'
      }
    }
  }
}
