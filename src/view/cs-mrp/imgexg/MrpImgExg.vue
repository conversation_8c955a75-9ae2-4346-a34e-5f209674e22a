<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam"></FormBuilder>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard>
        <xdo-ag-grid class="dc-table" ref="table" checkboxSelection rowSelection='multiple' :columns="tableColumns" :data="tableData"
                     :height="dynamicHeight" @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div class=""></div>
        <div ref="area_page" style="height: 26px; overflow: hidden">
          <XdoPage class="dc-page" show-total show-sizer
                   :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" :page-size-opts='pageParam.pageSizeOpts'
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="font-size:12px; position: relative; top: -22px; float: right; margin-right: 90px">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <MrpImgExgEdit v-if="!showList" :editOption="editConfig" @onEditBack="onEditBack"></MrpImgExgEdit>
    <ImportPage :importShow.sync="modelImportShow" :importKey="importConfig.importKey" :importConfig="importConfig.config" @onImportSuccess="afterImport"></ImportPage>
    <Modal v-model="showTake" width="1024" title="数据提取" :closable="false" :footer-hide="true" :mask-closable="false">
      <a class="ivu-modal-close" @click="handleCloseTake">
        <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
      </a>
      <mrp-img-exg-extract :editOption="editOption"></mrp-img-exg-extract>
    </Modal>
  </section>
</template>

<script src="./mrp_img_exg.js"></script>
