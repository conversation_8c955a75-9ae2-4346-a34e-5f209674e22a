import { productClassify } from '@/view/cs-common'
import { commonAPI } from '@/api'
import { imgExgDataSourceList } from '../common/mrp_constant'

export default {
  data () {
    return {
      searchSchema: {
        titleWidth: 80
      },
      showSearch: false,
      searchParam: {
        headId: '',
        gmark: '',
        facGNo: '',
        codeTS: '',
        gname: '',
        exgVersion: '',
        billNo: '',
        dataSource: ''
      }
    }
  },
  created() {
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.map(it => it.VALUE)
        }
      )
  },
  methods: {
    handleShowSearch(){
      this.showSearch = !this.showSearch
      this.refreshDynamicHeight(165, !this.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit () {
      this.pageParam.page = 1
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [
        {
          type:"select", key: 'gmark', title: '物料类型',
          props: { options: productClassify.GMARK_MRP_SELECT }
        },
        {
          title: '企业料号', key: 'facGNo',
          attrs: {
            maxlength: 30
          }
        },
        {
          title: '商品编码', key: 'codeTS'
        },
        {
          title: '商品名称', key: 'gname'
        },
        {
          title: 'BOM版本号', key: 'exgVersion',
          attrs: {
            maxlength: 20,
          }
        },
        {
          title: '单据号', key: 'billNo'
        },
        {
          type:"select", key: 'dataSource', title: '数据来源',
          props: { options: imgExgDataSourceList, optionLabelRender: (it) => it.label  }
        }
      ]
    }
  }
}
