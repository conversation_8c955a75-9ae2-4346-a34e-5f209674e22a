
import tableMixin from './mrp-img-exg-extract-table'
import searchMixin from './mrp-img-exg-extract-search'
import { csAPI } from '@/api'

export default {
  name: 'MrpImgExgExtract',
  mixins:[searchMixin, tableMixin],
  props: {
    editOption: {type: Object, default: () => ({})}
  },
  data () {
    return {
      actions: [
        { "command": "take-select", "icon": "ios-create-outline", "label": "提取勾选", "key": "xdo-btn-edit", "disabled": false, "loading": false, "needed": true },
        { "command": "take-all", "icon": "ios-create-outline", "label": "全部提取", "key": "xdo-btn-edit", "disabled": false, "loading": false, "needed": true }
      ],
      toolbarEventMap: {
        'take-select': this.handleTakeSelected,
        'take-all': this.handleTakeAll
      }
    }
  },
  mounted() {
    this.searchParam.headId = this.editOption.data.sid
    this.doQuery()
  },
  methods: {
    handleTakeSelected() {
      if (this.tableSelectedRows.length > 0) {
        this.searchParam.erpSidList = this.tableSelectedRows.map(it => it.sid)
        this.doExtractSave()
      } else {
        this.$Message.warning('请选择要提取的数据！')
      }
    },
    handleTakeAll() {
      this.searchParam.erpSidList = []
      this.doExtractSave()
    },
    doExtractSave() {
      this.actions.forEach(it => it.loading = true)
      this.$http.post(csAPI.mrp.imgExg.extractSave, this.searchParam)
        .then(() => {
          this.$Message.success('提取成功')
          this.doQuery()
        }, () => {})
        .finally(() => {
          this.searchParam.erpSidList = []
          this.actions.forEach(it => it.loading = false)
        })
    },
    handleToolbarClick(command) {
      if (this.toolbarEventMap[command] && typeof this.toolbarEventMap[command === 'function']) {
        this.toolbarEventMap[command]()
      }
    },
    doQuery() {
      this.loadData()
    },
    loadData() {
      const pageParam = {
        limit: this.pageParam.limit,
        page: this.pageParam.page
      }
      this.$http.post(csAPI.mrp.imgExg.extract, this.searchParam, { params: pageParam})
        .then(res => {
          this.tableData = res.data.data
          this.pageParam.dataTotal = res.data.total
        }, () => {})
        .finally(() => {
          this.tableSelectedRows = []
        })
    }
  }
}
