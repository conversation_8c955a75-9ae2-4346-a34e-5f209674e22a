<template>
  <section>
    <div>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <FormBuilder ref="headSearch" v-show="showSearch" :schema="searchSchema" :items="searchElements" :model="searchParam">
            <template v-slot:deliveryDateFrom>
              <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
            </template>
            <template v-slot:soldDateFrom>
              <dc-dateRange label="" @onDateRangeChanged="handleDateChange"></dc-dateRange>
            </template>
          </FormBuilder>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard>
        <xdo-ag-grid class="dc-table" ref="table" checkboxSelection rowSelection='multiple'  :columns="tableColumns" :data="tableData"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal" show-total show-sizer :page-size-opts='pageParam.pageSizeOpts'
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>
<script src="./mrp-img-exg-extract.js"></script>
