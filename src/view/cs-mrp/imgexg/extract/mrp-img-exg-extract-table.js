import { editStatus, productClassify } from '@/view/cs-common'
import { getKeyValue } from "@/libs/util"

export default {
  data () {
    return {
      tableColumns: [
        {
          title: '单据号',
          minWidth: 120,
          align: 'center',
          key: 'billNo',
        },
        {
          title: '物料类型',
          width: 120,
          align: 'center',
          key: 'gmark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify.GMARK_MRP_SELECT, value) }
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
        },
        {
          title: '数量',
          minWidth: 120,
          align: 'center',
          key: 'qty',
        },
        {
          title: '交易单位',
          minWidth: 120,
          align: 'center',
          key: 'unit',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('UNIT', value)}` : '' }
        },
        {
          title: '交易日期',
          minWidth: 120,
          align: 'center',
          key: 'deliveryDate',
        },
        {
          title: '保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify.BONDED_FLAG, value) }
        },
        {
          title: 'BOM版本号',
          width: 120,
          align: 'center',
          key: 'exgVersion',
        },
        {
          title: '备注',
          width: 200,
          align: 'center',
          key: 'note',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.doQuery()
    },
    handleSelectionChange (param) {
      this.tableSelectedRows = param.api.getSelectedRows()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.doQuery()
      }
    }
  }
}
