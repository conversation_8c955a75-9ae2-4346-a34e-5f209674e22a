import { commonAPI } from '@/api'
import { erpInterfaceData, productClassify } from '@/view/cs-common/constant'

export default {
  data () {
    const dataSource = [
      { label: '成品出入库', value: 'extractExgIE'},
      { label: '料件出入库', value: 'extractImgIE'},
      { label: '内销明细', value: 'extractMrp'},
      { label: '下线报废数据', value: 'extractWoDown'},
    ]

    return {
      searchSchema: {
        titleWidth: 80
      },
      showSearch: false,
      searchParam: {
        headId: '',
        origin: 'extractExgIE',
        billNo: '',
        gmark: '',
        ieType: '',
        facGNo: '',
        deliveryDateFrom: '',  // 交易日期开始
        deliveryDateTo: '', // 交易日期结束
        inWay: '7',
        outWay: '7',
        bondMark: '0',
        note: '',
        erpSidList: []
      },
      dynamicForm: {
        extractExgIE: { origin: true, billNo: true, gmark: true, ieType: true, facGNo: true, outWay: true, bondMark: true, deliveryDateFrom: true, note: true },
        extractImgIE: { origin: true, billNo: true, gmark: true, ieType: true, facGNo: true, inWay: true, bondMark: true, deliveryDateFrom: true, note: true },
        extractMrp: { origin: true, billNo: true, gmark: true, facGNo: true, soldDateFrom: true, note: true },
        extractWoDown: { origin: true, facGNo: true }
      },
      formSchema: [
        {
          type:"select", key: 'origin', title: '数据来源',
          props: { options: dataSource, optionLabelRender: (it) => it.label, clearable: false }
        },
        {
          key: 'billNo', title: '单据号'
        },
        {
          type:"select", key: 'gmark', title: '物料类型',
          props: { options: productClassify.GMARK_MRP_SELECT }
        },
        {
          type:"select", key: 'ieType', title: '移动类型',
          props: { options: productClassify.MOVE_MODE }
        },
        {
          title: '企业料号', key: 'facGNo'
        },
        {
          type: "select", key: 'inWay', title: '进口方式',
          props: { options: erpInterfaceData.IMPORT_METHOD_MAP }
        },
        {
          type: "select", key: 'outWay', title: '出口方式',
          props: { options: erpInterfaceData.EXPORT_METHOD_MAP }
        },
        {
          type: "select", key: 'bondMark', title: '保完税标志',
          props: { options: productClassify.BONDED_FLAG_SELECT }
        },
        {
          type:'dateRange',title: '交易日期', key: 'deliveryDateFrom'
        },
        {
          type:'dateRange',title: '销售日期', key: 'soldDateFrom'
        },
        {
          key: 'note', title: '备注'
        }
      ]
    }
  },
  created() {
    commonAPI.getEmsNoList()
      .then((data) => {
          this.emsNoList = data.map(it => it.VALUE)
        }
      )
  },
  methods: {
    handleDateChange (values) {
      if (values instanceof Array && values.length === 2) {
        this.$set(this.searchParam, "deliveryDateFrom", values[0])
        this.$set(this.searchParam, "deliveryDateTo", values[1])
      } else {
        this.$set(this.searchParam, "deliveryDateFrom", '')
        this.$set(this.searchParam, "deliveryDateTo", '')
      }
    },
    handleShowSearch(){
      this.showSearch = !this.showSearch
    },
    handleSearchSubmit () {
      this.pageParam.page = 1
      this.doQuery()
    }
  },
  computed: {
    searchElements () {
      return [...this.formSchema.filter(it => this.dynamicForm[this.searchParam.origin].hasOwnProperty(it.key))]
    }
  }
}
