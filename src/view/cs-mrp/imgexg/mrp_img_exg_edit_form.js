import { productClassify, editStatus } from '@/view/cs-common'
import { csAPI } from '@/api'

export default {
  data () {
    return {
      editSchema: {
        titleWidth: 100,
        class: 'dc-form dc-form-3'
      },
      editModel: {
        sid: '',
        headId: '',
        gmark: '',
        facGNo: '',
        qty: '',
        codeTS: '',
        gname: '',
        gmodel: '',
        unit: '',
        decPrice: '',
        curr: '',
        exgVersion: '',
        note: ''
      },
      firstInit: true,
      emsNoList: [],
      facGNoList: []
    }
  },
  mounted() {

    if (this.editOption.status === editStatus.ADD) {
      this.editModel.headId = this.editOption.parent.data.sid
    } else {
      Object.assign(this.editModel, this.editOption.data)
      if (this.editModel.gmark === '4') {
        this.facGNoList = [this.editModel.facGNo]
      } else {
        this.handleFacGNoChange()
      }
    }
  },
  methods: {
    loadSearchByFacGNo() {
      if (!(this.editModel.gmark && this.editModel.facGNo)) {
        return;
      }
      const param = {
        emsNo: this.editOption.parent && this.editOption.parent.data.emsNo,
        bondedFlag: "0",
        gmark: this.editModel.gmark,
        facGNo: this.editModel.facGNo
      }
      this.$http.post(csAPI.csMaterielCenter.bonded.getIeInfo, param).then(res => {
        if (res.data.success) {
          const data = res.data.data
          this.editModel.codeTS = data.codeTS
          this.editModel.gname = data.gname
          this.editModel.gmodel = data.gmodel
          this.editModel.unit = data.unit
          this.editModel.gno = data.serialNo
          this.editModel.copGNo = data.copGNo
        }
      }).catch(() => {
      })
    },
    handleGMarkChange () {
      if (this.firstInit) {
        this.firstInit = false
        return;
      }
      this.editModel.facGNo = ''
      this.editModel.codeTS = ''
      this.editModel.gname = ''
      this.editModel.gmodel = ''
      this.editModel.unit = ''
      this.editModel.gno = ''
      this.editModel.copGNo = ''
      this.facGNoList = []
      this.handleFacGNoChange()
    },
    handleFacGNoChange () {
      if (!this.editModel.gmark || this.editModel.gmark === '4') {
        return;
      }
      const param = {
        bondedFlag: '0',
        emsNo: this.editOption.parent.data.emsNo,
        gmark: this.editModel.gmark,
        facGNo: this.editModel.facGNo
      }
      this.$http.post(csAPI.materialRelationship.comm.getFacGNolist, param)
        .then(res => {
          this.facGNoList = res.data.data
        }, () => {})
    }
  },
  watch: {
    'editModel.facGNo': {
      handler: function () {
        this.loadSearchByFacGNo()
      }
    }
  },
  computed: {
    editDisabled () {
      return this.editOption.status === editStatus.SHOW
    },
    disabledBomVersion () {
      return this.editModel.gmark === 'I'
    },
    disabledFetchFacGNo() {
      return this.editModel.gmark !== '4'
    },
    buttons () {
      return [
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSave, label: '保存' },
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSaveContinue, label: '保存继续' },
        { type: 'primary', disabled: false, loading: false, needed: !this.editDisabled, click: this.handleSaveClose, label: '保存关闭' },
        { type: 'primary', disabled: false, loading: false, needed: true, click: this.handleBack, label: '返回' }
      ]
    },
    doQuery () {
      if (!this.editModel.gmark) {
        return false
      }
      return true
    },
    getFacGNoList () {
      return this.facGNoList;
    },
    formRules () {
      return {
        gmark:[{ required: true, message: '不能为空！', trigger: 'blur'}],
        facGNo:[{ required: true, message: '不能为空！', trigger: 'blur'}],
        qty:[{ required: true, message: '不能为空！', trigger: 'blur'}],
        exgVersion: this.editModel.gmark === 'I' ? [{}] : [{ required: true, message: '不能为空！', trigger: 'blur'}]
      }
    },
    editElements () {
      return [
        {
          type:"select", key: 'gmark', title: '物料类型',
          props: { options: productClassify.GMARK_MRP_SELECT },
          events: {  'on-change': this.handleGMarkChange }
        },
        {
          type:"select", title: '企业料号', key: 'facGNo',
          attrs: { maxlength: 50 },
          props: { mixer: this.editModel && this.editModel.gmark === '4', options: this.getFacGNoList, optionLabelRender: (it) => it },
          events: {  'on-change': this.handleFacGNoChange }
        },
        {
          type:'xdoInput', title: '内销数量', key: 'qty',
          props: {
            notConvertNumber: true,
            decimal: true,
            intLength: 10,
            precision: 5
          }
        },
        {
          title: '商品编码', key: 'codeTS',
          props: { disabled: this.disabledFetchFacGNo }
        },
        {
          title: '商品名称', key: 'gname',
          props: { disabled: this.disabledFetchFacGNo  }
        },
        {
          title: '规格型号', key: 'gmodel',
          props: { disabled: this.disabledFetchFacGNo  }
        },
        {
          type: 'pcode', title: '申报单位', key: 'unit',
          props: { meta: 'UNIT', disabled: this.disabledFetchFacGNo }
        },
        {
          type:'xdoInput', title: '参考单价', key: 'decPrice',
          props: { notConvertNumber: true, decimal: true, intLength: 10, precision: 4 }
        },
        {
          type: 'pcode', title: '币制', key: 'curr',
          props: {
            meta: 'CURR_OUTDATED'
          }
        },
        {
          title: 'BOM版本号', key: 'exgVersion',
          attrs: {
            maxlength: 30, disabled: this.disabledBomVersion
          }
        },
        {
          title: '备注', key: 'note',
          props: {
            type: 'text'
          },
          attrs: {
            maxlength: 100,
          }
        }
      ]
    }
  }
}
