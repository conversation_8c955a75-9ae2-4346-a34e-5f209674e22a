import { editStatus, productClassify } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-mrp/common/operation_renderer'
import { getKeyValue } from "@/libs/util"

export default {
  data () {
    return {
      tableColumns: [
        {
          title: '操作',
          width: 120,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this)
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
        },
        {
          title: '物料类型',
          width: 120,
          align: 'center',
          key: 'gmark',
          valueFormatter: ({value}) => { return getKeyValue(productClassify.GMARK_MRP_SELECT, value) }
        },
        {
          title: '内销数量',
          minWidth: 120,
          align: 'center',
          key: 'qty',
        },
        {
          title: '商品编码',
          minWidth: 120,
          align: 'center',
          key: 'codeTS',
        },
        {
          title: '商品名称',
          minWidth: 120,
          align: 'center',
          key: 'gname',
        },
        {
          title: '规格型号',
          minWidth: 120,
          align: 'center',
          key: 'gmodel',
        },
        {
          title: '申报单位',
          width: 120,
          align: 'center',
          key: 'unit',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('UNIT', value)}` : '' }
        },
        {
          title: '参考单价',
          width: 120,
          align: 'center',
          key: 'decPrice',
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'curr',
          valueFormatter: ({value}) => { return value ? `${value} ${this.pcodeGet('CURR_OUTDATED', value)}` : '' }
        },
        {
          title: 'BOM版本号',
          width: 120,
          align: 'center',
          key: 'exgVersion',
        },
        {
          title: '单据号',
          width: 120,
          key: 'billNo',
        },
        {
          title: '数据来源',
          width: 120,
          key: 'dataSource',
        },
        {
          title: '备注',
          width: 200,
          align: 'center',
          key: 'note',
        }
      ],
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: 0,
        pageSizeOpts: [10,20,30,40,50]
      },
      tableData: [],
      tableSelectedRows: []
    }
  },
  methods: {
    handleViewByRow (data){
      this.editConfig.status = editStatus.SHOW
      this.editConfig.data = data
      this.showList = false
    },
    handleEditByRow (data) {
      this.editConfig.status = editStatus.EDIT
      this.editConfig.data = data
      this.showList = false
    },
    pageChange(val){
      this.pageParam.page = val
      this.loadData()
    },
    pageSizeChange(val){
      this.pageParam.limit = val
      if(this.pageParam.page === 1) {
        this.loadData()
      }
    }
  }
}
