import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { csAPI } from '@/api'

export const reviewDataList = {
  name: 'reviewDataList',
  mixins: [baseListConfig, listDataProcessing],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function(value) {
        return ['I', 'E'].includes(value)
      }
    },
    showSearch: {
      type: Boolean,
      default: () => false
    },
    searchLines: {
      type: Number,
      default: () => (3)
    },
    searchModel: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let fields = this.getFields()
    return {
      baseFields: [
        ...fields
      ],
      listConfig: {
        operationColumnShow: false       // 显示操作列
      },
      frmData: {
        entryNo: ''
      },
      showModal: false,
      cmbSource: {},
      OffsetHeight: 2,
      hasChildTabs: true,
      toolbarEventMap: {
        'update': this.handleUpdate,
        'export': this.doDownload,
        'manualCompare': this.handleManual
      }
    }
  },
  methods: {
    getSearchParams() {
      return {
        ...this.searchModel,
        iemark: this.ieMark
      }
    },
    getFields() {
      return []
    },
    /**
     * 更新
     */
    handleUpdate() {
      let me = this
      me.$emit('doUpdate')
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleManual() {
      this.showModal = true
    },
    closeManual() {
      this.showModal = false
    },
    commitManual() {
      let me = this
      if (!me.frmData.entryNo) {
        me.$Message.error('请输入报关单号！')
        return
      }
      me.$http.post(csAPI.decReviewData.wrong.manualCompare + '/' + me.frmData.entryNo).then(() => {
        me.$Message.success('手工复核任务已提交！')
        me.showModal = false
        me.handleSearchSubmit()

      }).catch(() => {
      })
    }
  }
}
