import { csAPI } from '@/api'
import WrongDataList from '../components/wrong-data-list'
import CorrectDataList from '../components/correct-data-list'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const decReviewTabs = {
  components: {
    WrongDataList,
    CorrectDataList
  },
  mixins: [baseSearchConfig],
  data() {
    let params = this.getParams()
    return {
      tabName: 'wrongTab',
      tabs: {
        wrongTab: true,
        correctTab: false
      },
      cmbSource: {},
      baseParams: [
        ...params
      ],
      searchModel: {},
      ajaxUrl: {
        update: csAPI.decReviewData.comm.update
      }
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
    }
  },
  methods: {
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        range: true,
        key: 'declareDate',
        title: '报关单申报日期'
      }, {
        key: 'seqNo',
        title: '报关单统一编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        key: 'agentCode',
        title: '申报单位'
      }, {
        type: 'pcode',
        title: '监管方式',
        key: 'tradeMode',
        props: {
          meta: 'TRADE'
        }
      }, {
        type: 'pcode',
        key: 'trafMode',
        title: '运输方式',
        props: {
          meta: 'TRANSF'
        }
      }, {
        key: 'fieldName',
        title: '异常栏位'
      }]
    },
    handleSearchSubmit() {
      let me = this
      me.$set(me, 'searchModel', me.getSearchParams())
      if (me.tabName === 'wrongTab') {
        me.doWrongSearch()
      } else if (me.tabName === 'correctTab') {
        me.doCorrectSearch()
      }
    },
    doWrongSearch() {
      let me = this
      if (me.$refs['wrong']) {
        if (typeof me.$refs['wrong'].handleSearchSubmit === 'function') {
          me.$refs['wrong'].handleSearchSubmit()
        }
      }
    },
    doCorrectSearch() {
      let me = this
      if (me.$refs['correct']) {
        if (typeof me.$refs['correct'].handleSearchSubmit === 'function') {
          me.$refs['correct'].handleSearchSubmit()
        }
      }
    },
    doUpdate() {
      let me = this
      me.$http.post(me.ajaxUrl.update).then(() => {
        me.$set(me, 'searchModel', me.getSearchParams())
        me.doWrongSearch()
        me.doCorrectSearch()
      }).catch(() => {
      })
    },
    doDownload(type) {
      let me = this
      me.$set(me, 'searchModel', me.getSearchParams())
      if (type === 'wrong') {
        if (typeof me.$refs['wrong'].handleDownload === 'function') {
          me.$refs['wrong'].handleDownload()
        }
      } else if (type === 'correct') {
        if (typeof me.$refs['correct'].handleDownload === 'function') {
          me.$refs['correct'].handleDownload()
        }
      }
    }
  }
}
