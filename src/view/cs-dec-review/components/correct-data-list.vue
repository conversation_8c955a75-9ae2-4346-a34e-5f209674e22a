<template>
  <section>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" :disable="gridDisable"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { reviewDataList } from '../js/reviewDataList'

  export default {
    name: 'correctDataList',
    mixins: [reviewDataList],
    data() {
      return {
        listConfig: {
          exportTitle: '正确数据'
        },
        ajaxUrl: {
          exportUrl: csAPI.decReviewData.correct.exportUrl,
          selectAllPaged: csAPI.decReviewData.correct.selectAllPaged
        }
      }
    },
    mounted: function() {
      this.loadFunctions('correct', 190)
    },
    methods: {
      getFields() {
        return [{
          width: 180,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          width: 120,
          key: 'declareDate',
          title: '报关单申报日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }, {
          width: 150,
          key: 'seqNo',
          title: '报关单统一编号'
        }, {
          width: 200,
          key: 'entryNo',
          title: '报关单号'
        }, {
          width: 180,
          tooltip: true,
          key: 'agentCode',
          title: '申报单位'
        }, {
          width: 120,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        }, {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        }, {
          width: 200,
          tooltip: true,
          key: 'erpInsertUser',
          title: '制单人'
        }]
      },
      doDownload() {
        let me = this
        me.$emit('doDownload', 'correct')
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }
</style>
