<template>
  <section>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                   :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data"
                   :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                   @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
    <Modal v-model="showModal" width="600" title="手工复核" :closable="false"
           :mask-closable="false">
      <XdoForm ref="dataForm" class="dc-form xdo-enter-form"
               label-position="right" :label-width="70" v-model="frmData">
        <XdoFormItem prop="note" label="报关单号" class="dc-merge-1-4">
          <XdoIInput type="text" v-model="frmData.entryNo" :maxlength="500"></XdoIInput>
        </XdoFormItem>
      </XdoForm>

      <div slot="footer">
        <Button type="text" @click="closeManual">取消复核</Button>
        <Button type="primary" @click="commitManual">确认复核</Button>
      </div>
    </Modal>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { reviewDataList } from '../js/reviewDataList'

  export default {
    name: 'wrongDataList',
    mixins: [reviewDataList],
    data() {
      return {
        listConfig: {
          exportTitle: '异常数据'
        },
        ajaxUrl: {
          exportUrl: csAPI.decReviewData.wrong.exportUrl,
          selectAllPaged: csAPI.decReviewData.wrong.selectAllPaged
        }
      }
    },
    mounted: function() {
      this.loadFunctions('error', 190)
    },
    methods: {
      getFields() {
        return [{
          key: 'gno',
          width: 120,
          title: '报关序号'
        }, {
          width: 180,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          width: 120,
          key: 'declareDate',
          title: '报关单申报日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }, {
          width: 150,
          key: 'seqNo',
          title: '报关单统一编号'
        }, {
          width: 200,
          key: 'entryNo',
          title: '报关单号'
        }, {
          width: 180,
          tooltip: true,
          key: 'agentCode',
          title: '申报单位'
        }, {
          width: 120,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        }, {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        }, {
          width: 180,
          key: 'fieldName',
          title: '异常栏位'
        }, {
          width: 200,
          tooltip: true,
          key: 'hgField',
          title: '订阅数据'
        }, {
          width: 200,
          tooltip: true,
          key: 'gwField',
          title: '草单数据'
        }, {
          width: 200,
          tooltip: true,
          key: 'erpInsertUser',
          title: '制单人'
        }]
      },
      doDownload() {
        let me = this
        me.$emit('doDownload', 'wrong')
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }
</style>
