<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </div>
    </XdoCard>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="wrongTab" label="异常数据">
        <WrongDataList ref="wrong" :show-search="showSearch" :ie-mark="ieMark"
                       :search-lines="searchLines" :search-model="searchModel"
                       @doUpdate="doUpdate" @doDownload="doDownload"></WrongDataList>
      </TabPane>
      <TabPane name="correctTab" label="正确数据">
        <CorrectDataList ref="correct" :show-search="showSearch" :ie-mark="ieMark"
                         :search-lines="searchLines" :search-model="searchModel"
                         @doUpdate="doUpdate" @doDownload="doDownload"></CorrectDataList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { decReviewTabs } from './js/decReviewTabs'

  export default {
    name: 'decReviewITabs',
    mixins: [decReviewTabs],
    data() {
      return {
        ieMark: 'I'
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
