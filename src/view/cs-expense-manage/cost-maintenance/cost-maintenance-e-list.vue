<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CostMaintenanceSearch ref="headSearch" :i-e-mark="iEMark" :cmb-source="cmbSource"></CostMaintenanceSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:import>
            <Dropdown trigger="click" style="text-align: center;">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>导入<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="productModule">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  生成模版
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <Upload multiple="true" :action="uploadFileConfig.action" :headers="uploadFileConfig.headers"
                          :show-upload-list="false" :format="['xls','xlsx']" :on-format-error="handleFormatError" :on-success="handleOnSuccess">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  普通导入
                    </XdoButton>
                  </Upload>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CostMaintenanceEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                         :i-e-mark="iEMark" :cmb-source="cmbSource"></CostMaintenanceEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
    <ErrInfor v-if="importShow" @closeModal="closeModal" :importData='importData' :importShow="importShow"></ErrInfor>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import ErrInfor from './errInfor'
  import { costMaintenanceList } from '../js/cost-maintenance/costMaintenanceList'

  export default {
    name: 'costMaintenanceEList',
    components: {
      ErrInfor
    },
    mixins: [costMaintenanceList],
    data() {
      return {
        iEMark: 'E',
        gridConfig: {
          exportTitle: '出口费用维护'
        },
        ajaxUrl: {
          deleteUrl: csAPI.expenseManage.costMaintenance.out.head.delete,
          exportUrl: csAPI.expenseManage.costMaintenance.out.head.exportUrl,
          selectAllPaged: csAPI.expenseManage.costMaintenance.out.head.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
