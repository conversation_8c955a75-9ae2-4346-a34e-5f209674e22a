<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ExpenseAccountSettingSearch ref="headSearch"></ExpenseAccountSettingSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <ExpenseAccountSettingEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                               :cmb-source="cmbSource"></ExpenseAccountSettingEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
    <SyncImport :show.sync="importShow" :uploadConfig="impData" @import:success="onAfterImport"></SyncImport>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { certificate, expenseManage } from '@/view/cs-common'
  import SyncImport from '../components/sync-import/sync-import'
  import { expenseAccountSettingList } from '../js/expense-account-setting/expenseAccountSettingList'

  export default {
    name: 'expenseAccountSettingList',
    mixins: [expenseAccountSettingList],
    components: {
      SyncImport
    },
    data() {
      return {
        // 查询条件行数
        searchLines: 3,
        importShow: false,
        certificate: certificate,
        gridConfig: {
          exportTitle: '固定费用科目'
        },
        expenseManage: expenseManage,
        toolbarEventMap: {
          'import': this.handleImport
        },
        cmbSource: {
          isFixed: [{
            value: '0', label: '固定'
          }, {
            value: '1', label: '非固定'
          }]
        },
        // 导入参数
        impData: {
          importType: 'cost',
          filename: '费用科目',
          uploadUrl: csAPI.expenseManage.expenseAccountSettingNew.importUrl,
          downloadUrl: csAPI.expenseManage.expenseAccountSettingNew.exportTpl
        },
        ajaxUrl: {
          deleteUrl: csAPI.expenseManage.expenseAccountSettingNew.delete,
          exportUrl: csAPI.expenseManage.expenseAccountSettingNew.exportUrl,
          selectAllPaged: csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged
        }
      }
    },
    methods: {
      handleImport() {
        let me = this
        me.$set(me, 'importShow', true)
      },
      /**
       * 导入成功后事件
       */
      onAfterImport() {
        let me = this
        me.$set(me, 'importShow', false)
        me.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
