<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            <XdoButton v-if="searchSetBtnShow" type="warning" class="dc-margin-right" @click="handleSearchSetupShow">设置</XdoButton>
            <XdoButton v-if="detailSetBtnShow" type="warning" class="dc-margin-right" @click="handleDetailSetupShow">设置编辑字段</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch" class="xdo-enter-root" v-focus>
            <div class="separateLine"></div>
            <FormBuilder ref="headSearch" :schema="schema" :items="searchFields" :model="searchParam">
            </FormBuilder>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 26px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalMessage}}</span>
        </div>
      </XdoCard>
    </div>
    <CostMaintenanceEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                         :i-e-mark="iEMark" :detailConfig="myDetailConfig"></CostMaintenanceEdit>
    <FormSetup v-model="searchSetupShow" :resId="searchId" :columns="baseSearchFields"
               @updateColumns="handleUpdateSearch"></FormSetup>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseListFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <FormSetup v-model="detailSetupShow" :resId="detailId" :columns="baseDetailFields"
               @updateColumns="handleUpdateDetail"></FormSetup>
    <SyncImport :show.sync="importShow" :uploadConfig="impData" @import:success="onAfterImport"></SyncImport>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { costMaintenanceNewList } from '../js/cost-maintenance-new/costMaintenanceNewList'

  export default {
    name: 'costMaintenanceNewEList',
    mixins: [costMaintenanceNewList],
    data() {
      return {
        iEMark: 'E',
        gridConfig: {
          exportTitle: '出口费用维护'
        },
        // 导入参数
        impData: {
          importType: 'exp',
          filename: '出口费用',
          uploadUrl: csAPI.expenseManage.costMaintenanceNew.out.head.importUrl,
          downloadUrl: csAPI.expenseManage.costMaintenanceNew.out.head.exportTpl
        },
        ajaxUrl: {
          cutUrl: csAPI.ieParams.CUT,
          forwardUrl: csAPI.ieParams.FOD,
          prdOrCliUrl: csAPI.ieParams.PRD,
          selectComboxByCode: csAPI.ieParams.selectComboxByCode,
          locked: csAPI.expenseManage.costMaintenanceNew.out.head.locked,
          unlocked: csAPI.expenseManage.costMaintenanceNew.out.head.unlocked,
          deleteUrl: csAPI.expenseManage.costMaintenanceNew.out.head.delete,
          exportUrl: csAPI.expenseManage.costMaintenanceNew.out.head.exportUrl,
          selectAllPaged: csAPI.expenseManage.costMaintenanceNew.out.head.selectAllErpPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
