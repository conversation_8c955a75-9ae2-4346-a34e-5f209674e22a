import { namespace } from '@/project'
// 外汇管理
import ExchangeList from './exchange/exchange-list'
// import CostHeadIMain from './costReal/i/costHead/CostHeadIMain'
// 进口费用报表
import ExpenseReportIList from './expense-report/expense-report-i-list'
// 出口费用报表
import ExpenseReportEList from './expense-report/expense-report-e-list'
// 进口费用比对
import costComparisonIList from './cost-comparison/cost-comparison-i-list'
// 出口费用比对
import costComparisonEList from './cost-comparison/cost-comparison-e-list'
// 进口费用维护
import CostMaintenanceIList from './cost-maintenance-new/cost-maintenance-new-i-list'
// 出口费用维护
import CostMaintenanceEList from './cost-maintenance-new/cost-maintenance-new-e-list'
//运保费比对报表
import actualCostCompareReport from './actual-cost-compare-report/actual-cost-compare-report'
// 固定费用科目
import ExpenseAccountSettingList from './expense-account-setting/expense-account-setting-list'
// 非固定发生科目
import expenseAccountSettingUnfixedList from './expense-account-setting-unfixed/expense-account-setting-unfixed-list'

export default [
  {
    path: '/' + namespace + '/expenseManage/expenseAccountSetting',
    name: 'expenseAccountSettingList',
    meta: {
      icon: 'ios-document',
      title: '固定费用科目'
    },
    component: ExpenseAccountSettingList
  },
  {
    path: '/' + namespace + '/expenseManage/expenseAccountSettingUnfixedList',
    name: 'expenseAccountSettingUnfixedList',
    meta: {
      icon: 'ios-document',
      title: '非固定发生科目'
    },
    component: expenseAccountSettingUnfixedList
  },
  {
    path: '/' + namespace + '/expenseManage/costMaintenanceI',
    name: 'costMaintenanceNewIList',
    meta: {
      icon: 'ios-document',
      title: '进口费用维护'
    },
    component: CostMaintenanceIList
  },
  {
    path: '/' + namespace + '/expenseManage/costMaintenanceE',
    name: 'costMaintenanceNewEList',
    meta: {
      icon: 'ios-document',
      title: '出口费用维护'
    },
    component: CostMaintenanceEList
  },
  {
    path: '/' + namespace + '/expenseManage/exchange',
    name: 'exchangeList',
    meta: {
      icon: 'ios-document',
      title: '外汇管理'
    },
    component: ExchangeList
  },
  {
    path: '/' + namespace + '/expenseManage/reporti',
    name: 'expenseReportIList',
    meta: {
      icon: 'ios-document',
      title: '进口费用报表'
    },
    component: ExpenseReportIList
  },
  {
    path: '/' + namespace + '/expenseManage/reporte',
    name: 'expenseReportEList',
    meta: {
      icon: 'ios-document',
      title: '出口费用报表'
    },
    component: ExpenseReportEList
  },
  {
    path: '/' + namespace + '/expenseManage/costComparisonIList',
    name: 'costComparisonIList',
    meta: {
      icon: 'ios-document',
      title: '进口费用比对'
    },
    component: costComparisonIList
  },
  {
    path: '/' + namespace + '/expenseManage/costComparisonEList',
    name: 'costComparisonEList',
    meta: {
      icon: 'ios-document',
      title: '出口费用比对'
    },
    component: costComparisonEList
  },
  {
    path: '/' + namespace + '/expenseManage/actualCostReport',
    name: 'actualCostReport',
    meta: {
      icon: 'ios-document',
      title: '运保费比对表'
    },
    component: actualCostCompareReport
  }
]
