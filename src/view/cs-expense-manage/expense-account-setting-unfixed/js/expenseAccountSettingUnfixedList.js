import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import {certificate, expenseManage, expenseManager } from '@/view/cs-common'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const expenseAccountSettingUnfixedList = {
  name: 'expenseAccountSettingUnfixedList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        iemark: certificate.I_E_MARK,
        courseType: expenseManager.courseType,
        validStatus: expenseManage.VALID_STATUS_MAP,
        status: certificate.EXPENSE_ACCOUNT_STATUS_MAP
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [
        {
          key: 'iemark',
          type: 'select',
          title: '费用类型'
        }, {
          type: 'select',
          key: 'courseType',
          title: '费用科目大类'
        }, {
          title: '费用科目代码',
          key: 'costCourseCode'
        }, {
          title: '费用科目名称',
          key: 'costCourseName'
        }, {
          key: 'status',
          type: 'select',
          title: '费用科目状态'
        }, {
          type: 'select',
          title: '有效状态',
          key: 'validStatus'
        }
      ]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['isFixed'] = '1'
      return paramObj
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'status',
        title: '费用科目状态',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        key: 'courseType',
        title: '费用科目大类',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.courseType)
        })
      }, {
        width: 200,
        title: '费用科目代码',
        key: 'costCourseCode'
      }, {
        width: 250,
        title: '费用科目名称',
        key: 'costCourseName'
      }, {
        width: 200,
        key: 'iemark',
        title: '费用类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.iemark)
        })
      }, {
        width: 90,
        title: '有效状态',
        key: 'validStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.validStatus)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    }
  }
}
