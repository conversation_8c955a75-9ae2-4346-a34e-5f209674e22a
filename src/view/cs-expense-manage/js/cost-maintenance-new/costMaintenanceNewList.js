import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commFields } from '../comm/commFields'
import SyncImport from '../../components/sync-import/sync-import'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'
import { editStatus, expenseManage, importExportManage } from '@/view/cs-common'
import { ArrayToLocaleLowerCase, ArrayToLocaleLowerCaseAll, isNumber } from '@/libs/util'
import CostMaintenanceEdit from '../../components/cost-maintenance-new/cost-maintenance-new-edit'

export const costMaintenanceNewList = {
  mixins: [commFields, baseColumns, pms],
  components: {
    SyncImport,
    CostMaintenanceEdit
  },
  data() {
    return {
      // 通用字段
      baseFields: [
        {
          width: 90,
          key: 'status',
          type: 'select',
          title: '维护状态'
        },
        {
          width: 90,
          key: 'costStatus',
          type: 'select',
          title: '费用状态'
        },
        {
          width: 60,
          title: '附件',
          key: 'attachmentCount',
          render: (h, params) => {
            let count = params.row['attachmentCount']
            if (isNumber(count) && Number(count) > 0) {
              return h('span', {
                style: {
                  color: 'green',
                  fontWeight: 'bold',
                  paddingLeft: '10px'
                }
              }, '有')
            }
            return h('span', {
              style: {
                color: 'gray',
                paddingLeft: '10px'
              }
            }, '无')
          }
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号',
          belong: 'decErpHead',
          props: {
            disabled: true
          }
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          key: 'batchNo',
          title: '批次号',
        },
        {
          width: 130,
          type: 'datePicker',
          key: 'erpInsertTime',
          belong: 'decErpHead',
          title: '预录入单制单日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 160,
          key: 'payNo',
          type: 'input',
          tooltip: true,
          title: '账单编号'
        },
        {
          width: 120,
          type: 'select',
          key: 'remitteeType',
          title: '收款单位类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.remitteeType)
          }
        },
        {
          width: 120,
          type: 'select',
          title: '收款单位',
          key: 'remitteeCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.remitteeCode)
          }
        },
        {
          width: 250,
          tooltip: true,
          type: 'input',
          title: '收款单位',
          key: 'remitteeName',
          keyField: 'remitteeCode',
          valueField: 'remitteeName'
        },
        {
          width: 250,
          type: 'input',
          tooltip: true,
          key: 'entryNo',
          title: '报关单号',
          belong: 'decErpHead'
        },
        {
          width: 120,
          type: 'pcode',
          key: 'trafMode',
          title: '运输方式',
          belong: 'decErpHead',
          props: {
            meta: 'TRANSF' // this.pcode.transf
          }
        },
        {
          width: 160,
          type: 'select',
          title: '货运代理',
          key: 'forwardCode',
          belong: 'decErpHead',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.forwardCodeData)
          }
        },
        {
          width: 250,
          type: 'select',
          title: '报关行',
          key: 'declareCode'
        },
        {
          width: 180,
          type: 'input',
          tooltip: true,
          key: 'rmbSumTotal',
          belong: 'decErpHead',
          title: '人民币费用总额'
        },
        {
          width: 120,
          type: 'pcode',
          key: 'wrapType',
          title: '包装种类',
          belong: 'decErpHead',
          props: {
            meta: 'WRAP' // this.pcode.wrap
          }
        },
        {
          width: 150,
          tooltip: true,
          title: '总毛重',
          key: 'grossWt',
          belong: 'decErpHead'
        },
        {
          width: 150,
          key: 'netWt',
          tooltip: true,
          title: '总净重',
          belong: 'decErpHead'
        },
        {
          width: 130,
          type: 'select',
          title: '贸易条款',
          key: 'tradeTerms',
          showType: 'single',
          belong: 'decErpHead'
        },
        {
          width: 150,
          key: 'ieport',
          tooltip: true,
          type: 'pcode',
          title: '进境关别',
          belong: 'decErpHead',
          props: {
            meta: 'CUSTOMS_REL' // this.pcode.customs_rel
          }
        },
        {
          width: 180,
          tooltip: true,
          type: 'pcode',
          key: 'tradeCountry',
          title: '启运国(地区)',
          belong: 'decErpHead',
          props: {
            meta: 'COUNTRY_OUTDATED' // this.pcode.country_outdated
          }
        },
        {
          width: 150,
          tooltip: true,
          type: 'pcode',
          props: {
            meta: 'CUSTOMS_REL' // this.pcode.customs_rel
          },
          title: '申报地海关',
          belong: 'decErpHead',
          key: 'masterCustoms'
        },
        {
          type: 'select',
          title: '境外收发货人',
          key: 'overseasShipper'
        },
        {
          width: 180,
          tooltip: true,
          title: '境外收发货人',
          belong: 'decErpHead',
          key: 'overseasShipperName'
        },
        {
          width: 280,
          tooltip: true,
          key: 'erpNote',
          belong: 'decErpHead',
          title: '预录入单表头备注'
        },
        {
          width: 150,
          tooltip: true,
          title: '总金额',
          key: 'sumTotal',
          belong: 'decErpHead'
        },
        {
          width: 150,
          key: 'sumQty',
          tooltip: true,
          title: '总数量',
          belong: 'decErpHead'
        },
        {
          width: 150,
          key: 'curr',
          title: '币制',
          tooltip: true,
          type: 'pcode',
          props: {
            meta: 'CURR_OUTDATED' // this.pcode.curr_outdated
          }
        },
        {
          width: 180,
          tooltip: true,
          type: 'input',
          title: '货运代理',
          key: 'forwardName',
          belong: 'decErpHead',
          keyField: 'forwardCode',
          valueField: 'forwardName'
        },
        {
          width: 160,
          tooltip: true,
          type: 'input',
          key: 'cweight',
          title: '计费重量',
          belong: 'decErpHead'
        },
        {
          width: 260,
          tooltip: true,
          type: 'input',
          title: '报关行',
          key: 'declareName',
          belong: 'decErpHead',
          keyField: 'declareCode',
          valueField: 'declareName'
        },
        {
          width: 160,
          key: 'hawb',
          tooltip: true,
          title: '提运单号',
          belong: 'decErpHead'
        },
        {
          width: 150,
          title: '件数',
          tooltip: true,
          key: 'packNum',
          belong: 'decErpHead'
        },
        {
          width: 180,
          key: 'gname',
          tooltip: true,
          type: 'input',
          title: '主要商品名称',
          belong: 'decErpHead'
        },
        {
          width: 160,
          tooltip: true,
          type: 'input',
          title: '发票号',
          key: 'billInvoiceNo',
          belong: 'decErpHead'
        },
        {
          width: 160,
          tooltip: true,
          type: 'input',
          title: '供应商',
          key: 'supplier',
          belong: 'decErpHead'
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          key: 'invoiceNo',
          title: '费用发票号'
        },
        {
          width: 180,
          type: 'input',
          tooltip: true,
          key: 'applyNo',
          title: '请款单号'
        },
        {
          width: 100,
          title: '请款日期',
          key: 'applyDate',
          type: 'datePicker',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 100,
          key: 'payDate',
          title: '付款日期',
          type: 'datePicker',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          title: '确认单1',
          key: 'confirm1'
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          title: '确认单2',
          key: 'confirm2'
        },
        {
          width: 200,
          key: 'note',
          title: '备注',
          type: 'input',
          tooltip: true
        },
        {
          width: 250,
          type: 'input',
          tooltip: true,
          title: '维护人',
          props: {
            disabled: true
          },
          key: 'updateUser'
        },
        {
          width: 150,
          tooltip: true,
          type: 'input',
          title: '维护人',
          props: {
            disabled: true
          },
          key: 'updateUserName'
        },
        {
          width: 136,
          props: {
            disabled: true
          },
          title: '维护日期',
          key: 'updateTime',
          type: 'datePicker',
          itemType: 'datetime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 90,
          key: 'locked',
          type: 'select',
          title: '是否锁定',
          props: {
            disabled: true
          }
        },
        // 以下暂未使用
        {
          key: 'sid',
          width: 100,
          title: '主键',
          type: 'input',
          tooltip: true
        },
        {
          width: 180,
          type: 'input',
          tooltip: true,
          title: '清单SID',
          key: 'billHeadId'
        },
        {
          width: 100,
          title: '创建日期',
          key: 'insertTime',
          type: 'datePicker',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          tooltip: true,
          type: 'input',
          title: '创建人',
          key: 'insertUser'
        },
        {
          width: 160,
          tooltip: true,
          type: 'input',
          title: '创建人',
          key: 'insertUserName',
          keyField: 'insertUser',
          valueField: 'insertUserName'
        },
        {
          width: 90,
          type: 'select',
          key: 'payType',
          title: '是否付汇'
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          key: 'tradeCode',
          title: '所属企业编码'
        },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          title: '用户名',
          key: 'userName',
          align: 'center'
        },
        {
          type: 'pcode',
          title: '成交方式',
          key: 'transMode',
          belong: 'decErpHead',
          props: {
            meta: 'TRANSAC'
          }
        }, {
          title: '运费',
          key: '1111',
          belong: 'decErpHead',
          children: [
            {
              width: 80,
              title: '类型',
              tooltip: true,
              key: 'feeMark',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.dynamicSource.feeMark)
              }
            },
            {
              width: 80,
              title: '费率',
              tooltip: true,
              key: 'feeRate'
            },
            {
              width: 150,
              title: '币制',
              tooltip: true,
              key: 'feeCurr',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        }, {
          title: '保费',
          key: '2222',
          belong: 'decErpHead',
          children: [
            {
              width: 80,
              title: '类型',
              tooltip: true,
              key: 'insurMark',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.dynamicSource.feeMark)
              }
            },
            {
              width: 80,
              title: '费率',
              tooltip: true,
              key: 'insurRate'
            },
            {
              width: 150,
              title: '币制',
              tooltip: true,
              key: 'insurCurr',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        },{
          title: '杂费',
          key: '3333',
          belong: 'decErpHead',
          children: [
            {
              width: 80,
              title: '类型',
              tooltip: true,
              key: 'otherMark',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.dynamicSource.feeMark)
              }
            },
            {
              width: 80,
              title: '费率',
              tooltip: true,
              key: 'otherRate'
            },
            {
              width: 150,
              title: '币制',
              tooltip: true,
              key: 'otherCurr',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        }, {
          title: '实际运费',
          children: [
            {
              width: 80,
              title: '类型',
              tooltip: true,
              key: 'actualFeeMark',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.dynamicSource.feeMark)
              }
            },
            {
              width: 80,
              title: '费率',
              tooltip: true,
              key: 'actualFeeRate'
            },
            {
              width: 150,
              title: '币制',
              tooltip: true,
              key: 'actualFeeCurr',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        }, {
          title: '实际保费',
          children: [
            {
              width: 80,
              title: '类型',
              tooltip: true,
              key: 'actualInsurMark',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.dynamicSource.feeMark)
              }
            },
            {
              width: 80,
              title: '费率',
              tooltip: true,
              key: 'actualInsurRate'
            },
            {
              width: 150,
              title: '币制',
              tooltip: true,
              key: 'actualInsurCurr',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        }, {
          width: 150,
          key: 'isAgreement',
          type: 'select',
          title: '运保费是否一致',
          props: {
            disabled: true
          }
        },
        {
          width: 130,
          type: 'input',
          tooltip: true,
          title: '实际运保费备注',
          key: 'actualNote',
          align: 'center'
        },
        {
          width: 130,
          type: 'input',
          tooltip: true,
          title: '账单提单号',
          key: 'billDeliveryId',
          align: 'center'
        }, {
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'billPackNum',
          type: 'xdoInput',
          title: '账单总件数'
        }, {
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'billVolume',
          type: 'xdoInput',
          title: '账单总体积'
        }, {
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'billSumDecTotal',
          type: 'xdoInput',
          title: '账单总进口金额'
        }, {
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'billGrossWt',
          type: 'xdoInput',
          title: '账单总毛重'
        }
      ],
      searchRemittee: [],
      toolbarEventMap: {
        'add': this.handleAdd,
        'checkConfirm': this.checkConfirm,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload,
        'lock': this.handleLock,
        'unlock': this.handleUnLock,
        'setting': this.handleListSetupShow
      },
      schema: {
        titleWidth: 120,
        class: 'dc-form-4'
      },
      dynamicSource: {
        clientInfo: [],
        // 收款单位类型
        remitteeType: [
          { value: '1', label: '货代' },
          { value: '2', label: '报关行' },
          { value: '3', label: '供应商' }
        ],
        // 收款单位代码
        remitteeCode: [],
        remitteeCodeSource: [],

        cutData: [],
        prdOrCliObj: {},
        prdOrCliData: [],
        sourceLoadCount: 0,
        forwardCodeData: [],

        remitteeCodeData: [],
        tradeTermNewData: [],
        overseasShipperData: [],
        tradeTermList: importExportManage.tradeTermList,
        feeMark: importExportManage.feeTypeMap
      },
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      totalMessage: '',
      importShow: false,
      needDetailFields: true,
      searchSetBtnShow: false,
      detailSetBtnShow: false
    }
  },
  created: function() {
    let me = this
    // 供应商、货代、报关行
    me.$http.post(me.ajaxUrl.selectComboxByCode + '/PRD,FOD,CUT').then(res => {
      let tmpResult = ArrayToLocaleLowerCaseAll(res.data.data),
        firstLevel = {}, children = [], result = [], prdOrCliObj = {}
      if (Array.isArray(tmpResult) && tmpResult.length > 0) {
        tmpResult.forEach(item => {
          if (!firstLevel.hasOwnProperty(item.type)) {
            firstLevel[item.type] = item['type_name']
          }
        })
        Object.keys(firstLevel).forEach(type => {
          children = tmpResult.filter(item => {
            return item.type === type
          }).map(vl => {
            return {
              label: vl.label,
              value: vl.value
            }
          })
          result.push({
            value: type,
            label: firstLevel[type],
            children: children
          })
        })
        me.$set(me.dynamicSource, 'clientInfo', result)
        let cutArray = result.filter(item => {
            return item.value === 'CUT'
          }),
          forwardArray = result.filter(item => {
            return item.value === 'FOD'
          }),
          cutData = [],
          forwardData = []
        if (Array.isArray(cutArray) && cutArray.length > 0) {
          cutData = cutArray[0].children
        }
        if (Array.isArray(forwardArray) && forwardArray.length > 0) {
          forwardData = forwardArray[0].children
        }
        me.$set(me.dynamicSource, 'cutData', cutData)
        me.$set(me.dynamicSource, 'forwardCodeData', forwardData)
        tmpResult.forEach(item => {
          if (!prdOrCliObj.hasOwnProperty(item.value)) {
            prdOrCliObj[item.value] = item.label
          }
        })
        me.$set(me.dynamicSource, 'prdOrCliObj', prdOrCliObj)
        me.$set(me.dynamicSource, 'remitteeCodeSource', tmpResult.map(item => {
          let type = ''
          if (item.type === 'PRD') {
            type = '3'
          } else if (item.type === 'CUT') {
            type = '2'
          } else if (item.type === 'FOD') {
            type = '1'
          }
          return {
            type: type,
            label: item.label,
            value: item.value
          }
        }))
      }
    }).catch(() => {
      me.$set(me.dynamicSource, 'cutData', [])
      me.$set(me.dynamicSource, 'clientInfo', [])
      me.$set(me.dynamicSource, 'prdOrCliObj', {})
      me.$set(me.dynamicSource, 'forwardCodeData', [])
      me.$set(me.dynamicSource, 'remitteeCodeSource', [])
    }).finally(() => {
      me.resetDynamicSource('declareCode', me.dynamicSource.cutData)
      me.resetDynamicSource('remitteeCode', [])
      me.resetDynamicSource('remitteeType', me.dynamicSource.remitteeType)
      me.resetDynamicSource('forwardCode', me.dynamicSource.forwardCodeData)
    })

    // 贸易条款
    me.pcodeList(me.pcode.transac).then(() => {
      me.dynamicSource.tradeTermNewData = [...me.dynamicSource.tradeTermList]
    }).catch(() => {
      me.dynamicSource.tradeTermNewData = me.dynamicSource.tradeTermList
    }).finally(() => {
      me.resetDynamicSource('tradeTerms', me.dynamicSource.tradeTermNewData)
    })

    if (me.iEMark === 'I') {
      // 境外发货人
      me.$http.post(csAPI.ieParams.PRD).then(res => {
        me.$set(me.dynamicSource, 'overseasShipperData', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.dynamicSource, 'overseasShipperData', [])
      }).finally(() => {
        me.resetDynamicSource('overseasShipper', me.dynamicSource.overseasShipperData)
      })
    } else if (me.iEMark === 'E') {
      // 境外收货人
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.$set(me.dynamicSource, 'overseasShipperData', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.dynamicSource, 'overseasShipperData', [])
      }).finally(() => {
        me.resetDynamicSource('overseasShipper', me.dynamicSource.overseasShipperData)
      })
    }
  },
  mounted: function() {
    let me = this
    me.actionLoadMethods()
  },
  computed: {
    ieportLabel() {
      if (this.iEMark === 'I') {
        return '进境关别'
      } else if (this.iEMark === 'E') {
        return '出境关别'
      } else {
        return '进/出境关别'
      }
    },
    tradeCountryLabel() {
      if (this.iEMark === 'I') {
        return '启运国'
      } else if (this.iEMark === 'E') {
        return '运抵国'
      } else {
        return '启/运抵国'
      }
    },
    overseasShipperLabel() {
      if (this.iEMark === 'I') {
        return '境外发货人'
      } else if (this.iEMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收/发货人'
      }
    },
    overseasShipperNameLabel() {
      if (this.iEMark === 'I') {
        return '境外发货人'
      } else if (this.iEMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收/发货人'
      }
    },
    supplierLabel() {
      if (this.iEMark === 'I') {
        return '供应商'
      } else if (this.iEMark === 'E') {
        return '客户'
      } else {
        return '客户/供应商'
      }
    },
    billSumDecTotalLabel() {
      if (this.iEMark === 'I') {
        return '账单总进口金额'
      } else if (this.iEMark === 'E') {
        return '账单总出口金额'
      }
    },
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {
        ieport: this.ieportLabel,
        supplier: this.supplierLabel,
        tradeCountry: this.tradeCountryLabel,
        overseasShipper: this.overseasShipperLabel,
        overseasShipperName: this.overseasShipperNameLabel,
        billSumDecTotal: this.billSumDecTotalLabel
      }
    },
    /**
     * 数据源
     */
    cmbSource() {
      return {
        'status': expenseManage.STATUS_MAP,
        'costStatus': expenseManage.STATUS_MAP2,
        'locked': expenseManage.LOCKED_MAP,
        'payType': expenseManage.PAY_TYPE_MAP,
        'isAgreement': expenseManage.PAY_TYPE_MAP
      }
    },
    /**
     * 详细信息配置
     */
    myDetailConfig() {
      let me = this,
        costData = {},
        decFields = [],
        costFields = [],
        decData = {
          rmbSumTotal: null
        }
      me.detailConfig.detailFields.forEach(item => {
        if (item.key === 'note') {
          item.itemClass = 'dc-merge-3-5'
        }
        if (item.belong === 'decErpHead') {
          decFields.push(item)
          decData[item.key] = me.detailConfig.frmData[item.key]
          if (item.key === 'emsListNo') {
            costFields.push(item)
            costData[item.key] = me.detailConfig.frmData[item.key]
          }
        } else {
          costFields.push(item)
          costData[item.key] = me.detailConfig.frmData[item.key]
        }
      })
      let accountFields = me.getAccountFields()
      let tmpListFields = ['emsListNo']
      accountFields.forEach(item => {
        tmpListFields.push(item)
      })
      let costListFields = me.baseFields.filter(field => {
        return tmpListFields.includes(field.key)
      })
      return {
        decData: decData,
        decFields: decFields,
        costData: costData,
        costFields: costFields,
        clientInfo: me.dynamicSource.clientInfo,
        prdOrCliObj: me.dynamicSource.prdOrCliObj,
        remitteeCodeSource: me.dynamicSource.remitteeCodeSource,
        costListFields: me.setCmbSource4List(costListFields, me.cmbSource)
      }
    }
  },
  watch: {
    'searchParam.remitteeType': {
      handler: function(type) {
        let me = this
        me.$set(me.searchParam, 'remitteeCode', '')
        me.$set(me.dynamicSource, 'remitteeCode', me.dynamicSource.remitteeCodeSource.filter(item => item.type === type))
        me.resetDynamicSource('remitteeCode', me.dynamicSource.remitteeCode)
      }
    }
  },
  methods: {
    async checkConfirm(){
      let me =this;
      let sids=[]
      for (let row of me.gridConfig.selectRows) {
        let sid = row.sid;
        if (sid) {
          sids.push(sid)
        }
      }
      console.log(me.gridConfig.selectRows)
      // sids.push(sid)
      await me.$http.post(csAPI.sapErp.batch.head.checkConfirm,{
        sids: sids
      }).then( ()=> {
        me.$Message.success("保存成功")
      })
      this.getList()
    },
    actionLoadMethods() {
      let me = this
      me.loadFunctions().then(() => {
        /*if (typeof me.actionLoad === "function") {
          me.actionLoad()
        }*/
      })
    },
    actionLoad() {
      let me = this
      me.actions = []
      me.actions.push({
        ...me.actionsComm,
        label: '新增',
        command: 'add',
        icon: 'ios-add',
        key: 'xdo-btn-add'
      }, {
        ...me.actionsComm,
        label: '编辑',
        command: 'edit',
        key: 'xdo-btn-edit',
        icon: 'ios-create-outline'
      },
      //   {
      //   ...me.actionsComm,
      //   label: '结帐确认',
      //   command: 'checkConfirm',
      //   key: 'xdo-btn-edit',
      //   icon: 'ios-create-outline'
      // },
        {
        ...me.actionsComm,
        label: '删除',
        command: 'delete',
        key: 'xdo-btn-delete',
        icon: 'ios-trash-outline'
      }, {
        ...me.actionsComm,
        label: '导入',
        command: 'import',
        key: 'xdo-btn-upload',
        icon: 'ios-cloud-upload-outline'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      }, {
        ...me.actionsComm,
        label: '锁定',
        command: 'lock',
        key: 'xdo-btn-delete',
        icon: 'ios-lock-outline'
      }, {
        ...me.actionsComm,
        label: '解锁',
        command: 'unlock',
        key: 'xdo-btn-edit',
        icon: 'ios-unlock-outline'
      }, {
        ...me.actionsComm,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting',
        key: 'xdo-btn-setting'
      })
    },
    /**
     * 获取基础查询条件字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseSearchFields() {
      return ['status','costStatus','batchNo', 'emsListNo', 'erpInsertTime', 'payNo', 'remitteeType', 'remitteeCode', 'entryNo', 'trafMode', 'forwardCode', 'declareCode',
        'confirm1', 'confirm2', 'applyNo', 'note', 'applyDate', 'payDate', 'hawb', 'tradeTerms', 'overseasShipper', 'isAgreement', 'billDeliveryId']
    },
    /**
     * 获取基础显示列字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseListFields() {
      return ['status','costStatus','batchNo', 'attachmentCount', 'emsListNo', 'payNo', 'remitteeType', 'remitteeName', 'rmbSumTotal', 'entryNo',
        'erpInsertTime', 'trafMode', 'wrapType', 'grossWt', 'netWt', 'tradeTerms', 'ieport', 'tradeCountry', 'masterCustoms',
        'overseasShipperName', 'erpNote', 'billInvoiceNo', 'supplier', 'sumTotal', 'sumQty', 'curr', 'forwardCode', 'cweight',
        'declareName', 'hawb', 'packNum', 'gname', 'invoiceNo', 'applyNo', 'applyDate', 'payDate', 'confirm1', 'confirm2',
        'note', 'updateUser', 'updateTime', 'locked', 'transMode', 'feeMark', 'feeCurr', 'feeRate', 'insurRate', 'insurCurr',
        'insurMark', 'otherMark','otherRate', 'otherCurr', 'actualFeeRate', 'actualFeeCurr', 'actualFeeMark', 'actualInsurRate', 'actualInsurCurr', 'actualInsurMark',
        'isAgreement', 'actualNote', 'billDeliveryId', 'billPackNum', 'billVolume', 'billSumDecTotal', 'billGrossWt']
    },
    /**
     * 获取费用字段
     */
    getAccountFields() {
      return ['payNo', 'remitteeType', 'remitteeCode', 'remitteeName', 'invoiceNo', 'applyNo', 'applyDate', 'payDate',
        'confirm1', 'confirm2', 'note', 'locked', 'updateUser', 'updateTime', 'billDeliveryId', 'billPackNum', 'billVolume', 'billSumDecTotal', 'billGrossWt']
    },
    /**
     * 获取基础编辑字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseDetailFields() {
      return [
        // 提单信息
        'emsListNo', 'rmbSumTotal', 'entryNo', 'erpInsertTime', 'trafMode', 'wrapType', 'grossWt', 'netWt', 'sumQty', 'tradeTerms',
        'ieport', 'tradeCountry', 'masterCustoms', 'overseasShipperName', 'gname', 'erpNote', 'forwardCode', 'cweight', 'declareName',
        'hawb', 'packNum', 'billInvoiceNo', 'supplier', 'sumTotal', 'transMode', '1111', '2222', '3333',
        // 费用表头信息
        ...this.getAccountFields()
      ]
    },
    /**
     * 获取默认编辑字段(需外部覆盖)
     * @returns {Array}
     */
    getDefaultDetailFields() {
      return [
        // 提单信息
        'emsListNo', 'entryNo', 'erpInsertTime', 'trafMode', 'wrapType', 'packNum', 'grossWt', 'netWt',
        'tradeTerms', 'ieport', 'tradeCountry', 'masterCustoms', 'overseasShipperName', 'supplier', 'hawb', 'gname',
        'forwardCode', 'billInvoiceNo', 'sumQty', /*'rmbSumTotal',*/'sumTotal', 'cweight', 'declareName', 'erpNote',
        'transMode', '1111', '2222', '3333',
        // 费用表头信息
        ...this.getAccountFields()
      ]
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      if (me.checkRowSelected('新增', true)) {
        let row = me.gridConfig.selectRows[0]
        if (me.customCheck([row], '新增')) {
          me.editConfig.editStatus = editStatus.ADD
          me.editConfig.editData = row
          me.showList = false
        }
      }
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleImport() {
      let me = this
      me.importShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.importShow = false
      me.getList()
    },
    /**
     * Excel导出
     */
    handleDownload() {
      let me = this
      me.gridConfig.exportColumns = me.gridConfig.exportColumns.map(col => {
        if (col.key === 'forwardCode') {
          col.key = 'forwardName'
        }
        return col
      })
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 是否包含未维护项
     */
    hasUnProtects() {
      let me = this,
        unProtects = me.gridConfig.selectRows.filter(item => {
          return item.status !== '1'
        })
      return Array.isArray(unProtects) && unProtects.length > 0
    },
    /**
     * 格锁定
     */
    handleLock() {
      let me = this
      if (me.hasUnProtects()) {
        me.$Message.warning('未维护的数据不能执行锁定操作!')
        return
      }
      if (me.checkRowSelected('锁定')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          loading: true,
          cancelText: '取消',
          content: '确认锁定所选项吗',
          onOk: () => {
            me.setButtonLoading('lock', true)
            let params = me.getSelectedParams()
            me.$http.put(`${me.ajaxUrl.locked}/${params}`).then(() => {
              me.$Message.success('锁定成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              setTimeout(() => {
                me.setButtonLoading('lock', false)
                me.$Modal.remove()
              }, 150)
            })
          }
        })
      }
    },
    /**
     * 解锁
     */
    handleUnLock() {
      let me = this
      if (me.hasUnProtects()) {
        me.$Message.warning('未维护的数据不能执行解锁操作!')
        return
      }
      if (me.checkRowSelected('解锁')) {
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确认',
          cancelText: '取消',
          content: '确认解锁所选项吗',
          onOk: () => {
            me.setButtonLoading('unlock', true)
            let params = me.getSelectedParams()
            me.$http.put(`${me.ajaxUrl.unlocked}/${params}`).then(() => {
              me.$Message.success('解锁成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              setTimeout(() => {
                me.setButtonLoading('unlock', false)
                me.$Modal.remove()
              }, 150)
            })
          }
        })
      }
    },
    /**
     * 执行查询
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.tableloading = true
      me.$nextTick(() => {
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.tableloading = false
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          if (res.data.code === 200 && res.data.message) {
            me.totalMessage = res.data.message
          }
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
        })
      })
    }
  }
}
