import { isNullOrEmpty } from '@/libs/util'
import { getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const commFields = {
  mixins: [commList, baseColumns],
  data() {
    return {
      /**
       * 通用信息
       */
      extendId: '',
      baseFields: [],           //  基础(所有)字段
      formColumns: 4,           //  每行组件数量
      dynamicSource: {},        //  动态数据源
      /**
       * 查询字段
       */
      searchId: '',
      searchParam: {},
      searchFields: [],
      tmpSearchFields: [],
      baseSearchFields: [],
      searchSetupShow: false,
      needSearchFields: true,
      searchSetBtnShow: false,
      searchDisabledFields: [],
      /**
       * 列表字段
       */
      listId: '',
      listData: {},
      listFields: [],
      tmpListFields: [],
      baseListFields: [],
      listSetupShow: false,
      isGridDisable: false,        // 列是否包含编辑按钮
      hasOptionColumn: true,       // 包含操作列
      needListFields: true,
      /**
       * 编辑字段
       */
      detailId: '',
      frmData: {},
      detailFields: [],
      tmpDetailFields: [],
      baseDetailFields: [],
      detailSetupShow: false,
      detailSetBtnShow: false,
      needDetailFields: false
    }
  },
  watch: {
    tmpSearchFields: {
      deep: true,
      handler: function (fields) {
        let me = this
        if (me.needSearchFields) {
          let tmpSearchFields = me.setCmbSource4Components(fields, me.cmbSource)
          me.$set(me, 'searchFields', tmpSearchFields.map(field => {
            if (me.searchDisabledFields.includes(field.key)) {
              if (field.props) {
                field.props.disabled = true
              } else {
                field.props = {
                  disabled: true
                }
              }
            } else {
              if (field.props) {
                field.props.disabled = false
              } else {
                field.props = {
                  disabled: false
                }
              }
            }
            return field
          }))
          me.$nextTick(() => {
            if (me.searchFields) {
              me.searchLines = Math.ceil(me.searchFields.length / me.formColumns)
            }
            Object.keys(me.searchParam).forEach(property => {
              if (me.defaultParamValues.hasOwnProperty(property)) {
                me.$set(me.searchParam, property, me.defaultParamValues[property])
              }
            })
          })
        }
      }
    },
    tmpDetailFields: {
      deep: true,
      handler: function (fields) {
        let me = this
        if (me.needDetailFields) {
          me.$set(me, 'detailFields', me.setCmbSource4Components(fields, me.cmbSource))
          me.$nextTick(() => {
            Object.keys(me.frmData).forEach(property => {
              if (me.defaultValue.hasOwnProperty(property)) {
                me.$set(me.frmData, property, me.defaultValue[property])
              }
            })
          })
        }
      }
    }
  },
  created: function () {
    let me = this
    me.pageInit()
  },
  mounted: function () {
    let me = this
    if (me.needSearchFields) {
      me.handleUpdateSearch()
    }
    if (me.needListFields) {
      me.setListColumns()
    }
    if (me.needDetailFields) {
      me.handleUpdateDetail()
    }
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    },
    /**
     * 数据源
     */
    cmbSource() {
      return {}
    },
    /**
     * 默认值(查询)
     * @returns {{}}
     */
    defaultParamValues() {
      return {}
    },
    /**
     * 默认值(编辑)
     * @returns {{}}
     */
    defaultValue() {
      return {}
    },
    /**
     * 详细信息配置
     */
    detailConfig() {
      return {
        frmData: this.frmData,
        detailFields: this.detailFields
      }
    }
  },
  methods: {
    /**
     * 设置动态列名称
     * @param fields
     */
    setDynamicLabel(fields) {
      let me = this
      return fields.map(field => {
        if (me.dynamicLabel.hasOwnProperty(field.key)) {
          field.title = me.dynamicLabel[field.key]
        }
        return field
      })
    },
    /**
     * 界面初始化
     */
    pageInit() {
      let me = this
      let rootId = me.$route.path + '/' + me.$options.name
      if (!isNullOrEmpty(me.extendId)) {
        rootId += '/' + me.extendId
      }
      let labelValidFields = me.setDynamicLabel(me.baseFields)
      if (me.needSearchFields) {
        me.$set(me, 'searchId', rootId + '/searchId')
        let baseSearchFields = me.getBaseSearchFields()
        me.$set(me, 'baseSearchFields', labelValidFields.filter(field => {
          return baseSearchFields.includes(field.key)
        }))
      }
      if (me.needListFields) {
        me.$set(me, 'listId', rootId + '/listId')
        let baseListFields = me.getBaseListFields()
        me.$set(me, 'baseListFields', labelValidFields.filter(field => {


          return baseListFields.includes(field.key) || ['运费', '保费', '杂费', '实际运费','实际保费'].includes(field.title)
        }))
      }
      if (me.needDetailFields) {
        me.$set(me, 'detailId', rootId + '/detailId')
        let baseDetailFields = me.getBaseDetailFields()
        me.$set(me, 'baseDetailFields', labelValidFields.filter(field => {
          return baseDetailFields.includes(field.key)
        }))
      }
    },
    /**
     * 获取基础查询条件字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseSearchFields() {
      return []
    },
    /**
     * 获取默认查询条件字段(需外部覆盖)
     * @returns {Array}
     */
    getDefaultSearchFields() {
      return []
    },
    /**
     * 获取基础显示列字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseListFields() {
      return []
    },
    /**
     * 获取默认显示列字段(需外部覆盖)
     * @returns {Array}
     */
    getDefaultListFields() {
      return []
    },
    /**
     * 获取基础编辑字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseDetailFields() {
      return []
    },
    /**
     * 获取默认编辑字段(需外部覆盖)
     * @returns {Array}
     */
    getDefaultDetailFields() {
      return []
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return this.searchParam
    },
    /**
     * 获取最终要显示的Form字段
     * @param formId
     * @param fields
     * @param defaultFields
     * @param isSearchForm
     */
    getShowFields(formId, fields, defaultFields, isSearchForm = false) {
      let me = this
      let showFields = me.$bom3.userCustom('form', formId, fields, defaultFields) || []
      if (isSearchForm) {
        showFields = showFields.map(field => {
          if (field.type === 'datePicker') {
            field.type = 'dateRange'
            let fromKey = field.key + 'From'
            let toKey = field.key + 'To'
            field.fields = [{key: fromKey}, {key: toKey}]
          }
          return field
        })
      }
      let data = {}
      showFields.forEach(item => {
        let itemType = item.type
        if (Array.isArray(item.fields)) {
          item.fields.forEach(field => {
            if (itemType === 'xdoInput') {
              data[field.key] = null
            } else {
              data[field.key] = ''
            }
          })
        } else {
          if (itemType === 'xdoInput') {
            data[item.key] = null
          } else {
            data[item.key] = ''
          }
        }
      })
      return {
        fields: showFields,
        data: data
      }
    },
    /**
     * 设置查询条件自定义弹出框
     */
    handleSearchSetupShow() {
      let me = this
      me.$set(me, 'searchSetupShow', true)
    },
    /**
     * 更新查询条件
     */
    handleUpdateSearch() {
      let me = this
      let defaultFields = me.getDefaultSearchFields()
      if (!(Array.isArray(defaultFields) && defaultFields.length > 0)) {
        defaultFields = me.getBaseSearchFields()
      }
      let showFieldsAndData = me.getShowFields(me.searchId, me.baseSearchFields, defaultFields, true)
      me.$set(me, 'searchParam', showFieldsAndData.data)
      me.$set(me, 'tmpSearchFields', showFieldsAndData.fields)
    },
    /**
     * 显示列自定义弹出框
     */
    handleListSetupShow() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'tmpListFields', columns)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.getBaseListFields())
      if (me.needListFields) {
        if (me.hasOptionColumn) {
          me.$set(me, 'listFields', [...me.getDefaultColumns(), ...me.setCmbSource4List(columns, me.cmbSource)])
        } else {
          me.$set(me, 'listFields', me.setCmbSource4List(columns, me.cmbSource))
        }
        me.$nextTick(() => {
          me.$set(me.gridConfig, 'gridColumns', me.listFields)
        })
      }
    },
    /**
     * 设置查询列表列信息
     */
    setListColumns() {
      let me = this
      let defaultFields = me.getDefaultListFields()
      if (!(Array.isArray(defaultFields) && defaultFields.length > 0)) {
        defaultFields = me.getBaseListFields().map(field => {
          return {
            key: field
          }
        })
      }
      let showColumns = me.$bom3.showTableColumns(me.listId, me.baseListFields, defaultFields)
      me.handleUpdateColumn(showColumns)
    },
    /**
     * 显示详细界面自定义弹出框
     */
    handleDetailSetupShow() {
      let me = this
      me.$set(me, 'detailSetupShow', true)
    },
    /**
     * 更新详细界面字段
     */
    handleUpdateDetail() {
      let me = this
      let defaultFields = me.getDefaultDetailFields()
      if (!(Array.isArray(defaultFields) && defaultFields.length > 0)) {
        defaultFields = me.getBaseDetailFields()
      }
      let showFieldsAndData = me.getShowFields(me.detailId, me.baseDetailFields, defaultFields)
      me.$set(me, 'frmData', showFieldsAndData.data)
      me.$set(me, 'tmpDetailFields', showFieldsAndData.fields)
    },
    /**
     * 设置字段数据源(存在的)为查询、编辑组件
     * @param fields
     * @param source
     */
    setCmbSource4Components(fields, source) {
      return fields.map(field => {
        if (source.hasOwnProperty(field.key)) {
          if (field.props) {
            field.props.options = source[field.key]
          } else {
            field.props = {
              options: source[field.key]
            }
          }
        } else if (field.type === 'select') {
          if (field.props) {
            field.props.options = []
          } else {
            field.props = {
              options: []
            }
          }
        }
        if (field.showType === 'single') {
          if (field.props) {
            field.props.optionLabelRender = (opt) => opt.label
          } else {
            field.props = {
              optionLabelRender: (opt) => opt.label
            }
          }
        }
        return field
      })
    },
    /**
     * 设置字段数据源(存在的)为列表
     * @param fields
     * @param source
     */
    setCmbSource4List(fields, source) {
      let me = this
      return fields.map(field => {
        if (field.showType === 'single') {
          field.render = (h, params) => {
            return h('span', params.row[params.column.key])
          }
        } else {
          if (source.hasOwnProperty(field.key)) {
            field.render = (h, params) => {
              return this.cmbShowRender(h, params, source[field.key])
            }
          }
          if (field.props && !isNullOrEmpty(field.props.meta)) {
            field.render = (h, params) => {
              return this.cmbShowRender(h, params, [], field.props.meta)
            }
          }
          if (!isNullOrEmpty(field['keyField']) && !isNullOrEmpty(field['valueField'])) {
            field.render = (h, params) => {
              return me.keyValueRender(h, params, field['keyField'], field['valueField'])
            }
          }
          field.ctrlType = field.type
        }
        delete field.type
        return field
      })
    },
    /**
     * 重置动态数据源
     * @param field
     * @param source
     */
    resetDynamicSource(field, source) {
      let me = this
      // 查询条件
      if (me.needSearchFields) {
        me.baseSearchFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        me.searchFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        let fieldVal = me.searchParam[field]
        me.$set(me.searchParam, field, ' ')
        me.$set(me.searchParam, field, fieldVal)
      }
      // 列表字段
      if (me.needListFields) {
        me.baseListFields.forEach(item => {
          if (item.key === field) {
            item.render = (h, params) => {
              return this.cmbShowRender(h, params, source)
            }
          }
        })
        me.listFields.forEach(item => {
          if (item.key === field) {
            item.render = (h, params) => {
              return this.cmbShowRender(h, params, source)
            }
          }
        })
        me.gridConfig.gridColumns.forEach(item => {
          if (item.key === field) {
            item.render = (h, params) => {
              return this.cmbShowRender(h, params, source)
            }
          }
        })
      }
      // 详细界面
      if (me.needDetailFields) {
        me.baseDetailFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        me.detailFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        let fieldVal2 = me.frmData[field]
        me.$set(me.frmData, field, ' ')
        me.$set(me.frmData, field, fieldVal2)
      }
    },
    /**
     * 自定义key-value列展示规则
     * @param h
     * @param params
     * @param key
     * @param value
     * @returns {*}
     */
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
