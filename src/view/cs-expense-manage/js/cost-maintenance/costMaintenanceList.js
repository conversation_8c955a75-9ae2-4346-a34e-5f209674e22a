import { csAPI } from '@/api'
import ImportPage from 'xdo-import'
import { columns } from './costMaintenanceColumns'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { expenseManageList } from '../comm/expenseManageList'
import { expenseManage, importExportManage } from '@/view/cs-common'
import CostMaintenanceEdit from '../../components/cost-maintenance/cost-maintenance-edit'
import CostMaintenanceSearch from '../../components/cost-maintenance/cost-maintenance-search'

export const costMaintenanceList = {
  mixins: [expenseManageList, columns],
  components: {
    ImportPage,
    CostMaintenanceEdit,
    CostMaintenanceSearch
  },
  data() {
    return {
      toolbarEventMap: {
        'edit': this.handleEdit,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        cutData: [],
        prdOrCliObj: {},
        prdOrCliData: [],
        forwardCodeData: [],
        tradeTermNewData: [],
        tradeTermList: importExportManage.tradeTermList
      },
      searchLines: 2,
      importData: {},
      allColumns: [],
      importShow: false,
      expenseManage: expenseManage,
      uploadFileConfig: {
        action: '',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.token
        }
      }
    }
  },
  created: function () {
    let me = this
    // 货代
    me.$http.post(csAPI.ieParams.FOD).then(res => {
      me.cmbSource.forwardCodeData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.forwardCodeData = []
    })
    // 贸易条款
    me.pcodeList(me.pcode.transac).then(res => {
      let data = JSON.parse(JSON.stringify(res || {}))
      me.cmbSource.tradeTermNewData = [...me.cmbSource.tradeTermList, ...data]
    }).catch(() => {
      me.cmbSource.tradeTermNewData = me.cmbSource.tradeTermList
    })
    // 报关行
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.cmbSource.cutData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.cutData = []
    })
    // 供应商/客户
    let prdOrCliUrl = ''
    if (me.iEMark === 'I') {
      // 供应商
      prdOrCliUrl = csAPI.ieParams.PRD
      me.uploadFileConfig.action = csAPI.expenseManage.costMaintenance.in.head.importUrl
    } else {
      // 客户
      prdOrCliUrl = csAPI.ieParams.CLI
      me.uploadFileConfig.action = csAPI.expenseManage.costMaintenance.out.head.importUrl
    }
    me.$http.post(prdOrCliUrl).then(res => {
      me.cmbSource.prdOrCliData = res.data.data.map(item => {
        return {
          label: item['LABEL'],
          value: item['VALUE']
        }
      })
      me.cmbSource.prdOrCliObj = {}
      res.data.data.forEach(item => {
        me.cmbSource.prdOrCliObj[item.VALUE] = item['LABEL']
      })
    }).catch(() => {
      me.cmbSource.prdOrCliObj = {}
      me.cmbSource.prdOrCliData = []
    })
  },
  mounted: function () {
    let me = this,
      orgColumns = me.headCommCols.map(item => {
        if (item.key === 'supplierName') {
          item.title = me.supplierLabel
        }
        return item
      })
    if (me.iEMark === 'I') {
      me.$set(me, 'realTotalColumns', [...orgColumns, ...me.iHeadColumns, ...me.allColumns])
      me.$set(me, 'defaultColumns', me.iHeadDefaultColumns)
      me.setShowFields(me.realTotalColumns)
    } else if (me.iEMark === 'E') {
      me.$set(me, 'realTotalColumns', [...orgColumns, ...me.eHeadColumns, ...me.allColumns])
      me.$set(me, 'defaultColumns', me.eHeadDefaultColumns)
      me.setShowFields(me.realTotalColumns)
    } else {
      console.error('请设置进出口类型iEMark')
    }
  },
  computed: {
    supplierLabel() {
      let me = this
      if (me.iEMark === 'I') {
        return '境外发货人'
      } else if (me.iEMark === 'E') {
        return '境外收货人'
      }
      return '境外收/发货人'
    }
  },
  methods: {
    actionLoad() {
      let me = this
      if (me.actions.filter(item => {
        return item.command === 'edit'
      }).length === 0) {
        me.actions.push({
          ...me.actionsComm,
          label: '编辑',
          command: 'edit',
          icon: 'ios-create-outline'
        })
      }
      if (me.actions.filter(item => {
        return item.command === 'copy'
      }).length === 0) {
        me.actions.push({
          ...me.actionsComm,
          label: '复制',
          command: 'copy',
          icon: 'ios-copy-outline'
        })
      }
      if (me.actions.filter(item => {
        return item.command === 'import'
      }).length === 0) {
        me.actions.push({
          ...me.actionsComm,
          label: '导入',
          command: 'import',
          icon: 'ios-cloud-upload-outline'
        })
      }
      if (me.actions.filter(item => {
        return item.command === 'export'
      }).length === 0) {
        me.actions.push({
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          icon: 'ios-cloud-download-outline'
        })
      }
      if (me.actions.filter(item => {
        return item.command === 'setting'
      }).length === 0) {
        me.actions.push({
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      }
    },
    handleCopy() {
      let me = this
      if (me.checkRowSelected('复制', true)) {
        let row = me.gridConfig.selectRows[0]
        me.$http.post(me.ajaxUrl.copy + '/' + row.sid).then(res => {
          me.handleEditByRow(res.data.data)
        }).catch(() => {
        })
      }
    },
    handleFormatError() {
      let me = this
      me.$Message.warning('文件格式不对')
    },
    //发票上传成功
    handleOnSuccess(e) {
      let me = this
      me.importData = e
      me.importShow = true
    },
    closeModal(e) {
      if (!e) {
        let me = this
        me.importData = {}
        me.importShow = false
      }
    }
  }
}
