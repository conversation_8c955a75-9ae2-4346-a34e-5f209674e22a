import { baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseEditColumns],
  data () {
    let baseFields = this.getDefaultColumns()
    return {
      commCols: [
        ...baseFields,
        {
          title: '科目名称',
          width: 200,
          align: 'center',
          key: 'costCourseCode',
          render: (h, params) => {
            return this.cmbRender(h, params, this.cmbSource.subjectData)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '科目费用',
          minWidth: 200,
          align: 'center',
          key: 'decTotal',
          render: (h, params) => {
            return this.numberRender(h, params, 10, 5)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          width: 160,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbRender(h, params, this.cmbSource.currData, 'CURR_OUTDATED')
          },
          ellipsis: true,
          tooltip: true
        }
      ]
    }
  }
}
