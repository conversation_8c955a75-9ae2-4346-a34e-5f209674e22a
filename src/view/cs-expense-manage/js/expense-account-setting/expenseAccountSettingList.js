import { columns } from './expenseAccountSettingColumns'
import { expenseManageList } from '../comm/expenseManageList'
import ExpenseAccountSettingEdit from '../../components/expense-account-setting/expense-account-setting-edit'
import ExpenseAccountSettingSearch from '../../components/expense-account-setting/expense-account-setting-search'

export const expenseAccountSettingList = {
  mixins: [expenseManageList, columns],
  components: {
    ExpenseAccountSettingEdit,
    ExpenseAccountSettingSearch
  },
  data() {
    return {
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  mounted: function () {
    let me = this
    me.$set(me, 'defaultColumns', me.defaultColumns)
    me.$set(me, 'realTotalColumns', [...me.allColumns])
    me.setShowFields(me.realTotalColumns)
  },
  methods: {
    actionLoad() {
      // let me = this
      // me.actions = []
      // me.actions.push({
      //   ...me.actionsComm,
      //   label: '新增',
      //   command: 'add',
      //   icon: 'ios-add'
      // }, {
      //   ...me.actionsComm,
      //   label: '编辑',
      //   command: 'edit',
      //   icon: 'ios-create-outline'
      // }, {
      //   ...me.actionsComm,
      //   label: '删除',
      //   command: 'delete',
      //   icon: 'ios-trash-outline'
      // }, {
      //   ...me.actionsComm,
      //   label: '导出',
      //   command: 'export',
      //   icon: 'ios-cloud-download-outline'
      // }, {
      //   ...me.actionsComm,
      //   icon: 'ios-cog',
      //   label: '自定义配置',
      //   command: 'setting',
      //   key: 'xdo-btn-setting'
      // })
    }
  }
}
