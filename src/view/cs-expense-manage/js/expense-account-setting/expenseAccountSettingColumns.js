import { expenseManager } from '@/view/cs-common/constant'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      allColumns: [
        {
          width: 150,
          key: 'status',
          tooltip: true,
          title: '费用科目状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.EXPENSE_ACCOUNT_STATUS_MAP)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'courseType',
          title: '费用科目大类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, expenseManager.courseType)
          }
        },
        {
          width: 200,
          tooltip: true,
          title: '费用科目代码',
          key: 'costCourseCode'
        },
        {
          tooltip: true,
          minWidth: 250,
          title: '费用科目名称',
          key: 'costCourseName'
        },
        {
          width: 200,
          key: 'iemark',
          tooltip: true,
          title: '进出口类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.I_E_MARK)
          }
        },
        {

          width: 90,
          tooltip: true,
          title: '有效状态',
          key: 'validStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.expenseManage.VALID_STATUS_MAP)
          }
          // },
          // {
          //   width: 136,
          //   tooltip: true,
          //   key: 'isFixed',
          //   title: '费用科目属性',
          //   render: (h, params) => {
          //     return this.cmbShowRender(h, params, this.cmbSource.isFixed)
          //   }
        }
      ],
      defaultColumns: [{
        key: 'serialNo'
      }, {
        key: 'status'
      }, {
        key: 'costCourseCode'
      }, {
        key: 'costCourseName'
      }, {
        key: 'iemark'
      }, {
        key: 'validStatus'
      }, {
        key: 'courseType'
        // }, {
        //   key: 'isFixed'
      }]
    }
  }
}
