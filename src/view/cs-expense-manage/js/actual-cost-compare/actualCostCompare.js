import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { getKeyValue } from '@/libs/util'
import { expenseManager, importExportManage } from '@/view/cs-common/constant'
import { excelExport } from '@/api'

export const actualCostCompare = {
  name: 'actualCostCompare',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      expenseManager: expenseManager,
      importExportManage: importExportManage,
      cmbSource: {
        'isAgreement': importExportManage.isPassMap,
        'ieMark': expenseManager.ieType
      },
      listConfig: {
        checkColumnShow: false,
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{type: string, title: string, key: string, props: {meta: string}}|{title: string, key: string}|{title: string, key: string}|{type: string, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        type: 'select',
        key: 'isAgreement',
        title: '运保费是否一致'
      }, {
        type: 'select',
        key: 'ieMark',
        title: '进出口标志'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        type: 'pcode',
        title: '成交方式',
        key: 'transMode',
        props: {
          meta: 'TRANSAC' // this.pcode.transac
        }
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 查询结果字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, type: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 180,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 180,
        key: 'ieMark',
        title: '进出口标志',
        render: (h, params) => {
          return h('span', getKeyValue(expenseManager.ieType, params.row.ieMark, false))
        }
      }, {
        width: 120,
        key: 'transMode',
        title: '成交方式',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transac)
        })
      }, {
        width: 180,
        key: 'insertTime',
        title: '制单日期'
      }, {
        title: '运费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'feeMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'feeRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'feeCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        title: '保费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'insurMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'insurRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'insurCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        title: '实际运费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'actualFeeMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'actualFeeRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'actualFeeCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        title: '实际保费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'actualInsurMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'actualInsurRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'actualInsurCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        width: 130,
        key: 'isAgreement',
        type: 'select',
        title: '运保费是否一致',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.isAgreement)
        }
      },
        {
          width: 160,
          type: 'input',
          tooltip: true,
          title: '备注',
          key: 'note',
          align: 'center'
        }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    doExport(exportUrl, btnKey, finallyFun) {
      let me = this,
        exportColumns = me.exportHeader
      if (exportColumns.filter(e => '运费' === e.value).length > 0) {
        exportColumns = [...exportColumns, { 'key': 'feeRate', 'value': '运费率' }, { 'key': 'feeCurr', 'value': '运费币制' }, { 'key': 'feeMark', 'value': '运费类型' }]
      }
      if (exportColumns.filter(e => '保费' === e.value).length > 0) {
        exportColumns = [...exportColumns, { 'key': 'insurRate', 'value': '保费率' }, { 'key': 'insurCurr', 'value': '保费币制' }, { 'key': 'insurMark', 'value': '保费类型' }]
      }
      if (exportColumns.filter(e => '实际运费' === e.value).length > 0) {
        exportColumns = [...exportColumns, { 'key': 'actualFeeRate', 'value': '实际运费率' }, { 'key': 'actualFeeCurr', 'value': '实际运费币制' }, { 'key': 'actualFeeMark', 'value': '实际运费类型' }]
      }
      if (exportColumns.filter(e => '实际保费' === e.value).length > 0) {
        exportColumns = [...exportColumns, { 'key': 'actualInsurRate', 'value': '实际保费率' }, { 'key': 'actualInsurCurr', 'value': '实际保费币制' }, { 'key': 'actualInsurMark', 'value': '实际保费类型' }]
      }

      for (var i = 0; i < exportColumns.length; i++) {
        if (['运费', '保费', '实际运费', '实际保费'].includes(exportColumns[i].value)) {
          exportColumns.splice(i, 1)
          i--
        }
      }

      me.$nextTick(() => {
        me.setToolbarLoading(btnKey, true)
        let params = me.getSearchParams()
        excelExport(exportUrl, {
          exportColumns: params,
          header: exportColumns,
          name: me.listConfig.exportTitle
        }).finally(() => {
          me.setToolbarLoading(btnKey)
          if (typeof finallyFun === 'function') {
            finallyFun.call(me)
          }
        })
      })
    }
  }
}
