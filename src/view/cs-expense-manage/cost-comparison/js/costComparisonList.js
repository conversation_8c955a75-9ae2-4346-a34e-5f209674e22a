// import { ArrayToLocaleLowerCase } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const costComparisonList = {
  name: 'costComparisonList',
  components: {},
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        // remitteeCode: [],
        // costCourseCode: []
      },
      listConfig: {
        checkColumnShow: false,
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  // created: function () {
  //   let me = this
  //   // 科目
  //   me.$http.post(me.ajaxUrl.getSubject, me.subjectParams).then(res => {
  //     me.$set(me.cmbSource, 'costCourseCode', res.data.data.map(item => {
  //       return {
  //         status: item.status,
  //         label: item.costCourseName,
  //         value: item.costCourseCode
  //       }
  //     }))
  //   }).catch(() => {
  //     me.$set(me.cmbSource, 'costCourseCode', [])
  //   }).finally(() => {
  //     me.searchFieldsReLoad('costCourseCode')
  //   })
  //   // 收款单位
  //   me.$http.post(me.ajaxUrl.selectCmbByCode + '/PRD,FOD,CUT').then(res => {
  //     me.$set(me.cmbSource, 'remitteeCode', ArrayToLocaleLowerCase(res.data.data))
  //   }).catch(() => {
  //     me.$set(me.cmbSource, 'remitteeCode', [])
  //   }).finally(() => {
  //     me.searchFieldsReLoad('remitteeCode')
  //   })
  // },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{type: string, title: string, key: string, props: {meta: string}}|{title: string, key: string}|{title: string, key: string}|{type: string, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        type: 'pcode',
        props: {
          meta: 'TRANSF'
        },
        key: 'trafMode',
        title: '运输方式'
      }, {
        key: 'hawb',
        title: '提运单号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        // type: 'select',
        title: '收款单位',
        // key: 'remitteeCode'
        key: 'remitteeName'
      }, {
        key: 'payNo',
        title: '账单编号'
      }, {
        // type: 'select',
        title: '科目名称',
        // key: 'costCourseCode'
        key: 'costCourseName'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 查询结果字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, type: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 180,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        key: 'hawb',
        title: '提运单号'
      }, {
        width: 180,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 120,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 160,
        key: 'payNo',
        title: '账单编号'
      }, {
        width: 150,
        key: 'quoNo',
        title: '报价单编号'
      }, {
        width: 120,
        title: '收款单位',
        key: 'remitteeName',
        // key: 'remitteeCode',
        // cellRendererFramework: me.baseCellRenderer(function (h, params) {
        //   return me.cmbShowRender(h, params, me.dynamicSource.remitteeCode)
        // })
      }, {
        width: 150,
        title: '科目名称',
        key: 'costCourseName',
        // key: 'costCourseCode',
        // cellRendererFramework: me.baseCellRenderer(function (h, params) {
        //   return me.cmbShowRender(h, params, me.dynamicSource.costCourseCode)
        // })
      }, {
        width: 150,
        key: 'actlAmt',
        title: '实际费用'
      }, {
        width: 150,
        key: 'atclCurr',
        title: '实际费用币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 150,
        title: '预估费用',
        key: 'predictAmt'
      }, {
        width: 150,
        title: '预估费用币制',
        key: 'predictCurr',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 150,
        key: 'actlAmtRmb',
        title: '实际费用(人民币)'
      }, {
        width: 150,
        key: 'predictAmtRmb',
        title: '预估费用(人民币)'
      }, {
        width: 150,
        key: 'diffTotal',
        title: '差异(人民币)'
      }, {
        width: 88,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
