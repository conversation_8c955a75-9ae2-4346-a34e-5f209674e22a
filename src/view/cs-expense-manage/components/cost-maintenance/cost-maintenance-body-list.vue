<template>
  <section class="content-box">
    <div v-show="showList">
      <div class="action" ref="area_actions" style="border-bottom: 1px solid #E8EAEC;">
        <template v-for="item in buttons">
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
        </template>
      </div>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="grdHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import { costMaintenanceBodyList } from '../../js/cost-maintenance/costMaintenanceBodyList'

  export default {
    name: 'paramExportMethodList',
    mixins: [costMaintenanceBodyList],
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        grdHeight: 200,
        buttons: [
          {...btnComm, click: this.rowAdd, icon: 'ios-add', label: '新增'},
          {...btnComm, click: this.rowEdit, icon: 'ios-create-outline', label: '编辑'},
          {...btnComm, click: this.handleDelete, icon: 'ios-trash-outline', label: '删除'}
        ]
      }
    },
    watch: {
      'gridConfig.data': {
        immediate: true,
        handler: function (data) {
          if (Array.isArray(data)) {
            let realHeight = this.getRealHeight(data.length)
            this.$set(this, 'grdHeight', realHeight)
          } else {
            this.$set(this, 'grdHeight', this.getDynamicHeight())
          }
        }
      },
      'parentConfig.editStatus': {
        immediate: true,
        handler: function (status) {
          this.buttons[0].needed = (status === editStatus.EDIT)
          this.buttons[1].needed = (status === editStatus.EDIT)
          this.buttons[2].needed = (status === editStatus.EDIT)
        }
      }
    },
    methods: {
      getRealHeight(rowLine) {
        let otherHeight = 33
        let tableHeight = otherHeight + rowLine * 30
        let maxHeight = this.getDynamicHeight()
        if (tableHeight < maxHeight) {
          return tableHeight
        }
        return maxHeight
      },
      getDynamicHeight() {
        return this.dynamicHeight - 306
      },
      /**
       * 設置列表中編輯按鈕是否顯示
       * @returns {string}
       */
      operationEditShow () {
        if (this.parentConfig.editStatus === editStatus.EDIT) {
          return ''
        }
        return 'none'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
