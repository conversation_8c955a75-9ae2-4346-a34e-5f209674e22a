<template>
  <div>
    <div>
      <section v-focus>
        <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader"
                 label-position="right" :label-width="150">
          <XdoCard :bordered="false" class="dc-merge-1-4">
            <div class="dc-form" style="padding-right: 10px; margin-top: 10px;">
              <DcFormItem prop="feeMark" label="运费" ref="feeMark">
                <FeeCascader :options="feeOptions" @onFeeDataChanged="onFeeDataChanged"
                             :disabled="showDisable"></FeeCascader>
              </DcFormItem>
              <DcFormItem prop="insurMark" label="保费" ref="insurMark">
                <FeeCascader :options="insurOptions" @onFeeDataChanged="onInsurDataChanged"
                             :disabled="showDisable"></FeeCascader>
              </DcFormItem>
              <XdoFormItem prop="isAgreement" label="运保费是否一致">
                <xdo-select v-model="frmData.isAgreement"
                            :options="this.taxExemptionEquipment.ROYALTY_MAP"
                            :disabled="showDisable" :clearable="!showDisable"
                            :optionLabelRender="pcodeRender"></xdo-select>
              </XdoFormItem>
              <XdoFormItem prop="note" label="备注">
                <XdoIInput type="text" v-model="frmData.note"
                           :disabled="showDisable" :clearable="!showDisable" :maxlength="255"></XdoIInput>
              </XdoFormItem>
            </div>
          </XdoCard>
        </XdoForm>
        <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
          <template v-for="item in buttons">
            <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled"
                    :loading="item.loading" @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
            </Button>
          </template>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
  import { csAPI } from '@/api'
  import { detailJS } from './js/actualMainCost'
  import FeeCascader from '@/view/cs-ie-manage-mixins/components/fee-cascader'
  import { taxExemptionEquipment } from '@/view/cs-common/constant'

  export default {
    name: 'ActualMainCost',
    mixins: [detailJS],
    components: {
      FeeCascader
    },
    props: {
      detailConfig: { type: Object, default: () => ({}) }
    },
    data() {
      return {
        taxExemptionEquipment: taxExemptionEquipment,
        formName: 'dataForm',
        ajaxUrl: {
          insert: csAPI.expenseManager.actualMainCost.insert,
          update: csAPI.expenseManager.actualMainCost.update,
          getBean: csAPI.expenseManager.actualMainCost.getBean
        },
        rulesHeader: {
          feeMark: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          feeCurr: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          feeRate: [{ type: 'number', required: true, message: '不能为空!', trigger: 'blur' }],
          insurCurr: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          insurRate: [{ type: 'number', required: true, message: '不能为空!', trigger: 'blur' }],
          insurMark: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          isAgreement: [{ required: true, message: '不能为空!', trigger: 'blur' }]
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
