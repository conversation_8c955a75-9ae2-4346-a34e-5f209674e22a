<template>
  <section>
    <div v-show="showList">
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        <span style="padding: 0; font-weight: bold; position: absolute; top: 42px; right: 100px;">{{summaryInfo}}</span>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="200" :disable="!canModify"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      </XdoCard>
    </div>
    <CostMaintenanceBodyEdit v-if="!showList" :detailConfig="detailConfig" :editConfig="editConfig"
                             :i-e-mark="iEMark" :erp-insert-time="parentConfig.erpInsertTime"
                             @onEditBack="editBack"></CostMaintenanceBodyEdit>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNumber, isNullOrEmpty } from '@/libs/util'
  import CostMaintenanceBodyEdit from './cost-maintenance-new-body-edit'
  import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'
  import { costMaintenanceNewBodyList } from '../../js/cost-maintenance-new/costMaintenanceNewBodyList'

  export default {
    name: 'costMaintenanceNewHeadList',
    mixins: [pms, costMaintenanceNewBodyList, baseColumns],
    components: {
      CostMaintenanceBodyEdit
    },
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          erpInsertTime: '',
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        // 分页相关
        pageParam: {
          limit: 1000
        },
        ajaxUrl: {
          deleteUrl: '',
          selectAllPaged: ''
        },
        actionsComm: {
          needed: true,
          loading: false,
          disabled: false
        },
        dynamicSource: {
          subjectData: []
        },
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete
        },
        initSearch: false,
        summaryInfo: ''
      }
    },
    watch: {
      'parentConfig.editData.sid': {
        immediate: true,
        handler: function (sid) {
          let me = this
          let actionDisable = false
          if (isNullOrEmpty(sid)) {
            actionDisable = true
            me.$set(me.editConfig, 'headId', '')
            me.$set(me.gridConfig, 'data', [])
            me.$set(me.gridConfig, 'selectRows', [])
          } else {
            actionDisable = false
            me.$set(me.editConfig, 'headId', sid)
            me.pageParam.page = 1
            me.getList()
          }
          me.$nextTick(() => {
            me.actions.forEach(action => {
              action.disabled = actionDisable
            })
            me.$set(me, 'showList', true)
          })
        }
      }
    },
    created: function () {
      let me = this
      let subjectParams = {}
      if (me.iEMark === 'I') {
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.expenseManage.costMaintenanceNew.in.body.delete)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.expenseManage.costMaintenanceNew.in.body.selectAllPaged)
        subjectParams = {
          iemark: 'I,N'
        }
      } else {
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.expenseManage.costMaintenanceNew.out.body.delete)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.expenseManage.costMaintenanceNew.out.body.selectAllPaged)
        subjectParams = {
          iemark: 'E,N'
        }
      }
      // 科目
      me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, subjectParams).then(res => {
        me.dynamicSource.subjectData = res.data.data.map(item => {
          return {
            status: item.status,
            value: item.costCourseCode,
            label: item.costCourseName
          }
        })
      }).catch(() => {
        me.dynamicSource.subjectData = []
      }).finally(() => {
        me.resetDynamicSource('costCourseCode', me.dynamicSource.subjectData)
      })

      me.$set(me.gridConfig, 'gridColumns', [...me.getDefaultColumns(), ...me.detailConfig.detailFields])
    },
    computed: {
      /**
       * 默认值(编辑)
       * @returns {{}}
       */
      defaultValue() {
        return {
          curr: '142'
        }
      },
      canModify() {
        return this.parentConfig.editStatus === editStatus.ADD || this.parentConfig.editStatus === editStatus.EDIT
      },
      loadedSource() {
        return {
          costCourseCode: this.dynamicSource.subjectData
        }
      }
    },
    methods: {
      /**
       * 获取查询条件(可外部覆盖)
       */
      getSearchParams() {
        return {
          headId: this.parentConfig.editData.sid
        }
      },
      /**
       * 点击新增按钮
       */
      handleAdd() {
        let me = this
        if (me.customCheck([], '新增')) {
          me.editConfig.editData = {}
          me.gridConfig.selectRows = []
          me.editConfig.editStatus = editStatus.ADD
          me.showList = false
        }
      },
      /**
       * 自定义编辑、删除检查(可外部覆盖)
       * @param selRows 选中的行数组
       * @param opTitle (操作标签)
       * @returns {boolean}
       */
      customCheck(selRows, opTitle) {
        let me = this
        if (me.parentConfig.editData.locked === '1') {
          me.$Message.warning('已锁定数据不可' + opTitle + '!')
          return false
        }
        return true
      },
      afterSearch() {
        let me = this
        let allRmbDecTotal = 0
        if (me.gridConfig.data.length > 0) {
          me.gridConfig.data.forEach(item => {
            if (isNumber(item['rmbDecTotal'])) {
              allRmbDecTotal = allRmbDecTotal + Number(item['rmbDecTotal'])
            }
          })
        }
        allRmbDecTotal = Math.round(allRmbDecTotal * 10000) / 10000
        me.$set(me, 'summaryInfo', `人民币总费用: ${allRmbDecTotal}`)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
