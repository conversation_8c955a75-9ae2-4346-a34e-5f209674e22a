<template>
  <section style="margin-top: 10px;" class="xdo-enter-root" v-focus>
    <FormBuilder ref="dataForm" :schema="schema" :items="frmFields" :rules="rulesHeader" :model="frmData">
      <template v-slot:billPackNum>
        <dc-numberInput v-model="frmData.billPackNum" integerDigits="11" precision="0"
                       ></dc-numberInput>
      </template>
      <template v-slot:billVolume>
        <dc-numberInput v-model="frmData.billVolume" integerDigits="14" precision="5"
                       ></dc-numberInput>
      </template>
      <template v-slot:billSumDecTotal>
        <dc-numberInput v-model="frmData.billSumDecTotal" integerDigits="15" precision="5"
                       ></dc-numberInput>
      </template>
      <template v-slot:billGrossWt>
        <dc-numberInput v-model="frmData.billGrossWt"
                        integerDigits="14" precision="5">
        </dc-numberInput>
      </template>
    </FormBuilder>
    <div class="xdo-enter-action action" style="text-align: center; margin-bottom: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import DcNumberInput from '@/components/dc-number-input/dc-number-input'

  export default {
    name: 'costMaintenanceNewHeadEdit',
    mixins: [commEdit],
    components: [
      DcNumberInput
    ],
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      detailConfig: {
        type: Object,
        default: () => ({
          costData: {},
          costFields: [],
          clientInfo: [],
          prdOrCliObj: {},
          remitteeCodeSource: []
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        ajaxUrl: {
          insert: '',
          update: ''
        },
        remitteeArr: [],
        formName: 'dataForm',
        schema: {
          titleWidth: 100,
          class: 'dc-form dc-form-4'
        },
        rulesHeader: {
          payNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'
        }, {
          ...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'
        }],
        disabledFields: ['emsListNo', 'updateUser', 'updateUserName', 'updateTime', 'locked']
      }
    },
    computed: {
      frmFields() {
        let me = this
        return me.detailConfig.costFields.filter(field => {
          return field.key !== 'remitteeName'
        }).map(field => {
          if (!me.disabledFields.includes(field.key)) {
            if (field.props) {
              field.props.disabled = me.showDisable
            } else {
              field.props = {
                disabled: me.showDisable
              }
            }
          }
          return field
        })
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function (status) {
          this.buttons[0].needed = status !== editStatus.SHOW
        }
      },
      'frmData.remitteeType': {
        handler: function (type) {
          let me = this,
            field = me.detailConfig.costFields.find(p => p.key === 'remitteeCode')
          field.props.options = me.detailConfig.remitteeCodeSource.filter(item => item.type === type)
          me.$set(me.frmData, 'remitteeCode', '')
        }
      }
    },
    created: function () {
      let me = this
      if (me.iEMark === 'I') {
        me.$set(me.ajaxUrl, 'insert', csAPI.expenseManage.costMaintenanceNew.in.head.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.expenseManage.costMaintenanceNew.in.head.update)
      } else {
        me.$set(me.ajaxUrl, 'insert', csAPI.expenseManage.costMaintenanceNew.out.head.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.expenseManage.costMaintenanceNew.out.head.update)
      }
    },
    mounted: function () {
      let me = this,
        remitteeCode = me.frmData.remitteeCode
      me.$set(me.frmData, 'remitteeCode', ' ')
      me.$nextTick(() => {
        let field = me.detailConfig.costFields.find(p => p.key === 'remitteeCode')
        field.props.options = me.detailConfig.remitteeCodeSource.filter(item => item.type === me.frmData.remitteeType)
        me.$set(me.frmData, 'remitteeCode', remitteeCode)
      })
    },
    methods: {
      getDefaultData() {
        return JSON.parse(JSON.stringify(this.detailConfig.costData))
      },
      beforeSave() {
        let me = this,
          item = me.detailConfig.remitteeCodeSource.find(p => p.value === me.frmData.remitteeCode)
        if (item) {
          me.$set(me.frmData, 'remitteeName', item.label)
        } else {
          me.$set(me.frmData, 'remitteeName', '')
        }
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        if (typeof me.beforeSave === "function") {
          me.beforeSave.call(me)
        }
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
