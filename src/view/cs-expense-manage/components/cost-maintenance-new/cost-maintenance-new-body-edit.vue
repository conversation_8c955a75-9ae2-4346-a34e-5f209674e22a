<template>
  <section style="margin-top: 10px;" class="xdo-enter-root" v-focus>
    <FormBuilder ref="dataForm" :schema="schema" :items="frmFields" :rules="rulesHeader" :model="frmData">
      <template v-slot:decTotal>
        <DcNumberInput v-model="frmData.decTotal" integer-digits="10" precision="4"
                       :disabled="showDisable"></DcNumberInput>
      </template>
      <template v-slot:exchangeRate>
        <DcNumberInput v-model="frmData.exchangeRate" integer-digits="3" precision="6"
                       :disabled="showDisable"></DcNumberInput>
      </template>
      <template v-slot:noRateAmount>
        <DcNumberInput v-model="frmData.noRateAmount" integer-digits="10" precision="4" :disabled="showDisable"
                       @on-enter="calRate"></DcNumberInput>
      </template>
      <template v-slot:rate>
        <DcNumberInput v-model="frmData.rate" integer-digits="10" precision="4" :disabled="showDisable"></DcNumberInput>
      </template>
    </FormBuilder>
    <div class="xdo-enter-action action" style="text-align: center; margin-bottom: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import DcNumberInput from '@/components/dc-number-input/dc-number-input'

  export default {
    name: 'costMaintenanceBodyEdit',
    mixins: [commEdit],
    components: {
      DcNumberInput
    },
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E'].includes(value)
        }
      },
      detailConfig: {
        type: Object,
        default: () => ({
          frmData: {},
          detailFields: []
        })
      },
      erpInsertTime: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        tmpFields: [],
        formName: 'dataForm',
        disabledFields: ['rmbDecTotal'],
        schema: {
          titleWidth: 100,
          class: 'dc-form dc-form-4 xdo-enter-form'
        },
        rulesHeader: {
          curr: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          decTotal: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          exchangeRate: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          costCourseCode: [{ required: true, message: '不能为空!', trigger: 'blur' }]
        },
        buttons: [{
          ...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'
        }, {
          ...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'
        }],
        ajaxUrl: {
          insert: '',
          update: '',
          getExchangeRate: csAPI.expenseManage.costMaintenanceNew.comm.getExchangeRate
        }
      }
    },
    computed: {
      frmFields() {
        return [...this.tmpFields, {
          type: 'xdoInput',
          key: 'noRateAmount',
          title: '不含税金额'
        }, {
          type: 'xdoInput',
          key: 'rate',
          title: '税金'
        }, {
          type: 'input',
          key: 'note',
          title: '备注',
          props: {
            maxlength: 255,
            disabled: this.showDisable
          }
        }]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function(status) {
          let me = this
          me.buttons[0].needed = status !== editStatus.SHOW
          me.$set(me, 'tmpFields', me.detailConfig.detailFields.map(field => {
            if (!me.disabledFields.includes(field.key)) {
              if (field.props) {
                field.props.disabled = me.showDisable
              } else {
                field.props = {
                  disabled: me.showDisable
                }
              }
            }
            return field
          }))
        }
      },
      'frmData.curr': {
        immediate: true,
        handler: function(curr) {
          let me = this
          if (!isNullOrEmpty(curr)) {
            // 汇率
            // me.$http.get('/api/pcode?type=EXCHANGE_RATE').then(res => {
            //   let currRates = res.data.data['EXCHANGE_RATE'].filter(item => {
            //     return item['CURR_CODE_NUM'] === curr
            //   })
            //   if (Array.isArray(currRates) && currRates.length === 1) {
            //     me.$set(me.frmData, 'exchangeRate', currRates[0]['CURR_PRICE_RMB'])
            //   }
            // }).catch(() => {
            // })
            let currMonth = ''
            if (!isNullOrEmpty(me.erpInsertTime)) {
              currMonth = me.erpInsertTime.substring(0, 7).replace('-', '')
            }
            me.$http.post(me.ajaxUrl.getExchangeRate, {
              currCodeNum: curr,
              currDate: currMonth
            }).then(res => {
              if (Array.isArray(res.data.data) && res.data.data.length > 0) {
                me.$set(me.frmData, 'exchangeRate', String(res.data.data[0]['currPriceRmb']))
              } else {
                me.$set(me.frmData, 'exchangeRate', '1')
              }
            }).catch(() => {
              me.$set(me.frmData, 'exchangeRate', '')
            })
          } else {
            me.$set(me.frmData, 'exchangeRate', '')
          }
        }
      },
      'frmData.decTotal': {
        handler: function() {
          this.feeCalculate()
        }
      },
      'frmData.exchangeRate': {
        handler: function() {
          this.feeCalculate()
        }
      }
    },
    created: function() {
      let me = this
      if (me.iEMark === 'I') {
        me.$set(me.ajaxUrl, 'insert', csAPI.expenseManage.costMaintenanceNew.in.body.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.expenseManage.costMaintenanceNew.in.body.update)
      } else {
        me.$set(me.ajaxUrl, 'insert', csAPI.expenseManage.costMaintenanceNew.out.body.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.expenseManage.costMaintenanceNew.out.body.update)
      }
    },
    methods: {
      getDefaultData() {
        return JSON.parse(JSON.stringify(this.detailConfig.frmData))
      },
      feeCalculate() {
        let me = this
        if (isNumber(me.frmData.decTotal) && isNumber(me.frmData.exchangeRate)) {
          let decTotal = Number(me.frmData.decTotal)
          let exchangeRate = Number(me.frmData.exchangeRate)
          let rmbDecTotal = decTotal * exchangeRate
          me.$set(me.frmData, 'rmbDecTotal', (Math.round(rmbDecTotal * 100000) / 100000).toFixed(5))
        } else {
          me.$set(me.frmData, 'rmbDecTotal', '')
        }
      },
      beforeSave() {
        let me = this
        if (isNullOrEmpty(me.frmData.headId)) {
          me.$set(me.frmData, 'headId', me.editConfig.headId)
        }
        me.$set(me.frmData, 'exchangeRate', me.frmData.exchangeRate.toString())
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        if (typeof me.beforeSave === 'function') {
          me.beforeSave.call(me)
        }
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      },
      calRate() {
        if (this.frmData.rmbDecTotal !== '' && this.frmData.noRateAmount !== '') {
          let rate = (this.frmData.rmbDecTotal - this.frmData.noRateAmount).toFixed(4)
          this.$set(this.frmData, 'rate', rate)
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }
</style>
