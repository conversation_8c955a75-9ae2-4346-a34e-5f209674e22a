<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch" class="xdo-enter-root" v-focus>
            <div class="separateLine"></div>
            <FormBuilder ref="headSearch" :schema="schema" :items="searchFields" :model="searchParam">
            </FormBuilder>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseListFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { expenseReportList } from '../js/expense-report/expenseReportList'
  import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'

  export default {
    name: 'expenseReportEList',
    mixins: [expenseReportList, dynamicExport],
    data() {
      return {
        iEMark: 'E',
        gridConfig: {
          exportTitle: '出口费用报表'
        },
        taskInfo: {
          taskCode: 'COST_E_REPORT'     // 添加任务使用的taskCode
        },
        ajaxUrl: {
          selectComboxByCode: csAPI.ieParams.selectComboxByCode,
          exportUrl: csAPI.expenseManage.expenseReports.exports.exportUrl,
          selectAllPaged: csAPI.expenseManage.expenseReports.exports.selectAllPaged,
          costCourseUrl: csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged
        }
      }
    },
    computed: {
      /**
       * 导出列头信息
       * @returns {{value: *, key: *}[]}
       */
      exportHeader() {
        let me = this
        return me.gridConfig.gridColumns.filter(column => {
          return !['selection', 'operation'].includes(column.key) && !isNullOrEmpty(column.key)
        }).map(theCol => {
          return {
            key: theCol.key,
            value: theCol.title
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }
</style>
