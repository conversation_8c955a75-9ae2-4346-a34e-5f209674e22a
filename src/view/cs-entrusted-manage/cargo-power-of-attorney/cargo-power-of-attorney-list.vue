<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <Button type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</Button>
            <Button type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</Button>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <cargoPowerOfAttorneyTabs v-if="!showList" :edit-config="editConfig" :list-source="cmbSource" @onEditBack="editBack"></cargoPowerOfAttorneyTabs>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <cargoPowerOfAttorneyExtractPop :show.sync="extractPop.show" @doResearch="handleSearchSubmit"></cargoPowerOfAttorneyExtractPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { cargoPowerOfAttorneyList } from './js/cargoPowerOfAttorneyList'

  export default {
    name: 'cargoPowerOfAttorneyList',
    mixins: [cargoPowerOfAttorneyList],
    data() {
      return {
        listConfig: {
          exportTitle: '货运委托书表头'
        },
        ajaxUrl: {
          deleteUrl: csAPI.entrustedManage.cargoPowerOfAttorney.head.delete,
          selectAllPaged: csAPI.entrustedManage.cargoPowerOfAttorney.head.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
