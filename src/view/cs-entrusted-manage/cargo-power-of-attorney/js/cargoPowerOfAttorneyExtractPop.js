import { importExportManage } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const cargoPowerOfAttorneyExtractPop = {
  name: 'cargoPowerOfAttorneyExtractPop',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    show: {
      type: Boolean,
      require: true
    },
    headId: {
      type: String,
      default: () => ('')
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      pmsLevel: 'extract',
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'extract-all': this.handleExtractAll,
        'extract-choose': this.handleExtractChoose
      },
      cmbSource: {
        forwardCode: [],
        overseasShipper: [],
        declareCodeCustoms: [],
        tradeTerms: importExportManage.tradeTermList,
        bondMark: importExportManage.bondedFlagMap4Erp
      }
    }
  },
  created: function () {
    let me = this
    me.sourceLoad()
    me.$set(me.listConfig, 'settingColumns', me.getFields())
    me.loadListConfig()
  },
  methods: {
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        title: '发票号',
        key: 'invoiceNo'
      }, {
        type: 'select',
        title: '货运代理',
        key: 'forwardCode'
      }, {
        type: 'select',
        title: '境外收货人',
        key: 'overseasShipper'
      }, {
        type: 'pcode',
        key: 'trafMode',
        title: '运输方式',
        props: {
          meta: 'TRANSF'
        }
      }, {
        type: 'pcode',
        title: '监管方式',
        key: 'tradeMode',
        props: {
          meta: 'TRADE'
        }
      }, {
        type: 'select',
        title: '贸易条款',
        key: 'tradeTerms',
        props: {
          optionLabelRender: (opt) => opt.label
        }
      }, {
        type: 'pcode',
        title: '成交方式',
        key: 'transMode',
        props: {
          meta: 'TRANSAC'
        }
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 136,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        title: '内审状态',
        key: 'apprStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'apprStatus', 'apprStatusName')
        })
      }, {
        width: 110,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 110,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 100,
        key: 'bondMark',
        title: '保完税标识',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 200,
        title: '货运代理',
        key: 'forwardCode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.forwardCode)
        }, true)
      }, {
        width: 200,
        title: '境外收货人',
        key: 'overseasShipper',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.overseasShipper)
        }, true)
      }, {
        width: 110,
        key: 'notify',
        title: 'Notify'
      }, {
        width: 200,
        title: '贸易条款',
        key: 'tradeTerms',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.tradeTerms)
        }, true)
      }, {
        width: 130,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 150,
        key: 'wrapType',
        title: '包装种类',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.wrap)
        })
      }, {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 100,
        title: '件数',
        key: 'packNum'
      }, {
        width: 150,
        key: 'netWt',
        title: '总净重'
      }, {
        width: 150,
        key: 'grossWt',
        title: '总毛重'
      }, {
        width: 160,
        key: 'volume',
        title: '总体积'
      }, {
        width: 150,
        title: '运抵国',
        key: 'tradeCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.country_outdated)
        })
      }, {
        width: 200,
        title: '报关行',
        key: 'declareCodeCustoms',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.declareCodeCustoms)
        }, true)
      }, {
        width: 100,
        key: 'transMode',
        title: '成交方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transac)
        }, true)
      }]
    },
    /**
     * 提取勾选
     */
    handleExtractChoose() {
      let me = this
      if (me.checkRowSelected('提取勾选')) {
        me.setToolbarLoading('extract-choose', true)
        let selected = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.extraction, selected).then(() => {
          me.$Message.success('提取成功!')
          me.$emit('doResearch')
          me.$emit('update:show', false)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('extract-choose')
        })
      }
    },
    /**
     * 提取全部
     */
    handleExtractAll() {
      let me = this,
        searchParam = me.getSearchParams()
      me.setToolbarLoading('extract-all', true)
      me.$http.post(me.ajaxUrl.extraction, {
        data: [],
        ...searchParam,
        headId: me.headId
      }).then(() => {
        me.$Message.success('提取成功!')
        me.$emit('doResearch')
      }).catch(() => {
      }).finally(() => {
        me.$emit('update:show', false)
        me.setToolbarLoading('extract-all')
      })
    }
  }
}
