<template>
  <section>
    <XdoCard :border="false">
      <div>
        <Button class="btntool" type="warning" @click="handlerDocGenerate" :loading="docGenerateLoading" :disabled="canGenerate">生成单据</Button>&nbsp;
        <Button class="btntool" type="warning" @click="handlerBatchDownload" :loading="batchDownloadLoading">批量下载</Button>&nbsp;
      </div>
      <XdoTable class="dc-table" ref="table" :columns="grdConfig.columns" :data="grdConfig.data" stripe border>
        <template slot-scope="{ row }" slot="action">
          <Dropdown trigger="click" transfer>
            <XdoButton type="text" style="font-size: 12px; width: 95px;">
              <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载 <XdoIcon type="ios-arrow-down"></XdoIcon>
            </XdoButton>
            <DropdownMenu slot="list">
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="fileDownload(row,0)">
                  预览PDF
                </XdoButton>&nbsp;
              </DropdownItem>
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="fileDownload(row,2)">
                  下载PDF
                </XdoButton>&nbsp;
              </DropdownItem>
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="fileDownload(row,1)">
                  下载Excel
                </XdoButton>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </template>
      </XdoTable>
    </XdoCard>
    <dc-file-preview-pop :show.sync="filePreview.show" :file-data="filePreview.fileData" :customize-url="filePreview.pdfUrl"></dc-file-preview-pop>
  </section>
</template>

<script>
  import { csAPI, zipExport } from '@/api'
  import { blobSaveFile, getHttpHeaderFileName, isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'documentManagement',
    props: {
      headId: {
        type: String,
        default: () => ('')
      },
      canGenerate: {
        type: Boolean,
        default: () => false
      }
    },
    data() {
      return {
        grdConfig: {
          data: [],
          columns: [{
            minWidth: 120,
            align: 'center',
            title: '模版名称',
            key: 'templateName'
          }, {
            minWidth: 120,
            align: 'center',
            key: 'billType',
            title: '单据类型',
            render: (h, params) => {
              let showTitle = '',
                billType = params.row.billType
              if (billType === '1') {
                showTitle = '发票'
              } else if (billType === '2') {
                showTitle = '箱单'
              } else if (billType === '3') {
                showTitle = '合同'
              } else if (billType === '4') {
                showTitle = '托书'
              } else if (billType === '5') {
                showTitle = '订舱单'
              }
              return h('span', showTitle)
            }
          }, {
            minWidth: 120,
            key: 'billNo',
            title: '单据号',
            align: 'center'
          }, {
            key: 'sid',
            width: 120,
            title: '操作',
            slot: 'action',
            align: 'center'
          }]
        },
        docGenerateLoading: false,
        batchDownloadLoading: false,
        filePreview: {
          show: false,
          fileData: [],
          pdfUrl: csAPI.exportBill.download.DdownloadPdf
        },
        ajaxUrl: {
          downLoad: csAPI.entrustedManage.cargoPowerOfAttorney.head.downLoad,
          downLoadBatch: csAPI.entrustedManage.cargoPowerOfAttorney.head.downLoadBatch,
          docGeneration: csAPI.entrustedManage.cargoPowerOfAttorney.head.docGeneration
        }
      }
    },
    watch: {
      headId: {
        immediate: true,
        handler: function () {
          this.dataLoad()
        }
      }
    },
    methods: {
      dataLoad() {
        let me = this
        me.$http.post(csAPI.exportBill.billlist.billList + `/${me.headId}`).then(val => {
          me.grdConfig.data = val.data.data
        }).catch(() => {
        })
      },
      /**
       * 生成单据
       */
      handlerDocGenerate() {
        let me = this
        me.$set(me, 'docGenerateLoading', true)
        me.$http.post(me.ajaxUrl.docGeneration, {
          ieMark: 'E',
          headId: me.headId
        }).then(() => {
          me.dataLoad()
          me.$Message.success('单据生成成功')
        }).catch(() => {
        }).finally(() => {
          me.$set(me, 'docGenerateLoading', false)
        })
      },
      /**
       * 批量下载
       */
      handlerBatchDownload() {
        let me = this
        zipExport(me.ajaxUrl.downLoadBatch, {
          // ieMark: 'E',
          headId: me.headId
        })
      },
      /**
       * 下载PDF
       * @param stream
       * @param headers
       */
      pdfDownload(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: `application/pdf`})
        blobSaveFile(blob, filename)
      },
      /**
       * 下载Excel
       * @param stream
       * @param headers
       */
      excelDownload(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: `application/vnd.ms-excel`})
        blobSaveFile(blob, filename)
      },
      /**
       * 文件下载
       * @param e
       * @param num
       */
      fileDownload(e, num) {
        let me = this
        if (num === 0 || num === 2) {
          me.isPdf = true
        } else if (num === 1) {
          me.isPdf = false
        }

        let fastDfsId = ''
        if (me.isPdf && !isNullOrEmpty(e.fdfsPdfId)) {
          fastDfsId = e.fdfsPdfId
          me.isPdf = false
        } else {
          fastDfsId = e.fdfsId
        }

        me.$http.post(csAPI.exportBill.download.downloadForEntrust, null, {
          responseType: 'blob',
          params: {
            isPdf: me.isPdf,
            fastDfsId: fastDfsId,
            fileName: e['buildFileName']
          }
        }).then(res => {
          if (num === 0) {
            const filename = getHttpHeaderFileName(res.headers)
            // 预览
            me.$set(me.filePreview, 'show', true)
            me.$set(me.filePreview, 'fileData', [{
              sid: e.sid,
              originFileName: filename
            }])
          } else if (num === 1) {
            me.$Message.success('下载成功!')
            me.excelDownload(res.data, res.headers)
          } else if (num === 2) {
            me.$Message.success('下载成功!')
            me.pdfDownload(res.data, res.headers)
          }
        }).catch(() => {
        })
      }
    }
  }
</script>
