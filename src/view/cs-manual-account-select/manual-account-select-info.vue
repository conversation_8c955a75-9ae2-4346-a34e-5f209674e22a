<template>
  <section>
    当前选中的手账册号:
    <br />
    {{configData.emsNo}}
    <XdoButton type="primary" class="dc-margin-right" @click="btnSearch">选择</XdoButton>
    <ManualAccountSelectPop :show.sync="popShow"></ManualAccountSelectPop>
    <XdoButton type="primary" class="dc-margin-right" @click="btnSelect">选择2</XdoButton>
  </section>
</template>

<script>
  import { namespace } from '@/project'
  import { manualAccountSelectInfo } from './js/manualAccountSelectInfo'

  export default {
    name: 'manualAccountSelectInfo',
    mixins: [manualAccountSelectInfo],
    data() {
      return {
        popShow: false
      }
    },
    computed: {
      configData() {
        return this.$store.state[`${namespace}`].manualAccount
      }
    },
    methods: {
      btnSearch() {
        let me = this
        me.$set(me, 'popShow', true)
      },
      btnSelect() {
        let me = this
        me.$Message.info({
          // duration: 0,
          // closable: true,
          // onClose: function () {
          //   console.info('关闭')
          // },
          render: h => {
            return h('dc-manual-account-select', [])
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
