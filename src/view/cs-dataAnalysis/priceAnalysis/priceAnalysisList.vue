<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <XdoCard>
            <XdoForm ref="formInline" class="dc-form dc-form-3" label-position="right" :label-width="100" inline>
              <XdoFormItem label="手账册号">
                <xdo-select v-model="emsno" clearable :options="addentrustEmsNo" dataValue="key" dataLabel="value"></xdo-select>
              </XdoFormItem>
              <XdoFormItem label="周期">
                <XdoRow>
                  <XdoCol span="11">
                    <XdoFormItem prop="dateFrom" style="margin-right: 0 !important;">
                      <XdoDatePicker type="date" @on-change="fromDateChange" placeholder="请选择开始时间" v-model="dateFrom"></XdoDatePicker>
                    </XdoFormItem>
                  </XdoCol>
                  <XdoCol span="2" style="text-align: center;">-</XdoCol>
                  <XdoCol span="11">
                    <XdoFormItem prop="dateTo" style="margin-right: 0 !important;">
                      <XdoDatePicker type="date" @on-change="toDateChange" placeholder="请选择结束时间" v-model="dateTo"></XdoDatePicker>
                    </XdoFormItem>
                  </XdoCol>
                </XdoRow>
              </XdoFormItem>
            </XdoForm>
          </XdoCard>
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <PriceAnalysisSearch ref="bodySearch"></PriceAnalysisSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                    @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</Button>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard>{{this.infortitle}}</XdoCard>
      <XdoCard>
        <XdoTable class="dc-table" ref="table" stripe border
                  :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <XdoModal v-model="showModal" title="设置基础价格" :footer-hide="true" :mask-closable="false" :width="900"
              @on-cancel="modalClose">
      <SettingPrice v-if="showModal" :price-param="priceParam" :emsNo="emsno"
                    @modalClose="modalClose"></SettingPrice>
    </XdoModal>
  </section>
</template>

<script>
  import SettingPrice from './settingPrice'
  import { csAPI, excelExport } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import PriceAnalysisSearch from './priceAnalysisSearch'
  import { columnsConfig, excelColumnsConfig, columns } from './priceAnalysisColumns'
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'

  export default {
    name: 'priceAnalysisList',
    components: {
      SettingPrice,
      PriceAnalysisSearch
    },
    mixins: [dynamicHeight, columns],
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        infortitle: '',
        showList: true,
        showModal: false,
        showSearch: false,
        typeNo: '',
        emsno: '',
        dateFrom: '',
        dateTo: '',
        priceParam: {},
        searchData: {},
        addentrustEmsNo: [],
        pageParam: {
          page: 1,
          limit: 10,
          dataTotal: 0,
          pageSizeOpts: [10, 20, 30, 40, 50]
        },
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          exportTitle: '',
          gridColumns: [],
          exportColumns: []
        },
        actions: [
          {...btnComm, click: this.handleComputed, icon: 'ios-add', label: '计算', key: 'xdo-btn-add'},
          {...btnComm, click: this.settingPrice, icon: 'ios-create-outline', label: '设置预警价格', key: 'xdo-btn-edit'},
          {...btnComm, click: this.exportExcel, icon: 'ios-cloud-download-outline', label: '导出', key: 'xdo-btn-open'}
        ]
      }
    },
    mounted() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
      me.refreshDynamicHeight(225, !me.showSearch ? ['area_search'] : null)
      me.setEmsNo().then(data => {
          if (data !== '') {
            me.getCircle()
            me.getResult()
            me.handleSearchSubmit()
          }
        }
      )
    },
    beforeDestroy() {
      clearInterval(this.getResult)
    },
    methods: {
      //初始化赋值
      setEmsNo() {
        return new Promise(resolve => {
          let me = this
          me.getEmsNo()
          setTimeout(function () {
            resolve(me.emsno)
          }, 2000)
        })
      },
      //获取当前时间
      getNowTime() {
        let date = new Date()
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        this.dateTo = `${year}-${month}-${day}`
        this.$refs.bodySearch.searchParam.endDate = `${year}-${month}-${day} 23:59:59`
      },
      fromDateChange(e) {
        if (e !== '') {
          this.$refs.bodySearch.searchParam.beginDate = e + ` 00:00:00`
        } else {
          this.$refs.bodySearch.searchParam.beginDate = ''
        }
      },
      toDateChange(e) {
        if (e !== '') {
          this.$refs.bodySearch.searchParam.endDate = e + ` 23:59:59`
        } else {
          this.$refs.bodySearch.searchParam.endDate = ''
        }
      },
      //获取手账册号
      getEmsNo() {
        let me = this
        me.$http.post(csAPI.dataanalysis.priceanalysis.getemsNo).then(res => {
          me.addentrustEmsNo = res.data.data.filter(item => {
            return !isNullOrEmpty(item.VALUE)
          }).map(item => {
            return item.VALUE
          })
          let nonBanded = me.addentrustEmsNo.filter(item => {
            return item === '非保税'
          })
          if (nonBanded.length === 0) {
            me.addentrustEmsNo.push('非保税')
          }
          me.emsno = me.addentrustEmsNo[0]
        }).catch(() => {
        })
      },
      //获取周期
      getCircle() {
        let me = this
        me.dateFrom = ''
        me.dateTo = ''
        me.$refs.bodySearch.searchParam.beginDat = ''
        me.$refs.bodySearch.searchParam.endDate = ''
        me.$http.post(csAPI.dataanalysis.priceanalysis.getTime, {
          emsNo: me.emsno
        }).then(res => {
          let data = res.data.data
          if (data !== null) {
            me.priceParam = data
            if (data.beginDate !== null) {
              me.dateFrom = data.beginDate.trim().substr(0, data.beginDate.indexOf(' '))
              me.$refs.bodySearch.searchParam.beginDate = data.beginDate
            }
            if (data.endDate !== null) {
              me.dateTo = data.endDate.trim().substr(0, data.endDate.indexOf(' '))
              me.$refs.bodySearch.searchParam.endDate = data.endDate
            } else {
              me.getNowTime()
            }
          } else {
            me.getNowTime()
          }
        })
      },
      //获取计算结果
      getResult() {
        let me = this
        me.$http.post(csAPI.dataanalysis.priceanalysis.getResult, {
          jobType: '03',
          extendFiled1: me.emsno
        }).then(res => {
          let data = res.data.data
          if (data === null) {
            me.infortitle = '当前无计算结果'
          } else {
            if (data.status === '0' || data.status === '0') {
              me.infortitle = '运行中，请等待计算结果'
            } else if (data.status === '2') {
              me.infortitle = `计算完成！更新时间:${data.endTime}`
            } else if (data.status === '3') {
              me.infortitle = `计算失败, 请重新计算!`
            }
          }
        })
      },
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(225, !me.showSearch ? ['area_search'] : null)
      },
      //弹框
      settingPrice() {
        let me = this
        new Promise(resolve => {
          me.getCircle()
          setTimeout(function () {
            resolve(true)
          }, 100)
        }).then(
          data => {
            if (data) {
              me.showModal = true
            }
          }
        )
      },
      //查询
      handleSearchSubmit() {
        let me = this
        me.$refs.bodySearch.searchParam.emsNo = me.emsno
        me.$http.post(csAPI.dataanalysis.priceanalysis.getInfo, me.$refs.bodySearch.searchParam, {
          params: {
            page: me.pageParam.page,
            limit: me.pageParam.limit
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.dataTotal = res.data.total
        }).catch(() => {
        })
      },
      //分页触发
      pageChange(val) {
        let me = this
        me.pageParam.page = val
        me.handleSearchSubmit()
      },
      pageSizeChange(val) {
        let me = this
        me.pageParam.limit = val
        if (me.pageParam.page === 1) {
          me.handleSearchSubmit()
        }
      },
      exportExcel() {
        let me = this
        me.gridConfig.exportTitle = `价格分析报表`
        const url = csAPI.dataanalysis.priceanalysis.getExport
        me.doExport(url, 2, me.$refs.bodySearch.searchParam)
      },
      /**
       * 执行导出
       * @param exportUrl
       * @param actionIndex
       * @param columns
       */
      doExport(exportUrl, actionIndex, columns) {
        let me = this
        me.$nextTick(() => {
          if (typeof actionIndex === 'number' && actionIndex > -1 && actionIndex < me.actions.length) {
            me.actions[actionIndex].loading = true
          }
          excelExport(exportUrl, {
            exportColumns: columns,
            name: me.gridConfig.exportTitle,
            header: me.gridConfig.exportColumns
          }).finally(() => {
            if (typeof actionIndex === 'number' && actionIndex > -1 && actionIndex < me.actions.length) {
              me.actions[actionIndex].loading = false
            }
          })
        })
      },
      //关闭弹框
      modalClose(e) {
        if (e) {
          let me = this
          me.priceParam = {}
          me.showModal = false
        }
      },
      //计算
      handleComputed() {
        let me = this
        me.$http.post(csAPI.dataanalysis.priceanalysis.getComputed, {
          jobType: '03',
          extendFiled1: me.emsno
        }).then(() => {
          me.getResult()
        }).catch(() => {
        })
      },
      //列表值转换
      getMark(e) {
        if (e === 'I') {
          return '料件'
        } else if (e === 'E') {
          return '成品'
        }
      },
      getDiffPercent(e) {
        return e + '%'
      },
      gettype(e) {
        if (e === '1') {
          return '加权平均价'
        } else if (e === '2') {
          return '最高单价'
        } else if (e === '3') {
          return '最低单价'
        } else {
          return '物料备案单价'
        }
      },
      bondMark(e) {
        if (e === '0') {
          return '保税'
        } else if (e === '1') {
          return '非保税'
        }
      }
    },
    watch: {
      emsno: {
        immediate: true,
        handler: function (newVal) {
          let me = this
          if (!isNullOrEmpty(newVal)) {
            me.getCircle()
            me.getResult()
            me.handleSearchSubmit()
            setInterval(me.getResult, 5 * 60000)
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>


