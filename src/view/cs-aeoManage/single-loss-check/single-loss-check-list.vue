<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:audit-pass>
            <Dropdown trigger="click">
              <Button type="text" style="font-size: 12px; width: 95px;">
                <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>内审通过<Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="handlePassByChoose">
                    <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>勾选通过
                  </Button>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="handlePassByParams">
                    <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>全部通过
                  </Button>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>&nbsp;
          </template>
          <template v-slot:audit-return>
            <Dropdown trigger="click">
              <Button type="text" style="font-size: 12px; width: 95px;">
                <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-edit"/>内审退回<Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="handleReturnByChoose">
                    <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>勾选退回
                  </Button>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="handleReturnByParams">
                    <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>全部退回
                  </Button>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <SingleLossCheckTabs v-if="!showList" :edit-config="editConfig" :in-source="dynamicSource" @onEditBack="editBack"></SingleLossCheckTabs>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <SingleLossAuditPop :show.sync="auditOptions.show" :passed="auditOptions.passed" :title="auditOptions.title"
                        :audit-type="auditOptions.auditType" :apprType="auditOptions.apprType" :params="auditOptions.params"
                        @doAudit="handleDoAudit"></SingleLossAuditPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { singleLossCheckList } from './js/singleLossCheckList'

  export default {
    name: 'singleLossCheckList',
    mixins: [singleLossCheckList],
    data() {
      return {
        listConfig: {
          exportTitle: '单损耗表头信息'
        },
        ajaxUrl: {
          auditDataM: csAPI.csMaterielCenter.singleLoss.aeo.auditDataM,
          returnDataM: csAPI.csMaterielCenter.singleLoss.aeo.returnDataM,
          selectAllPaged: csAPI.csMaterielCenter.singleLoss.head.selectAllPaged,
          auditDataByParams: csAPI.csMaterielCenter.singleLoss.aeo.auditDataByParams,
          returnDataByParams: csAPI.csMaterielCenter.singleLoss.aeo.returnDataByParams
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
