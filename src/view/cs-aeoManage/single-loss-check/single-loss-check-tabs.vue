<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <SingleLossHeadEdit ref="head" :edit-config="editConfig" :in-source="inSource" @onEditBack="editBack"></SingleLossHeadEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体">
        <SingleLossBodyList ref="body" :parent-config="parentConfig"></SingleLossBodyList>
      </TabPane>
      <TabPane name="aeoTab" v-if="showBody" label="内审情况">
        <AeoInfoList v-if="tabs.aeoTab" :sid="editConfig.editData.sid" show-title="false"></AeoInfoList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton size="small" type="primary" :disabled="canApp" :loading="btnAppLoading" @click="handleAuditPass">内审通过</XdoButton>
        <XdoButton size="small" type="error" :disabled="canApp" :loading="btnAppLoading" @click="handleAuditReturn">内审退回</XdoButton>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
    <SingleLossAuditPop :show.sync="auditOptions.show" :passed="auditOptions.passed" :title="auditOptions.title"
                        :audit-type="auditOptions.auditType" :apprType="auditOptions.apprType" :params="auditOptions.params"
                        @doAudit="handleDoAudit"></SingleLossAuditPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { singleLossCheckTabs } from './js/singleLossCheckTabs'

  export default {
    name: 'singleLossTabs',
    mixins: [singleLossCheckTabs],
    data() {
      return {
        ajaxUrl: {
          auditData: csAPI.csMaterielCenter.singleLoss.aeo.auditDataM,
          returnData: csAPI.csMaterielCenter.singleLoss.aeo.returnDataM
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  /deep/ .ivu-tabs-nav-right {
    display: grid;
    grid-column-gap: 6px;
    grid-template-columns: repeat(3, 1fr);
  }

  /deep/ .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    color: black !important;
    font-weight: bold !important;
    border: 1px solid red !important;
  }
</style>
