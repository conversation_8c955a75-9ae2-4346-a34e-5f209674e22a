import { editStatus, AeoInfoList } from '@/view/cs-common'
import SingleLossAuditPop from '../components/single-loss-audit-pop'
import SingleLossHeadEdit from '../../../cs-materielCenter/single-loss/single-loss-head-edit'
import SingleLossBodyList from '../../../cs-materielCenter/single-loss/single-loss-body-list'

export const singleLossCheckTabs = {
  name: 'singleLossCheckTabs',
  components: {
    AeoInfoList,
    SingleLossAuditPop,
    SingleLossHeadEdit,
    SingleLossBodyList
  },
  props: {
    inSource: {
      type: Object,
      default: () => ({})
    },
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        aeoTab: false
      },
      auditOptions: {
        title: '',
        params: {
          sid: ''
        },
        show: false,
        passed: true,
        auditType: 'single',
        apprType: 'CONSUME'
      },
      btnAppLoading: false
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
      if (value === 'headTab') {
        me.$nextTick(() => {
          if (me.$refs.head) {
            console.info(value)
          }
        })
      } else if (value === 'bodyTab') {
        me.$nextTick(() => {
          if (me.$refs.body) {
            console.info(value)
          }
        })
      }
    }
  },
  computed: {
    showBody() {
      return this.editConfig.editStatus !== editStatus.ADD
    },
    parentConfig() {
      return {
        headId: this.editConfig.editData.sid,
        editStatus: this.editConfig.editStatus
      }
    },
    canApp() {
      return this.editConfig.editData.apprStatus !== '2'
    }
  },
  methods: {
    handleAuditPass() {
      let me = this
      me.$set(me.auditOptions, 'passed', true)
      me.handleDoAudit({
        apprNote: '',
        sids: [me.editConfig.editData.sid],
        apprType: me.auditOptions.apprType
      })
    },
    handleAuditReturn() {
      let me = this
      me.$set(me.auditOptions, 'passed', false)
      me.$set(me.auditOptions, 'title', '单损耗内审退回')
      me.$set(me.auditOptions.params, 'sid', me.editConfig.editData.sid)
      me.$set(me.auditOptions, 'show', true)
    },
    /**
     * 执行审核
     * @param auditResult
     */
    handleDoAudit(auditResult) {
      let me = this,
        auditUrl = '',
        auditMsg = ''
      if (me.auditOptions.passed) {
        auditUrl = me.ajaxUrl.auditData
        auditMsg = '内审通过成功!'
      } else {
        auditUrl = me.ajaxUrl.returnData
        auditMsg = '内审退回成功!'
      }
      me.$set(me, 'btnAppLoading', true)
      me.$http.post(auditUrl, auditResult).then(() => {
        me.$set(me.auditOptions, 'show', false)
        me.$Message.success(auditMsg)
        me.backToList()
      }).catch(() => {
      }).finally(() => {
        me.$set(me, 'btnAppLoading', false)
      })
    },
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  }
}
