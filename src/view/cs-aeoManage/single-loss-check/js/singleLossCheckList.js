import { isNullOrEmpty } from '@/libs/util'
import { aeoManage } from '@/view/cs-common'
import SingleLossCheckTabs from '../single-loss-check-tabs'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import SingleLossAuditPop from '../components/single-loss-audit-pop'
import { detailGeneralMethod } from '@/view/cs-materielCenter/base/detailGeneralMethod'

export const singleLossCheckList = {
  name: 'singleLossCheckList',
  mixins: [baseSearchConfig, baseListConfig, detailGeneralMethod],
  components: {
    SingleLossAuditPop,
    SingleLossCheckTabs
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      listConfig: {
        colOptions: true
      },
      auditOptions: {
        show: false,
        passed: true,
        title: '',
        auditType: '',
        apprType: 'CONSUME',
        params: {
          sids: [],
          searchParams: {}
        }
      },
      cmbSource: {
        copEmsNo: [],
        apprStatus: aeoManage.apprStatusMap
      },
      defaultFields: [],
      toolbarEventMap: {
        'audit-pass': this.handleAuditPass,
        'audit-return': this.handleAuditReturn
      }
    }
  },
  created: function () {
    let me = this,
      rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    me.handleUpdateColumn(showColumns)
    me.loadCmbSource()
  },
  computed: {
    extendParams() {
      return {
        type: '2'
      }
    }
  },
  methods: {
    loadCmbSource() {
      let me = this
      // 获取备案信息
      me.getEmsNoList((req) => {
        if (Array.isArray(req.data)) {
          me.$set(me.cmbSource, 'copEmsNo', req.data.filter(item => {
            return !isNullOrEmpty(item.copEmsNo)
          }).map(item => {
            return {
              value: item.copEmsNo,
              label: item.copEmsNo
            }
          }))
        }
      })
    },
    getParams() {
      return [{
        title: '状态',
        type: 'select',
        key: 'apprStatus',
        defaultValue: '2'
      }, {
        range: true,
        key: 'apprDate',
        title: '发送内审日期'
      }, {
        key: 'copEmsNo',
        title: '企业内部编号'
      }, {
        key: 'emsNo',
        title: '备案号'
      }]
    },
    getFields() {
      return [{
        width: 120,
        title: '状态',
        key: 'apprStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.apprStatus)
        }
      }, {
        width: 100,
        key: 'apprDate',
        title: '发送内审日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 180,
        key: 'copEmsNo',
        title: '企业内部编号'
      }, {
        width: 150,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 100,
        key: 'billFlag',
        title: '类型',
        render: (h, params) => {
          let billFlag = params.row['billFlag'],
            billFlagName = billFlag
          if (['11', '12'].includes(billFlag)) {
            billFlagName = billFlag + ' 手册'
          } else if (['21', '22'].includes(billFlag)) {
            billFlagName = billFlag + ' 账册'
          }
          return this.toolTipRender(h, billFlagName.trim())
        }
      }, {
        width: 100,
        title: '有效期',
        key: 'validDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 100,
        title: '录入日期',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 150,
        tooltip: true,
        title: '最后发送人',
        key: 'sendApprUserName'
      }, {
        width: 150,
        tooltip: true,
        title: '内审员',
        key: 'apprUserName'
      }]
    },
    handleTableColumnSetup() {
      let me = this
      me.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handlePassByChoose() {
      let me = this
      if (me.checkRowSelected('单损耗内审通过')) {
        let cantRows = me.listConfig.selectRows.filter(row => {
          return row.apprStatus !== '2'
        })
        if (Array.isArray(cantRows) && cantRows.length > 0) {
          me.$Message.warning('仅状态为【待审核】的数据可执行审核操作!')
          return
        }
        me.$set(me.auditOptions, 'passed', true)
        me.$set(me.auditOptions, 'title', '单损耗内审通过(根据勾选项)')
        me.$set(me.auditOptions, 'auditType', 'batch')
        me.$set(me.auditOptions.params, 'sids', me.getSelectedParams())
        me.$set(me.auditOptions, 'show', true)
      }
    },
    handlePassByParams() {
      let me = this
      me.$set(me.auditOptions, 'passed', true)
      me.$set(me.auditOptions, 'title', '单损耗内审通过(根据查询条件)')
      me.$set(me.auditOptions, 'auditType', 'params')
      me.$set(me.auditOptions.params, 'searchParams', me.getSearchParams())
      me.$set(me.auditOptions, 'show', true)
    },
    handleReturnByChoose() {
      let me = this
      if (me.checkRowSelected('单损耗内审退回')) {
        let cantRows = me.listConfig.selectRows.filter(row => {
          return row.apprStatus !== '2'
        })
        if (Array.isArray(cantRows) && cantRows.length > 0) {
          me.$Message.warning('仅状态为【待审核】的数据可执行审核操作!')
          return
        }
        me.$set(me.auditOptions, 'passed', false)
        me.$set(me.auditOptions, 'title', '单损耗内审退回(根据勾选项)')
        me.$set(me.auditOptions, 'auditType', 'batch')
        me.$set(me.auditOptions.params, 'sids', me.getSelectedParams())
        me.$set(me.auditOptions, 'show', true)
      }
    },
    handleReturnByParams() {
      let me = this
      me.$set(me.auditOptions, 'passed', false)
      me.$set(me.auditOptions, 'title', '单损耗内审退回(根据查询条件)')
      me.$set(me.auditOptions, 'auditType', 'params')
      me.$set(me.auditOptions.params, 'searchParams', me.getSearchParams())
      me.$set(me.auditOptions, 'show', true)
    },
    /**
     * 执行审核
     * @param auditResult
     */
    handleDoAudit(auditResult) {
      let me = this,
        auditUrl = '',
        auditMsg = ''
      if (me.auditOptions.passed) {
        if (me.auditOptions.auditType === 'batch') {
          auditUrl = me.ajaxUrl.auditDataM
        } else {
          auditUrl = me.ajaxUrl.auditDataByParams
        }
        auditMsg = '内审通过成功!'
      } else {
        if (me.auditOptions.auditType === 'batch') {
          auditUrl = me.ajaxUrl.returnDataM
        } else {
          auditUrl = me.ajaxUrl.returnDataByParams
        }
        auditMsg = '内审退回成功!'
      }
      me.$http.post(auditUrl, auditResult).then(() => {
        me.$set(me.auditOptions, 'show', false)
        me.$Message.success(auditMsg)
        me.getList()
      }).catch(() => {
      })
    }
  }
}
