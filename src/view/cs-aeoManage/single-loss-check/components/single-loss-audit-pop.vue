<template>
  <XdoModal v-model="show" ref="theModel" width="700" :title="title"
            :mask-closable="false" footer-hide :closable="false">
    <XdoForm ref="frmReview" class="dc-form" :model="reviewData" :rules="rulesReview" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="auditNote" class="dc-merge-1-4" label="内审意见">
        <XdoIInput type="textarea" v-model="reviewData.auditNote" clearable :maxlength="127"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
    <div class="action" style="text-align: center;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{item.label}}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'singleLossAuditPop',
    props: {
      show: {
        type: Boolean,
        default: false
      },
      passed: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: () => ('')
      },
      auditType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['single', 'batch', 'params'].includes(value)
        }
      },
      apprType: {
        type: String,
        required: true,
        default: () => ('CONSUME')
      },
      params: {
        type: Object,
        default: () => ({
          sid: '',
          sids: [],
          searchParams: {}
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        reviewData: {
          auditNote: ''
        },
        rulesReview: {
          auditNote: [{required: false, message: '请先维护内审意见', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, label: '确定', type: 'error', icon: 'dc-btn-refuse', click: this.handleOk
        }, {
          ...btnComm, label: '取消', type: 'primary', icon: 'dc-btn-cancel', click: this.handleCancel
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              let modelChildren = me.$refs['theModel'].$el.childNodes
              let item
              for (let i = 0; i < modelChildren.length; i++) {
                item = modelChildren[i]
                if (!isNullOrEmpty(item.className)) {
                  if (item.className.indexOf('ivu-modal-mask') > -1 || item.className.indexOf('ivu-modal-wrap') > -1) {
                    item.style.zIndex = '1000'
                  }
                }
              }
            })
            me.rulesReview.auditNote[0].required = !me.passed
            me.$refs['frmReview'].resetFields()
          }
        }
      }
    },
    methods: {
      handleOk() {
        let me = this
        me.$refs['frmReview'].validate().then(isValid => {
          if (isValid) {
            let resultObj = {
              apprType: me.apprType,
              apprNote: me.reviewData.auditNote
            }
            if (me.auditType === 'single') {
              resultObj.sids = [me.params.sid]
            } else if (me.auditType === 'batch') {
              resultObj.sids = me.params.sids
            } else if (me.auditType === 'params') {
              resultObj = me.params.searchParams
              resultObj['apprType'] = me.apprType
              resultObj['apprNote'] = me.reviewData.auditNote
            }
            me.$emit('doAudit', resultObj)
          }
        })
      },
      handleCancel() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style scoped>
</style>
