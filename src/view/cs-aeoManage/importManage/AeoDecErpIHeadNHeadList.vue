<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <AeoDecErpIESearch ref="headSearch" :showSearch="showSearch" :i-e-mark="ieMark"></AeoDecErpIESearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" v-if="grdShow" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <ApprReject :show.sync="reject.apprShow" :batch-sid="reject.batchData" :title="reject.title"
                @return:success="handleRejectReturn"></ApprReject>
    <AeoDecErpIHeadTab v-if="!showList" ref="DecErpIHeadNTabs" @onEditBack="editBack"
                       :editConfig="editConfig"></AeoDecErpIHeadTab>
    <InnerMessagePop :show.sync="innerMsg.show" :message="innerMsg.message"></InnerMessagePop>
    <TableColumnSetup v-model="columnSetup.show" :resId="columnSetup.listId" :columns="columnSetup.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import AeoDecErpIHeadTab from './AeoDecErpIHeadTab'
  import { aeoIEManageList } from '../base/aeoIEManageList'
  import { columnsConfig, columns } from './decErpIHeadNHeadListColumns'
  import { tableColumnSetup } from '@/view/cs-aeoManage/base/tableColumnSetup'

  export default {
    name: 'AeoDecErpIHeadNHeadList',
    components: {
      AeoDecErpIHeadTab
    },
    mixins: [aeoIEManageList, columns, tableColumnSetup],
    data() {
      return {
        ieMark: 'I',
        ajaxUrl: {
          getForwardCodes: csAPI.ieParams.FOD,
          auditDataM: csAPI.aeoManage.aeoReview.actions.auditDataM,
          auditDataAll: csAPI.aeoManage.aeoReview.actions.auditDataAll,
          selectAllPaged: csAPI.csImportExport.decErpIHeadN.aeoReview.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.$set(me.columnSetup, 'settingColumns', getColumnsByConfig(me.totalColumns.filter(col => !['selection', 'operation'].includes(col.key)), columnsConfig))
      me.loadDefaultColumns()
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
