<template>
  <section>
    <ApprReject :show.sync="reject.apprShow" ref="reviewModal" :business-sid="reject.businessSid" :appr-result="reject.apprResult"
                :appr-type="reject.apprType" :title="reject.title" :return-reason="auditMessage"
                @return:success="handleRejectReturn"></ApprReject>
    <Layout>
      <Content style="min-height: 1030px">
        <Tabs v-model="tabName" :animated="false" type="card" class="dc-tab">
          <TabPane name="headTab" label="表头">
            <Head ref="head" @onEditBack="editBack" :edit-config="editConfig" :can-aeo-audit="canAeoAudit"
                  :saved-audit-data="headAuditData" @onHeadAuditChanged="onHeadAuditChanged"></Head>
          </TabPane>
          <TabPane name="bodyTab" v-if="showBody" label="表体">
            <Body ref="body" v-if="tabs.bodyTab" :head-data="headData" :can-aeo-audit="canAeoAudit"
                  :saved-audit-data="bodyAuditData" @onBodyAuditChanged="onBodyAuditChanged"></Body>
          </TabPane>
          <TabPane name="packingTab" v-if="packingTabShow" label="箱单信息">
            <PackingInfoList ref="packing" v-if="tabs.packingTab" :parent-config="parentConfig" :bond-mark="editConfig.editData.bondMark"></PackingInfoList>
          </TabPane>
          <TabPane name="billTab" v-if="showBody" label="清单">
            <Bill ref="bill" v-if="tabs.billTab" :head-data="headData" @onEditBack="editBack"></Bill>
          </TabPane>
          <TabPane name="logisticsTab" v-if="logisticsTabShow" label="物流追踪">
            <Logistics ref="logistics" v-if="tabs.logisticsTab" :head-id="headId" :aeo-show="aeoShow" :init-data="true" @onEditBack="editBack"></Logistics>
          </TabPane>
          <TabPane name="entryTab" v-if="entryTabShow" label="报关追踪">
            <CustomsTrack ref="entryTrack" v-if="tabs.entryTab" :head-id="headId" :aeo-show="aeoShow" @onEditBack="editBack"></CustomsTrack>
          </TabPane>
          <TabPane name="aeoTab" v-if="showBody" label="内审情况">
            <AeoInfoList ref="aeoInfo" v-if="tabs.aeoTab" :sid="headId" :show-title="aeoShowTitle"></AeoInfoList>
          </TabPane>
          <template v-for="item in buttons">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" slot="extra"
                    @click="item.click" :icon="item.icon" :key="item.icon">{{item.label}}</Button>&nbsp;
          </template>
        </Tabs>
      </Content>
      <Sider hide-trigger collapsible :width="380" :collapsed-width="36" v-model="collapsed" style="overflow: hidden; background: transparent;">
        <div class="right-sider">
          <XdoIcon type="md-menu" :style="`${iconClass}`" @click.native="handleRightSliderClick"/>
        </div>
        <XdoCard>
          <Tabs v-model="tabsRightName" :animated="false" type="card" class="dc-tab">
            <TabPane name="attachTab" label="随附单据">
              <Attach ref="attachInfo" :head-id="parentConfig.editData.sid" :aeo-show="aeoShow" :edit="parentConfig" iemark="E"></Attach>
            </TabPane>
            <TabPane name="documentsTab" label="随附单证">
              <DecAttachedDocumentsList :parent-config="parentConfig" ie-mark="E" aeoShow></DecAttachedDocumentsList>
            </TabPane>
          </Tabs>
        </XdoCard>
      </Sider>
    </Layout>
  </section>
</template>

<script>
  import { aeoIEManageTabs } from '../base/aeoIEManageTabs'
  import Logistics from '../../cs-exportManage/logistics/Logistics'
  import CustomsTrack from '../../cs-exportManage/entry/CustomsTrack'
  import Head from '../components/dec-erp-complex/dec-erp-head-complex-e'
  import Body from '../components/dec-erp-complex/dec-erp-body-complex-e'
  import Bill from '../components/dec-erp-complex/dec-erp-bill-complex-e'
  import PackingInfoList from '../../cs-ie-manage/packing-info/packing-info-list'

  export default {
    name: 'AeoDecErpEHeadTab',
    mixins: [aeoIEManageTabs],
    components: {
      Head,
      Body,
      Bill,
      Logistics,
      CustomsTrack,
      PackingInfoList
    },
    data() {
      return {
        reject: {
          apprType: 'E',
          title: '出口内审退回'
        },
        aeoKey: 'aeoDecErpEHeadTab'
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-tabs-nav-right {
    display: grid;
    grid-column-gap: 6px;
    grid-template-columns: repeat(3, 1fr);
  }

  /deep/ .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    color: black !important;
    font-weight: bold !important;
    border: 1px solid red !important;
  }

  .right-sider {
    height: 38px;
    padding: 7px 0 0 6px;
    background: rgb(245, 247, 247);
    border-bottom: 1px solid rgb(214, 219, 222);

    .ivu-layout-sider-children {
      overflow-y: hidden;
      margin-right: -18px;
    }
  }

  .right-sider i {
    color: #389de9;
    cursor: pointer;
    font-size: 26px;
    //transform: rotate(0deg);
    //-webkit-transform: rotate(0deg);
    transition: transform .2s linear;
    -webkit-transition: -webkit-transform .2s linear;
  }
</style>
