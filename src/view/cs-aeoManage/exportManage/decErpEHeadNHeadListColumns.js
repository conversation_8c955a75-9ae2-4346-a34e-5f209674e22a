import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'emsListNo'
  , 'tradeCode'
  , 'tradeName'
  , 'tradeCreditCode'
  , 'declareCode'
  , 'declareName'
  , 'declareCreditCode'
  , 'inputCode'
  , 'inputName'
  , 'inputCreditCode'
  , 'supplierCode'
  , 'supplierName'
  , 'forwardCode'
  , 'forwardName'
  , 'ieport'
  , 'masterCustoms'
  , 'tradeMode'
  , 'trafMode'
  , 'gmark'
  , 'tradeNation'
  , 'mergeType'
  , 'note'
  , 'inputDate'
  , 'transMode'
  , 'tradeCountry'
  , 'despPort'
  , 'promiseItems'
  , 'validMark'
  , 'emsNo'
  , 'mawb'
  , 'hawb'
  , 'licenseNo'
  , 'invoiceNo'
  , 'contrNo'
  , 'packNum'
  , 'volume'
  , 'netWt'
  , 'destPort'
  , 'wrapType'
  , 'trafName'
  , 'otherCurr'
  , 'otherMark'
  , 'otherRate'
  , 'feeCurr'
  , 'feeMark'
  , 'feeRate'
  , 'insurCurr'
  , 'insurMark'
  , 'insurRate'
  , 'grossWt'
  , 'iedate'
  , 'districtCode'
  , 'receiveCode'
  , 'receiveName'
  , 'voyageDate'
  , 'shipDate'
  , 'overseasShipper'
  , 'overseasShipperName'
  , 'cutMode'
  , 'warehouse'
  , 'destinationCountry'
  , 'entryPort'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'declareDate'
  , 'maker'
  , 'sendUserFull'
  , 'apprDate'
  , 'apprUserFull'
  , 'apprStatus'
  , 'apprStatusName'
]

const excelColumnsConfig = [
  'sid'
  , 'emsListNo'
  , 'tradeCode'
  , 'tradeName'
  , 'tradeCreditCode'
  , 'declareCode'
  , 'declareName'
  , 'declareCreditCode'
  , 'inputCode'
  , 'inputName'
  , 'inputCreditCode'
  , 'supplierCode'
  , 'supplierName'
  , 'forwardCode'
  , 'forwardName'
  , 'ieport'
  , 'masterCustoms'
  , 'tradeMode'
  , 'trafMode'
  , 'gmark'
  , 'tradeNation'
  , 'mergeType'
  , 'note'
  , 'inputDate'
  , 'transMode'
  , 'tradeCountry'
  , 'despPort'
  , 'promiseItems'
  , 'validMark'
  , 'emsNo'
  , 'mawb'
  , 'hawb'
  , 'licenseNo'
  , 'invoiceNo'
  , 'contrNo'
  , 'packNum'
  , 'volume'
  , 'netWt'
  , 'destPort'
  , 'wrapType'
  , 'trafName'
  , 'otherCurr'
  , 'otherMark'
  , 'otherRate'
  , 'feeCurr'
  , 'feeMark'
  , 'feeRate'
  , 'insurCurr'
  , 'insurMark'
  , 'insurRate'
  , 'grossWt'
  , 'iedate'
  , 'districtCode'
  , 'receiveCode'
  , 'receiveName'
  , 'voyageDate'
  , 'shipDate'
  , 'overseasShipper'
  , 'overseasShipperName'
  , 'cutMode'
  , 'warehouse'
  , 'destinationCountry'
  , 'entryPort'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'declareDate'
  , 'maker'
  , 'sendUserFull'
  , 'apprDate'
  , 'apprUserFull'
  , 'apprStatus'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          key: 'selection',
          type: 'selection'
        },
        {
          key: 'operation'
        },
        {
          width: 120,
          title: '内审状态',
          key: 'apprStatusName'
        },
        {
          width: 120,
          title: '待审核日期',
          key: 'declareDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 150,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 180,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 150,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 150,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 100,
          title: '航班日期',
          key: 'flightDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 150,
          tooltip: true,
          key: 'declareName',
          title: '申报单位名称'
        },
        {
          width: 150,
          title: '货代',
          key: 'forwardCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.forwardCodeList)
          }
        },
        {
          width: 120,
          key: 'tradeMode',
          title: '监管方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 120,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 150,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 100,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 100,
          title: '净重',
          key: 'totolNet'
        },
        {
          width: 100,
          title: '毛重',
          key: 'totalWt'
        },
        {
          width: 150,
          key: 'maker',
          title: '制单员'
        },
        {
          width: 150,
          title: '最终修改人',
          key: 'sendUserFull'
        },
        {
          width: 150,
          title: '内审员',
          key: 'apprUserFull'
        },
        {
          width: 150,
          key: 'tradeCountry',
          title: '运抵国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
