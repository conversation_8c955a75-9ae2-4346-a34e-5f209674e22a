<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </div>
    </XdoCard>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="importTab" label="进口内审记录">
        <ErrorStatisticsList ref="importList" :show-search="showSearch" ie-mark="I"
                             :search-lines="searchLines" :search-model="searchConfig.model"></ErrorStatisticsList>
      </TabPane>
      <TabPane name="exportTab" label="出口内审记录">
        <ErrorStatisticsList ref="exportList" :show-search="showSearch" ie-mark="E"
                             :search-lines="searchLines" :search-model="searchConfig.model"></ErrorStatisticsList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { errorStatisticsTabs } from './js/errorStatisticsTabs'

  export default {
    name: 'errorStatisticsTabs',
    mixins: [errorStatisticsTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
