import { baseListConfig } from '@/mixin/generic/baseListConfig'

export const errorStatisticsList = {
  name: 'errorStatisticsList',
  mixins: [baseListConfig],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function(value) {
        return ['I', 'E'].includes(value)
      }
    },
    showSearch: {
      type: Boolean,
      default: () => false
    },
    searchLines: {
      type: Number,
      default: () => (3)
    },
    searchModel: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let fields = this.getFields()
    return {
      baseFields: [
        ...fields
      ],
      cmbSource: {},
      OffsetHeight: 2,
      hasActions: false,
      initSearch: false,
      hasChildTabs: true
    }
  },
  methods: {
    getSearchParams() {
      return {
        ...this.searchModel,
        apprType: this.ieMark
      }
    },
    getFields() {
      return []
    }
  }
}
