import { formatDate } from '@/libs/datetime'
import ErrorStatisticsList from '../error-statistics-list'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const errorStatisticsTabs = {
  components: {
    ErrorStatisticsList
  },
  mixins: [baseSearchConfig],
  data() {
    let params = this.getParams()
    return {
      tabName: 'importTab',
      tabs: {
        importTab: true,
        exportTab: false
      },
      cmbSource: {},
      baseParams: [
        ...params
      ]
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
    }
  },
  mounted() {
    let me = this
    me.$nextTick(() => {
      let nowTime = new Date(),
        today = formatDate(nowTime, 'yyyy-MM-dd'),
        fromDay = formatDate(new Date(nowTime.setFullYear(nowTime.getFullYear() - 1)), 'yyyy-MM-dd')
      me.baseParams[0].props.values = [fromDay, today]
      me.$nextTick(() => {
        me.handleSearchSubmit()
      })
    })
  },
  methods: {
    getParams() {
      return [{
        range: true,
        title: '制单日期',
        key: 'insertTime',
        props: {
          values: []
        }
      }, {
        title: '账号',
        key: 'insertUser'
      }]
    },
    handleSearchSubmit() {
      let me = this
      // if (me.tabName === 'importTab') {
      me.doImportSearch()
      // } else if (me.tabName === 'exportTab') {
      me.doExportSearch()
      // }
    },
    doImportSearch() {
      let me = this
      if (me.$refs['importList']) {
        if (typeof me.$refs['importList'].handleSearchSubmit === 'function') {
          me.$refs['importList'].handleSearchSubmit()
        }
      }
    },
    doExportSearch() {
      let me = this
      if (me.$refs['exportList']) {
        if (typeof me.$refs['exportList'].handleSearchSubmit === 'function') {
          me.$refs['exportList'].handleSearchSubmit()
        }
      }
    }
  }
}
