<template>
  <section>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" :disable="gridDisable"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { errorStatisticsList } from './js/errorStatisticsList'

  export default {
    name: 'errorStatisticsList',
    mixins: [errorStatisticsList],
    data() {
      return {
        ajaxUrl: {
          selectAllPaged: csAPI.aeoManage.errorStatistics.selectAllPaged
        }
      }
    },
    created: function () {
      let me = this
      if (me.ieMark === 'I') {
        me.$set(me.listConfig, 'exportTitle', '进口内审记录')
      } else if (me.ieMark === 'E') {
        me.$set(me.listConfig, 'exportTitle', '出口内审记录')
      }
    },
    methods: {
      getFields() {
        return [{
          width: 158,
          title: '账号',
          tooltip: true,
          key: 'userName'
        }, {
          width: 150,
          key: 'allCount',
          title: '总制单票数'
        }, {
          width: 150,
          title: '一次通过票数',
          key: 'noErrorCount'
        }, {
          width: 150,
          title: '内审退回票数',
          key: 'rollBackCount'
        }, {
          width: 150,
          key: 'passCount',
          title: '总内审通过票数'
        }, {
          width: 138,
          key: 'percent',
          title: '制单准确率'
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }
</style>
