import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
import { editStatus, aeoManage, ApprReject } from '@/view/cs-common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import AeoDecErpIESearch from '@/view/cs-aeoManage/components/ie-manage-search/ie-manage-search'

export const aeoIEManageList = {
  components: {
    ApprReject,
    AeoDecErpIESearch
  },
  mixins: [pms, commList],
  data() {
    return {
      searchLines: 5,
      innerMsg: {
        message: '',
        show: false
      },
      sendBill: false,
      reject: {
        batchData: [],
        apprShow: false,
        title: '批量内审退回'
      },
      toolbarEventMap: {
        'audit-pass': this.batchApplyPass,
        'audit-pass-send': this.passSend,
        'audit-return': this.batchApplyBack
      },
      cmbDataSource: {
        forwardCodeList: [],
        apprStatusList: aeoManage.apprStatusMap
      }
    }
  },
  created: function () {
    let me = this
    // 货代
    me.$http.post(me.ajaxUrl.getForwardCodes).then(res => {
      me.cmbDataSource.forwardCodeList = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbDataSource.forwardCodeList = []
    })
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
  },
  methods: {
    batchApply(pass) {
      let me = this,
        statusArr = ['2'],
        theRows = me.gridConfig.selectRows
      if (!pass) {
        statusArr = ['2', '8']
      }
      if (me.checkRowSelected('内审')) {
        let otherStatus = theRows.filter((item) => {
          return !statusArr.includes(item.apprStatus)
        })
        if (otherStatus.length > 0) {
          me.$Message.warning('仅待审核状态可内审!')
          return
        }
        if (pass) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认内审通过吗',
            onOk: () => {
              let theDatas = theRows.map(item => {
                return {
                  apprType: me.ieMark,
                  businessSid: item.sid,
                  auditNote: '已对单证涉及的价格、归类、原产地、数量、品名、规格等内容进行内审'
                }
              })
              if (me.sendBill) {
                me.$http.post(me.ajaxUrl.auditDataAll, theDatas, {
                  noIntercept: true
                }).then(res => {
                  if (res.data.success === true) {
                    if (isNullOrEmpty(res.data.message)) {
                      me.$Message.success('内审通过并发送清单成功!')
                    } else {
                      me.$Message.success(res.data.message)
                    }
                  } else {
                    let errMsg = '内审通过并发送清单失败!'
                    if (!isNullOrEmpty(res.data.message)) {
                      errMsg = res.data.message
                    }
                    me.$set(me.innerMsg, 'message', errMsg)
                    me.$set(me.innerMsg, 'show', true)
                  }
                }).catch(() => {
                }).finally(() => {
                  me.handleSearchSubmit()
                })
              } else {
                me.$http.post(me.ajaxUrl.auditDataM, theDatas).then(() => {
                  me.$Message.success('内审通过成功!')
                }).catch(() => {
                }).finally(() => {
                  me.handleSearchSubmit()
                })
              }
            }
          })
        } else {
          me.$set(me.reject, 'batchData', theRows.map(item => {
            return {
              sid: item.sid,
              apprType: me.ieMark
            }
          }))
          me.$set(me.reject, 'apprShow', true)
        }
      }
    },
    batchApplyPass() {
      let me = this
      me.$set(me, 'sendBill', false)
      me.batchApply(true)
    },
    passSend() {
      let me = this
      me.$set(me, 'sendBill', true)
      me.batchApply(true)
    },
    batchApplyBack() {
      let me = this
      me.batchApply(false)
    },
    /**
     * 列表中点击数据展示
     * @param item
     */
    handleViewByRow(item) {
      let me = this
      me.$set(me.editConfig, 'editStatus', editStatus.SHOW)
      me.$set(me.editConfig, 'editData', item)
      me.$set(me.editConfig, 'headId', item.sid)
      me.$set(me, 'showList', false)
    },
    /**
     * 审核退回后刷新界面
     * @param success
     */
    handleRejectReturn(success) {
      let me = this
      if (success) {
        me.$set(me.gridConfig, 'selectData', [])
        me.$set(me.gridConfig, 'selectRows', [])
        me.getList()
      }
      me.$set(me.reject, 'apprShow', false)
    },
    /**
     * 发送清单
     */
    sendBills() {
      let me = this,
        sids = me.getSelectedParams()
      me.$set(me.ajaxUrl, 'sendBills', csAPI.aeoManage.aeoReview.actions.sendBills)
      me.$http.post(`${me.ajaxUrl.sendBills}/${sids}/${me.ieMark}`).then(() => {
        me.$Message.success('发送清单成功!')
        me.getList()
      }).catch(() => {
      })
    }
  }
}
