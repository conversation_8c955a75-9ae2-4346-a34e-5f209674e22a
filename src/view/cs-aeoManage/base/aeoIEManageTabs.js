import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { isNumber, isNullOrEmpty } from '@/libs/util'
import { auditTrailProcessing } from './auditTrailProcessing'
import { editStatus, ApprReject, AeoInfoList } from '@/view/cs-common'
import Attach from '@/view/cs-ie-manage/attached-document/attached-document'
import { localJsonGet, localJsonSave } from '@/components/dynamic/config/js/comm/utils'
import DecAttachedDocumentsList from '../../cs-ie-manage/dec-attached-documents/dec-attached-documents-list'

export const aeoIEManageTabs = {
  components: {
    Attach,
    ApprReject,
    AeoInfoList,
    DecAttachedDocumentsList
  },
  mixins: [pms, auditTrailProcessing],
  props: {
    editConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      showBody: false,
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        packingTab: false,
        billTab: false,
        logisticsTab: false,
        entryTab: false,
        aeoTab: false
      },
      tabsRight: {
        attachTab: true,
        documentsTab: false
      },
      tabsRightName: 'attachTab',
      reject: {
        apprShow: false,
        apprResult: false,
        businessSid: this.editConfig.headId
      },
      headId: '',
      headData: {},
      aeoShow: true,
      aeoShowTitle: false,
      actions: [],
      buttons: [{
        ...btnComm, type: 'primary', label: '内审通过', click: this.handlePass
      }, {
        ...btnComm, type: 'error', label: '内审退回', click: this.handleRefuse
      }, {
        ...btnComm, type: 'primary', label: '关闭', click: this.handleBack
      }],
      collapsed: false,
      parentConfig: {
        editData: {},
        editStatus: editStatus.SHOW
      }
    }
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function(config) {
        let me = this
        if (config.editData && !isNullOrEmpty(config.editData.sid)) {
          me.showBody = true
          me.parentConfig.editData = config.editData
        } else {
          me.showBody = false
          me.parentConfig.editData = {
            sid: '',
            emsListNo: '',
            apprStatus: '',
            districtCode: '',
            districtPostCode: ''
          }
        }
      }
    },
    tabName(value) {
      let me = this
      me.tabs[value] = true
      if (['bodyTab', 'billTab'].includes(value)) {
        me.$nextTick(() => {
          let bodyHeight = null, tabsContent = undefined
          if ('bodyTab' === value && me.$refs.body) {
            bodyHeight = me.$refs.body.$el.offsetHeight
            tabsContent = me.$refs.body.$el.parentNode
          } else if ('billTab' === value && me.$refs.bill) {
            bodyHeight = me.$refs.bill.$el.offsetHeight
            tabsContent = me.$refs.bill.$el.parentNode
          }
          if (tabsContent && isNumber(bodyHeight)) {
            while (tabsContent && !(tabsContent.className.indexOf('ivu-tabs-content') > -1)) {
              tabsContent = tabsContent.parentNode
            }
            if (tabsContent) {
              tabsContent.style.height = bodyHeight + 'px'
            }
            let layoutContent = tabsContent.parentNode
            while (layoutContent && !(layoutContent.className.indexOf('ivu-layout-content') > -1)) {
              layoutContent = layoutContent.parentNode
            }
            let contentBox = layoutContent.parentNode
            while (contentBox && contentBox.className !== 'content-box') {
              contentBox = contentBox.parentNode
            }
            if (layoutContent) {
              layoutContent.style.height = (contentBox.offsetHeight) + 'px'
              layoutContent.style.overflow = 'hidden'
            }
          }
        })
      }
    },
    tabsRightName(value) {
      let me = this
      me.tabsRight[value] = true
    }
  },
  created: function() {
    let me = this,
      localResult = localJsonGet(me.aeoKey)
    if (localResult.success && localResult.data) {
      if (localResult.data['collapsed'] === true) {
        me.$set(me, 'collapsed', true)
      } else if (localResult.data['collapsed'] === false) {
        me.$set(me, 'collapsed', false)
      }
    }
    me.loadFunctions('tabs').then()
  },
  mounted: function() {
    let me = this
    me.showBody = true
    me.headId = me.editConfig.headId
    me.headData = me.editConfig.editData
  },
  methods: {
    editBack(val) {
      if (val) {
        let me = this
        me.$emit('onEditBack', {
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      }
    },
    editSave(val) {
      let me = this
      me.showBody = true
      me.headId = val.sid
      me.headData = val
    },
    /**
     * 刷新清单信息
     */
    refreshBill() {
      let me = this
      if (me.$refs.bill) {
        if (typeof me.$refs.bill.initFrm === 'function') {
          me.$refs.bill.initFrm()
        }
      }
    },
    handlePass() {
      let me = this
      if (me.hasErrFields) {
        me.$Message.success('有错误数据，无法进行此操作!')
        return
      }
      me.buttons[0].loading = true
      me.$http.post(csAPI.aeoManage.aeoReview.actions.auditData, {
        apprType: me.reject.apprType,
        businessSid: me.reject.businessSid,
        apprNote: '已对单证涉及的价格、归类、原产地、数量、品名、规格等内容进行内审'
      }).then(() => {
        me.$Message.success('内审通过成功!')
        // me.doAuditTrailClear()
        me.handleRejectReturn(true, true)
      }).catch(() => {
      }).finally(() => {
        me.buttons[0].loading = false
      })
    },
    handleRefuse() {
      let me = this
      me.$set(me.reject, 'apprShow', true)
      me.$set(me.reject, 'apprResult', false)
    },
    handleBack() {
      let me = this
      me.editBack(true)
    },
    handleRejectReturn(success, isReturn) {
      let me = this
      me.reject.apprShow = false
      if (isReturn === true) {
        me.doSaveAuditTrail(function() {
          if (success) {
            me.editBack(true)
          }
        })
      } else {
        if (success) {
          me.editBack(true)
        }
      }
    },
    /**
     * 显示/隐藏右侧信息
     * @param e
     */
    handleRightSliderClick(e) {
      let me = this
      me.$set(me, 'collapsed', !me.collapsed)
      if (me.collapsed) {
        e.target.style.transform = 'rotate(90deg)'
        e.target.style['-webkit-transform'] = 'rotate(90deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 10px'
        }
      } else {
        e.target.style.transform = 'rotate(0deg)'
        e.target.style['-webkit-transform'] = 'rotate(0deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 22px'
        }
      }
      localJsonSave(me.aeoKey, {
        collapsed: me.collapsed
      })
    },
    getHeadId() {
      let me = this
      return me.editConfig.editData.sid
    },
    /**
     * 根据tab标志判断是否显示Tab
     * @param tabCommand
     */
    tabShowByAction(tabCommand) {
      let me = this,
        tabActions = me.actions.filter(action => {
          return action.command === tabCommand
        })
      if (Array.isArray(tabActions) && tabActions.length > 0) {
        return me.showBody
      }
      return false
    },
  },
  computed: {
    /**
     * 箱单信息Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    packingTabShow() {
      let me = this,
        tabCommand = 'packingTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 物流追踪Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    logisticsTabShow() {
      let me = this,
        tabCommand = 'logisticsTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 报关追踪Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    entryTabShow() {
      let me = this,
        tabCommand = 'entryTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 当状态为【待审核】时方可执行审核操作
     * @returns {boolean}
     */
    canAeoAudit() {
      let me = this
      if (!isNullOrEmpty(me.editConfig.editData.sid)) {
        if (['2', '8'].includes(me.editConfig.editData.apprStatus)) {
          return true
        }
      }
      return false
    },
    iconClass() {
      let me = this
      if (me.collapsed) {
        return 'transform: rotate(90deg); -webkit-transform: rotate(90deg);'
      } else {
        return 'transform: rotate(0deg); -webkit-transform: rotate(0deg);'
      }
    }
  }
}
