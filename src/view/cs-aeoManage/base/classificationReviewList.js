import pms from '@/libs/pms'
import { isNullOrEmpty } from '@/libs/util'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { editStatus, productClassify, ApprReject } from '@/view/cs-common'
import SearchForm from '@/view/cs-aeoManage/components/material-search/material-search'
import { detailGeneralMethod } from '@/view/cs-productClassify/base/detailGeneralMethod'
import MaterielProductHSCodeCheckPop from '../components/materiel-product-hscode-check/materiel-product-hscode-check-pop'

export const classificationReviewList = {
  components: {
    SearchForm,
    ApprReject,
    MaterielProductHSCodeCheckPop
  },
  mixins: [pms, commList, detailGeneralMethod],
  data() {
    return {
      editConfig: {
        gmark: ''
      },
      listShow: true,
      BondedImg: false,
      BondedExg: false,
      NonBonded: false,
      modalShow: false,
      batchData: [],
      // 查询条件行数
      searchLines: 4,
      searchData: {},
      cmbSource: {
        emsNoData: []
      },
      hsCodeCheckPop: {
        type: '',       // 0: 勾选; 1: 全部
        data: [],
        show: false
      },
      reject: {
        batchData: [],
        apprShow: false,
        title: '批量内审退回'
      },
      productClassify: productClassify,
      toolbarEventMap: {
        'all-pass': this.batchAllPass,
        'all-return': this.batchAllBack,
        'audit-pass': this.batchApplyPass,
        'audit-return': this.batchApplyBack
      }
    }
  },
  watch: {
    BondedImg: {
      immediate: true,
      handler: function (val) {
        if (val === true) {
          this.$set(this.editConfig, 'gmark', 'I')
        }
      }
    },
    BondedExg: {
      immediate: true,
      handler: function (val) {
        if (val === true) {
          this.$set(this.editConfig, 'gmark', 'E')
        }
      }
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
    me.getEmsNoList(req => {
      me.$set(me.cmbSource, 'emsNoData', req.data.map(item => {
        return {
          value: item.emsNo,
          label: item.emsNo,
          copEmsNo: item.copEmsNo
        }
      }))
    })
  },
  methods: {
    /**
     * 获取GMark类型
     * @param bondedFlag
     * @param gMark
     * @returns {string|*}
     */
    getAuditType(bondedFlag, gMark) {
      if (bondedFlag === '1') {
        return 'M'
      }
      return gMark
    },
    /**
     * 行双击事件
     * @param item
     */
    handleRowDblClick(item) {
      let me = this
      me.editConfig.editStatus = editStatus.SHOW
      me.editConfig.editData = item
      me.editConfig.headId = item.sid
      me.showList = false

      me.NonBonded = item.bondedFlag === "1"
      me.showList = false
      me.BondedExg = item.gmark === "E" && !me.NonBonded
      me.BondedImg = item.gmark === "I" && !me.NonBonded
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      let me = this
      me.handleRowDblClick(row)
    },
    /**
     * 处理审核
     * @param pass
     */
    batchApply(pass) {
      let me = this
      if (me.checkRowSelected('内审')) {
        const theRows = me.gridConfig.selectRows
        let statusArr = ['2']
        if (!pass) {
          statusArr = ['2', '8']
        }
        let otherStatus = theRows.filter((item) => {
          return !statusArr.includes(item.apprStatus)
        })
        if (otherStatus.length > 0) {
          me.$Message.warning('仅待审核状态可内审!')
          return
        }
        if (pass) {
          let sids = me.getSelectedParams()
          me.$http.post(me.ajaxUrl.checkCodeTS, sids).then(res => {
            if (Array.isArray(res.data.data) && res.data.data.length > 0) {
              me.$set(me.hsCodeCheckPop, 'type', '0')
              me.$set(me.hsCodeCheckPop, 'data', res.data.data)
              me.$set(me.hsCodeCheckPop, 'show', true)
            } else {
              me.$Modal.confirm({
                title: '提醒',
                okText: '确认',
                cancelText: '取消',
                content: '确认内审通过吗',
                onOk: () => {
                  me.doApplyBatchPass()
                }
              })
            }
          }).catch(() => {
          })
        } else {
          me.reject.batchData = theRows.map(item => {
            return {
              sid: item.sid,
              apprType: me.apprTypePre + me.getAuditType(item.bondedFlag, item.gmark)
            }
          })
          me.reject.apprShow = true
        }
      }
    },
    /**
     * 执行审核通过(勾选项)
     */
    doApplyBatchPass() {
      let me = this,
        theRows = me.gridConfig.selectRows,
        theDatas = theRows.map(item => {
          return {
            businessSid: item.sid,
            auditNote: '已对单证涉及的价格、归类、原产地、数量、品名、规格等内容进行内审',
            apprType: me.apprTypePre + me.getAuditType(item.bondedFlag, item.gmark)
          }
        })
      me.$http.post(me.ajaxUrl.auditDataM, theDatas).then(res => {
        if (isNullOrEmpty(res.data.message)) {
          me.$Message.success('内审通过成功!')
        } else {
          me.$Message.success(res.data.message)
        }
        me.handleSearchSubmit()
      }).catch(() => {
      })
    },
    /**
     * 处理全部审核
     * @param pass
     */
    batchAllApply(pass) {
      let me = this
      if (pass) {
        me.$http.post(me.ajaxUrl.checkCodeTSAll, me.getSearchParams()).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.hsCodeCheckPop, 'type', '1')
            me.$set(me.hsCodeCheckPop, 'data', res.data.data)
            me.$set(me.hsCodeCheckPop, 'show', true)
          } else {
            me.$Modal.confirm({
              title: '提醒',
              okText: '确认',
              cancelText: '取消',
              content: '确认内审通过吗',
              onOk: () => {
                me.doApplyAllPass()
              }
            })
          }
        }).catch(() => {
        })
      } else {
        me.reject.batchData = []
        me.reject.apprShow = true
        me.searchData = this.$refs.headSearch.searchParam
      }
    },
    /**
     * 执行审核通过(所有)
     */
    doApplyAllPass() {
      let me = this,
        url = ''
      if (me.apprTypePre === 'O') {
        url = me.ajaxUrl.auditDateByList
      } else if (me.apprTypePre === 'Y') {
        url = me.ajaxUrl.matImgexg
      }
      me.$http.post(url, me.getSearchParams()).then(res => {
        if (isNullOrEmpty(res.data.message)) {
          me.$Message.success('内审通过成功!')
        } else {
          me.$Message.success(res.data.message)
        }
        me.handleSearchSubmit()
      }).catch(() => {
      })
    },
    /**
     * 继续审核通过
     */
    doContinue() {
      let me = this
      if (me.hsCodeCheckPop.type === '0') {
        me.doApplyBatchPass()
      }
      if (me.hsCodeCheckPop.type === '1') {
        me.doApplyAllPass()
      }
      me.$set(me.hsCodeCheckPop, 'type', '')
      me.$set(me.hsCodeCheckPop, 'data', [])
      me.$set(me.hsCodeCheckPop, 'show', false)
    },
    /**
     * 审核通过
     */
    batchApplyPass() {
      let me = this
      me.batchApply(true)
    },
    /**
     * 审核退回
     */
    batchApplyBack() {
      let me = this
      me.batchApply(false)
    },
    /**
     * 审核退回后刷新界面
     */
    handleRejectReturn(success) {
      let me = this
      if (success) {
        me.gridConfig.selectData = []
        me.gridConfig.selectRows = []
        me.getList()
      }
      me.reject.apprShow = false
    },
    /**
     * 全部通过
     */
    batchAllPass() {
      let me = this
      me.batchAllApply(true)
    },
    /**
     * 全部退回
     */
    batchAllBack() {
      let me = this
      me.batchAllApply(false)
    }
  }
}
