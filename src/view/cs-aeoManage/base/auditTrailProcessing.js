import { csAPI } from '@/api'
import { isNullOrEmpty, isNumber } from '@/libs/util'

// 审核记录相关操作(查询界面)
export const auditTrailProcessing = {
  data() {
    return {
      auditTrail: {
        head: {},
        body: {}
      },
      auditMessage: '',
      headErrLength: 0,
      bodyErrLength: 0,
      ajaxUrl: {
        clearAuditLog: csAPI.aeoManage.auditTrack.clear,
        insertAuditLog: csAPI.aeoManage.auditTrack.insert,
        getAuditLog: csAPI.aeoManage.auditTrack.selectAllPaged
      }
    }
  },
  created: function() {
    let me = this
    me.loadAuditData()
  },
  computed: {
    headAuditData() {
      let me = this
      if (me.auditTrail.hasOwnProperty('head')) {
        return me.auditTrail['head']['auditData'] || {}
      }
      return {}
    },
    bodyAuditData() {
      let me = this
      if (me.auditTrail.hasOwnProperty('body')) {
        return me.auditTrail['body'] || {}
      }
      return {}
    },
    hasErrFields() {
      let me = this
      if (me.headErrLength > 0) {
        return true
      }
      return me.bodyErrLength > 0
    }
  },
  methods: {
    /**
     * 获取表头主键
     * @returns {string}
     */
    getHeadId() {
      return ''
    },
    /**
     * 将审核痕迹由对象转换为保存至服务器的实体数组
     */
    convertAuditDataToArray() {
      let me = this,
        headId = me.getHeadId(),
        headObj = me.auditTrail.head || {},
        bodyObj = me.bodyAuditData,
        saveArray = []
      if (!isNullOrEmpty(headId)) {
        if (headObj && headObj.hasOwnProperty('auditData')) {
          saveArray.push({
            headId: headId,
            listId: headId,
            errorCount: me.headErrLength,
            errorMessage: JSON.stringify(headObj)
          })
        }
        if (bodyObj) {
          let bodyTmp = {}
          Object.keys(bodyObj).forEach(bodySid => {
            bodyTmp = bodyObj[bodySid]
            if (bodyTmp.hasOwnProperty('auditData')) {
              saveArray.push({
                headId: headId,
                listId: bodySid,
                errorCount: me.getBodyErrLength(bodyTmp.auditData),
                errorMessage: JSON.stringify(bodyTmp)
              })
            }
          })
        }
      }
      return saveArray
    },
    /**
     * 将数据库返回之数据转换为审核痕迹对象
     * @param auditDataArray
     */
    convertAuditDataToObject(auditDataArray) {
      let me = this,
        headId = me.getHeadId(),
        headObj = {},
        headErrLength = 0,
        body = {},
        bodyErrLength = 0
      if (!isNullOrEmpty(headId) && Array.isArray(auditDataArray)) {
        // 表头数据
        let headItems = auditDataArray.filter(auditData => {
          return auditData.listId === headId
        })
        if (Array.isArray(headItems) && headItems.length === 1) {
          try {
            headObj = JSON.parse(headItems[0].errorMessage)
            if (isNumber(headItems[0].errorCount)) {
              headErrLength = Number(headItems[0].errorCount)
            }
          } catch (e) {
            headObj = {}
            headErrLength = 0
            console.error(e.message)
          }
        }
        // 表体数据
        let bodyItems = auditDataArray.filter(auditData => {
          return auditData.headId === headId && auditData.headId !== auditData.listId
        }), bodyObj = {}
        if (Array.isArray(bodyItems)) {
          bodyItems.forEach(bodyItem => {
            if (!isNullOrEmpty(bodyItem.listId)) {
              try {
                bodyObj = JSON.parse(bodyItem.errorMessage)
                if (isNumber(bodyItem.errorCount)) {
                  bodyErrLength += Number(bodyItem.errorCount)
                }
              } catch (e) {
                bodyObj = {}
                bodyErrLength = 0
                console.error(e.message)
              }
              body[bodyItem.listId] = bodyObj
            }
          })
        }
      }
      return {
        head: headObj,
        headErrLength: headErrLength,
        body: body,
        bodyErrLength: bodyErrLength
      }
    },
    /**
     * 加载审核痕迹信息
     */
    loadAuditData() {
      let me = this,
        headId = me.getHeadId()
      if (!isNullOrEmpty(headId)) {
        me.$http.post(me.ajaxUrl.getAuditLog + '/' + headId).then(res => {
          let result = me.convertAuditDataToObject(res.data.data)
          me.$set(me.auditTrail, 'head', result.head)
          me.$set(me, 'headErrLength', result.headErrLength)
          me.$set(me.auditTrail, 'body', result.body)
          me.$set(me, 'bodyErrLength', result.bodyErrLength)
          me.resetFullMsg()
        }).catch(() => {
        })
      }
    },
    /**
     * 重置错误信息
     */
    resetFullMsg() {
      let me = this,
        message = '', bodyMsg = ''
      if (me.auditTrail.hasOwnProperty('head')) {
        if (!isNullOrEmpty(me.auditTrail.head.message)) {
          message = '表头问题: ' + me.auditTrail.head.message
        }
      }
      if (me.auditTrail.hasOwnProperty('body')) {
        let bodyObj = me.auditTrail.body || {},
          body = {}
        Object.keys(bodyObj).forEach(sid => {
          body = bodyObj[sid] || {}
          if (!isNullOrEmpty(body.message)) {
            bodyMsg += body.message + '\n'
          }
        })
      }
      if (!isNullOrEmpty(bodyMsg)) {
        message = message.trim() + '\n表体问题: ' + bodyMsg
      }
      me.$set(me, 'auditMessage', message)
    },
    /**
     * 重置表头错误字段数量
     */
    resetHeadErrLength() {
      let me = this,
        errLength = 0
      if (me.headAuditData) {
        Object.keys(me.headAuditData).forEach(fieldName => {
          if (me.headAuditData[fieldName] === '2') {
            errLength++
          }
        })
      }
      me.$set(me, 'headErrLength', errLength)
    },
    /**
     * 表头审批痕迹变更
     * @param headAudit
     */
    onHeadAuditChanged(headAudit) {
      let me = this,
        message = '',
        auditData = {}
      if (headAudit) {
        if (headAudit.auditData) {
          auditData = headAudit.auditData
        }
        if (!isNullOrEmpty(headAudit.message)) {
          message = headAudit.message
        }
      }
      me.$set(me.auditTrail, 'head', {
        message: message,
        auditData: auditData
      })
      me.resetHeadErrLength()
      me.resetFullMsg()
    },
    /**
     * 重置表体错误字段数量
     */
    resetBodyErrLength() {
      let me = this,
        errLength = 0
      if (me.bodyAuditData) {
        Object.keys(me.bodyAuditData).forEach(bodyKey => {
          let body = me.bodyAuditData[bodyKey]
          if (body && body.hasOwnProperty('auditData')) {
            Object.keys(body.auditData).forEach(field => {
              if (body.auditData[field] === '2') {
                errLength++
              }
            })
          }
        })
      }
      me.$set(me, 'bodyErrLength', errLength)
    },
    /**
     * 根据表体痕迹获取错误字段量
     * @param bodyAudit
     */
    getBodyErrLength(bodyAudit) {
      let result = 0
      if (bodyAudit) {
        Object.keys(bodyAudit).forEach(field => {
          if (bodyAudit[field] === '2') {
            result++
          }
        })
      }
      return result
    },
    /**
     * 表体审批痕迹变更
     * @param bodyAudit
     */
    onBodyAuditChanged(bodyAudit) {
      let me = this,
        message = '',
        auditData = {},
        body = me.auditTrail.body || {}
      if (bodyAudit && !isNullOrEmpty(bodyAudit.sid)) {
        if (bodyAudit.auditData) {
          auditData = bodyAudit.auditData
        }
        if (!isNullOrEmpty(bodyAudit.message)) {
          message = bodyAudit.message
        }
        body[bodyAudit.sid] = {
          message: message,
          auditData: auditData
        }
        me.$set(me.auditTrail, 'body', body)
        me.resetBodyErrLength()
        me.resetFullMsg()
      }
    },
    /**
     * 保存审核痕迹
     * @param afterSave
     */
    doSaveAuditTrail(afterSave) {
      let me = this,
        headId = me.getHeadId()
      if (!isNullOrEmpty(headId)) {
        let saveArray = me.convertAuditDataToArray()
        me.$http.post(me.ajaxUrl.insertAuditLog + '/' + headId, saveArray).then(() => {
        }).catch(() => {
        }).finally(() => {
          if (typeof afterSave === 'function') {
            afterSave.call(me)
          }
        })
      }
    }
    /**
     * 清空审核痕迹
     * @param afterClear
     */
    , doAuditTrailClear(afterClear) {
      let me = this,
        headId = me.getHeadId()
      if (!isNullOrEmpty(headId)) {
        me.$http.post(me.ajaxUrl.clearAuditLog + '/' + headId).then(() => {
        }).catch(() => {
        }).finally(() => {
          if (typeof afterClear === 'function') {
            afterClear.call(me)
          }
        })
      }
    }
  }
}
