// 审核记录相关操作(详细界面)
export const auditTrailDetail = {
  props: {
    savedAuditData: {
      type: Object,
      default: () => ({})
    },
    canAeoAudit: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      keyTitle: {},
      auditData: {},
      formDataName: 'frmData',
      onAuditChangeEvent: 'onHeadAuditChanged'
    }
  },
  watch: {
    savedAuditData: {
      deep: true,
      immediate: true,
      handler: function (auditData) {
        let me = this
        if (auditData.hasOwnProperty('auditData')) {
          me.loadAuditData(auditData.auditData)
        } else {
          me.loadAuditData(auditData)
        }
      }
    }
  },
  computed: {
    /**
     * (审核痕迹)展示信息
     * @returns {auditData|{}|string|{}|default.props.auditData|{default, type}|*}
     */
    auditDataShow() {
      let me = this,
        originalData = {}
      if (!me.aeoShow) {
        Object.keys(me.auditData).forEach(key => {
          if (me.auditData[key] === '2') {
            originalData[key] = '2'
          }
        })
        return originalData
      }
      return me.auditData
    }
  },
  methods: {
    /**
     * 加载审核痕迹信息
     */
    loadAuditData(auditData) {
      let me = this
      me.$set(me, 'auditData', (auditData || {}))
    },
    /**
     * 根据企业编号修改信息内容(用于表体)
     * @param message
     * @returns {*}
     */
    setMsgByFacGNo(message) {
      return message
    },
    /**
     * 审核痕迹变更事件
     * @param auditData
     * @param keyTitle
     */
    onAuditChange(auditData, keyTitle) {
      let me = this,
        message = ''
      if (keyTitle) {
        Object.keys(keyTitle).forEach(key => {
          me.$set(me.keyTitle, key, keyTitle[key])
        })
      } else {
        Object.keys(auditData).forEach(key => {
          me.$set(me.auditData, key, auditData[key])
        })
        Object.keys(me.auditData).forEach(key => {
          if (me.keyTitle.hasOwnProperty(key) && me.auditData[key] === '2') {
            message += me.keyTitle[key] + ','
          }
        })
        me.$emit(me.onAuditChangeEvent, {
          auditData: me.auditData,
          sid: me[me.formDataName].sid,
          message: me.setMsgByFacGNo(message)
        })
      }
    }
  }
}
