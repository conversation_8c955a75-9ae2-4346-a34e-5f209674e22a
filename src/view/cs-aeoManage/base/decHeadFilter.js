import { namespace } from '@/project'
import { editStatus } from '@/view/cs-common'
import DynamicHeadEdit from '../../cs-ie-manage/dec-erp-head/dynamic/dynamic-head-edit'

export const decHeadFilter = {
  name: 'decHeadFilter',
  components: {
    DynamicHeadEdit
  },
  props: {
    editConfig: {
      type: Object,
      default: () => ({
        editData: {}
      })
    },
    canAeoAudit: {
      type: Boolean,
      default: () => false
    },
    savedAuditData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    /**
     * 通关业务设置
     */
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    /**
     * 动态表头
     * @returns {boolean}
     */
    dynamicHeadTabShow() {
      return this.configData.decType === '1'
    },
    aeoConfig() {
      return {
        editStatus: editStatus.SHOW,
        editData: this.editConfig.editData
      }
    },
    bondMark() {
      let me = this
      if (['0', '1'].includes(me.editConfig.editData.bondMark)) {
        return me.editConfig.editData.bondMark
      }
      return ''
    },
    /**
     * 是否大提单
     * @returns {boolean}
     */
    compositeHead() {
      let me = this
      if (me.dynamicHeadTabShow) {
        return false
      }
      return me.editConfig.editData && me.editConfig.editData.decType === '0'
    },
    /**
     * 是否小提单(保税)
     */
    isBonded() {
      let me = this
      if (me.dynamicHeadTabShow) {
        return false
      }
      return me.editConfig.editData
        && me.editConfig.editData.decType === '1'
        && me.editConfig.editData.bondMark === '0'
    },
    /**
     * 是否小提单(非保税)
     */
    isNonBonded() {
      let me = this
      if (me.dynamicHeadTabShow) {
        return false
      }
      return me.editConfig.editData
        && me.editConfig.editData.decType === '1'
        && me.editConfig.editData.bondMark === '1'
    }
  },
  methods: {
    onEditBack() {
      let me = this
      me.$emit('onEditBack', {
        editData: {},
        showList: false,
        editStatus: editStatus.SHOW
      })
    },
    onHeadAuditChanged(auditData) {
      let me = this
      me.$emit('onHeadAuditChanged', auditData)
    }
  }
}
