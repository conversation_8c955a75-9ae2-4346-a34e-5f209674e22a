/**
 * 自定义设置
 * @type {{data(): {toolbarEventMap: {setting: tableColumnSetup.methods.handleTableColumnSetup}, grdShow: boolean, columnSetup: {listId: string, show: boolean, settingColumns: []}}, created: tableColumnSetup.created, methods: {loadDefaultColumns(): void, handleUpdateColumn(*): void, getListId(): void, handleTableColumnSetup(): void}}}
 */
export const tableColumnSetup = {
  data() {
    return {
      grdShow: true,
      columnSetup: {
        listId: '',
        show: false,
        settingColumns: []
      },
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this
    me.getListId()
  },
  methods: {
    loadDefaultColumns() {
      let me = this
      me.handleUpdateColumn(me.$bom3.showTableColumns(me.columnSetup.listId, me.columnSetup.settingColumns))
    },
    /**
     * 获取设置key
     */
    getListId() {
      let me = this
      me.$set(me.columnSetup, 'listId', me.$route.path + '/' + me.$options.name + '/listId')
    },
    /**
     * 自定义列设置
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me.columnSetup, 'show', true)
    },
    /**
     * 设置显示列
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'grdShow', false)
      me.$set(me.gridConfig, 'gridColumns', [{
        key: 'selection',
        type: 'selection'
      }, {
        key: 'operation'
      }, ...columns])
      me.$set(me.columnSetup, 'show', false)
      me.$nextTick(() => {
        me.$set(me, 'grdShow', true)
      })
    }
  }
}
