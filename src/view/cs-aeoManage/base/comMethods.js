import { csAPI } from '@/api'

export const comMethods = {
  data() {
    return {
      ajaxUrl: {
        freeParams: csAPI.tax.list.freeParams,
        getItemNo: csAPI.taxExemptionEquipment.projectConfirmation.getItemNoList
      }
    }
  },
  created() {
    let me = this
    // 获取供应商
    me.$http.post(csAPI.ieParams.PRD).then(res => {
      me.cmbSource.tradeCodeData = res.data.data.map(item => {
        return {
          label: item['LABEL'],
          value: item['VALUE']
        }
      })
    }).catch(() => {
      me.cmbSource.tradeCodeData = []
    })
    // 获取报关行
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.cmbSource.declareData = res.data.data.map(item => {
        return {
          label: item['LABEL'],
          value: item['CODE']
        }
      })
    }).catch(() => {
      me.cmbSource.declareData = []
    })
    me.getFreeParams()
    me.getItemNo()
  },
  methods: {
    /**
     * 获取企业自定义参数
     */
    getFreeParams() {
      let me = this
      me.$http.post(`${me.ajaxUrl.freeParams}/${'AVOID_PARAMS'}`).then(res => {
        for (let item in res.data.data) {
          Object.keys(res.data.data[item]).map(key => {
            if (item === '0') {
              return me.cmbSource.apprFrom.push(res.data.data[item][key])
            } else if (item === '1') {
              return me.cmbSource.apprDept.push(res.data.data[item][key])
            } else if (item === '2') {
              return me.cmbSource.apprItem.push(res.data.data[item][key])
            } else if (item === '3') {
              return me.cmbSource.billType.push(res.data.data[item][key])
            }
          })
        }
      }).catch(() => {
      })
    },
    getItemNo() {
      let me = this
      me.$http.post(me.ajaxUrl.getItemNo).then(res => {
        me.$set(me.cmbSource, 'itemNoData', res.data.data.map(item => {
          return {
            label: item,
            value: item
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'itemNoData', [])
      })
    }
  }
}
