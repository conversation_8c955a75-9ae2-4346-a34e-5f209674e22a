<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="ImportTab" v-if="auditIShow" label="进口内审记录" index="'A'">
        <InternalAuditRecordIEQueryList v-if="tabs.ImportTab" :tabName="tabName"></InternalAuditRecordIEQueryList>
      </TabPane>
      <TabPane name="ExportTab" v-if="auditEShow" label="出口内审记录" index="'B'">
        <InternalAuditRecordIEQueryList v-if="tabs.ExportTab" :tabName="tabName"></InternalAuditRecordIEQueryList>
      </TabPane>
      <TabPane name="RecordTab" v-if="filingShow" label="海关备案内审记录" index="'C'">
        <InternalAuditRecordQueryList v-if="tabs.RecordTab" :tabName="tabName"></InternalAuditRecordQueryList>
      </TabPane>
      <TabPane name="MatterTab" v-if="materialShow" label="企业物料内审记录" index="'D'">
        <InternalAuditRecordMatterList v-if="tabs.MatterTab" :tabName="tabName"></InternalAuditRecordMatterList>
      </TabPane>
      <TabPane name="singleTab" v-if="singleLossShow" label="单损耗内审记录" index="'E'">
        <SingleLossCheckRecordQueryList v-if="tabs.singleTab" :tabName="tabName"></SingleLossCheckRecordQueryList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import InternalAuditRecordQueryList from '../internal-audit-record-query/internal-audit-record-query-list'
  import InternalAuditRecordMatterList from '../internal-audit-record-matter/internal-audit-record-matter-list'
  import InternalAuditRecordIEQueryList from '../internal-audit-record-ie-query/internal-audit-record-ie-query-list'
  import SingleLossCheckRecordQueryList from '../single-loss-check-record-query/single-loss-check-record-query-list'

  export default {
    mixins: [pms],
    name: 'internalAuditRecordQueryTab',
    components: {
      InternalAuditRecordQueryList,
      InternalAuditRecordMatterList,
      SingleLossCheckRecordQueryList,
      InternalAuditRecordIEQueryList
    },
    data() {
      return {
        actions: [],
        tabName: 'ImportTab',
        tabs: {
          ImportTab: true,
          ExportTab: false,
          RecordTab: false,
          MatterTab: false,
          singleTab: false
        }
      }
    },
    watch: {
      tabName: {
        immediate: true,
        handler: function(tName) {
          let me = this
          me.tabs[tName] = true
          for (let tabI in me.tabs) {
            if (tabI !== tName) {
              me.tabs[tabI] = false
            }
          }
        }
      }
    },
    computed: {
      /**
       * 进口内审记录
       * @returns {dynamicTabs.methods.showBody|boolean}
       */
      auditIShow() {
        let me = this,
          tabCommand = 'auditI'
        return me.tabShowByAction(tabCommand)
      },
      /**
       * 出口内审记录
       * @returns {dynamicTabs.methods.showBody|boolean}
       */
      auditEShow() {
        let me = this,
          tabCommand = 'auditE'
        return me.tabShowByAction(tabCommand)
      },
      /**
       * 海关备案内审记录
       */
      filingShow() {
        let me = this,
          tabCommand = 'filing'
        return me.tabShowByAction(tabCommand)
      },
      /**
       * 企业物料内审记录
       */
      materialShow() {
        let me = this,
          tabCommand = 'material'
        return me.tabShowByAction(tabCommand)
      },
      /**
       * 单损耗内审记录
       */
      singleLossShow() {
        let me = this,
          tabCommand = 'singleLoss'
        return me.tabShowByAction(tabCommand)
      }
    },
    created: function() {
      let me = this
      me.loadFunctions('tabs').then(() => {
      })
    },
    methods: {
      /**
       * 根据 tab 标志判断是否显示 Tab
       * @param tabCommand
       */
      tabShowByAction(tabCommand) {
        let me = this,
          tabActions = me.actions.filter(action => {
            return action.command === tabCommand
          })
        return Array.isArray(tabActions) && tabActions.length > 0
      }
    }
  }
</script>
