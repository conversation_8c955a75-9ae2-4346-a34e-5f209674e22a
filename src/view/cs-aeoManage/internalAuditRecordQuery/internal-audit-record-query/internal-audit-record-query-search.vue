<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="操作时间" @onDateRangeChanged="handleApprDateChange"></dc-dateRange>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleInsertTimeChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="status" label="内审情况">
        <xdo-select v-model="searchParam.status" :options="this.cmbSource.apprStatusData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="erpInsertUser" label="操作员">
        <XdoIInput type="text" v-model="searchParam.erpInsertUser"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { aeoManage, certificate } from '@/view/cs-common'

  export default {
    name: 'internalAuditRecordQuerySearch',
    data() {
      return {
        searchParam: {
          copGNo: '',
          apprDateFrom: '',
          apprDateTo: '',
          status: '',
          erpInsertUser: '',
          apprUser: '',
        },
        certificate: certificate,
        cmbSource: {
          apprStatusData: aeoManage.apprStatusMapList
        }
      }
    },
    methods: {
      /**
       * 出、出库日期修改
       */
      handleApprDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "apprDateFrom", values[0])
          this.$set(this.searchParam, "apprDateTo", values[1])
        } else {
          this.$set(this.searchParam, "apprDateFrom", '')
          this.$set(this.searchParam, "apprDateTo", '')
        }
      },
      /**
       * 制单日期
       * @param values
       */
      handleInsertTimeChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }

    },
    computed: {
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style scoped>
</style>
