<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" label-width="110">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { aeoManage } from '@/view/cs-common'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

  export default {
    name: 'singleLossCheckRecordQueryList',
    mixins: [baseListConfig, baseSearchConfig],
    data() {
      let params = this.getCommParams()
      let fields = this.getCommFields()
      return {
        autoCreate: false,
        baseParams: [
          ...params
        ],
        baseFields: [
          ...fields
        ],
        actionsComm: {
          needed: true,
          loading: false,
          disabled: false
        },
        hasChildTabs: true,
        toolbarEventMap: {
          'export': this.handleDownload
        },
        cmbSource: {
          apprStatus: aeoManage.apprStatusMapList
        },
        ajaxUrl: {
          exportUrl: csAPI.csMaterielCenter.singleLoss.aeo.auditRecordExport,
          selectAllPaged: csAPI.csMaterielCenter.singleLoss.aeo.auditRecordList
        }
      }
    },
    created: function () {
      let me = this
      let rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/listId')
      let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
      me.handleUpdateColumn(showColumns)
    },
    computed: {
      /**
       * 动态标签
       */
      dynamicLabel() {
        return {}
      },
      defaultTimes() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    },
    methods: {
      /**
       * 首次查询前赋值
       */
      beforeFirstSearch() {
        let me = this
        me.$set(me.searchConfig.model, 'insertTimeFrom', me.defaultTimes[0])
        me.$set(me.searchConfig.model, 'insertTimeTo', me.defaultTimes[1])
      },
      getCommParams() {
        return [{
          key: 'copEmsNo',
          title: '企业内部编号'
        }, {
          key: 'emsNo',
          title: '备案号'
        }, {
          range: true,
          key: 'apprDate',
          title: '操作时间'
        },{
          range: true,
          key: 'insertTime',
          title: '制单日期'
        }, {
          type: 'select',
          key: 'apprStatus',
          title: '内审情况'
        }]
      },
      getCommFields() {
        return [{
          width: 180,
          key: 'copEmsNo',
          title: '企业内部编号'
        }, {
          width: 150,
          key: 'emsNo',
          title: '备案号'
        }, {
          width: 100,
          key: 'billFlag',
          title: '类型',
          render: (h, params) => {
            let billFlag = params.row['billFlag'],
              billFlagName = billFlag
            if (['11', '12'].includes(billFlag)) {
              billFlagName = billFlag + ' 手册'
            } else if (['21', '22'].includes(billFlag)) {
              billFlagName = billFlag + ' 账册'
            }
            return this.toolTipRender(h, billFlagName.trim())
          }
        }, {
          width: 150,
          key: 'apprDate',
          title: '操作时间',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }, {
          width: 160,
          title: '操作员',
          key: 'apprUserName'
        }, {
          width: 160,
          title: '内审情况',
          key: 'apprStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.apprStatus)
          }
        }, {
          width: 300,
          key: 'apprNote',
          title: '内审意见'
        }]
      },
      handleTableColumnSetup() {
        let me = this
        me.listSetupShow = true
      },
      /**
       * 导出
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
