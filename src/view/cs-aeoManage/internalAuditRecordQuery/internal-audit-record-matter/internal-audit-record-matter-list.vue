<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <internalAuditRecordMatterSearch ref="headSearch"></internalAuditRecordMatterSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { certificate } from '@/view/cs-common'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import internalAuditRecordMatterSearch from './internal-audit-record-matter-search'
  import { columnsConfig, excelColumnsConfig, columns } from './internalAuditRecordQueryColumns'

  export default {
    name: 'InternalAuditRecordMatterList',
    mixins: [commList, columns, pms],
    props: {
      tabName: {
        type: String,
        required: true
      }
    },
    components: {
      internalAuditRecordMatterSearch
    },
    data() {
      return {
        // 查询条件行数
        searchLines: 2,
        hasChildTabs: true,
        certificate: certificate,
        gridConfig: {
          exportTitle: '企业物料内审记录'
        },
        toolbarEventMap: {
          'export': this.handleDownload
        },
        ajaxUrl: {
          exportUrl: csAPI.aeoManage.internalAuditRecordQuery.exportCommodity,
          selectAllPaged: csAPI.aeoManage.internalAuditRecordQuery.getProductInternalAuditRecord
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
      me.loadFunctions().then()
    },
    methods: {
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
