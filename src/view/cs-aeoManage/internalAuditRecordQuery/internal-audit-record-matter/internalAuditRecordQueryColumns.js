import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { baseColumnsExport } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'facGNo'
  , 'copGNo'
  , 'apprDate'
  , 'apprStatusName'
  , 'apprNote'
  , 'erpInsertUser'
  , 'apprUserFull'
  , 'userName'
]

const columnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

// 企业物料
const columns = {
  mixins: [columnRender],
  data() {
    return {
      totalColumns: [
        {
          title: '企业料号',
          width: 180,
          align: 'center',
          key: 'facGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备案料号',
          key: 'copGNo',
          minWidth: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '操作时间',
          width: 150,
          align: 'center',
          key: 'apprDate'
        },
        {
          title: '操作员',
          key: 'userName',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审情况',
          key: 'apprStatusName',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审意见',
          key: 'apprNote',
          minWidth: 250,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
