<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="操作时间" @onDateRangeChanged="handleApprDateChange"></dc-dateRange>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleErpInsertTimeChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="status" label="内审情况">
        <xdo-select v-model="searchParam.status" :options="this.cmbSource.apprStatusData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="erpInsertUser" label="操作员">
        <XdoIInput type="text" v-model="searchParam.erpInsertUser"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { aeoManage, certificate } from '@/view/cs-common'

  export default {
    name: 'internalAuditRecordIEQuerySearch',
    data() {
      return {
        searchParam: {
          emsListNo: '',
          apprType: '',
          apprDateFrom: '',
          apprDateTo: '',
          erpInsertTimeFrom: '',
          erpInsertTimeTo: '',
          status: '',
          erpInsertUser: '',
          apprUser: '',
          entryNo: '',
          contrNo: ''
        },
        certificate: certificate,
        cmbSource: {
          apprStatusData: aeoManage.apprStatusMapList
        }
      }
    },
    methods: {
      /**
       * 出、出库日期修改
       */
      handleApprDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "apprDateFrom", values[0])
          this.$set(this.searchParam, "apprDateTo", values[1])
        } else {
          this.$set(this.searchParam, "apprDateFrom", '')
          this.$set(this.searchParam, "apprDateTo", '')
        }
      },
      /**
       * 制单日期
       * @param values
       */
      handleErpInsertTimeChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "erpInsertTimeFrom", values[0])
          this.$set(this.searchParam, "erpInsertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "erpInsertTimeFrom", '')
          this.$set(this.searchParam, "erpInsertTimeTo", '')
        }
      }

    },
    computed: {
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }

  }
</script>

<style scoped>
</style>
