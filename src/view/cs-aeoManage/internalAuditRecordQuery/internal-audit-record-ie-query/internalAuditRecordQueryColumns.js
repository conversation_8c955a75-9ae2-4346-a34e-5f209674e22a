import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { baseColumnsExport } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'emsListNo'
  , 'entryNo'
  , 'apprDate'
  , 'apprStatusName'
  , 'apprNote'
  , 'erpInsertUser'
  , 'apprUserFull'
  , 'userName'
  , 'contrNo'
]

const columnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [columnRender],
  data() {
    return {
      totalColumns: [
        {
          title: '单据内部编号',
          width: 180,
          align: 'center',
          key: 'emsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单号',
          key: 'entryNo',
          minWidth: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '合同协议号',
          minWidth: 120,
          align: 'center',
          key: 'contrNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '操作时间',
          width: 150,
          align: 'center',
          key: 'apprDate'
        },
        {
          title: '操作员',
          key: 'userName',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审情况',
          key: 'apprStatusName',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审意见',
          key: 'apprNote',
          minWidth: 250,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
