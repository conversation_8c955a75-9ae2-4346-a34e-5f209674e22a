/***
 * 关务-AEO管理-路由
 */
import { namespace } from '@/project'
import AeoTaxHeadList from './taxManage/AeoTaxHeadList'
import ErrorStatisticsTabs from './error-statistics/error-statistics-tabs'
import AeoDecErpIHeadNHeadList from './importManage/AeoDecErpIHeadNHeadList'
import AeoDecErpEHeadNHeadList from './exportManage/AeoDecErpEHeadNHeadList'
import SingleLossCheckList from './single-loss-check/single-loss-check-list'
import FilingClassifyCheckList from './filingClassifyCheck/filingClassifyCheckList'
import ProdClassify<PERSON>heckHeadList from './prodClassifyCheck/prodClassifyCheckHeadList'
import internalAuditRecordQueryTab from './internalAuditRecordQuery/internal-audit-record-mainlist/internal-audit-record-query-tab'

export default  [
  {
    path: '/' + namespace + '/aeoManage/filingClassifyCheck',
    name: 'filingClassifyCheckList',
    meta: {
      icon: 'ios-document',
      title: '备案归类审核'
    },
    component: FilingClassifyCheckList
  },
  {
    path: '/' + namespace + '/aeoManage/prodClassifyCheck',
    name: 'prodClassifyCheckHeadList',
    meta: {
      icon: 'ios-document',
      title: '企业物料审核'
    },
    component: ProdClassifyCheckHeadList
  },
  {
    path: '/' + namespace + '/aeoManage/AeoDecErpIHeadNHeadList',
    name: 'AeoDecErpIHeadNHeadList',
    meta: {
      icon: 'ios-document',
      title: '进口业务审核'
    },
    component: AeoDecErpIHeadNHeadList
  },
  {
    path: '/' + namespace + '/aeoManage/AeoDecErpEHeadNHeadList',
    name: 'AeoDecErpEHeadNHeadList',
    meta: {
      icon: 'ios-document',
      title: '出口业务审核'
    },
    component: AeoDecErpEHeadNHeadList
  },
  {
    path: '/' + namespace + '/aeoManage/AeoTaxHeadList',
    name: 'AeoTaxHeadList',
    meta: {
      icon: 'ios-document',
      title: '免表业务审核'
    },
    component: AeoTaxHeadList
  },
  {
    path: '/' + namespace + '/internalAuditRecordQuery/InternalAuditRecordQueryList',
    name: 'internalAuditRecordQueryTab',
    meta: {
      icon: 'ios-document',
      title: '内审记录查询'
    },
    component: internalAuditRecordQueryTab
  },
  {
    path: '/' + namespace + '/internalAuditRecordQuery/singleLossCheckList',
    name: 'singleLossCheckList',
    meta: {
      icon: 'ios-document',
      title: '单损耗审核'
    },
    component: SingleLossCheckList
  },
  {
    path: '/' + namespace + '/internalAuditRecordQuery/ErrorStatisticsTabs',
    name: 'errorStatisticsTabs',
    meta: {
      icon: 'ios-document',
      title: '内审差错统计'
    },
    component: ErrorStatisticsTabs
  }
]
