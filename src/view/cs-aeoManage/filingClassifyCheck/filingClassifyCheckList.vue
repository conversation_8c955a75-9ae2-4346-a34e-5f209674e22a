<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <SearchForm ref="headSearch" :parent-type="parentType"></SearchForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        <Dropdown trigger="click">
          <Button type="text" style="font-size: 12px; width: 95px;">
            <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>内审通过<Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchApplyPass">
                <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>勾选通过
              </Button>&nbsp;
            </DropdownItem>
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchAllPass">
                <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>全部通过
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>&nbsp;
        <Dropdown trigger="click">
          <Button type="text" style="font-size: 12px; width: 95px;">
            <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-edit"/>内审退回<Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchApplyBack">
                <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>勾选退回
              </Button>&nbsp;
            </DropdownItem>
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchAllBack">
                <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>全部退回
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <div v-if="!showList">
      <BondedEdit v-if="BondedImg" @onEditBack="editBack" :editConfig="editConfig" :isCheck="true"></BondedEdit>
      <BondedEdit v-if="BondedExg" @onEditBack="editBack" :editConfig="editConfig" :isCheck="true"></BondedEdit>
    </div>
    <ApprReject :show.sync="reject.apprShow" :batch-sid="reject.batchData" :title="reject.title"
                @return:success="handleRejectReturn" :searchData="searchData" :apprType="apprTypePre"></ApprReject>
    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
    <TableColumnSetup v-model="columnSetupshow" :resId="columnSetupListId" :columns="columnSetupSettingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>

  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { columnsConfig, columns } from './filingClassifyCheckListColumns'
  import { classificationReviewList } from '../base/classificationReviewList'
  import BondedEdit from '@/view/cs-materielCenter/components/merging-edit/merging-edit-tabs'

  export default {
    name: 'prodClassifyCheckHeadList',
    components: {
      BondedEdit
    },
    mixins: [classificationReviewList, columns],
    data() {
      return {
        columnSetupListId:[],
        columnSetupshow:false,
        columnSetupSettingColumns:[],
        apprTypePre: 'O',
        parentType: 'filingClassify',
        pageSizeOpts: [10, 20, 50, 100, 200, 500, 1000],
        toolbarEventMap: {
          'setting': this.handleTableColumnSetup
        },
        ajaxUrl: {
          auditDataM: csAPI.aeoManage.aeoReview.actions.auditDataM,
          auditDateByList: csAPI.aeoManage.aeoReview.actions.auditDateByList,
          selectAllPaged: csAPI.materialRelationship.aeoManage.getListForMatAuditPaged,
          checkCodeTS: csAPI.aeoManage.aeoReview.actions.mergingAuditHsCodeCheckBySelected,
          checkCodeTSAll: csAPI.aeoManage.aeoReview.actions.mergingAuditHsCodeCheckByParams
        }
      }
    },
    created: function () {
      let me = this
      me.columnSetupListId = me.$route.path+'/'+me.$options.name
      me.$set(me,'columnSetupSettingColumns', getColumnsByConfig(me.totalColumns, columnsConfig))
      let columns = me.$bom3.showTableColumns(me.columnSetupListId,me.columnSetupSettingColumns)
      me.handleUpdateColumn(columns)
    },
    methods: {
      handleTableColumnSetup(){
        this.columnSetupshow = true
      },
      /**
       * 设置列
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(columns => {
          return {
            key: columns.key,
            value: columns.title
          }
        })
      },
      /**
       * 处理审核
       * @param pass
       */
      batchApply(pass) {
        let me = this
        if (me.checkRowSelected('内审')) {
          const theRows = me.gridConfig.selectRows
          let statusArr = ['2']
          if (!pass) {
            statusArr = ['2', '8']
          }
          let otherStatus = theRows.filter((item) => {
            return !statusArr.includes(item.apprStatus)
          })
          if (otherStatus.length > 0) {
            me.$Message.warning('仅待审核状态可内审!')
            return
          }
          if (pass) {
            let sids = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.checkCodeTS, sids).then(res => {
              if (Array.isArray(res.data.data) && res.data.data.length > 0) {
                me.$set(me.hsCodeCheckPop, 'type', '0')
                me.$set(me.hsCodeCheckPop, 'data', res.data.data)
                me.$set(me.hsCodeCheckPop, 'show', true)
              } else {
                me.$Modal.confirm({
                  title: '提醒',
                  okText: '确认',
                  cancelText: '取消',
                  content: '确认内审通过吗',
                  onOk: () => {
                    me.doApplyBatchPass()
                  }
                })
              }
            }).catch(() => {
            })
          } else {
            me.reject.batchData = theRows.map(item => {
              return {
                sid: item.sid,
                apprType: me.apprTypePre + me.getAuditType(item.bondedFlag, item.gmark)
              }
            })
            me.reject.apprShow = true
          }
        }
      },
      /**
       * 处理全部审核
       * @param pass
       */
      batchAllApply(pass) {
        let me = this
        if (pass) {
          me.$http.post(me.ajaxUrl.checkCodeTSAll, me.getSearchParams()).then(res => {
            if (Array.isArray(res.data.data) && res.data.data.length > 0) {
              me.$set(me.hsCodeCheckPop, 'type', '1')
              me.$set(me.hsCodeCheckPop, 'data', res.data.data)
              me.$set(me.hsCodeCheckPop, 'show', true)
            } else {
              me.$Modal.confirm({
                title: '提醒',
                okText: '确认',
                cancelText: '取消',
                content: '确认内审通过吗',
                onOk: () => {
                  me.doApplyAllPass()
                }
              })
            }
          }).catch(() => {
          })
        } else {
          me.reject.batchData = []
          me.reject.apprShow = true
          me.searchData = me.$refs.headSearch.searchParam
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
