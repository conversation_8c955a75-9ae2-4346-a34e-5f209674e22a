import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
   'sid'
  , 'apprStatus'
  , 'insertTime'
  , 'copGNo'
  , 'facGNo'
  , 'emsNo'
  , 'gmark'
  , 'serialNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'qty'
  , 'unit'
  , 'unit1'
  , 'unit2'
  , 'declareElements'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'dutyMode'
  , 'country'
  , 'classMark'
  , 'factor1'
  , 'factor2'
  , 'factorWt'
  , 'classRemark'
  , 'modifyMark'
  , 'etpsExeMark'
  //, 'copGName'
  , 'copGModel'
  , 'factory'
  , 'costCenter'
  , 'tradeCode'
  , 'netWt'
  , 'unitWt'
  , 'status'
  , 'note'
  , 'updateUser'
  , 'updateTime'
  , 'maker'
  , 'sendUserFull'
  , 'declareDate'
  , 'apprUserFull'
  , 'apprDate'
  , 'attach'
  , 'recordDate'
  , 'copEmsNo'
  , 'apprStatus'
  , 'usedQty'
  , 'bondedFlag'
  , 'gMarkName'
  , 'apprUserFull'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          key: 'selection',
          type: 'selection'
        },
        {
          key: 'operation'
        },
        {
          width: 100,
          title: '归类状态',
          key: 'apprStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.APPR_STATUS_MAP)
          }
        },
        {
          width: 120,
          key: 'declareDate',
          title: '发送内审日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 180,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'gMarkName',
          title: '物料类型'
        },
        {
          width: 140,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'serialNo',
          title: '备案序号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 150,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 200,
          tooltip: true,
          key: 'gmodel',
          title: '申报规格型号'
        },
        {
          key: 'qty',
          width: 120,
          tooltip: true,
          title: '备案数量'
        },
        {
          width: 100,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 160,
          key: 'decTotal',
          title: '备案总价'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'country',
          title: '产销国',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 80,
          key: 'attach',
          title: '随附单据',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.ATTACH_STATUS)
          }
        },
        {
          width: 120,
          key: 'recordDate',
          title: '备案有效期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 150,
          key: 'maker',
          title: '制单员'
        },
        {
          width: 150,
          tooltip: true,
          title: '最终修改人',
          key: 'sendUserFull'
        },
        {
          width: 150,
          tooltip: true,
          title: '内审员',
          key: 'apprUserFull'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
