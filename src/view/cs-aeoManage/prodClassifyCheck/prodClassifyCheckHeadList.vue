<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <SearchForm ref="headSearch" :parent-type="parentType"></SearchForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        <Dropdown trigger="click">
          <Button type="text" style="font-size: 12px; width: 95px;">
            <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>内审通过<Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchApplyPass">
                <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>勾选通过
              </Button>&nbsp;
            </DropdownItem>
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchAllPass">
                <Icon type="ios-checkmark-circle-outline" size="22" class="xdo-btn-edit"/>全部通过
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>&nbsp;
        <Dropdown trigger="click">
          <Button type="text" style="font-size: 12px; width: 95px;">
            <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-edit"/>内审退回<Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchApplyBack">
                <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>勾选退回
              </Button>&nbsp;
            </DropdownItem>
            <DropdownItem style="padding: 0; margin: 0;">
              <Button type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="batchAllBack">
                <Icon type="ios-close-circle-outline" size="22" class="xdo-btn-delete"/>全部退回
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <div v-if="!showList">
      <BondedEdit v-if="BondedImg" @onEditBack="editBack" :editConfig="editConfig" :isCheck="true" :parent-source="cmbSource"></BondedEdit>
      <BondedEdit v-if="BondedExg" @onEditBack="editBack" :editConfig="editConfig" :isCheck="true" :parent-source="cmbSource"></BondedEdit>
      <nonBondedTabs v-if="NonBonded" @onEditBack="editBack" :editConfig="editConfig" :isCheck="true"></nonBondedTabs>
    </div>
    <ApprReject :show.sync="reject.apprShow" :batch-sid="reject.batchData" :title="reject.title"
                @return:success="handleRejectReturn" :searchData="searchData" :apprType="apprTypePre"></ApprReject>
    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
    <TableColumnSetup v-model="prodeSetupShow" :resId="prodeId" :columns="prodeListColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { columnsConfig, columns } from './prodClassifyCheckHeadListColumns'
  import { classificationReviewList } from '../base/classificationReviewList'
  import nonBondedTabs from '@/view/cs-productClassify/nonBonded/nonBondedTabs'
  import BondedEdit from '@/view/cs-productClassify/components/bonded-edit/bonded-edit-tabs'
  import {tableColumnSetup} from "@/view/cs-aeoManage/base/tableColumnSetup";

  export default {
    name: 'prodClassifyCheckHeadList',
    components: {
      BondedEdit,
      nonBondedTabs
    },
    mixins: [classificationReviewList, columns,tableColumnSetup],
    data() {
      return {
        prodeId:'',
        prodeSetupShow:false,
        prodeListColumns:[],
        searchLines: 5,
        apprTypePre: 'Y',
        parentType: 'prodClassify',
        pageSizeOpts: [10, 20, 50, 100, 200, 500, 1000],

        toolbarEventMap: {
          'setting': this.handleTableColumnSetup
        },
        ajaxUrl: {
          matImgexg: csAPI.aeoManage.aeoReview.actions.matImgexg,
          auditDataM: csAPI.aeoManage.aeoReview.actions.auditDataM,
          selectAllPaged: csAPI.csProductClassify.bonded.getListForMatAuditPaged,
          checkCodeTS: csAPI.aeoManage.aeoReview.actions.mixingHsCodeCheckBySelected,
          checkCodeTSAll: csAPI.aeoManage.aeoReview.actions.mixingHsCodeCheckByParams
        }
      }
    },
    created: function () {
      let me = this
      me.prodeId = me.$route.path+'/'+me.$options.name
      me.$set(me,'prodeListColumns', getColumnsByConfig(me.totalColumns, columnsConfig))
      let columns = me.$bom3.showTableColumns(me.prodeId,me.prodeListColumns)
      me.handleUpdateColumn(columns)
    },
    methods: {
      handleTableColumnSetup() {
        this.prodeSetupShow = true
      },
      /**
       * 设置列
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(columns => {
          return {
            key: columns.key,
            value: columns.title
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
