import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
   'sid'
  , 'apprStatus'
  , 'insertTime'
  , 'copGNo'
  , 'facGNo'
  , 'emsNo'
  , 'gmark'
  , 'serialNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'qty'
  , 'unit'
  , 'unit1'
  , 'unit2'
  , 'declareElements'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'dutyMode'
  , 'country'
  , 'classMark'
  , 'factor1'
  , 'factor2'
  , 'factorWt'
  , 'classRemark'
  , 'modifyMark'
  , 'etpsExeMark'
  , 'copGName'
  , 'copGModel'
  , 'factory'
  , 'costCenter'
  , 'tradeCode'
  , 'netWt'
  , 'unitWt'
  , 'status'
  , 'note'
  , 'updateUser'
  , 'updateTime'
  , 'maker'
  , 'sendUserFull'
  , 'declareDate'
  , 'apprUserFull'
  , 'apprDate'
  , 'attach'
  , 'recordDate'
  , 'copEmsNo'
  , 'apprStatus'
  , 'usedQty'
  , 'bondedFlag'
  , 'gMarkName'
  , 'apprUserFull'
  , 'copGModelEn'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          key: 'selection',
          type: 'selection'
        },
        {
          key: 'operation'
        },
        {
          width: 100,
          title: '归类状态',
          key: 'apprStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.APPR_STATUS_MAP)
          }
        },
        {
          width: 120,
          key: 'declareDate',
          title: '发送内审日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 160,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 100,
          key: 'bondedFlag',
          title: '保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.BONDED_FLAG)
          }
        },
        {
          width: 100,
          tooltip: true,
          key: 'gMarkName',
          title: '物料种类'
        },
        {
          width: 120,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 140,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'serialNo',
          title: '备案序号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 160,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 200,
          key: 'gmodel',
          tooltip: true,
          title: '申报规格型号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'copGName',
          title: '中文名称'
        },
        {
          width: 150,
          tooltip: true,
          key: 'copGModel',
          title: '料号申报要素'
        },
        {
          width: 160,
          key: 'qty',
          title: '备案数量'
        },
        {
          width: 150,
          key: 'unit',
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 140,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 160,
          tooltip: true,
          title: '产销国',
          key: 'country',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 80,
          key: 'attach',
          title: '随附单据',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.ATTACH_STATUS)
          }
        },
        {
          width: 120,
          key: 'recordDate',
          title: '备案有效期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 150,
          key: 'maker',
          title: '制单员'
        },
        {
          width: 150,
          tooltip: true,
          title: '最终修改人',
          key: 'sendUserFull'
        },
        {
          width: 150,
          tooltip: true,
          title: '内审员',
          key: 'apprUserFull'
        },
        {
          width: 150,
          tooltip: true,
          key: 'copGModelEn',
          title: '英文规格型号'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
