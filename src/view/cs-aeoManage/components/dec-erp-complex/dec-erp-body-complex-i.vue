<template>
  <section>
    <OriginalBody v-if="!isNew" ref="body" aeoShow :can-aeo-audit="canAeoAudit"
                  :head-id="aeoConfig.headId" :operation-status="aeoConfig.editStatus" :head-data="aeoConfig.editData"
                  :body-audits="savedAuditData" @onBodyAuditChanged="onBodyAuditChanged"></OriginalBody>
    <NewBody v-if="isNew" ref="body" aeoShow :can-aeo-audit="canAeoAudit" iemark="I"
             :parent-config="aeoConfig" :bond-mark="aeoConfig.editData.bondMark"
             :body-audits="savedAuditData" @onBodyAuditChanged="onBodyAuditChanged"></NewBody>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import NewBody from '../../../cs-ie-manage/dec-erp-body/dec-erp-body-list'
  import OriginalBody from '../../../cs-importManage/body/DecErpIListNBodyList'

  export default {
    name: 'decErpBodyComplexI',
    components: {
      NewBody,
      OriginalBody
    },
    props: {
      headData: {
        type: Object,
        default: () => ({
          sid: ''
        })
      },
      canAeoAudit: {
        type: Boolean,
        default: () => false
      },
      savedAuditData: {
        type: Object,
        default: () => ({})
      }
    },
    computed: {
      aeoConfig() {
        return {
          editData: this.headData,
          headId: this.headData.sid,
          editStatus: editStatus.SHOW
        }
      },
      isNew() {
        if (this.headData) {
          if (this.headData.decType === '1') {
            return true
          }
        }
        return false
      }
    },
    methods: {
      /**
       * 表体审批痕迹变更
       * @param auditData
       */
      onBodyAuditChanged(auditData) {
        let me = this
        me.$emit('onBodyAuditChanged', auditData)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
