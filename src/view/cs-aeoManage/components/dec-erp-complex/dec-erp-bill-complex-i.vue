<template>
  <section>
    <OriginalBill v-if="!isNew" ref="bill" :head-id="aeoConfig.headId" iemark="I" aeoShow :head-data="aeoConfig.editData" :operation-status="aeoConfig.editStatus"></OriginalBill>
    <NewBill v-if="isNew" ref="bill" :parent-config="aeoConfig" iemark="I" aeoShow :bond-mark="aeoConfig.editData.bondMark"></NewBill>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import OriginalBill from '../../../cs-ie-manage-mixins/bill/bill-head'
  import NewBill from '../../../cs-ie-manage/components/bill-detail/bill-details'

  export default {
    name: 'decErpBillComplexI',
    components: {
      NewBill,
      OriginalBill
    },
    props: {
      headData: {
        type: Object,
        default: () => ({
          sid: ''
        })
      }
    },
    computed: {
      aeoConfig() {
        return {
          editData: this.headData,
          headId: this.headData.sid,
          editStatus: editStatus.SHOW
        }
      },
      isNew() {
        if (this.headData) {
          if (this.headData.decType === '1') {
            return true
          }
        }
        return false
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
