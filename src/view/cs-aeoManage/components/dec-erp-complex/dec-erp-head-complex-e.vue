<template>
  <section>
    <OriginalHead ref="head" v-if="compositeHead"
                  aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                  @onHeadAuditChanged="onHeadAuditChanged"></OriginalHead>
    <NewBondedHead ref="head" v-if="isBonded"
                   aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                   @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></NewBondedHead>
    <NewNonBondedHead ref="head" v-if="isNonBonded"
                      aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                      @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></NewNonBondedHead>
    <DynamicHeadEdit ref="head" v-if="dynamicHeadTabShow" ie-mark="E" :bond-mark="bondMark"
                     aeoAudit :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                     @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></DynamicHeadEdit>
  </section>
</template>

<script>
  import { decHeadFilter } from '../../base/decHeadFilter'
  import OriginalHead from '../../../cs-exportManage/head/DecErpEHeadNHeadEdit'
  import NewBondedHead from '../../../cs-ie-manage/dec-erp-head/export/bonded-ehead-edit'
  import NewNonBondedHead from '../../../cs-ie-manage/dec-erp-head/export/non-bonded-ehead-edit'

  export default {
    name: 'decErpHeadComplexE',
    components: {
      OriginalHead,
      NewBondedHead,
      NewNonBondedHead
    },
    mixins: [decHeadFilter]
  }
</script>

<style lang="less" scoped>
</style>
