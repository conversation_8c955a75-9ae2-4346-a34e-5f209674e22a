<template>
  <section>
    <OriginalHead ref="head" v-if="compositeHead"
                  aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                  @onHeadAuditChanged="onHeadAuditChanged"></OriginalHead>
    <NewBondedHead ref="head" v-if="isBonded"
                   aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                   @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></NewBondedHead>
    <NewNonBondedHead ref="head" v-if="isNonBonded"
                      aeoShow :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                      @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></NewNonBondedHead>
    <DynamicHeadEdit ref="head" v-if="dynamicHeadTabShow" ie-mark="I" :bond-mark="bondMark"
                     aeoAudit :can-aeo-audit="canAeoAudit" :edit-config="aeoConfig" :saved-audit-data="savedAuditData"
                     @onHeadAuditChanged="onHeadAuditChanged" @onEditBack="onEditBack"></DynamicHeadEdit>
  </section>
</template>

<script>
  import { decHeadFilter } from '../../base/decHeadFilter'
  import OriginalHead from '../../../cs-importManage/head/DecErpIHeadNHeadEdit'
  import NewBondedHead from '../../../cs-ie-manage/dec-erp-head/import/bonded-ihead-edit'
  import NewNonBondedHead from '../../../cs-ie-manage/dec-erp-head/import/non-bonded-ihead-edit'

  export default {
    name: 'decErpHeadComplexI',
    components: {
      OriginalHead,
      NewBondedHead,
      NewNonBondedHead
    },
    mixins: [decHeadFilter]
  }
</script>

<style lang="less" scoped>
</style>
