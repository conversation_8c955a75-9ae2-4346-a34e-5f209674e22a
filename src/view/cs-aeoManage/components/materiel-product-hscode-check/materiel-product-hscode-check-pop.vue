<template>
  <XdoModal width="1024" mask v-model="show" title="HS编码不一致提示信息"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="400"></DcAgGrid>
    </XdoCard>
    <div class="xdo-enter-action action" style="margin-top: 2px; text-align: right;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { baseListConfig } from '@/mixin/generic/baseListConfig'

  export default {
    name: 'materielProductHsCodeCheckPop',
    mixins: [baseListConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      data: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false,
        type: 'primary'
      }
      let fields = this.getFields()
      return {
        baseFields: [
          ...fields
        ],
        pageParam: {
          limit: 10000
        },
        initSearch: false,
        buttons: [{
          ...btnComm, click: this.handleContinue, icon: 'dc-btn-save', label: '继续操作'
        }, {
          ...btnComm, click: this.handleClose, icon: 'dc-btn-cancel', label: '返回'
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$set(me.listConfig, 'data', me.data)
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      getFields() {
        return [{
          width: 160,
          key: 'facGNo',
          title: '企业料号'
        }, {
          width: 832,
          tooltip: true,
          key: 'errMsg',
          title: '错误原因'
        }]
      },
      /**
       * 继续
       */
      handleContinue() {
        let me = this
        me.$emit('doContinue')
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-body {
    padding: 0 4px 4px 4px;
  }
</style>
