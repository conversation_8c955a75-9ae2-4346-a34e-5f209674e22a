<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="apprStatus" label="归类状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.aeoManage.apprStatusMap" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="showBondedFlag" prop="bondedFlag" label="保完税标志">
        <xdo-select v-model="searchParam.bondedFlag" :options="this.productClassify.BONDED_FLAG_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gmark" label="物料类型">
        <xdo-select v-model="searchParam.gmark" :options="this.select.gmarkData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.select.emsNoSelect"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="serialNo" label="备案序号">
        <xdo-input v-model="searchParam.serialNo" number int-length="11"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="gmodel" label="申报规格型号">
        <XdoIInput type="text" v-model="searchParam.gmodel" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="attach" label="随附单据">
        <xdo-select v-model="searchParam.attach" :options="this.productClassify.ATTACH_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="发送内审日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
      <XdoFormItem v-if="parentType === 'prodClassify'" prop="copGModelEn" label="英文规格型号">
        <XdoIInput type="text" v-model="searchParam.copGModelEn" :maxlength="255" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="parentType === 'prodClassify'" prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="userName" label="制单员">
        <XdoIInput type="text" v-model="searchParam.userName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { productClassify, aeoManage } from '@/view/cs-common'
  import { detailGeneralMethod } from '@/view/cs-materielCenter/base/detailGeneralMethod'

  export default {
    name: 'materialSearch',
    mixins: [detailGeneralMethod],
    props: {
      parentType: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchParam: {
          apprStatus: '2',
          emsNo: '',
          serialNo: null,
          copGNo: '',
          codeTS: '',
          gname: '',
          gmodel: '',
          attach: '',
          declareDateFrom: '',
          declareDateTo: '',
          bondedFlag: '',
          gmark: '',
          copGModelEn: '',
          facGNo: '',
          userName: ''
        },
        select: {
          gmarkData: [],
          emsNoSelect: []
        },
        aeoManage: aeoManage,
        showBondedFlag: false,
        productClassify: productClassify
      }
    },
    created: function () {
      let me = this
      me.getEmsNoList((req) => {
        if (Array.isArray(req.data)) {
          me.select.emsNoSelect = req.data.filter(item => {
            return !isNullOrEmpty(item.emsNo)
          }).map(item => {
            return {
              value: item.emsNo,
              label: item.emsNo,
              copEmsNo: item.copEmsNo
            }
          })
        }
      })
    },
    mounted() {
      let me = this
      if (me.parentType === 'matCenter') {
        me.showBondedFlag = true
        me.select.gmarkData = me.productClassify.GMARK_SELECT
      } else if (me.parentType === 'prodClassify') {
        me.showBondedFlag = true
        me.select.gmarkData = me.productClassify.GMARK_SELECT
      } else if (me.parentType === 'filingClassify') {
        me.showBondedFlag = false
        me.searchParam.bondedFlag = '0'
        me.select.gmarkData = me.productClassify.GMARK_SINGLE_SELECT
      }
    },
    methods: {
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
