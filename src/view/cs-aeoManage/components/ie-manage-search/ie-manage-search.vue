<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="apprStatus" label="状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.cmbDataSource.apprStatusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="待审核日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareName" label="申报单位名称">
        <XdoIInput type="text" v-model="searchParam.declareName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="forwardCode" label="货代">
        <xdo-select v-model="searchParam.forwardCode" :options="this.cmbDataSource.forwardCodeList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="masterCustoms" label="申报地海关">
        <xdo-select v-model="searchParam.masterCustoms" :asyncOptions="pcodeList"
                    :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="apprUser" label="内审员">
        <XdoIInput type="text" v-model="searchParam.apprUser"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeCountry" :label="tradeCountryLabel">
        <xdo-select v-model="searchParam.tradeCountry" :meta="pcode.country_outdated"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="userName" label="制单员">
        <XdoIInput type="text" v-model="searchParam.userName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iEMark === 'I'" prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { aeoManage } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'DecErpEHeadNHeadSearch',
    props: {
      showSearch: {
        type: Boolean,
        default: () => ({})
      },
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        searchParam: {
          apprStatus: '2',
          emsNo: '',
          emsListNo: '',
          invoiceNo: '',
          declareDateFrom: '',
          declareDateTo: '',
          trafMode: '',
          declareName: '',
          forwardCode: '',
          tradeMode: '',
          masterCustoms: '',
          apprUser: '',
          contrNo: '',
          tradeCountry: '',
          userName: '',
          hawb: '',
          batchNo: '',
        },
        cmbDataSource: {
          emsNoList: [],
          forwardCodeList: [],
          apprStatusList: aeoManage.apprStatusMap
        }
      }
    },
    watch: {
      showSearch: {
        immediate: true,
        handler: function (val) {
          if (val) {
            let me = this
            // 货代
            me.$http.post(csAPI.ieParams.FOD).then(res => {
              me.cmbDataSource.forwardCodeList = ArrayToLocaleLowerCase(res.data.data)
            }).catch(() => {
              me.cmbDataSource.forwardCodeList = []
            })
          }
        }
      }
    },
    created: function () {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        me.cmbDataSource.emsNoList = res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        })
      }).catch(() => {
        me.cmbDataSource.emsNoList = []
      })
    },
    methods: {
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      }
    },
    computed: {
      tradeCountryLabel() {
        if (this.iEMark === 'I') {
          return '启运国(地区)'
        } else {
          return '运抵国(地区)'
        }
      }
    }
  }
</script>

<style scoped>
</style>
