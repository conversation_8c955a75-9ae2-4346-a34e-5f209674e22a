<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <AeoTaxHeadSearch ref="headSearch" :showSearch="showSearch" :i-e-mark="ieMark"></AeoTaxHeadSearch>
        </div>
      </XdoCard>
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <ApprReject :show.sync="reject.apprShow" :batch-sid="reject.batchData" :title="reject.title"
                @return:success="handleRejectReturn"></ApprReject>
    <AeoTaxHeadTab v-if="!showList" ref="AeoTaxHeadTabs" @onEditBack="editBack"
                   :editConfig="editConfig" :cmbSource="cmbSource"></AeoTaxHeadTab>
    <InnerMessagePop :show.sync="innerMsg.show" :message="innerMsg.message"></InnerMessagePop>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import AeoTaxHeadTab from './AeoTaxHeadTab'
  import { getColumnsByConfig } from '@/common'
  import { comMethods } from '../base/comMethods'
  import { taxPreference } from '@/view/cs-common'
  import AeoTaxHeadSearch from './AeoTaxHeadSearch'
  import { aeoIEManageList } from '../base/aeoIEManageList'
  import { columnsConfig, columns } from './taxHeadListColumns'

  export default {
    name: 'AeoTaxHeadList',
    mixins: [aeoIEManageList, columns,pms,comMethods],
    components: {
      AeoTaxHeadTab,
      AeoTaxHeadSearch
    },
    data() {
      return {
        ieMark: 'T',
        searchLines: 1,
        taxPreference: taxPreference,
        cmbSource: {
          apprFrom: [],
          apprDept: [],
          apprItem: [],
          billType: [],
          itemNoData: [],
          declareData: [],
          tradeCodeData: [],
          mtype: taxPreference.MTYPE_MAP,
          applType: taxPreference.APPL_MAP,
          iemark: taxPreference.IE_MARK_MAP,
          statusList: taxPreference.STATUS_MAP,
          hasSpecFile: taxPreference.HASSPEC_MAP,
          isDeclare: taxPreference.IS_DECLARE_MAP,
          isEnabled: taxPreference.IS_ENABLED_MAP,
          businessType: taxPreference.BUSINESS_MAP,
          sendstatusList: taxPreference.SEND_STATUS_MAP,
          enterpriseType: taxPreference.ENTERPRISE_TYPE_MAP
        },
        ajaxUrl: {
          getForwardCodes: csAPI.ieParams.FOD,
          selectAllPaged: csAPI.tax.list.selectListForAudit,
          auditDataM: csAPI.aeoManage.aeoReview.actions.auditDataM,
          auditDataAll: csAPI.aeoManage.aeoReview.actions.auditDataAll
        }
      }
    },
    watch: {
      isShowModel: {
        immediate: true,
        handler: function(val) {
          if (!val) {
            let me = this
            me.selectRows = []
            me.getList()
          }
        }
      }
    },
    mounted() {
      let me = this
      me.tableId = me.$route.path
      let columns = me.$bom3.showTableColumns(me.tableId, me.totalColumns)
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
      me.loadFunctions('default', 120)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
