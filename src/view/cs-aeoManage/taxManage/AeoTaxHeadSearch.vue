<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="apprStatus" label="状态">
        <xdo-select v-model="searchParam.apprStatus" :options="cmbDataSource.apprStatusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="业务编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { aeoManage } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'AeoTaxHeadSearch',
    props: {
      showSearch: {
        type: Boolean,
        default: () => ({})
      },
      iEMark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E', 'T'].includes(value)
        }
      }
    },
    data() {
      return {
        searchParam: {
          apprStatus: '2',
          emsNo: '',
          emsListNo: '',
          contrNo: '',
          batchNo: '',
        },
        cmbDataSource: {
          emsNoList: [],
          forwardCodeList: [],
          apprStatusList: aeoManage.apprStatusMap
        }
      }
    },
    watch: {
      showSearch: {
        immediate: true,
        handler: function(val) {
          if (val) {
            let me = this
            // 货代
            me.$http.post(csAPI.ieParams.FOD).then(res => {
              me.cmbDataSource.forwardCodeList = ArrayToLocaleLowerCase(res.data.data)
            }).catch(() => {
              me.cmbDataSource.forwardCodeList = []
            })
          }
        }
      }
    },
    created: function() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        me.cmbDataSource.emsNoList = res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        })
      }).catch(() => {
        me.cmbDataSource.emsNoList = []
      })
    },
    computed: {
      tradeCountryLabel() {
        if (this.iEMark === 'I') {
          return '启运国(地区)'
        } else {
          return '运抵国(地区)'
        }
      }
    },
    methods: {
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
