import { getKeyValue } from '@/libs/util'
import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'batchNo'
  , 'declareCode'
  , 'emsListNo'
  , 'status'
  , 'exemptsNo'
  , 'tmpNo'
  , 'seqNo'
  , 'apprFromName'
  , 'cutMode'
  , 'transMode'
  , 'contrNo'
  , 'entryPort'
  , 'masterCustoms'
  , 'declareCustoms'
  , 'isDeclare'
  , 'hasSpecFile'
  , 'applType'
  , 'mtype'
  , 'receiptSatus'
  , 'erpEmsListNo'
  , 'entryNo'
  , 'iemark'
  , 'insertUser'
  , 'insertTime'
  // , 'projectId'
  // , 'billTypeName'
  // , 'apprItemName'
  // , 'apprDeptName'
  // , 'licenseNo'
  // , 'itemMode'
  // , 'validDate'
  // , 'linkMan'
  // , 'linkManTel'
  // , 'contactCellphone'
  // , 'taxAssureReason'
  // , 'supplierCode'
  // , 'isEnabled'
  // , 'note'
  // , 'itemNo'
  // , 'declareCode'
  // , 'tradeCode'
  // , 'tradeName'
  // , 'assureNo'
  // , 'projectFund'
  // , 'entrustedCoScc'
  // , 'businessType'
  // , 'comCoScc'
  // , 'applyCoScc'
  // , 'apprStatus'
  // , 'apprStatusName'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          title: '批次号',
          key: 'batchNo',
        },
        {
          width: 238,
          title: '受托单位',
          key: 'declareCode',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.declareData, params.row.declareCode))
          }
        },
        {
          width: 136,
          title: '业务编号',
          key: 'emsListNo'
        },
        {
          width: 120,
          title: '内审状态',
          key: 'apprStatus',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.statusList, params.row.apprStatus))
          }
        },
        {
          width: 120,
          key: 'status',
          title: '发送状态',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.sendstatusList, params.row.status))
          }
        },
        {
          width: 120,
          key: 'exemptsNo',
          title: '免表海关编号'
        },
        {
          width: 120,
          key: 'tmpNo',
          title: '暂存单编号'
        },
        {
          width: 120,
          key: 'seqNo',
          title: '中心统一编号'
        },
        {
          width: 120,
          title: '审批依据',
          key: 'apprFromName'
        },
        {
          width: 100,
          key: 'cutMode',
          title: '征免性质',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.levytype, params.row.cutMode), params.row.cutMode))
          }
        },
        {
          width: 88,
          title: '成交方式',
          key: 'transMode',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transac, params.row.transMode), params.row.transMode))
          }
        },
        {
          width: 120,
          title: '合同号',
          key: 'contrNo'
        },
        {
          width: 120,
          key: 'entryPort',
          title: '进(出)口岸',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.entryPort), params.row.entryPort))
          }
        },
        {
          width: 120,
          title: '主管海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
          }
        },
        {
          width: 120,
          title: '申报地海关',
          key: 'declareCustoms',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.declareCustoms), params.row.declareCustoms))
          }
        },
        {
          width: 120,
          key: 'isDeclare',
          title: '是否已申报进口',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.isDeclare, params.row.isDeclare))
          }
        },
        {
          width: 156,
          key: 'hasSpecFile',
          title: '状况报告书递交标志',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.hasSpecFile, params.row.hasSpecFile))
          }
        },
        {
          width: 88,
          key: 'applType',
          title: '申请形式',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.applType, params.row.applType))
          }
        },
        {
          width: 88,
          key: 'mtype',
          title: '免表类型',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.mtype, params.row.mtype))
          }
        },
        {
          width: 150,
          title: '回执状态',
          key: 'receiptSatus'
        },
        {
          width: 156,
          title: '单据内部编号',
          key: 'erpEmsListNo'
        },
        {
          width: 120,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 108,
          key: 'iemark',
          title: '进(出)口标志',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.iemark, params.row.iemark))
          }
        },
        {
          width: 156,
          title: '录入人',
          key: 'insertUser'
        },
        {
          width: 88,
          title: '录入日期',
          key: 'insertTime'
        },

        // {
        //   title: '业务种类',
        //   key: 'billTypeName',
        //
        //   width: 120,
        // },
        // {
        //   title: '项目统一编号',
        //   key: 'itemNo',
        //
        //   width: 120,
        // },
        // {
        //   title: '产业政策审批条目',
        //   key: 'apprItemName',
        //
        //   width: 120,
        // },
        // {
        //   title: '审批部门',
        //   key: 'apprDeptName',
        //
        //   width: 120,
        // },
        // {
        //   title: '许可证号',
        //   key: 'licenseNo',
        //
        //   width: 120
        // },
        // {
        //   title: '企业代码',
        //   key: 'tradeCode',
        //
        //   width: 120
        // },
        // {
        //   title: '企业名称',
        //   key: 'tradeName',
        //
        //   width: 120
        // },
        // {
        //   title: '项目性质',
        //   key: 'itemMode',
        //
        //   width: 120
        // },
        // {
        //   title: '担保编号',
        //   key: 'assureNo',
        //
        //   width: 120
        // },
        // {
        //   title: '有效日期',
        //   key: 'validDate',
        //
        //   width: 120
        // },
        // {
        //   title: '联系人',
        //   key: 'linkMan',
        //
        //   width: 120
        // },
        // {
        //   title: '电话',
        //   key: 'linkManTel',
        //
        //   width: 120
        // },
        // {
        //   title: '手机',
        //   key: 'contactCellphone',
        //
        //   width: 120
        // },
        // {
        //   title: '税款担保原因',
        //   key: 'taxAssureReason',
        //
        //   width: 120
        // },
        // {
        //   title: '供应商',
        //   key: 'supplierCode',
        //
        //   width: 120,
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.cmbSource.tradeCodeData, params.row.supplierCode))
        //   }
        // },
        // {
        //   title: '报关行',
        //   key: 'declareCode',
        //
        //   width: 120,
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.cmbSource.declareData, params.row.declareCode))
        //   }
        // },
        // {
        //   title: '是否有效',
        //   key: 'isEnabled',
        //
        //   width: 120,
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.cmbSource.isEnabled, params.row.isEnabled))
        //   }
        // },
        // {
        //   title: '表头备注',
        //   key: 'note',
        //
        //   width: 120
        // },
        // {
        //   title: '申请人统一社会信用代码',
        //   key: 'applyCoScc',
        //
        //   width: 250
        // },
        // {
        //   title: '收发货人统一社会信用代码',
        //   key: 'comCoScc',
        //
        //   width: 250
        // },
        // {
        //   title: '业务类型',
        //   key: 'businessType',
        //
        //   width: 120,
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.cmbSource.businessType, params.row.businessType))
        //   }
        // },
        // {
        //   title: '受托单位统一社会信用代码',
        //   key: 'entrustedCoScc',
        //
        //   width: 250
        // },
        // {
        //   title: '项目资金性质',
        //   key: 'projectFund',
        //
        //   width: 150
        // },
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
