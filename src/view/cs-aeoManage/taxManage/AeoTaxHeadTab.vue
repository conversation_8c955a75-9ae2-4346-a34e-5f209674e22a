<template>
  <section>
    <ApprReject :show.sync="reject.apprShow" ref="reviewModal" :business-sid="reject.businessSid" :appr-result="reject.apprResult"
                :appr-type="reject.apprType" :title="reject.title" :return-reason="auditMessage"
                @return:success="handleRejectReturn"></ApprReject>
    <Layout>
      <Content>
        <Tabs v-model="tabName" :animated="false" type="card" class="dc-tab">
          <TabPane name="headTab" label="表头">
            <Head ref="head" v-if="tabs.headTab" :parent-config="parentConfig" :cmbSource="cmbSource"
                  aeo-show :can-aeo-audit="canAeoAudit" :saved-audit-data="headAuditData"
                  @onEditBack="editBack" @onHeadAuditChanged="onHeadAuditChanged"></Head>
          </TabPane>
          <TabPane name="bodyTab" v-if="showBody" label="表体">
            <Body ref="body" v-if="tabs.bodyTab" :parent-config="parentConfig"
                  aeo-show :can-aeo-audit="canAeoAudit" :body-audits="bodyAuditData"
                  @onBodyAuditChanged="onBodyAuditChanged" @onEditBack="editBack"></Body>
          </TabPane>
          <TabPane name="attachTab" v-if="showBody" label="随附单据">
            <Attach ref="attach" v-if="tabs.attachTab" :parent-config="parentConfig"
                    @onEditBack="editBack"></Attach>
          </TabPane>
          <TabPane name="aeoTab" v-if="showBody" label="内审情况">
            <AeoInfoList ref="aeoInfo" v-if="tabs.aeoTab" :sid="headId" :show-title="aeoShowTitle"></AeoInfoList>
          </TabPane>
          <template v-for="item in buttons">
            <Button size="small" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" slot="extra"
                    @click="item.click" :icon="item.icon" :key="item.icon">{{item.label}}</Button>
          </template>
        </Tabs>
      </Content>

    </Layout>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { aeoIEManageTabs } from '../base/aeoIEManageTabs'
  import Head from '../../cs-tax-exemption-equipment/tax-preference-information/head/tax-preference-information-head'
  import Attach from '../../cs-tax-exemption-equipment/tax-preference-information/attached-document/attached-document2'
  import Body from '../../cs-tax-exemption-equipment/tax-preference-information/body/tax-preference-information-body-list'

  export default {
    name: 'AeoTaxHeadTab',
    mixins: [aeoIEManageTabs],
    props: {
      cmbSource: {
        type: Object,
        default: () => ({})
      },
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      Head,
      Body,
      Attach
    },
    data() {
      return {
        reject: {
          apprType: 'T',
          title: '减免税业务审核'
        },
        aeoKey: 'aeoTaxHeadTab'
      }
    },
    methods: {
      handlePass() {
        let me = this
        if (me.hasErrFields) {
          me.$Message.success('有错误数据，无法进行此操作!')
        } else {
          me.buttons[0].loading = true
          me.$http.post(csAPI.aeoManage.aeoReview.actions.auditData, {
            apprNote: '*#06#3721',
            apprType: me.reject.apprType,
            businessSid: me.reject.businessSid
          }).then(() => {
            me.$Message.success('内审通过成功!')
            me.handleRejectReturn(true, true)
          }).catch(() => {
          }).finally(() => {
            me.buttons[0].loading = false
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-tabs-nav-right {
    display: grid;
    grid-column-gap: 6px;
    grid-template-columns: repeat(3, 1fr);
  }

  /deep/ .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    color: black !important;
    font-weight: bold !important;
    border: 1px solid red !important;
  }

  .right-sider {
    height: 38px;
    padding: 7px 0 0 6px;
    background: rgb(245, 247, 247);
    border-bottom: 1px solid rgb(214, 219, 222);

    .ivu-layout-sider-children {
      overflow-y: hidden;
      margin-right: -18px;
    }
  }

  .right-sider i {
    color: #389de9;
    cursor: pointer;
    font-size: 26px;
    //transform: rotate(0deg);
    //-webkit-transform: rotate(0deg);
    transition: transform .2s linear;
    -webkit-transition: -webkit-transform .2s linear;
  }
</style>
