<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="realShowList">
      <CopEmsNoSelect :show.sync="copEmsNoSelectShow" :moduleType="importKey"
                      @onConfirm="onConfirm"></CopEmsNoSelect>
      <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                  @onImportSuccess="onAfterImport"></ImportPage>
      <ImportPage :importKey="updateImportKey" :importShow.sync="updateImportShow" :importConfig="updateImportConfig"
                  @onImportSuccess="onAfterImport"></ImportPage>
      <EmsNoSelect :show.sync="emsNoSelectShow"
                   @doExtract="doExtract"></EmsNoSelect>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <BondedSearch ref="headSearch" :gmark="editConfig.gmark" :parent-source="cmbSource"></BondedSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:allupdate>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                    <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:send-audit>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-send" size="22" class="xdo-icon"/>发送内审<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-send" @click="handleSendAppr" :loading="downloading">
                    <XdoIcon type="ios-send" size="22" class="xdo-icon"/>  勾选发送
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-send" @click="handleSendAllAppr">
                    <XdoIcon type="ios-send" size="22" class="xdo-icon"/>  全部发送
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <BondedEdit v-if="!showList" :editConfig="editConfig" :parent-source="cmbSource"
                @onEditBack="editBack"></BondedEdit>
    <StartStopReasonsPop :show.sync="reasonPopShow" :status="doStatus"
                         @confirm:success="doSetStatus"></StartStopReasonsPop>
    <StartStopReasonsList v-if="showLog" :headId="currHeadId"
                          @hideLogs="hideLogs"></StartStopReasonsList>
    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="myAllColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ProductCopyPop :show.sync="productCopyShow" ie-mark="E" bond-mark="0"
                    @doCopy="doCopy"></ProductCopyPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { bondedList } from '@/view/cs-productClassify/base/bondedList'

  export default {
    name: 'bondedExgHeadList',
    mixins: [bondedList],
    data() {
      let importConfig = this.getCommImportConfig('BONDED-EXG'),
        updateImportConfig = this.getCommImportConfig('BONDED-EXG-UPDATE', {
          moduleType: 'BondedExgUpdate'
        })
      return {
        editConfig: {
          gmark: 'E'
        },
        importKey: 'BondedExg',
        importConfig: importConfig,
        updateImportKey: 'BondedExgUpdate',
        updateImportConfig: updateImportConfig,
        pageSizeOpts: [10, 20, 50, 100, 200, 500, 1000],
        ajaxUrl: {
          copyUrl: csAPI.csProductClassify.bonded.copy,
          delete: csAPI.csProductClassify.bonded.delete,
          sendData: {
            apprType: 'YE',
            url: csAPI.aeoManage.aeoReview.actions.sendDataM
          },
          setStatus: csAPI.csProductClassify.bonded.setStatus,
          exportUrl: csAPI.csProductClassify.bonded.exportUrl,
          v1matImgexg: csAPI.aeoManage.aeoReview.actions.v1matImgexg,
          selectAllPaged: csAPI.csProductClassify.bonded.selectAllPaged,
          takePassedImgExgInfo: csAPI.csMaterielCenter.bonded.takePassedImgExgInfo,
          checkCodeTS: csAPI.aeoManage.aeoReview.actions.bondedHsCodeCheckBySelected,
          checkCodeTSAll: csAPI.aeoManage.aeoReview.actions.bondedHsCodeCheckByParams
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
