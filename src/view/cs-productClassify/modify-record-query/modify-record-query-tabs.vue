<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="recordedTab" label="备案物料" index="'1'">
        <recordedMaterialsList v-if="tabs.recordedTab"></recordedMaterialsList>
      </TabPane>
      <TabPane name="enterpriseTab" label="企业物料" index="'2'">
        <enterpriseMaterialsList v-if="tabs.enterpriseTab"></enterpriseMaterialsList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { modifyRecordQueryTabs } from './js/modifyRecordQueryTabs'

  export default {
    name: 'modifyRecordQueryTabs',
    mixins: [modifyRecordQueryTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
