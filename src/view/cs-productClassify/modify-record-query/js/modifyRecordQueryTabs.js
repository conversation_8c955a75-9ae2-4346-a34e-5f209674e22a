import recordedMaterialsList from '../recorded-materials-list'
import enterpriseMaterialsList from '../enterprise-materials-list'

export const modifyRecordQueryTabs = {
  components: {
    recordedMaterialsList,
    enterpriseMaterialsList
  },
  data() {
    return {
      tabName: 'recordedTab',
      tabs: {
        recordedTab: true,
        enterpriseTab: false
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (tabName) {
        let me = this
        me.tabs[tabName] = true
      }
    }
  }
}
