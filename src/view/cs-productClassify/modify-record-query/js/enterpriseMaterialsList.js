import { commMaterialsList } from './commMaterialsList'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

export const enterpriseMaterialsList = {
  name: 'enterpriseMaterialsList',
  mixins: [commMaterialsList],
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{type: string, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGNo',
        title: '备案料号'
      }, {
        title: '变更栏位',
        key: 'changeName'
      }, {
        title: '变更原因',
        key: 'changeReason'
      }, {
        key: 'gmark',
        type: 'select',
        title: '物料类型'
      }, {
        range: true,
        title: '变更日期',
        key: 'insertTime'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['queryType'] = 'M'
      return paramObj
    },
    /**
     * 显示列
     * @returns {({cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 100,
        key: 'bondMark',
        title: '保完税标识',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 100,
        key: 'gmark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmark)
        })
      }, {
        width: 150,
        key: 'emsNo',
        title: '备案号',
      }, {
        width: 100,
        key: 'serialNo',
        title: '备案序号'
      }, {
        width: 120,
        key: 'copGNo',
        title: '备案料号'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 156,
        title: '变更栏位',
        key: 'changeName'
      }, {
        width: 200,
        title: '变更后内容',
        key: 'changeAfter',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 200,
        title: '变更前内容',
        key: 'changeBefore',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 200,
        title: '变更原因',
        key: 'changeReason',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 88,
        title: '变更日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
        })
      }, {
        width: 186,
        title: '变更人',
        key: 'userName'
      }]
    }
  }
}
