import { getKeyValue } from '@/libs/util'

const columnsConfig = ['selection'
  , 'sid'
  , 'facGNo'
  , 'codeTS'
  , 'gmark'
  , 'documentCode'
  , 'documentName'
  , 'iemark'
]

const excelColumnsConfig = [
  'sid'
  , 'facGNo'
  , 'codeTS'
  , 'gmark'
  , 'documentCode'
  , 'documentName'
  , 'iemarkName'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: 'HS编码'
        },
        {
          width: 100,
          key: 'gmark',
          title: '物料类型',
          render: (h, params) => {
            return h('span', getKeyValue(this.productClassify.G_MARK_STATUS_MAP, params.row.gmark))
          }
        },
        {
          width: 120,
          key: 'documentCode',
          title: '证书自定义代码'
        },
        {
          width: 120,
          title: '证件名称',
          key: 'documentName'
        },
        {
          width: 120,
          key: 'iemark',
          title: '进出口标志',
          render: (h, params) => {
            return h('span', getKeyValue(this.certificate.I_E_MARK, params.row.iemark))
          }
        }
      ]
    }
  }
}

const excelColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          key: 'facGNo',
          title: '企业料号'
        },
        {
          key: 'codeTS',
          title: 'HS编码'
        },
        {
          key: 'gmark',
          title: '物料类型'
        },
        {
          key: 'documentCode',
          title: '证书自定义代码'
        },
        {
          key: 'documentName',
          title: '证件名称'
        },
        {
          key: 'iemarkName',
          title: '进出口标志'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
