<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="documentCode" label="涉证代码">
        <XdoIInput type="text" v-model="searchParam.documentCode"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'bondedSearch',
    data() {
      return {
        searchParam: {
          documentCode: ''
        },
        productClassify: productClassify
      }
    }
  }
</script>

<style scoped>
</style>
