<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="apprStatus" label="状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.productClassify.APPR_STATUS_SELECT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.parentSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="serialNo" label="备案序号">
        <xdo-input v-model="searchParam.serialNo" number int-length="11"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gmodel" label="申报规格型号">
        <XdoIInput type="text" v-model="searchParam.gmodel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGName" label="中文名称" v-if="gmark === 'I'">
        <XdoIInput type="text" v-model="searchParam.copGName" :maxlength="50"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="curr" label="币制">
        <xdo-select v-model="searchParam.curr" :asyncOptions="pcodeList" :meta="pcode.curr_outdated"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="unit" label="申报计量单位">
        <xdo-select v-model="searchParam.unit" :asyncOptions="pcodeList" :meta="pcode.unit"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="country" label="产销国">
        <xdo-select v-model="searchParam.country" :asyncOptions="pcodeList" :meta="pcode.country_outdated"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="attach" label="随附单据">
        <xdo-select v-model="searchParam.attach" :options="this.productClassify.ATTACH_SELECT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="使用状态">
        <xdo-select v-model="searchParam.status" :options="this.productClassify.ENABLE_SELECT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange"></dc-dateRange>
      <XdoFormItem prop="credentials" label="监管条件">
        <XdoIInput type="text" v-model="searchParam.credentials" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="是否涉检">
        <xdo-select v-model="searchParam.credentialStatus" :options="this.productClassify.CREDENTIAL_FLAG"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="最后修改日期" @onDateRangeChanged="handleUpdateDateChange"></dc-dateRange>
      <XdoFormItem prop="userName" label="制单员">
        <XdoIInput type="text" v-model="searchParam.userName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>

      <XdoFormItem prop="netWt" label="对应净重">
        <xdo-select v-model="searchParam.netWt" :options="this.productClassify.NET_WT_SELECT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="supplierCode" label="客户/供应商代码">
        <XdoIInput type="text" v-model="searchParam.supplierCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="factoryCode" label="工厂代码">
        <XdoIInput type="text" v-model="searchParam.factoryCode"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { namespace } from '@/project'
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'bondedSearch',
    props: {
      gmark: {
        type: String,
        require: true
      },
      parentSource: {
        type: Object,
        default: () => ({
          emsNoData: []
        })
      }
    },
    data() {
      return {
        searchParam: {
          apprStatus: '',
          emsNo: this.$store.getters[`${namespace}/emsNoMat`],
          serialNo: null,
          facGNo: '',
          copGNo: '',
          codeTS: '',
          gmodel: '',
          copGName: '',
          curr: '',
          unit: '',
          country: '',
          attach: '',
          recordDateFrom: '',
          recordDateTo: '',
          gmark: '',
          status: '0',
          credentials: '',
          userName: '',
          credentialStatus:'',
          gname:'',
          netWt:'',
          supplierCode: '',
          factoryCode: ''
        },
        productClassify: productClassify
      }
    },
    mounted() {
      let me = this
      me.$set(me.searchParam, 'gmark', me.gmark)
    },
    methods: {
      handleRecordDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "recordDateFrom", values[0])
          this.$set(this.searchParam, "recordDateTo", values[1])
        } else {
          this.$set(this.searchParam, "recordDateFrom", '')
          this.$set(this.searchParam, "recordDateTo", '')
        }
      },
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      handleUpdateDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "updateTimeFrom", values[0])
          this.$set(this.searchParam, "updateTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "updateTimeFrom", '')
          this.$set(this.searchParam, "updateTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
