<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <dc-dateRange label="变更日期" @onDateRangeChanged="handleChangedDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'bondedSearch',
    props: {
      searchField: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      return {
        searchParam: {
          insertTimeFrom: '',
          insertTimeTo: ''
        },
        productClassify: productClassify
      }
    },
    methods: {
      handleChangedDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
