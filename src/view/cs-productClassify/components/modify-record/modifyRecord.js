import { getKeyValue } from '@/libs/util'

const columnsConfig = ['selection'
  , 'operation'
  , 'sid'
  , 'changeName'
  , 'changeAfter'
  , 'changeBefore'
  , 'changeReason'
  , 'userName'
  , 'insertTime'
]

const excelColumnsConfig = [
  'sid'
  , 'changeName'
  , 'changeAfter'
  , 'changeBefore'
  , 'insertTime'
  , 'userName'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          width: 60,
          align: 'center',
          render: (h,params) => {
            return h('a', {
              props: {
                type: 'primary',
                size: 'small'
              },
              style: {
                display: this.getOperationStatus()
              },
              on: {
                click: () => {
                  this.handleBackfill(params.row)
                }
              }
            }, '回填')
          },
          key: 'operation'
        },
        {
          title: '备案号',
          minWidth: 150,
          align: 'center',
          key: 'emsNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备案序号',
          minWidth: 100,
          align: 'center',
          key: 'serialNo'
        },
        {
          title: '备案料号',
          minWidth: 120,
          align: 'center',
          key: 'copGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '物料类型',
          key: 'gmark',
          render: (h, params) => {
            return h('span', getKeyValue(this.productClassify.G_MARK_STATUS_MAP, params.row.gmark))
          },
          align: 'center',
          width: 100
        },
        {
          title: '变更栏位',
          minWidth: 100,
          align: 'center',
          key: 'changeName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '变更后内容',
          minWidth: 100,
          align: 'center',
          key: 'changeAfter',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '变更前内容',
          minWidth: 100,
          align: 'center',
          key: 'changeBefore',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '变更原因',
          minWidth: 200,
          align: 'center',
          key: 'changeReason',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '变更日期',
          minWidth: 120,
          align: 'center',
          render: (h, params) => {
            return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
          },
          key: 'insertTime',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '变更人',
          minWidth: 120,
          align: 'center',
          key: 'userName'
        }
      ]
    }
  }
}

const excelColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          title: '备案号',
          key: 'emsNo'
        },
        {
          title: '备案序号',
          key: 'serialNo'
        },
        {
          title: '备案料号',
          key: 'copGNo'
        },
        {
          key: 'facGNo',
          title: '企业料号'
        },
        {
          key: 'gmark',
          title: '物料类型'
        },
        {
          key: 'changeName',
          title: '变更栏位'
        },
        {
          key: 'changeAfter',
          title: '变更后内容'
        },
        {
          key: 'changeBefore',
          title: '变更前内容'
        },
        {
          key: 'changeReason',
          title: '变更原因'
        },
        {
          key: 'insertTime',
          title: '变更日期'
        },
        {
          title: '变更人',
          key: 'userName'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
