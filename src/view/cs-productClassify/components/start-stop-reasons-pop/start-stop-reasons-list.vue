<template>
  <section>
    <XdoCard :bordered="false">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleBack">返回</XdoButton>
          </XdoBreadCrumb>
        </div>
      </XdoCard>
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'

  export default {
    name: 'startStopReasonsList',
    mixins: [baseListConfig],
    props: {
      headId: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        autoCreate: false,
        baseFields: [{
          title: '操作时间',
          width: 150,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        }, {
          title: '操作人',
          width: 150,
          align: 'center',
          key: 'userName'
        }, {
          title: '操作',
          width: 120,
          align: 'center',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.status)
          }
        }, {
          title: '原因',
          width: 260,
          align: 'center',
          key: 'note'
        }],
        ajaxUrl: {
          selectAllPaged: csAPI.csProductClassify.statusLog.selectAllPaged
        },
        cmbSource: {
          status: [{value: '0', label: '启用'}, {value: '1', label: '停用'}]
        }
      }
    },
    created: function () {
      let me = this
      let rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/listId')
      let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
      me.handleUpdateColumn(showColumns)
    },
    methods: {
      /**
       * 获取查询条件(可外部覆盖)
       */
      getSearchParams() {
        return {
          headId: this.headId
        }
      },
      handleBack() {
        let me = this
        me.$emit('hideLogs', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
