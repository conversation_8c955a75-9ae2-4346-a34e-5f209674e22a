import { csAPI } from '@/api'

export const detailGeneralMethod = {
  methods: {
    getEmsNoList(callback) {
      this.$http.post(csAPI.csProductClassify.bonded.getZtythEmsListNo).then(res => {
        callback(res.data)
      }).catch(() => {
        callback({data: []})
      })
    },
    getCopEmsNoList(callback) {
      this.$http.post(csAPI.csProductClassify.bonded.getZtythCopEmsNo).then(res => {
        callback(res.data)
      })
    }
  }
}
