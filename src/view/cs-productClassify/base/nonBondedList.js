import pms from '@/libs/pms'
import ImportPage from 'xdo-import'
import { isNullOrEmpty } from '@/libs/util'
import { productClassify } from '@/view/cs-common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import ProductCopyPop from '../components/product-copy/product-copy-pop'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import nonBondedTabs from '@/view/cs-productClassify/nonBonded/nonBondedTabs'
import nonBondedHeadSearch from '@/view/cs-productClassify/nonBonded/nonBondedHeadSearch'
import StartStopReasonsPop from '../components/start-stop-reasons-pop/start-stop-reasons-pop'
import StartStopReasonsList from '../components/start-stop-reasons-pop/start-stop-reasons-list'
import MaterielProductHSCodeCheckPop from '../../cs-aeoManage/components/materiel-product-hscode-check/materiel-product-hscode-check-pop'

export const nonBondedList = {
  components: {
    ImportPage,
    nonBondedTabs,
    ProductCopyPop,
    nonBondedHeadSearch,
    StartStopReasonsPop,
    StartStopReasonsList,
    MaterielProductHSCodeCheckPop
  },
  mixins: [pms, commList, dynamicImport],
  data() {
    return {
      actions: [],
      // 查询条件行数
      searchLines: 4,
      downloading: false,
      // 是否显示导入页面
      modifyImport: false,
      modelImportShow: false,
      updateImportShow: false,
      productClassify: productClassify,
      reasonPopShow: false,
      doStatus: '',
      showLog: false,
      currHeadId: '',
      hsCodeCheckPop: {
        type: '',       // 0: 勾选; 1: 全部
        data: [],
        show: false
      }
    }
  },
  mounted: function () {
    this.loadFunctions().then()
  },
  computed: {
    realShowList() {
      if (this.showLog === true) {
        return false
      }
      return this.showList
    }
  },
  methods: {
    /**
     * 自定义编辑、删除检查(可外部覆盖)
     * @param selRows 选中的行数组
     * @param opTitle (操作标签)
     * @returns {boolean}
     */
    customCheck(selRows, opTitle) {
      if (selRows[0].apprStatus !== '2') {
        return true
      } else {
        this.$Message.warning('内审流程中的数据不可' + opTitle + '!')
        return false
      }
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnIndexOrKey
     */
    doDelete(delUrl, btnIndexOrKey) {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.gridConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              me.setButtonLoading(btnIndexOrKey, true)
              let params = me.getSelectedParams()
              me.$http.post(delUrl, params).then(() => {
                me.$Message.success('删除成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading(btnIndexOrKey, false)
              })
            }
          })
        }
      }
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete + '/delete', me.actions.findIndex(it => it.command === 'delete'))
    },
    downLoadOrdinary() {
      this.handleDownload('0')
    },
    downLoadModify() {
      this.handleDownload('1')
    },
    handleImport() {
      if (this.modifyImport) {
        this.updateImportShow = true
      } else {
        this.modelImportShow = true
      }
    },
    //导入成功后事件
    onAfterImport() {
      this.modelImportShow = false
      this.updateImportShow = false
      this.getList()
    },
    uploadOrdinary() {
      this.modifyImport = false
      this.handleImport()
    },
    uploadModify() {
      this.modifyImport = true
      this.handleImport()
    },
    handleSendAppr() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        let cantSendRows = me.gridConfig.selectRows.filter((item) => {
          return !['0', '-1'].includes(item.apprStatus)
        })
        if (Array.isArray(cantSendRows) && cantSendRows.length > 0) {
          me.$Message.warning('只有“暂存”、“内审退回” 状态数据可发送内审!')
        } else {
          let sids = me.getSelectedParams()
          me.$http.post(me.ajaxUrl.checkCodeTS, sids).then(res => {
            if (Array.isArray(res.data.data) && res.data.data.length > 0) {
              me.$set(me.hsCodeCheckPop, 'type', '0')
              me.$set(me.hsCodeCheckPop, 'data', res.data.data)
              me.$set(me.hsCodeCheckPop, 'show', true)
            } else {
              me.$Modal.confirm({
                title: '提醒',
                loading: true,
                okText: '确定',
                cancelText: '取消',
                content: '确认发送内审吗',
                onOk: () => {
                  me.doApplyBatchPass()
                }
              })
            }
          }).catch(() => {
          })
        }
      } else {
        me.$Message.warning('请选择要内审的数据!')
      }
    },
    doApplyBatchPass() {
      let me = this
      me.setToolbarLoading('send-audit', true)
      const data = me.gridConfig.selectRows.map(item => {
        return {
          apprNote: '',
          apprType: 'YM',
          businessSid: item.sid
        }
      })
      me.$http.post(me.ajaxUrl.sendDataM, data).then(() => {
        me.gridConfig.selectRows = []
        me.getList()
        me.$Message.success('发送成功!')
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('send-audit', false)
        me.$Modal.remove()
      })
    },
    /**
     * 全部发送内审
     */
    handleSendAllAppr() {
      let me = this
      me.$http.post(me.ajaxUrl.checkCodeTSAll, me.getSearchParams()).then(res => {
        if (Array.isArray(res.data.data) && res.data.data.length > 0) {
          me.$set(me.hsCodeCheckPop, 'type', '1')
          me.$set(me.hsCodeCheckPop, 'data', res.data.data)
          me.$set(me.hsCodeCheckPop, 'show', true)
        } else {
          me.$Modal.confirm({
            title: '提醒',
            loading: true,
            okText: '确定',
            cancelText: '取消',
            content: '确认发送内审吗',
            onOk: () => {
              me.doApplyAllPass()
            }
          })
        }
      }).catch(() => {
      })
    },
    doApplyAllPass() {
      let me = this,
        params = me.getSearchParams()
      me.setToolbarLoading('send-audit', true)
      me.$http.post(me.ajaxUrl.matNonBonded, params).then(() => {
        me.$Message.success('发送成功')
        me.getList()
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('send-audit', false)
        me.$Modal.remove()
      })
    },
    /**
     * 停用
     */
    handleDisable() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        let inoperableRows = me.gridConfig.selectRows.filter(item => {
          return item.status === '1'
        })
        if (Array.isArray(inoperableRows) && inoperableRows.length > 0) {
          me.$Message.warning('使用状态为"停用"的数据不能再次停用!')
          return
        }
        me.$Modal.confirm({
          title: '提醒',
          okText: '停用',
          cancelText: '取消',
          content: '确认停用所选项吗',
          onOk: () => {
            me.$set(me, 'doStatus', '1')
            me.$set(me, 'reasonPopShow', true)
          }
        })
      } else {
        me.$Message.warning('请选择要停用的数据！')
      }
    },
    /**
     * 启用
     */
    handleEnable() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        let inoperableRows = me.gridConfig.selectRows.filter(item => {
          return item.status === '0'
        })
        if (Array.isArray(inoperableRows) && inoperableRows.length > 0) {
          me.$Message.warning('使用状态为"启用"的数据不能再次启用!')
          return
        }
        me.$Modal.confirm({
          title: '提醒',
          okText: '启用',
          cancelText: '取消',
          content: '确认启用所选项吗',
          onOk: () => {
            me.$set(me, 'doStatus', '0')
            me.$set(me, 'reasonPopShow', true)
          }
        })
      } else {
        me.$Message.warning('请选择要启用的数据!')
      }
    },
    doSetStatus(note, status) {
      let me = this,
        actionKey = me.doStatus === '1' ? 'disable' : 'enable',
        actMessage = me.doStatus === '1' ? '停用成功' : '启用成功'
      me.setToolbarLoading(actionKey, true)
      const data = me.gridConfig.selectRows.map(item => {
        return item.sid
      })
      me.$http.post(me.ajaxUrl.setStatus, {
        note: note,
        sidList: data,
        status: status
      }).then(() => {
        me.gridConfig.selectRows = []
        me.$Message.success(actMessage + '!')
        me.getList()
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading(actionKey)
        me.$set(me, 'reasonPopShow', false)
      })
    },
    hideLogs(flag, headId) {
      let me = this
      if (isNullOrEmpty(headId)) {
        headId = ''
      }
      me.$set(me, 'currHeadId', headId)
      me.$set(me, 'showLog', flag)
    },
    /**
     * 继续审核通过
     */
    doContinue() {
      let me = this
      if (me.hsCodeCheckPop.type === '0') {
        me.doApplyBatchPass()
      }
      if (me.hsCodeCheckPop.type === '1') {
        me.doApplyAllPass()
      }
      me.$set(me.hsCodeCheckPop, 'type', '')
      me.$set(me.hsCodeCheckPop, 'data', [])
      me.$set(me.hsCodeCheckPop, 'show', false)
    }
  }
}
