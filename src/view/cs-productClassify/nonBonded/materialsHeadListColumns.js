
const commColumns = ['selection'
  , 'operation'
  , 'sid'
  , 'serialNo'
  , 'category1'
  , 'category2'
  , 'gName'
  , 'copGNameEn'
  , 'taxCodeColumn'
  , 'performance'
  , 'listName1'
  , 'listName2'
  , 'attachmentNumber'
  , 'policyBasis'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
]
const columns = {
  data() {
    return {
      totalColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          key: 'selection'
        },
        {
          title: '序号',
          minWidth: 70,
          align: 'center',
          key: 'serialNo',
        },
        {
          title: '类别1',
          minWidth: 60,
          align: 'center',
          key: 'category1',
        },
        {
          title: '类别2',
          minWidth: 130,
          tooltip: true,
          align: 'center',
          key: 'category2',
        },
        {
          title: '商品名称',
          minWidth: 260,
          tooltip: true,
          align: 'center',
          key: 'gName',
        },
        {
          title: '英文名称(仅参考)',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'copGNameEn',
        },
        {
          title: '税则号列(仅参考)',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'taxCodeColumn',
        },
        {
          title: '性能指标',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'performance',
        },
        {
          title: '清单名称1',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'listName1',
        },
        {
          title: '清单名称2',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'listName2',
        },
        {
          title: '附件号',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'attachmentNumber',
        },
        {
          title: '政策依据',
          minWidth: 120,
          tooltip: true,
          align: 'center',
          key: 'policyBasis',
        }
      ]
    }
  }
}
export {
  commColumns,
  columns
}
