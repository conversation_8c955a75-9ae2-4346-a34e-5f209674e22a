<template>
  <section v-focus>
    <MerchElement :tyShow.sync="tyShow" :is-edit="isEdit" :validNames="validNames"
                  :modelString="headerData.gmodel" :code-ts="headerData.codeTS" :code-name="headerData.gname"
                  @onChange="handleGModelChange"></MerchElement>

    <XdoCard :bordered="false" class="dc-card">
      <p style="font-weight: bold; padding: 3px 22px; border-bottom: #dcdee2 solid 1px;">
        基础信息
      </p>
      <XdoForm ref="headerEditFrom" class="dc-form xdo-enter-form" :model="headerData" :rules="rulesHeader"
               label-position="right" :label-width="120">
        <XdoFormItem prop="facGNo" label="企业料号" v-if="isCheck">
          <XdoIInput type="text" ref="copGNoInput" v-model="headerData.facGNo" :disabled="copGNoDisable"
                     :maxlength="100"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="copGNo" label="企业料号" v-else>
          <XdoIInput type="text" ref="copGNoInput" v-model="headerData.copGNo" :disabled="copGNoDisable"
                     :maxlength="100"></XdoIInput>
        </XdoFormItem>

        <XdoFormItem prop="gmark" label="物料种类">
          <xdo-select v-model="headerData.gmark" :disabled="showDisable"
                      :options="this.productClassify.COPMARK_SELECT_NON" :optionLabelRender="pcodeRender" />
        </XdoFormItem>
<!--        <dc-dateRange label="备案有效期" :disabled="showDisable" :values="recordDateRange"-->
<!--                      @onDateRangeChanged="handleRecordDateChange"></dc-dateRange>-->
        <XdoFormItem prop="codeTS" label="商品编码">
          <XdoIInput type="text" v-model="headerData.codeTS" :disabled="showDisable" @on-enter="codeTSEnter"
                     @on-blur="codeTSEnter" :maxlength="10"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="gname" label="商品名称">
          <XdoIInput type="text" v-model="headerData.gname" :disabled="showDisable" :maxlength="255"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="copGName" label="海关税则描述">
          <XdoIInput type="text" v-model="headerData.copGName" :disabled="showDisable" :maxlength="255"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="copMark" label="企业物料类型">
          <xdo-select v-model="headerData.copMark" :disabled="showDisable" :options="copMarkList"
                      :optionLabelRender="pcodeRender" />
        </XdoFormItem>
        <FormItem prop="gmodel" label="申报规格型号" class="dc-merge-1-4">
          <Input type="text" v-model="headerData.gmodel" disabled :maxlength="255" style="width: 80%;"></Input>
          <!--点击加号，弹出界面-->
          <span><span>{{ usedCountGModel }}</span>/255 </span>
          <XdoButton @click="handleAddtype">规范申报</XdoButton>
        </FormItem>
        <XdoFormItem prop="taxation" label="征免税">
          <xdo-select v-model="headerData.taxation" :disabled="showDisable"
                      :options="this.productClassify.TAXATION_SELECT" @on-change="changeShowFree"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="freeBasis" label="免税依据">
          <xdo-select v-model="headerData.freeBasis" :options="this.freeBasisList"
                      :disabled="showDisable||showFree"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="freeBillNo" label="免表清单序号">
          <XdoIInput type="text" v-model="headerData.freeBillNo" :disabled="showDisable||showFree" :maxlength="512" @on-enter="getMatFreeRemark" ></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="matFreeRemark" label="免税物料备注">
          <XdoIInput type="text" v-model="headerData.matFreeRemark" :disabled="showDisable||showFree" :maxlength="512"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="unit" label="申报计量单位">
          <xdo-select v-model="headerData.unit" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.unit"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="unit1" label="法定计量单位">
          <xdo-select v-model="headerData.unit1" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="unit2" label="法定第二计量单位">
          <xdo-select v-model="headerData.unit2" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
<!--        <XdoFormItem prop="curr" label="申报币制">-->
<!--          <xdo-select v-model="headerData.curr" :disabled="showDisable" :asyncOptions="pcodeList"-->
<!--                      :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="country" label="产销国(地区)">-->
<!--          <xdo-select v-model="headerData.country" :disabled="showDisable" :asyncOptions="pcodeList"-->
<!--                      :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="decPrice" label="申报单价">-->
<!--          <xdo-input v-model="headerData.decPrice" decimal int-length="11" precision="5"-->
<!--                     :disabled="showDisable"></xdo-input>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="copGModel" label="料号申报要素" class="dc-merge-1-3">-->
<!--          <XdoIInput type="text" v-model="headerData.copGModel" :disabled="showDisable" :maxlength="255"></XdoIInput>-->
<!--        </XdoFormItem>-->
        <XdoFormItem prop="apprStatus" label="状态">
          <xdo-select v-model="headerData.apprStatus" disabled :options="this.productClassify.RECORD_STATUS_COPY_SELECT"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
<!--        <XdoFormItem prop="copGNameEn" label="英文名称">-->
<!--          <XdoIInput type="text" v-model="headerData.copGNameEn" :disabled="showDisable" :maxlength="255"></XdoIInput>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="copGModelEn" label="英文规格型号" class="dc-merge-2-4">-->
<!--          <XdoIInput type="text" v-model="headerData.copGModelEn" :disabled="showDisable" :maxlength="255"></XdoIInput>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="factor1" label="法一比例因子">-->
<!--          <div style="grid-column-gap: 0; padding: 0;">-->
<!--            <xdo-input v-model="headerData.factor1" decimal int-length="11" precision="5" :disabled="showDisable">-->
<!--              <span slot="prepend">1{{unitType}}=</span><span slot="append">{{unitType1}}</span>-->
<!--            </xdo-input>-->
<!--          </div>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="factor2" label="法二比例因子">-->
<!--          <div style="grid-column-gap: 0; padding: 0;">-->
<!--            <xdo-input v-model="headerData.factor2" decimal int-length="11" precision="5" :disabled="factor2Disable">-->
<!--              <span slot="prepend">1{{unitType}}=</span><span slot="append">{{unitType2}}</span>-->
<!--            </xdo-input>-->
<!--          </div>-->
<!--        </XdoFormItem>-->
        <XdoFormItem prop="unitErp" label="ERP计量单位">
          <XdoIInput type="text" v-model="headerData.unitErp" :disabled="showDisable" :maxlength="10"
                     @on-enter="onUnitErpEnter"></XdoIInput>
        </XdoFormItem>
<!--        <XdoFormItem prop="factorErp" label="ERP比例因子">-->
<!--          <div style="grid-column-gap: 0; padding: 0;">-->
<!--            <xdo-input v-model="headerData.factorErp" decimal int-length="8" precision="8" :disabled="factorErpDisable">-->
<!--              <span slot="prepend">1{{headerData.unitErp}}=</span><span slot="append">{{unitType}}</span>-->
<!--            </xdo-input>-->
<!--          </div>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="qtyWt" label="净重数量">-->
<!--          <xdo-input v-model="headerData.qtyWt" decimal int-length="11" precision="8"-->
<!--                     :disabled="showDisable"></xdo-input>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="netWt" label="对应净重">-->
<!--          <xdo-input v-model="headerData.netWt" decimal int-length="13" precision="8" notConvertNumber-->
<!--                     :disabled="showDisable"></xdo-input>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="originalGNo" label="原始物料">-->
<!--          <XdoIInput type="text" v-model="headerData.originalGNo" :disabled="showDisable" :maxlength="50"></XdoIInput>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="costCenter" label="成本中心">-->
<!--          <XdoIInput type="text" v-model="headerData.costCenter" :disabled="showDisable" :maxlength="30"></XdoIInput>-->
<!--        </XdoFormItem>-->
        <XdoFormItem prop="wrapType" label="包装方式">
          <XdoIInput type="text" v-model="headerData.wrapType" :disabled="showDisable" :maxlength="30"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="impOrdinaryRate" label="普通税率">
          <XdoIInput type="text" v-model="headerData.impOrdinaryRate" disabled :maxlength="30"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="impDiscountRate" label="最惠国税率">
          <XdoIInput type="text" v-model="headerData.impDiscountRate" disabled :maxlength="30"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="impTempRate" label="暂定税率">
          <XdoIInput type="text" v-model="headerData.impTempRate" disabled :maxlength="30"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="addTaxRate" label="增值税税率">
          <XdoIInput type="text" v-model="headerData.addTaxRate" disabled :maxlength="30"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="credentials" label="监管条件">
          <XdoIInput type="text" v-model="headerData.credentials" disabled style="width: 100%;"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="inspmonitorcond" label="检验检疫">
          <XdoIInput type="text" v-model="headerData.inspmonitorcond" disabled style="width: 100%;"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="ciqNo" label="CIQ代码">
          <XdoIInput type="text" v-model="headerData.ciqNo" :disabled="showDisable" :maxlength="3"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="note" label="备注">
          <XdoIInput type="text" v-model="headerData.note" :disabled="showDisable" :maxlength="255"></XdoIInput>
        </XdoFormItem>
<!--        <XdoFormItem prop="clientPrice" label="客供单价">-->
<!--          <xdo-input v-model="headerData.clientPrice" decimal int-length="10" precision="5" :disabled="showDisable">-->
<!--          </xdo-input>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem v-show="taxFreeShow" prop="note" label="商品构成" class="dc-merge-1-4">-->
<!--          <XdoIInput type="text" v-model="headerData.structure" :disabled="showDisable" :maxlength="255"></XdoIInput>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem v-show="taxFreeShow" prop="note" label="商品用途" class="dc-merge-1-4">-->
<!--          <XdoIInput type="text" v-model="headerData.useTo" :disabled="showDisable" :maxlength="255"></XdoIInput>-->
<!--        </XdoFormItem>-->

        <FormItem v-show="taxFreeShow" prop="correspondBill" label="对应清单" class="dc-merge-1-4">
          <Input type="text" v-model="headerData.correspondBill" disabled :maxlength="255" style="width: 80%;"></Input>
          <!--点击加号，弹出界面-->
          <XdoButton @click="handleAddmaterials">物料清单</XdoButton>
        </FormItem>
        <XdoFormItem prop="supplierCode" label="客户/供应商代码">
          <XdoIInput type="text" v-model="headerData.supplierCode" :disabled="showDisable" :maxlength="50"></XdoIInput>
        </XdoFormItem>
<!--        <XdoFormItem prop="factoryCode" label="工厂代码">-->
<!--          <XdoIInput type="text" v-model="headerData.factoryCode" :disabled="showDisable" :maxlength="50"></XdoIInput>-->
<!--        </XdoFormItem>-->

        <div class="dc-merge-1-4" v-if="showRat">
          <p style="font-weight: bold; padding: 3px 22px; border-bottom: #dcdee2 solid 1px;">消费税信息</p>
        </div>

        <XdoFormItem prop="impconsumerate" label="消费税率" class="dc-merge-1-4">
          <XdoIInput type="text" v-model="headerData.impconsumerate" disabled style="width: 80%;"></XdoIInput>
        </XdoFormItem>
        <!--        <XdoFormItem label="从价定率" v-if="showRat">
                  <xdo-input v-model="headerData.impConsumeRatePerc" decimal int-length="11" precision="5" disabled>
                    <span slot="prepend">比率税率:</span><span slot="append">%</span>
                  </xdo-input>
                </XdoFormItem>
                <XdoFormItem v-if="showRat"></XdoFormItem>
                <XdoFormItem v-if="showRat"></XdoFormItem>
                <XdoFormItem label="从量定额" v-if="showRat">
                  <xdo-input v-model="headerData.impConsumeRatePrice" decimal int-length="11" precision="5" disabled>
                    <span slot="prepend">单价税额:</span><span slot="append">{{headerData.impConsumeRateUnit}}</span>
                  </xdo-input>
                </XdoFormItem>
                <XdoFormItem label="申报单位比例因子" v-if="showRat">
                  <xdo-input v-model="headerData.factorQty" decimal int-length="11" precision="5" :disabled="showDisable">
                    <span slot="prepend">1{{unitType}}=</span><span slot="append">{{headerData.impConsumeRateUnit}}</span>
                  </xdo-input>
                </XdoFormItem>-->

<!--        <XdoFormItem prop="copMark" label="物料种类">-->
<!--          <xdo-select v-model="headerData.copMark" :options="this.copMarkList"-->
<!--                      :optionLabelRender="pcodeRender" :disabled="showDisable" @on-change="changeGmark"></xdo-select>-->
<!--        </XdoFormItem>-->
<!--        <XdoFormItem prop="outCopMark" label="外库物料标志">-->
<!--          <xdo-select v-model="headerData.outCopMark" :options="this.outCopMarkList"-->
<!--                      :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>-->
<!--        </XdoFormItem>-->
        <XdoFormItem prop="outMessage" label="外库信息">
          <XdoIInput type="text" v-model="headerData.outMessage" :disabled="showDisable" :maxlength="512"></XdoIInput>
        </XdoFormItem>

        <XdoFormItem prop="productElement" label="商品构成">
          <XdoIInput type="text" v-model="headerData.productElement" :disabled="showDisable" :maxlength="512"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="productExplain" label="商品用途">
          <XdoIInput type="text" v-model="headerData.productExplain" :disabled="showDisable" :maxlength="512"></XdoIInput>
        </XdoFormItem>

      </XdoForm>
    </XdoCard>

    <div v-if="headerData.sid" style="padding: 0 2px; background-color: white; margin: 2px;">
      <AcmpInfoListCustom :sid="headerData.sid" :showAction="showAction" :just-view="!showAction"
                          business-type="2"></AcmpInfoListCustom>
    </div>

    <div v-if="isCheck">
      <p>内审意见:</p>
      <i-input type="textarea" :rows="3" v-model.trim="apprNote" ref="ApprNote"></i-input>
    </div>

    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <div v-if="!isCheck" class="buttonsClass">
        <template v-for="item in editActions">
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
          </Button>
        </template>
      </div>
      <div v-if="isCheck" class="buttonsClass">
        <template v-for="item in checkActions">
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
          </Button>
        </template>
      </div>
    </div>

    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
    <materialsHeadList :show.sync="materialsShow" :codeTS="headerData.codeTS"
                       @confirmReturn="confirmReturn" @onClose="onClose"></materialsHeadList>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty } from '@/libs/util'
  import { detailGeneralMethod } from '../base/detailGeneralMethod'
  import { AcmpInfoListCustom, editStatus, MerchElement, productClassify } from '@/view/cs-common'
  import MaterielProductHSCodeCheckPop
    from '../../cs-aeoManage/components/materiel-product-hscode-check/materiel-product-hscode-check-pop'
  import materialsHeadList from './materialsHeadList'

  export default {
    name: 'nonBondedHeadEdit',
    components: {
      MerchElement,
      AcmpInfoListCustom,
      MaterielProductHSCodeCheckPop,
      materialsHeadList
    },
    mixins: [detailGeneralMethod],
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      },
      isCheck: {
        type: Boolean
      }
    },
    data() {
      let btnCom = {
        needed: true,
        loading: false,
        disabled: false
      }
      let defaultData = this.getNonBondedDefaultData()
      return {
        showFree:false,
        headerData: {
          ...defaultData
        },
        rulesHeader: {
          facGNo: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          copGNo: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          gmark: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          codeTS: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          gname: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          gmodel: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          unit: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          unit1: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          decPrice: [{ type: 'number', message: '不是有效的数字!' }],
          factor1: [{ type: 'number', message: '不是有效的数字!' }],
          factor2: [{ type: 'number', message: '不是有效的数字!' }],
          factorErp: [{ type: 'number', message: '不是有效的数字!' }],
          qtyWt: [{ type: 'number', message: '不是有效的数字!' }]//,
          // netWt: [{ type: 'number', message: '不是有效的数字!' }]
        },
        editActions: [
          { type: 'warning', ...btnCom, click: this.handleSave, label: '保存' },
          { type: 'warning', ...btnCom, click: this.handleSaveContinue, label: '保存继续' },
          { type: 'success', ...btnCom, click: this.onSendExamine, label: '发送内审' },
          { type: 'primary', ...btnCom, click: this.handleBack, label: '关闭' }
        ],
        checkActions: [
          { type: 'success', ...btnCom, click: this.handleCheckPass, label: '内审通过' },
          { type: 'error', ...btnCom, click: this.handleCheckReturn, label: '内审退回' },
          { type: 'primary', ...btnCom, click: this.handleBack, label: '关闭' }
        ],
        apprNote: '',
        tyShow: false,
        usedCountGModel: 0,
        productClassify: productClassify,
        ajaxUrl: {
          insert: csAPI.csProductClassify.nonBonded.insert,
          update: csAPI.csProductClassify.nonBonded.update,
          sendData: csAPI.aeoManage.aeoReview.actions.sendData,
          auditData: csAPI.aeoManage.aeoReview.actions.auditData,
          returnData: csAPI.aeoManage.aeoReview.actions.returnData,
          nonBondedHsCodeCheckBySelected: csAPI.aeoManage.aeoReview.actions.nonBondedHsCodeCheckBySelected
        },
        unitType: '',
        unitType1: '',
        unitType2: '',
        hsCodeCheckPop: {
          type: '',       // 0: 发送; 1: 审核
          data: [],
          show: false
        },
        materialsShow: false,
        firstLoad: true
        , copMarkList: []
        , outCopMarkList: []
        , freeBasisList: [
          {value:'428-XC',label:'428-XC'},
          {value:'428-JC',label:'428-JC'},
          {value:'428-LB',label:'428-LB'},
        ]
      }
    },
    watch: {
      'headerData.gmodel': {
        handler: function() {
          this.calcUsedCountGModel()
        }
      },
      'headerData.codeTS': {
        handler: function(val) {
          let result = !isNullOrEmpty(val) ? val.trim() : ''
          if (result.length === 10) {
            let me = this
            me.$http.post(`${csAPI.csMaterielCenter.nonBonded.rate.getRate}/${result}`).then(res => {
              if (res.data.data) {
                let result = res.data.data
                me.$set(me.headerData, 'impConsumeRatePerc', result.impConsumeRatePerc)
                me.$set(me.headerData, 'impConsumeRatePrice', result.impConsumeRatePrice)
                me.$set(me.headerData, 'impConsumeRateUnit', result.impConsumeRateUnit)
              }
            })
            me.pcodeRemote(me.pcode.complex, result).then(res => {
              if (Array.isArray(res) && res.length > 0) {
                me.$set(me.headerData, 'credentials', res[0].CONTROL_MA)
              } else {
                me.$set(me.headerData, 'credentials', '')
              }
            }).catch(() => {
              me.$set(me.headerData, 'credentials', '')
            })
            me.pcodeRemote('COMPLEX_CIQ', result).then(res => {
              if (Array.isArray(res) && res.length > 0) {
                me.$set(me.headerData, 'inspmonitorcond', res[0]['INSPMONITORCOND'])
              } else {
                me.$set(me.headerData, 'inspmonitorcond', '')
              }
            }).catch(() => {
              me.$set(me.headerData, 'inspmonitorcond', '')
            })
            me.codeRateInfo()
          }
        }
      },
      editConfig: {
        deep: true,
        immediate: true,
        handler: function(config) {
          this.editActions[2].disabled = false
          if (config && config.editStatus === editStatus.ADD) {
            this.resetFormData()
            this.editActions[2].disabled = true
          } else if (config && config.editStatus === editStatus.EDIT) {
            this.headerData = { ...config.editData }
            if (config.editData.apprStatus === '2' || config.editData.apprStatus === '8') {
              this.editActions[2].disabled = true
            }
          } else if (config && config.editStatus === editStatus.SHOW) {
            this.headerData = { ...config.editData }
            this.editActions[2].disabled = true
          } else {
            console.error('缺失编辑信息!')
          }
        }
      },
      editDisabled: {
        immediate: true,
        handler: function(val) {
          // 待内审 内审完成  禁用按钮
          this.editActions[0].disabled = val
          this.editActions[1].disabled = val
          // this.editActions[2].disabled = val || this.headerData.apprStatus === '8'
        }
      },
      'headerData.apprStatus': {
        immediate: true,
        handler: function(val) {
          if (val === '2') {
            this.checkActions[0].disabled = false
            this.checkActions[1].disabled = false
          } else {
            this.checkActions[0].disabled = true
            this.checkActions[1].disabled = true
          }
        }
      },
      'headerData.unit': {
        immediate: true,
        handler: function(val) {
          if (!isNullOrEmpty(val)) {
            let me = this
            me.pcodeList(me.pcode.unit).then(res => {
              res.forEach(item => {
                if (item.value === me.headerData.unit) {
                  me.unitType = item.label
                }
              })
            })
          }
        }
      },
      'headerData.unit1': {
        immediate: true,
        handler: function(val) {
          let me = this
          if (!isNullOrEmpty(val)) {
            me.pcodeList(me.pcode.unit).then(res => {
              res.forEach(item => {
                if (item.value === me.headerData.unit1) {
                  me.unitType1 = item.label
                }
              })
            })
          }
          if (!me.firstLoad) {
            me.$set(me.headerData, 'factor1', '')
          }
        }
      },
      'headerData.unit2': {
        immediate: true,
        handler: function(val) {
          let me = this
          if (!isNullOrEmpty(val)) {
            me.pcodeList(me.pcode.unit).then(res => {
              res.forEach(item => {
                if (item.value === me.headerData.unit2) {
                  me.unitType2 = item.label
                }
              })
            })
          }
          if (!me.firstLoad) {
            me.$set(me.headerData, 'factor2', '')
          }
        }
      }
    },
    mounted() {
      let me = this
      if (me.isCheck) {
        me.rulesHeader.copGNo[0].required = false
      } else {
        me.rulesHeader.facGNo[0].required = false
      }
      me.$nextTick(() => {
        me.$set(me, 'firstLoad', false)
      })
    },
    methods: {
      // changeGmark(){
      //   if (this.headerData.copMark === '0'){
      //     this.headerData.gmark = 'I'
      //   }else if (this.headerData.copMark === '1'){
      //     this.headerData.gmark = '4'
      //   }else if (this.headerData.copMark === '2'){
      //     this.headerData.gmark = 'E'
      //   }else if (this.headerData.copMark === '3'){
      //     this.headerData.gmark = '2'
      //   }else if (this.headerData.copMark === '4'){
      //     this.headerData.gmark = '3'
      //   }
      // },
      changeShowFree(){
        if (this.headerData.taxation === '1'){
          this.showFree = true
        }else {
          this.showFree = false
        }
      },
      /**
       * 获取免税物料备注
       */
      getMatFreeRemark(){
        let me = this
        me.$http.post(csAPI.csProductClassify.nonBonded.getMatFreeRemark+'/'+me.headerData.freeBasis+'/'+me.headerData.freeBillNo).then(res => {
          if (!isNullOrEmpty(res.data)){
            me.headerData.matFreeRemark = res.data
          }else {
            me.$Message.warning('未查询到当前免税依据和序号对应的商品编码和商品名称！')
            me.headerData.matFreeRemark = ''
          }
        }).catch(() => {
        }).finally(() => {

        })
      },
      /**
       * 获取默认值
       */
      getNonBondedDefaultData() {
        return {
          sid: '',
          facGNo: '',
          copGNo: '',
          gmark: '',
          recordDateStart: '',
          recordDateEnd: '',
          codeTS: '',
          gname: '',
          copGName: '',
          gmodel: '',
          unit: '',
          unit1: '',
          unit2: '',
          curr: '',
          country: '',
          decPrice: null,
          copGModel: '',
          apprStatus: '',
          copGNameEn: '',
          copGModelEn: '',
          factor1: null,
          factor2: null,
          unitErp: '',
          factorErp: null,
          qtyWt: null,
          netWt: '',
          originalGNo: '',
          costCenter: '',
          wrapType: '',
          impOrdinaryRate: '',
          impDiscountRate: '',
          impTempRate: '',
          addTaxRate: '',
          credentials: '',
          inspmonitorcond: '',
          note: '',
          ciqNo: '',
          impConsumeRatePerc: '',
          impConsumeRatePrice: '',
          impConsumeRateUnit: '',
          factorQty: '',
          clientPrice: null,
          impconsumerate: '',
          structure: '',
          useTo: '',
          correspondBill: '',
          billListName1: '',
          billListName2: '',
          billAttachmentNumber: '',
          billSerialNo: '',
          supplierCode: '',
          factoryCode: ''

          ,copMark: '',
          outCopMark: '',
          outMessage: '',
          freeBillNo: '',
          matFreeRemark: '',
          freeBasis: '',
          productElement: '',
          productExplain: '',
          taxation:'',
        }
      },
      /**
       * 数据重置
       */
      resetFormData() {
        if (this.$refs['headerEditFrom']) {
          this.$refs['headerEditFrom'].resetFields()
        }
        this.headerData = this.getNonBondedDefaultData()
      },
      /**
       * 响应有效期修改值
       */
      handleRecordDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.headerData, 'recordDateStart', values[0])
          this.$set(this.headerData, 'recordDateEnd', values[1])
        } else {
          this.$set(this.headerData, 'recordDateStart', '')
          this.$set(this.headerData, 'recordDateEnd', '')
        }
      },
      /**
       * 商品编码带出计量单位
       */
      codeTSEnter() {
        let me = this,
          val = me.headerData.codeTS
        if (!isNullOrEmpty(val)) {
          val = val.trim()
        } else {
          val = ''
        }
        if (val.length === 10) {
          me.pcodeRemote(me.pcode.complex, val).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              const result = res[0]
              me.$set(me.headerData, 'unit1', result['UNIT_1'])
              me.$set(me.headerData, 'unit2', result['UNIT_2'])
              // if (isNullOrEmpty(me.headerData.gname)) {
                me.$set(me.headerData, 'copGName', result.NAME.substr(0, 50))
              // }
              // if (isNullOrEmpty(result['UNIT_2'])) {
              //   me.$set(me.headerData, 'factor2', '')
              // }
            } else {
              me.resetUnit()
            }
          })
        } else if (val.length > 0) {
          me.resetUnit()
        }
      },
      onUnitErpEnter() {
        let me = this,
          val = me.headerData.unitErp.trim()
        if (isNullOrEmpty(val)) {
          me.$set(me.headerData, 'factorErp', '')
        }
      },
      resetUnit() {
        let me = this
        me.$Message.warning('商品编码不存在')
        me.$set(me.headerData, 'unit1', '')
        me.$set(me.headerData, 'unit2', '')
        me.$set(me.headerData, 'factor2', '')
        me.$set(me.headerData, 'copGName', '')
      },
      /**
       * 计算规格型号已使用的字数
       */
      calcUsedCountGModel() {
        let me = this,
          bytesCount = 0,
          strGModel = me.headerData.gmodel
        if (!isNullOrEmpty(strGModel)) {
          let chars = ''
          for (let i = 0; i < strGModel.length; i++) {
            chars = strGModel.charAt(i)
            /* eslint-disable no-control-regex */
            if (/^[\u0000-\u00ff]$/.test(chars)) {
              bytesCount += 1
            } else {
              bytesCount += 2
            }
          }
        }
        me.$set(me, 'usedCountGModel', bytesCount)
      },
      /**
       * 执行保存操作
       */
      doSave(callback) {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.headerData)
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.editActions[0].loading = true
              me.editActions[1].loading = true
              me.$http.post(me.ajaxUrl.insert, data).then(res => {
                if (typeof callback === 'function') {
                  callback(res.data.data)
                } else {
                  me.editConfig.editStatus = editStatus.EDIT
                }
                me.$Message.success('新增成功!')
              }).catch(() => {
              }).finally(() => {
                me.editActions[0].loading = false
                me.editActions[1].loading = false
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              me.editActions[0].loading = true
              me.editActions[1].loading = true
              me.$http.put(`${me.ajaxUrl.update}/${me.headerData.sid}`, data).then(res => {
                if (typeof callback === 'function') {
                  callback(res.data.data)
                } else {
                  me.editConfig.editStatus = editStatus.EDIT
                }
                me.$Message.success('修改成功!')
              }).catch(() => {
              }).finally(() => {
                me.editActions[0].loading = false
                me.editActions[1].loading = false
              })
            }
          }
        })
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.doSave((data) => {
          me.$emit('onEditBack', {
            data: data,
            hide: false,
            changed: true
          })
          me.codeRateInfo()
        })
      },

      /**
       * 商品编码带出消费税率
       */
      codeRateInfo() {
        let me = this,
          val = me.headerData.codeTS
        if (!isNullOrEmpty(val)) {
          val = val.trim()
        } else {
          val = ''
        }
        me.pcodeRemote('COMPLEX_RATE_INFO', val).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            me.$set(me.headerData, 'impconsumerate', res[0]['IMPCONSUMERATE'])
            me.$set(me.headerData, 'impOrdinaryRate', res[0]['IMPORDINARYRATE'])
            me.$set(me.headerData, 'impDiscountRate', res[0]['IMPDISCOUNTRATE'])
            me.$set(me.headerData, 'impTempRate', res[0]['IMPTEMPRATE'])
            me.$set(me.headerData, 'addTaxRate', res[0]['IMPVATRATE'])
          } else {
            me.$set(me.headerData, 'impconsumerate', '')
            me.$set(me.headerData, 'impOrdinaryRate', '')
            me.$set(me.headerData, 'impDiscountRate', '')
            me.$set(me.headerData, 'impTempRate', '')
            me.$set(me.headerData, 'addTaxRate', '')
          }
        }).catch(() => {
          me.$set(me.headerData, 'impconsumerate', '')
          me.$set(me.headerData, 'impOrdinaryRate', '')
          me.$set(me.headerData, 'impDiscountRate', '')
          me.$set(me.headerData, 'impTempRate', '')
          me.$set(me.headerData, 'addTaxRate', '')
        })
      },
      /**
       * 保存继续
       */
      handleSaveContinue() {
        let me = this
        me.doSave(() => {
          me.$emit('onEditBack', {
            data: {},
            hide: false,
            changed: true
          })
          me.codeRateInfo()
        })
      },
      /**
       * 发送内审
       */
      onSendExamine() {
        let me = this
        me.$http.post(me.ajaxUrl.nonBondedHsCodeCheckBySelected, [me.headerData.sid]).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.hsCodeCheckPop, 'type', '0')
            me.$set(me.hsCodeCheckPop, 'data', res.data.data)
            me.$set(me.hsCodeCheckPop, 'show', true)
          } else {
            me.$Modal.confirm({
              title: '提醒',
              okText: '确定',
              cancelText: '取消',
              content: '确认发送内审吗',
              onOk: () => {
                me.doApplySend()
              }
            })
          }
        }).catch(() => {
        })
      },
      doApplySend() {
        let me = this
        me.editActions[2].loading = true
        me.$http.post(me.ajaxUrl.sendData, {
          apprType: 'YM',
          businessSid: me.headerData.sid,
          apprNote: ''
        }).then(() => {
          me.$Message.success('发送内审成功!')
          //返回查询列表
          me.handleBack()
        }).catch(() => {
        }).finally(() => {
          me.editActions[2].loading = false
        })
      },
      /**
       * 弹出规格型号
       */
      handleAddtype() {
        this.tyShow = true
      },
      handleGModelChange(val) {
        let me = this
        me.$set(me.headerData, 'gmodel', val)
      },
      /**
       * 弹出规格型号
       */
      handleAddmaterials() {
        this.materialsShow = true
      },
      /**
       * 审核通过
       */
      handleCheckPass() {
        let me = this
        me.$http.post(me.ajaxUrl.nonBondedHsCodeCheckBySelected, [me.headerData.sid]).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.hsCodeCheckPop, 'type', '1')
            me.$set(me.hsCodeCheckPop, 'data', res.data.data)
            me.$set(me.hsCodeCheckPop, 'show', true)
          } else {
            me.$Modal.confirm({
              title: '提醒',
              okText: '确定',
              cancelText: '取消',
              content: '确认审核通过吗',
              onOk: () => {
                me.doApplyBatchPass()
              }
            })
          }
        }).catch(() => {
        })
      },
      doApplyBatchPass() {
        let me = this
        me.checkActions[0].loading = true
        me.$http.post(me.ajaxUrl.auditData, {
          apprType: 'YM',
          apprNote: me.apprNote,
          businessSid: me.headerData.sid
        }).then(() => {
          me.$Message.success('审核通过成功!')
          me.handleBack()
        }).catch(() => {
        }).finally(() => {
          me.checkActions[0].loading = false
        })
      },
      /**
       * 继续发送或审核通过
       */
      doContinue() {
        let me = this
        if (me.hsCodeCheckPop.type === '0') {
          me.doApplySend()
        }
        if (me.hsCodeCheckPop.type === '1') {
          me.doApplyBatchPass()
        }
        me.$set(me.hsCodeCheckPop, 'type', '')
        me.$set(me.hsCodeCheckPop, 'data', [])
        me.$set(me.hsCodeCheckPop, 'show', false)
      },
      /**
       * 内审退回
       */
      handleCheckReturn() {
        let me = this
        if (isNullOrEmpty(me.apprNote)) {
          me.$Message.warning('请输入内审意见!')
          me.$refs.ApprNote.focus()
        } else {
          me.checkActions[1].loading = true
          me.$http.post(me.ajaxUrl.returnData, {
            apprType: 'YM',
            apprNote: me.apprNote,
            businessSid: me.headerData.sid
          }).then(() => {
            me.$Message.success('内审退回成功!')
            me.handleBack()
          }).catch(() => {
          }).finally(() => {
            me.checkActions[1].loading = false
          })
        }
      },
      /**
       * 关闭
       */
      handleBack() {
        let me = this
        me.$emit('onEditBack', {
          data: {},
          hide: true,
          changed: false
        })
      },
      confirmReturn(val) {
        let me = this
        me.$set(me, 'materialsShow', false)
        me.$set(me.headerData, 'billListName1', val['listName1'])
        me.$set(me.headerData, 'billListName2', val['listName2'])
        me.$set(me.headerData, 'billAttachmentNumber', val['attachmentNumber'])
        me.$set(me.headerData, 'billSerialNo', val['serialNo'])
        me.$set(me.headerData, 'correspondBill', val['correspondBill'])
      },
      onClose(){
        this.$set(this, 'materialsShow', false)
      }
    },
    computed: {
      configData() {
        return this.$store.state[`${namespace}`].clearanceBusinessSetting
      },



      /**
       * 免表信息显示
       */
      taxFreeShow() {
        let me = this
        return me.configData.dutyFreeApp === '1'
      },
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      },
      recordDateRange() {
        return [this.headerData.recordDateStart, this.headerData.recordDateEnd]
      },
      factor1Val() {
        if (!isNullOrEmpty(this.headerData.unit) && !isNullOrEmpty(this.headerData.unit1)) {
          return this.headerData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unit) + ' : ' + this.headerData.unit1 + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unit1)
        }
        return ''
      },
      factor2Val() {
        if (!isNullOrEmpty(this.headerData.unit) && !isNullOrEmpty(this.headerData.unit2)) {
          return this.headerData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unit) + ' : ' + this.headerData.unit2 + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unit2)
        }
        return ''
      },
      factorErpVal() {
        if (!isNullOrEmpty(this.headerData.unit) && !isNullOrEmpty(this.headerData.unitErp)) {
          return this.headerData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unit) + ' : ' + this.headerData.unitErp + ' ' + this.pcodeGet(this.pcode.unit, this.headerData.unitErp)
        }
        return ''
      },
      showDisable() {
        if (this.editConfig && this.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {
          return false
        } else if (this.editConfig && this.editConfig.editStatus === editStatus.SHOW) {
          return true
        } else {
          return true
        }
      },
      factor2Disable() {
        let me = this
        if (isNullOrEmpty(me.headerData.unit2)) {
          return true
        }
        return me.showDisable
      },
      factorErpDisable() {
        let me = this
        if (isNullOrEmpty(me.headerData.unitErp)) {
          return true
        }
        return me.showDisable
      },
      /**
       * 是否禁用操作
       * @returns {boolean}
       */
      editDisabled() {
        return !!(['2'].includes(this.headerData.apprStatus) || this.showDisable)
      },
      copGNoDisable() {
        let me = this
        if (me.editConfig && me.editConfig.editData && me.editConfig.editData['modifyMark'] === '1') {
          return true
        }
        if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT && me.headerData.apprStatus === '0') {
          return false
        }
        return true
      },
      isEdit() {
        return this.editConfig.editStatus === editStatus.EDIT || this.editConfig.editStatus === editStatus.ADD
      },
      showRat() {
        return this.configData.checkExciseTax !== '0'
      },
      /**
       * ['品牌类型', '出口享惠情况']
       * @returns {string[]}
       */
      validNames() {
        let me = this,
          result = ['品牌类型']
        if (me.headerData.gmark === 'E') {
          result.push('出口享惠情况')
        }
        return result
      }
    }

    ,created: function () {

      let me = this
      // 物料种类
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/COP_MARK').then(res => {
        me.copMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.copMarkList = []
      })

      // 外库物料标志
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/OUT_COP_MARK').then(res => {
        me.outCopMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.outCopMarkList = []
      })
    },
  }
</script>

<style lang="less" scoped>
  .dc-card > .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }

  .buttonsClass button {
    margin-right: 5px;
  }
</style>
