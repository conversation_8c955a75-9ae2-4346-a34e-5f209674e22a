import {isNullOrEmpty} from '@/libs/util'
import {baseColumns} from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 100,
          title: '状态',
          key: 'apprStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.APPR_STATUS_MAP)
          },
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 100,
          key: 'status',
          title: '使用状态',
          render: (h, params) => {
            let showTitle = ''
            if (params.row.status === '0') {
              showTitle = '0 启用'
            } else if (params.row.status === '1') {
              showTitle = '1 停用'
            }
            if (isNullOrEmpty(showTitle)) {
              return h('span', '')
            } else {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                on: {
                  click: () => {
                    this.hideLogs(true, params.row.sid)
                  }
                }
              }, showTitle)
            }
          }
        },
        {
          width: 120,
          title: '制单日期',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          },
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 180,
          key: 'copGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 100,
          key: 'gmark',
          title: '物料种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.COPMARK_SELECT_NON)
          },
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 100,
          key: 'ciqNo',
          title: 'CIQ代码'
        },
        {
          width: 150,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 150,
          key: 'gmodel',
          tooltip: true,
          title: '申报规格型号'
        },
        {
          width: 150,
          tooltip: true,
          key: 'copGName',
          title: '海关税则描述'
        },
        {
          width: 150,
          tooltip: true,
          key: 'copGModel',
          title: '料号申报要素'
        },
        {
          width: 120,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 100,
          key: 'curr',
          tooltip: true,
          title: '申报币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 100,
          key: 'factor1',
          title: '法一比例因子'
        },
        {
          width: 100,
          key: 'factor2',
          title: '法二比例因子'
        },
        {
          width: 120,
          tooltip: true,
          key: 'unitErp',
          title: 'ERP计量单位'
        },
        {
          width: 100,
          key: 'factorErp',
          title: 'ERP比例因子'
        },
        {
          width: 100,
          key: 'qtyWt',
          title: '净重数量'
        },
        {
          width: 100,
          key: 'netWt',
          title: '对应净重'
        },
        {
          width: 120,
          tooltip: true,
          key: 'country',
          title: '产销国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 80,
          title: '随附单据',
          key: 'attachName'
        },
        {
          width: 120,
          title: '有效开始日期',
          key: 'recordDateStart',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          title: '有效截止日期',
          key: 'recordDateEnd',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          title: '制单员',
          key: 'userName',
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 100,
          tooltip: true,
          title: '原始物料',
          key: 'originalGNo'
        },
        {
          width: 100,
          tooltip: true,
          title: '监管条件',
          key: 'credentials',
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 100,
          tooltip: true,
          title: '检验检疫',
          key: 'inspmonitorcond'
        },
        {
          width: 150,
          tooltip: true,
          key: 'updateTime',
          title: '最后修改日期',
          sortable: true,
          headerClass: 'split-title'
        },
        {
          width: 120,
          title: '成本中心',
          key: 'costCenter'
        },
        {
          width: 200,
          key: 'note',
          title: '备注'
        },
        {
          width: 150,
          tooltip: true,
          title: '英文名称',
          key: 'copGNameEn'
        },
        {
          width: 150,
          tooltip: true,
          key: 'copGModelEn',
          title: '英文规格型号'
        }, {
          width: 150,
          title: '客供单价',
          key: 'clientPrice'
        },
        {
          width: 150,
          title: '商品构成',
          key: 'structure'
        },
        {
          width: 150,
          key: 'useTo',
          title: '商品用途'
        }, {
          title: '客户/供应商代码',
          key: 'supplierCode'
        },
        {
          title: '工厂代码',
          key: 'factoryCode'
        },


        {
          title: '企业物料类型',
          minWidth: 110,
          align: 'center',
          key: 'copMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.copMarkList)
          }
        },
        {
          title: '外库物料标志',
          minWidth: 120,
          align: 'center',
          key: 'outCopMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.outCopMarkList)
          }
        },
        {
          title: '外库信息',
          minWidth: 255,
          align: 'center',
          key: 'outMessage',
        },
        {
          title: '免表清单序号',
          minWidth: 180,
          align: 'center',
          key: 'freeBillNo',
        },
        {
          title: '免税依据',
          minWidth: 180,
          align: 'center',
          key: 'freeBasis',
        },
        {
          title: '免税物料备注',
          minWidth: 180,
          align: 'center',
          key: 'matFreeRemark',
        },
        {
          title: '商品构成',
          minWidth: 255,
          align: 'center',
          key: 'productElement',
        },
        {
          title: '商品用途',
          minWidth: 255,
          align: 'center',
          key: 'productExplain',
        },
        {
          title: '征免税',
          width: 255,
          key: 'taxation',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.TAXATION_SELECT)
          }
        }
      ],
      totalExcelColumns: [
        {
          key: 'copGNo',
          title: '企业料号'
        },
        {
          key: 'gmark',
          title: '物料种类'
        },
        {
          key: 'codeTS',
          title: '商品编码'
        },
        {
          key: 'gname',
          title: '商品名称'
        },
        {
          key: 'gmodel',
          title: '申报规格型号'
        },
        {
          key: 'unit',
          title: '申报计量单位'
        },
        // {
        //   key: 'copGName',
        //   title: '中文名称'
        // },
        // {
        //   key: 'copGModel',
        //   title: '中文规格型号'
        // },
        // {
        //   key: 'country',
        //   title: '产销国(地区)'
        // },
        {
          title: '单价',
          key: 'decPrice'
        },
        {
          key: 'curr',
          title: '币制'
        },
        // {
        //   title: '英文名称',
        //   key: 'copGNameEn'
        // },
        // {
        //   key: 'copGModelEn',
        //   title: '英文规格型号'
        // },
        // {
        //   title: '法一比例因子',
        //   key: 'factor1'
        // },
        // {
        //   title: '法二比例因子',
        //   key: 'factor2'
        // },
        {
          title: 'ERP计量单位',
          key: 'unitErp'
        },
        // {
        //   title: 'ERP比例因子',
        //   key: 'factorErp'
        // },
        // {
        //   title: '净重数量',
        //   key: 'qtyWt'
        // },
        // {
        //   title: '对应净重',
        //   key: 'netWt'
        // },
        // {
        //   title: '原始物料',
        //   key: 'originalGNo'
        // },
        // {
        //   title: '成本中心',
        //   key: 'costCenter'
        // },
        {
          key: 'note',
          title: '备注'
        },
        // {
        //   title: '有效开始日期',
        //   key: 'recordDateStart'
        // },
        // {
        //   title: '有效截止日期',
        //   key: 'recordDateEnd'
        // },
        {
          key: 'ciqNo',
          title: 'CIQ代码'
        },
        // {
        //   title: '客供单价',
        //   key: 'clientPrice'
        // },
        // {
        //   width: 150,
        //   title: '商品构成',
        //   key: 'structure'
        // },
        // {
        //   width: 150,
        //   key: 'useTo',
        //   title: '商品用途'
        // },

        // {
        //   title: '物料种类',
        //   width: 90,
        //   key: 'copMark',
        // },
        // {
        //   title: '外库物料标志',
        //   width: 120,
        //   key: 'outCopMark',
        // },
        {
          title: '外库信息',
          width: 255,
          key: 'outMessage',
        },
        {
          title: '免表清单序号',
          width: 180,
          key: 'freeBillNo',
        },
        // {
        //   title: '商品构成',
        //   width: 255,
        //   key: 'productElement',
        // },
        // {
        //   title: '商品用途',
        //   width: 255,
        //   key: 'productExplain',
        // },
        {
          title: '征免税',
          width: 255,
          key: 'taxation',
        }, {
          title: '免税依据',
          width: 255,
          key: 'freeBasis',
        }
      ]
    }
  }
}
