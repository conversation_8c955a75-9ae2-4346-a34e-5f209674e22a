<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="realShowList" ref="billBase">
      <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig" @onImportSuccess="onAfterImport"></ImportPage>
      <ImportPage :importKey="updateImportKey" :importShow.sync="updateImportShow" :importConfig="updateImportConfig" @onImportSuccess="onAfterImport"></ImportPage>
      <!--测试专用，后期请删除-->
      <ImportPage :importKey="importTextKey" :importShow.sync="TextImportShow" :importConfig="importTextConfig" @onImportSuccess="onAfterTextImport"></ImportPage>
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <nonBondedHeadSearch ref="headSearch"></nonBondedHeadSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:allupdate>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify" :loading="downloading">
                    <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:send-audit>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-send" size="22" class="xdo-icon"/>发送内审<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-send" @click="handleSendAppr" :loading="downloading">
                    <XdoIcon type="ios-send" size="22" class="xdo-icon"/>  勾选发送
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-send" @click="handleSendAllAppr">
                    <XdoIcon type="ios-send" size="22" class="xdo-icon"/>  全部发送
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <nonBondedTabs v-if="!showList" ref="nonBondedTabs" @onEditBack="editBack" :editConfig="editConfig"></nonBondedTabs>
    <RecordInformation :show.sync="show" :editConfig="editConfig"></RecordInformation>
    <StartStopReasonsPop :show.sync="reasonPopShow" :status="doStatus" @confirm:success="doSetStatus"></StartStopReasonsPop>
    <StartStopReasonsList v-if="showLog" :headId="currHeadId" @hideLogs="hideLogs"></StartStopReasonsList>
    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="totalColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ProductCopyPop :show.sync="productCopyShow" bond-mark="1" @doCopy="doCopy"></ProductCopyPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import RecordInformation from './RecordInformation'
  import { columns } from './nonBondedHeadListColumns'
  import { nonBondedList } from '../base/nonBondedList'

  export default {
    name: 'nonBondedHeadList',
    mixins: [nonBondedList, columns],
    components: {
      RecordInformation
    },
    data() {
      let importConfig = this.getCommImportConfig('NONBONDED', {
          moduleType: 'Nonbonded'
        }),
        updateImportConfig = this.getCommImportConfig('NONBONDED-UPDATE', {
          moduleType: 'NonbondedUpdate'
        }),
        textImportConfig = this.getCommImportConfig('PACKING-E-LIST', {
          headId: '123456',
          emsNo: 'E231419A0054',
          insertUserName: this.$store.state.user.userName
        })
      return {
        listId: '',
        show: false,
        searchLines: 5,
        exportColumns: [],
        listSetupShow: false,
        productCopyShow: false,
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'send-audit': this.handleSendAppr,
          'disable': this.handleDisable,
          'enable': this.handleEnable,
          'import': this.uploadOrdinary,
          'export': this.downLoadOrdinary,
          'copy': this.handleCopy,
          'export-test': this.textImport, // 测试专用，后期请删除
          'extract': this.handleExtract,
          'settings': this.handleTableColumnSetup
        },
        //导入界面组件key
        importKey: 'nonbonded',
        importConfig: importConfig,
        updateImportKey: 'nonbondedUpdate',
        updateImportConfig: updateImportConfig,
        // 测试专用，后期请删除
        TextImportShow: false,
        importTextKey: 'PACKING-E-LIST',
        importTextConfig: textImportConfig,
        pageSizeOpts: [10, 20, 50, 100, 200, 500, 1000],
        ajaxUrl: {
          copyUrl: csAPI.csProductClassify.nonBonded.copy,
          delete: csAPI.csProductClassify.nonBonded.delete,
          extract: csAPI.aeoManage.aeoReview.actions.extract,
          exportUrl: csAPI.csProductClassify.nonBonded.exportUrl,
          setStatus: csAPI.csProductClassify.nonBonded.setStatus,
          sendDataM: csAPI.aeoManage.aeoReview.actions.sendDataM,
          matNonBonded: csAPI.aeoManage.aeoReview.actions.matNonBonded,
          selectAllPaged: csAPI.csProductClassify.nonBonded.selectAllPaged,
          checkCodeTS: csAPI.aeoManage.aeoReview.actions.nonBondedHsCodeCheckBySelected,
          checkCodeTSAll: csAPI.aeoManage.aeoReview.actions.nonBondedHsCodeCheckByParams
        }

        , copMarkList: []
        , outCopMarkList: []
      }
    },
    created: function () {
      let me = this
      me.listId = me.$route.path + '/' + me.$options.name
      let columns = me.$bom3.showTableColumns(me.listId, me.totalColumns)
      me.handleUpdateColumn(columns)

      // 物料种类
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/COP_MARK').then(res => {
        me.copMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.copMarkList = []
      })

      // 外库物料标志
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/OUT_COP_MARK').then(res => {
        me.outCopMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.outCopMarkList = []
      })
    },
    methods: {
      handleTableColumnSetup() {
        this.listSetupShow = true
      },
      /**
       * 保存列表设置
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.$set(me, 'exportColumns', columns.map(column => {
          return {
            key: column.key,
            value: column.title
          }
        }))
      },
      handleDownload(status) {

        let me = this
        let title = ''
        if (status === '0') {
          title = '商品归类-非保税预归类'
          me.gridConfig.exportColumns = me.exportColumns
        } else {
          title = '商品归类-非保税预归类(修改)'
          me.gridConfig.exportColumns = me.totalExcelColumns.map(column => {
            return {
              key: column.key,
              value: column.title
            }
          })

        }
        me.$set(me.gridConfig, 'exportTitle', title)
        me.downloading = true
        me.doExport(me.ajaxUrl.exportUrl + '/' + status, -1, function () {
          me.downloading = false
        })
      },
      handleCopy() {
        let me = this
        if (me.checkRowSelected('复制', true)) {
          me.$set(me, 'productCopyShow', true)
        }
      },
      /**
       * 执行复制
       * @param facGNo
       */
      doCopy(facGNo){
        let me = this,
          sid = me.gridConfig.selectRows[0].sid
        me.$http.post(me.ajaxUrl.copyUrl, {
          sid: sid,
          copGNo: facGNo
        }).then(res => {
          me.$Message.success(res.data.message)
          me.getList()
        }).catch(() => {
        })
      },
      /**
       * 提取(LG)
       */
      handleExtract() {
        let me = this
        me.$http.post(me.ajaxUrl.extract + '/1').then(res => {
          me.$Message.success(res.data.message)
        }, () => {
        }).finally(() => {
          me.getList()
        })
      },
      /**
       * 测试专用，后期请删除
       */
      textImport() {
        this.TextImportShow = true
      },
      onAfterTextImport() {
        this.TextImportShow = false
        this.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
  /deep/ .split-title{
    background-color: #8DBED5;
  }
</style>
