/***
 * 关务-商品归类-路由
 */
import { namespace } from '@/project'
import bondedImgHeadList from './bondedImg/bondedImgHeadList'
import bondedExgHeadList from './bondedExg/bondedExgHeadList'
import nonBondedHeadList from './nonBonded/nonBondedHeadList'
import modifyRecordQueryTabs from './modify-record-query/modify-record-query-tabs'

export default [
  {
    path: '/' + namespace + '/productClassify/bondedImg',
    name: 'bondedImgHeadList',
    meta: {
      title: '企业料件信息'
    },
    component: bondedImgHeadList
  },
  {
    path: '/' + namespace + '/productClassify/bondedExg',
    name: 'bondedExgHeadList',
    meta: {
      title: '企业成品信息'
    },
    component: bondedExgHeadList
  },
  {
    path: '/' + namespace + '/productClassify/nonBonded',
    name: 'nonBondedHeadList',
    meta: {
      title: '非保税预归类'
    },
    component: nonBondedHeadList
  },
  {
    path: '/' + namespace + '/productClassify/modifyRecordQuery',
    name: 'modifyRecordQueryTabs',
    meta: {
      title: '修改记录查询'
    },
    component: modifyRecordQueryTabs
  }
]
