<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary"  class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <deepProduceImpRelationHeadSearch ref="headSearch"></deepProduceImpRelationHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                       style="font-size: 12px" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </XdoButton>&nbsp;
          </template>
          <!--<Button type="warning" :disabled="isUpdate" class="dc-margin-right" @click="getInto">提取</Button>-->
        </div>
      </XdoCard>

      <XdoCard :bordered="false">
        <!--@on-row-dblclick="handleRowDblClick"-->
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
               :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                show-total show-sizer
                @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <!--编辑页面 先不开，需要研究-->
    <deepProduceImpRelationHeadTemplateTabs v-if="!showList&&showTabs" @oneditback="editBack" :typeNo="typeNo" :searchData="searchData" :textData="textData" :compData="compData"></deepProduceImpRelationHeadTemplateTabs>
  </section>
</template>
<script>

  import { csAPI, excelExport } from '@/api'
  import { columnsConfig, excelColumnsConfig, columns } from './deepProduceImpRelationHeadListColumns'
  import deepProduceImpRelationHeadSearch from './deepProduceImpRelationHeadSearch'
  import deepProduceImpRelationHeadTemplateTabs from "./deepProduceImpRelationHeadTemplateTabs";
  import { editStatus, pageParam } from '../../cs-common'
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'

  export default {
    name: 'deepProduceImpRelationHeadList',
    components: {
      deepProduceImpRelationHeadTemplateTabs,
      deepProduceImpRelationHeadSearch,
    },
    mixins: [dynamicHeight, columns],
    data() {
      return {
        chaxun:false,
        isUpdate:false,
        isNotice:true,
        notice:'',

        showHead: true,
        showList:true,
        showSearch: false,
        showTabs:false,
        actions: [
          { type: 'text', disabled: false, click: this.handleAdd, icon: 'ios-add', label: '新增', key:'xdo-btn-add', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleEdit, icon: 'ios-create-outline', label: '编辑', key:'xdo-btn-edit', loading: false, needed: true },
          // { type: 'text', disabled: false, click: this.handleImport, icon: 'ios-cloud-upload-outline', label: '导入', key:'xdo-btn-upload', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleDownload, icon: 'ios-cloud-download-outline', label: '导出', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleDelete, icon: 'ios-trash-outline', label: '删除', key:'xdo-btn-delete', loading: false, needed: true }
        ],
        editConfig: {
          editData: {},
          headId: '',
          editStatus: editStatus.SHOW
        },
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        dataList:[],
        compData:[]
      }
    },
    mounted: function() {
//      this.searchDataList()
      this.refreshDynamicHeight(120, !this.showSearch ? ['area_search'] : null)
      this.handleSearchSubmit()
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    },
    methods: {
      //页面中点击删除走的方法
      handleDelete(){
        if (this.gridConfig.selectRows.length > 0) {
          this.$Modal.confirm({
            title: '提醒',
            content: '确认删除所选项吗',
            okText: '删除',
            cancelText: '取消',
            onOk: () => {
              const sids = this.gridConfig.selectRows.map(item => {
                return item.sid
              })
              this.$http.delete(csAPI.deep.deepImpRecord.head.deleteHead + `/${sids}`).then(() => {
                this.$Message.success('删除成功！')
                this.gridConfig.selectRows = []
                this.getList()  //调一下获取表体列表的方法
              }).catch(() => {
              }).finally(() => {
              })
            },
            onCancel: () => {
            }
          })
        } else {
          this.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }
      },

      //点击页面导出时候的方法
      handleDownload() {
        const params = {
          exportColumns: Object.assign({}, this.$refs.headSearch.searchParam),
          name: '深加工关系维护表头数据.xls',
          header: getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
        }
        excelExport(csAPI.deep.deepImpRecord.head.exporheaList, params).finally(() => {
        })
      },

      //页面点击每一行的查看的时候走的js方法查看时候走的方法，该方法在column.js中定义的方法
      handleView(val){
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.searchData = val
        this.typeNo = 2 //查看
      },
      //点击编辑的时候走的方法
      handleEditOper(){
        this.searchData = this.gridConfig.selectData
//        this.searchData = this.gridConfig.selectRows[0]
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.compData = this.dataList
        this.typeNo = 1//用来标记是新增还是修改，1表示修改操作
        this.gridConfig.selectRows = []
      },
      //点击页面新增按钮的方法
      handleAdd(){
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
//        this.compData = this.dataList
        this.searchData = {}
        this.typeNo = 0 //用来标记是新增还是修改，0表示新增操作
      },

      //页面编辑按钮
      handleEdit(){
        if (this.gridConfig.selectRows.length === 0) {
          this.$Message.warning('请选择您要编辑的数据！')
        } else if (this.gridConfig.selectRows.length > 1) {
          this.$Message.warning('一次仅能编辑一条数据！')
        } else {
          this.searchData = this.gridConfig.selectRows[0]
          this.showHead = !this.showHead
          this.showList = !this.showList
          this.showTabs = !this.showTabs
          this.compData = this.dataList
          this.typeNo = 1//用来标记是新增还是修改，1表示修改操作
          this.gridConfig.selectRows = []
        }
      },
      //点击查询条件时候走的方法
      handleShowSearch() {
        this.showSearch = !this.showSearch
        this.chaxun = !this.chaxun  //展示出查询按钮的显示隐藏
        this.refreshDynamicHeight(158, !this.showSearch ? ['area_search'] : null)
      },
      //页面一加载就走的方法走了后台查询所有的方法
      handleSearchSubmit() {
//        console.log('走了请求后台的方法了')
        this.pageParam.page = 1
        this.getList() //走一下后台的初始化方法
      },
      //最终请求后台查询所有数据的js方法
      getList() {
        pageParam.page = this.pageParam.page
        pageParam.limit = this.pageParam.limit
        const data = this.$refs.headSearch.searchParam

        this.$http.post(csAPI.deep.deepImpRecord.head.getHeadList.selectHeadList, data, { params: pageParam }).then(res => {
          this.gridConfig.data = res.data.data
          console.log(res.data.data)
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
        })
      },

      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        this.pageParam.page = page
        this.getList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        this.getList()
      },

      editBack(val) {
        if (val) {
          this.showHead = !this.showHead
          this.showList = !this.showList
          this.showTabs = !this.showTabs
          this.getList()
        }
      }
    },
    computed: {}
  }
</script>
<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
