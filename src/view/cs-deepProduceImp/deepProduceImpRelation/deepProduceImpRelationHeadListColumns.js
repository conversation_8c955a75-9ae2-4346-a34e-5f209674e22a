//import { formatDate } from '@/libs/datetime'

const columnsConfig = ['selection'
  , 'operation'
  , 'tradeCodeOut' //转出方海关十位代码
  , 'tradeNameOut'
  , 'emsNoOut' //转出方手账册号
  , 'validDateOut' //转出方手账册有效期
  , 'insertTime'  //建立日期
  , 'userName'  //建立人
]
const excelColumnsConfig = [
   'tradeCodeOut' //转出方海关十位代码
  , 'tradeNameOut'
  , 'emsNoOut' //转出方手账册号
  , 'validDateOut' //转出方手账册有效期
  , 'insertTime'  //建立日期
  , 'userName'  //建立人
]
const columns = {
  data() {
    return {
      totalColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          key: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.handleEditOper()
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleView(params.row)
                  }
                }
              }, '查看')
            ])
          },
          key: 'operation'
        },
        {
          title: '转出方海关十位代码',
          minWidth: 120,
          align: 'center',
          key: 'tradeCodeOut'
        },
        {
          title: '转出方企业名称',
          minWidth: 180,
          align: 'center',
          key: 'tradeNameOut'
        },
        {
          title: '转出方手（账）册号',
          minWidth: 120,
          align: 'center',
          key: 'emsNoOut'
        },
        {
          title: '转出方手（账）册有效期',
          minWidth: 120,
          align: 'center',
          key: 'validDateOut'
        },
        {
          title: '录入日期',
          minWidth: 120,
          align: 'center',
          key: 'insertTime'
        },
        {
          title: '录入人',
          minWidth: 120,
          align: 'center',
          key: 'userName'
        }
      ]
    }
  }
}
export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
