<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="转入数据管理">
        <Head ref="head" @onEditback="editback" :typeNoInfor="this.typeNo" :searchDataInfor="this.searchData" :textDataInfor="this.textData" :compData="compData"></Head>
      </TabPane>
      <TabPane name="billTab"   label="清单">
        <DetailList ref="bill" @onEditback="editback" :typeNoInfor="this.typeNo" :searchDataInfor="this.searchData" :textDataInfor="this.textData" :compData="compData"></DetailList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
    import Head from './deepProduceImpFromToHeadEdit'
    import DetailList from './deepProduceImpFromToDetailList'
    export default {
        name: "deepProduceImpFromToHeadTemplateTabs",
        components:{Head,DetailList},
        props:{
          typeNo:{type: String, default: () => ({})},
          searchData: {type: Object, default: () => ({})},
          textData:{type: Object, default: () => ({})},
//          compData:{type: Array, default: () => ({})},
        },
        data(){
          return{
            tabName: 'headTab',
            searchDataInfor:{},
            textDataInfor:{},
            typeNoInfor:''
          }
        },
      mounted(){
          //this.searchSend()
      },
      methods:{
          editback(val){
            if(val){
              this.$emit('oneditback',true)
            }
          },
          //传值
//           searchSend(){
//             this.searchDataInfor = this.searchData
//             this.textDataInfor = this.textData
//             this.typeNoInfor = this.typeNo
//           }
      }
    }
</script>

<style scoped>

</style>
