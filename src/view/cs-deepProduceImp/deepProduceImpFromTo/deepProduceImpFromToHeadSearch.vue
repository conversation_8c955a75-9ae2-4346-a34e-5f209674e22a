<template>
  <section v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="130" inline>
      <XdoFormItem prop="status" label="状态">
        <!--<xdo-select v-model="searchParam.status" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>-->
        <xdo-select v-model="searchParam.status" :options="this.inDataSource.status" @on-change="onErrorTypeChange"></xdo-select>

      </XdoFormItem>
      <XdoFormItem prop="emsCopNoIn" label="转入内部编号">
        <XdoIInput type="text" v-model="searchParam.emsCopNoIn"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="结转周期"  @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="tradeNameOut" label="转出方名称及代码">
        <XdoIInput type="text" v-model="searchParam.tradeNameOut"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsNoOut" label="转出方备案号">
        <XdoIInput type="text" v-model="searchParam.emsNoOut"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsNoIn" label="转入方备案号">
        <XdoIInput type="text" v-model="searchParam.emsNoIn"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="applyNo" label="申请表编号">
        <XdoIInput type="text" v-model="searchParam.applyNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="receiveBillNo" label="收货单编号">
        <XdoIInput type="text" v-model="searchParam.receiveBillNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNoIn" label="转入报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoIn"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNoOut" label="转出报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoOut"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { delList } from '../../cs-common'

  export default {
    name: 'DecEntryHeadFormalHeadSearch',
    data() {
      return {
        searchParam: {
          beginDateFrom: '',//结转周期开始时间
          beginDateTo: '',//结转周期结束时间
          status: '',//状态
          emsCopNoIn: '',//转入内部编号
          tradeNameOut: '',//转出方名称及代码
          emsNoOut: '',//转出方备案号
          emsNoIn: '',//转入方备案号
          applyNo: '',//申请表编号
          receiveBillNo: '',//收货单编号
          entryNoIn: '',//转入报关单号
          entryNoOut: '' //转出报关单号
        },
        inDataSource: {
          status: delList.status
        }
      }
    },
    methods: {
      handleDMDateChange(e) {
        this.searchParam.beginDateFrom = e[0];//转出方手账册有效期开始时间赋值（e默认传数组，第一个元素为开始时间，第一个为开始时间）
        this.searchParam.beginDateTo = e[1];//转出方手账册有效期结束时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
      }
    }
  }
</script>

<style scoped>
</style>
