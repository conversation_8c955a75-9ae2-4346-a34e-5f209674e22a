<template>
  <div>
    <ReportPage :printConfig="printConfig" :modelkey="modelkey"></ReportPage>
  </div>
</template>

<script>
  import {ReportPage} from 'xdo-report'
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: "ReprotList",
    components: {ReportPage},
    data () {
      return {
        printConfig:{
          api: this.$http,
          baseuri:'',//配置接口根路径(可为空，默认使用'/xdoReport/api')
          companyName:'',
          basesearchuri:'gwstd/api',// 业务接口根路径(必传)
          //如果使用自定义select,需传入下值，每个属性名需要与配置中的paraName相同，属性值需要是键值对格式
          selectvalue:{
            //转出方海关十位代码
            tradeCodeOut:[
              {
                value: '',
                label: ''
              }
            ],
            //转入方手/账册号
            emsNoIn:[
              {
                value: '',
                label: ''
              }
            ],
            //转出方企业代码
            customerCodeOut:[
              {
                value: '',
                label: ''
              }
            ]
          },
          //隐藏参数，pageSign根据后端配置填入，params内为实际参数，支持数值和函数
          hidenParams:[
            {
              pageSign:'1234',
              params:
                {
                  a:1
                }
            },
            {
              pageSign:'1234568',
              params:{
                c:5
              }
            }
          ]
        },
        modelkey:{
          companyCode:'123232',//企业代码
          moduleKey:'deep',//必传，模块标识(业务传入前端组件)
          ext:''
        }
      }
    },
    mounted(){
      this.getZcfhgswdm() //加载获取转出方海关十位代码
      this.getZrfSzch() //加载获取转入方手/账册号
      this.getZcfqydm() //加载获取转出方企业代码
      this.getName() //获取当前公司名字
    },
    methods:{
      //获取转出方海关十位代码
      getZcfhgswdm(){
//        console.log('走aaa')
        this.$http.post(csAPI.ieParams.GETHGSWDM).then(res => {
          if (res.status === 200) {
            if (res.data.success === true) {
              this.printConfig.selectvalue.tradeCodeOut = ArrayToLocaleLowerCase(res.data.data)
//              console.log(this.printConfig.selectvalue.tradeCodeOut)
            } else {
              this.printConfig.selectvalue.tradeCodeOut = []
            }
          }
        }).catch(() => {
          this.$Message.warning('该供应商还未维护海关代码!')
          this.printConfig.selectvalue.tradeCodeOut=[]
        })
      },

      //获取转入方手/账册号
      getZrfSzch(){
        let type='1';
        let tradeCode='2';
        this.$http.post(csAPI.ieParams.GETZRC+`/${type}`+`/${tradeCode}`).then(res => {
          let tmpArr = []
          for (let item of res.data.data) {
            tmpArr.push({
              value: item.VALUE,
              label: item.VALUE
            })
          }
            console.log(tmpArr)
          this.printConfig.selectvalue.emsNoIn = tmpArr
        }).catch(() => {
          this.$Message.warning('未查到转入方手/账册号!')
          this.printConfig.selectvalue.emsNoIn=''
        })
      },

      //获取转出方企业代码
      getZcfqydm(){
//        console.log('走aaa')
        this.$http.post(csAPI.ieParams.GETDQMD).then(res => {
          if (res.status === 200) {
            if (res.data.success === true) {
              this.printConfig.selectvalue.customerCodeOut = ArrayToLocaleLowerCase(res.data.data)
//              console.log(this.printConfig.selectvalue.tradeCodeOut)
            } else {
              this.printConfig.selectvalue.customerCodeOut = []
            }
          }
        }).catch(() => {
          this.$Message.warning('该供应商还未维护企业代码!')
          this.printConfig.selectvalue.customerCodeOut=[]
        })
      },
      //获取当前公司名字
      getName(){
//        console.log('222')
//        console.log(this.$store.state.user.companyName)
        let name=this.$store.state.user.companyName
        this.printConfig.companyName=name
      }
    }
  }
</script>
<style scoped>
</style>



















