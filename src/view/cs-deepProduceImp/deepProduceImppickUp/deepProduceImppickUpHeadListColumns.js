//import { formatDate } from '@/libs/datetime'

const columnsConfig = ['selection'
  , 'operation'
  ,'isRecord'//是否备案
  ,'confirmStatus'//状态
  ,'billNo'//单证编号
  ,'lineNo'//序号
  ,'lineNo'//供应商名称
  ,'warehouseNo'//库位别
  ,'ieType'//移动类型编号
  ,'gmark'//物料类型标识
  ,'linkedNo'//关联编号
  ,'poNo'//PO号
  ,'invoiceNo'//发票号
  ,'deliveryDate'//日期
  ,'facGNo'//料号
  ,'qty'//数量
  ,'unit'//单位
  ,'decPrice'//单价
  ,'decTotal'//总价
  ,'curr'//币制
  ,'bondMark'//保完税标志
  ,'supplierCode'//供应商代码
  ,'inWay'//进口方式
  ,'expStatus'//结转状态
  ,'supplierLinkMan'//供应商联系人
  ,'businessMan'//业务员代号
  ,'costCenter'//成本中心
  ,'note'//备注
]
const excelColumnsConfig = [
  'operation'
  ,'isRecord'//是否备案
  ,'confirmStatus'//状态
  ,'billNo'//单证编号
  ,'lineNo'//序号
  ,'lineNo'//供应商名称
  ,'warehouseNo'//库位别
  ,'ieType'//移动类型编号
  ,'gmark'//物料类型标识
  ,'linkedNo'//关联编号
  ,'poNo'//PO号
  ,'invoiceNo'//发票号
  ,'deliveryDate'//日期
  ,'facGNo'//料号
  ,'qty'//数量
  ,'unit'//单位
  ,'decPrice'//单价供应商
  ,'decTotal'//总价
  ,'curr'//币制
  ,'bondMark'//保完税标志
  ,'supplierCode'//供应商代码
  ,'inWay'//进口方式
  ,'expStatus'//结转状态
  ,'supplierLinkMan'//供应商联系人
  ,'businessMan'//业务员代号
  ,'costCenter'//成本中心
  ,'note'//备注
]
const columns = {
  data() {
    return {
      totalColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          key: 'selection'
        },


        {
          title: '备案',
          minWidth: 120,
          align: 'center',
          key: 'isRecord'
        },
        {
          title: '状态',
          minWidth: 120,
          align: 'center',
          key: 'confirmStatus'
        },
        {
          title: '单证编号',
          minWidth: 120,
          align: 'center',
          key: 'billNo'
        },
        {
          title: '序号',
          minWidth: 120,
          align: 'center',
          key: 'lineNo'
        },
        {
          title: '供应商名称',
          minWidth: 120,
          align: 'center',
          key: 'supplierCode'
        },
        {
          title: '库位别',
          minWidth: 120,
          align: 'center',
          key: 'warehouseNo'
        },
        {
          title: '移动类型编号',
          minWidth: 120,
          align: 'center',
          key: 'ieType'
        },
        {
          title: '物料类型标识',
          minWidth: 120,
          align: 'center',
          key: 'gmark'
        },
        {
          title: '关联编号',
          minWidth: 120,
          align: 'center',
          key: 'linkedNo'
        },
        {
          title: 'PO号',
          minWidth: 120,
          align: 'center',
          key: 'poNo'
        },
        {
          title: '发票号',
          minWidth: 120,
          align: 'center',
          key: 'invoiceNo'
        },
        {
          title: '交易日期',
          minWidth: 120,
          align: 'center',
          key: 'deliveryDate'
        },
        {
          title: '料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo'
        },
        {
          title: '数量',
          minWidth: 120,
          align: 'center',
          key: 'qty'
        },
        {
          title: '单位',
          minWidth: 120,
          align: 'center',
          key: 'unit'
        },
        {
          title: '单价',
          minWidth: 120,
          align: 'center',
          key: 'decPrice'
        },
        {
          title: '总价',
          minWidth: 120,
          align: 'center',
          key: 'decTotal'
        },
        {
          title: '币制',
          minWidth: 120,
          align: 'center',
          key: 'curr'
        },
        {
          title: '保完税标志',
          minWidth: 120,
          align: 'center',
          key: 'bondMark'
        },
        {
          title: '供应商代码',
          minWidth: 120,
          align: 'center',
          key: 'supplierCode',
          // render:(h,params) => {
          //   return h ('span',`${params.row.supplierCode}  ${params.row.supplierCode}`)
          // }
        },
        {
          title: '进口方式',
          minWidth: 120,
          align: 'center',
          key: 'inWay'
        },
        {
          title: '结转状态',
          minWidth: 120,
          align: 'center',
          key: 'expStatus'
        },
        {
          title: '供应商联系人',
          minWidth: 120,
          align: 'center',
          key: 'supplierLinkMan'
        },
        {
          title: '业务员代号',
          minWidth: 120,
          align: 'center',
          key: 'businessMan'
        },
        {
          title: '成本中心',
          minWidth: 120,
          align: 'center',
          key: 'costCenter'
        },
        {
          title: '备注',
          minWidth: 120,
          align: 'center',
          key: 'note'
        }





      ]
    }
  }
}
export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
