<template>
  <section v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="130" inline>
      <XdoFormItem prop="confirmStatus" label="状态">
        <xdo-select v-model="searchParam.confirmStatus" :options="this.inDataSource.status"  @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="billNo" label="单证编号">
        <XdoIInput type="text" v-model="searchParam.billNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="日期"  @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="inWay" label="进口方式">
        <xdo-select v-model="searchParam.inWay" :options="this.inDataSource.impWay" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="lineNo" label="序号">
        <XdoIInput type="text" v-model="searchParam.lineNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="warehouseNo" label="库位别">
        <XdoIInput type="text" v-model="searchParam.warehouseNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="移动类型编号">
        <xdo-select v-model="searchParam.ieType" :options="this.inDataSource.typeMove" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gmark" label="物料类型标识">
        <xdo-select v-model="searchParam.gmark" :options="this.inDataSource.wlFlag" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="poNo" label="PO号">
        <XdoIInput type="text" v-model="searchParam.poNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="bondMark" label="保完税标识">
        <xdo-select v-model="searchParam.bondMark" :options="this.inDataSource.noBondFlag" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="bondMark" label="是否备案">
        <xdo-select v-model="searchParam.isRecord" :options="this.inDataSource.sfba" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="供应商">
        <XdoIInput type="text" v-model="searchParam.supplierCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="customerCode" label="供应商代码">
        <XdoIInput type="text" v-model="searchParam.customerCode"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { delList } from '../../cs-common'

  export default {
    name: 'DecEntryHeadFormalHeadSearch',
    props: {
      delSearch: {type: String, default: () => ({})},//传值List页面中的 typeNo
    },
    data() {
      return {
        searchParam: {
          confirmStatus: '0',//状态
          billNo: '',//单证编号
          deliveryDateFrom: '',//日期
          deliveryDateTo: '',//日期
          inWay: '',//进口方式
          lineNo: '',//序号
          warehouseNo: '',//库位别
          ieType: '',//移动类型编号
          gmark: '',//物料类型标识
          poNo: '',//PO号
          invoiceNo: '',//发票号
          facGNo: '',//料号
          isRecord: '',//是否备案
          bondMark: '',//保完税标识
          supplierCode: '',
          customerCode: ''
        },
        inDataSource: {
          status: delList.status,//状态
          impWay: delList.impWay,//进口方式
          typeMove: delList.typeMove,//移动类型编号
          wlFlag: delList.wlFlag,//物料类型标识
          noBondFlag: delList.noBondFlag,//保完税标识
          sfba: delList.sfba//保完税标识
        }
      }
    },
    watch: {
      //监控变量 delSearch
      delSearch: function (val) {
        this.getAeoData(val)
      }
    },
    methods: {
      getAeoData(val) {
        if (val === true) {
          this.searchParam = {}
        }
      },
      handleDMDateChange(e) {
        this.searchParam.deliveryDateFrom = e[0];//转出方手账册有效期开始时间赋值（e默认传数组，第一个元素为开始时间，第一个为开始时间）
        this.searchParam.deliveryDateTo = e[1];//转出方手账册有效期结束时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
      }
    }
  }
</script>

<style scoped>
</style>
