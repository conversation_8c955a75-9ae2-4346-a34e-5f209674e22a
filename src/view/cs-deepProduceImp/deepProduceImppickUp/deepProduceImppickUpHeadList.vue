<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary"  class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <deepProduceImppickUpHeadSearch ref="headSearch" :delSearch="delSearch"></deepProduceImppickUpHeadSearch>
          </div>
        </div>
      </XdoCard>



      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                       style="font-size: 12px" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </XdoButton>&nbsp;
          </template>
          <!--<Button type="warning" :disabled="isUpdate" class="dc-margin-right" @click="getInto">提取</Button>-->
        </div>
      </XdoCard>

      <XdoCard :bordered="false">
        <!--@on-row-dblclick="handleRowDblClick"-->
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
               :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                show-total show-sizer
                @on-change="pageChange" @on-page-size-change="pageSizeChange">
            <span><span style="font-weight: bolder;margin-right: 10px">{{this.allPrice}}</span>  共{{pageParam.dataTotal}}条</span>
          </XdoPage>
        </div>
      </XdoCard>

    </div>

      <!-- 该模块为erp提取模块  -->
        <XdoModal  v-model="showErp" width="98%" :styles="{top:'30px'}" :footer-hide="true" :mask-closable="false">
          <div>
            <div v-if="showErp">
              <ShippingImgIeList @closePickUp="closePickUp" :typeNoInfor="typeNo"></ShippingImgIeList>
            </div>
          </div>
        </XdoModal>
<!--  该模块为确认的弹框页面    -->
        <XdoModal  v-model="showConfirm"  width="50%" :styles="{top:'60px'}" :footer-hide="true" :mask-closable="false">
          <div style="height: 200px">
            <div v-if="showConfirm">
              <confirm @close="close" :typeNoInfor="sids" @result="result" :flag="flag" :searchCon="searchCon"></confirm>
<!--              <confirm :typeNoInfor=""></confirm>-->
            </div>
          </div>
        </XdoModal>
      </section>
</template>
<script>

  import { csAPI } from '@/api'
  import { columnsConfig, columns } from './deepProduceImppickUpHeadListColumns'
  import deepProduceImppickUpHeadSearch from './deepProduceImppickUpHeadSearch'
  import confirm from './confirm'
  import { editStatus, pageParam } from '../../cs-common'
  import { dynamicHeight, getColumnsByConfig } from '@/common'
  import ShippingImgIeList from "../../cs-sap/shipping-erpMaterialIList/shipping-material-list";


  export default {
    name: 'deepProduceImppickUpHeadList',
    components: {
      ShippingImgIeList,
      deepProduceImppickUpHeadSearch,
      confirm,
    },
    mixins: [dynamicHeight, columns],
    data() {
      return {
        flag:'',//0:确认勾选  1：确认全部
        typeNo:'',
        tradeCodeOut:'',
        sids:[],
        searchCon:{},

        isUpdate:false,
        isNotice:true,
        notice:'',
        delSearch:'',
        showHead: true,
        showSearch: false,
        showErp: false,
        showConfirm: false,
        actions: [
          { type: 'text', disabled: false, click: this.handlePickUp, icon: 'ios-add', label: '提取ERP数据', key:'xdo-btn-add', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleConfirmSome, icon: 'ios-checkmark', label: '确认勾选', key:'xdo-btn-add', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleConfirmAll, icon: 'md-done-all', label: '确认全部', key:'xdo-btn-upload', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.handleDel, icon: 'ios-trash-outline', label: '删除', key:'xdo-btn-upload', loading: false, needed: true }
         ],
        editConfig: {
          editData: {},
          headId: '',
          editStatus: editStatus.SHOW
        },
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        dataList:[],
        compData:[],
        allNum:'',
        allPrice:''
      }
    },
    mounted: function() {
      //this.searchDataList()
      this.refreshDynamicHeight(120, !this.showSearch ? ['area_search'] : null)
      this.handleSearchSubmit()
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    },
    methods: {
      handleDMDateChange(e){
        this.searchParam.deliveryDateFrom=e[0];//转出方手账册有效期开始时间赋值（e默认传数组，第一个元素为开始时间，第一个为开始时间）
        this.searchParam.deliveryDateTo=e[1];//转出方手账册有效期结束时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
      },
      //点击页面提取ERP数据按钮的方法
      handlePickUp(){
        // this.showHead = !this.showHead
        this.showErp = !this.showErp
        this.typeNo = 0 //用来标记是新增还是修改，0表示新增操作

      },
      //点击页面确认勾选的时候走的js方法
      handleConfirmSome(){
        //默认把当前选中的数据传到子页面
        if(this.gridConfig.selectRows.length === 0){
          this.$Message.success('请勾选数据！')
        }else{
          this.showConfirm = !this.showConfirm
          this.sids=this.gridConfig.selectRows
          this.flag=0
        }
      },
      //确认全部  这时候把当前的查询条件全部传给子页面
      handleConfirmAll(){
        //默认把所有的数据
        this.showConfirm = !this.showConfirm
        this.searchCon = this.$refs.headSearch.searchParam  //拿到查询页面的查询参数传到confirm子页面中
        this.flag=1

      },
      result(e){
        if(e===true){
          //清空search里面值
          // this.$emit('delSearch',true) //页面自动关闭
          this.delSearch=true

        }



      },
      //删除功能
      handleDel(){
//          console.log('走了删除的aa')
        if (this.gridConfig.selectRows.length > 0) {
          this.$Modal.confirm({
            title: '提醒',
            content: '确认删除所选项吗',
            okText: '删除',
            cancelText: '取消',
            onOk: () => {
              const sids = this.gridConfig.selectRows.map(item => {
                return item.sid
              })
              this.$http.delete(csAPI.deep.deepImpRecord.head.delPickUpList.delPickUpList + `/${sids}`).then(() => {
                this.$Message.success('删除成功！')
                this.gridConfig.selectRows = []
                this.getList() //走一下后台的初始化方法
              }).catch(() => {
              }).finally(() => {
              })
            },
            onCancel: () => {
            }
          })
        } else {
          this.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }

      },



      //页面一加载就走的方法走了后台查询所有的方法
      handleSearchSubmit() {
          // console.log('走了请求后台的方法了')
        this.pageParam.page = 1
        this.getList() //走一下后台的初始化方法

      },
      //最终请求后台查询所有数据的js方法
      getList() {
        pageParam.page = this.pageParam.page
        pageParam.limit = this.pageParam.limit
        const data = this.$refs.headSearch.searchParam
        this.$http.post(csAPI.deep.deepImpRecord.head.getPickUpList.selectPickUpList, data, { params: pageParam }).then(res => {
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
          this.getAllNo()
        })
      },
      getAllNo(){
        const data = this.$refs.headSearch.searchParam
        this.$http.post(csAPI.deep.deepImpRecord.head.getAllNo,data).then(res => {
          if(res.data.code === 200){
            this.allPrice = res.data.data
          }
        },()=>{})
      },
      //确认页面关闭
      close(e){
        if(e === false){
          this.showConfirm = e
        }
        this.getList() //走一下后台的初始化方法
      },
      //提取页面自动关闭
      closePickUp(e){
        if(e === false){
          this.showErp = e
        }
        this.getList() //走一下后台的初始化方法
      },
































      //页面点击每一行的查看的时候走的js方法查看时候走的方法，该方法在column.js中定义的方法
      handleView(val){
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.searchData = val
        this.typeNo = 2 //查看
      },
      //点击编辑的时候走的方法
      handleEditOper(){
        this.searchData = this.gridConfig.selectData
//        this.searchData = this.gridConfig.selectRows[0]
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.compData = this.dataList
        this.typeNo = 1//用来标记是新增还是修改，1表示修改操作
        this.gridConfig.selectRows = []
      },


      //页面编辑按钮
      handleEdit(){
        if (this.gridConfig.selectRows.length === 0) {
          this.$Message.warning('请选择您要编辑的数据！')
        } else if (this.gridConfig.selectRows.length > 1) {
          this.$Message.warning('一次仅能编辑一条数据！')
        } else {
          this.searchData = this.gridConfig.selectRows[0]
          this.showHead = !this.showHead
          this.showList = !this.showList
          this.showTabs = !this.showTabs
          this.compData = this.dataList
          this.typeNo = 1//用来标记是新增还是修改，1表示修改操作
          this.gridConfig.selectRows = []
        }
      },
      //点击查询条件时候走的方法
      handleShowSearch() {
        this.showSearch = !this.showSearch
        this.chaxun = !this.chaxun  //展示出查询按钮的显示隐藏
        this.refreshDynamicHeight(120, !this.showSearch ? ['area_search'] : null)
      },

      //这是为当前勾选的行的js方法，当前勾选的所有的数据以数组的形式放在 selectRows  里面，只要遍历这个 selectRows 数组就可以拿出你想要的数据
      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        this.pageParam.page = page
        this.getList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        if(this.pageParam.page === 1) {
          this.getList()
        }
      },

      editBack(val) {
        if (val) {
          this.showHead = !this.showHead
          this.getList()
        }
      }
    },
    computed: {}
  }
</script>
<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
