<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120"
             inline>
      <XdoFormItem prop="orderNo" label="订单号码">
        <XdoIInput type="text" v-model="searchParam.orderNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="goodDesc" label="商品描述">
        <XdoIInput type="text" v-model="searchParam.goodDesc"></XdoIInput>
      </XdoFormItem>

    </XdoForm>
  </section>
</template>

<script>


// import { csOcrCon } from '../../api'


export default {
  name: 'GwOcrInvoiceIBodySearch',
  components: {},
  props: {
    /**
     * 是否需要location 信息   false 否  true 是
     */
    needLocBody:{type:Boolean ,default:false},
    /**
     * 表头 信息
     */
    editConfig: { type: Object, default: () => ({}) }

  },
  data() {
    return {
      // selectBoxData: csOcrCon,
      searchParam:
        {
          orderNo: '', //订单号码
          facGNo: '', //企业料号
          goodDesc: '', //商品描述
          headId:this.editConfig.editData.sid,
          needLoc:this.needLocBody
        }
    }
  },

}
</script>
<style scoped>
</style>













