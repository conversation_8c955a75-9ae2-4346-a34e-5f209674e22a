import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import MyRowEditInput from '../../components/MyRowEditInput'
import MyRowEditSelect from '../../components/MyRowEditSelect'
import MyRowEditInputNumber from '../../components/MyRowEditInputNumber'
import { csOcrApi } from '../../../api'
import { editStatus } from '../../../api/constant'
import { renderGridTitleSort } from '@/common/gridTitleSort/renderGridTitleSort'


// import {csOcrCon}  from '../../../api'
// import { ArrayToLocaleLowerCase } from '@/libs/util'
// import { csAPI } from '@/api'


const columns = {
  mixins: [columnRender],
  components: {
    MyRowEditInput,
    MyRowEditSelect,
    MyRowEditInputNumber
  },
  data() {
    let totalColumnsBase = [


      { title: '序号', width: 150, key: 'serialNo',
        headerComponentFramework: renderGridTitleSort(this)
      },
      {
        title: '订单号码', width: 150, key: 'orderNo',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '订单序号', width: 150, key: 'orderSerialNo',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput',
        // headerComponentFramework: renderGridTitleSort(this)
      },
      {
        title: '企业料号', width: 150, key: 'facGNo',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '商品描述', width: 150, key: 'goodDesc',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '交易数量', width: 150, key: 'qty',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 16,
          precision: 5
        }
      },
      {
        title: '交易单位', width: 150, key: 'unit',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },

      {
        title: '转换后单位', width: 150, key: 'unitConvert',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditSelect',
        cellEditorParams: {
          pCodeKey: 'UNIT'
          //optionData:[]
        },
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },

      {
        title: '法一数量', width: 150, key: 'qty1',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 16,
          precision: 5
        }
      },
      {
        title: '单价', width: 150, key: 'decPrice',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 16,
          precision: 5
        }
      },
      {
        title: '百个单价', width: 150, key: 'decPriceH',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 16,
          precision: 5
        }
      },
      {
        title: '千个单价', width: 150, key: 'decPriceT',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 16,
          precision: 5
        }
      },
      {
        title: '币制', width: 150, key: 'curr',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '转换后币制', width: 150, key: 'currConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }

      },
      {
        title: '总价', width: 150, key: 'decTotal',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 14,
          precision: 4
        }
      },  //申报总价
      {
        title: '净重', width: 150, key: 'netWt',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 14,
          precision: 8
        }
      },
      {
        title: '净重单位', width: 150, key: 'netWtUnit',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '转换后净重单位', width: 150, key: 'netWtUnitConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },
      {
        title: '毛重', width: 150, key: 'grossWt',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 14,
          precision: 5
        }
      },   //总毛重
      {
        title: '毛重单位', width: 150, key: 'grossWtUnit',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '转换后毛重单位', width: 150, key: 'grossWtUnitConvert',

        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },
      {
        title: '原产国', width: 150, key: 'originCountry',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '转换后原产国', width: 150, key: 'originCountryConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }
      },
      {
        title: '件数', width: 150, key: 'packNum',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInputNumber',
        cellEditorParams: {
          intLength: 6,
          precision: 0
        }
      },
      {
        title: '包装方式', width: 150, key: 'wrapType',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      },
      {
        title: '包装编号', width: 150, key: 'wrapNo',
        editable: true,
        onCellValueChanged: this.handUpdateRow,
        cellEditorFramework: 'MyRowEditInput'
      }


      // { title: '业务类型(INVOICE.发票 PACKING.箱单)', width: 150,key: 'bussinessType'},
      // { title: '发票箱单表头的SID', width: 150,key: 'headId'},
      // { title: '创建时间', width: 150,key: 'insertTime'},
      // { title: '创建人', width: 150,key: 'insertUser'},
      // { title: '创建人名称', width: 150,key: 'insertUserName'},
      // { title: '主键', width: 150,key: 'sid'},
      // { title: '任务ID', width: 150,key: 'taskId'},
      // { title: '企业编码', width: 150,key: 'tradeCode'},
      // { title: '修改数据时间', width: 150,key: 'updateTime'},
      // { title: '修改人', width: 150,key: 'updateUser'},
      // { title: '修改人名称', width: 150,key: 'updateUserName'},

    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],
      apiColumns: csOcrApi,
      gridOptions: null,
      pageParam:{
        sort: 'serialNo;asc', //'columnsKey;sortType,columnsKey;sortType'  // 设置默认排序   sortType : asc  || desc
      }

    }
  },
  created() {
    //his.editConfig.editStatus !== editStatus.ADD

    /**
     *  控制 展示的 的时候不允许 行内编辑
     */
    if (this.editConfig.editStatus !== editStatus.EDIT) {
      this.totalColumns = this.totalColumns.map((item
      ) => {
        item.editable = false
        return item
      })
    }
  },

  methods: {
    handleGridReady(params) {
      this.gridOptions = params
    },

    /**
     * 单元格 内容 变化事件  行内修改
     * @param e
     */
    handUpdateRow(e) {
      this.$http.put(`${csOcrApi.gwOcrInvoiceI.body.update}/${e.data.sid}`, e.data, { noIntercept: true })
        .then(res => {
          if (res.data.success) {

           // console.log(res.data.data)

            Object.keys(res.data.data).forEach((key)=>{
             if (e.data.hasOwnProperty(key) && key !=='loc') {
               e.data[key] = res.data.data[key]
             }
            })
            this.$Message.success(res.data.message)
          } else {
            this.$Message.error(res.data.message)
          }
        }, () => {
        }).finally(() => {
        this.gridOptions.api.redrawRows() //重绘
      })
    }
  }
}
export {
  columns
}

