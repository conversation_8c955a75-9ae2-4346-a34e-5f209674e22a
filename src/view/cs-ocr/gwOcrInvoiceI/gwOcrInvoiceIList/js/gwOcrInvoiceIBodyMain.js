import pms from '@/libs/pms'
import { commColumnsCustom } from '@/view/cs-acustomization/common/comm/commColumnsCustom'
import { csOcrApi } from '../../../api'

// import { ArrayToLocaleLowerCase } from '@/libs/util'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data () {
    return {
      p_group: 'body', //按钮的 组名  跟PMS有关
      checkboxSelection:false,
      isShowOp:false,
      isShowSetting:true,
      // 查询条件行数
      searchLines: 1,
      ajaxUrl: {
        // delete: csOcrApi.expenseManager.quotationFast.delete,
        // exportUrl: csOcrApi.gwOcrInvoiceI.head.exportUrl,
        selectAllPaged: csOcrApi.gwOcrInvoiceI.body.selectAllPaged,
        sumData: csOcrApi.gwOcrInvoiceI.body.sumData
      },
      gridConfig: {
        exportTitle: '进口发票箱单表体'
      },
      toolbarEventMap: {
        // 'add': this.handleAdd,           // '新增'
        // 'edit': this.handleEdit,         // '编辑'
        // 'delete': this.handleDelete,     // '删除'
        // 'export': this.handleDownload,   // '导出'
        // 'uploadFile':this.handleUploadFile,  // 上传
      },
    }
  },

}



