<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false" v-show="isShowSearch">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <GwOcrInvoiceIBodySearch :editConfig="editConfig" :needLocBody="needLocBody"
                                     ref="headSearch"></GwOcrInvoiceIBodySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false" v-show="isShowToolBar">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid"
                     :columns="gridConfig.gridColumns"
                     :data="gridConfig.data"
                     :height="dynamicHeight+chayi"
                     :checkboxSelection="checkboxSelection"
                     rowSelection='multiple'
                     @grid-ready="handleGridReady"
                     @cellClicked="handleClickGridCell"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   show-total show-sizer
                   :page-size-opts='pageSizeOpts' @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
            <div style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">
              <span>总数量: </span><span>{{ sumData.qty }}</span>
              <span style="margin-left: 30px">总毛重: </span><span>{{ sumData.grossWt }}</span>
              <span style="margin-left: 30px">总净重:</span><span>{{ sumData.netWt }}</span>
              <span style="margin-left: 30px">总件数:</span><span>{{ sumData.packNum }}</span>
              <span style="margin-left: 30px">总金额:</span><span>{{ sumData.totalAmount }}</span>
            </div>




        </div>
      </XdoCard>
    </div>
    <GwOcrInvoiceIBodyDetail v-if="!showList" @onEditBack="editBack" :editConfig="editConfig">


    </GwOcrInvoiceIBodyDetail>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>
  </section>
</template>
<script>


import { columns } from './js/gwOcrInvoiceIBodyColumns'
import { mainJS } from './js/gwOcrInvoiceIBodyMain'
import GwOcrInvoiceIBodyDetail from './GwOcrInvoiceIBodyDetail'
import GwOcrInvoiceIBodySearch from './GwOcrInvoiceIBodySearch'


export default {
  name: 'GwOcrInvoiceIBodyMain',
  moduleName: '进口发票箱单表体-列表',
  mixins: [columns, mainJS],
  components: {
    GwOcrInvoiceIBodyDetail,
    GwOcrInvoiceIBodySearch
  },
  props: {

    isShowSearch: { type: Boolean, default: true },

    isShowToolBar: { type: Boolean, default: true },
    /**
     * 是否需要location 信息   false 否  true 是
     */
    needLocBody: { type: Boolean, default: false },
    /**
     * 表头 信息
     */
    editConfig: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      //汇总 数据对象
      sumData: {
        grossWt: null,	 //总毛重
        netWt: null,  //净重
        packNum: null,	//件数
        qty: null,	//交易数量
        totalAmount: null	//总金额

      }
    }
  },
  mounted: function() {
  },
  methods: {


    /**
     * 点击查询按钮
     */
    handleSearchSubmit() {
      if (this.getSearchParams().headId) {
        this.pageParam.page = 1
        this.debounce(this.getList, 2000)()

        this.handleGetSum(this.getSearchParams().headId)
      }

    },
    /**
     * 获取表体汇总 数据
     */
    handleGetSum(headId) {
      this.$http.get(`${this.ajaxUrl.sumData}/${headId}`).then((res) => {
        if (res.data.data) {
          this.sumData =   res.data.data
          this.$emit('onGetSumData',this.sumData)
        }
      })
    },

    /**
     *  点击 grid 列 事件
     * @param event
     */
    handleClickGridCell(event) {
      let locData = event.data.loc[event.colDef.field + 'Loc']
      this.$emit('onClickGridCell', locData)

    }
    /**
     * 删除
     */
    // handleDelete() {
    //   this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    // },
    /**
     * 导出
     */
    // handleDownload() {
    //   this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    // },
  },
  computed: {
    chayi() {
      let heightAdd = 0
      if (!this.isShowSearch) {
        heightAdd += 35
      }

      if (!this.isShowToolBar) {
        heightAdd += 35
      }
      if (this.needLocBody) {
        heightAdd -= 385+145
      }
      return heightAdd
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}

.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
