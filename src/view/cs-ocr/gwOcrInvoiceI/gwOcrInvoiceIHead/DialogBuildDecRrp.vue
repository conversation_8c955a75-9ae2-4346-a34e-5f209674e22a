<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="700"
    title="生成预录入单">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px;color: #389DE9;line-height: 1;"></XdoIcon>
    </a>
    <XdoForm class="dc-form dc-form-2 xdo-enter-form" :show-message="false" :model="selectForm" :label-width="110">
      <XdoFormItem label="预录入单内部编号">
        <XdoIInput type="text" v-model="selectForm.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="选择模板">
        <xdo-select v-model="selectForm.templateId" clearable :options="templateNoData" dataValue="KEY"
                    dataLabel="VALUE"
                    :optionLabelRender="(item) =>  item.VALUE"></xdo-select>
      </XdoFormItem>

      <XdoFormItem class="dc-merge-1-3" style="text-align: right">
        <XdoButton type="success" icon="ios-cloud-upload" @click="handleConfirm" :loading="loadingTrue">确定
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>

    <div style="padding-top: 3px">
      <Table :row-class-name="rowClassName" :columns="tableColumns" :data="tableData" border></Table>
    </div>

  </XdoModal>
</template>


<script>
import { csOcrApi } from '../../api'

export default {
  name: 'DialogBuildDecRrp', //
  props: {
    show: { type: Boolean, required: true },

    emsListNo: { type: String },
    sidsDialog: { type: Array }

  },
  data() {
    return {
      tableColumns: [

        {
          title: '栏位类型', key: 'colType',
          render: (h, params) => {
            if (params.row.colType === 'head') {
              return h('span', '表头')
            } else if (params.row.colType === 'list') {
              return h('span', '表体')
            } else {
              return h('span', params.row.colType)
            }
          }//栏位类型 ： ：
        },
        { title: '栏位名称', key: 'colName' },
        { title: '发票', key: 'fpValue' },
        { title: '箱单', key: 'xdValue' },
        {
          title: '差异', key: 'diffValue'
          // render: (h, params) => {
          //   if (params.row.diffMark) {
          //     return h('span', { style: { color: 'red' }},params.row.diffValue)
          //   } else {
          //     return h('span', params.row.diffValue)
          //   }
          // }// 差异

        }
      ],
      tableData: [],


      loadingTrue: false,
      selectForm: {
        emsListNo: this.emsListNo, //
        templateId: '' //

      },
      templateNoData: '',  // 模板编号 下拉源
      ajaxUrl: {
        decErpHeadTemplate: csOcrApi.commApi.decErpHeadTemplate,
        getSumInfo: csOcrApi.gwOcrInvoiceI.head.getSumInfo

      }
    }
  },
  created() {
    this.$http.post(`${this.ajaxUrl.decErpHeadTemplate}/I`, {}).then((res) => {
      this.templateNoData = res.data.data
    })
  },
  methods: {

    rowClassName(row) {


      if (row.diffMark) {
        return 'demo-table-error-row'
      }
      return ''


    },

    /**
     * 获取 汇总 列表信息
     */
    handleGetSumInfo() {
      this.tableData = []
      this.$http.post(`${this.ajaxUrl.getSumInfo}/${this.sidsDialog}`).then((res) => {
        this.tableData = res.data.data
      })
    },

    /**
     * 确定
     */
    handleConfirm() {
      this.$emit('onConfirm', this.selectForm)
      this.loadingTrue = true
    },
    /**
     * 关闭
     */
    handleClose() {
      this.$emit('update:show', false)
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.selectForm.emsListNo = this.emsListNo
        this.handleGetSumInfo()
      }
    }
  }


}
</script>

<style scoped>

/deep/ .ivu-table .demo-table-error-row td {
  background-color: #ed4014;
  color: #fff;
}


</style>

