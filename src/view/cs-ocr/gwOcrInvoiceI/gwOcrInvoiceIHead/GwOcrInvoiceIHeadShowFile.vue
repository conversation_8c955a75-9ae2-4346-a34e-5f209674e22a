<template>

  <div>

    <div style="display: flex; justify-content: space-between; line-height: 30px; height: 30px">
      <div>

          <Icon type="ios-book-outline" size="18"  > </Icon>

      

           <span  style="font-size: 13px; margin: 0 5px">识别结果</span>
      </div>
      <div style="padding-right: 5px">
        <a href="#"  @click="editBack" >
          <XdoIcon type="ios-undo" size="22" style="color: green;"/>
        </a>
      </div>
    </div>
    <ShowFile :needLocBody="this.needLocBody" :editConfig="this.editConfig"></ShowFile>
  </div>
</template>

<script>
import ShowFile from '../components/ShowFile'

export default {
  name: 'GwOcrInvoiceIHeadShowFile',
  components: {
    ShowFile
  },
  props: {
    /**
     * 是否需要location 信息   false 否  true 是
     */
    needLocBody: { type: Boolean, default: false },
    /**
     * 表头 信息
     */
    editConfig: { type: Object, default: () => ({}) }
  },

  methods: {
    editBack() {
      this.$emit('onEditBack', { isShowFile: false })
    }
  }
}
</script>

<style scoped>

</style>
