<template>
  <section class="dc-edit" v-focus>
    <XdoCard class="xdo-card" :bordered="false">
      <div class="xdo-card-body">
        <XdoForm ref="formItem" class="dc-form" :model="formItem" label-position="right" :label-width="100"
                 :rules="ruleValidate" inline>

          <XdoFormItem @click.native="handlePreview('invoiceNo')" label="发票号码" prop="invoiceNo"
                       xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.invoiceNo" :disabled="showDisable" xid="d_invoiceNo"></XdoIInput>
          </XdoFormItem>


          <XdoFormItem prop="invoiceDate" label="发票日期" xid="c_head_edit_form_item"
                       @click.native="handlePreview('invoiceDate')">
            <XdoDatePicker type="date" format="yyyy-MM-dd" placeholder=""
                           :value="formItem.invoiceDate ? formItem.invoiceDate.slice(0, 10) : formItem.invoiceDate"
                           @on-change="formItem.invoiceDate=($event === '' ? $event : ($event + ' 23:59:59'))" transfer
                           :disabled="showDisable" xid="d_invoiceDate"></XdoDatePicker>
          </XdoFormItem>

          <XdoFormItem prop="packingNo" label="装箱单号码" xid="c_head_edit_form_item"
                       @click.native="handlePreview('packingNo')">
            <XdoIInput type="text" v-model="formItem.packingNo" :disabled="showDisable" xid="d_packingNo"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="referenceNo" label="关联号码" xid="c_head_edit_form_item"
                       @click.native="handlePreview('referenceNo')">
            <XdoIInput type="text" v-model="formItem.referenceNo" :disabled="showDisable"
                       xid="d_referenceNo"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="shipper" label="发货人" xid="c_head_edit_form_item" @click.native="handlePreview('shipper')">
            <XdoIInput type="text" v-model="formItem.shipper" :disabled="showDisable" xid="d_shipper"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="shipperCode" label="境外发货人代码" xid="c_head_edit_form_item"
                       @click.native="handlePreview('shipperCode')">
            <XdoIInput type="text" v-model="formItem.shipperCode" :disabled="showDisable"
                       xid="d_shipperCode"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="consignee" label="收货人" xid="c_head_edit_form_item"
                       @click.native="handlePreview('consignee')">
            <XdoIInput type="text" v-model="formItem.consignee" :disabled="showDisable" xid="d_consignee"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="tradeTerms" label="贸易条款" xid="c_head_edit_form_item"
                       @click.native="handlePreview('tradeTerms')">
            <xdo-select v-model="formItem.tradeTerms" :options="this.csOcrConData.tradeTerms"
                        :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="qty" label="总数量" xid="c_head_edit_form_item" @click.native="handlePreview('qty')"
                       :class="{'classDiff':this.qtyDiff}">
            <xdo-input v-model.trim="formItem.qty" placeholder="" decimal int-length="16" precision="5"
                       :class="{'classDiffInput':this.qtyDiff}"
                       :disabled="showDisable" xid="d_qty"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="unit" label="总数量单位" xid="c_head_edit_form_item" @click.native="handlePreview('unit')">
            <XdoIInput type="text" v-model="formItem.unit" :disabled="showDisable"
                       @on-enter="handleEnterGetMap('UNIT','unit','unitConvert')"
                       xid="d_unit"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="unitConvert" label="转换后单位" xid="c_head_edit_form_item"
                       @click.native="handlePreview('unitConvert')">
            <xdo-select v-model="formItem.unitConvert"
                        :disabled="true"
                        :asyncOptions="pcodeList"
                        meta="UNIT"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="totalAmount" label="总金额" xid="c_head_edit_form_item"
                       @click.native="handlePreview('totalAmount')" :class="{'classDiff':this.totalAmountDiff}">
            <xdo-input v-model.trim="formItem.totalAmount" placeholder="" decimal int-length="19" precision="5"
                       :class="{'classDiffInput':this.totalAmountDiff}"
                       style="color: red"
                       :disabled="showDisable" xid="d_totalAmount"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制" xid="c_head_edit_form_item" @click.native="handlePreview('curr')">
            <XdoIInput type="text" v-model="formItem.curr" :disabled="showDisable"
                       @on-enter="handleEnterGetMap('CURR','curr','currConvert')"
                       xid="d_curr"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="currConvert" label="转换后币制" xid="c_head_edit_form_item"
                       @click.native="handlePreview('currConvert')">
            <xdo-select v-model="formItem.currConvert"
                        :disabled="true"
                        :asyncOptions="pcodeList"
                        meta="CURR_OUTDATED"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="originCountry" label="统一原产国" xid="c_head_edit_form_item"
                       @click.native="handlePreview('originCountry')">
            <XdoIInput type="text" v-model="formItem.originCountry" :disabled="showDisable"
                       @on-enter="handleEnterGetMap('COUNTRY','originCountry','originCountryConvert')"
                       xid="d_originCountry"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="originCountryConvert" label="转换后原产国" xid="c_head_edit_form_item"
                       @click.native="handlePreview('originCountryConvert')">
            <xdo-select v-model="formItem.originCountryConvert"
                        :disabled="true"
                        :asyncOptions="pcodeList"
                        meta="COUNTRY_OUTDATED"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="orderNo" label="统一订单号" xid="c_head_edit_form_item"
                       @click.native="handlePreview('orderNo')">
            <XdoIInput type="text" v-model="formItem.orderNo" :disabled="showDisable" xid="d_orderNo"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="palletNum" label="托盘数(件数)" xid="c_head_edit_form_item"
                       @click.native="handlePreview('palletNum')">
            <xdo-input v-model.trim="formItem.palletNum" placeholder="" number int-length="5" :disabled="showDisable"
                       xid="d_palletNum"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="bulkCtns" label="散箱数" xid="c_head_edit_form_item"
                       @click.native="handlePreview('bulkCtns')">
            <xdo-input v-model.trim="formItem.bulkCtns" placeholder="" number int-length="5" :disabled="showDisable"
                       xid="d_bulkCtns"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="totalCtns" label="总箱数" xid="c_head_edit_form_item"
                       @click.native="handlePreview('totalCtns')">
            <xdo-input v-model.trim="formItem.totalCtns" placeholder="" number int-length="5" :disabled="showDisable"
                       xid="d_totalCtns"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="packNum" label="总件数" xid="c_head_edit_form_item" @click.native="handlePreview('packNum')"
                       :class="{'classDiff':this.packNumDiff}">
            <xdo-input v-model.trim="formItem.packNum" placeholder="" number int-length="5" :disabled="showDisable"
                       :class="{'classDiffInput':this.packNumDiff}"
                       xid="d_packNum"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="netWt" label="总净重" xid="c_head_edit_form_item" @click.native="handlePreview('netWt')"
                       :class="{'classDiff':this.netWtDiff}">
            <xdo-input v-model.trim="formItem.netWt" placeholder="" decimal int-length="19" precision="5"
                       :class="{'classDiffInput':this.netWtDiff}"
                       :disabled="showDisable" xid="d_netWt"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="netWtUnit" label="净重单位" xid="c_head_edit_form_item"
                       @click.native="handlePreview('netWtUnit')">
            <XdoIInput type="text" v-model="formItem.netWtUnit" :disabled="showDisable"
                       @on-enter="handleEnterGetMap('UNIT','netWtUnit','netWtUnitConvert')"
                       xid="d_netWtUnit"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="netWtUnitConvert" label="转换后净重单位" xid="c_head_edit_form_item"
                       @click.native="handlePreview('netWtUnitConvert')">
            <xdo-select v-model="formItem.netWtUnitConvert"
                        :disabled="true"
                        :asyncOptions="pcodeList"
                        meta="UNIT"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="grossWt" label="总毛重" xid="c_head_edit_form_item" @click.native="handlePreview('grossWt')"
                       :class="{'classDiff':this.grossWtDiff}">
            <xdo-input v-model.trim="formItem.grossWt" placeholder="" decimal int-length="19" precision="5"
                       :class="{'classDiffInput':this.grossWtDiff}"
                       :disabled="showDisable" xid="d_grossWt"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="grossWtUnit" label="毛重单位" xid="c_head_edit_form_item"
                       @click.native="handlePreview('grossWtUnit')">
            <XdoIInput type="text" v-model="formItem.grossWtUnit" :disabled="showDisable"
                       @on-enter="handleEnterGetMap('UNIT','grossWtUnit','grossWtUnitConvert')"
                       xid="d_grossWtUnit"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="grossWtUnitConvert" label="转换后毛重单位" xid="c_head_edit_form_item"
                       @click.native="handlePreview('grossWtUnitConvert')">
            <xdo-select v-model="formItem.grossWtUnitConvert"
                        :disabled="true"
                        :asyncOptions="pcodeList"
                        meta="UNIT"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="volume" label="总体积" xid="c_head_edit_form_item" @click.native="handlePreview('volume')">
            <xdo-input v-model.trim="formItem.volume" placeholder="" decimal int-length="19" precision="5"
                       :disabled="showDisable" xid="d_volume"></xdo-input>
          </XdoFormItem>


          <!--          2021年12月7日 新增字段-->
          <XdoFormItem prop="mawb" label="主提运单号"  @click.native="handlePreview('mawb')">
            <XdoIInput type="text" v-model="formItem.mawb"
                       :disabled="showDisable"/>
          </XdoFormItem>





          <XdoFormItem prop="hawb" label="分提运单号"  @click.native="handlePreview('hawb')">
            <XdoIInput type="text" v-model="formItem.hawb"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="forwarder" label="货代"  @click.native="handlePreview('forwarder')">
            <XdoIInput type="text" v-model="formItem.forwarder"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="depport" label="启运港"  @click.native="handlePreview('depport')">
            <XdoIInput type="text" v-model="formItem.depport"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="desport" label="目的港"  @click.native="handlePreview('desport')">
            <XdoIInput type="text" v-model="formItem.desport"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="billDate" label="提单日期"  @click.native="handlePreview('billDate')">
            <XdoIInput type="text" v-model="formItem.billDate"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="fltNo" label="航班号"  @click.native="handlePreview('fltNo')">
            <XdoIInput type="text" v-model="formItem.fltNo"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="fltDate" label="航班日期"  @click.native="handlePreview('fltDate')">
            <XdoIInput type="text" v-model="formItem.fltDate"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="fltNo1st" label="头程航班号"  @click.native="handlePreview('fltNo1st')">
            <XdoIInput type="text" v-model="formItem.fltNo1st"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="fltDate1st" label="头程航班日期"  @click.native="handlePreview('fltDate1st')">
            <XdoIInput type="text" v-model="formItem.fltDate1st"
                       :disabled="showDisable"/>
          </XdoFormItem>

          <XdoFormItem prop="freight" label="运费"  @click.native="handlePreview('freight')">

            <xdo-input v-model.trim="formItem.freight" placeholder="" decimal int-length="15" precision="5"
                       :disabled="showDisable" ></xdo-input>


          </XdoFormItem>

          <XdoFormItem prop="totalChwt" label="计费重量"  @click.native="handlePreview('totalChwt')">


            <xdo-input v-model.trim="formItem.totalChwt" placeholder="" decimal int-length="6" precision="2"
                       :disabled="showDisable" ></xdo-input>


          </XdoFormItem>



          <!--          <XdoFormItem prop="createStatus" label="生成状态(1已识别 2已生成)" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.createStatus" :disabled="showDisable"-->
          <!--                       xid="d_createStatus"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="modifyStatus" label="修改状态（1未修改；2已修改）" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.modifyStatus" :disabled="showDisable"-->
          <!--                       xid="d_modifyStatus"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="scanTime" label="扫描时间(暂时写入插入时间)" xid="c_head_edit_form_item">-->
          <!--            <XdoDatePicker type="date" format="yyyy-MM-dd" placeholder=""-->
          <!--                           :value="formItem.scanTime ? formItem.scanTime.slice(0, 10) : formItem.scanTime"-->
          <!--                           @on-change="formItem.scanTime=($event === '' ? $event : ($event + ' 23:59:59'))" transfer-->
          <!--                           :disabled="showDisable" xid="d_scanTime"></XdoDatePicker>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="headId" label="生成到预录入单后返回的表头ID" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.headId" :disabled="showDisable" xid="d_headId"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="tradeCode" label="企业编码" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.tradeCode" :disabled="showDisable" xid="d_tradeCode"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="insertUserName" label="创建人名称" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.insertUserName" :disabled="showDisable"-->
          <!--                       xid="d_insertUserName"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="updateUserName" label="修改人名称" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.updateUserName" :disabled="showDisable"-->
          <!--                       xid="d_updateUserName"></XdoIInput>-->
          <!--          </XdoFormItem>-->
          <!--          <XdoFormItem prop="dataType" label="数据类型(INVOICE.发票 PACKING.箱单)" xid="c_head_edit_form_item">-->
          <!--            <XdoIInput type="text" v-model="formItem.dataType" :disabled="showDisable" xid="d_dataType"></XdoIInput>-->
          <!--          </XdoFormItem>-->
        </XdoForm>
      </div>
    </XdoCard>
    <div v-if="isShowBtns" class="xdo-enter-action dc-edit-actions">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed === true" :type="item.type" :disabled="item.disabled"
                   :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>
<script>
import { csOcrApi, csOcrCon } from '../../api'
import { editStatus } from '../../api/constant'
// import { decimalKeypress, decimalKeyup, numberKeypress, numberKeyup } from '@/libs/num'

export default {
  name: 'GwOcrInvoiceIHeadDetail',
  props: {
    /**
     * 是否需要location 信息   false 否  true 是
     */
    needLocBody: { type: Boolean, default: false },
    /**
     * 表头 信息
     */
    editConfig: { type: Object, default: () => ({}) },

    /**
     * 表体 汇总 数据
     */
    sumData: {
      type: Object, default: () => (null
        //   {
        //   // grossWt: null,	 //总毛重
        //   // netWt: null,  //净重
        //   // packNum: null,	//件数
        //   // qty: null,	//交易数量
        //   // totalAmount: null	//总金额
        // }
      )
    }
  },
  data() {
    return {
      isShowBtns: true,
      csOcrConData: csOcrCon,
      formItem: {
        sid: '',
        invoiceNo: '',
        invoiceDate: '',
        packingNo: '',
        referenceNo: '',
        shipper: '',
        shipperCode: '',
        consignee: '',
        tradeTerms: '',
        qty: '',
        unit: '',
        unitConvert: '',
        totalAmount: '',
        curr: '',
        currConvert: '',
        originCountry: '',
        originCountryConvert: '',
        orderNo: '',
        palletNum: '',
        bulkCtns: '',
        totalCtns: '',
        packNum: '',
        netWt: '',
        netWtUnit: '',
        netWtUnitConvert: '',
        grossWt: '',
        grossWtUnit: '',
        grossWtUnitConvert: '',
        volume: '',
        createStatus: '',
        modifyStatus: '',
        scanTime: '',
        headId: '',
        tradeCode: '',
        insertUserName: '',
        updateUserName: '',
        dataType: '',

        mawb:'',
        hawb:'',
        forwarder:'',
        depport:'',
        desport:'',
        billDate:'',
        fltNo:'',
        fltDate:'',
        fltNo1st:'',
        fltDate1st:'',
        freight:'',
        totalChwt:'',


      },
      ruleValidate: {
        invoiceNo: [
          { required: false, message: '发票号码不能为空！', trigger: 'blur' },
          { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        ],
        mawb: [
          { required: false, message: '主提运单不能为空！', trigger: 'blur' },
          { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        ],

        // invoiceDate: [
        //   { max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // packingNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // referenceNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // shipper: [
        //   { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // shipperCode: [
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // consignee: [
        //   { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // tradeTerms: [
        //   { max: 5, message: '长度不能超过5位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // qty: [],
        // unit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // unitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // totalAmount: [],
        // curr: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // currConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // originCountry: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // originCountryConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // orderNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // palletNum: [],
        // bulkCtns: [],
        // totalCtns: [],
        // packNum: [],
        // netWt: [],
        // netWtUnit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // netWtUnitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // grossWt: [],
        // grossWtUnit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // grossWtUnitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // volume: [],
        // createStatus: [
        //   { required: true, message: '生成状态(1已识别 2已生成)不能为空！', trigger: 'blur' },
        //   { max: 1, message: '长度不能超过1位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // modifyStatus: [
        //   { required: true, message: '修改状态（1未修改；2已修改）不能为空！', trigger: 'blur' },
        //   { max: 1, message: '长度不能超过1位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // scanTime: [
        //   { max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // headId: [
        //   { max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // tradeCode: [
        //   { required: true, message: '企业编码不能为空！', trigger: 'blur' },
        //   { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // insertUserName: [
        //   { required: true, message: '创建人名称不能为空！', trigger: 'blur' },
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // updateUserName: [
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // dataType: [
        //   { required: true, message: '数据类型(INVOICE.发票 PACKING.箱单)不能为空！', trigger: 'blur' },
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ]
      },
      buttons: [
        {
          code: 'Save',
          type: 'primary',
          disabled: false,
          click: this.handleSave,
          label: '保存',
          loading: false,
          needed: true
        }
        // {
        //   code: 'Cancel',
        //   type: 'primary',
        //   disabled: false,
        //   click: this.handleBack,
        //   label: '返回',
        //   loading: false,
        //   needed: true
        // }
      ],
      sid: '',
      showDisable: false

    }
  },
  created: function() {
    if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {

      // 设置校验规则
      let dataType = this.editConfig.editData.dataType;
      if (dataType === 'INVOICE' || dataType === "PACKING") {
        this.ruleValidate.invoiceNo[0].required = true;
        this.ruleValidate.mawb[0].required = false;
      } else if (dataType === "LADING") {
        this.ruleValidate.invoiceNo[0].required = false;
        this.ruleValidate.mawb[0].required = true;
      } else {
        this.ruleValidate.invoiceNo[0].required = false;
        this.ruleValidate.mawb[0].required = false;
      }

    }
  },
  mounted: function() {
    // if (this.editConfig && this.editConfig.editStatus === editStatus.ADD) {
    //   this.resetFormData()
    //   this.showDisable = false
    //   this.buttons.filter(item => item.code === 'Save')[0].needed = true
    //   return
    // }
    if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {
      // 获取LOC信息
      this.formItem = { ...this.editConfig.editData }
      this.showDisable = false
      this.sid = this.editConfig.editData.sid
      this.buttons.filter(item => item.code === 'Save')[0].needed = true
      this.$http.get(csOcrApi.gwOcrInvoiceI.headLoc.getOne + `/${this.editConfig.editData.sid}`).then(res => {
        this.formItem = { ...this.editConfig.editData, ...res.data.data }

        if  (this.formItem.dataType==='INVOICE' || this.formItem.dataType === "PACKING"){
          this.handlePreview('invoiceNo')
        }
        if (this.formItem.dataType === "LADING") { //提单
          this.handlePreview('mawb')
        }



      })
      return
    }
    // if (this.editConfig && this.editConfig.editStatus === editStatus.SHOW) {
    //   this.formItem = { ...this.editConfig.editData }
    //   this.showDisable = true
    //   this.buttons.filter(item => item.code === 'Save')[0].needed = false
    //   return
    // }
    // 文件查看
    if (this.editConfig && this.editConfig.editStatus === editStatus.SHOWFILE) {
      // 获取LOC信息
      this.$http.get(csOcrApi.gwOcrInvoiceI.headLoc.getOne + `/${this.editConfig.editData.sid}`).then(res => {
        this.formItem = { ...this.editConfig.editData, ...res.data.data }
        if  (this.formItem.dataType==='INVOICE' || this.formItem.dataType === "PACKING"){
          this.handlePreview('invoiceNo')
        }
        if (this.formItem.dataType === "LADING") { //提单
          this.handlePreview('mawb')
        }
      })
      this.showDisable = true
      // this.buttons.filter(item => item.code === 'Save')[0].needed = false
      // this.buttons.filter(item => item.code === 'Cancel')[0].needed = false
      this.isShowBtns = false
      return
    }


  },
  methods: {

    //申报计量单位  UNIT币制 CURR , 国别 COUNTRY
    handleEnterGetMap(paramsType,columnsName,columnsNameChange,){
      if (!this.formItem[columnsName]){
        this.formItem[columnsNameChange] = ''
        return
      }

      this.handleMapInfo(paramsType, this.formItem[columnsName]).then((res)=>{
           this.formItem[columnsNameChange] = res.data.data
      })
    },

    handleMapInfo(paramsType, value) {
      return   this.$http.get(`${csOcrApi.commApi.getConvertedValue}/${paramsType}/${value}`)
    },
    /**
     * 根据 loc信息 定位图片
     * @param A
     */
    handlePreview(A) {
      let locationsData = this.formItem[A + 'Loc']
      this.$emit('onPreviewPdf', locationsData)

    },

    resetFormData() {
      this.$refs['formItem'].resetFields()
    },
    handleSave() {
      this.$refs['formItem'].validate().then(isValid => {
        if (isValid) {
          let http = ''
          this.buttons.filter(item => item.code === 'Save')[0].loading = true
          if (this.sid) {
            http = this.$http.put(`${csOcrApi.gwOcrInvoiceI.head.update}/${this.sid}`, this.formItem, { noIntercept: true })
          } else {
            http = this.$http.post(csOcrApi.gwOcrInvoiceI.head.insert, this.formItem, { noIntercept: true })
          }
          http.then(res => {
            if (res.data.success) {
              this.$Message.success(res.data.message)
              this.formItem = { ...this.formItem, ...res.data.data }
              // this.handleBack()
            } else {
              this.$Message.error(res.data.message)
            }
          }, () => {
          }).finally(() => {
            this.buttons.filter(item => item.code === 'Save')[0].loading = false
          })
        }
      })
    },
    handleBack() {
      this.$emit('onEditBack', { showList: true })
    }

  },
  computed: {

    grossWtDiff() {


      return this.sumData && !(this.sumData.grossWt == this.formItem.grossWt)
    },	 //总毛重
    netWtDiff() {
      return this.sumData && !(this.sumData.netWt == this.formItem.netWt)
    },  //净重
    packNumDiff() {
      return this.sumData && !(this.sumData.packNum == this.formItem.packNum)
    },	//件数
    qtyDiff() {
      return this.sumData && !(this.sumData.qty == this.formItem.qty)
    },	//交易数量
    totalAmountDiff() {
      return this.sumData && !(this.sumData.totalAmount == this.formItem.totalAmount)
    }	//总金额

  }


}
</script>
<style lang="less" scoped>
/deep/ .classDiff {
  label {
    color: red;
  }
}

/deep/ .classDiffInput {
  input {
    color: red;
  }
}


</style>
