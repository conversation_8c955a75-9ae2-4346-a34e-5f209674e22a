import pms from '@/libs/pms'
import { commColumnsCustom } from '@/view/cs-acustomization/common/comm/commColumnsCustom'
import { csOcrApi } from '../../../api'
import { editStatus } from '../../../api/constant'

// import { ArrayToLocaleLowerCase } from '@/libs/util'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      isShowBuild: false,

      isShowSetting: true,

      // 查询条件行数
      searchLines: 3,
      ajaxUrl: {
        delete: csOcrApi.gwOcrInvoiceI.head.delete,
        exportUrl: csOcrApi.gwOcrInvoiceI.head.exportUrl,
        selectAllPaged: csOcrApi.gwOcrInvoiceI.head.selectAllPaged,
        uploadFile: csOcrApi.gwOcrInvoiceI.head.uploadFile,
        buildDecRrp: csOcrApi.gwOcrInvoiceI.head.buildDecRrp
      },
      gridConfig: {
        exportTitle: '进口资料'
      },
      toolbarEventMap: {
        // 'add': this.handleAdd,           // '新增'
        'edit': this.handleEditFileBtn,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload,   // '导出'
        'uploadFile': this.handleUploadFile,  // 上传
        'buildDecRrp': this.handleBuildDecRrp  //生成预录入单
      },
      needLocBody: false, //是否需要location 信息


      isShowFile: false,
      isShowUpLoad: false,

      //传给 生成弹出页面的 选中的  sids
      sidsDialog: []

    }
  },
  mounted: function() {
  },
  methods: {
    /**
     * 生成预录入单
     */
    handleBuildDecRrp() {
      if (this.checkRowSelected('生成')) {
        let checkData = this.gridConfig.selectRows.filter(x => x.createStatus === '2')  // //(1已识别 2已生成)
        if (checkData.length > 0) {
          this.$Message.warning('已生成的数据不能重复生成')
          return
        }



        //选中数据的 内部编号 唯一性校验
        let emsListNos = this.gridConfig.selectRows.map((item) => {
          return item.emsListNo
        }).filter(i=> i)

        let a = Array.from(new Set(emsListNos))
        if (a.length > 1) {
          this.$Message.warning(`存在不同单据内部编号的发票/箱单，不允许生成！(${a})`)
          return
        }

        //用于 弹出页面 获取汇总信息需要
        this.sidsDialog = this.gridConfig.selectRows.map((item) => {
          return item.sid
        })

        this.isShowBuild = true
      }
    },
    /**
     * 生成预录入单 确认
     *  selectForm: {
        emsListNo:'', //
        templateId: '', //
      },
     */
    handleBuildDecRrpConfirm(selectForm) {
      let sids = this.gridConfig.selectRows.map((item) => {
        return item.sid
      })
      selectForm.sids = sids
      this.$http.post(`${this.ajaxUrl.buildDecRrp}`, selectForm).then(() => {
        this.$refs.ref_DialogBuildDecRrp.loadingTrue = false
        this.$Message.success('生成成功!')
        this.isShowBuild = false
        this.handleSearchSubmit()
        console.log(this.$refs.ref_DialogBuildDecRrp.loadingTrue)
      }).catch(() => {
        this.$refs.ref_DialogBuildDecRrp.loadingTrue = false
      }).finally(
        this.$refs.ref_DialogBuildDecRrp.loadingTrue = false
      )
    },

    /**
     * 删除
     */
    handleDelete() {
      this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * GwOcrInvoiceIHeadUpload 组件的返回按钮 事件
     */
    handleUploadBack() {
      this.showMe('showList')
      this.handleSearchSubmit()
    },
    /**
     * ShowFile 组件的 返回按钮 回传事件
     * @param data    { isShowFile: false }
     */
    editBackShowFile() {
      this.showMe('showList')
      this.handleSearchSubmit()

    },
    /**
     * 文件查看
     * @param row
     */
    handleShowFile(row) {
      this.needLocBody = true
      this.editConfig.editStatus = editStatus.SHOWFILE //文件查看
      this.editConfig.editData = row
      this.showMe('isShowFile')
    },

    /**
     * 工具栏 按钮 修改
     */
    handleEditFileBtn(){

      if (this.checkRowSelected('编辑', true)) {
        this.handleEditFile(this.gridConfig.selectRows[0])
      }

    },
    /**
     * 行内修改
     * @param row
     */
    handleEditFile(row) {

      //(1已识别 2已生成)
      if  (row.createStatus ==='2')
      {
        this.$Message.warning('已生成的数据不允许修改!')
        return
      }

      this.needLocBody = true
      this.editConfig.editStatus = editStatus.EDIT //
      this.editConfig.editData = row
      this.showMe('isShowFile')
    },


    /**
     * 上传
     */
    handleUploadFile() {
      this.showMe('isShowUpLoad')

    },

    /**
     * 显示 隐藏的 对应关系
     * @param key
     */
    showMe(key) {

      if (key === 'showList') {
        this.showList = true
        this.isShowFile = false
        this.isShowUpLoad = false
      }
      if (key === 'isShowFile') {
        this.showList = false
        this.isShowFile = true
        this.isShowUpLoad = false
      }
      if (key === 'isShowUpLoad') {
        this.showList = false
        this.isShowFile = false
        this.isShowUpLoad = true
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      //去除 展示 操作列
      this.gridConfig.exportColumns = this.gridConfig.exportColumns.filter(x => x.key !== 'showFile')
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}



