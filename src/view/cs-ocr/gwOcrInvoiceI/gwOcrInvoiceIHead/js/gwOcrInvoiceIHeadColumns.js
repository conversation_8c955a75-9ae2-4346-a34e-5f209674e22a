import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
// import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'
import { csOcrCon } from '../../../api'
import  {renderGridTitleSort} from '@/common/gridTitleSort/renderGridTitleSort'



const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [

      //  发票号码 发票日期 识别时间 修改时间

      // {
      //   title: '文件查看',
      //   width: 100,
      //   align: 'center',
      //   key: 'showFile',
      //   cellRendererFramework: operationRenderer(this, [{ title: '展示', handle: 'handleShowFile', marginRight: '0' }])
      // },
      {
        title: '文件类型', width: 150, key: 'dataType', //(INVOICE.发票 PACKING.箱单, INV_PACK:发票箱单)
        render: (h, params) => {
          return this.cmbShowRender(h, params, csOcrCon.dataType)
        },

      },
      {
        title: '状态', width: 150, key: 'createStatus', //(1已识别 2已生成)
        render: (h, params) => {
          return this.cmbShowRender(h, params, csOcrCon.createStatus)
        },

      },
      {
        title: '修改状态', width: 150, key: 'modifyStatus',    //（1未修改；2已修改）
        render: (h, params) => {
          return this.cmbShowRender(h, params, csOcrCon.modifyStatus)
        },

      },

      { title: '发票号码', width: 150, key: 'invoiceNo',
        headerComponentFramework: renderGridTitleSort(this)
      },
      { title: '发票日期', width: 150, key: 'invoiceDate' ,
        headerComponentFramework: renderGridTitleSort(this)
      },
      { title: '装箱单号码', width: 150, key: 'packingNo' },
      { title: '关联号码', width: 150, key: 'referenceNo' },
      { title: '发货人', width: 150, key: 'shipper' },
      { title: '境外发货人代码', width: 150, key: 'shipperCode' },
      { title: '收货人', width: 150, key: 'consignee' },
      {
        title: '贸易条款', width: 150, key: 'tradeTerms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, csOcrCon.tradeTerms)
        }
      },

      { title: '总数量', width: 150, key: 'qty' },
      { title: '总数量单位', width: 150, key: 'unit' },
      {
        title: '转换后单位', width: 150, key: 'unitConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },


      { title: '总金额', width: 150, key: 'totalAmount' },
      { title: '币制', width: 150, key: 'curr' },
      {
        title: '转换后币制', width: 150, key: 'currConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }
      },
      { title: '统一原产国', width: 150, key: 'originCountry' },
      {
        title: '转换后原产国', width: 150, key: 'originCountryConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }

      },
      { title: '统一订单号', width: 150, key: 'orderNo' },
      { title: '托盘数(件数)', width: 150, key: 'palletNum' },
      { title: '散箱数', width: 150, key: 'bulkCtns' },
      { title: '总箱数', width: 150, key: 'totalCtns' },
      { title: '总件数', width: 150, key: 'packNum' },
      { title: '总净重', width: 150, key: 'netWt' },
      { title: '净重单位', width: 150, key: 'netWtUnit' },
      {
        title: '转换后净重单位', width: 150, key: 'netWtUnitConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },
      { title: '总毛重', width: 150, key: 'grossWt' },
      { title: '毛重单位', width: 150, key: 'grossWtUnit' },
      {
        title: '转换后毛重单位', width: 150, key: 'grossWtUnitConvert',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'UNIT')
        }
      },
      { title: '总体积', width: 150, key: 'volume' },

      { title: '制单员', width: 150, key: 'insertUserName' },
      { title: '识别时间', width: 150, key: 'scanTime',
        headerComponentFramework: renderGridTitleSort(this)
      },  //暂时写入插入时间
      { title: '修改时间', width: 150, key: 'updateTime',
        headerComponentFramework: renderGridTitleSort(this)
      },

      { title: '单据内部编号', width: 150, key: 'emsListNo',
        headerComponentFramework: renderGridTitleSort(this)
      },
      { title: '保完税标志', width: 150, key: 'bondMark',
        render:(h, params)=>{
          return this.cmbShowRender(h, params, csOcrCon.bondMark)
        },

      },
      { title: '监管方式', width: 150, key: 'tradeMode',
        render:(h, params)=>{
          return this.cmbShowRender(h, params, [],'TRADE')
        }
      },

      { title: '备案号', width: 150, key: 'emsNo' },




      // 2021年12月7日 新增字段

      { title: '主提运单号', width: 130, key: 'mawb' },
      { title: '分提运单号', width: 130, key: 'hawb' },
      { title: '货代', width: 130, key: 'forwarder' },
      { title: '启运港', width: 130, key: 'depport' },
      { title: '目的港', width: 130, key: 'desport' },
      { title: '提单日期', width: 130, key: 'billDate' },
      { title: '航班号', width: 130, key: 'fltNo' },
      { title: '航班日期', width: 130, key: 'fltDate' },
      { title: '头程航班号', width: 130, key: 'fltNo1st' },
      { title: '头程航班日期', width: 130, key: 'fltDate1st' },
      { title: '运费', width: 130, key: 'freight' },
      { title: '计费重量', width: 130, key: 'totalChwt' }




      //
      // { title: '生成到预录入单后返回的表头ID', width: 150, key: 'headId' },
      // { title: '创建时间', width: 150, key: 'insertTime' },
      // { title: '创建人', width: 150, key: 'insertUser' },
      // { title: '主键', width: 150, key: 'sid' },
      // { title: '任务ID', width: 150, key: 'taskId' },
      // { title: '企业编码', width: 150, key: 'tradeCode' },
      // { title: '修改人', width: 150, key: 'updateUser' },
      // { title: '修改人名称', width: 150, key: 'updateUserName' },

    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],

      // pageParam:{
      //   sort: '', //'columnsKey;sortType,columnsKey;sortType'  // 设置默认排序   sortType : asc  || desc
      // }
    }
  },
  created() {
    /**
     * 获取货代和报关行
     */
  },




}
export {
  columns
}

