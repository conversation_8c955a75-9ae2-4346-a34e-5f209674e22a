<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <GwOcrInvoiceIHeadSearch ref="headSearch"></GwOcrInvoiceIHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid"
                     :columns="gridConfig.gridColumns"
                     :data="gridConfig.data"
                     :height="dynamicHeight"
                     :checkboxSelection="checkboxSelection"
                     rowSelection='multiple'
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   show-total show-sizer
                   :page-size-opts='pageSizeOpts' @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <GwOcrInvoiceIHeadShowFile v-if="isShowFile" @onEditBack="editBackShowFile"

                               :editConfig="editConfig"
                               :needLocBody="needLocBody"></GwOcrInvoiceIHeadShowFile>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>


    <GwOcrInvoiceIHeadUpload v-if="isShowUpLoad" @onBack="handleUploadBack"></GwOcrInvoiceIHeadUpload>


    <DialogBuildDecRrp ref="ref_DialogBuildDecRrp" :show.sync="isShowBuild"
                       @onConfirm="handleBuildDecRrpConfirm"
                       :emsListNo="DialogBuildDecRrp"
                       :sidsDialog="this.sidsDialog"/>


  </section>
</template>
<script>


import { columns } from './js/gwOcrInvoiceIHeadColumns'
import { mainJS } from './js/gwOcrInvoiceIHeadMain'
import GwOcrInvoiceIHeadSearch from './GwOcrInvoiceIHeadSearch'
import GwOcrInvoiceIHeadShowFile from './GwOcrInvoiceIHeadShowFile'
import GwOcrInvoiceIHeadUpload from './GwOcrInvoiceIHeadUpload'

import DialogBuildDecRrp from './DialogBuildDecRrp'
import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'


export default {
  name: 'GwOcrInvoiceIHeadMain',
  moduleName: '进口资料-列表',
  components: {
    GwOcrInvoiceIHeadSearch,
    // GwOcrInvoiceIHeadDetail,
    GwOcrInvoiceIHeadShowFile,
    GwOcrInvoiceIHeadUpload,
    DialogBuildDecRrp
  },
  mixins: [columns, mainJS],
  data() {
    return {}
  },
  mounted: function() {
  },
  computed:{
    DialogBuildDecRrp(){

      if (this.gridConfig.selectRows.filter(i => i.emsListNo).length) {
        return this.gridConfig.selectRows.filter(i => i.emsListNo)[0].emsListNo
      } else {
        return ''
      }
    }
  },


  methods: {
    /**
     * 保存列表设置  覆盖
     * @param columns
     */
    handleUpdateColumn(columns) {
      let edit = this.actions.filter(item => {
        return item.command === 'edit'
      })
      //判断是否存在修改按钮  若存在 就增加 查看修改列
      let bascol = []
      if (Array.isArray(edit) && edit.length > 0) {
        bascol = [{
          title: '操作',
          fixed: 'left',
          width: 130,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this,
            [
              { title: '编辑', handle: 'handleEditFile' },
              { title: '展示', handle: 'handleShowFile', marginRight: '0' }
            ])
        }]
      } else {
        bascol = this.isShowOp ? [{
          title: '操作',
          fixed: 'left',
          width: 90,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this, [{ title: '展示', handle: 'handleShowFile', marginRight: '0' }])
        }] : []
      }
      this.gridConfig.gridColumns = [...bascol, ...columns]
      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    }

  }
}
</script>

<style lang="less" scoped>
.ivu-form-item {
  margin-bottom: 5px;
}

.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
