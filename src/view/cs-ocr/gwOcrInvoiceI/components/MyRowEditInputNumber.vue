<template>
  <div>


    <xdo-input v-if="precision===0"  v-model="value" ref="input" number :int-length="intLength" :precision="precision"
               notConvertNumber
               clearable></xdo-input>
    <xdo-input v-else=""  v-model="value" ref="input" decimal :int-length="intLength" :precision="precision"
               notConvertNumber
               clearable></xdo-input>

  </div>
</template>

<script>
export default {
  name: 'MyRowEditInputNumber',
  data() {
    return {
      value: null,
      intLength: 16,
      precision: 5
    }
  },
  methods: {
    /* Component Editor Lifecycle methods */
    // the final value to send to the grid, on completion of editing
    /*组件编辑器生命周期方法*/
    //编辑完成后发送到网格的最终值
    getValue() {
      return this.value
    },
    // Gets called once before editing starts, to give editor a chance to
    // cancel the editing before it even starts.
    //在编辑开始之前调用一次，给编辑器一个机会
    //取消编辑甚至在它开始之前。
    isCancelBeforeStart() {

      return false
    },

    // Gets called once when editing is finished (eg if Enter is pressed).
    // If you return true, then the result of the edit will be ignored.
    //当编辑完成时调用一次(例如如果按下Enter键)。
    //如果你返回true，那么编辑的结果将被忽略。
    isCancelAfterEnd() {
      //获取 当前列的 key
      // console.log(this.params.colDef.field)
      //this.params.data.unitConvert = '035'


      return false  // this.value > 1000
    }
  },
  mounted() {

    this.intLength = this.intLength && this.params.intLength
    if (this.precision || this.precision ===0 ){
      this.precision = this.precision && this.params.precision
    }

    this.value = this.params.value
    this.$nextTick(
      () => this.$refs.input.focus()
    )
  }

}
</script>

<style scoped>

</style>
