<template>
  <div>
    <XdoIInput type="text" v-model="value" ref="input" style="width: 100%" />
  </div>
</template>

<script>
export default {
  name: 'MyRowEditInput',
  data() {
    return {
      value: null,
    }
  },
  methods: {
    /* Component Editor Lifecycle methods */
    // the final value to send to the grid, on completion of editing
    /*组件编辑器生命周期方法*/
    //编辑完成后发送到网格的最终值
    getValue() {
      return this.value
    },
    // Gets called once before editing starts, to give editor a chance to
    // cancel the editing before it even starts.
    //在编辑开始之前调用一次，给编辑器一个机会
    //取消编辑甚至在它开始之前。
    isCancelBeforeStart() {

      return false
    },

    // Gets called once when editing is finished (eg if Enter is pressed).
    // If you return true, then the result of the edit will be ignored.
    //当编辑完成时调用一次(例如如果按下Enter键)。
    //如果你返回true，那么编辑的结果将被忽略。
    isCancelAfterEnd() {
      //获取 当前列的 key
      // console.log(this.params.colDef.field)
      //this.params.data.unitConvert = '035'


      return false  // this.value > 1000
    }
  },
  mounted() {
    this.value = this.params.value
    this.$nextTick(
      () => this.$refs.input.focus()
    )
  }

}
</script>

<style scoped>

</style>
