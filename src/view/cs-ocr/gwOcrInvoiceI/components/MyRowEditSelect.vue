<template>
  <div>
    <!--/*  <XdoIInput type="text" v-model="value" ref="input" style="width: 100%" />*/-->

    <!--  <xdo-select  transfer v-model="value"  ref="input"   :asyncOptions="pcodeList" :meta="pcode.unit"-->
    <!--              :optionLabelRender="pcodeRender"></xdo-select>-->
    <Select v-model="value" ref="aaa" style="width:100%" filterable transfer clearable>
      <Option v-for="item in optionData" :value="item.value" :key="item.value">{{
          `${item.value} ${item.label}`
        }}
      </Option>
    </Select>


  </div>
</template>

<script>
export default {
  name: 'MyRowEditSelect',
  data() {
    return {
      value: null,
      optionData: []
    }
  },
  methods: {


    /* Component Editor Lifecycle methods */
    // the final value to send to the grid, on completion of editing
    getValue() {
      // this simple editor doubles any value entered into the input
      return this.value
    },

    // Gets called once before editing starts, to give editor a chance to
    // cancel the editing before it even starts.
    isCancelBeforeStart() {
      return false
    },

    // Gets called once when editing is finished (eg if En<PERSON> is pressed).
    // If you return true, then the result of the edit will be ignored.
    isCancelAfterEnd() {
      // our editor will reject any value greater than 1000

      //获取 当前列的 key
      // console.log(this.params.colDef.field)

      // this.params.data.unitConvert = '035'
      return false  // this.value > 1000
    }
  },
  mounted() {

    //this.params.pCodeKey
    //this.params.selectData
    // console.log(this.params.pcodeKey)
    //
    //


    if (this.params.optionData){
      this.optionData = this.params.optionData
    }
    if (this.params.pCodeKey){
      this.pcodeList(this.params.pCodeKey).then((res) => {
        this.optionData = res
      })
    }

    this.value = this.params.value


    // this.$nextTick(
    //   () => this.$refs.aaa.focus()
    // )
  }

}
</script>

<style scoped>

</style>
