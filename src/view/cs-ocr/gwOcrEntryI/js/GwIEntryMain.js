import pms from '@/libs/pms'
// import { csApiKnorr } from '@/view/cs-knorr/common/comm'
import { commColumnsCustom } from '@/view/cs-knorr/common/comm'
// import { operationRenderer } from '@/view/cs-knorr/common/comm/operation_renderer'
import { dynamicImport } from '@/view/cs-knorr/common/dynamic-import/dynamicImport'
import { csOcrApi } from '@/view/cs-ocr/api'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  data() {
    return {
      // 查询条件行数
      searchLines: 6,
      // 审核相关
      sid: '',
      diffData: {},
      approvalStatus: '',
      isShowDailogAudit: false,
      gridConfig: {
        exportTitle: '进口报关草单'
      },
      toolbarEventMap: {
        'uploadFile': this.handleUpload,
        'delete': this.handleDelete,
        'export': this.handleExport
      },
      ajaxUrl: {
        delete: csOcrApi.entry.head.delete,
        export: csOcrApi.entry.head.export,
        selectAllPaged: csOcrApi.entry.head.selectAllPaged
      },
      isShowUpLoad: false,
    }
  },
  methods: {
    /*
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {

      this.gridConfig.gridColumns = []
      this.$nextTick(function () {
        this.gridConfig.gridColumns = [...columns]
      })
      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.tableloading = true
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
          me.tableloading = false
        })
      })
    },
    handleUpload() {
      this.showList = false
      this.isShowUpLoad = true
    },
    handleUploadBack() {
      this.showList = true
      this.isShowUpLoad = false
      this.handleSearchSubmit()
    },
    /**
     * 删除
     */
    handleDelete() {
      this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleExport() {
      this.doExport(this.ajaxUrl.export, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
