<template>
  <div>
    <xdo-ag-grid ref="ref_agGrid"

                 :columns="gridConfig.gridColumns"
                 :data="gridConfig.data"
                 :checkboxSelection="true"
                 :height="dynamicHeight"
                 rowSelection='single'
                 @selectionChanged="handleSelectionChanged">
    </xdo-ag-grid>
  </div>
</template>
<script>
import { csOcrApi } from '../../api'

export default {
  name: 'HeadMainByTaskId',
  props: {
    taskId: { type: String }
  },
  data() {
    return {
      ajaxUrl: {
        selectAllPaged: csOcrApi.gwOcrInvoiceI.head.selectAllPaged
      },
      gridConfig: {
        data: [],
        gridColumns: [
          { title: '业务编号', width: 180, key: 'businessNo' }
        ]
      },
      // 分页相关
      pageParam: {
        page: 1,
        limit: 1000,
        dataTotal: -1
      },

    }
  },
  mounted() {

  },
  methods: {
    /**
     *
     */
    handleGetListByTaskId(params) {
      this.$http.post(this.ajaxUrl.selectAllPaged, params, {
        params: {
          ...this.pageParam
        }
      }).then(res => {
        this.gridConfig.data = res.data.data
        if (this.gridConfig.data && this.gridConfig.data.length>0){
          this.$emit('onSelectRow', this.gridConfig.data[0])
        }
      }).catch(() => {
      }).finally(() => {
      })
    },
    /**
     * 选中行事件
     * @param params 选中的数据
     */
    handleSelectionChanged(params) {
      let selectRow = params.api.getSelectedRows()
      this.$emit('onSelectRow', selectRow[0])

    }
  },
  watch: {
    taskId: function(val) {
      if (this.taskId) {
        this.handleGetListByTaskId({ taskId: val })

      }
    }
  },
  computed: {
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight () {
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 40

      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight



      return hiddenHeight
    }
  }
}
</script>

<style scoped>

</style>
