<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam"
             label-position="right" :label-width="120"  inline>
      <XdoFormItem prop="ddRiqi" label="开始日期">


      <XdoDatePicker type="date"  placeholder="请选择日期" v-model="searchParam.ddRiqiFrom"/>

      </XdoFormItem>

      <XdoFormItem prop="ddRiqi" label="结束日期">


        <XdoDatePicker type="date"  placeholder="请选择日期" v-model="searchParam.ddRiqiTo"/>

      </XdoFormItem>


      <!--      <XdoFormItem prop="createStatus" label="状态">-->
<!--        <xdo-select v-model="searchParam.createStatus"-->
<!--                    :options="this.selectBoxData.createStatus"-->
<!--                    :optionLabelRender="pcodeRender">-->
<!--        </xdo-select>-->
<!--      </XdoFormItem>-->

<!--      <XdoFormItem prop="dataType" label="文件类型">-->
<!--      <xdo-select v-model="searchParam.dataType"-->
<!--                  :options="this.selectBoxData.dataType"-->
<!--                  :optionLabelRender="pcodeRender"></xdo-select>-->
<!--    </XdoFormItem>-->
<!--      <XdoFormItem prop="invoiceNo" label="发票号码">-->
<!--        <XdoIInput type="text" v-model="searchParam.invoiceNo"-->
<!--        ></XdoIInput>-->
<!--      </XdoFormItem>-->
<!--      <XdoFormItem prop="packingNo" label="装箱单号码">-->
<!--        <XdoIInput type="text" v-model="searchParam.packingNo"-->
<!--        ></XdoIInput>-->
<!--      </XdoFormItem>-->
<!--      <XdoFormItem prop="shipper" label="发货人">-->
<!--        <XdoIInput type="text" v-model="searchParam.shipper"-->
<!--        ></XdoIInput>-->
<!--      </XdoFormItem>-->
<!--      <DcDateRange label="发票日期" @onDateRangeChanged="handleDateChange"></DcDateRange>-->
<!--      <XdoFormItem prop="emsListNo" label="单据内部编号">-->
<!--        <XdoIInput type="text" v-model="searchParam.emsListNo"-->
<!--        ></XdoIInput>-->
<!--      </XdoFormItem>-->
    </XdoForm>
  </section>
</template>

<script>


import { csOcrCon } from '../../api'
// import DcDateRange from '@/components/dc-date-range/dc-date-range'

export default {
  name: 'TestDemoHeadSearch',
  components: {
    // DcDateRange
  },
  data() {
    return {
      selectBoxData: csOcrCon,
      searchParam:
        {
          createStatus: '', //状态
          dataType: '', //文件类型
          invoiceNo: '', //发票号码
          packingNo: '', //装箱单号码
          shipper: '', //发货人
          invoiceDateFrom: '', //发票日期
          invoiceDateTo: '',
          emsListNo: '', //单据内部编号

          ddRiqiFrom:'', //日期
          ddRiqiTo:''
        }
    }
  },
  created() {

  },
  methods: {

    handleDateChange(values) {
      this.searchParam.invoiceDateFrom = values[0]
      this.searchParam.invoiceDateTo = values[1]
    }
  }
}
</script>
<style scoped>
</style>













