<template>
  <section>
    <div v-show='showList'>
      <XdoCard :bordered='false'>
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton type='primary' class='dc-margin-right' @click='handleSearchSubmit'>查询</XdoButton>
            <XdoButton type='warning' class='dc-margin-right' @click='handleShowSearch'>查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show='showSearch'>
          <div class='separateLine'></div>
          <!--查询条件-->
          <TestDemoHeadSearch ref='headSearch'></TestDemoHeadSearch>
        </div>
      </XdoCard>

      <XdoCard :bordered='false'>
        <div class='action' ref='area_actions'>
          <!--工具栏-->
          <xdo-toolbar @click='handleToolbarClick' :action-source='actions'></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered='false'>
        <!--        gird-->
        <xdo-ag-grid ref='ref_agGrid' :checkboxSelection='checkboxSelection'
                     rowSelection='multiple'
                     :columns='gridConfig.gridColumns'
                     :data='gridConfig.data'
                     :height='dynamicHeight'
                     :GridReady='handleGridReady'
                     @selectionChanged='handleSelectionChanged'>
        </xdo-ag-grid>
        <div ref='area_page'>
          <!--分页-->
          <XdoPage class='dc-page' show-total show-sizer
                   :page-size-opts='pageSizeOpts'
                   :current='pageParam.page'
                   :page-size='pageParam.limit'
                   :total='pageParam.dataTotal'
                   @on-change='pageChange'
                   @on-page-size-change='pageSizeChange'/>
        </div>
      </XdoCard>
    </div>
    <!--详细信息-->
<!--    <TestDemoHeadDetail v-if="!showList" @onEditBack="handleCommonEditBack" :editConfig="editConfig"/>-->
    <!--    详细页面 Tab-->
        <TestDemoTab v-if="!showList" @onEditBack="handleCommonEditBack" :parent-edit-config="editConfig"/>

    <!--自定义配置列-->
    <TableColumnSetup v-model='showTableColumnSetup'
                      :resId='tableId'
                      @updateColumns='handleUpdateColumn'
                      :columns='totalColumns'
                      class='height:500px'/>
    <!--    导入组件-->
    <ImportPage :importKey="importConfig.config.taskCode"
                :importShow.sync="importConfig.show"
                :importConfig="importConfig.config"
                @onImportSuccess="handleAfterImport"></ImportPage>

    <!--    弹出框-->

    <DialogTest :show.sync="isShowDialogTest"  @onConfirm="handleDialogTestConfirm"> </DialogTest>


  </section>
</template>

<script>


import { commonMain } from '@/view/cs-acustomization/common/comm/commonMain'
// import { editStatus } from ''

import { columns } from './js/testDemoHeadColumns'
import TestDemoHeadSearch from './TestDemoHeadSearch'
import { csOcrApi } from '../../api'
// import TestDemoHeadDetail from './TestDemoHeadDetail'



import ImportPage from 'xdo-import'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import DialogTest from './DialogTest'

import  TestDemoTab from './TestDemoTab'


export default {
  name: 'TestDemoHeadMain',
  moduleName: '基础demo-列表',
  components: {
    TestDemoHeadSearch,
    // TestDemoHeadDetail,
    TestDemoTab,
    ImportPage,
    DialogTest

    // TestDemoTab
  },
  mixins: [columns, commonMain, dynamicImport],
  data() {
    return {
      isShowDialogTest:false,

      gridConfig: {
        exportTitle: '基础demo'
      },
      isShowSetting: true,
      searchLines: 3,
      importConfig: {
        show: false,
        config: this.getCommImportConfig('TEST_DEMO')
      },
      toolbarEventMap: {
        'add': this.handleCommonAdd, //新增
        'edit': this.handleCommonEdit,//编辑
        'delete': this.handleDelete,//删除
        'import': this.handleImport,// 导入
        'export': this.handleExport//导出
      },
      ajaxUrl: {
        delete: csOcrApi.testDemo.head.delete,
        export: csOcrApi.testDemo.head.export,
        list: csOcrApi.testDemo.head.list
        // selectBySid: ''
      }
    }
  },
  methods: {

    /**
     * 弹出框确定事件
     * @param selectData  弹出框 回传的数据
     */
    handleDialogTestConfirm(selectData){
      console.log(selectData)
    },

    /**
     * 行内 点击发票号 事件
     *  * params.value 当前字段的值
     * params.data  当前行的值
     */
    handleShowInvoiceNo(params) {
      console.log(params.value)
      console.log(params.data)
      this.isShowDialogTest =true

    },

    /**
     * 自定义 事件
     */
    handleZiDingYiShiJian() {
      this.handleCommonSetButtonLoading('xxx', true)
      this.$http.post(this.ajaxUrl.extract, {}).then(() => {
        this.$Message.success('成功！')
        // this.pageParam.page = 1
        // this.getList()

        this.handleSearchSubmit()  //此处有防漏处理  1秒
      }).catch(() => {
      }).finally(() => {
        this.handleCommonSetButtonLoading('xxx', false)
      })
    },

    /**
     * 导入
     */
    handleImport() {
      this.importConfig.show = true
    },

    /**
     *导入完成后事件
     */
    handleAfterImport() {
      // do some
    },

    /**
     * 删除
     */
    handleDelete() {
      this.handleCommonDelete(this.ajaxUrl.delete, 'delete')
    },

    /**
     * 导出 按钮 的 事件
     */
    handleExport() {
      this.handleCommonExport(this.ajaxUrl.export, 'export')
    }

  }
}
</script>

<style lang='less' scoped>
/**
 点击 查询条件按钮 的时候显示的 虚线
 */
.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
