<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="Tab" @on-click="tabClick">
    <TabPane name="head" label="表头" tab="Tab">
      <TestDemoHeadDetail v-if="tabs.head" @onEditBack="handleEditBack" :editConfig="parentEditConfig"/>
    </TabPane>
    <TabPane name="body" label="表体" tab="Tab">
      <div v-if="tabs.body">表体</div>
    </TabPane>

    <template v-slot:extra>
      <XdoButton type="text" @click="handleBackToList()">
         <span>
        写所需要的内容
      </span>
        <XdoIcon type="ios-undo" size="22" style="color: green;"/>

      </XdoButton>
    </template>
  </XdoTabs>
</template>

<script>


import { editStatus } from '../../api/constant'
import  TestDemoHeadDetail  from './TestDemoHeadDetail'




export default {
  name: 'TestDemoTab',  //
  moduleName: '测试Demo',
  components: {
    TestDemoHeadDetail

  },
  props: {
    parentEditConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'head',
      tabs: {
        head: true,
        body: false
      }
    }
  },
  methods: {

    /**
     * 表头 编辑页面 返回
     */
    handleEditBack(backObj){
      this.$emit('onEditBack', backObj)
    },
    /**
     * 当前页面
     * 返回 列表页面
     */
    handleBackToList() {
      let backObj = {
        showList: true,
        editStatus: editStatus.SHOW,
        editData: {}
      }
      this.$emit('onEditBack', backObj)
    },

    /**
     * 切换 tab 的时候发生
     * 切换的 时候 刷新 当前tab  的组件
     * @param tableName
     */
    tabClick(tableName) {
      for (let key in this.tabs) {
        this.tabs[key] = false
      }
      this.tabs[tableName] = true

    }
  }
}
</script>
