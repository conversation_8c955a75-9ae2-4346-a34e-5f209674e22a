import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
// import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'
// import { csOcrCon } from '../../../api'
// import { operationRendererOwner } from '@/view/cs-common/agGridJs/operation_renderer'
// import  {renderGridTitleSort} from '@/common/gridTitleSort/renderGridTitleSort'
// 使用  headerComponentFramework: renderGridTitleSort(this)



const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [


      { title:'日期',key: 'ddRiqi'},
      {title:'时间',key:'ddR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'},
      { title: '小数',key:'xiaoshu'},
      { title: '整数',key:'zhengshu'},
      {title:'字符串',key:'zifu'},
      {title:'10位编码', key:'tradeCode'},
      {title:'remark1',key: 'remark1'},
      {title:'remark2',key: 'remark2'},
      {title:'remark3',key: 'remark3'},
      {title:'remark4',key: 'remark4'},

      // {
      //   title: '文件类型', width: 150, key: 'dataType', //(INVOICE.发票 PACKING.箱单, INV_PACK:发票箱单)
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, csOcrCon.dataType)
      //   },
      // },
      // {
      //   title: '状态', width: 150, key: 'createStatus', //(1已识别 2已生成)
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, csOcrCon.createStatus)
      //   },
      // },
      // {
      //   title: '修改状态', width: 150, key: 'modifyStatus',    //（1未修改；2已修改）
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, csOcrCon.modifyStatus)
      //   },
      // },
      //
      // // { title: '发票号码', width: 150, key: 'invoiceNo',
      // //   // headerComponentFramework: renderGridTitleSort(this)
      // // },
      // {
      //   title: '发票号码',
      //   fixed: 'left',
      //   width: 90,
      //   align: 'center',
      //   key: 'invoiceNo',
      //   cellRendererFramework: operationRendererOwner(this, [{ title: '', handle: 'handleShowInvoiceNo', marginRight: '0' }])
      // },


      // { title: '发票日期', width: 150, key: 'invoiceDate' ,
      //   // headerComponentFramework: renderGridTitleSort(this)
      // },
      // { title: '装箱单号码', width: 150, key: 'packingNo' },
      // { title: '关联号码', width: 150, key: 'referenceNo' },
      // { title: '发货人', width: 150, key: 'shipper' },
      // { title: '境外发货人代码', width: 150, key: 'shipperCode' },
      // { title: '收货人', width: 150, key: 'consignee' },
      // {
      //   title: '贸易条款', width: 150, key: 'tradeTerms',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, csOcrCon.tradeTerms)
      //   }
      // },
      //
      // { title: '总数量', width: 150, key: 'qty' },
      // { title: '总数量单位', width: 150, key: 'unit' },
      // {
      //   title: '转换后单位', width: 150, key: 'unitConvert',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, [], 'UNIT')
      //   }
      // },
      //
      //
      // { title: '总金额', width: 150, key: 'totalAmount' },
      // { title: '币制', width: 150, key: 'curr' },
      // {
      //   title: '转换后币制', width: 150, key: 'currConvert',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
      //   }
      // },
      // { title: '统一原产国', width: 150, key: 'originCountry' },
      // {
      //   title: '转换后原产国', width: 150, key: 'originCountryConvert',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
      //   }
      //
      // },
      // { title: '统一订单号', width: 150, key: 'orderNo' },
      // { title: '托盘数(件数)', width: 150, key: 'palletNum' },
      // { title: '散箱数', width: 150, key: 'bulkCtns' },
      // { title: '总箱数', width: 150, key: 'totalCtns' },
      // { title: '总件数', width: 150, key: 'packNum' },
      // { title: '总净重', width: 150, key: 'netWt' },
      // { title: '净重单位', width: 150, key: 'netWtUnit' },
      // {
      //   title: '转换后净重单位', width: 150, key: 'netWtUnitConvert',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, [], 'UNIT')
      //   }
      // },
      // { title: '总毛重', width: 150, key: 'grossWt' },
      // { title: '毛重单位', width: 150, key: 'grossWtUnit' },
      // {
      //   title: '转换后毛重单位', width: 150, key: 'grossWtUnitConvert',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, [], 'UNIT')
      //   }
      // },
      // { title: '总体积', width: 150, key: 'volume' },
      //
      // { title: '制单员', width: 150, key: 'insertUserName' },
      // { title: '识别时间', width: 150, key: 'scanTime',
      //   // headerComponentFramework: renderGridTitleSort(this)
      // },  //暂时写入插入时间
      // { title: '修改时间', width: 150, key: 'updateTime',
      //   // headerComponentFramework: renderGridTitleSort(this)
      // },
      //
      // { title: '单据内部编号', width: 150, key: 'emsListNo',
      //   // headerComponentFramework: renderGridTitleSort(this)
      // },
      // { title: '保完税标志', width: 150, key: 'bondMark',
      //   render:(h, params)=>{
      //     return this.cmbShowRender(h, params, csOcrCon.bondMark)
      //   },
      //
      // },
      // { title: '监管方式', width: 150, key: 'tradeMode',
      //   render:(h, params)=>{
      //     return this.cmbShowRender(h, params, [],'TRADE')
      //   }
      // },
      //
      // { title: '备案号', width: 150, key: 'emsNo' },



    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],

      // pageParam:{
      //   sort: '', //'columnsKey;sortType,columnsKey;sortType'  // 设置默认排序   sortType : asc  || desc
      // }
    }
  },
  created() {
    /**
     * 获取货代和报关行
     */
  },




}
export {
  columns
}

