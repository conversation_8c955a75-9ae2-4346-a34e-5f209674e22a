<template>
  <section class="dc-edit" v-focus>
    <XdoCard class="xdo-card" :bordered="false">
      <div class="xdo-card-body">

        <div title="废弃">
<!--          <XdoForm ref="formItem" class="dc-form" :model="formItem" label-position="right" :label-width="120"-->
<!--                   :rules="ruleValidate" inline>-->
<!--            <XdoFormItem prop="invoiceNo" label="发票号码" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.invoiceNo" :disabled="showDisable" xid="d_invoiceNo"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="invoiceDate" label="发票日期" xid="c_head_edit_form_item">-->
<!--              <XdoDatePicker type="date" format="yyyy-MM-dd" placeholder=""-->
<!--                             :value="formItem.invoiceDate ? formItem.invoiceDate.slice(0, 10) : formItem.invoiceDate"-->
<!--                             @on-change="formItem.invoiceDate=($event === '' ? $event : ($event + ' 23:59:59'))"-->
<!--                             transfer :disabled="showDisable" xid="d_invoiceDate"></XdoDatePicker>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="packingNo" label="装箱单号码111" xid="c_head_edit_form_item">-->
<!--              <xdo-select v-model="formItem.packingNo"-->
<!--                          :disabled="showDisable"-->
<!--                          :options="this.comboxData.packingNo"-->
<!--                          meta="UNIT"-->
<!--                          :optionLabelRender="pcodeRender"></xdo-select>-->


<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="referenceNo" label="关联号码111">-->
<!--              <XdoIInput type="text" v-model="formItem.referenceNo" :disabled="showDisable" :maxlength="3"></XdoIInput>-->
<!--            </XdoFormItem>-->


<!--            <XdoFormItem prop="shipper" label="发货人" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.shipper" :disabled="showDisable" xid="d_shipper"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="shipperCode" label="境外发货人代码" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.shipperCode" :disabled="showDisable"-->
<!--                         xid="d_shipperCode"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="consignee" label="收货人" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.consignee" :disabled="showDisable" xid="d_consignee"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="tradeTerms" label="贸易条款" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.tradeTerms" :disabled="showDisable"-->
<!--                         xid="d_tradeTerms"></XdoIInput>-->
<!--            </XdoFormItem>-->


<!--            <XdoFormItem prop="qty" label="总数量111">-->
<!--              <xdo-input v-model.trim="formItem.qty" placeholder="" decimal notConvertNumber int-length="10"-->
<!--                         precision="5"-->
<!--                         :disabled="showDisable"-->
<!--                         :clearable="!showDisable"></xdo-input>-->
<!--            </XdoFormItem>-->


<!--            <XdoFormItem prop="unit" label="总数量单位" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.unit" :disabled="showDisable" xid="d_unit"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="unitConvert" label="转换后单位111" xid="c_head_edit_form_item">-->

<!--              <xdo-select v-model="formItem.unitConvert"-->
<!--                          :disabled="showDisable"-->
<!--                          :asyncOptions="pcodeList"-->
<!--                          meta="UNIT"-->
<!--                          :optionLabelRender="pcodeRender"></xdo-select>-->


<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="totalAmount" label="总金额" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.totalAmount" placeholder="" decimal int-length="8" precision="5"-->
<!--                         :disabled="showDisable" xid="d_totalAmount"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="curr" label="币制" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.curr" :disabled="showDisable" xid="d_curr"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="currConvert" label="转换后币制" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.currConvert" :disabled="showDisable"-->
<!--                         xid="d_currConvert"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="originCountry" label="统一原产国" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.originCountry" :disabled="showDisable"-->
<!--                         xid="d_originCountry"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="originCountryConvert" label="转换后原产国" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.originCountryConvert" :disabled="showDisable"-->
<!--                         xid="d_originCountryConvert"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="orderNo" label="统一订单号" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.orderNo" :disabled="showDisable" xid="d_orderNo"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="palletNum" label="托盘数(件数)" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.palletNum" placeholder="" number int-length="5" :disabled="showDisable"-->
<!--                         xid="d_palletNum"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="bulkCtns" label="散箱数" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.bulkCtns" placeholder="" number int-length="5" :disabled="showDisable"-->
<!--                         xid="d_bulkCtns"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="totalCtns" label="总箱数" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.totalCtns" placeholder="" number int-length="5" :disabled="showDisable"-->
<!--                         xid="d_totalCtns"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="packNum" label="总件数" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.packNum" placeholder="" number int-length="5" :disabled="showDisable"-->
<!--                         xid="d_packNum"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="netWt" label="总净重" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.netWt" placeholder="" decimal int-length="13" precision="5"-->
<!--                         :disabled="showDisable" xid="d_netWt"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="netWtUnit" label="净重单位" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.netWtUnit" :disabled="showDisable" xid="d_netWtUnit"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="netWtUnitConvert" label="转换后净重单位" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.netWtUnitConvert" :disabled="showDisable"-->
<!--                         xid="d_netWtUnitConvert"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="grossWt" label="总毛重" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.grossWt" placeholder="" decimal int-length="13" precision="5"-->
<!--                         :disabled="showDisable" xid="d_grossWt"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="grossWtUnit" label="毛重单位" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.grossWtUnit" :disabled="showDisable"-->
<!--                         xid="d_grossWtUnit"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="grossWtUnitConvert" label="转换后毛重单位" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.grossWtUnitConvert" :disabled="showDisable"-->
<!--                         xid="d_grossWtUnitConvert"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="volume" label="总体积" xid="c_head_edit_form_item">-->
<!--              <xdo-input v-model.trim="formItem.volume" placeholder="" decimal int-length="13" precision="5"-->
<!--                         :disabled="showDisable" xid="d_volume"></xdo-input>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="createStatus" label="生成状态(1已识别 2已生成)" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.createStatus" :disabled="showDisable"-->
<!--                         xid="d_createStatus"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="modifyStatus" label="修改状态（1未修改；2已修改）" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.modifyStatus" :disabled="showDisable"-->
<!--                         xid="d_modifyStatus"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="scanTime" label="扫描时间(暂时写入插入时间)" xid="c_head_edit_form_item">-->
<!--              <XdoDatePicker type="date" format="yyyy-MM-dd" placeholder=""-->
<!--                             :value="formItem.scanTime ? formItem.scanTime.slice(0, 10) : formItem.scanTime"-->
<!--                             @on-change="formItem.scanTime=($event === '' ? $event : ($event + ' 23:59:59'))" transfer-->
<!--                             :disabled="showDisable" xid="d_scanTime"></XdoDatePicker>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="headId" label="生成到预录入单后返回的表头ID" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.headId" :disabled="showDisable" xid="d_headId"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="tradeCode" label="企业编码" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.tradeCode" :disabled="showDisable" xid="d_tradeCode"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="insertUserName" label="创建人名称" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.insertUserName" :disabled="showDisable"-->
<!--                         xid="d_insertUserName"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="updateUserName" label="修改人名称" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.updateUserName" :disabled="showDisable"-->
<!--                         xid="d_updateUserName"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--            <XdoFormItem prop="dataType" label="数据类型(INVOICE.发票 PACKING.箱单)" xid="c_head_edit_form_item">-->
<!--              <XdoIInput type="text" v-model="formItem.dataType" :disabled="showDisable" xid="d_dataType"></XdoIInput>-->
<!--            </XdoFormItem>-->
<!--          </XdoForm>-->
        </div>

        <XdoForm ref="formItem" class="dc-form" :model="formItem" label-position="right" :label-width="120" :rules="ruleValidate" inline>
          <XdoFormItem prop="zifu" label="字符" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.zifu" :disabled="showDisable" xid="d_zifu" :maxlength="20" ></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="zhengshu" label="整数" xid="c_head_edit_form_item">
            <xdo-input v-model.trim="formItem.zhengshu" placeholder="" number int-length="19" :disabled="showDisable" xid="d_zhengshu"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="ddRiqiShijian" label="日期时间" xid="c_head_edit_form_item">
            <XdoDatePicker type="date" format="yyyy-MM-dd"  :disabled="showDisable"
                           placeholder="" :value="formItem.ddRiqiShijian ? formItem.ddRiqiShijian.slice(0, 10) : formItem.ddRiqiShijian"
                           @on-change="formItem.ddRiqiShijian=($event === '' ? $event : ($event + ' 23:59:59'))" transfer
                           xid="d_ddRiqiShijian"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="xiaoshu" label="小数" xid="c_head_edit_form_item">
                          <xdo-input v-model.trim="formItem.xiaoshu" placeholder="" decimal notConvertNumber int-length="10"
                                     precision="5"
                                     :disabled="showDisable"
                                     :clearable="!showDisable"></xdo-input>



          </XdoFormItem>
          <XdoFormItem prop="ddRiqi" label="日期" xid="c_head_edit_form_item">
            <XdoDatePicker type="date" format="yyyy-MM-dd"  :disabled="showDisable"
                           placeholder="" v-model="formItem.ddRiqi"
                             transfer
                          ></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="remark1" label="下拉框(海关)" xid="c_head_edit_form_item">


            <xdo-select v-model="formItem.remark1"
                                        :disabled="showDisable"
                                         :options="this.comboxData.packingNo"

                                         meta="UNIT"
                                        :optionLabelRender="pcodeRender"></xdo-select>






            <XdoIInput type="text" v-model="formItem.remark1" :disabled="showDisable" xid="d_remark1"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remark2" label="remark2" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.remark2" :disabled="showDisable" xid="d_remark2"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remark3" label="remark3" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.remark3" :disabled="showDisable" xid="d_remark3"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remark4" label="remark4" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.remark4" :disabled="showDisable" xid="d_remark4"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remark5" label="remark5" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.remark5" :disabled="showDisable" xid="d_remark5"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="tradeCode" label="海关10位编码" xid="c_head_edit_form_item">
            <XdoIInput type="text" v-model="formItem.tradeCode" :disabled="showDisable" xid="d_tradeCode"></XdoIInput>
          </XdoFormItem>
        </XdoForm>

      </div>
    </XdoCard>
    <div class="xdo-enter-action dc-edit-actions">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed === true" :type="item.type" :disabled="item.disabled"
                   :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </div>

    <div>
      <!--    文件上传-->
      <AcmpInfoListCustom :sid="editConfig.editData.sid" :showAction="showActionWenJianShang"
                          :just-view="!showActionWenJianShang"
                          business-type="WenJianShangChuan" title="文件上传"></AcmpInfoListCustom>
    </div>

  </section>
</template>
<script>
import { csOcrApi, csOcrCon } from '../../api'
import { editStatus } from '@/view/cs-common'
import AcmpInfoListCustom from '@/view/cs-common/components/AcmpInfoListCustom'

export default {
  name: 'TestDemoHeadDetail',
  components: {
    AcmpInfoListCustom
  },
  props: {
    editConfig: { type: Object, default: () => ({}) }
  },
  data() {
    return {

      showActionWenJianShang: true,  //显示文件上传的按钮  这里查看的时候要置为 false 做权限管控,

      comboxData: csOcrCon,

      formItem: {

        sid: '',
        zifu: '',
        zhengshu: '',
        ddRiqiShijian: '',
        xiaoshu: '',
        ddRiqi: '',
        remark1: '',
        remark2: '',
        remark3: '',
        remark4: '',
        remark5: '',
        tradeCode: ''


      },


      /**
       * 表单校验
       */
      ruleValidate: {
        // invoiceNo: [
        //   { required: true, message: '发票号码不能为空！', trigger: 'blur' },
        //   { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // invoiceDate: [
        //   { required: true, message: '日期不能为空！', trigger: 'blur' },
        // ],
        // packingNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // referenceNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // shipper: [
        //   { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // shipperCode: [
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // consignee: [
        //   { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // tradeTerms: [
        //   { max: 5, message: '长度不能超过5位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        //
        //
        // qty: [{ required: true, pattern: /^-?\d{1,15}(\.\d{1,5})?$/, message: '不能为空！', trigger: 'blur' }], //带负数
        //
        //
        // unit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // unitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // totalAmount: [
        // ],
        // curr: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // currConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // originCountry: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // originCountryConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // orderNo: [
        //   { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // palletNum: [
        // ],
        // bulkCtns: [
        // ],
        // totalCtns: [
        // ],
        // packNum: [
        // ],
        // netWt: [
        // ],
        // netWtUnit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // netWtUnitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // grossWt: [
        // ],
        // grossWtUnit: [
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // grossWtUnitConvert: [
        //   { max: 3, message: '长度不能超过3位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // volume: [
        // ],
        // createStatus: [
        //   { required: true, message: '生成状态(1已识别 2已生成)不能为空！', trigger: 'blur' },
        //   { max: 1, message: '长度不能超过1位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // modifyStatus: [
        //   { required: true, message: '修改状态（1未修改；2已修改）不能为空！', trigger: 'blur' },
        //   { max: 1, message: '长度不能超过1位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // scanTime: [
        //   { max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // headId: [
        //   { max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // tradeCode: [
        //   { required: true, message: '企业编码不能为空！', trigger: 'blur' },
        //   { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // insertUserName: [
        //   { required: true, message: '创建人名称不能为空！', trigger: 'blur' },
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // updateUserName: [
        //   { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
        // ],
        // dataType: [
        //   { required: true, message: '数据类型(INVOICE.发票 PACKING.箱单)不能为空！', trigger: 'blur' },
        //   { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
        // ],
      },
      buttons: [
        {
          code: 'Save',
          type: 'primary',
          disabled: false,
          click: this.handleSave,
          label: '保存',
          loading: false,
          needed: true
        },
        {
          code: 'Cancel',
          type: 'primary',
          disabled: false,
          click: this.handleBack,
          label: '返回',
          loading: false,
          needed: true
        }
      ],
      sid: '',
      showDisable: false
    }
  },
  mounted: function() {
    if (this.editConfig && this.editConfig.editStatus === editStatus.ADD) {
      this.resetFormData()
      this.showDisable = false
      this.buttons.filter(item => item.code === 'Save')[0].needed = true
      return
    }
    if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {
      this.formItem = { ...this.editConfig.editData }
      this.showDisable = false
      this.sid = this.editConfig.editData.sid
      this.buttons.filter(item => item.code === 'Save')[0].needed = true
      return
    }
    if (this.editConfig && this.editConfig.editStatus === editStatus.SHOW) {
      this.formItem = { ...this.editConfig.editData }
      this.showDisable = true
      this.buttons.filter(item => item.code === 'Save')[0].needed = false
      return
    }
  },
  methods: {
    resetFormData() {
      this.$refs['formItem'].resetFields()
    },
    handleSave() {
      this.$refs['formItem'].validate().then(isValid => {
        if (isValid) {
          let http = ''
          this.buttons.filter(item => item.code === 'Save')[0].loading = true
          if (this.sid) {
            http = this.$http.put(`${csOcrApi.testDemo.head.update}/${this.sid}`, this.formItem, { noIntercept: true })
          } else {
            http = this.$http.post(csOcrApi.testDemo.head.insert, this.formItem, { noIntercept: true })
          }
          http.then(res => {
            if (res.data.success) {
              this.$Message.success(res.data.message)

              this.frmData = { ...this.frmData, ...res.data.data }
              this.sid = this.frmData.sid

              //this.handleBack() //如果这个取消   todo //新增 需要 改成 修改 并把后端返回的对象填充到 当前页面
            } else {
              this.$Message.error(res.data.message)
            }
          }, () => {
          }).finally(() => {
            this.buttons.filter(item => item.code === 'Save')[0].loading = false
          })
        }
      })
    },
    handleBack() {
      this.$emit('onEditBack', { showList: true })
    }

  }
}
</script>
<style lang="less" scoped>
</style>
