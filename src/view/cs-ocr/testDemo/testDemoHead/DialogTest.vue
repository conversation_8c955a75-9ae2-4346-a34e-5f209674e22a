<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="500"
    title="可选项">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px;color: #389DE9;line-height: 1;"></XdoIcon>
    </a>
    <XdoForm class="dc-form dc-form-2 xdo-enter-form" ref="form" :show-message="false" :model="selectForm" :label-width="50">
      <XdoFormItem label="">
        <XdoCheckbox v-model="selectForm.isFeeInsur" style="padding-top: 4px" true-value="1" false-value="0">是否添加运保费</XdoCheckbox>
      </XdoFormItem>
      <XdoFormItem label="">
        <XdoCheckbox v-model="selectForm.isHandlingFee" style="padding-top: 4px" true-value="1" false-value="0">是否添加操作费</XdoCheckbox >
      </XdoFormItem>
      <XdoFormItem label="备注" class="dc-merge-1-3">
        <XdoIInput type="text" v-model="selectForm.remark"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem class="dc-merge-1-3" style="text-align: right">
        <XdoButton type="success" icon="ios-cloud-upload" @click="handleConfirm">确定
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>


<script>
export default {
  name: 'DialogTest', //
  props:{
    show: { type: Boolean, required: true },
  },
  data() {
    return {
      selectForm: {
        isFeeInsur:'1', //
        isHandlingFee: '1', //
        remark: '' //备注
      },
    }
  },
  methods: {
    /**
     * 确定
     */
    handleConfirm() {
      this.$emit('update:show', false)
      this.$emit('onConfirm', this.selectForm)

    },
    /**
     * 关闭
     */
    handleClose() {
      this.$emit('update:show', false)
    }
  }

}
</script>

