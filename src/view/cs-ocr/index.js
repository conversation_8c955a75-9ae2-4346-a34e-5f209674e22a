import { namespace } from '@/project'
import TestDemoHeadMain  from './testDemo/testDemoHead/TestDemoHeadMain'
import GwOcrInvoiceIHeadMain from './gwOcrInvoiceI/gwOcrInvoiceIHead/GwOcrInvoiceIHeadMain'
import GwIEntryMain from './gwOcrEntryI/GwIEntryMain'
import GwEEntryMain from './gwOcrEntryE/GwEEntryMain'

export default [
  {
    path: '/' + namespace + '/ocr/GwOcrInvoiceIHeadMain',
    name: 'GwOcrInvoiceIHeadMain',
    meta: {
      icon: 'ios-document',
      title: '进口发票箱单'
    },
    component: GwOcrInvoiceIHeadMain
  },
  {
    path: '/' + namespace + '/ocr/GwIEntryMain',
    name: 'GwIEntryMain',
    meta: {
      icon: 'ios-document',
      title: '进口报关草单'
    },
    component: GwIEntryMain
  },
  {
    path: '/' + namespace + '/ocr/GwEEntryMain',
    name: 'GwEEntryMain',
    meta: {
      icon: 'ios-document',
      title: '出口报关草单'
    },
    component: GwEEntryMain
  },
  {
    path: '/' + namespace + '/ocr/TestDemoHeadMain',
    name: 'TestDemoHeadMain',
    meta: {
      icon: 'ios-document',
      title: '测试demo'
    },
    component: TestDemoHeadMain
  },
]
