import { columnRender } from '@/view/cs-knorr/common/comm'
import { customDec } from '@/view/cs-knorr/common/comm/constant'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '清单内部编号',
        key: 'emsListNo',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '提运单号码',
        key: 'billNo',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '报关单号',
        key: 'entryId',
        width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '申报单位社会信用代码',
        minWidth: 120,
        align: 'center',
        key: 'agentCodeScc',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '申报单位名称',
        minWidth: 120,
        align: 'center',
        key: 'agentName',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '境内发货人社会信用代码',
        minWidth: 120,
        align: 'center',
        key: 'tradeCodeScc',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '境内发货人名称',
        minWidth: 120,
        align: 'center',
        key: 'tradeName',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '境外收货人',
        minWidth: 120,
        align: 'center',
        key: 'overseasTradeCode',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '合同协议号',
        minWidth: 120,
        align: 'center',
        key: 'contrNo',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '征免性质',
        minWidth: 120,
        align: 'center',
        key: 'cutMode',
        ellipsis: true,
        tooltip: true
      },
      {
        width: 180,
        tooltip: true,
        title: '征免性质名称',
        key: 'cutModeName'
      },
      {
        title: '申报日期',
        width: 120,
        align: 'center',
        key: 'ddate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyyMMdd')
        }
      },
      {
        title: '指运港',
        key: 'distinatePort',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '毛重',
        key: 'grossWt',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: '净重',
        key: 'netWt',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '出口日期',
        key: 'ieDate',
        width: 110,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params,'yyyyMMdd')
        }
      },
      {
        title: '备案号',
        key: 'manualNo',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: '申报地海关',
        key: 'masterCustoms',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        width: 90,
        tooltip: true,
        title: '出境关别',
        key: 'ieport'
      },
      {
        width: 180,
        tooltip: true,
        title: '出境关别名称',
        key: 'ieportName'
      },
      {
        width: 90,
        tooltip: true,
        title: '离境口岸',
        key: 'entryPortCode'
      },
      {
        width: 180,
        tooltip: true,
        title: '离境口岸名称',
        key: 'entryPortName'
      },
      {
        title: '生产销售单位社会信用编码',
        width: 150,
        align: 'center',
        key: 'ownerScc',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '生产销售单位',
        key: 'ownerName',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: '件数',
        key: 'packNo',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: '监管方式',
        key: 'tradeMode',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 180,
        tooltip: true,
        title: '监管方式名称',
        key: 'tradeModeName'
      },
      {
        title: '运抵国(地区)',
        key: 'tradeCountry',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 180,
        tooltip: true,
        title: '运抵国(地区)名称',
        key: 'tradeCountryName'
      },
      {
        title: '运输方式',
        key: 'trafMode',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '运输方式名称',
        key: 'trafName',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: '成交方式',
        key: 'transMode',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 180,
        tooltip: true,
        title: '成交方式名称',
        key: 'transModeName'
      },
      {
        title: '运输工具名称及航次号',
        key: 'voyageNo',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '包装种类',
        key: 'wrapType',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 180,
        tooltip: true,
        title: '包装种类名称',
        key: 'wrapTypeName'
      },
      {
        title: '许可证编号',
        key: 'licenseNo',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '贸易国(地区)',
        key: 'tradeAreaCode',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 180,
        tooltip: true,
        title: '贸易国名称',
        key: 'tradeAreaName'
      },
      {
        title: '运费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'feeMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap)
            }
          },
          {
            title: '费率',
            key: 'feeRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'feeCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
          }
        ]
      },
      {
        title: '保费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'insurMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap)
            }
          },
          {
            title: '费率',
            key: 'insurRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'insurCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
          }
        ]
      },
      {
        title: '杂费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'otherMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap)
            }
          },
          {
            title: '费率',
            key: 'otherRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'otherCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
          }
        ]
      },
      {
        title: '特殊关系确认',
        key: 'confirmSpecial',
        width: 90,
        align: 'center'
      },
      {
        title: '价格影响确认',
        key: 'confirmPrice',
        width: 90,
        align: 'center'
      },
      {
        width: 90,
        key: 'confirmRoyalties',
        title: '支付特许权使用费确认',
      },
      {
        width: 90,
        title: '公式定价确认',
        key: 'confirmFormulaPrice'
      },
      {
        width: 90,
        tooltip: true,
        title: '暂定价格确认',
        key: 'confirmTempPrice'
      },
      {
        width: 90,
        tooltip: true,
        title: '自报自缴',
        key: 'dutySelf'
      },
      {
        width: 180,
        tooltip: true,
        title: '随附单证及编号',
        key: 'acmpNo'
      },
      {
        title: '备注',
        key: 'note',
        width: 250,
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}

export {
  columns
}
