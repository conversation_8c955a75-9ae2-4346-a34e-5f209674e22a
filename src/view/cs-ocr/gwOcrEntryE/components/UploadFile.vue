<template>
  <div style="display: inline-block">
    <table>
      <tr>
        <td>
          内部编号：
        </td>
        <td>
          <XdoIInput type="text" v-model="uploadFileConfig.data.emsListNo"></XdoIInput>
        </td>
        <td>
          <Upload
            :action="uploadFileConfig.action"
            :headers="uploadFileConfig.headers"
            :data="uploadFileConfig.data"
            :show-upload-list="false"
            multiple
            accept=".zip,.rar,.pdf"
            :format="['zip','rar','pdf']"
            :before-upload="handleUpload"
            :on-format-error="handleFormatError"
            :on-success="handleSuccess">
            <Button icon="ios-search-outline" :loading="isSHowLoading ||meLoading " @click="handleBeforeSelect">选择文件</Button>
          </Upload>
        </td>

        <td style="padding-left: 5px">
          <XdoButton icon="ios-cloud-upload-outline" :loading="isSHowLoading || meLoading" type="warning" @click="upload">文件识别
          </XdoButton>
        </td>
        <td>
          <div class="ab" style="width: 320px; margin-left: 30px">
            <Tooltip :max-width="310" transfer :content="computeFileName" placement="bottom-start">
              <span > {{ computeFileName }} </span>
            </Tooltip>
          </div>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>


import { csOcrApi } from '../../api'
// import { entry } from '@/view/cs-ocr/api/apiUrl'

export default {
  props: {
    //按钮 禁用
    isSHowLoading: { type: Boolean, require: false }
  },

  name: 'UploadFile',
  data() {
    return {
      // entry: entry,
      fileArray: [], //选择的文件 数组
      uploadFileConfig: {
        action: csOcrApi.entry.head.uploadEntryFile,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.token
        },
        data: {
          emsListNo: '', //内部编号
          ieMark: 'E'
        }
      },
      meLoading:false
    }
  },
  methods: {

    /**
     * 选择文件之前的 事件  清空数组
     */
    handleBeforeSelect() {
      this.fileArray = []
    },

    /**
     * :before-upload="handleUpload"
     * @param file
     * @returns {boolean}
     */
    handleUpload(file) {


      this.fileArray.push(file)

      // if (this.fileArray){
      //   this.fileName = file.name
      // }
      return false
    },
    /**
     * 手动点击上传按钮 事件
     */
    upload() {
      if (!this.fileArray || this.fileArray.length === 0) {
        this.$Message.warning('请选择需要上传的文件')
        return
      }
      // this.fileArray.forEach((item)=>{
      //   console.log(item)
      //   let suffix = item.name.substring(item.name.lastIndexOf('.') + 1)
      //   if (!['zip','rar','.pdf'].includes(suffix)){
      //     this.$Message.warning('上传失败,只能上传 zip,rar,pdf文件')
      //     return
      //   }
      // })
      this.meLoading = true
      const fd = new FormData()
      this.fileArray.map((item) => {
        fd.append('file', item)
        return item
      })

      fd.append('ieMark', this.uploadFileConfig.data.ieMark)
      fd.append('emsListNo', this.uploadFileConfig.data.emsListNo)

      this.$http.post(this.uploadFileConfig.action, fd, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then((res) => {
        this.$emit('onSuccess', res)
      }).catch(() => {
      }).finally(() => {

        this.meLoading = false
          console.log(this.meLoading)
      })
    },
    handleFormatError() {
      this.$Message.warning('文件格式不正确')
    },
    handleBeforeUpload() {
      // const check = this.uploadList.length < this.sizeUp
      // if (!check) {
      //   this.$Message.warning('只能上传' + this.sizeUp + '个文件')
      // }
      // return check
    },
    handleSuccess(res) {
      //alert(11111)
      this.$emit('onSuccess', res)

    }
  },
  computed: {
    computeFileName() {
      if (this.fileArray.length === 0) {

        return ''
      }

      let fileNameList = this.fileArray.map((i) => {
        return i.name
      })
      return fileNameList.join('，')
    }
  }
}
</script>

<style scoped>

.ab {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
}

</style>
