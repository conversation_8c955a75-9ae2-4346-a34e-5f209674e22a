<template>
  <div>
    <div class="layout">
      <Layout>
        <Content>
          <div>
<!--            <GwOcrInvoiceIHeadDetail @onPreviewPdf="handlePreviewPdf" @onEditBack="editBack" :editConfig="editConfig" :sumData="sumData">-->
<!--            </GwOcrInvoiceIHeadDetail>-->
<!--            <GwOcrInvoiceIBodyMain :needLocBody="needLocBody" :editConfig="editConfig" :isShowToolBar="false" :isShowSearch="false"-->
<!--                                   @onClickGridCell="handlePreviewPdf" @onGetSumData='handleGetSumData'-->


<!--            ></GwOcrInvoiceIBodyMain>-->
          </div>
        </Content>
        <Sider ref="side1" hide-trigger collapsible style="background-color: white ; border-left: #c5c8ce solid 1px"
               :width="500" :collapsed-width="30"
               v-model="isCollapsed">
          <Icon @click.native="collapsedSider" :class="rotateIcon" type="md-menu" size="24"></Icon> <span style="font-size: 13px; margin-left: 5px">源文件</span>
          <PreviewPdf :locationsData="locationsData"></PreviewPdf>
        </Sider>
      </Layout>
    </div>
    <!--    <div>-->
    <!--      -->
    <!--    </div>-->
  </div>
</template>

<script>

// import GwOcrInvoiceIHeadDetail from '../gwOcrInvoiceIHead/GwOcrInvoiceIHeadDetail'
// import GwOcrInvoiceIBodyMain from '../gwOcrInvoiceIList/GwOcrInvoiceIBodyMain'
import PreviewPdf from '../components/PreviewPdf'

export default {
  name: 'ShowFile',
  components: {
    // GwOcrInvoiceIHeadDetail,
    // GwOcrInvoiceIBodyMain,
    PreviewPdf
  },
  props: {
    /**
     * 是否需要location 信息   false 否  true 是
     */
    needLocBody: { type: Boolean, default: false },
    /**
     * 表头 信息
     */
    editConfig: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      isCollapsed: true,
      locationsData: {},

      sumData: null,
      //   {
      //   // grossWt: null,	 //总毛重
      //   // netWt: null,  //净重
      //   // packNum: null,	//件数
      //   // qty: null,	//交易数量
      //   // totalAmount: null	//总金额
      // },
        //表体 汇总信息
    }
  },
  methods: {
    /**
     *   表体组件 查询事件 触发
     * @param sumDa 表体 汇总信息
     */
    handleGetSumData(sumDa){

      this.sumData =  {...this.sumData, ...sumDa}

      //console.log(this.sumData)
    },

    /**
     * 判断字符串 是否是 一个合格的 json
     * @param str
     * @returns {boolean}
     */
    isJsonMe(str) {
      if (typeof str == 'string') {
        try {
          let obj = JSON.parse(str)
          if (typeof obj == 'object' && obj) {
            return true
          } else {
            return false
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e)
          return false
        }
      }
      else {
        return false
      }
    },

    /**
     *  定位 Pdf 位置
     * @param loc 信息
     */
    handlePreviewPdf(locationsDataReturn) {

      if (!this.isJsonMe(locationsDataReturn)) {
        this.$Message.warning('此栏位未识别!')
        return
      }
      this.locationsData = JSON.parse(locationsDataReturn)
    },

    /**
     * 返回 表头 列表  页面
     */
    editBack() {
      this.$emit('onEditBack', { isShowFile: false })
    },

    collapsedSider() {
      this.$refs.side1.toggleCollapse()

    }
  },
  computed: {
    rotateIcon() {
      return [
        'menu-icon',
        this.isCollapsed ? 'rotate-icon' : ''
      ]
    },
    menuitemClasses() {
      return [
        'menu-item',
        this.isCollapsed ? 'collapsed-menu' : ''
      ]
    }
  }
}
</script>

<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 69px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width .2s ease .2s;
}

.menu-item i {
  transform: translateX(0px);
  transition: font-size .2s ease, transform .2s ease;
  vertical-align: middle;
  font-size: 16px;
}

.collapsed-menu span {
  width: 0px;
  transition: width .2s ease;
}

.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size .2s ease .2s, transform .2s ease .2s;
  vertical-align: middle;
  font-size: 22px;
}

.menu-icon {
  transition: all .3s;
}

.rotate-icon {
  transform: rotate(-90deg);
}
</style>
