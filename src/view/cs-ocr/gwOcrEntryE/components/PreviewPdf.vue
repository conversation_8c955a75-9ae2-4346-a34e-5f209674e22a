<template>

  <div>

    <iframe  id='monitor' ref="monitor" width="100%" height="820px"
            :src="urlMe"  ></iframe>
  </div>


</template>


<script>

export default {
  name: 'PreviewPdf',
  props:{
    /**
     * {
     *  imagename:'',
     *  locations:[[],[],[],[]]
     * }
     */
    locationsData:{type:Object}
  },
  data(){
    return {
      isShow:true,
    }
  },
  computed:{
    urlMe(){
      let httpHead =  window.document.location.protocol
      //console.log(httpHead)
      if    (httpHead=='http:'){
        return `${httpHead}//ocr.dcjet.com.cn:18081/jpgScaleDemo.html`
      }else {
        return `${httpHead}//ocr.dcjet.com.cn:18082/jpgScaleDemo.html`
      }
    }
  },
  watch:{
    locationsData:{
      deep: true,
      // immediate: true,
      handler: function (val) {
        //构建传输的数据
        let postData = {...{
          // 固定
          method: 'reloadAndhighlight',
          //缩放比例
          initScale: 0.65,

        },...val}
        this.$refs.monitor.contentWindow.postMessage(JSON.stringify(postData) , '*')
      }
    }
  },


  methods: {

    postMsg () {
      // OcrJson Ocr = new OcrJson();
      // Ocr.method = "reloadAndhighlight";
      // Ocr.initScale = 0.4f;
      // Ocr.imagename = strImgurl;
      // Ocr.locations = serializer.Deserialize<List<List<int>>>(strOption);
      // return serializer.Serialize(Ocr);
      //构建传输的数据
      let postData = {
        // 固定
        method: 'reloadAndhighlight',
        //缩放比例
        initScale: 0.65,
        //当前字段信息 所在的图片
        imagename: 'http://ocr.dcjet.com.cn:51000/file/d04f5beb-0a8c-4c8a-9fa3-bcc318460f82_page0_detection.jpg',
        //当前字段信息的坐标
        locations: [
          [
            1055,
            1340
          ],
          [
            1152,
            1340
          ],
          [
            1152,
            1376
          ],
          [
            1055,
            1376
          ]
        ]
      }
      this.$refs.monitor.contentWindow.postMessage(JSON.stringify(postData) , '*')
    },


  }
}
</script>

<style scoped>

</style>
