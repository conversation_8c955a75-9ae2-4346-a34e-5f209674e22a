<template>
  <div >
    <XdoCard :bordered="false">
      <div style="display: flex; justify-content: space-between; line-height: 30px; height: 30px">
        <div>
          <UploadFile :isSHowLoading="isSHowLoading" @onSuccess="handleUpFileSuccess"></UploadFile>
        </div>
        <div>
          <span style="color: cornflowerblue">{{ upLoadMes }}</span>
          <Button   ghost v-show="isSHowLoading"   loading style="margin-left: 15px;color: #696666"  >loading...</Button>
        </div>
        <div>
          <span style="color: cornflowerblue">{{ upLoadRes }}</span>
        </div>
        <div>
          <XdoButton type="text" @click="handleBack">
            <XdoIcon type="ios-undo" size="22" style="color: green;"/>
          </XdoButton>
        </div>
      </div>
    </XdoCard>
    <XdoCard :bordered="false" title="">


      <div class="layout">
        <Layout>

          <Sider ref="side222" hide-trigger collapsible style="background-color: white ; border-left: #c5c8ce solid 1px"
                 :width="190" :collapsed-width="30"
                 v-model="isCollapsed">
            <Icon @click.native="collapsedSider" :class="rotateIcon" type="md-menu" size="24"></Icon>

            <HeadMainByTaskId :taskId="taskIdPro" @onSelectRow="handleHeadMainSelectRow"></HeadMainByTaskId>

          </Sider>
          <Content>
            <div>
              <ShowFile v-if="isShowFile" :editConfig="editConfig" :needLocBody="needLocBody"></ShowFile>
            </div>
          </Content>

        </Layout>
      </div>


      <!--      <div style="display: flex; justify-self: left">-->
      <!--        <div style="width: 10%;">-->
      <!--          <HeadMainByTaskId :taskId="taskIdPro" @onSelectRow="handleHeadMainSelectRow"></HeadMainByTaskId>-->
      <!--        </div>-->
      <!--        <div>-->
      <!--          <ShowFile v-if="isShowFile" :editConfig="editConfig"-->
      <!--                    :needLocBody="needLocBody"> >-->
      <!--          </ShowFile>-->
      <!--        </div>-->
      <!--      </div>-->
    </XdoCard>
  </div>
</template>

<script>

import UploadFile from './components/UploadFile'
import HeadMainByTaskId from './components/HeadMainByTaskId'
import ShowFile from './components/ShowFile'
import { csOcrApi } from '../api'
import { editStatus } from '../api/constant'

export default {
  name: 'GwOcrEntryUpload',
  components: {
    UploadFile,
    HeadMainByTaskId,
    ShowFile

  },
  data() {
    return {


      isCollapsed: false,


      upLoadMes: '',
      taskId: '',   //上传 成功以后  后端回传的 任务编号
      taskIdPro: '',
      setIntervalNo: null,  //存储的 定时任务的 对象
      ajaxUrl: {
        getOne: csOcrApi.gwOcrInvoiceI.log.head.getOne
      },
      needLocBody: true,
      editConfig: {
        editData: {},
        editStatus: editStatus.SHOWFILE
      },
      isShowFile: false,
      taskStatus: ''


    }
  },
  mounted() {
    this.taskIdPro = 'f579884d-febe-4ae3-aeaf-c94babc63e07'
  },
  methods: {

    collapsedSider() {
      this.$refs.side222.toggleCollapse()

    },

    handleHeadMainSelectRow(row) {

      if (row) {
        this.editConfig.editData = row
        this.isShowFile = false
        this.$nextTick(function() {
          this.isShowFile = true
        })
      }


    },
    /**
     * 返回事件
     */
    handleBack() {
      this.handleClearInterval()
      this.$emit('onBack')
    },
    /**
     * 文件上传 成功事件
     * @param res
     */
    handleUpFileSuccess(res) {

      this.handleClearInterval()

      if (res.data.success) {
        this.taskId = res.data.data
        this.upLoadMes = '文件识别中,请不要关闭页面!'
        this.taskStatus = '0'    // 执行状态(-100 异常 -99上传失败  0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败) 4 重复识别
        // 开启定时任务
        this.handleSetInterval()
      } else {
        this.taskStatus = '-99'    // 执行状态(-100 异常 -99上传失败  0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败) 4 重复识别
        this.upLoadMes = res.data.message
        // console.log(res.message)
      }
    },
    /**
     * 关闭的 定时器
     */
    handleClearInterval() {
      this.taskStatus = ''
      if (this.setIntervalNo) {
        clearInterval(this.setIntervalNo)
      }
    },

    /**
     * 执行 定时 任务
     */
    handleSetInterval() {
      this.setIntervalNo = setInterval(this.handleGetTaskInfo, 5000)
    },

    /**
     * 获取 任务 信息
     */
    handleGetTaskInfo() {

      this.$http.get(this.ajaxUrl.getOne + `/${this.taskId}`, { noIntercept: true }).then(res => {
        if (res.data.success) {
          this.taskStatus = res.data.data.status   // 执行状态(-100 异常 -99上传失败  0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败) 4 重复识别
          //status   执行状态(0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败) 4 重复识别
          if (['1', '2', '3', '4'].includes(res.data.data.status)) {
            this.handleClearInterval()
          }

          if (['1'].includes(res.data.data.status)) {
              this.upLoadMes = res.data.data.errorMessage   //这里是 接口返回的  数据行 的 message
          }
          if (['3'].includes(res.data.data.status)) {
              this.upLoadMes = 'OCR识别解析失败'   //这里是 接口返回的  数据行 的 message
          }

          if (['4'].includes(res.data.data.status)) {
            this.upLoadMes = '重复识别,已存在相同业务编号!'
            //这里是 接口返回的  数据行 的 message
          }

          if (['2'].includes(res.data.data.status)) {
            this.upLoadMes = 'OCR识别完成解析成功'  //这里是 接口返回的  数据行 的 message
            this.taskIdPro = res.data.data.taskId   //处罚数据的 查询
          }
        } else {
          this.handleClearInterval()
          this.upLoadMes = res.data.message    //这里是 接口 直接 返回的  message
        }
      }).catch(function(res) {
        this.handleClearInterval()
        this.upLoadMes = res    //这里是 接口 直接 返回的  message

      })
    }
  },
  computed: {
    isSHowLoading() {
      switch (this.taskStatus) {
        case '-100':
          return false
        case '-99':
          return false
        case '0':
          return true //调用上传接口成功
        case '1':
          return false  //调用上传接口失败
        case '2':
          return false  //OCR识别完成解析成功
        case '3':
          return false   //OCR识别完成但解析失败
        case '4':
          return false   // 重复识别
        default:
          return false
      }
    },
    upLoadRes() {
      //this.taskStatus= res.data.data.status   // 执行状态(-100 异常 -99 上传失败  0.调用上传接口成功 1.调用上传接口失败 2.OCR识别完成解析成功 3.OCR识别完成但解析失败) 4 重复识别
      switch (this.taskStatus) {
        case '-100':
          return '异常'
        case '-99':
          return '上传失败'
        case '0':
          return '上传成功' //调用上传接口成功
        case '1':
          return '上传失败'  //调用上传接口失败
        case '2':
          return '识别完成'  //OCR识别完成解析成功
        case '3':
          return '识别失败'   //OCR识别完成但解析失败
        case '4':
          return '重复识别'
        default:
          return ''
      }

    },

    rotateIcon() {
      return [
        'menu-icon',
        this.isCollapsed ? 'rotate-icon' : ''
      ]
    },
    menuitemClasses() {
      return [
        'menu-item',
        this.isCollapsed ? 'collapsed-menu' : ''
      ]
    }
  }
}
</script>

<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 69px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width .2s ease .2s;
}

.menu-item i {
  transform: translateX(0px);
  transition: font-size .2s ease, transform .2s ease;
  vertical-align: middle;
  font-size: 16px;
}

.collapsed-menu span {
  width: 0px;
  transition: width .2s ease;
}

.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size .2s ease .2s, transform .2s ease .2s;
  vertical-align: middle;
  font-size: 22px;
}

.menu-icon {
  transition: all .3s;
}

.rotate-icon {
  transform: rotate(-90deg);
}
</style>
