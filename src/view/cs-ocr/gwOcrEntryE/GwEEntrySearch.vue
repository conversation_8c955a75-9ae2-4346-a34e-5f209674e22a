<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="ocrStatus" label="状态">
        <xdo-select v-model="searchParam.ocrStatus" :options="this.OCR_STATUS"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="清单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryId" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryId"></XdoIInput>
      </XdoFormItem>

    </XdoForm>
  </section>
</template>

<script>

  import { ocrStatus } from '@/view/cs-ocr/api/constant'

  export default {
    name: 'GwEEntrySearch',
    components: {
    },
    data() {
      return {
        OCR_STATUS: ocrStatus,
        searchParam: {
          ocrStatus: '',
          emsListNo: '',
          entryId: '',
          ieFlag: 'E'
        },
      }
    },
    methods: {

    }
  }
</script>

<style scoped>
</style>
