<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <GwEEntrySearch ref="headSearch"></GwEEntrySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   show-total show-sizer
                   :page-size-opts='pageSizeOpts' @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>

    <GwOcrEntryUpload v-if="isShowUpLoad" @onBack="handleUploadBack"></GwOcrEntryUpload>

    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>

  </section>
</template>

<script>

  import { mainJS } from './js/GwEEntryMain'
  import { columns } from './js/GwEEntryColunmns'
  import GwEEntrySearch from './GwEEntrySearch'
  import GwOcrEntryUpload from './GwOcrEntryUpload'

  export default {
    name: 'GwEEntryMain',
    moduleName: '进口报关草单',
    components: {
      GwEEntrySearch,
      GwOcrEntryUpload
    },
    mixins: [columns, mainJS],
    data() {
      return {

      }
    },
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>

