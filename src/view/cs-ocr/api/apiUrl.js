/*
* 关务 访问后端接口地址
* */
const baseUri = '/gwstd/api'

/**
 * 基础资料接口
 *
 */

export const commApi = {

  //获取所有小提单模板
  decErpHeadTemplate: `${baseUri}/v1/decErpHeadTemplete/selectDec` ,     // ：v1/decErpHeadTemplete/selectDec/{iemark}  //返回date中是 key,value,
  //获取 企业自定义参数 对应的海关参数
  getConvertedValue:`${baseUri}/v1/biCustomerParams/getConvertedValue`    // 获取转换信息 get  /{type}/{code}

}


export const gwOcrInvoiceI = {


  head: {
    insert: '',// `${baseUri}/v1/gwOcrInvoiceIHead`,
    update: `${baseUri}/v1/gwOcrInvoiceIHead`,  //修改
    delete: `${baseUri}/v1/gwOcrInvoiceIHead`,
    selectAllPaged: `${baseUri}/v1/gwOcrInvoiceIHead/list`, //分页查询接口
    exportUrl: `${baseUri}/v1/gwOcrInvoiceIHead/export`,    //Excel数据导出接口
    uploadFile: `${baseUri}/v1/gwOcrInvoiceIHead/uploadFile`,//上传文件
    buildDecRrp: `${baseUri}/v1/gwOcrInvoiceIHead/createDec`,     //生成预录入单
    getSumInfo: `${baseUri}/v1/gwOcrInvoiceIHead/getSumInfo` //  获取发票、箱单汇总信息  /v1/gwOcrInvoiceIHead/getSumInfo/{sids}
  },
  body: {
    selectAllPaged: `${baseUri}/v1/gwOcrInvoiceIList/list`, //分页查询接口
    sumData: `${baseUri}/v1/gwOcrInvoiceIList/sumData`,    //GET  /v1/gwOcrInvoiceIList/sumData/{headId} 表体汇总
    update: `${baseUri}/v1/gwOcrInvoiceIList`  //修改

  },
  headLoc: {
    getOne: `${baseUri}/v1/gwOcrInvoiceIHeadLoc/getOne`   //
  },
  log: {
    head: {
      getOne: `${baseUri}/v1/gwOcrLog/getOne`
    }
  }


}



//测试
export  const  testDemo = {
  head:{
    insert: `${baseUri}/v1/testDemo`,
    update: `${baseUri}/v1/testDemo`,  //修改
    delete: `${baseUri}/v1/testDemo`,
    list: `${baseUri}/v1/testDemo/list`, //分页查询接口
    export: `${baseUri}/v1/testDemo/export`,    //Excel数据导出接口
  }
}

export const entry = {
  head:{
    uploadEntryFile: `${baseUri}/v1/entryThird/uploadEntryFile`,//上传文件
    delete: `${baseUri}/v1/entryThird`,
    selectAllPaged: `${baseUri}/v1/entryThird/list`, //分页查询接口
    export: `${baseUri}/v1/entryThird/export`,    //Excel数据导出接口
  }
}






