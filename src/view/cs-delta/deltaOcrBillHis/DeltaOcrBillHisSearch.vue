<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="enNo" label="EN号码" xid="c_head_search_form_item">
        <XdoIInput type="text" v-model="searchParam.enNo" xid="s_enNo"></XdoIInput>
      </XdoFormItem>
      <DcDateRange label="提单识别时间" @onDateRangeChanged="handleDateChange"></DcDateRange>
    </XdoForm>
  </section>
</template>
<script>
import DcDateRange from '@/components/dc-date-range/dc-date-range'

export default {
  name: 'DeltaOcrBillHisSearch',
  components: {
    DcDateRange
  },
  data() {
    return {
      searchParam: {
        enNo: '',
        scanTimeFrom: '',
        scanTimeTo: ''
      }
    }
  },
  mounted() {
  },
  methods: {
    handleDateChange(values) {
      this.searchParam.scanTimeFrom = values[0]
      this.searchParam.scanTimeTo = values[1]
    }
  }
}
</script>
<style scoped>
</style>
