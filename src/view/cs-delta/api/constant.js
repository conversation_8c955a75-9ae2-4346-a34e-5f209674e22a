


export const editStatus = {
  // 新增
  ADD: `add`,
  // 修改
  EDIT: `edit`,
  // 只读
  SHOW: `show`,

  //文件查看
  SHOWFILE:'showFile'
}

//字段类型  （0 字符；1 数字）
export const fieldType = [
  {value: '0', label: '字符'},
  {value: '1', label: '数值'},
]

export const fieldDesc = [
  {value: 'enNo', label: 'EN号码'},
  {value: 'shipTo', label: 'SHIP TO'},
  {value: 'consignee', label: '收货人'},
  {value: 'notifyParty', label: '通知人'},
  {value: 'despPort', label: '启运港'},
  {value: 'markNo', label: '标记唛码'},
  {value: 'outerPackaging', label: '外包装数量'},
  {value: 'gName', label: '主要品名'},
  {value: 'grossWt', label: '总毛重'},
  {value: 'volume', label: '总体积'},
]

//(0.未比对 1.一致 2.不一致)
export const compareReuslt = [
  {value: '0', label: '未比对'},
  {value: '1', label: '一致'},
  {value: '2', label: '不一致'},
]

//(1.提单已识别 2.托书已获取 3.托书获取失败 4.比对成功 5.比对失败)
export const ocrStatus = [
  {value: '1', label: '提单已识别'},
  {value: '2', label: '托书已获取'},
  {value: '3', label: '托书获取失败'},
  {value: '4', label: '比对成功'},
  {value: '5', label: '比对失败'},
]

//(0.未通知 1.已通知)
export const emailNotifyStatus = [
  {value: '0', label: '未通知'},
  {value: '1', label: '已通知'},
]




