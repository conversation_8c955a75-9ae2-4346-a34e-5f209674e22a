<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam"
             label-position="right" :label-width="120" inline>

      <XdoFormItem prop="fieldType" label="字段类型">
        <xdo-select v-model="searchParam.fieldType" :options="this.selectBoxData.fieldType" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="beforeConvert" label="转换前的值">
        <XdoIInput type="text" v-model="searchParam.beforeConvert"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="afterConvert" label="转换后的值">
        <XdoIInput type="text" v-model="searchParam.afterConvert"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deltaConstant } from '../api'

  export default {
    name: 'DeltaOcrTransSearch',
    data() {
      return {
        selectBoxData: deltaConstant,
        searchParam: {
          fieldType: '',
          beforeConvert: '',
          afterConvert: '',
        },
      }
    },
    mounted(){
    },
    methods: {
    }
  }
</script>
<style scoped>
</style>
