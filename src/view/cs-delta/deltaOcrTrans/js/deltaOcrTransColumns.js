import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { deltaConstant } from '@/view/cs-delta/api'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '字段类型',
        minWidth: 120,
        align: 'center',
        key: 'fieldType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, deltaConstant.fieldType)
        },
      },
      {
        title: '转换前的值',
        minWidth: 120,
        align: 'center',
        key: 'beforeConvert',
      },
      {
        title: '转换后的值',
        minWidth: 120,
        align: 'center',
        key: 'afterConvert',
      },
      // {
      //   title: '企业编码',
      //   minWidth: 120,
      //   align: 'center',
      //   key: 'tradeCode',
      // },
      {
        title: '创建人名称',
        minWidth: 120,
        align: 'center',
        key: 'insertUserName',
      },
      {
        title: '修改人名称',
        minWidth: 120,
        align: 'center',
        key: 'updateUserName',
      },
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}
export {
  columns,
}
