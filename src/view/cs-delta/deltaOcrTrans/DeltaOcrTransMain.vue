<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show='showSearch'>
          <div class='separateLine'></div>
          <!--查询条件-->
          <DeltaOcrTransSearch ref='headSearch'></DeltaOcrTransSearch>
        </div>
      </XdoCard>

      <XdoCard :bordered='false'>
        <div class='action' ref='area_actions'>
          <!--工具栏-->
          <xdo-toolbar @click='handleToolbarClick' :action-source='actions'></xdo-toolbar>
        </div>
      </XdoCard>


      <XdoCard :bordered='false'>
        <!--        gird-->
        <xdo-ag-grid ref='ref_agGrid' :checkboxSelection='checkboxSelection'
                     rowSelection='multiple'
                     :columns='gridConfig.gridColumns'
                     :data='gridConfig.data'
                     :height='dynamicHeight'
                     :GridReady='handleGridReady'
                     @selectionChanged='handleSelectionChanged'>
        </xdo-ag-grid>
        <div ref='area_page'>
          <!--分页-->
          <XdoPage class='dc-page' show-total show-sizer
                   :page-size-opts='pageSizeOpts'
                   :current='pageParam.page'
                   :page-size='pageParam.limit'
                   :total='pageParam.dataTotal'
                   @on-change='pageChange'
                   @on-page-size-change='pageSizeChange'/>
        </div>
      </XdoCard>
    </div>
    <DeltaOcrTransDetail v-if="!showList" @onEditBack="handleCommonEditBack" :editConfig="editConfig"></DeltaOcrTransDetail>

    <!--自定义配置列-->
    <TableColumnSetup v-model='showTableColumnSetup'
                      :resId='tableId'
                      @updateColumns='handleUpdateColumn'
                      :columns='totalColumns'
                      class='height:500px'/>
  </section>
</template>
<script>
  import { commonMain } from '@/view/cs-acustomization/common/comm/commonMain'

  import { columns } from './js/deltaOcrTransColumns'
  import DeltaOcrTransSearch from './DeltaOcrTransSearch'
  import DeltaOcrTransDetail from './DeltaOcrTransDetail'
  import { deltaApi } from '../api'



  export default {
    name: 'DeltaOcrTransMain',
    moduleName: '中达转换关系-列表',
    components: {
      DeltaOcrTransSearch,
      DeltaOcrTransDetail,
    },
    mixins: [ columns, commonMain ],
    data() {
      return {
        gridConfig: {
          exportTitle: '中达OCR转换关系'
        },
        isShowSetting: true,
        searchLines: 3,
        toolbarEventMap: {
          'add': this.handleCommonAdd,
          'edit': this.handleCommonEdit,
          'delete': this.handleDelete,
          'export': this.handleExport,
        },
        ajaxUrl: {
          delete: deltaApi.deltaUrl.deltaOcrTrans.delete,
          export: deltaApi.deltaUrl.deltaOcrTrans.export,
          list: deltaApi.deltaUrl.deltaOcrTrans.list,
        },
      }
    },
    methods: {
      /**
       * 删除
       */
      handleDelete() {
        this.handleCommonDelete(this.ajaxUrl.delete, 'delete')
      },
      /**
       * 导出 按钮 的 事件
       */
      handleExport() {
        this.handleCommonExport(this.ajaxUrl.export, 'export')
      }

    }
  }
</script>


<style lang='less' scoped>
/**
 点击 查询条件按钮 的时候显示的 虚线
 */
.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
