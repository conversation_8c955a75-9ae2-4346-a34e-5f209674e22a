<template>
    <section class="dc-edit" v-focus>
        <XdoCard class="xdo-card" :bordered="false">
            <div class="xdo-card-body">
                <XdoForm ref="formItem" class="dc-form" :model="formItem" label-position="right" :label-width="120" :rules="ruleValidate" inline>
                  <XdoFormItem prop="fieldType" label="字段类型" xid="c_head_edit_form_item">
                    <xdo-select v-model="formItem.fieldType" :options="comboxData.fieldType" :disabled="showDisable" :optionLabelRender="pcodeRender"></xdo-select>
                  </XdoFormItem>
                  <XdoFormItem prop="beforeConvert" label="转换前的值" xid="c_head_edit_form_item">
                    <XdoIInput type="text" v-model="formItem.beforeConvert" :disabled="showDisable" xid="d_beforeConvert"></XdoIInput>
                  </XdoFormItem>
                  <XdoFormItem prop="afterConvert" label="转换后的值" xid="c_head_edit_form_item">
                    <XdoIInput type="text" v-model="formItem.afterConvert" :disabled="showDisable" xid="d_afterConvert"></XdoIInput>
                  </XdoFormItem>

                </XdoForm>
            </div>
        </XdoCard>
        <div class="xdo-enter-action dc-edit-actions">
            <template v-for="item in buttons">
                <XdoButton v-if="item.needed === true" :type="item.type" :disabled="item.disabled"
                           :loading="item.loading"
                           @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
                </XdoButton>&nbsp;
            </template>
        </div>
    </section>
</template>
<script>
    import { deltaApi, deltaConstant } from '../api'
    import { editStatus } from '@/view/cs-common/constant'
    // import { decimalKeypress,decimalKeyup,numberKeypress,numberKeyup} from '@/libs/num'
    export default {
        name: 'DeltaOcrTransDetail',
        props: {
            editConfig: { type: Object, default: () => ({}) }
        },
        data() {
            return {
                comboxData:deltaConstant,
                formItem: {
                    sid: '',
                    beforeConvert: '',
                    afterConvert: '',
                    fieldType: '',
                },
                ruleValidate: {
                            beforeConvert: [
                                { required: true, message: '转换前的值不能为空！', trigger: 'blur' },
                                    { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
                            ],
                            afterConvert: [
                                { required: true, message: '转换前的值不能为空！', trigger: 'blur' },
                                    { max: 20, message: '长度不能超过20位字节(汉字占2位)！', trigger: 'blur' }
                            ],
                            fieldType: [
                                { required: true, message: '所需要影响的字段类型(0.字符 1.数值)不能为空！', trigger: 'blur' },
                                    { max: 2, message: '长度不能超过2位字节(汉字占2位)！', trigger: 'blur' }
                            ],
                },
                buttons: [
                    {
                        code: 'Save',
                        type: 'primary',
                        disabled: false,
                        click: this.handleSave,
                        label: '保存',
                        loading: false,
                        needed: true
                    },
                    {
                        code: 'Cancel',
                        type: 'primary',
                        disabled: false,
                        click: this.handleBack,
                        label: '返回',
                        loading: false,
                        needed: true
                    }
                ],
                sid: '',
                showDisable: false
            }
        },
        mounted: function(){
            if (this.editConfig && this.editConfig.editStatus === editStatus.ADD) {
                this.resetFormData()
                this.showDisable = false
                this.buttons.filter(item => item.code === "Save")[0].needed = true
                return
            }
            if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {
                this.formItem = { ...this.editConfig.editData }
                this.showDisable = false
                this.sid = this.editConfig.editData.sid
                this.buttons.filter(item => item.code === "Save")[0].needed = true
                return
            }
            if (this.editConfig && this.editConfig.editStatus === editStatus.SHOW) {
                this.formItem = { ...this.editConfig.editData }
                this.showDisable = true
                this.buttons.filter(item => item.code === "Save")[0].needed = false
                return
            }
        },
        methods: {
            resetFormData() {
                this.$refs['formItem'].resetFields()
            },
            handleSave() {
                this.$refs['formItem'].validate().then(isValid => {
                    if (isValid) {
                        let http = ''
                        this.buttons.filter(item => item.code === "Save")[0].loading = true
                        if (this.sid) {
                            http = this.$http.put(`${deltaApi.deltaUrl.deltaOcrTrans.update}/${this.sid}`, this.formItem, {noIntercept: true})
                        } else {
                            http = this.$http.post(deltaApi.deltaUrl.deltaOcrTrans.insert, this.formItem, {noIntercept: true})
                        }
                        http.then(res => {
                            if (res.data.success) {
                                this.$Message.success(res.data.message)
                                this.handleBack()
                            } else {
                                this.$Message.error(res.data.message)
                            }
                        }, () => {}).finally(() => {
                            this.buttons.filter(item => item.code === "Save")[0].loading = false
                        })
                    }
                })
            },
            handleBack() {
                this.$emit('onEditBack', {showList: true})
            },
            // decimalKeypress,decimalKeyup,numberKeypress,numberKeyup
        }
    }
</script>
<style lang="less" scoped>
</style>
