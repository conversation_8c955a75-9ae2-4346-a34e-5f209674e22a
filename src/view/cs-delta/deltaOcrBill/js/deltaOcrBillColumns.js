import { operationRendererOwner } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'
import { deltaConstant } from '@/view/cs-delta/api'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    return {
      totalColumns: [
        {
          title: 'EN号码',
          minWidth: 100,
          align: 'center',
          key: 'enNo',
          cellRendererFramework: operationRendererOwner(this, [{ title: '', handle: 'viewCompareResult', marginRight: '0' }])
        },
        {
          title: 'SHIP TO',
          minWidth: 120,
          align: 'center',
          key: 'shipTo',
        },
        {
          title: '收货人',
          minWidth: 120,
          align: 'center',
          key: 'consignee',
        },
        {
          title: '通知人',
          minWidth: 120,
          align: 'center',
          key: 'notifyParty',
        },
        {
          title: '启运港',
          minWidth: 120,
          align: 'center',
          key: 'despPort',
        },
        {
          title: '标记唛码',
          minWidth: 120,
          align: 'center',
          key: 'markNo',
        },
        {
          title: '总毛重',
          minWidth: 120,
          align: 'center',
          key: 'grossWt',
        },
        {
          title: '总体积',
          minWidth: 120,
          align: 'center',
          key: 'volume',
        },
        {
          title: '外包装数量',
          minWidth: 120,
          align: 'center',
          key: 'outerPackaging',
        },
        {
          title: '主要品名',
          minWidth: 120,
          align: 'center',
          key: 'gName',
        },
        // {
        //   title: '接收记录表头表中的SID',
        //   minWidth: 120,
        //   align: 'center',
        //   key: 'headId',
        // },
        {
          title: '数据状态',
          minWidth: 120,
          align: 'center',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, deltaConstant.ocrStatus)
          },
        },
        {
          title: '比对完成时间',
          minWidth: 120,
          align: 'center',
          key: 'compareTime',
          render: (h, params) => {
            return h('span', params.row.compareTime)
          }
        },
        {
          title: '比对结果',
          minWidth: 120,
          align: 'center',
          key: 'compareResult',
          render: (h, params) => {
            return this.cmbShowRender(h, params, deltaConstant.compareReuslt)
          },
        },
        {
          title: '托书获取时间',
          minWidth: 120,
          align: 'center',
          key: 'bookingGetTime',
          render: (h, params) => {
            return h('span', params.row.bookingGetTime)
          }
        },
        {
          title: '邮件通知状态',
          minWidth: 120,
          align: 'center',
          key: 'mailSendFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, deltaConstant.emailNotifyStatus)
          },
        },
        {
          title: '邮件发送时间',
          minWidth: 120,
          align: 'center',
          key: 'mailSendTime',
          render: (h, params) => {
            return h('span', params.row.mailSendTime)
          }
        },
        {
          title: '提单识别时间',
          minWidth: 120,
          align: 'center',
          key: 'scanTime',
          render: (h, params) => {
            return h('span', params.row.scanTime)
          }
        },
        {
          title: '企业编码',
          minWidth: 120,
          align: 'center',
          key: 'tradeCode',
        },
        {
          title: '创建人名称',
          minWidth: 120,
          align: 'center',
          key: 'insertUserName',
        },
        {
          title: '修改人名称',
          minWidth: 120,
          align: 'center',
          key: 'updateUserName',
        },
      ]
    }
  }
}
export {
  columns,
}
