<template>

  <div>
    <div style="display: flex; justify-content: space-between; line-height:25px; height: 30px; border: 1px solid #d7dde4;">
      <div>
        <Icon type="ios-book-outline" size="18"  > </Icon>
        <span  style="font-size: 13px; margin: 0 5px">比对结果</span>
      </div>
      <div style="padding-right: 5px">
        <a href="#"  @click="editBack" >
          <XdoIcon type="ios-undo" size="22" style="color: green;"/>
        </a>
      </div>
    </div>

    <div style="display: flex; justify-content: space-between; line-height: 25px; height: 30px;">
<!--      <Layout>-->
        <Content style="width: 0px;">
        <xdo-ag-grid ref="ref_agGrid"
                     :columns="gridConfig.gridColumns"
                     :data="gridConfig.data"
                     :height="dynamicHeight"
                     rowSelection='single'
                     @cellClicked="handleClickGridCell"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        </Content>


      <Sider ref="side1" hide-trigger collapsible style="background-color: white ; border-left: #c5c8ce solid 1px"
             :width="500" :collapsed-width="30"
             v-model="isCollapsed">
        <Icon @click.native="collapsedSider" :class="rotateIcon" type="md-menu" size="24"></Icon>
        <span style="font-size: 13px; margin-left: 5px">源文件</span>
        <PreviewPdf :locationsData="locationsData"></PreviewPdf>
      </Sider>
<!--      </Layout>-->

    </div>
  </div>

</template>

<script>
import PreviewPdf from '@/view/cs-ocr/gwOcrInvoiceI/components/PreviewPdf'
import { deltaApi } from '../api'
// import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
// import { getKeyValue } from '@/libs/util'

export default {
  name: 'DeltaOcrBillCompare',
  // mixins: [columnRender, getKeyValue],
  components: {
    PreviewPdf,
  },
  props: {
    headId: {
      type: String,
      default: '',
      // required: true,
    },
  },
  data() {
    return {
      isNative: false,
      gridConfig: {
        gridColumns: [
          {
            title: '栏位信息',
            width: 180,
            key: 'fieldName',
            // render: (h, params) => {
            //   return this.cmbShowRender(h, params, deltaConstant.fieldDesc)
              // return h('span', deltaConstant.fieldDesc.filter(it => it.value === params.value)[0].label)
            // },
          },
          {
            title: '托书',
            width: 180,
            key: 'bookingValue',
          },
          {
            title: '提单识别',
            width: 180,
            key: 'billValue',
            cellClassRules: {
              'demo-table-error-row': (params) => { return params.data.compareResult !== '1' }
            }
          },
        ],
        data: [],

      },
      // gridOptions: {
      //   getRowStyle: this.rowClassName
      // },
      isCollapsed: true,
      locationsData: {},

      ajaxUrl: {
        list: deltaApi.deltaUrl.deltaOcrBillRes.list
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      let params = {headId: this.headId}
      this.$http.post(this.ajaxUrl.list, params
      ).then(res => {
        this.gridConfig.data = res.data.data
      }).catch(() => {
        //
      }).finally(() => {
        //
      })
    })
  },
  methods: {
    editBack() {
      this.$emit('onEditBack')
    },
    /**
     * 选中行事件
     * @param params 选中的数据
     */
    handleSelectionChanged(params) {
      let selectRow = params.api.getSelectedRows()
      this.$emit('onSelectRow', selectRow[0])
    },
    collapsedSider() {
      this.isCollapsed = !this.isCollapsed
    },
    // rowClassName(row) {
    //   if (row.bookingValue !== row.billValue) {
    //     return 'demo-table-error-row'
    //   }
    //   return ''
    // },

    /**
     *  点击 grid 列 事件
     * @param event
     */
    handleClickGridCell(event) {
      let locData = event.data.loc
      if (!this.isJsonMe(locData)) {
        this.$Message.warning('此栏位未识别!')
        return
      }
      this.locationsData = JSON.parse(locData)
      // 自动弹出
      if (this.isCollapsed) {
        this.isCollapsed = !this.isCollapsed
      }
    },
    /**
     * 判断字符串 是否是 一个合格的 json
     * @param str
     * @returns {boolean}
     */
    isJsonMe(str) {
      if (typeof str == 'string') {
        try {
          let obj = JSON.parse(str)
          if (typeof obj == 'object' && obj) {
            return true
          } else {
            return false
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e)
          return false
        }
      }
      else {
        return false
      }
    },

  },
  computed: {
    rotateIcon() {
      return [
        'menu-icon',
        this.isCollapsed ? 'rotate-icon' : ''
      ]
    },
    dynamicHeight() {
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 40

      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight


      return hiddenHeight
    }
  }
}
</script>

<style scoped>
/deep/ .demo-table-error-row {
  background-color: #ed4014;
  color: #fff;
}

</style>
