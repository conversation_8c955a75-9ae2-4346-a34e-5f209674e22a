<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="enNum" label="EN号码" xid="c_head_search_form_item">
        <XdoIInput type="text" v-model="searchParam.enNo" xid="s_enNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="consignee" label="收货人" xid="c_head_search_form_item">
        <XdoIInput type="text" v-model="searchParam.consignee" xid="s_consignee"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="despPort" label="启运港" xid="c_head_search_form_item">
        <XdoIInput type="text" v-model="searchParam.despPort" xid="s_despPort"></XdoIInput>
      </XdoFormItem>
<!--      (1.提单已识别 2.托书已获取 3.托书获取失败 4.比对成功 5.比对失败)-->
      <XdoFormItem prop="status" label="数据状态" xid="c_head_search_form_item">
<!--        <XdoIInput type="text" v-model="searchParam.status" xid="s_status"></XdoIInput>-->
        <xdo-select v-model="searchParam.status" :options="this.selectBoxData.ocrStatus" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
<!--      (0.未比对 1.一致 2.不一致)-->
      <XdoFormItem prop="compareResult" label="比对结果" xid="c_head_search_form_item">
<!--        <XdoIInput type="text" v-model="searchParam.compareResult" xid="s_compareResult"></XdoIInput>-->
        <xdo-select v-model="searchParam.compareResult" :options="this.selectBoxData.compareReuslt" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <DcDateRange label="提单识别时间" @onDateRangeChanged="handleDateChange"></DcDateRange>
    </XdoForm>
  </section>
</template>
<script>
import DcDateRange from '@/components/dc-date-range/dc-date-range'
import { deltaConstant } from '@/view/cs-delta/api'
export default {
  name: 'DeltaOcrBillSearch',
  components: {
    DcDateRange
  },
  data() {
    return {
      selectBoxData: deltaConstant,
      searchParam: {
        enNo: '',
        consignee: '',
        despPort: '',
        status: '',
        compareResult: '',
        scanTimeFrom: '',
        scanTimeTo: '',
      }
    }
  },
  mounted() {
  },
  methods: {
    handleDateChange(values) {
      this.searchParam.scanTimeFrom = values[0]
      this.searchParam.scanTimeTo = values[1]
    }
  }
}
</script>
<style scoped>
</style>
