<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show='showSearch'>
          <div class='separateLine'></div>
          <!--查询条件-->
          <DeltaOcrBillSearch ref='headSearch'></DeltaOcrBillSearch>
        </div>
      </XdoCard>

      <XdoCard :bordered='false'>
        <div class='action' ref='area_actions'>
          <!--工具栏-->
          <xdo-toolbar @click='handleToolbarClick' :action-source='actions'></xdo-toolbar>
        </div>
      </XdoCard>


      <XdoCard :bordered='false'>
        <!--        gird-->
        <xdo-ag-grid ref='ref_agGrid' :checkboxSelection="checkboxSelection"
                     rowSelection='multiple'
                     :columns='gridConfig.gridColumns'
                     :data='gridConfig.data'
                     :height='dynamicHeight'
                     :GridReady='handleGridReady'
                     @selectionChanged='handleSelectionChanged'>
        </xdo-ag-grid>
        <div ref='area_page'>
          <!--分页-->
          <XdoPage class='dc-page' show-total show-sizer
                   :page-size-opts='pageSizeOpts'
                   :current='pageParam.page'
                   :page-size='pageParam.limit'
                   :total='pageParam.dataTotal'
                   @on-change='pageChange'
                   @on-page-size-change='pageSizeChange'/>
        </div>
      </XdoCard>
    </div>

    <div v-if="!showList">
      <DeltaOcrBillCompare @onEditBack="onEditBack" :headId="currHeadId"></DeltaOcrBillCompare>
    </div>
    <!--自定义配置列-->
    <TableColumnSetup v-model='showTableColumnSetup'
                      :resId='tableId'
                      @updateColumns='handleUpdateColumn'
                      :columns='totalColumns'
                      class='height:500px'/>
  </section>
</template>
<script>
import { commonMain } from '@/view/cs-acustomization/common/comm/commonMain'
import { columns } from './js/deltaOcrBillColumns'
import DeltaOcrBillSearch from './DeltaOcrBillSearch'
import DeltaOcrBillCompare from './DeltaOcrBillCompare'
import { deltaApi } from '../api'

export default {
  name: 'DeltaOcrBillHisMain',
  moduleName: '提单OCR识别',
  components: {
    DeltaOcrBillSearch,
    DeltaOcrBillCompare
  },
  mixins: [columns, commonMain],
  data() {
    return {
      isShowOpView: false, //
      gridConfig: {
        exportTitle: '提单OCR识别数据'
      },
      isShowSetting: true,

      searchLines: 3,
      toolbarEventMap: {
        'export': this.handleExport,
        'getBooking': this.handleGetBooking,
        'compare': this.handleCompare,
        'resendEmail': this.handleResendEmail,
      },
      ajaxUrl: {
        export: deltaApi.deltaUrl.deltaOcrBill.export,
        list: deltaApi.deltaUrl.deltaOcrBill.list,
        compare: deltaApi.deltaUrl.deltaOcrBill.compare,
        resendEmail: deltaApi.deltaUrl.deltaOcrBill.resendEmail,
        getBooking: deltaApi.deltaUrl.deltaOcrBill.getBooking,
      },
      currHeadId: '',
    }
  },
  methods: {
    onEditBack() {
      this.showList = true
    },
    /**
     * 删除
     */
    handleDelete() {
      this.handleCommonDelete(this.ajaxUrl.delete, 'delete')
    },
    /**
     * 导出 按钮 的 事件
     */
    handleExport() {
      this.handleCommonExport(this.ajaxUrl.export, 'export')
    },

    /**
     * 获取托书
     */
    handleGetBooking() {
      if (this.handleCommonCheckRowSelected('获取托书')) {
        let params = this.handleSelectRowsSids()
        this.$http.post(`${this.ajaxUrl.getBooking}/${params}`).then(() => {
          this.$Message.success('托书已获取！')
          this.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          this.handleCommonSetButtonLoading(btnIndexOrKey, false)
        })
      }
    },
    /**
     * 邮件重发
     */
    handleResendEmail() {
      if (this.handleCommonCheckRowSelected('邮件重发')) {
        let params = this.handleSelectRowsSids()
        this.$http.post(`${this.ajaxUrl.resendEmail}/${params}`).then(() => {
          this.$Message.success('邮件已发送！')
          this.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          this.handleCommonSetButtonLoading(btnIndexOrKey, false)
        })
      }
    },
    /**
     * 比对
     */
    handleCompare() {
      if (this.handleCommonCheckRowSelected('比对')) {
        let params = this.handleSelectRowsSids()
        this.$http.post(`${this.ajaxUrl.compare}/${params}`).then(() => {
          this.$Message.success('比对完成！')
          this.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          this.handleCommonSetButtonLoading(btnIndexOrKey, false)
        })
      }
    },
    /**
     * 查看比对结果
     */
    viewCompareResult(params) {
      this.showList = !this.showList
      this.currHeadId = params.data.sid
    },

  }
}
</script>


<style lang='less' scoped>
/**
 点击 查询条件按钮 的时候显示的 虚线
 */
.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
