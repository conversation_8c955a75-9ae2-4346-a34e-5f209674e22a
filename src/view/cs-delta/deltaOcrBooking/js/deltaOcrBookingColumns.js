import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: 'EN号码',
        minWidth: 120,
        align: 'center',
        key: 'enNo',
      },
      {
        title: 'SHIP TO',
        minWidth: 120,
        align: 'center',
        key: 'shipTo',
      },
      {
        title: '收货人',
        minWidth: 120,
        align: 'center',
        key: 'consignee',
      },
      {
        title: '通知人',
        minWidth: 120,
        align: 'center',
        key: 'notifyParty',
      },
      {
        title: '启运港',
        minWidth: 120,
        align: 'center',
        key: 'despPort',
      },
      {
        title: '标记唛码',
        minWidth: 120,
        align: 'center',
        key: 'markNo',
      },
      {
        title: '总毛重',
        minWidth: 120,
        align: 'center',
        key: 'grossWt',
      },
      {
        title: '总体积',
        minWidth: 120,
        align: 'center',
        key: 'volume',
      },
      {
        title: '外包装数量',
        minWidth: 120,
        align: 'center',
        key: 'outerPackaging',
      },
      {
        title: '主要品名',
        minWidth: 120,
        align: 'center',
        key: 'gName',
      },
      {
        title: '企业编码',
        minWidth: 120,
        align: 'center',
        key: 'tradeCode',
      },
      {
        title: '创建人名称',
        minWidth: 120,
        align: 'center',
        key: 'insertUserName',
      },
      {
        title: '托书获取时间',
        minWidth: 120,
        align: 'center',
        key: 'insertTime',
        render: (h, params) => {
          return h('span', params.row.insertTime)
        }
      },
      {
        title: '修改人名称',
        minWidth: 120,
        align: 'center',
        key: 'updateUserName',
      },
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}
export {
  columns,
}
