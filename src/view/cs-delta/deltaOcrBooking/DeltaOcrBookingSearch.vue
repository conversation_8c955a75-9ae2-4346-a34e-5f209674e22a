<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam"
             label-position="right" :label-width="120" inline>

      <XdoFormItem prop="enNo" label="EN号码">
        <XdoIInput type="text" v-model="searchParam.enNo"></XdoIInput>
      </XdoFormItem>

      <DcDateRange label="托书获取时间" @onDateRangeChanged="handleDateChange"></DcDateRange>
    </XdoForm>
  </section>
</template>
<script>
import DcDateRange from '@/components/dc-date-range/dc-date-range'

export default {
  name: 'DeltaOcrBookingSearch',
  components: {
    DcDateRange
  },
  data() {
    return {
      searchParam: {
        enNo: '',
        insertTimeFrom: '',
        insertTimeTo: '',
      }
    }
  },
  mounted() {
  },
  methods: {
    handleDateChange(values) {
      this.searchParam.insertTimeFrom = values[0]
      this.searchParam.insertTimeTo = values[1]
    }
  }
}
</script>
<style scoped>
</style>
