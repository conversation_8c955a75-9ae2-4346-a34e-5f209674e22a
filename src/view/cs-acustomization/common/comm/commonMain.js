import pms from '@/libs/pms'
import { excelExport } from '@/api'
import { editStatus } from '@/view/cs-common'
import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'


export const commonMain = {
  mixins: [pms],
  data() {
    return {
      gridOptions: {},
      p_group: 'default', //按钮的 组名  跟PMS有关
      isShowOpView: true,  //在没有编辑权限的时候  是否显示查看按钮 = 操作列
      isShowSetting: false, // 自定义配置 按钮
      showTableColumnSetup: false, //控制 自定义设置 页面 显示
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup // 自定义设置列 事件
      },

      ajaxUrl: { // 调用后台相关URL
        list: '', // 查询
        selectBySid: '' //sid 查询单个对象  get
      },
      isSelectBySid: false,  //在跳转到 详情页面的时候  是否 从后端捞取数据 若配置为:true  需要设置  this.ajaxUrl.selectBySid 值的

      actions: [], // 操作按钮


      showList: true, // 列表界面是否显示
      checkboxSelection: true, //是否 显示 选择框
      gridConfig: { // 列表相关配置
        data: [],
        selectRows: [],
        gridColumns: [],
        exportColumns: [],
        exportTitle: '导出文件名称'
      },
      pageParam: {    // 分页相关
        page: 1,
        limit: 20,
        dataTotal: -1
      },
      pageSizeOpts: [10, 20, 50, 100],

      editConfig: { // 传入编辑界面的信息
        headId: '',
        editData: {},
        editStatus: editStatus.SHOW
      },

      initSearch: true, // 初始化即查询
      showSearch: false, // 是否显示查询条件
      searchLines: 3, // 查询条件行数

      hasActions: true, // 是否有按钮行
      hasPager: true, // 是否有分页行
      hasChildTabs: false, // 是否有子Tab组件

      OffsetHeight: 0,  // (列表高度)偏移值
      timer: null, //定时
      tableId: ''  //获取 自定义列设置的 key器 用于防抖

    }
  },
  mounted: function() {


    //查询
    if (this.initSearch) {
      this.$nextTick(() => {
        this.handleCommonSubmitSearchBeforeInMounted()
        this.handleSearchSubmit()
      })
    }
    //加载 按钮
    this.loadFunctions(this.p_group).then(() => {
      this.handleCommonLoadFunctionsAfterOne()
      this.addSettingButton() //自定义列设置
      this.handleCommonLoadOtherActions()
      this.setShowFields(this.totalColumns)
    })

  },
  methods: {

    /**
     * 用于重绘head rows等功能 调用官网api
     * @param params
     */
    handleGridReady(params) {

      this.gridOptions = params
      // this.gridOptions.api.redrawRows() //重绘
    },

    /**
     * mounted => 查询前 调用事件
     */
    handleCommonSubmitSearchBeforeInMounted() {
    },

    /**
     *加载 pms 按钮后 第一个调用的方法
     */
    handleCommonLoadFunctionsAfterOne() {

    },

    /**
     * 加载  其他自定义按钮
     */
    handleCommonLoadOtherActions() {

    },

    /**
     * 查询 后 success
     */
    handleCommonAfterSearchSuccess() {
      // console.info('查询成功后执行的操作')
    },

    /**
     * 查询 后
     */
    handleCommonAfterSearchCatch() {
      // console.info('查询失败后执行的操作')
    },

    /**
     * 查询执行完成后的默认操作
     */
    handleCommonAfterSearchFinally() {
      // console.info('查询执行完成后的默认操作')
    },

    /**
     * pms 加载完之后 加载 自定义按钮 actionLoad
     */
    addSettingButton() {
      if (this.isShowSetting) {
        this.actions.push({
          needed: true,
          loading: false,
          disabled: false,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      }
    },

    /**
     * 设置 列表 显示 字段
     * @param totalColumns
     * @param defaultColumns
     */
    setShowFields(totalColumns, defaultColumns) {
      let me = this
      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = []
      if (Array.isArray(defaultColumns) && defaultColumns.length > 0) {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns, defaultColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns)
      }
      me.handleUpdateColumn(columns)
    },

    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showTableColumnSetup = true
    },

    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let edit = this.actions.filter(item => {
        return item.command === 'edit'
      })

      let bascol = []
      if (edit && edit.length > 0) {
        if (this.checkboxSelection) {
          bascol = [{
            title: '操作',
            fixed: 'left',
            width: 120,
            align: 'center',
            key: 'operation',
            cellRendererFramework: operationRenderer(this)
          }]
        }
      } else {
        bascol = this.isShowOpView ? [{
          title: '操作',
          fixed: 'left',
          width: 90,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this, [{ title: '查看', handle: 'handleViewByRow', marginRight: '0' }])
        }] : []
      }
      this.gridConfig.gridColumns = [...bascol, ...columns]
      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },

    /**
     * AG 选中行变化事件
     * @param params
     */
    handleSelectionChanged(params) {
      this.handleSelectionChange(params.api.getSelectedRows())
    },

    /**
     * toolBar 编辑
     */
    handleCommonEdit() {
      if (this.handleCommonCheckRowSelected('编辑', true)) {
        this.handleEditByRow(this.gridConfig.selectRows[0])
      }
    },

    /**
     * 行内 编辑
     */
    handleEditByRow(row) {
      if (this.isSelectBySid) {
        this.$http.get(this.ajaxUrl.selectBySid + `/${row.sid}`).then(res => {
          row = res.data.data
        })
      }
      this.editConfig.editStatus = editStatus.EDIT
      this.editConfig.editData = row
      this.showList = false
    },

    /**
     * 行内 查看
     */
    handleViewByRow(row) {
      if (this.isSelectBySid) {
        this.$http.get(this.ajaxUrl.selectBySid + `/${row.sid}`).then(res => {
          row = res.data.data
        })
      }
      this.editConfig.editStatus = editStatus.SHOW
      this.editConfig.editData = row
      this.showList = false
    },


    /**
     * 显示/隐藏查询条件
     */
    handleShowSearch() {
      this.showSearch = !this.showSearch
    },

    /**
     * 查询 按钮事件
     */
    handleSearchSubmit() {
      this.pageParam.page = 1
      this.debounce(this.getList, 1000)()
    },

    /**
     * 执行数据查询
     */
    getList() {
      if (!this.ajaxUrl.list) {
        console.error('查询api不能为空!')
        return
      }
      this.doSearch(this.ajaxUrl.list)
    },

    /**
     * 执行查询
     * @param searchUrl 调用地址
     */
    doSearch(searchUrl) {
      this.$nextTick(() => {
        let params = this.handleCommonGetSearchParams()
        this.$http.post(searchUrl, params, {
          params: {
            ...this.pageParam
          }
        }).then(res => {
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
          this.handleCommonAfterSearchSuccess()
        }).catch(() => {
          this.handleCommonAfterSearchCatch()
        }).finally(() => {
          this.gridConfig.selectRows = []
          this.handleCommonAfterSearchFinally()
        })
      })
    },


    /**
     * 获取查询条件(可外部覆盖)
     * @returns {*}
     */
    handleCommonGetSearchParams() {
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
    },

    /**
     * 防抖
     * @param fn  调用方法
     * @param delay 几毫秒以后点击 有效
     * @returns {(function(): void)|*}
     */
    debounce(fn, delay) {
      let me = this
      return function() {
        clearTimeout(me.timer)
        let call = !me.timer
        if (call) {
          fn.call(this)
        }
        me.timer = setTimeout(function() {
          me.timer = false
        }, delay)
      }
    },

    /**
     * 新增
     */
    handleCommonAdd() {
      this.editConfig.editData = {}
      this.gridConfig.selectRows = []
      this.editConfig.editStatus = editStatus.ADD
      this.showList = false
    },

    /**
     * 是否存在被选中的行
     * @param opTitle (操作标签)
     * @param isSingle (是否有且仅有一条)
     */
    handleCommonCheckRowSelected(opTitle, isSingle) {
      if (isSingle !== true) {
        isSingle = false
      }
      if (!opTitle) {
        opTitle = '操作'
      }
      if (!this.gridConfig.selectRows.length) {

        this.$Message.warning({
          content: '未选择数据, 请选择您要' + opTitle + '的数据!',
          duration: 4
        })
        return false
      }
      if (isSingle === true && this.gridConfig.selectRows.length > 1) {
        this.$Message.warning({
          content: '一次仅能' + opTitle + '一条数据！',
          duration: 4
        })

        return false
      }
      return true
    },

    /**
     * 設置按鈕是否進入加載狀態
     * @param indexOrKey
     * @param flag
     */
    handleCommonSetButtonLoading(indexOrKey, flag) {
      if (typeof indexOrKey === 'number' && indexOrKey > -1 && indexOrKey < this.actions.length) {
        this.actions[indexOrKey].loading = flag
      } else if (typeof indexOrKey === 'string' && indexOrKey) {
        if (typeof this.setToolbarLoading === 'function') {
          this.setToolbarLoading(indexOrKey, flag)
        }
      }
    },

    /**
     * 执行导出
     * @param exportUrl
     * @param btnIndexOrKey
     * @param finallyFun
     */
    handleCommonExport(exportUrl, btnIndexOrKey, finallyFun) {
      this.$nextTick(() => {
        this.handleCommonSetButtonLoading(btnIndexOrKey, true)
        let params = this.handleCommonGetSearchParams()
        excelExport(exportUrl, {
          exportColumns: params,
          name: this.gridConfig.exportTitle,
          header: this.gridConfig.exportColumns
        }).finally(() => {
          this.handleCommonSetButtonLoading(btnIndexOrKey, false)
          if (typeof finallyFun === 'function') {
            finallyFun.call(this)
          }
        })
      })
    },

    /**
     * 获取选中数据的参数组(可外部覆盖)
     */
    handleSelectRowsSids() {
      return this.gridConfig.selectRows.map(item => {
        return item.sid
      })
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnIndexOrKey
     */
    handleCommonDelete(delUrl, btnIndexOrKey) {
      if (this.handleCommonCheckRowSelected('删除')) {
        this.$Modal.confirm({
          title: '提醒',
          content: '确认删除所选项吗',
          okText: '删除',
          cancelText: '取消',
          onOk: () => {
            this.handleCommonSetButtonLoading(btnIndexOrKey, true)
            let params = this.handleSelectRowsSids()
            this.$http.delete(`${delUrl}/${params}`).then(() => {
              this.$Message.success('删除成功！')
              this.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              this.handleCommonSetButtonLoading(btnIndexOrKey, false)
            })
          }
        })
      }
    },

    /**
     * 行选中 或 取消选中
     * @param selectRows
     */
    handleSelectionChange(selectRows) {
      this.gridConfig.selectRows = selectRows
    },

    /**
     * 页次切换
     * @param page
     */
    pageChange(page) {
      this.pageParam.page = page
      this.getList()
    },

    /**
     * 切换每页记录数
     * @param pageSize
     */
    pageSizeChange(pageSize) {
      this.pageParam.limit = pageSize
      if (this.pageParam.page === 1) {
        this.getList()
      }
    },

    /**
     * 返回 列表页 事件处理
     * editData: {},
     * showList: true,
     * editStatus: editStatus.SHOW
     * @param backObj
     */
    handleCommonEditBack(backObj) {
      this.showList = backObj.showList
      if (this.showList) {
        this.getList()
      }
      //this.editConfig.editStatus = backObj.editStatus
      //this.editConfig.editData = backObj.editData
    }
  },

  computed: {
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight() {
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 0
      if (this.hasChildTabs) {
        childTabHeight = 38
      }
      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight
      // 去除按鈕行高度: 28px(需要添加2px: 与上下之間的空隙)
      if (this.hasActions) {
        hiddenHeight = hiddenHeight - 30
      }
      // 去除分頁信息行高度: 28px
      if (this.hasPager) {
        hiddenHeight = hiddenHeight - 28
      }
      // 減去偏移量(某些特殊情況使用)
      hiddenHeight = hiddenHeight - this.OffsetHeight - 2
      // 當查詢條件塊打開時候去除其高度
      if (this.showSearch === true) {
        // 與上方間隙高度
        let gapHeight = 6
        // 分行高度
        let lineHeight = 28
        return hiddenHeight - gapHeight - lineHeight * this.searchLines
      }
      return hiddenHeight
    }
  }
}
