import pms from '@/libs/pms'
import { csAPIShengyi } from '../../api'
import { commColumnsCustom } from '@/view/cs-acustomization/common/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 3,
      isShowOp: false,
      isShowSetting: true,
      checkboxSelection: false,
      gridConfig: {
        exportTitle: '出口通知单'
      },
      toolbarEventMap: {
        'export': this.handleDownload   // '导出'
      },
      ajaxUrl: {
        exportUrl: csAPIShengyi.taxCip.exportUrl,
        selectAllPaged: csAPIShengyi.taxCip.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
