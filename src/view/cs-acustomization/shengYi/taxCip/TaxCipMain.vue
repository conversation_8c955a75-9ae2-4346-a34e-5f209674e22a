<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <TaxCipSearch ref="headSearch"></TaxCipSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" rowSelection="multiple" :height="dynamicHeight"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :checkboxSelection="checkboxSelection"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange" />
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns" class="height: 500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { mainJS } from './js/taxCipMain'
  import TaxCipSearch from './TaxCipSearch'
  import { columns } from './js/taxCipColumns'

  export default {
    name: 'TaxCipMain',
    moduleName: '出货通知单',
    components: {
      TaxCipSearch
    },
    mixins: [columns, mainJS]
  }
</script>

<style lang='less' scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
