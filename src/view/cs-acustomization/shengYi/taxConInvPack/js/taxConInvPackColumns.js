import { taxConInvPack } from '@/view/cs-acustomization/shengYi/api/constant'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '日期', width: 130, key: 'mdate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },
      { title: '发运单号', width: 130, key: 'billNo' },
      { title: '客户采购订单', width: 130, key: 'po' },
      { title: '卡板数', width: 130, key: 'kqty' },
      { title: '客户物料', width: 130, key: 'ccopGNo' },
      { title: '客户项号', width: 130, key: 'cgNo' },
      { title: '物料描述', width: 130, key: 'gmark' },
      { title: '数量', width: 130, key: 'qty1' },
      { title: '单位', width: 130, key: 'unit' },
      { title: '净重', width: 130, key: 'net' },
      { title: '毛重', width: 130, key: 'gross' },
      { title: '金额', width: 130, key: 'decTotal' },
      { title: '币别', width: 130, key: 'decCurr' },
      { title: '企业物料代码', width: 130, key: 'sapCopGNo' },
      { title: '海关物料代码', width: 130, key: 'copGNo' },
      { title: '数量', width: 130, key: 'qty2' },
      { title: '客户代码', width: 130, key: 'custId' },
      { title: '客户名称', width: 130, key: 'custName' },
      { title: '合同备案号', width: 130, key: 'contractNo' },
      { title: '发票号码', width: 130, key: 'invoiceNo' },
      { title: '箱单号码', width: 130, key: 'packingNo' },
      { title: '中文品名', width: 130, key: 'gnameCh' },
      { title: '单价', width: 130, key: 'decP1' },
      { title: '单价', width: 130, key: 'decP2' },
      { title: '英文品名', width: 130, key: 'gnameEn' },
      { title: '归并序号', width: 130, key: 'mergeId' },
      { title: '表体序号', width: 130, key: 'seq' },
      {
        title: '新美亚客户', width: 130, key: 'isXmy',
        render: (h, params) => {
          return this.cmbShowRender(h, params, taxConInvPack.isXmy)
        }
      },
      { title: '备案号', width: 130, key: 'emsNo' }
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}
