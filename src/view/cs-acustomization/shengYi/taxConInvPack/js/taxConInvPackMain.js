import pms from '@/libs/pms'
import { csAPIShengyi } from '@/view/cs-acustomization/shengYi/api'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import { commColumnsCustom } from '@/view/cs-acustomization/common/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  data() {
    let importConfig = this.getCommImportConfig('CUS_STATUS_IMP')
    return {
      // 查询条件行数
      searchLines: 1,
      isShowOp: false,
      isShowSetting: true,
      // 是否显示导入页面
      modelImportShow: false,
      gridConfig: {
        exportTitle: '客户对账单'
      },
      importConfig: importConfig,
      importKey: 'CUS_STATUS_IMP',        // 自己
      toolbarEventMap: {
        'delete': this.handleDelete,      // '删除'
        'import': this.handleImport,      // '导入'
        'extract': this.handleExtract     // '引入'
      },
      ajaxUrl: {
        delete: csAPIShengyi.taxConInvPack.delete,
        exportUrl: csAPIShengyi.taxConInvPack.exportUrl,
        extract: csAPIShengyi.taxConInvPack.selectAllPaged,
        selectAllPaged: csAPIShengyi.taxConInvPack.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 弹出导入窗体
     */
    handleImport() {
      this.modelImportShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      this.modelImportShow = false
      this.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
