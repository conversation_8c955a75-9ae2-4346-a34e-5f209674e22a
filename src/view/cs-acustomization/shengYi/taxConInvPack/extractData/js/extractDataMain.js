import pms from '@/libs/pms'
import { csAPIShengyi } from '@/view/cs-acustomization/shengYi/api'
import { commColumnsCustom } from '@/view/cs-acustomization/common/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      isShowOp: false,
      initSearch: false,
      gridConfig: {
        exportTitle: '客户对账单'
      },
      toolbarEventMap: {
        'saveExtract': this.handleSaveExtract,        // '保存'
        'selectAllPaged': this.handleSearchSubmit     // '引入'
      },
      ajaxUrl: {
        saveExtract: csAPIShengyi.taxConInvPack.extract,
        selectAllPaged: csAPIShengyi.taxConInvPack.extractSelect
      }
    }
  },
  methods: {
    /**
     * 重写 获取查询阐述
     * @returns {*}
     */
    getSearchParams() {
      let searchData = Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
      searchData.invs = searchData.invs.split(/[(\r\n)\r\n]+/)
      return searchData
    },
    loadFunctionsAfterOne() {
      this.actions = []
      this.actions.push({
        ...this.actionsComm,
        label: '引入',
        key: 'xdo-btn-apply',
        command: 'selectAllPaged',
        icon: 'ios-cloud-done-outline'
      }, {
        ...this.actionsComm,
        label: '保存',
        icon: 'ios-add',
        key: 'xdo-btn-add',
        command: 'saveExtract'
      })
    },
    /**
     * 保存 引入数据
     */
    handleSaveExtract() {
      if (!this.gridConfig.data.length) {
        this.$Message.warning('保存失败：无数据!')
        return
      }
      let searchData = this.getSearchParams()
      // {emsNo}/{isXmy}/{invs}
      this.setButtonLoading('saveExtract', true)
      this.$http.post(`${this.ajaxUrl.saveExtract}`, searchData).then(() => {
        this.$Message.success('保存成功!')
        this.$emit('onConfirm')
      }).catch(() => {
      }).finally(() => {
        this.setButtonLoading('saveExtract', false)
      })
    }
  }
}
