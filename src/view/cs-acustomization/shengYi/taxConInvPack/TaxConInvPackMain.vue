<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <TaxConInvPackSearch ref="headSearch"></TaxConInvPackSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:export>
              <Dropdown trigger="click">
                <XdoButton type="text" style="font-size: 12px;" :loading="downloading">
                  <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                  导出
                  <XdoIcon type="ios-arrow-down"></XdoIcon>
                </XdoButton>
                <DropdownMenu slot="list">
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%; text-align: left;"
                               class="xdo-btn-download" @click="handleDownload" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      普通导出
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDown(1)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      客户合同
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDown(2)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      客户发票
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDown(3)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      客户箱单
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDown(6)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      专用发票
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDownloadBuild(4)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      报关数据
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload"
                               @click="handleDownloadBuild(5)" :loading="downloading">
                      <XdoIcon type="ios-code-download" size="22" class="xdo-icon"/>
                      其他报关数据
                    </XdoButton>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid"
                     :columns="gridConfig.gridColumns"
                     :data="gridConfig.data"
                     :height="dynamicHeight"
                     :checkboxSelection="checkboxSelection"
                     rowSelection='multiple'
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
    <DialogSelectClient :show.sync="showDialogSelectClient"
                        @onConfirm="handleDialogSelectClient"></DialogSelectClient>
    <DialogSelectInvNo :show.sync="showDialogSelectInvNo"
                       @onConfirm="handleDialogSelectInvNo"></DialogSelectInvNo>
    <DialogExtract :show.sync="showDialogExtract" @onConfirm="handleDialogExtract"></DialogExtract>
  </section>
</template>

<script>
  import ImportPage from 'xdo-import'
  import { excelExport } from '@/api'
  import DialogExtract from './DialogExtract'
  import { mainJS } from './js/taxConInvPackMain'
  import DialogSelectInvNo from './DialogSelectInvNo'
  import { columns } from './js/taxConInvPackColumns'
  import DialogSelectClient from './DialogSelectClient'
  import TaxConInvPackSearch from './TaxConInvPackSearch'
  import { csAPIShengyi } from '@/view/cs-acustomization/shengYi/api'

  export default {
    name: 'TaxConInvPackMain',
    moduleName: '客户对账单',
    components: {
      ImportPage,
      DialogExtract,
      DialogSelectInvNo,
      DialogSelectClient,
      TaxConInvPackSearch
    },
    mixins: [columns, mainJS],
    data() {
      return {
        exportType: 0,
        downloading: false,
        showDialogExtract: false,
        showDialogSelectInvNo: false,
        showDialogSelectClient: false
      }
    },
    methods: {
      handleExtract() {
        this.showDialogExtract = true
      },
      handleDialogExtract() {
        this.showDialogExtract = false
        this.handleSearchSubmit()
      },
      /**
       *导出
       * @param type
       * 1客户合同
       * 2客户发票
       * 3客户箱单
       * 4报关数据
       * 5其他报关数据
       */
      handleDown(type) {
        this.exportType = type
        this.showDialogSelectClient = true
      },
      /**
       *
       * @param selectData
       */
      handleDialogSelectClient(selectData) {
        let address = this.handleGetAddressByExport(this.exportType)
        this.downloading = true
        excelExport(`${address}/${selectData.cusCode}`, {
          name: '导出单证'
        }).finally(() => {
          this.downloading = false
        })
      },
      /**
       *而生成报关数据
       * @param type
       */
      handleDownloadBuild(type) {
        this.exportType = type
        this.showDialogSelectInvNo = true
      },
      handleDialogSelectInvNo(selectData) {
        let address = this.handleGetAddressByExport(this.exportType)
        this.downloading = true
        this.$http.post(`${address}/${selectData.invNo}`).then(() => {
          this.$Message.success('生成成功!')
        }).catch(() => {
        }).finally(() => {
          this.downloading = false
        })
      },
      /**
       * 根据 导出类型  确定 导出请求的Url地址
       * 1客户合同
       * 2客户发票
       * 3客户箱单
       * 4报关数据
       * 5其他报关数据
       */
      handleGetAddressByExport(exportType) {
        let address = ''
        switch (exportType) {
          case 1:
            address = csAPIShengyi.taxConInvPack.exportCon
            break
          case 2:
            address = csAPIShengyi.taxConInvPack.exportInv
            break
          case 3:
            address = csAPIShengyi.taxConInvPack.exportPac
            break
          case 4:
            address = csAPIShengyi.taxConInvPack.exportDec
            break
          case 5:
            address = csAPIShengyi.taxConInvPack.exportDecOther
            break
          case 6:
            address = csAPIShengyi.taxConInvPack.exportTnv
            break
          default:
        }
        return address
      }
    }
  }
</script>

<style lang="less" scoped>
  .xdo-btn-upload {
    text-align: left;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
