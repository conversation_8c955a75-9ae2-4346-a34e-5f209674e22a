<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'bookingManagementEdit',
    mixins: [baseDetailConfig],
    props: {
      inSource: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csMaterielCenter.singleLoss.body.insert,
          update: csAPI.csMaterielCenter.singleLoss.body.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function (disable) {
          let me = this
          me.fieldsReset()
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !disable
        }
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {
          ...this.inSource
        }
      }
    },
    methods: {
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
      },
      getFields() {
        return [{
          type: 'select',
          key: 'ucnsDclStat',
          title: '单耗申报状态',
          props: {
            disabled: true
          }
        }, {
          type: 'select',
          title: '发送状态',
          key: 'sendApiStatus',
          props: {
            disabled: true
          }
        }, {
          type: 'select',
          title: '处理标志',
          key: 'modifyMark',
          props: {
            disabled: true
          }
        }, {
          type: 'xdoInput',
          title: '成品序号',
          key: 'exgGNo',
          props: {
            disabled: true
          }
        }, {
          title: '成品料号',
          key: 'copExgNo',
          props: {
            disabled: true
          }
        }, {
          key: 'exgGName',
          title: '成品商品名称',
          props: {
            disabled: true
          }
        }, {
          title: '单耗版本号',
          key: 'exgVersion',
          props: {
            disabled: true
          }
        }, {
          type: 'xdoInput',
          title: '料件序号',
          key: 'imgGNo',
          props: {
            disabled: true
          }
        }, {
          title: '料件料号',
          key: 'copImgNo',
          props: {
            disabled: true
          }
        }, {
          title: '料件商品名称',
          key: 'imgGName',
          props: {
            disabled: true
          }
        }, {
          type: 'xdoInput',
          title: '净耗',
          key: 'decCm',
          required: true,
          props: {
            intDigits: 9,
            precision: 9
          }
        }, {
          type: 'xdoInput',
          title: '有形损耗率(%)',
          key: 'decDmVisiable',
          props: {
            intDigits: 2,
            precision: 9
          }
        }, {
          type: 'xdoInput',
          title: '无形损耗率(%)',
          key: 'decDmInvisiable',
          props: {
            intDigits: 2,
            precision: 9
          }
        }, {
          type: 'xdoInput',
          title: '保税料件比例(%)',
          key: 'bondMtpckPrpr',
          props: {
            intDigits: 3,
            precision: 9
          }
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 100
          }
        }]
      },
      handleSave() {
        let me = this
        me.doSave(() => {
          me.refreshIncomingData(true, editStatus.SHOW, {})
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
