<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <SingleLossHeadEdit ref="head" :edit-config="editConfig" :in-source="inSource"
                            @onEditBack="editBack"></SingleLossHeadEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="单损耗">
        <SingleLossBodyList ref="body" :parent-config="parentConfig"></SingleLossBodyList>
      </TabPane>
      <TabPane name="aeoTab" v-if="showBody" label="内审情况">
        <AeoInfoList v-if="tabs.aeoTab" :sid="editConfig.editData.sid" show-title="false"></AeoInfoList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { singleLossTabs } from './js/singleLossTabs'

  export default {
    name: 'singleLossTabs',
    mixins: [singleLossTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
