import SingleLossHeadEdit from '../single-loss-head-edit'
import SingleLossBodyList from '../single-loss-body-list'
import { editStatus, AeoInfoList } from '@/view/cs-common'

export const singleLossTabs = {
  name: 'singleLossTabs',
  components: {
    AeoInfoList,
    SingleLossHeadEdit,
    SingleLossBodyList
  },
  props: {
    inSource: {
      type: Object,
      default: () => ({})
    },
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        aeoTab: false
      }
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
      if (value === 'headTab') {
        me.$nextTick(() => {
          if (me.$refs.head) {
            console.info(value)
          }
        })
      } else if (value === 'bodyTab') {
        me.$nextTick(() => {
          if (me.$refs.body) {
            console.info(value)
          }
        })
      }
    }
  },
  computed: {
    showBody() {
      return this.editConfig.editStatus !== editStatus.ADD
    },
    parentConfig() {
      let me = this,
        manual = false
      if (['11', '12'].includes(me.editConfig.editData.billFlag)) {
        manual = true
      }
      return {
        manual: manual,
        headId: me.editConfig.editData.sid,
        emsNo: me.editConfig.editData.emsNo,
        editStatus: me.editConfig.editStatus,
        copEmsNo: me.editConfig.editData.copEmsNo
      }
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  }
}
