import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import SingleLossTabs from '../single-loss-tabs'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { editStatus, productClassify } from '../../../cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { detailGeneralMethod } from '../../base/detailGeneralMethod'

export const singleLossList = {
  name: 'singleLossList',
  mixins: [baseSearchConfig, baseListConfig, detailGeneralMethod],
  components: {
    SingleLossTabs
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      listConfig: {
        colOptions: true
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'pass-set': this.handlePassSet,
        'send-set': this.handleSendRecord,
        'export': this.handleDownload,
        'send-audit': this.handleSendAudit
      },
      cmbSource: {
        copEmsNo: [],
        apprStatus: productClassify.APPR_STATUS_MAP_SS
      }
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    me.handleUpdateColumn(showColumns)
    me.loadCmbSource()
  },
  computed: {
    extendParams() {
      return {
        type: '1'
      }
    }
  },
  methods: {
    loadCmbSource() {
      let me = this
      // 获取备案信息
      me.$http.post(csAPI.csProductClassify.bonded.getCopEmsNoOld).then(res => {
        if (Array.isArray(res.data.data)) {
          me.$set(me.cmbSource, 'copEmsNo', res.data.data.filter(item => {
            return !isNullOrEmpty(item.copEmsNo)
          }).map(item => {
            return {
              label: isNullOrEmpty(item.emsNo) ? '' : item.emsNo,
              value: item.copEmsNo,
              billFlag: item.billFlag
            }
          }))
        }
      }).catch(() => {
        me.$set(me.cmbSource, 'copEmsNo', [])
      })
      // me.getEmsNoList((req) => {
      //   if (Array.isArray(req.data)) {
      //     me.$set(me.cmbSource, 'copEmsNo', req.data.filter(item => {
      //       return !isNullOrEmpty(item.copEmsNo)
      //     }).map(item => {
      //       return {
      //         value: item.copEmsNo,
      //         label: item.copEmsNo
      //       }
      //     }))
      //   }
      // })
    },
    getParams() {
      return [{
        title: '状态',
        type: 'select',
        key: 'apprStatus'
      }, {
        key: 'copEmsNo',
        title: '企业内部编号'
      }, {
        key: 'emsNo',
        title: '备案号'
      }, {
        range: true,
        title: '有效期',
        key: 'validDate'
      }]
    },
    getFields() {
      let me = this
      return [{
        width: 116,
        title: '状态',
        align: 'center',
        key: 'apprStatus',
        render: (h, params) => {
          return me.cmbShowRender(h, params, me.cmbSource.apprStatus)
        }
      }, {
        width: 180,
        key: 'copEmsNo',
        title: '企业内部编号'
      }, {
        width: 150,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 100,
        key: 'billFlag',
        title: '类型',
        render: (h, params) => {
          let billFlag = params.row['billFlag'],
            billFlagName = billFlag
          if (['11', '12'].includes(billFlag)) {
            billFlagName = billFlag + ' 手册'
          } else if (['21', '22'].includes(billFlag)) {
            billFlagName = billFlag + ' 账册'
          }
          return me.toolTipRender(h, billFlagName.trim())
        }
      }, {
        width: 100,
        title: '有效期',
        key: 'validDate',
        render: (h, params) => {
          return me.dateTimeShowRender(h, params)
        }
      }, {
        width: 100,
        title: '录入日期',
        key: 'insertTime',
        render: (h, params) => {
          return me.dateTimeShowRender(h, params)
        }
        // }, {
        //   width: 136,
        //   key: 'apprDate',
        //   title: '审核时间',
        //   render: (h, params) => {
        //     return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        //   }
      }, {
        width: 150,
        title: '发送备案时间',
        key: 'sendApiDate',
        render: (h, params) => {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        }
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (!['-1', '0', '8', 'JD', 'JC'].includes(row.apprStatus)) {
        me.$Message.warning('状态为: 内审退回、暂存、内审通过、备案通过、备案退回才可编辑!')
      } else {
        me.showList = false
        me.editConfig.editData = row
        me.editConfig.editStatus = editStatus.EDIT
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 发送内审
     */
    handleSendAudit() {
      let me = this
      if (me.checkRowSelected('发送内审')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '发送',
          cancelText: '取消',
          content: '确认所选项发送内审吗',
          onOk: () => {
            me.setButtonLoading('send-audit', true)
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.sendAudit}/${params}`).then(() => {
              me.$Message.success('发送内审成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setButtonLoading('send-audit', false)
            })
          }
        })
      }
    },
    /**
     * 发送备案
     */
    handleSendRecord() {
      let me = this
      if (me.checkRowSelected('发送备案')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '发送',
          cancelText: '取消',
          content: '确认所选项发送备案吗',
          onOk: () => {
            me.setButtonLoading('send-set', true)
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.sendRecord}/${params}`).then(() => {
              me.$Message.success('发送备案成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setButtonLoading('send-set', false)
            })
          }
        })
      }
    },
    /**
     * 备案通过
     */
    handlePassSet() {
      let me = this
      if (me.checkRowSelected('备案通过')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '确认所选项备案通过吗',
          onOk: () => {
            me.setButtonLoading('pass-set', true)
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.passSetBySelected}/${params}`).then(() => {
              me.$Message.success('备案通过成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setButtonLoading('pass-set', false)
            })
          }
        })
      }
    }
  }
}
