import ImportPage from 'xdo-import'
import SingleLossBodyEdit from '../single-loss-body-edit'
import BalanceCheckPop from '../components/balance-check-pop'
import { editStatus, productClassify } from '@/view/cs-common'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import DeclarationStatusSettingPop from '../components/declaration-status-setting-pop'

export const singleLossBodyList = {
  name: 'singleLossBodyList',
  mixins: [baseSearchConfig, baseListConfig, dynamicImport],
  components: {
    ImportPage,
    BalanceCheckPop,
    SingleLossBodyEdit,
    DeclarationStatusSettingPop
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        emsNo: '',
        headId: '',
        copEmsNo: '',
        manual: false,
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let params = this.getParams(),
      fields = this.getFields(),
      importConfig = this.getCommImportConfig('CONSUME', {
        emsNo: this.parentConfig.emsNo,
        headId: this.parentConfig.headId,
        insertUserName: this.$store.state.user.userName
      })
    return {
      autoCreate: false,
      hasChildTabs: true,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      listConfig: {
        colOptions: true
      },
      pmsLevel: 'body',
      balancePopConfig: {
        show: false
      },
      importConfig: {
        show: false,
        importKey: 'CONSUME',
        config: importConfig
      },
      decStatusSettingPopConfig: {
        show: false
      },
      toolbarEventMap: {
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'balance': this.handleBalance,
        'rollback': this.handleRollback,
        'app-config': this.handleAppConfig
      },
      cmbSource: {
        sendApiStatus: productClassify.SEND_STATUS_MAP,
        modifyMark: productClassify.MODIFY_MARK_SELECT,
        ucnsDclStat: productClassify.SINGLE_APP_STATUS_MAP
      }
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    me.handleUpdateColumn(showColumns)
  },
  computed: {
    extendParams() {
      return {
        headId: this.parentConfig.headId
      }
    },
    gridDisable() {
      return this.parentConfig.editStatus !== editStatus.EDIT
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      if (me.gridDisable) {
        me.actions = me.actions.filter(action => {
          return ['balance', 'app-config', 'export'].includes(action.command)
        })
      }
      if (!me.parentConfig.manual) {
        me.actions = me.actions.filter(action => {
          return !['balance'].includes(action.command)
        })
      }
    },
    getParams() {
      return [{
        title: '单耗申报状态',
        type: 'select',
        key: 'ucnsDclStat'
      }, {
        title: '处理标志',
        type: 'select',
        key: 'modifyMark'
      }, {
        title: '发送状态',
        type: 'select',
        key: 'sendApiStatus'
      }, {
        type: 'xdoInput',
        title: '成品序号',
        key: 'exgGNo',
        props: {
          intDigits: 5
        }
      }, {
        title: '成品料号',
        key: 'copExgNo'
      }, {
        type: 'xdoInput',
        title: '料件序号',
        key: 'imgGNo',
        props: {
          intDigits: 5
        }
      }, {
        title: '料件料号',
        key: 'copImgNo'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.parentConfig['headId']
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 150,
        title: '单耗申报状态',
        key: 'ucnsDclStat',
        render: (h, params) => {
          return me.cmbShowRender(h, params, me.cmbSource.ucnsDclStat)
        }
      }, {
        width: 120,
        title: '发送状态',
        key: 'sendApiStatus',
        render: (h, params) => {
          return me.cmbShowRender(h, params, me.cmbSource.sendApiStatus)
        }
      }, {
        width: 120,
        title: '处理标志',
        key: 'modifyMark',
        render: (h, params) => {
          return me.cmbShowRender(h, params, me.cmbSource.modifyMark)
        }
      }, {
        width: 120,
        title: '成品序号',
        key: 'exgGNo'
      }, {
        width: 180,
        title: '成品料号',
        key: 'copExgNo'
      }, {
        width: 220,
        title: '成品商品名称',
        key: 'exgGName'
      }, {
        width: 120,
        title: '单耗版本号',
        key: 'exgVersion'
      }, {
        width: 120,
        title: '料件序号',
        key: 'imgGNo'
      }, {
        width: 180,
        title: '料件料号',
        key: 'copImgNo'
      }, {
        width: 220,
        title: '料件商品名称',
        key: 'imgGName'
      }, {
        width: 150,
        title: '净耗',
        key: 'decCm'
      }, {
        width: 150,
        title: '有形损耗率(%)',
        key: 'decDmVisiable'
      }, {
        width: 180,
        title: '无形损耗率(%)',
        key: 'decDmInvisiable'
      }, {
        width: 180,
        key: 'bondMtpckPrpr',
        title: '保税料件比例(%)'
        // }, {
        //   width: 136,
        //   key: 'apprDate',
        //   title: '审核时间',
        //   render: (h, params) => {
        //     return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        //   }
      }, {
        width: 250,
        key: 'note',
        title: '备注'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleRollback() {
      let me = this
      if (me.checkRowSelected('恢复')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '恢复',
          cancelText: '取消',
          content: '确认恢复所选项吗',
          onOk: () => {
            me.setButtonLoading('rollback', true)
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.rollback + '/' + params).then(() => {
              me.$Message.success('恢复成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setButtonLoading('rollback', false)
            })
          }
        })
      }
    },
    handleBalance() {
      let me = this
      me.$set(me.balancePopConfig, 'show', true)
    },
    handleAppConfig() {
      let me = this
      me.$set(me.decStatusSettingPopConfig, 'show', true)
    },
    handleImport() {
      let me = this
      me.$set(me.importConfig, 'show', true)
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.$set(me.importConfig, 'show', false)
      me.getList()
    }
  }
}
