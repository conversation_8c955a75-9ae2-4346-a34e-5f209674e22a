<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        <template v-slot:billFlag>
          <XdoIInput v-model="billFlagName" disabled></XdoIInput>
        </template>
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'bookingManagementEdit',
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csMaterielCenter.singleLoss.head.insert,
          update: csAPI.csMaterielCenter.singleLoss.head.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      },
      'detailConfig.model.copEmsNo': {
        handler: function (copEmsNo) {
          let me = this,
            filterCopEmsNos = me.dynamicSource.copEmsNo.filter(item => {
              return item.value === copEmsNo
            })
          if (Array.isArray(filterCopEmsNos) && filterCopEmsNos.length > 0) {
            me.$set(me.detailConfig.model, 'emsNo', filterCopEmsNos[0].label)
            me.$set(me.detailConfig.model, 'billFlag', filterCopEmsNos[0].billFlag)
          } else {
            me.$set(me.detailConfig.model, 'emsNo', '')
            me.$set(me.detailConfig.model, 'billFlag', '')
          }
        }
      }
    },
    computed: {
      billFlagName() {
        let me = this,
          billFlag = me.detailConfig.model.billFlag,
          billFlagName = billFlag
        if (['11', '12'].includes(billFlag)) {
          billFlagName = billFlag + ' 手册'
        } else if (['21', '22'].includes(billFlag)) {
          billFlagName = billFlag + ' 账册'
        }
        return billFlagName
      }
    },
    methods: {
      getFields() {
        return [{
          type: 'select',
          key: 'copEmsNo',
          title: '企业内部编号',
          required: !this.newDisable,
          props: {
            disabled: this.newDisable,
            optionLabelRender: (item) => {
              if (isNullOrEmpty(item.label)) {
                return item.value
              }
              return item.value + ' | ' + item.label
            }
          }
        }, {
          key: 'emsNo',
          title: '备案号',
          props: {
            disabled: true
          }
        }, {
          key: 'billFlag',
          title: '类型',
          props: {
            disabled: true
          }
        }, {
          title: '有效期',
          key: 'validDate',
          type: 'datePicker'
        }, {
          title: '录入人',
          key: 'insertUser',
          props: {
            disabled: true
          }
        }, {
          type: 'datePicker',
          title: '录入日期',
          key: 'insertTime',
          props: {
            disabled: true
          }
        }, {
          type: 'datePicker',
          title: '发送备案时间',
          key: 'sendApiDate',
          props: {
            disabled: true
          }
        }, {
          key: 'note',
          title: '备注',
          itemClass: 'dc-merge-2-4'
        }]
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
