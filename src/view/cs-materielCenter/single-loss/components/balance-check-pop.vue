<template>
  <XdoModal width="1230" mask v-model="show" title="平衡检查"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="400"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNumber } from '@/libs/util'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'

  export default {
    name: 'balanceCheckPop',
    mixins: [baseListConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      let fields = this.getFields()
      return {
        baseFields: [
          ...fields
        ],
        cmbSource: {},
        autoCreate: false,
        initSearch: false,
        pmsLevel: 'balance',
        toolbarEventMap: {
          'export': this.handleDownload
        },
        ajaxUrl: {
          pBalance: csAPI.csMaterielCenter.singleLoss.body.pBalance,
          exportUrl: csAPI.csMaterielCenter.singleLoss.body.exportBalance,
          selectAllPaged: csAPI.csMaterielCenter.singleLoss.body.getBalanceList
        }
      }
    },
    created: function () {
      let me = this
      let rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/listId')
      let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
      me.handleUpdateColumn(showColumns)
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            this.analysisAndLoad()
          }
        }
      }
    },
    methods: {
      analysisAndLoad() {
        let me = this,
          params = me.getSearchParams()
        me.$http.post(me.ajaxUrl.pBalance, params).then(() => {
          me.getList()
        }).catch(() => {
        })
      },
      getSearchParams() {
        return {
          headId: this.headId
        }
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      actionLoaded() {
        let me = this
        me.actions.push({
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          key: 'xdo-btn-download',
          icon: 'ios-cloud-download-outline'
        })
      },
      getFields() {
        let me = this
        return [{
          width: 80,
          key: 'serialNo',
          title: '料件序号'
        }, {
          width: 160,
          key: 'copGNo',
          title: '料件料号'
        }, {
          width: 196,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        }, {
          width: 120,
          key: 'unit',
          title: '计量单位',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.unit)
          }
        }, {
          width: 168,
          key: 'qty',
          title: '申报数量'
        }, {
          width: 168,
          key: 'consumption',
          title: '消耗量'
        }, {
          width: 168,
          title: '差额量',
          key: 'differenceQty',
          render: (h, params) => {
            let qty = params.row['differenceQty']
            if (isNumber(qty)) {
              if (Number(qty) < 0) {
                return h('span', {
                  style: {
                    color: 'red',
                    fontWeight: 'bold',
                    paddingLeft: '10px'
                  }
                }, String(qty))
              } else {
                return h('span', String(qty))
              }
            }
            return h('span', '0')
          }
        }, {
          width: 120,
          title: '差额率(%)',
          key: 'differenceRate',
          render: (h, params) => {
            let rate = params.row['differenceRate']
            if (isNumber(rate)) {
              if (Number(rate) < 0) {
                return h('span', {
                  style: {
                    color: 'red',
                    fontWeight: 'bold',
                    paddingLeft: '10px'
                  }
                }, String(rate))
              } else {
                return h('span', String(rate))
              }
            }
            return h('span', '0')
          }
        }]
      },
      handleTableColumnSetup() {
        this.listSetupShow = true
      },
      /**
       * 导出
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-body {
    padding: 0 4px 4px 4px;
  }
</style>
