<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal width="960" mask v-model="show" title="单耗申报状态设置"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          <template v-slot:exgGNo>
            <div class="rangeContainer">
              <XdoFormItem prop="exgGNoS" class="side">
                <dc-numberInput v-model="searchConfig.model.exgGNoS" integerDigits="5"></dc-numberInput>
              </XdoFormItem>
              <div class="middleStyle">-</div>
              <XdoFormItem prop="exgGNoE" class="side">
                <dc-numberInput v-model="searchConfig.model.exgGNoE" integerDigits="5"></dc-numberInput>
              </XdoFormItem>
            </div>
          </template>
        </DynamicForm>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" height="400"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus, productClassify } from '@/view/cs-common'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

  export default {
    name: 'declarationStatusSettingPop',
    mixins: [baseSearchConfig, baseListConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        default: () => ('')
      },
      editStatus: {
        type: String,
        default: () => (editStatus.SHOW)
      }
    },
    data() {
      let params = this.getParams()
      let fields = this.getFields()
      return {
        baseParams: [
          ...params
        ],
        baseFields: [
          ...fields
        ],
        autoCreate: false,
        initSearch: false,
        pmsLevel: 'decStatusSetting',
        toolbarEventMap: {
          'set-declare': this.handleSetDeclare,
          'set-unDeclare': this.handleSetUnDeclare
        },
        cmbSource: {
          modifyMark: productClassify.MODIFY_MARK_SELECT,
          ucnsDclStat: productClassify.SINGLE_APP_STATUS_MAP
        },
        ajaxUrl: {
          selectAllPaged: csAPI.csMaterielCenter.singleLoss.body.distinctExgList,
          setConsumeDclStat: csAPI.csMaterielCenter.singleLoss.body.setConsumeDclStat
        }
      }
    },
    created: function () {
      let me = this
      let rootId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'listId', rootId + '/listId')
      let showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
      me.handleUpdateColumn(showColumns)
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              me.$set(me.searchConfig.model, 'ucnsDclStat', '1')
              me.handleSearchSubmit()
            })
          }
        }
      },
      'searchConfig.model.ucnsDclStat': {
        immediate: true,
        handler: function (status) {
          let me = this,
            disabled = status === '1'
          me.actions.forEach(action => {
            if (action.command === 'set-declare') {
              action.disabled = !disabled
              if (disabled) {
                action.key = 'xdo-btn-upload'
              } else {
                action.key = ''
              }
            } else if (action.command === 'set-unDeclare') {
              action.disabled = disabled
              if (disabled) {
                action.key = ''
              } else {
                action.key = 'xdo-btn-download'
              }
            }
          })
        }
      }
    },
    computed: {
      extendParams() {
        return {
          headId: this.headId
        }
      }
    },
    methods: {
      actionLoaded() {
        let me = this
        me.actions = []
        if (me.editStatus === editStatus.EDIT) {
          me.actions.push({
            ...me.actionsComm,
            label: '设为已申报',
            command: 'set-declare',
            key: 'xdo-btn-upload',
            icon: 'ios-cloud-upload-outline'
          }, {
            ...me.actionsComm,
            label: '设为未申报',
            command: 'set-unDeclare',
            key: 'xdo-btn-download',
            icon: 'ios-cloud-download-outline'
          })
        }
      },
      getParams() {
        return [{
          type: 'radioGroup',
          key: 'ucnsDclStat',
          title: '单耗申报状态',
          labelWidth: 100,
          props: {
            options: [{
              label: '1', title: '未申报'
            }, {
              label: '2', title: '已申报'
            }]
          }
        }, {
          key: 'exgGNo',
          title: '成品序号'
        }, {
          key: 'copExgNo',
          title: '成品料号'
        }, {
          title: '单耗版本号',
          key: 'exgVersion'
        }]
      },
      getFields() {
        let me = this
        return [{
          width: 100,
          key: 'exgGNo',
          title: '成品序号'
        }, {
          width: 180,
          key: 'copExgNo',
          title: '成品料号'
        }, {
          width: 120,
          title: '单耗版本号',
          key: 'exgVersion'
        }, {
          width: 243,
          key: 'exgGName',
          title: '商品名称'
        }, {
          width: 120,
          title: '处理标志',
          key: 'modifyMark',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.cmbSource.modifyMark)
          }
        }, {
          width: 130,
          key: 'ucnsDclStat',
          title: '单耗申报状态',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.cmbSource.ucnsDclStat)
          }
        }]
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleTableColumnSetup() {
        this.listSetupShow = true
      },
      handleUpdateColumn(columns) {
        let me = this
        me.listConfig.columns = [{
          width: 36,
          fixed: 'left',
          key: 'selection',
          type: 'selection'
        }, ...columns]
        me.listSetupShow = false
      },
      getSettingData(status) {
        let me = this,
          selRows = me.listConfig.selectRows.map(row => {
            return {
              exgGNo: row.exgGNo,
              exgVersion: row.exgVersion
            }
          })
        return {
          headId: this.headId,
          ucnsDclStat: status,
          data: selRows
        }
      },
      handleSetDeclare() {
        let me = this
        if (me.checkRowSelected('设为已申报')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认将所选项设为已申报吗',
            onOk: () => {
              me.setButtonLoading('set-declare', true)
              let params = me.getSettingData('2')
              me.$http.post(me.ajaxUrl.setConsumeDclStat, params).then(() => {
                me.$Message.success('设置成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading('set-declare', false)
              })
            }
          })
        }
      },
      handleSetUnDeclare() {
        let me = this
        if (me.checkRowSelected('设为未申报')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认所选项设为未申报吗',
            onOk: () => {
              me.setButtonLoading('set-unDeclare', true)
              let params = me.getSettingData('1')
              me.$http.post(me.ajaxUrl.setConsumeDclStat, params).then(() => {
                me.$Message.success('设置成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading('set-unDeclare', false)
              })
            }
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .rangeContainer {
    margin: 0;
    padding: 0;
    display: flex;
    display: -o-flex;
    display: -ms-flex;
    display: -moz-flex;
    display: -webkit-flex;
  }

  .side {
    flex: 1;
    margin: 0 !important;
    padding: 0 !important;
  }

  .middleStyle {
    width: 20px;
    padding-top: 2px;
    text-align: center;
  }
</style>
