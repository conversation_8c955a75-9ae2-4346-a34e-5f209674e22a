import ImportPage from 'xdo-import'
import { csAPI, pdfExport } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import turningEdit from '../../turning/turning-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { interimVerification, erpInterfaceData, importExportManage } from '@/view/cs-common'

export const turning = {
  name: 'turning',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    ImportPage,
    turningEdit
  },
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      cmbSource: {
        status: [{
          value: '1',
          label: ' 启用'
        }, {
          value: '0',
          label: ' 停用'
        }],
        emsNo: []
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'print': this.handlePrint,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload
      },
      erpInterfaceData: erpInterfaceData,
      importExportManage: importExportManage,
      interimVerification: interimVerification
    }
  },
  created: function () {
    let me = this,
      showColumns = [],
      rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
    me.getEmsNoSelect()
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 列显示更新
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.colOptions = true
      if (me.listConfig.colOptions) {
        me.$set(me.listConfig, 'columns', [...me.getDefaultColumns(), ...columns])
      } else {
        me.$set(me.listConfig, 'columns', columns)
      }
      me.listSetupShow = false
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleImport() {
      let me = this
      me.modelImportShow = true
    },
    afterImport() {
      let me = this
      me.modelImportShow = false
      me.handleSearchSubmit()
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 打印
     */
    handlePrint() {
      let me = this
      if (me.checkRowSelected('打印', true)) {
        let params = {}
        if (me.listConfig.exportTitle === '车削信息管理') {
          params = {
            name: '车削类' + me.listConfig.selectRows[0].facGNo
          }
        } else {
          params = {
            name: '冲压类' + me.listConfig.selectRows[0].facGNo
          }
        }
        pdfExport(`${me.ajaxUrl.printPdf}/${me.listConfig.selectRows[0].sid}`, params, 'get')
      }
    },
    /**
     * 获取手册号
     */
    getEmsNoSelect() {
      let me = this
      me.$http.post(csAPI.csProductClassify.bonded.getZtythCopEmsNo).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          if (!isNullOrEmpty(item.emsNo)) {
            tmpArr.push({
              value: item.emsNo,
              label: item.emsNo
            })
          }
        }
        me.cmbSource.emsNo = tmpArr
      }).catch(() => {
      })
    }
  }
}
