<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" label-width="110">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <turningEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" :inSource="cmbSource"></turningEdit>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                @onImportSuccess="afterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { turning } from '../js/turning/turning'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

  export default {
    name: 'turningList',
    mixins: [turning, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('RY_TURNING_IMPORT', {
        insertUserName: this.$store.state.user.userName
      })
      return {
        cmbSource: {},
        defaultFields: [],
        modelImportShow: false,
        listConfig: {
          exportTitle: '车削信息管理'
        },
        importConfig: importConfig,
        importKey: 'RY_TURNING_IMPORT',
        ajaxUrl: {
          exportUrl: csAPI.csMaterielCenter.turning.export,
          deleteUrl: csAPI.csMaterielCenter.turning.delete,
          printPdf: csAPI.csMaterielCenter.turning.printPdf,
          selectAllPaged: csAPI.csMaterielCenter.turning.list
        }
      }
    },
    methods: {
      getCommParams() {
        return [{
          key: 'facGNo',
          title: '荣益品号'
        }, {
          key: 'facGName',
          title: '成品品名'
        }, {
          key: 'version',
          title: '版本号'
        },]
      },
      getCommFields() {
        return [{
          width: 120,
          title: '序号',
          key: 'serialNo'
        }, {
          width: 120,
          key: 'facGNo',
          title: '荣益品号'
        }, {
          width: 90,
          key: 'facGName',
          title: '成品品名'
        }, {
          width: 120,
          title: '版本号',
          key: 'version'
        }, {
          width: 100,
          title: '原料品号',
          key: 'originalNo'
        }, {
          width: 160,
          title: '原料品名',
          key: 'originalName'
        }, {
          width: 120,
          key: 'erpProductH',
          title: 'erp产品高度'
        }, {
          width: 160,
          title: 'erp刀宽',
          key: 'erpKnifeW'
        }, {
          width: 200,
          key: 'erpSingleW',
          title: 'erp单支重量'
        }, {
          width: 120,
          title: '有效长度',
          key: 'effLength'
        }, {
          width: 120,
          title: '称重净耗',
          key: 'weighNetCon'
        }, {
          width: 120,
          title: '组成用量',
          key: 'compositionDosage'
        }, {
          width: 120,
          title: '直径',
          key: 'diameter'
        }, {
          width: 120,
          title: '系统净耗',
          key: 'systemNetCon'
        }, {
          width: 120,
          title: '损耗量',
          key: 'lossAmount'
        }, {
          width: 120,
          title: '损耗率',
          key: 'lossRate'
        }, {
          width: 120,
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.status)
          }
        }, {
          width: 120,
          key: 'emsNo',
          title: '手册号'
        }, {
          width: 160,
          key: 'copImgNo',
          title: '备案料件料号'
        }, {
          width: 120,
          key: 'imgSerialNo',
          title: '备案料件序号'
        }, {
          width: 160,
          tooltip: true,
          key: 'copImgUnit',
          title: '备案料件申报单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        }, {
          width: 120,
          key: 'copImgName',
          title: '备案料件品名'
        }, {
          width: 120,
          key: 'copExgNo',
          title: '备案成品料号'
        }, {
          width: 120,
          key: 'exgSerialNo',
          title: '备案成品序号'
        }, {
          width: 150,
          key: 'copExgUnit',
          title: '备案成品申报单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        }, {
          width: 120,
          key: 'copExgName',
          title: '备案成品名称'
        }, {
          width: 120,
          title: '备注1',
          key: 'remark1'
        }, {
          width: 120,
          title: '备注2',
          key: 'remark2'
        }, {
          width: 120,
          title: '备注3',
          key: 'remark3'
        }, {
          width: 120,
          title: '创建人',
          key: 'insertUser'
        }, {
          width: 120,
          title: '创建时间',
          key: 'insertTime'
        }, {
          width: 120,
          title: '修改人',
          key: 'updateUser'
        }, {
          width: 120,
          title: '修改时间',
          key: 'updateTime'
        }, {
          width: 160,
          key: 'tradeCode',
          title: '企业编码'
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
