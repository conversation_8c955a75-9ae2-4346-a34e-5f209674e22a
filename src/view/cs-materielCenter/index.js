/***
 * 关务-物料中心-路由
 */
import { namespace } from '@/project'
import turningList from './turning/turning-list'
import stampingList from './stamping/stamping-list'
import mergingImgHeadList from './mergingImg/mergingImgHeadList'
import mergingExgHeadList from './mergingExg/mergingExgHeadList'
import SingleLossHeadList from './single-loss/single-loss-head-list'
import PackingConfigList from './packing-config/packing-config-list'
import HsVerificationTabs from './hs-verification/hs-verification-tabs'
import KitInfoMaintainList from './kit-info-maintain/kit-info-maintain-list'

export default [
  {
    path: '/' + namespace + '/materielCenter/mergingImg',
    name: 'mergingImgHeadList',
    meta: {
      title: '备案料件归类'
    },
    component: mergingImgHeadList
  },
  {
    path: '/' + namespace + '/materielCenter/mergingExg',
    name: 'mergingExgHeadList',
    meta: {
      title: '备案成品归类'
    },
    component: mergingExgHeadList
  },
  {
    path: '/' + namespace + '/materielCenter/singleLossHeadList',
    name: 'singleLossHeadList',
    meta: {
      title: '单损耗信息'
    },
    component: SingleLossHeadList
  },
  {
    path: '/' + namespace + '/materielCenter/turning',
    name: 'turningList',
    meta: {
      title: '车削信息管理'
    },
    component: turningList
  },
  {
    path: '/' + namespace + '/productClassify/stamping',
    name: 'stampingList',
    meta: {
      title: '冲压信息管理'
    },
    component: stampingList
  },
  {
    path: '/' + namespace + '/productClassify/packingConfig',
    name: 'packingConfigList',
    meta: {
      title: '包装信息'
    },
    component: PackingConfigList
  },
  {
    path: '/' + namespace + '/productClassify/hsVerificationTabs',
    name: 'hsVerificationTabs',
    meta: {
      title: '商编校验'
    },
    component: HsVerificationTabs
  },
  {
    path: '/' + namespace + '/productClassify/kitInfoMaintainList',
    name: 'kitInfoMaintainList',
    meta: {
      title: '套件信息维护'
    },
    component: KitInfoMaintainList
  }
]
