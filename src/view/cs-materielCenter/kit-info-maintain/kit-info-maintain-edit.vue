<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus, productClassify } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'kitInfoMaintainEdit',
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        cmbSource: {
          copGNo: [],
          fullCopGNo: [],
          gmark: productClassify.GMARK_SELECT.filter(item => {
            return ['I', 'E', '2', '3'].includes(item.value)
          })
        },
        firstTimeChange: true,
        ajaxUrl: {
          insert: csAPI.csMaterielCenter.kitInfoMaintain.insert,
          update: csAPI.csMaterielCenter.kitInfoMaintain.update,
          getCopGNos: csAPI.csMaterielCenter.kitInfoMaintain.getCopGNos,
          getNonData: csAPI.csMaterielCenter.kitInfoMaintain.getNonData
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    created: function () {
      this.loadCopGNos()
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      loadCopGNos() {
        let me = this
        me.$http.post(me.ajaxUrl.getNonData, {}).then(res => {
          me.$set(me.cmbSource, 'fullCopGNo', res.data.data.map(item => {
            return {
              value: item.copGNo,
              label: item.copGNo,
              gmark: item.gmark,
              gname: item.gname,
              unit: item.unit
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'fullCopGNo', [])
        }).finally(() => {
          me.handleGMarkChanged()
          me.$set(me, 'firstTimeChange', false)
        })
      },
      handleGMarkChanged() {
        let me = this,
          gMark = me.detailConfig.model.gmark

        if (!me.firstTimeChange) {
          me.$set(me.detailConfig.model, 'copGNo', '')
          console.info('清空子件料号!')
        }

        if (!isNullOrEmpty(gMark)) {
          me.$set(me.cmbSource, 'copGNo', me.cmbSource.fullCopGNo.filter(item => {
            return item.gmark === gMark
          }))
        } else {
          me.$set(me.cmbSource, 'copGNo', [])
        }
        me.detailConfig.fields.filter(field => {
          return field.key === 'copGNo'
        }).forEach(item => {
          if (item.props) {
            item.props.options = me.dynamicSource.copGNo
          } else {
            item.props = {
              options: me.dynamicSource.copGNo
            }
          }
        })
      },
      getFields() {
        return [{
          required: true,
          key: 'copSuitNo',
          title: '套件料号',
          props: {
            maxlength: 50,
            disabled: this.newDisable
          }
        }, {
          required: true,
          key: 'suitName',
          title: '套件名称',
          props: {
            maxlength: 100
          }
        }, {
          key: 'gmark',
          required: true,
          type: 'select',
          title: '子件物料类型',
          on: {
            change: this.handleGMarkChanged
          }
        }, {
          key: 'copGNo',
          required: true,
          type: 'select',
          title: '子件料号',
          on: {
            change: this.handleCopGNoChanged
          },
          props: {
            optionLabelRender: (opt) => opt.label
          }
        }, {
          key: 'gname',
          title: '子件名称',
          props: {
            disabled: true
          }
        }, {
          key: 'unit',
          type: 'pcode',
          props: {
            meta: 'UNIT',
            disabled: true
          },
          title: '子件申报单位'
        }, {
          key: 'qty',
          required: true,
          props: {
            intDigits: 16,
            precision: 5
          },
          title: '子件数量',
          type: 'xdoInput'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 255
          },
          itemClass: 'dc-merge-2-4'
        }]
      },
      handleCopGNoChanged(newVal) {
        let me = this,
          selectedRow = me.cmbSource.fullCopGNo.find(item => {
            return item.value === newVal
          })
        if (selectedRow) {
          me.$set(me.detailConfig.model, 'gname', selectedRow['gname'])
          me.$set(me.detailConfig.model, 'unit', selectedRow['unit'])
        } else {
          me.$set(me.detailConfig.model, 'gname', '')
          me.$set(me.detailConfig.model, 'unit', '')
        }
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
