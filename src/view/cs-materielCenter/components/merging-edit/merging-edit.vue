<template>
  <section>
    <MerchElement :tyShow.sync="tyShow" :is-edit="isEdit"
                  :modelString="headerData.gmodel" :code-ts="headerData.codeTS" :code-name="headerData.gname"
                  @onChange="handleGModelChange"></MerchElement>

    <XdoCard :bordered="false">
      <div class="xdo-enter-root" v-focus>
        <XdoForm ref="frmEdit" class="dc-form xdo-enter-form" :model="headerData" :rules="rulesHeader" label-position="right" :label-width="120">
          <XdoFormItem prop="copGNo" label="备案料号">
            <XdoIInput type="text" v-model="headerData.copGNo" :disabled="copGNoDisable" :maxlength="32" @on-blur="copGNoBlur"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="emsNo" label="备案号">
            <xdo-select v-model="headerData.emsNo" :options="this.inSource.emsNoData" :disabled="isEmsNoDisabled"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="serialNo" label="备案序号">
            <xdo-input v-model="headerData.serialNo" number int-length="11" :disabled="copGNoDisable" ></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="copEmsNo" label="企业内部编号">
            <xdo-select v-model="headerData.copEmsNo" :options="this.inSource.copEmsNoData" :disabled="isEmsNoDisabled"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="物料类型" v-if="isCheck">
            <xdo-select v-model="headerData.gmark" disabled :options="this.productClassify.GMARK_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="codeTS" label="商品编码">
            <XdoIInput type="text" v-model="headerData.codeTS" :disabled="showDisable" @on-blur="codeTSEnter" :maxlength="10"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gname" label="商品名称">
            <XdoIInput type="text" v-model="headerData.gname" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>
          <FormItem prop="gmodel" label="申报规格型号" class="dc-merge-1-4">
            <Input type="text" v-model="headerData.gmodel" disabled :maxlength="255" style="width: 80%;"></Input>
            <!--点击加号，弹出界面-->
            <span><span>{{ usedCountGModel }}</span>/255 </span>
            <XdoButton @click="handleAddType">规范申报</XdoButton>
          </FormItem>
          <XdoFormItem prop="unit" label="申报计量单位">
            <xdo-select v-model="headerData.unit" :disabled="unitDisable" :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="unit1" required label="法定计量单位">
            <xdo-select v-model="headerData.unit1" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="unit2" label="法定第二计量单位">
            <xdo-select v-model="headerData.unit2" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="qty" label="备案数量">
            <xdo-input v-model="headerData.qty" decimal int-length="11" precision="5" :disabled="showDisable" @on-change="qtyPriceChanged"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decPrice" label="备案单价">
            <xdo-input v-model="headerData.decPrice" decimal int-length="11" precision="5" :disabled="showDisable" @on-change="qtyPriceChanged"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decTotal" label="备案总价">
            <xdo-input v-model="headerData.decTotal" decimal int-length="11" precision="5" notConvertNumber :disabled="showDisable" @on-change="decTotalChanged"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="headerData.curr" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="country" label="产销国">
            <xdo-select v-model="headerData.country" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="recordDate" label="备案有效期">
            <XdoDatePicker type="date" :value="headerData.recordDate" @on-change="headerData.recordDate=$event" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-1-3">
            <XdoIInput type="text" v-model="headerData.note" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="modifyMark" label="处理标志">
            <xdo-select v-model="headerData.modifyMark" disabled :options="this.productClassify.MODIFY_MARK_SELECT" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="dutyMode" label="征免方式">
            <xdo-select v-model="headerData.dutyMode" :asyncOptions="pcodeList" :disabled="showDisable"
                        :meta="pcode.levymode" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

        </XdoForm>
      </div>
    </XdoCard>

    <div v-if="headerData.sid">
      <AcmpInfoListCustom :sid="headerData.sid" :showAction="showAction" :just-view="!showAction" business-type="2"></AcmpInfoListCustom>
    </div>

    <div v-if="isCheck">
      <p>内审意见:</p>
      <i-input type="textarea" :rows="3" v-model.trim="apprNote" ref="ApprNote"></i-input>
    </div>

    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <div v-if="!isCheck" class="buttonsClass">
        <template v-for="item in editActions" >
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
          </Button>
        </template>
      </div>
      <div v-if="isCheck" class="buttonsClass">
        <template v-for="item in checkActions" >
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
          </Button>
        </template>
      </div>
    </div>

    <MaterielProductHSCodeCheckPop :show.sync="hsCodeCheckPop.show" :data="hsCodeCheckPop.data"
                                   @doContinue="doContinue"></MaterielProductHSCodeCheckPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { detailGeneralMethod } from '@/view/cs-materielCenter/base/detailGeneralMethod'
  import { editStatus, productClassify, MerchElement, AcmpInfoListCustom } from '@/view/cs-common'
  import MaterielProductHSCodeCheckPop from '../../../cs-aeoManage/components/materiel-product-hscode-check/materiel-product-hscode-check-pop'

  export default {
    name: 'mergingEdit',
    components: {
      MerchElement,
      AcmpInfoListCustom,
      MaterielProductHSCodeCheckPop
    },
    mixins: [detailGeneralMethod],
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      },
      isCheck: {
        type: Boolean,
        default: false
      },
      inSource: {
        type: Object,
        default: () => ({
          emsNoData: [],
          emsCopData: [],
          copEmsNoData: []
        })
      }
    },
    data() {
      let btnComm = {
          needed: true,
          loading: false,
          disabled: false
        },
        theData = this.getDefaultData()
      return {
        headerData: {
          ...theData
        },
        rulesHeader: {
          copGNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          serialNo: [{required: true, type: 'number', message: '不能为空!', trigger: 'blur'}],
          copEmsNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          codeTS: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gname: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gmodel: [{required: true, message: '不能为空!', trigger: 'blur'}],
          unit: [{required: true, message: '不能为空!', trigger: 'blur'}],
          unit1: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        editActions: [
          {...btnComm, type: 'warning', label: '保存', click: this.handleSave},
          {...btnComm, type: 'warning', label: '保存继续', click: this.handleSaveContinue},
          {...btnComm, type: 'success', label: '发送内审', click: this.onSendExamine},
          {...btnComm, type: 'primary', label: '关闭', click: this.handleBack}
        ],
        checkActions: [
          {...btnComm, type: 'success', label: '内审通过', click: this.handleCheckPass},
          {...btnComm, type: 'error', label: '内审退回', click: this.handleCheckReturn},
          {...btnComm, type: 'primary', label: '关闭', click: this.handleBack}
        ],
        tyShow: false,
        usedCountGModel: 0,
        productClassify: productClassify,
        ajaxUrl: {
          insert: '',
          update: '',
          sendData: csAPI.aeoManage.aeoReview.actions.sendData,
          auditData: csAPI.aeoManage.aeoReview.actions.auditData,
          returnData: csAPI.aeoManage.aeoReview.actions.returnData,
          mergingHsCodeCheckBySelected: csAPI.aeoManage.aeoReview.actions.mergingHsCodeCheckBySelected
        },
        apprNote: '',
        actionOptions: {
          apprType: ''
        },
        hsCodeCheckPop: {
          type: '',       // 0: 发送; 1: 审核
          data: [],
          show: false
        },
        emsCopLive: true,
        sourceLoaded: false
      }
    },
    watch: {
      'headerData.gmodel': function () {
        this.calcUsedCountGModel()
      },
      editConfig: {
        deep: true,
        immediate: true,
        handler: function (config) {
          if (config && config.editStatus === editStatus.ADD) {
            this.resetFormData()
            this.editActions[2].disabled = true
          } else if (config && config.editStatus === editStatus.EDIT) {
            this.headerData = {...config.editData}
            this.editActions[2].disabled = ['2', '8', 'JA'].includes(config.editData.apprStatus)
          } else if (config && config.editStatus === editStatus.SHOW) {
            this.headerData = {...config.editData}
            this.editActions[2].disabled = true
          } else {
            console.error('缺失编辑信息!')
          }
        }
      },
      editDisabled: {
        immediate: true,
        handler: function (val) {
          // 待内审 内审完成  禁用按钮
          this.editActions[0].disabled = val
          this.editActions[1].disabled = val
          // this.editActions[2].disabled = val || this.headerData.apprStatus === '8' || this.headerData.apprStatus === 'JC' || this.headerData.apprStatus === 'JD'
        }
      },
      'headerData.apprStatus': {
        immediate: true,
        handler: function (val) {
          if (['2', 'JA'].includes(val)) {
            this.checkActions[0].disabled = false
            this.checkActions[1].disabled = false
          } else {
            this.checkActions[0].disabled = true
            this.checkActions[1].disabled = true
          }
        }
      },
      'headerData.emsNo': {
        handler: function (emsNo) {
          let me = this
          if (me.emsCopLive) {
            me.$set(me, 'emsCopLive', false)
            me.onEmsNoChange(emsNo)
            me.$nextTick(() => {
              me.$set(me, 'emsCopLive', true)
            })
          }
        }
      },
      'headerData.copEmsNo': {
        handler: function (copEmsNo) {
          let me = this
          if (me.emsCopLive) {
            me.$set(me, 'emsCopLive', false)
            me.onCopEmsNoChange(copEmsNo)
            me.$nextTick(() => {
              me.$set(me, 'emsCopLive', true)
            })
          }
        }
      },
      inSource: {
        deep: true,
        immediate: true,
        handler: function (source) {
          let me = this
          if (me.sourceLoaded && Array.isArray(source.emsCopData) && source.emsCopData.length > 0) {
            me.onEmsNoChange(me.headerData.emsNo)
            me.$set(me, 'sourceLoaded', false)
          }
        }
      }
    },
    mounted: function () {
      let me = this
      me.headerData.gmark = me.editConfig.gmark
      if (me.editConfig.gmark === 'I') {
        me.actionOptions.apprType = 'OI'
        me.ajaxUrl.insert = csAPI.materialRelationship.material.insert
        me.ajaxUrl.update = csAPI.materialRelationship.material.update
      } else if (me.editConfig.gmark === 'E') {
        me.actionOptions.apprType = 'OE'
        me.ajaxUrl.insert = csAPI.materialRelationship.product.insert
        me.ajaxUrl.update = csAPI.materialRelationship.product.update
      }
      if (me.editConfig.editStatus === editStatus.ADD) {
        me.$set(me, 'sourceLoaded', true)
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          emsNo: this.$store.getters[`${namespace}/emsNoMat`],
          serialNo: null,
          copGNo: '',
          codeTS: '',
          gname: '',
          gmodel: '',
          unit: '',
          unit1: '',
          unit2: '',
          qty: null,
          decPrice: null,
          decTotal: '',
          curr: '',
          country: '',
          recordDate: '',
          apprStatus: '',
          modifyMark: '',
          note: '',
          gmark: this.editConfig.gmark,
          copEmsNo: '',
          billFlag: '',
          credentials: '',
          dutyMode: ''        // '3'
        }
      },
      copGNoBlur() {
        let me = this,
          tmpCopGNo = me.headerData.copGNo
        if (isNullOrEmpty(tmpCopGNo) !== true) {
          if (me.configData.caseWrite === '1') {
            me.$set(me.headerData, 'copGNo', tmpCopGNo.trim())
          } else {
            me.$set(me.headerData, 'copGNo', tmpCopGNo.trim().toUpperCase())
          }
        }
      },
      onEmsNoChange(emsNo) {
        let me = this
        if (!isNullOrEmpty(emsNo)) {
          let emsCopDatas = me.inSource.emsCopData.filter(item => {
            return item.value === emsNo
          })
          if (Array.isArray(emsCopDatas) && emsCopDatas.length > 0) {
            me.$set(me.headerData, 'copEmsNo', emsCopDatas[0].key)
          } else {
            me.$set(me.headerData, 'copEmsNo', '')
            me.$set(me.headerData, 'serialNo', null)
          }
          if (!isNullOrEmpty(me.headerData.copEmsNo)) {
            me.setSerialNo()
          }
        } else {
          me.$set(me.headerData, 'copEmsNo', '')
          me.$set(me.headerData, 'serialNo', null)
        }
      },
      onCopEmsNoChange(copEmsNo) {
        let me = this
        if (isNullOrEmpty(copEmsNo)) {
          me.$set(me.headerData, 'emsNo', '')
        } else {
          let emsCopDatas = me.inSource.emsCopData.filter(item => {
            return item.key === copEmsNo
          })
          if (Array.isArray(emsCopDatas) && emsCopDatas.length === 1) {
            me.$set(me.headerData, 'emsNo', emsCopDatas[0].value)
          }
          if (!isNullOrEmpty(me.headerData.emsNo)) {
            me.setSerialNo()
          } else {
            me.$set(me.headerData, 'serialNo', null)
          }
        }
      },
      setSerialNo() {
        let me = this
        if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
          let getSerialUrl = ''
          if (me.editConfig.gmark === 'I') {
            getSerialUrl = csAPI.materialRelationship.product.getImgMaxNo
          } else if (me.editConfig.gmark === 'E') {
            getSerialUrl = csAPI.materialRelationship.product.getExgMaxNo
          }
          if (!isNullOrEmpty(getSerialUrl)) {
            me.$http.post(getSerialUrl, {
              copEmsNo: me.headerData.copEmsNo
            }).then(res => {
              if (isNumber(res.data.data)) {
                me.$nextTick(() => {
                  me.$set(me.headerData, 'serialNo', Number(res.data.data))
                })
              }
            }).catch(res2 => {
              console.info(res2.message)
            })
          }
        }
      },
      codeTSEnter() {
        let me = this
        // 商品编码带出计量单位
        let val = me.headerData.codeTS.trim()
        if (val.length === 10) {
          me.pcodeRemote(me.pcode.complex, val).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.headerData, 'unit1', res[0]['UNIT_1'])
              me.$set(me.headerData, 'unit2', res[0]['UNIT_2'])
              me.$set(me.headerData, 'credentials', res[0]['CONTROL_MA'])
            } else {
              me.resetUnit()
              me.$Message.warning('商品编码不存在')
            }
          }).catch(() => {
            me.resetUnit()
            me.$Message.warning('商品编码不存在')
          })
        } else {
          me.resetUnit()
        }
      },
      resetUnit() {
        let me = this
        me.$set(me.headerData, 'unit1', '')
        me.$set(me.headerData, 'unit2', '')
        me.$set(me.headerData, 'credentials', '')
      },
      calcUsedCountGModel() {
        let me = this
        // 计算规格型号已使用的字数
        let bytesCount = 0
        let strGModel = me.headerData.gmodel
        if (!isNullOrEmpty(strGModel)) {
          let chars = ''
          for (let i = 0; i < strGModel.length; i++) {
            chars = strGModel.charAt(i)
            /* eslint-disable no-control-regex */
            if (/^[\u0000-\u00ff]$/.test(chars)) {
              bytesCount += 1
            } else {
              bytesCount += 2
            }
          }
        }
        me.usedCountGModel = bytesCount
      },
      /**
       * 执行保存
       */
      doSave(callback) {
        let me = this
        me.$refs['frmEdit'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.headerData)
            if (me.editConfig.editStatus === editStatus.ADD) {
              let theCopEmsNoObj = me.inSource.copEmsNoData.find(x => x.value === data.copEmsNo)
              if (theCopEmsNoObj && theCopEmsNoObj.hasOwnProperty('billFlag')) {
                data.billFlag = theCopEmsNoObj.billFlag
              } else {
                data.billFlag = ''
              }

              me.editActions[0].loading = true
              me.editActions[1].loading = true
              me.$http.post(me.ajaxUrl.insert, data).then(res => {
                if (typeof callback === 'function') {
                  callback(res.data.data)
                } else {
                  me.editConfig.editStatus = editStatus.EDIT
                }
                me.$Message.success('新增成功!')
              }).catch(() => {
              }).finally(() => {
                me.editActions[0].loading = false
                me.editActions[1].loading = false
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              me.editActions[0].loading = true
              me.editActions[1].loading = true
              me.$http.put(`${me.ajaxUrl.update}/${me.headerData.sid}`, data).then(res => {
                if (typeof callback === 'function') {
                  callback(res.data.data)
                } else {
                  me.editConfig.editStatus = editStatus.EDIT
                }
                me.$Message.success('修改成功!')
              }).catch(() => {
              }).finally(() => {
                me.editActions[0].loading = false
                me.editActions[1].loading = false
              })
            }
          }
        })
      },
      beforeSave() {
        let me = this
        if (isNumber(me.headerData.decTotal)) {
          me.$set(me.headerData, 'decTotal', Number(me.headerData.decTotal))
        } else {
          me.$set(me.headerData, 'decTotal', null)
        }
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.beforeSave()
        me.doSave((data) => {
          me.$emit('onEditBack', {
            data: data,
            hide: false,
            changed: true
          })
          let copEmsNo = data.copEmsNo,
            serialNo = data.serialNo
          me.$nextTick(() => {
            me.$set(me.headerData, 'copEmsNo', copEmsNo)
            me.$set(me.headerData, 'serialNo', serialNo)
          })
        })
      },
      /**
       * 保存继续
       */
      handleSaveContinue() {
        let me = this
        me.beforeSave()
        me.doSave(() => {
          me.$emit('onEditBack', {
            data: {},
            hide: false,
            changed: true
          })
          me.$nextTick(() => {
            me.$set(me.headerData, 'emsNo', me.$store.getters[`${namespace}/emsNoMat`])
          })
        })
      },
      /**
       * 发送内审
       */
      onSendExamine() {
        let me = this
        me.$http.post(me.ajaxUrl.mergingHsCodeCheckBySelected, [me.headerData.sid]).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.hsCodeCheckPop, 'type', '0')
            me.$set(me.hsCodeCheckPop, 'data', res.data.data)
            me.$set(me.hsCodeCheckPop, 'show', true)
          } else {
            me.$Modal.confirm({
              title: '提醒',
              okText: '确定',
              cancelText: '取消',
              content: '确认发送内审吗',
              onOk: () => {
                me.doApplySend()
              }
            })
          }
        }).catch(() => {
        })
      },
      doApplySend() {
        let me = this
        me.editActions[2].loading = true
        me.$http.post(me.ajaxUrl.sendData, {
          apprNote: '',
          businessSid: me.headerData.sid,
          apprType: me.actionOptions.apprType
        }).then(() => {
          me.$Message.success('发送内审成功!')
          //返回查询列表
          me.handleBack()
        }).catch(() => {
        }).finally(() => {
          me.editActions[2].loading = false
        })
      },
      /**
       * 审核通过
       */
      handleCheckPass() {
        let me = this
        me.$http.post(me.ajaxUrl.mergingHsCodeCheckBySelected, [me.headerData.sid]).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.hsCodeCheckPop, 'type', '1')
            me.$set(me.hsCodeCheckPop, 'data', res.data.data)
            me.$set(me.hsCodeCheckPop, 'show', true)
          } else {
            me.$Modal.confirm({
              title: '提醒',
              okText: '确定',
              cancelText: '取消',
              content: '确认审核通过吗',
              onOk: () => {
                me.doApplyBatchPass()
              }
            })
          }
        }).catch(() => {
        })
      },
      doApplyBatchPass() {
        let me = this
        me.checkActions[0].loading = true
        me.$http.post(me.ajaxUrl.auditData, {
          apprType: me.actionOptions.apprType,
          businessSid: me.headerData.sid,
          apprNote: me.apprNote
        }).then(() => {
          me.$Message.success('内审通过成功!')
          me.handleBack()
        }).catch(() => {
        }).finally(() => {
          me.checkActions[0].loading = false
        })
      },
      /**
       * 继续发送或审核通过
       */
      doContinue() {
        let me = this
        if (me.hsCodeCheckPop.type === '0') {
          me.doApplySend()
        }
        if (me.hsCodeCheckPop.type === '1') {
          me.doApplyBatchPass()
        }
        me.$set(me.hsCodeCheckPop, 'type', '')
        me.$set(me.hsCodeCheckPop, 'data', [])
        me.$set(me.hsCodeCheckPop, 'show', false)
      },
      /**
       * 内审退回
       */
      handleCheckReturn() {
        let me = this
        if (isNullOrEmpty(me.apprNote)) {
          me.$Message.warning('请输入内审意见!')
          me.$refs.ApprNote.focus()
          return
        }
        me.checkActions[1].loading = true
        me.$http.post(me.ajaxUrl.returnData, {
          apprType: me.actionOptions.apprType,
          businessSid: me.headerData.sid,
          apprNote: me.apprNote
        }).then(() => {
          me.$Message.success('内审退回成功!')
          me.handleBack()
        }).catch(() => {
        }).finally(() => {
          me.checkActions[1].loading = false
        })
      },
      handleAddType() {
        let me = this
        // 弹出规格型号
        me.tyShow = true
      },
      handleGModelChange(val) {
        let me = this
        me.$set(me.headerData, 'gmodel', val)
      },
      resetFormData() {
        let me = this
        if (me.$refs['frmEdit']) {
          me.$refs['frmEdit'].resetFields()
        }
        me.headerData = me.getDefaultData()
      },
      /**
       * 关闭
       */
      handleBack() {
        let me = this
        me.$emit('onEditBack', {
          data: {},
          hide: true,
          changed: false
        })
      },
      qtyPriceChanged() {
        let me = this
        if (isNumber(me.headerData.qty) && isNumber(me.headerData.decPrice)) {
          me.$set(me.headerData, 'decTotal', Math.round(me.headerData.qty * me.headerData.decPrice * 100000) / 100000)
        } else {
          me.$set(me.headerData, 'decTotal', null)
        }
      },
      decTotalChanged() {
        let me = this
        if (isNumber(me.headerData.decTotal) && isNumber(me.headerData.qty)) {
          me.$set(me.headerData, 'decPrice', Math.round(Number(me.headerData.decTotal) * 100000 / me.headerData.qty) / 100000)
        }
      }
    },
    computed: {
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      },
      showDisable() {
        if (this.editConfig && this.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (this.editConfig && this.editConfig.editStatus === editStatus.EDIT) {
          return false
        } else if (this.editConfig && this.editConfig.editStatus === editStatus.SHOW) {
          return true
        } else {
          return true
        }
      },
      unitDisable() {
        if (!this.showDisable) {
          return !(this.editConfig.editData.modifyMark === '3'
            && ['-1', '0', 'JD'].includes(this.editConfig.editData.apprStatus))
        }
        return this.showDisable
      },
      /**
       * 是否禁用操作
       * @returns {boolean}
       */
      editDisabled() {
        return !!(['2'].includes(this.headerData.apprStatus) || this.showDisable)
      },
      copGNoDisable() {
        let me = this
        if (me.editConfig && me.editConfig.editData && ['0', '1'].includes(me.editConfig.editData['modifyMark'])) {
          return true
        }
        if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
          return false
        } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT && me.headerData.apprStatus === '0') {
          return false
        }
        return true
      },
      isEmsNoDisabled() {
        return this.showDisable || this.editConfig.editStatus !== editStatus.ADD
      },
      isEdit() {
        return this.editConfig.editStatus === editStatus.EDIT || this.editConfig.editStatus === editStatus.ADD
      },
      configData() {
        return this.$store.state[`${namespace}`].clearanceBusinessSetting
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonsClass button {
    margin-right: 5px;
  }
</style>
