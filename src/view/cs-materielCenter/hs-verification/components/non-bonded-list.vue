<template>
  <section>
    <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      <div style="white-space: nowrap; display: inline-flex; font-weight: bold; width: 280px;">
        最新一次的更新时间: {{lastUpdateTime}}
      </div>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" :disable="gridDisable"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { hsVerificationList } from '../js/hsVerificationList'

  export default {
    name: 'nonBondedList',
    mixins: [hsVerificationList],
    data() {
      return {
        listConfig: {
          exportTitle: '非保税物料'
        },
        verificationType: 'nonBonded',
        ajaxUrl: {
          getTime: csAPI.materialRelationship.hsVerification.comm.getTime,
          exportUrl: csAPI.materialRelationship.hsVerification.nonBonded.exportUrl,
          selectAllPaged: csAPI.materialRelationship.hsVerification.nonBonded.selectAllPaged
        }
      }
    },
    methods: {
      getSearchParams() {
        return {
          copGNo: this.searchModel.copGNo,
          codeTS: this.searchModel.codeTS,
          gname: this.searchModel.gname
        }
      },
      getExtendFields() {
        return [{
          width: 130,
          key: 'copGNo',
          title: '企业料号'
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }
</style>
