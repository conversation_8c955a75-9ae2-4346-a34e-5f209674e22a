<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </div>
    </XdoCard>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="materialsTab" label="备案料件">
        <MaterialsList ref="materials" :show-search="showSearch"
                       :search-lines="searchLines" :search-model="searchModel"
                       @doDownload="doDownload" @doUpdate="showUpdatePop"></MaterialsList>
      </TabPane>
      <TabPane name="productsTab" label="备案成品">
        <ProductsList ref="products" :show-search="showSearch"
                      :search-lines="searchLines" :search-model="searchModel"
                      @doDownload="doDownload" @doUpdate="showUpdatePop"></ProductsList>
      </TabPane>
      <TabPane name="nonBondedTab" label="非保税物料">
        <NonBondedList ref="nonBonded" :show-search="showSearch"
                       :search-lines="searchLines" :search-model="searchModel"
                       @doDownload="doDownload" @doUpdate="showUpdatePop"></NonBondedList>
      </TabPane>
    </XdoTabs>
    <UpdatePop :show.sync="updatePopShow" :bonded="bonded" :in-source="source4Pop"
               @doHsUpdate="doUpdate"></UpdatePop>
  </section>
</template>

<script>
  import { hsVerificationTabs } from './js/hsVerificationTabs'

  export default {
    name: 'hsVerificationTabs',
    mixins: [hsVerificationTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
