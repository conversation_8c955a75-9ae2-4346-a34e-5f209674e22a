import ImportPage from 'xdo-import'
import { excelExport } from '@/api'
import DcImport from '@/components/dc-import'
import { productClassify } from '@/view/cs-common'
import { isNumber, isNullOrEmpty } from '@/libs/util'
import PackingConfigEdit from '../packing-config-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const packingConfigList = {
  name: 'packingConfigList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    DcImport,
    ImportPage,
    PackingConfigEdit
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      listConfig: {
        colOptions: true
      },
      importShow: false,
      updateImportShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload
      },
      cmbSource: {
        gmark: productClassify.GMARK_SELECT
      }
    }
  },
  methods: {
    getParams() {
      return [{
        key: 'gmark',
        type: 'select',
        title: '物料类型',
        defaultValue: 'E'
      }, {
        key: 'facGNo',
        title: '企业料号',
        props: {
          maxlength: 50
        }
      }, {
        key: 'cartonNo',
        title: '企业包装方式',          // 箱号
        props: {
          maxlength: 20
        }
      }]
    },
    getFields() {
      return [{
        width: 100,
        key: 'gmark',
        title: '物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.gmark)
        }
      }, {
        width: 160,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 160,
        key: 'cartonNo',
        title: '企业包装方式'          // 箱号
      }, {
        width: 180,
        key: 'cartonSize',
        title: '包装尺寸 (cm)',       // '每箱尺寸(长*宽*高) cm',
        render: (h, params) => {
          let result = '',
            cartonL = params.row['cartonL'],    // 长
            cartonW = params.row['cartonW'],    // 宽
            cartonH = params.row['cartonH']     // 高
          if (isNumber(cartonL) && isNumber(cartonW) && isNumber(cartonH)) {
            result = String(cartonL) + ' * ' + String(cartonW) + ' * ' + String(cartonH)
          }
          return h('span', {}, result)
        }
      }, {
        width: 150,
        key: 'volume',
        title: '包装体积(m3)'        // '每箱体积 m3'
      }, {
        width: 150,
        key: 'cartonWt',
        title: '包装物重量(kg)'      // '箱重 kg'
      }, {
        width: 150,
        key: 'cartonQty',
        title: '包装物容量',           // '物料数/箱'
        render: (h, params) => {
          let me = this,
            cartonQty = params.row['cartonQty'],
            cartonUnit = params.row['cartonUnit'],
            unitName = ''
          if (!isNullOrEmpty(cartonUnit)) {
            unitName = me.pcodeGet('UNIT', cartonUnit)
          }
          return h('span', {}, String(cartonQty) + ' ' + unitName)
        }
      }, {
        width: 150,
        key: 'palletSize',
        title: '托盘尺寸(cm)'
      }, {
        width: 150,
        key: 'palletQty',
        title: '托盘装载量'           // '箱数/托盘'
        // }, {
        //   width: 150,
        //   key: 'netWt',
        //   title: '净重 kg'
      }]
    },
    /**
     * 获取修改字段
     */
    getModifyFields() {
      return [{
        key: 'gmark',
        value: '物料类型'
      }, {
        key: 'facGNo',
        value: '企业料号'
      }, {
        key: 'cartonNo',
        value: '企业包装方式'
      }, {
        key: 'cartonQty',
        value: '包装物容量'
      }, {
        key: 'cartonUnit',
        value: '包装物容量单位'
      }, {
        key: 'cartonL',
        value: '包装尺寸-长(cm)'
      }, {
        key: 'cartonW',
        value: '包装尺寸-宽(cm)'
      }, {
        key: 'cartonH',
        value: '包装尺寸-高(cm)'
      }, {
        key: 'cartonWt',
        value: '包装物重量(kg)'
      }, {
        key: 'palletQty',
        value: '托盘装载量'
      }, {
        key: 'palletL',
        value: '托盘尺寸-长(cm)'
      }, {
        key: 'palletW',
        value: '托盘尺寸-宽(cm)'
      }, {
        key: 'palletH',
        value: '托盘尺寸-高(cm)'
      }, {
        key: 'maxLayers',
        value: '最高层数'
      }]
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导入
     */
    handleImport() {
      let me = this
      me.$set(me, 'importShow', true)
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.$set(me, 'importShow', false)
      me.$set(me, 'updateImportShow', false)
      me.getList()
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl + '/0', me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 修改导出
     */
    downLoadModify() {
      let me = this
      me.$set(me, 'downloading', true)
      let params = {}
      if (typeof me.getSearchParams === 'function') {
        params = me.getSearchParams()
      }
      excelExport(me.ajaxUrl.exportUrl + '/1', {
        exportColumns: params,
        header: me.getModifyFields(),
        name: me.listConfig.exportTitle
      }).finally(() => {
        me.$set(me, 'downloading', false)
      })
    },
    /**
     * 修改导入
     */
    uploadModify() {
      let me = this
      me.$set(me, 'updateImportShow', true)
    }
  }
}
