import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import ApplicableCustomersEdit from '../applicable-customers-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'

export const applicableCustomersList = {
  name: 'applicableCustomersList',
  mixins: [baseSearchConfig, listDataProcessing],
  components: {
    ApplicableCustomersEdit
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {
          sid: ''
        },
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let fields = this.getFields()
    return {
      baseFields: [
        ...fields
      ],
      pageParam: {
        limit: 100
      },
      pmsLevel: 'body',
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete
      }
    }
  },
  watch: {
    'parentConfig.editStatus': {
      immediate: true,
      handler: function (status) {
        let me = this
        if (status === editStatus.EDIT) {
          me.$set(me.listConfig, 'disable', false)
        } else {
          me.$set(me.listConfig, 'disable', true)
        }
        me.loadListConfig()
      }
    }
  },
  computed: {
    realActions() {
      let me = this,
        disabled = me.parentConfig.editStatus !== editStatus.EDIT
      return me.actions.map(action => {
        action.disabled = disabled
        return action
      })
    }
  },
  methods: {
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      if (isNullOrEmpty(me.parentConfig.editData.sid)) {
        paramObj['headId'] = 'undefined'
      } else {
        paramObj['headId'] = me.parentConfig.editData.sid
      }
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        flex: 1,
        // width: 188,
        title: '客户名称',
        key: 'clientCode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'clientCode', 'clientName')
        }, true)
        // }, {
        //   flex: 1,
        //   title: '联系人1',
        //   key: 'linkmanName1',
        //   cellRendererFramework: me.baseCellRenderer(null, true)
        // }, {
        //   flex: 1,
        //   title: '联系人2',
        //   key: 'linkmanName2',
        //   cellRendererFramework: me.baseCellRenderer(null, true)
        // }, {
        //   flex: 1,
        //   title: '联系人3',
        //   key: 'linkmanName3',
        //   cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      me.$set(me.editConfig, 'editData', {})
      me.$set(me.listConfig, 'selectRows', [])
      me.$set(me.editConfig, 'editStatus', editStatus.ADD)
      me.$set(me.editConfig, 'headId', me.parentConfig.editData.sid)
      me.$set(me, 'showList', false)
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck(row, '编辑')) {
        me.$set(me.editConfig, 'editData', row)
        me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        me.$set(me.editConfig, 'headId', me.parentConfig.editData.sid)
        me.$set(me, 'showList', false)
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
