<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="120"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
      <div style="padding: 0 2px; background-color: white; margin: 2px;">
        <AcmpInfoListCustom :sid="editConfig.editData.sid" title="资料图片-产品图片一" :showAction="showProductAction" :just-view="!showAction" business-type="first" :height="tableHeight"
                            @attachFile="attachFile"></AcmpInfoListCustom>
        <AcmpInfoListCustom :sid="singleWeighSid" title="资料图片-产品图片二" :showAction="showSingleAction" :just-view="!showAction" business-type="second" :height="tableHeight"
                            @attachFile="attachFile"></AcmpInfoListCustom>
      </div>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { importExportManage } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import AcmpInfoListCustom from '../turning/components/AcmpInfoListCustom'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'stampingEdit',
    mixins: [baseDetailConfig],
    components: {
      AcmpInfoListCustom
    },
    props: {
      inSource: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        fileName: '',
        isInit: false,
        attachForm: {},
        copImgList: [],
        copExgList: [],
        selectedFile: null,
        formName: 'frmData',
        showFileInput: true,
        showSingleAction: false,
        showProductAction: false,
        editForm: {
          acmpNo: '',
          acmpFormat: '',
          acmpType: '',
          billSerialNo: '',
          businessSid: '',
          businessType: this.businessType,  // 单证类型：必填，字符型，长度1位（2-手册、3-报核、4－清单、5－质疑
        },
        ajaxUrl: {
          insert: csAPI.csMaterielCenter.stamping.insert,
          update: csAPI.csMaterielCenter.stamping.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {
          ...this.inSource,
          tradeTerms: importExportManage.tradeTermList,
          copImgNo: this.copImgList,
          imgSerialNo: this.imgSerialNoList,
          copExgNo: this.copExgList,
          exgSerialNo: this.exgSerialNoList
        }
      },
      showAction() {
        return !!(this.editConfig && this.editConfig.editStatus === editStatus.EDIT)
      },
      singleWeighSid() {
        if (!isNullOrEmpty(this.editConfig.editData.sid)) {
          return this.editConfig.editData.sid + '-si'
        }
        return ''
      },
      imgSerialNoList() {
        let serialNoList = []
        if (this.copImgList.length > 0) {
          serialNoList = [].concat(this.copImgList)
          serialNoList = serialNoList.map(item => {
            return {
              copImgNo: item.label,
              value: item.imgSerialNo,
              label: item.imgSerialNo,
              copImgUnit: item.copImgUnit,
              copImgName: item.copImgName
            }
          })
        }
        return serialNoList
      },
      exgSerialNoList() {
        let serialNoList = []
        if (this.copExgList.length > 0) {
          serialNoList = [].concat(this.copExgList)
          serialNoList = serialNoList.map(item => {
            return {
              copExgNo: item.label,
              value: item.exgSerialNo,
              label: item.exgSerialNo,
              copExgUnit: item.copExgUnit,
              copExgName: item.copExgName
            }
          })
        }
        return serialNoList
      }
    },
    methods: {
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
      },
      getFields() {
        return [{
          isCard: true,
          title: '冲压类信息',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'facGNo',
          title: '荣益品号',
          required: true,
        }, {
          key: 'facGName',
          title: '成品品名'
        }, {
          key: 'version',
          title: '版本号',
          required: true,
        }, {
          key: 'originalNo',
          title: '原料品号',
        }, {
          key: 'originalName',
          title: '原料品名',
        }, {
          key: '123123',
          type: 'empty_formItem',
        }, {
          title: '密度(克/立方厘米)',
          key: 'density',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: '料厚mm',
          key: 'thickNess',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: '料宽mm',
          key: 'width',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          key: 'distance',
          title: '料距mm',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          key: 'touchPointNum',
          title: '模穴数',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: '模具图耗用(克/个)',
          key: 'mouldExpend',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          key: 'netConsumption',
          title: 'ERP净耗(克/个)',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          key: 'lossAmount',
          title: 'ERP损耗量(克/个)',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: 'ERP损耗率(%)',
          key: 'lossRate',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: '实际称重(克/个)',
          key: 'actualWeigh',
          type: 'xdoInput',
          props: {
            intDigits: 18,
            precision: 5,
          }
        }, {
          title: '状态',
          key: 'status',
          type: 'select',
          defaultValue: '1'
        }, {
          key: '124124',
          type: 'empty_formItem',
        }, {
          title: '备案成品料号',
          key: 'copExgNo',
          type: 'select',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          },
          on: {
            change: this.getIInfoForCopExgNo
          }
        }, {
          title: '备案成品序号',
          key: 'exgSerialNo',
          type: 'select',
          props: {
            optionLabelRender: (opt) => opt.label
          },
          on: {
            change: this.getIInfoForexgSerialNo
          }
        }, {
          key: 'copExgUnit',
          title: '备案成品申报单位',
          props: {
            disabled: true
          }
        }, {
          key: 'copExgName',
          title: '备案成品品名',
          props: {
            disabled: true
          }
        }, {
          key: '125125',
          type: 'empty_formItem',
          itemClass: 'dc-merge-2-4'
        }, {
          key: 'copImgNo',
          title: '备案料件料号',
          type: 'select',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          },
          on: {
            change: this.getIInfoForCopImgNo
          }
        }, {
          key: 'imgSerialNo',
          title: '备案料件序号',
          type: 'select',
          props: {
            optionLabelRender: (opt) => opt.label
          },
          on: {
            change: this.getIInfoForimgSerialNo
          }
        }, {
          key: 'copImgUnit',
          title: '备案料件申报单位',
          props: {
            disabled: true
          }
        }, {
          title: '备案料件品名',
          key: 'copImgName',
          props: {
            disabled: true
          }
        }, {
          title: '手册号',
          key: 'emsNo',
          type: 'select',
          props: {
            optionLabelRender: (opt) => opt.label
          },
          on: {
            change: this.getIeInfoForEmsNo
          }
        }, {
          title: '备注1',
          key: 'remark1',
        }, {
          title: '备注2',
          key: 'remark2',
        }, {
          title: '备注3',
          key: 'remark3'
        }]
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      },
      getIeInfoForEmsNo() {
        let me = this
        if (me.detailConfig.model.emsNo) {
          me.$http.post(csAPI.csMaterielCenter.turning.getIeInfoForEmsNo, {
            GMark: '',
            emsNo: me.detailConfig.model.emsNo
          }).then(res => {
            me.copImgList = (res.data.data.I && res.data.data.I.length > 0) ? res.data.data.I.map(item => {
              return {
                label: item.copGNo,
                value: item.copGNo,
                copImgUnit: item.unit,
                copImgName: item.gname,
                imgSerialNo: item.serialNo
              }
            }) : []
            me.copExgList = (res.data.data.E && res.data.data.E.length > 0) ? res.data.data.E.map(item => {
              return {
                label: item.copGNo,
                value: item.copGNo,
                copExgUnit: item.unit,
                copExgName: item.gname,
                exgSerialNo: item.serialNo
              }
            }) : []
          }, () => {
          }).finally(() => {
            if (me.isInit) {
              me.$set(me.detailConfig.model, 'copExgNo', '')
              me.$set(me.detailConfig.model, 'exgSerialNo', '')
              me.$set(me.detailConfig.model, 'copExgUnit', '')
              me.$set(me.detailConfig.model, 'copExgName', '')
              me.$set(me.detailConfig.model, 'copImgNo', '')
              me.$set(me.detailConfig.model, 'imgSerialNo', '')
              me.$set(me.detailConfig.model, 'copImgUnit', '')
              me.$set(me.detailConfig.model, 'copImgName', '')
            }
            me.isInit = true
          })
        }
      },
      getIInfoForCopExgNo(val) {
        let me = this
        //  根据备案成品料号返回相应栏位得值
        if (isNullOrEmpty(me.copExgList)) {
          me.$set(me.detailConfig.model, 'exgSerialNo', '')
          me.$set(me.detailConfig.model, 'copExgUnit', '')
          me.$set(me.detailConfig.model, 'copExgName', '')
        } else {
          if (val) {
            let params = []
            params = me.copExgList.filter(item => item.label === val)
            me.$set(me.detailConfig.model, 'exgSerialNo', params[0].exgSerialNo)
            me.$set(me.detailConfig.model, 'copExgUnit', params[0].copExgUnit)
            me.$set(me.detailConfig.model, 'copExgName', params[0].copExgName)
          }
        }
      },
      getIInfoForCopImgNo(val) {
        let me = this
        //  根据备案料件料号返回相应栏位得值
        if (isNullOrEmpty(me.copImgList)) {
          me.$set(me.detailConfig.model, 'imgSerialNo', '')
          me.$set(me.detailConfig.model, 'copImgUnit', '')
          me.$set(me.detailConfig.model, 'copImgName', '')
        } else {
          if (val) {
            let params = []
            params = me.copImgList.filter(item => item.label === val)
            me.$set(me.detailConfig.model, 'imgSerialNo', params[0].imgSerialNo)
            me.$set(me.detailConfig.model, 'copImgUnit', params[0].copImgUnit)
            me.$set(me.detailConfig.model, 'copImgName', params[0].copImgName)
          }
        }
      },
      getIInfoForexgSerialNo() {
        let me = this
        //  根据备案成品序号返回相应栏位得值
        if (me.detailConfig.model.exgSerialNo) {
          let params = []
          params = me.exgSerialNoList.filter(item => item.label === me.detailConfig.model.exgSerialNo)
          me.$set(me.detailConfig.model, 'copExgNo', params[0].copExgNo)
          me.$set(me.detailConfig.model, 'copImgUnit', params[0].copImgUnit)
          me.$set(me.detailConfig.model, 'copImgName', params[0].copImgName)
        }
      },
      getIInfoForimgSerialNo() {
        let me = this
        //  根据备案料件序号返回相应栏位得值
        if (me.detailConfig.model.imgSerialNo) {
          let params = []
          params = me.imgSerialNoList.filter(item => item.label === me.detailConfig.model.imgSerialNo)
          me.$set(me.detailConfig.model, 'copImgNo', params[0].copImgNo)
          me.$set(me.detailConfig.model, 'copImgUnit', params[0].copImgUnit)
          me.$set(me.detailConfig.model, 'copImgName', params[0].copImgName)
        }
      },
      attachFile(val, acmpType) {
        let me = this
        if (me.showDisable) {
          me.showProductAction = false
          me.showSingleAction = false
        } else {
          if (val && val.length > 0) {
            val.map(item => {
              if (item.acmpType === 'first') {
                return me.showProductAction = false
              } else if (item.acmpType === 'second') {
                return me.showSingleAction = false
              }
            })
          } else {
            if (acmpType === 'first') {
              me.showProductAction = true
            } else if (acmpType === 'second') {
              me.showSingleAction = true
            }
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
