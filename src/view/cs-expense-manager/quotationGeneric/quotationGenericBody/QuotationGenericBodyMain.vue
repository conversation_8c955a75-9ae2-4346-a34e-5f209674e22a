<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions" v-if="isLoadActoinsBody">
          <xdo-toolbar @click = "handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" rowSelection="multiple" :checkboxSelection="checkboxSelection"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="250"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <QuotationGenericBodyDetail v-if="!showList" :editConfig="editConfig" :editConfigH="this.editConfigH"
                                @onEditBack="editBack"></QuotationGenericBodyDetail>
  </section>
</template>

<script>
  import { mainJS } from './js/quotationGenericBodyMain'
  import { columns } from './js/quotationGenericBodyColumns'
  import QuotationGenericBodyDetail from './QuotationGenericBodyDetail'

  export default {
    moduleName: '报价单表体',
    name: 'QuotationFastBodyMain',
    components: {
      QuotationGenericBodyDetail
    },
    mixins: [columns, mainJS]
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
