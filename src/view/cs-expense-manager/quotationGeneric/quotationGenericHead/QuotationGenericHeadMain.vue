<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <QuotationGenericHeadSearch ref="headSearch"></QuotationGenericHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <QuotationGenericHeadDetail v-if="!showList" :in-source="cmbSource" :editConfig="editConfig"
                                @onEditBack="editBack"></QuotationGenericHeadDetail>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { mainJS } from './js/quotationGenericHeadMain'
  import { columns } from './js/quotationGenericHeadColumns'
  import QuotationGenericHeadDetail from './QuotationGenericHeadDetail'
  import QuotationGenericHeadSearch from './QuotationGenericHeadSearch'

  export default {
    name: 'QuotationGenericHeadMain',
    moduleName: '报价单表头信息(一般)',
    components: {
      QuotationGenericHeadSearch,
      QuotationGenericHeadDetail,
    },
    mixins: [columns, mainJS],
    data() {
      return {
        cmbSource: {
          exchequer: [],
          courseNameData: []
        }
      }
    },
    created: function () {
      /**
       * 收款单位名称
       */
      let me = this
      me.$http.post(csAPI.ieParams.selectComboxByCode + '/FOD,CUT').then(res => {
        me.$set(me.cmbSource, 'exchequer', res.data.data.map(item => {
          return {
            type: item['TYPE'],
            value: item['VALUE'],
            label: item['LABEL']
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'exchequer', [])
      })
      /**
       * 获取费用科目信息
       */
      me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, {}).then(res => {
        me.$set(me.cmbSource, 'courseNameData', res.data.data.map(item => {
          return {
            label: item.costCourseName,
            value: item.costCourseCode
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'courseNameData', [])
      })
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
