import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      buttons: [
        {...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'},
        {...btnComm, click: this.handleSaveClose, icon: 'dc-btn-save', label: '保存返回'},
        {...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        quoNo: '',            //报价单编号
        quoName: '',          //报价单名称
        exchequerType: '',    //收款单位类型
        exchequerName: '',    //收款单位名称
        ieType: '',           //进出口类型
        effDateFrom: null,    //有效开始日期
        effDateTo: null,      //有效结束日期
        curr: '',             //币制
        // goodsType :'',        // 物品种类
        // serviceType :'',      // 服务类型
        note: '',             //备注
        insertTime: null,     //创建时间
        status: '',           //使用状态
        tradeTerms: '',       //贸易条款
        quoType: '1',         //报价单类型  //报价单类型（1 一般报价单 2 快件报价单）
        trafMode: '',         //运输方式
        despPort: '',         //启运港
        destPort: '',         //指运港
        iePort: '',
        domesticLogistics: '',
        internationalLogistics: ''
      }
    }
  }
}
