import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus } from '@/view/cs-common'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 4,
      isShowSetting: true,
      gridConfig: {
        exportTitle: '报价单表头信息'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // 新增
        'edit': this.handleEdit,         // 编辑
        'delete': this.handleDelete,     // 删除
        'export': this.handleDownload    // 导出
      },
      ajaxUrl: {
        delete: csAPI.expenseManager.quotationFast.delete,
        selectComboxByCode: csAPI.ieParams.selectComboxByCode,
        exportUrl: csAPI.expenseManager.quotationFast.exportUrl,
        selectAllPaged: csAPI.expenseManager.quotationFast.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.customCheck([row], '编辑')) {
        if (row.status === '1') { // 已使用  不可编辑
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认修改',
            cancelText: '取消',
            content: '该报价单已使用，是否要修改?',
            onOk: () => {
              me.editConfig.editData = row
              me.editConfig.editStatus = editStatus.EDIT
              me.showList = false
            }
          })
        } else {
          me.editConfig.editData = row
          me.editConfig.editStatus = editStatus.EDIT
          me.showList = false
        }
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
