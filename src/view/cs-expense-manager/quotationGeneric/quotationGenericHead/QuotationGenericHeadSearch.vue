<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="quoNo" label="报价单编号">
        <XdoIInput type="text" v-model="searchParam.quoNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="quoName" label="报价单名称">
        <XdoIInput type="text" v-model="searchParam.quoName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="exchequerType" label="收款单位类型">
        <xdo-select v-model="searchParam.exchequerType" :options="expenseManager.exchequerType" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="exchequerName" label="收款单位名称">
        <xdo-select v-model="searchParam.exchequerName" :options="exchequerNameComData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="进出口类型">
        <xdo-select v-model="searchParam.ieType" :options="expenseManager.ieType" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="effDateFromStr" label="有效开始日期">
        <XdoDatePicker type="date" placeholder="请选择日期" v-model="searchParam.effDateFromStr" transfer></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem prop="effDateToStr" label="有效结束日期">
        <XdoDatePicker type="date" placeholder="请选择日期" v-model="searchParam.effDateToStr" transfer></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" meta="TRANSF_OUTDATED" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeTerms" label="贸易条款">
      <xdo-select v-model="searchParam.tradeTerms" :options="importExportManage.tradeTermList" :optionLabelRender="opt => opt.label"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="despPort" label="启运港">
        <xdo-select v-model="searchParam.despPort" meta="PORT_LIN" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="destPort" label="指运港">
        <xdo-select v-model="searchParam.destPort" meta="PORT_LIN" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { expenseManager, importExportManage } from '@/view/cs-common/constant'

  export default {
    name: 'QuotationGenericHeadSearch',
    data() {
      return {
        exchequerNameComData: [],
        expenseManager: expenseManager,
        importExportManage: importExportManage,
        searchParam: {
          quoNo: '',                //报价单编号
          quoName: '',              //报价单名称
          exchequerType: '',        //收款单位类型
          exchequerName: '',        //收款单位名称
          ieType: '',               //进出口类型
          effDateFromStr: null,     //有效开始日期
          effDateToStr: null,       //有效结束日期
          quoType: '1'              //报价单类型  // 报价单类型（1 一般报价单 2 快件报价单）
        }
      }
    },
    created() {
      /**
       * 获取货代和报关行
       */
      let me = this
      me.$http.post(csAPI.ieParams.selectComboxByCode + '/FOD,CUT').then(res => { //收款单位名称
        me.exchequerNameComData = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.exchequerNameComData = []
      })
    }
  }
</script>

<style scoped>
</style>
