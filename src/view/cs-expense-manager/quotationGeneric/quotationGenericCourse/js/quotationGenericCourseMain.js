import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus } from '@/view/cs-common'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  /**
   * 表头选择的数据
   */
  props: {
    editConfigH: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      // 查询条件行数
      searchLines: 1,
      p_group: 'body1',
      gridConfig: {
        exportTitle: '报价单科目'
      },
      cmbSource: {
        cabinetType: [{
          value: '0', label: '普货'
        }, {
          value: '1', label: '冷柜'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete      // '删除'
      },
      ajaxUrl: {
        delete: csAPI.expenseManager.quotationGenericCourse.delete,
        selectAllPaged: csAPI.expenseManager.quotationGenericCourse.selectAllPaged
      },
      isLoadActoinsBody: this.editConfigH.editStatus !== editStatus.SHOW  // true 加载   false 不加载
    }
  },
  methods: {
    /**
     * 弹出导入窗体
     */
    handleImport() {
      this.modelImportShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      this.modelImportShow = false
      this.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 重写：获取查询参数
     * @returns {{billNo: *}}
     */
    getSearchParams() {
      return {
        headId: this.editConfigH.editData.sid
      }
    },
    loadFunctionsAfterOne() {
      if (!this.isLoadActoinsBody) {
        this.actions = []
      }
    }
  }
}
