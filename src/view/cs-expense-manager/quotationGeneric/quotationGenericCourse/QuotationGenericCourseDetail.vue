<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="courseType" label="费用科目大类">
            <xdo-select v-model="frmData.courseType" :options="this.expenseManageData.courseType"
                        :optionLabelRender="pcodeRender" :disabled="showDisable" :clearable="!showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="courseCode" label="科目名称">
            <xdo-select v-model="frmData.courseCode" :options="this.courseNameData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable" :clearable="!showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="chargeType" label="计费种类">
            <xdo-select v-model="frmData.chargeType" :options="this.expenseManageData.chargeType"
                        :optionLabelRender="pcodeRender" :disabled="chargeTypeDisabled" :clearable="!showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="frmData.curr" meta="CURR_OUTDATED"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="minCharge" label="最低收费标准">
            <xdo-input type="text" v-model="frmData.minCharge" decimal
                       int-length="8" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="maxCharge" label="最高收费标准">
            <xdo-input type="text" v-model="frmData.maxCharge" decimal
                       int-length="8" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem v-if="cabinetTypeShow" prop="cabinetType" label="柜属性">
            <xdo-select v-model="frmData.cabinetType" :options="cmbSource.cabinetType"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="freeStore" label="免仓储天数">
            <xdo-input type="text" v-model="frmData.freeStore" decimal
                       int-length="3" precision="0" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="500"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNumber } from '@/libs/util'
  import { expenseManager } from '@/view/cs-common/constant'
  import { detailJS } from './js/quotationGenericCourseDetail'

  export default {
    name: 'QuotationGenericCourseDetail',
    mixins: [detailJS],
    data() {
      let validateMinMax = (rule, value, callback) => {
        let me = this
        if (isNumber(value) && isNumber(me.frmData.minCharge)) {
          if (Number(value) < Number(me.frmData.minCharge)) {
            callback(new Error('最高收费标准不能小于最低收费标准!'))
          }
        }
        callback()
      }
      return {
        hasBody: false,
        firstLoading: true,
        courseNameData: [],
        formName: 'dataForm',
        courseNameDataAll: [],
        cmbSource: {
          cabinetType: [{
            value: '0', label: '普货'
          }, {
            value: '1', label: '冷柜'
          }]
        },
        expenseManageData: expenseManager,
        ajaxUrl: {
          insert: csAPI.expenseManager.quotationGenericCourse.insert,
          update: csAPI.expenseManager.quotationGenericCourse.update
        },
        rulesHeader: {
          maxCharge: [validateMinMax],
          curr: [{required: true, message: '不能为空!', trigger: 'blur'}],
          headId: [{required: true, message: '不能为空!', trigger: 'blur'}],
          courseType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          courseCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          chargeType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          cabinetType: [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    created() {
      /**
       * 科目名称 courseName
       * costCourseCode  费用科目代码
       * costCourseName  费用科目名称
       * courseType      费用科目大类
       * ieMark
       * note            备注
       * status          状态
       * validStatus     有效状态 0 无效 1 有效
       */
      let me = this,
        ieType = me.editConfigH.editData.ieType
      if (ieType === 'I') {
        ieType = 'I,N'
      } else if (ieType === 'E') {
        ieType = 'E,N'
      }
      me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, {
        iemark: ieType,
        validStatus: '1'
      }).then(res => {
        me.courseNameDataAll = res.data.data
        me.handleGetCourseNameData(me.frmData.courseType)
      }).catch(() => {
      }).finally(() => {
      })
    },
    watch: {
      /**
       * 费用科目大类
       * courseType:[
       * { value: '1', label: '国外段' },
       * { value: '2', label: '国际段' },
       * { value: '3', label: '国内段' },
       * { value: '4', label: '其他' }],
       */
      'frmData.courseType': {
        handler: function (val) {
          let me = this
          me.handleGetCourseNameData(val)
        }
      },
      /**
       * 计费种类
       * 改变时去改变 第二个表体的显示列
       */
      'frmData.chargeType': {
        handler: function () {
          let me = this
          me.$emit('changeChargeType', me.frmData)
        }
      }
    },
    computed: {
      /**
       * 当计费种类选择为【4（天/重量计）】【5（天/体积计）】时，显示[柜属性]栏位
       */
      cabinetTypeShow() {
        let me = this
        return ['4', '5'].includes(me.frmData.chargeType)
      },
      /**
       * 计费种类是否只读
       */
      chargeTypeDisabled() {
        let me = this
        if (me.showDisable) {
          return true
        }
        return me.editConfig.editData.flag === '1'
      }
    },
    methods: {
      /**
       * 通过 科目种类 获取 科目名称数据源
       * @param val
       */
      handleGetCourseNameData(val) {
        if (val) {
          let me = this
          me.courseNameData = me.courseNameDataAll.filter(x => x.courseType === val).map(item => {
            return {
              value: item.costCourseCode,
              label: item.costCourseName
            }
          })
          if (me.firstLoading) {
            me.$set(me, 'firstLoading', false)
          } else {
            me.$set(me.frmData, 'courseCode', '')
          }
        }
      },
      beforeSave() {
        let me = this
        if (me.editConfig.editData.flag === '1') {
          me.$set(me, 'hasBody', true)
        } else {
          me.$set(me, 'hasBody', false)
        }
      },
      afterSave() {
        let me = this
        me.$nextTick(() => {
          if (me.hasBody) {
            me.$set(me.editConfig.editData, 'flag', '1')
          } else {
            me.$set(me.editConfig.editData, 'flag', '0')
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
