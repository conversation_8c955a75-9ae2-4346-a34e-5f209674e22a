<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click = "handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" height="250" rowSelection="single"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data"
                     :checkboxSelection="checkboxSelection"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <QuotationGenericCourseDetail style="height: 250px;" v-if="!showList" :editConfig="editConfig" :edit-config-h="editConfigH"
                                  @onEditBack="editBack" @changeChargeType="handleChangeChargeType"></QuotationGenericCourseDetail>
  </section>
</template>

<script>
  import { mainJS } from './js/quotationGenericCourseMain'
  import { columns } from './js/quotationGenericCourseColumns'
  import QuotationGenericCourseDetail from './QuotationGenericCourseDetail'

  export default {
    name: 'QuotationGenericCourseMain',
    moduleName: '报价单科目',
    components: {
      QuotationGenericCourseDetail,
    },
    /**
     * 表头选择的数据
     */
    props: {
      inSource: {
        type: Object,
        default: () => ({
          courseNameData: []
        })
      }
    },
    mixins: [columns, mainJS],
    methods: {
      /**
       * 编辑页面传出来的值    在 计费种类 被改变的时候 watch
       * @param frmData
       */
      handleChangeChargeType(frmData) {
        let me = this
        me.$emit('changeHang', [frmData])
      }
    },
    watch: {
      /**
       * 选择行的时候改变表体2 的显示列
       */
      'gridConfig.selectRows': {
        deep: true,
        immediate: true,
        handler: function () {
          let me = this
          me.$emit('changeHang', me.gridConfig.selectRows)
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
