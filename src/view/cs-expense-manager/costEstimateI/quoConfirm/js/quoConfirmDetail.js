import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      buttons: [
        {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
        {...btnComm, label: '预估费用', icon: 'dc-btn-save', click: this.handleYuGu},
        {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  methods: {
    /**
     * 返回
     */
    handleBack() {
      let me = this
      me.$emit('onEditBack')
    },
    /**
     *费用预估
     */
    handleYuGu() {
      let me = this
      me.$refs[me.formName].validate().then(isValid => {
        if (isValid) {
          me.handleSaveMe().then((res) => {
            me.frmData = Object.assign(me.frmData, res.data.data)
            me.$emit('onSaveSussess', me.frmData)
            if (!me.frmData.sid) {
              me.$Message.warning('请先保存数据，然后在进行预估!')
              return
            }
            me.buttons[1].loading = true
            me.$refs[me.formName].validate().then(isValid => {
              if (isValid) {
                let url = `${me.ajaxUrl.costCal}/${me.editConfig.editData.headId}`
                me.$http.post(url).then(() => {
                  me.$Message.success('预估完成!')
                  me.$emit('onComputeSussess', true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[1].loading = false
                })
              }
            })
          })
        }
      })
    },
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      let me = this
      me.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      if (isNullOrEmpty(me.formName)) {
        console.error('未设置form的名称：me.formName')
        return
      }
      me.$refs[me.formName].validate().then(isValid => {
        if (isValid) {
          me.buttons[0].loading = true
          me.handleSaveMe().then((res) => {
            me.frmData = Object.assign(me.frmData, res.data.data)
            me.$emit('onSaveSussess', me.frmData)
            me.$Message.success('保存成功!')
          }).catch(() => {
          }).finally(() => {
            me.buttons[0].loading = false
          })
        }
      })
    },
    async handleSaveMe() {
      let me = this
      return await me.$http.post(`${me.ajaxUrl.update}/${me.editConfig.editData.headId}`, me.frmData)
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        sid: '',
        headId: '',             // 对应提单sid,
        quoNo: '',              // 报价单编号
        weight: null,           // 计费重量
        cabinetType: '',        // 车柜类型
        cabinetAmount: null,    // 车柜数量
        quoTotal: null,         // 费用总额（rmb）

        quoSid: '',             // 报价单表头sid
        quoName: '',            // 报价单名称
        quoType: '',            // 报价单类型
        quoNote: '',            // 报价单表头备注

        insertTime: null,       // 创建时间
        maintainStatus: '',     // 维护状态
        lockStatus: '',         // 锁定状态
        volume: null            // 计费体积
      }
    }
  }
}
