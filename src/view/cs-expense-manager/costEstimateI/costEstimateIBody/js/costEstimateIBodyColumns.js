
import { expenseManager } from '@/view/cs-common/constant'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '维护状态', width: 130, key: 'maintainStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, expenseManager.maintainStatus)
        }
      },
      {
        title: '费用科目大类', width: 130, key: 'courseType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, expenseManager.courseType)
        }
      },
      {
        title: '费用科目名称', width: 130, key: 'courseCode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.inSource.courseNameData)
        }
      },
      {title: '预估金额', width: 130, key: 'decTotal'},
      {
        title: '币制', width: 130, key: 'curr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }
      },
      {title: '汇率', width: 130, key: 'exchangeRate'},
      {title: '人民币金额', width: 130, key: 'totalRmb'},
      {title: '确认金额(RMB)', width: 130, key: 'confTotal'},
      {title: '备注', width: 130, key: 'note'}
    ]
    return {
      totalColumns: [
        ...totalColumnsBase,
      ],
      courseNameData: []
    }
  }
}

export {
  columns
}
