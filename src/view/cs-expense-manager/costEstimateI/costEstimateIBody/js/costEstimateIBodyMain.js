import pms from '@/libs/pms'
import { csAPI, excelExport } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      gridConfig: {
        exportTitle: '进口费用预估明细'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload    // '导出'
      },
      ajaxUrl: {
        delete: csAPI.expenseManager.costEstimateI.list.delete,
        exportUrl: csAPI.expenseManager.costEstimateI.list.export,
        selectAllPaged: csAPI.expenseManager.costEstimateI.list.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 执行导出
     * @param exportUrl
     * @param btnIndexOrKey
     * @param finallyFun
     */
    doExport(exportUrl, btnIndexOrKey, finallyFun) {
      let me = this
      me.$nextTick(() => {
        me.setButtonLoading(btnIndexOrKey, true)
        let params = {erpHeadId: this.editConfigH.editData.relationSid}
        excelExport(exportUrl, {
          exportColumns: params,
          name: me.gridConfig.exportTitle,
          header: me.gridConfig.exportColumns
        }).finally(() => {
          me.setButtonLoading(btnIndexOrKey, false)
          if (typeof finallyFun === 'function') {
            finallyFun.call(me)
          }
        })
      })
    }
  }
}
