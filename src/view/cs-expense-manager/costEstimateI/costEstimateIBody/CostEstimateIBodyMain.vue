<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between;">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
          <span style="font-weight: bold; padding-right: 50px;">预估时间: {{estimateTime}}</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
      </XdoCard>
    </div>
    <CostEstimateIBodyDetail v-if="!showList" :editConfig="editConfig" :editConfigH="editConfigH"
                             @onEditBack="editBack"></CostEstimateIBodyDetail>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import { mainJS } from './js/costEstimateIBodyMain'
  import { columns } from './js/costEstimateIBodyColumns'
  import CostEstimateIBodyDetail from  './CostEstimateIBodyDetail'

  export default {
    name: 'CostEstimateIBodyMain',
    moduleName: '费用价格明细',
    components: {
      CostEstimateIBodyDetail
    },
    mixins: [columns, mainJS],
    props: {
      editConfigH: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      },
      inSource: {
        type: Object,
        default: () => ({
          courseNameData: []
        })
      }
    },
    data() {
      return {
        p_group: 'body',
        estimateTime:''
      }
    },
    methods: {
      loadFunctionsAfterOne() {
        let me = this
        if (me.editConfigH.editStatus === editStatus.SHOW) {
          me.actions = []
        }
      },
      /**
       * 执行查询
       * @param searchUrl
       */
      doSearch(searchUrl) {
        let me = this
        me.tableloading = true
        me.$nextTick(() => {
          me.$http.post(`${searchUrl}/${me.editConfigH.editData.relationSid}`).then(res => {
            me.tableloading = false
            me.gridConfig.data = res.data.data
            me.pageParam.page = res.data.pageIndex
            me.pageParam.dataTotal = res.data.total
            me.estimateTime = res.data.message
            me.afterSearchSuccess()
          }).catch(() => {
            me.afterSearchFailure()
          }).finally(() => {
            me.gridConfig.selectRows = []
            me.afterSearch()
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
