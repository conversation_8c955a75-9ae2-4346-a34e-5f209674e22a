import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      buttons: [
        {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
        {...btnComm, label: '保存返回', icon: 'dc-btn-save', click: this.handleSaveClose},
        {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  /**
   * 方法
   */
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        emsListNo: '',            // 提单内部编号
        maintainStatus: '',       // 维护状态
        lockStatus: '',           // 锁定状态
        forwardName: '',          // 货代名称
        declareNameCustoms: '',   // 申报单位名称
        quoNo: '',                // 报价单编号
        relationSid: '',          //
        ieport: '',               // 出境关别
        overseasShipperName: '',  // 境外发货人名称
        hawb: '',                 // 分提运单
        invoiceNo: '',            // 发票号码
        quoTotal: null,           //
        note: '',                 // 备注
        weight: null,             // 计费重量
        cabinetType: '',          // 车柜类型
        cabinetAmount: null,      // 车柜数量
        attachFlag: null,         //
        insertTime: null,         // 创建时间
        trafMode: '',             // 运输方式
        tradeTerms: '',           // 贸易条款
        tradeCountry: '',         // 运抵国
        despPort: '',             // 指运港
        wrapType: '',             // 包装种类
        packNum: null,            // 件数
        netWt: null,              // 总净重
        grossWt: null,            // 总毛重
        sumQty: null,             // 数量汇总
        sumDecTotal: null         // 金额汇总
      }
    }
  }
}
