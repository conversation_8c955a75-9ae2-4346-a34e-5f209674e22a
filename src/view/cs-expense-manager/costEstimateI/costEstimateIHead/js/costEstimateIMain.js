import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus } from '@/view/cs-common'
import estimatePop from '@/view/cs-expense-manager/components/costEstimate/estimate-pop'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  components: {
    estimatePop
  },
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 3,
      isShowSetting: true,
      gridConfig: {
        exportTitle: '进口费用预估'
      },
      estimationPop: {
        show: false,
        ieMark: 'I',
        automatic: false
      },
      toolbarEventMap: {
        'edit': this.handleEdit,                            // 编辑
        'lock': this.handleLock,                            // 锁定
        'unlock': this.handleUnlock,                        // 解锁
        'export': this.handleDownload,                      // 导出
        'auto-estimation': this.handleAutoEstimation,       // 自动预估
        'manual-estimation': this.handleManualEstimation    // 手动预估
      },
      ajaxUrl: {
        exportUrl: csAPI.expenseManager.costEstimateI.head.exportUrl,
        matchAndCal: csAPI.expenseManager.costEstimateI.head.matchAndCal,
        unlockOrlock: csAPI.expenseManager.costEstimateI.head.unlockOrlock,
        selectAllPaged: csAPI.expenseManager.costEstimateI.head.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.customCheck([row], '编辑')) {
        if (row.lockStatus === '1') {
          me.$Message.warning('编辑失败：已锁定的数据不允许编辑!')
          return
        }
        me.editConfig.editStatus = editStatus.EDIT
        me.editConfig.editData = row
        me.showList = false
      }
    },
    /**
     * 解锁 0 lockStatus
     * { value: '0', label: '未生成' },
     * { value: '1', label: '已生成' },
     * { value: '2', label: '已编辑' }
     */
    handleUnlock() {
      let me = this
      // 维护状态是“未生成”的数据不允许锁定。 maintainStatus
      if (!me.checkRowSelected('解锁', true)) {
        return
      }
      if (me.gridConfig.selectRows[0].lockStatus === '0') {
        me.$Message.warning('数据已解锁，无需在解锁!')
        return
      }
      me.lockOrUnlock('0')
    },
    /**
     * 锁定  1 lockStatus
     * { value: '0', label: '未生成' },
     * { value: '1', label: '已生成' },
     * { value: '2', label: '已编辑' }
     */
    handleLock() {
      let me = this
      // 维护状态是“未生成”的数据不允许锁定。 maintainStatus
      if (!me.checkRowSelected('锁定', true)) {
        return
      }
      if (me.gridConfig.selectRows[0].lockStatus === '1') {
        me.$Message.warning('数据已锁定，无需在锁定!')
        return
      }
      if (me.gridConfig.selectRows[0].maintainStatus === '0') {
        me.$Message.warning('预估状态是《未预估》的数据不允许锁定')
        return
      }
      me.lockOrUnlock('1')
    },
    /**
     * 解锁 锁定
     * @param lockStatus 0 解锁  1 锁定
     * @param toolbarEventMapKey
     */
    lockOrUnlock(lockStatus, toolbarEventMapKey) {
      let me = this
      if (!me.checkRowSelected(lockStatus === '0' ? '解锁' : '锁定', true)) {
        return
      }
      me.setButtonLoading(toolbarEventMapKey, true)
      me.$http.post(`${me.ajaxUrl.unlockOrlock}/${me.gridConfig.selectRows[0].sid}/${lockStatus}`).then(() => {
        me.$Message.success(lockStatus === '0' ? '解锁' : '锁定' + '成功')
        me.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        me.setButtonLoading(toolbarEventMapKey, false)
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 自动预估
     */
    handleAutoEstimation() {
      let me = this
      if (me.checkRowSelected('自动预估')) {
        me.$set(me.estimationPop, 'automatic', true)
        me.$set(me.estimationPop, 'show', true)
      }
    },
    /**
     * 手动预估
     */
    handleManualEstimation() {
      let me = this
      if (me.checkRowSelected('手动预估')) {
        me.$set(me.estimationPop, 'automatic', false)
        me.$set(me.estimationPop, 'show', true)
      }
    },
    /**
     * 执行预估
     * @param estimateParam
     */
    doEstimate(estimateParam) {
      let me = this
      me.$http.post(me.ajaxUrl.matchAndCal, {
        ...estimateParam,
        headIds: me.getSelectedParams()
      }).then(() => {
        me.$Message.success('预估完成!')
        me.handleSearchSubmit()
        me.$set(me.estimationPop, 'show', false)
      }).catch(() => {
      })
    }
  }
}
