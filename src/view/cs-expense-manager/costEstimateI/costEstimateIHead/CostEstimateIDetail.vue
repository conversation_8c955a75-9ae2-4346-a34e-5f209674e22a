<template>
  <section v-focus>
    <Collapse simple v-model="collapseName">
      <Panel name="decErp">
        预录入单信息
        <div slot="content" style="border-top: 1px solid #e8eaec; padding: 12px 0 0 0;">
          <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
            <XdoFormItem prop="emsListNo" label="提单内部编号">
              <XdoIInput type="text" v-model="frmData.emsListNo" disabled :maxlength="50"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="forwardName" label="货代名称">
              <XdoIInput type="text" v-model="frmData.forwardName" disabled :maxlength="100"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="declareNameCustoms" label="申报单位名称">
              <XdoIInput type="text" v-model="frmData.declareNameCustoms" disabled :maxlength="100"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="trafMode" label="运输方式">
              <xdo-select v-model="frmData.trafMode" meta="TRANSF" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="tradeTerms" label="贸易条款">
              <xdo-select v-model="frmData.tradeTerms" :options="this.importExportManage.tradeTermList"
                          disabled :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="despPort" label="启运港">
              <xdo-select v-model="frmData.despPort" meta="PORT_LIN" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="tradeCountry" label="启运国">
              <xdo-select v-model="frmData.tradeCountry" meta="COUNTRY_OUTDATED" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="overseasShipperName" label="境外发货人名称">
              <XdoIInput type="text" v-model="frmData.overseasShipperName" disabled :maxlength="250"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="ieport" label="进境关别">
              <xdo-select v-model="frmData.ieport" meta="CUSTOMS_REL" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="wrapType" label="包装种类">
              <xdo-select v-model="frmData.wrapType" meta="WRAP" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="packNum" label="件数">
              <xdo-input type="text" v-model="frmData.packNum" decimal notConvertNumber
                         int-length="11" precision="0" disabled></xdo-input>
            </XdoFormItem>
            <XdoFormItem prop="netWt" label="总净重">
              <xdo-input type="text" v-model="frmData.netWt" decimal notConvertNumber
                         int-length="22" precision="8" disabled></xdo-input>
            </XdoFormItem>
            <XdoFormItem prop="grossWt" label="总毛重">
              <xdo-input type="text" v-model="frmData.grossWt" decimal notConvertNumber
                         int-length="19" precision="5" disabled></xdo-input>
            </XdoFormItem>
            <!--          数量汇总-->
            <XdoFormItem prop="sumQty" label="总数量">
              <xdo-input type="text" v-model="frmData.sumQty" decimal notConvertNumber
                         int-length="19" precision="5" disabled></xdo-input>
            </XdoFormItem>
            <!--          金额汇总-->
            <XdoFormItem prop="sumDecTotal" label="总金额">
              <xdo-input type="text" v-model="frmData.sumDecTotal" decimal notConvertNumber
                         int-length="25" precision="5" disabled></xdo-input>
            </XdoFormItem>
            <!--          分提运单-->
            <XdoFormItem prop="hawb" label="提运单号">
              <XdoIInput type="text" v-model="frmData.hawb" disabled :maxlength="50"></XdoIInput>
            </XdoFormItem>

            <XdoFormItem prop="invoiceNo" label="发票号">
              <XdoIInput type="text" v-model="frmData.invoiceNo" disabled :maxlength="100"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="note" label="备注">
              <XdoIInput type="text" v-model="frmData.note" disabled :maxlength="512"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="note" label="合同协议号">
              <XdoIInput type="text" v-model="frmData.contrNo" disabled :maxlength="512"></XdoIInput>
            </XdoFormItem>
          </XdoForm>
        </div>
      </Panel>
    </Collapse>
  </section>
</template>

<script>
  import { detailJS } from './js/costEstimateIDetail'
  import { importExportManage } from '@/view/cs-common/constant'

  export default {
    name: 'CostEstimateIDetail',
    mixins: [detailJS],
    data() {
      return {
        ajaxUrl: {},
        rulesHeader: {},
        formName: 'dataForm',
        collapseName: ['decErp'],
        importExportManage: importExportManage
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
