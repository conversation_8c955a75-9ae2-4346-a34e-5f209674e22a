<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="Tab" @on-click="tabClick">
    <TabPane name="head" label="报价单确认" tab="Tab">
      <CostEstimateIDetail v-if="tabs.head" :editConfig="editConfig"></CostEstimateIDetail>
      <quoNoCommEdit v-if="isShowQuoConfirmDetail" :ie-mark="ieMark" :edit-config="editConfigQuo"
                     @baseExists="handleReload" @onEditBack="backToList"></quoNoCommEdit>
      <XdoCard :bordered="false" class="ieLogisticsTrackingCard" title="选择报价单">
        <quoNoMain v-if="isShowQuoConfirmDetail" :ie-mark="ieMark" :edit-config="editConfigQuo"
                   @onEstimateSuccess="onEstimateSuccess"></quoNoMain>
      </XdoCard>
    </TabPane>
    <!-- maintainStatus：已生成 -->
    <TabPane name="list" label="明细" tab="Tab" v-if="editConfig.editData.maintainStatus !== '0'">
      <CostEstimateIBodyMain ref="constList" v-if="tabs.list&&editConfig.editData.maintainStatus !== '0'"
                             :editConfigH="editConfig" :in-source="cmbSource"></CostEstimateIBodyMain>
    </TabPane>
    <template v-slot:extra>
      <XdoButton type="text" @click="backToList">
        <XdoIcon type="ios-undo" size="22" style="color: green;"/>
        <span>
        {{ editConfig.editData.updateTime }}
      </span>
      </XdoButton>
    </template>
  </XdoTabs>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import CostEstimateIDetail from './CostEstimateIDetail'
  import quoNoMain from '../../components/costEstimate/quo-no-main'
  import quoNoCommEdit from '../../components/costEstimate/quo-no-comm-edit'
  import CostEstimateIBodyMain from '../costEstimateIBody/CostEstimateIBodyMain'

  export default {
    name: 'CostEstimateITab',
    moduleName: '进口费用预估-详细页面',
    components: {
      quoNoMain,
      quoNoCommEdit,
      // AcmpInfoListCustom,
      CostEstimateIDetail,
      CostEstimateIBodyMain
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        tabName: 'head',
        tabs: {
          head: true,
          list: false
        },
        ieMark: 'I',
        cmbSource: {
          courseNameData: []
        },
        editConfigQuo: { // 报价单信息
          headId: '',
          editData: {},
          exists: false,
          editStatus: this.editConfig.editStatus
        }
      }
    },
    created() {
      let me = this
      // 获取 报价单信息  重新构建  relationSid = sid    sid = headId
      me.editConfigQuo.editData = Object.assign({}, me.editConfig.editData)
      me.editConfigQuo.editData.sid = me.editConfig.editData.relationSid
      me.editConfigQuo.headId = me.editConfig.editData.sid
      me.editConfigQuo.editData.headId = me.editConfig.editData.sid

      /**
       * 获取费用科目信息
       */
      me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, {}).then(res => {
        me.$set(me.cmbSource, 'courseNameData', res.data.data.map(item => {
          return {
            label: item.costCourseName,
            value: item.costCourseCode
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'courseNameData', [])
      })
    },
    methods: {
      /**
       * 报价单 信息  保存成功事件
       * @param exists 是否存在数据
       * @param frmData 当前数据
       */
      handleReload(exists, frmData) {
        let me = this
        me.$set(me.editConfigQuo, 'exists', exists)
        me.$set(me.editConfigQuo, 'editData', frmData)
        me.$set(me.editConfig.editData, 'relationSid', frmData.headId)
      },
      /**
       报价单确认 预估费用 返回事件
       * */
      onEstimateSuccess() {
        let me = this
        me.editConfig.editData.maintainStatus = '1'
        me.tabName = 'list'
        me.tabs[me.tabName] = true
        if (me.$refs.constList) {
          me.$refs.constList.getList()
        }
      },
      /**
       * 供编辑界面传回信息调用
       */
      backToList() {
        let me = this
        me.$emit('onEditBack', {
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      tabClick(tableName) {
        let me = this
        me.tabs[tableName] = true
      }
    },
    computed: {
      isShowQuoConfirmDetail: function () {
        return this.tabName === 'head'
      },
      showDisable: function () {
        return this.editConfig.editStatus !== editStatus.EDIT
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
