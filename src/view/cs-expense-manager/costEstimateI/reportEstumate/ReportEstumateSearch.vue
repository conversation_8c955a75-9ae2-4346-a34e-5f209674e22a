<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="提单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="lockStatus" label="锁定状态">
        <xdo-select v-model="searchParam.lockStatus"
                    :options="expenseManager.lockStatus"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="forwardName" label="货运代理">
        <XdoIInput type="text" v-model="searchParam.forwardName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="quoNo" label="报价单编号">
        <XdoIInput type="text" v-model="searchParam.quoNo"></XdoIInput>
      </XdoFormItem>
      <dcDateRange label="制单日期" @onDateRangeChanged="handleDateChange"> </dcDateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { expenseManager } from '@/view/cs-common/constant'
  import dcDateRange from '@/components/dc-date-range/dc-date-range'

  export default {
    name: 'ReportEstumateSearch',
    components: {
      dcDateRange
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',    // 提单内部编号
          lockStatus: '',   // 锁定状态
          forwardName: '',  // 货代名称
          quoNo: '',        // 报价单编号
          insertTimeFrom: '',
          insertTimeTo: ''
        },
        expenseManager: expenseManager
      }
    },
    methods: {
      handleDateChange(values) {
        this.searchParam.insertTimeFrom = values[0]
        this.searchParam.insertTimeTo = values[1]
      }
    }
  }
</script>

<style scoped>
</style>
