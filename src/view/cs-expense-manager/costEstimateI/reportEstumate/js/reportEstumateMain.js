import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 2,
      isShowOp: false,
      isShowSetting: true,
      gridConfig: {
        exportTitle: '进口费用预估明细'
      },
      toolbarEventMap: {
        'export': this.handleDownload   // '导出'
      },
      ajaxUrl: {
        exportUrl: csAPI.expenseManager.costEstimateI.report.export,
        selectAllPaged: csAPI.expenseManager.costEstimateI.report.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
