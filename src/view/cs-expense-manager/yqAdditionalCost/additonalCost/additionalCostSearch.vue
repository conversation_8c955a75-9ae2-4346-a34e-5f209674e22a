<template>
  <section>
    <XdoForm class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="ieType" label="进出口类型">
        <xdo-select v-model="searchParam.ieType" :options="cmbSource.ieType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="countryType" label="国别类型">
        <xdo-select v-model="searchParam.countryType" :options="cmbSource.countryType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="remitteeCode" label="收款单位">
        <xdo-select v-model="searchParam.remitteeCode" :options="cmbSource.remitteeCode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="startPlace" label="发货地">
        <xdo-select v-model="searchParam.startPlace" :options="cmbSource.country"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="endPlace" label="目的地">
        <xdo-select v-model="searchParam.endPlace" :options="cmbSource.country"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>

  export default {
    name: 'additionalCostSearch',
    props: {
      cmbSource: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        searchParam: {
          ieType: '',
          startPlace: '',
          endPlace: '',
          countryType: '',
          remitteeCode: ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
