<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false" title="" class="ieLogisticsTrackingCard">
      <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader"
               label-position="right" :label-width="100">
        <XdoFormItem prop="ieType" label="进出口类型">
          <xdo-select v-model="frmData.ieType" :options="this.cmbSource.ieType" :disabled="showDisable"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="countryType" label="国别类型">
          <xdo-select v-model="frmData.countryType" :options="this.cmbSource.countryType" :disabled="showDisable"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="remitteeCode" label="收款单位">
          <xdo-select v-model="frmData.remitteeCode" :options="this.cmbSource.remitteeCode" :disabled="showDisable"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="startPlace" label="发货地">
          <xdo-select v-model="frmData.startPlace" :options="this.cmbSource.country" :disabled="showDisable"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="endPlace" label="目的地">
          <xdo-select v-model="frmData.endPlace" :options="this.cmbSource.country" :disabled="showDisable"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="decPrice" label="ESS(CNY)/KG">
          <xdo-input type="text" v-model="frmData.decPrice" decimal :disabled="showDisable"
                     int-length="13" precision="5"></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="curr" label="币制">
          <xdo-select v-model="frmData.curr" :asyncOptions="pcodeList" :meta="pcode.curr_outdated"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { expenseManager } from '@/view/cs-common/constant'
  import { editStatus } from '@/view/cs-common'
  import { expenseManageEdit } from '@/view/cs-expense-manage/js/comm/expenseManageEdit'

  export default {
    name: 'additionalCostEdit',
    mixins: [expenseManageEdit],
    props: {
      cmbSource: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        formName: 'dataForm',
        expenseManageData: expenseManager,
        ajaxUrl: {
          insert: csAPI.expenseManager.costYqAdditional.insert,
          update: csAPI.expenseManager.costYqAdditional.update
        },
        rulesHeader: {
          ieType: [{ required: true, message: '不能为空', trigger: 'blur' }],
          countryType: [{ required: true, message: '不能为空', trigger: 'blur' }],
          remitteeCode: [{ required: true, message: '不能为空', trigger: 'blur' }],
          startPlace: [{ required: true, message: '不能为空', trigger: 'blur' }],
          endPlace: [{ required: true, message: '不能为空', trigger: 'blur' }],
          decPrice: [{ type: 'number', required: true, message: '不能为空', trigger: 'blur' }]
        }
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          ieType: '',
          countryType: '',
          remitteeCode: '',
          startPlace: '',
          endPlace: '',
          decPrice: '',
          curr: ''
        }
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
