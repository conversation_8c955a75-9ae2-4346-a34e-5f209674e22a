<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="Tab" @on-click="tabClick">
    <TabPane name="cost" label="疫情附加值" tab="Tab">
      <AdditionalCost v-if="tabs.cost"></AdditionalCost>
    </TabPane>
    <!-- maintainStatus：已生成 -->
    <TabPane name="country" label="国别州" tab="Tab">
      <CountryContinent></CountryContinent>
    </TabPane>
  </XdoTabs>
</template>

<script>
  import AdditionalCost from './additonalCost/additionalCostList'
  import CountryContinent from './countryContinent/countryContinentList'

  export default {
    name: 'yqAdditionalCost',
    moduleName: '疫情附加费',
    components: {
      AdditionalCost,
      CountryContinent
    },
    data() {
      return {
        tabName: 'cost',
        tabs: {
          cost: true,
          country: false
        }
      }
    },
    methods: {
      tabClick(tableName) {
        let me = this
        me.tabs[tableName] = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
