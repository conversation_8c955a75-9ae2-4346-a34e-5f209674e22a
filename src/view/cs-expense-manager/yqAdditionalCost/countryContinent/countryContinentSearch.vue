<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="country" label="国别">
        <xdo-select v-model="searchParam.country" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="continent" label="洲">
        <xdo-select v-model="searchParam.continent" :options="this.expenseManager.continent" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>

  import { expenseManager } from '@/view/cs-common/constant'
  export default {
    name: 'countryContinentSearch',
    data() {
      return {
        searchParam: {
          country: '',
          continent: ''
        },
        expenseManager:expenseManager
      }
    }
  }
</script>

<style scoped>
</style>
