<template>
  <section>
    <div v-show="showList">
      <Card :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <Button type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</Button>
            <Button type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</Button>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <countryContinentSearch ref="headSearch"></countryContinentSearch>
          </div>
        </div>
      </Card>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <Card :bordered="false">
        <Table class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight-50"
               :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></Table>
        <div ref="area_page">
          <Page class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </Card>
    </div>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import countryContinentSearch from './countryContinentSearch'
  import { getColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { columns, columnsConfig } from './countryContinentColumns'
  import { expenseManager } from '@/view/cs-common/constant'

  export default {
    name: 'countryContinentList',
    moduleName: '国别洲',
    mixins: [commList, pms, columns],
    components: {
      countryContinentSearch
    },
    data() {
      return {
        // 查询条件行数
        searchLines: 1,
        p_group: 'country',
        toolbarEventMap: {
          'add': this.rowAdd,
          'edit': this.rowEdit,
          'delete': this.handleDelete
        },
        cmbSource: {
          continent: expenseManager.continent,
          country: []
        },
        ajaxUrl: {
          insert: csAPI.expenseManager.costCountryContinent.insert,
          update: csAPI.expenseManager.costCountryContinent.update,
          delete: csAPI.expenseManager.costCountryContinent.delete,
          selectAllPaged: csAPI.expenseManager.costCountryContinent.selectAllPaged
        }
      }
    },
    mounted: function() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.loadFunctions('country').then()

      me.pcodeList(me.pcode.country_outdated).then(res => {
        me.cmbSource.country = JSON.parse(JSON.stringify(res || {}))
      }).catch(() => {
        me.cmbSource.country = []
      })
    },
    methods: {
      rowAdd() {
        let me = this
        me.addEmptyRow({
          country: '',
          continent: '',
          insertUser: me.$store.state.user.userNo,
          insertTime: (new Date()).toLocaleDateString()
        })
      },
      rowEdit() {
        let me = this
        if (me.checkRowSelected('编辑', true)) {
          let theRow = me.gridConfig.selectRows[0]
          me.gridConfig.selectRows = []
          let index = -1
          let theInd = -1
          for (let row of me.gridConfig.data) {
            theInd++
            if (row.sid === theRow.sid) {
              index = theInd
            }
          }
          if (index > -1) {
            me.setRowEdit(index)
          } else {
            console.error('当前选中的数据存在问题!')
          }
        }
      },
      handleDelete() {
        let me = this
        console.log(1111,me.gridConfig.selectRows)
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
