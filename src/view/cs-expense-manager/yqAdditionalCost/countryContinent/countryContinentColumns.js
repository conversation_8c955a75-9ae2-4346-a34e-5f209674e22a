import { baseColumnsShow, baseColumnsExport, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'country'
  , 'continent'
  , 'insertUser'
  , 'insertTime'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [baseEditColumns],
  data() {
    let me = this,
      baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields, {
          width: 200,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          title: '国别',
          key: 'country',
          render: (h, params) => {
            return this.cmbRender(h, params, me.cmbSource.country)
          }
        },
        {
          width: 220,
          align: 'center',
          title: '洲',
          key: 'continent',
          render: (h, params) => {
            return me.cmbRender(h, params, me.cmbSource.continent)
          }
        },
        {
          width: 120,
          title: '录入人',
          align: 'center',
          key: 'insertUser',
          ellipsis: true,
          tooltip: true
        },
        {
          width: 100,
          title: '录入日期',
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
