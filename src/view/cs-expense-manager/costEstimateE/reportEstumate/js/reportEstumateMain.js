import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 2,
      isShowOp: false,
      isShowSetting: true,
      gridConfig: {
        exportTitle: '出口费用预估明细'
      },
      toolbarEventMap: {
        'export': this.handleDownload   // '导出'
      },
      ajaxUrl: {
        exportUrl: csAPI.expenseManager.costEstimateE.report.export,
        selectAllPaged: csAPI.expenseManager.costEstimateE.report.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
