import { csAPI } from '@/api'
import { expenseManager, importExportManage } from '@/view/cs-common/constant'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {title: '提单内部编号', width: 150, key: 'emsListNo'},
      {
        title: '维护状态', width: 150, key: 'maintainStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, expenseManager.maintainStatus)
        }
      },
      {
        title: '锁定状态', width: 150, key: 'lockStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, expenseManager.lockStatus)
        }
      },
      {title: '报价单编号', width: 150, key: 'quoNo'},
      {title: '货运代理', width: 150, key: 'forwardName'},  // 货代名称
      {title: '申报单位名称', width: 150, key: 'declareNameCustoms'},
      {
        title: '运输方式', width: 150, key: 'trafMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'TRANSF')
        }
      },
      {
        title: '贸易条款', width: 150, key: 'tradeTerms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, importExportManage.tradeTermList)
        }
      },
      {
        title: '指运港', width: 150, key: 'destPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'PORT_LIN')
        }
      },
      {
        title: '运抵国', width: 150, key: 'tradeCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }
      },
      {title: '境外收货人名称', width: 150, key: 'overseasShipperName'},
      {
        title: '出境关别', width: 150, key: 'ieport',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CUSTOMS_REL')
        }
      },
      {
        title: '包装种类', width: 150, key: 'wrapType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'WRAP')
        }
      },
      {title: '件数', width: 150, key: 'packNum'},
      {title: '总净重', width: 150, key: 'netWt'},
      {title: '总毛重', width: 150, key: 'grossWt'},
      {title: '总数量', width: 150, key: 'sumQty'},       // 数量汇总
      {title: '总金额', width: 150, key: 'sumDecTotal'},  // 金额汇总
      {title: '提运单号', width: 150, key: 'hawb'},       // 分提运单
      {title: '发票号', width: 150, key: 'invoiceNo'},
      {title: '备注', width: 150, key: 'note'},
      {title: '计费重量', width: 150, key: 'weight'},
      {
        title: '车柜类型', width: 150, key: 'cabinetType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cabinetTypeData)
        }
      },
      {title: '车柜数量', width: 150, key: 'cabinetAmount'},
      {
        title: '费用科目名称', width: 150, key: 'courseCode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.courseNameData)
        }
      },
      {title: '预估金额', width: 150, key: 'decTotal'},
      {
        title: '币制', width: 150, key: 'curr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }
      },
      {title: '人民币金额', width: 150, key: 'totalRmb'},
      {title: '确认金额（RMB）', width: 150, key: 'confTotal'},
      {title: '费用明细备注', width: 150, key: 'costNote'}
    ]
    return {
      courseNameData: [],
      cabinetTypeData: [],
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  },
  /**
   * 创建
   */
  created: function () {
    let me = this
    /**
     * 获取 费用科目名称 data
     */
    me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, {
      validStatus: '1'
    }).then(res => {
      me.courseNameData = res.data.data.map(item => {
        return {
          value: item.costCourseCode,
          label: item.costCourseName
        }
      })
    }).catch(() => {
      me.courseNameData = []
    })
    /**
     * 获取自定义参数
     */
    me.$http.post(csAPI.enterpriseParamsLib.carCabinetType.selectAllPaged, {}).then(res => {
      // 车辆类型
      me.cabinetTypeData = res.data.data.map(item => {
        return {
          value: item.cabinetType,
          label: item.cabinetName,
          cabinetType: item.cabinetAttribute
        }
      })
    }).catch(() => {
      me.cabinetTypeData = []
    })
  }
}
