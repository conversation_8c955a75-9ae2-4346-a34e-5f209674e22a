<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="费用价格明细">
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="courseType" label="费用科目大类">
            <xdo-select v-model="frmData.courseType" :options="this.expenseManageData.courseType"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="courseCode" label="科目名称">
            <xdo-select v-model="frmData.courseCode" :options="this.courseNameData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="decTotal" label="预估金额">
            <xdo-input type="text" v-model="frmData.decTotal" decimal notConvertNumber
                       int-length="13" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>

          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="frmData.curr" meta="CURR_OUTDATED"
                        :asyncOptions="pcodeList" :disabled="showDisable" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="exchangeRate" label="汇率">
            <xdo-input type="text" v-model="frmData.exchangeRate" decimal notConvertNumber
                       int-length="13" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>

          <XdoFormItem prop="totalRmb" label="人民币金额">
            <xdo-input type="text" v-model="frmData.totalRmb" decimal notConvertNumber
                       int-length="13" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>

          <XdoFormItem prop="confTotal" label="确认金额(RMB)">
            <xdo-input type="text" v-model="frmData.confTotal" decimal notConvertNumber
                       int-length="13" precision="5" :disabled="showDisableNew"></xdo-input>
          </XdoFormItem>

          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note"
                       :disabled="showDisableNew" :maxlength="255"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { detailJS } from './js/costEstimateEBodyDetail'
  import { editStatus, expenseManager } from '@/view/cs-common/constant'

  export default {
    name: 'CostEstimateEBodyDetail',
    mixins: [detailJS],
    data() {
      return {
        courseNameData: [],
        formName: 'dataForm',
        courseNameDataAll: [],
        expenseManageData: expenseManager,
        ajaxUrl: {
          insert: csAPI.expenseManager.costEstimateE.list.insert,
          update: csAPI.expenseManager.costEstimateE.list.update
        },
        rulesHeader: {
          courseType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          courseCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          confTotal: [{required: true, pattern: /^\d{1,15}(\.\d{1,5})?$/, message: '不能为空！', trigger: 'blur'}] // 不带负数
        }
      }
    },
    created() {
      let me = this
      me.$http.post(csAPI.expenseManage.expenseAccountSettingNew.selectAllPaged, {
        validStatus: '1'
      }).then(res => {
        me.courseNameDataAll = res.data.data
        me.handleGetCourseNameData(me.frmData.courseType)
      }).catch(() => {
      }).finally(() => {
      })
    },
    methods: {
      /**
       * 通过 科目种类 获取 科目名称数据源
       * @param val
       */
      handleGetCourseNameData(val) {
        if (val) {
          let me = this,
            courseCode = me.frmData.courseCode
          me.courseNameData = me.courseNameDataAll.filter(x => x.courseType === val).map(item => {
            return {
              value: item.costCourseCode,
              label: item.costCourseName
            }
          })
          me.$set(me.frmData, 'courseCode', ' ')
          me.$nextTick(() => {
            me.$set(me.frmData, 'courseCode', courseCode)
          })
        }
      }
    },
    watch: {
      /**
       *  courseType:[ // 费用科目大类
       { value: '1', label: '国外段' },
       { value: '2', label: '国际段' },
       { value: '3', label: '国内段' },
       { value: '4', label: '其他' }],
       */
      'frmData.courseType': {
        handler: function (val) {
          this.handleGetCourseNameData(val)
        }
      }
    },
    computed: {
      /**
       * 输入组件是否可输
       * @returns {boolean}
       */
      showDisable() {
        return !(this.editConfig.editStatus === editStatus.ADD)
      },
      showDisableNew() {
        return !(this.editConfig.editStatus === editStatus.ADD || this.editConfig.editStatus === editStatus.EDIT)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>












