
import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  props: {
    editConfigH: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      buttons: [
        {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
        {...btnComm, label: '保存返回', icon: 'dc-btn-save', click: this.handleSaveClose},
        {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      let me = this
      me.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        maintainStatus: '',     // 维护状态
        courseType: '',         // 费用科目大类
        courseCode: '',         // 费用科目名称
        decTotal: null,         // 预估金额
        curr: '',               // 币制
        exchangeRate: null,     // 汇率
        totalRmb: null,         // 金额
        confTotal: null,        // 确认金额
        note: '',               // 备注
        erpHeadId: this.editConfigH.editData.relationSid,  // 对应供应商比价sid
        quoSid: '',             // 报价单表头sid
        insertTime: null        // 创建时间
      }
    }
  }
}
