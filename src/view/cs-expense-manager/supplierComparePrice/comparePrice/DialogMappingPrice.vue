<template>
  <XdoModal v-model="show" mask width="500" title="匹配报价单"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <xdo-ag-grid ref="ref_agGrid" :height="350" rowSelection="multiple" :checkboxSelection="checkboxSelection"
                 :columns="gridConfig.gridColumns" :data="gridConfig.data"
                 @selectionChanged="handleSelectionChanged">
    </xdo-ag-grid>
    <div class="dc-merge-1-3" style="text-align: right;">
      <XdoButton type="success" icon="ios-cloud-upload" :loading="btnLoading" @click="handleConfirm">
        确认提交计算
      </XdoButton>
      <XdoButton type="error" icon="ios-close" style="margin-left: 5px;" @click="handleClose">关闭</XdoButton>
    </div>
  </XdoModal>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import { mainJS } from './js/dialogMappingPriceMain'
  import { columns } from './js/dialogMappingPriceColumns'

  export default {
    name: 'DialogMappingPrice', // 匹配报价单
    mixins: [columns, mainJS],
    props: {
      show: {
        type: Boolean,
        required: true
      },
      /**
       * 列表页面选择的 选择的行的 信息
       */
      editConfigH: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    methods: {
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>
