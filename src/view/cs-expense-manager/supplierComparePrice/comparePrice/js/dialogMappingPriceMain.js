import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      isShowOp: false,
      btnLoading: false,
      p_group: 'p_group',
      gridConfig: {
        exportTitle: '匹配报价单弹出页面'
      },
      ajaxUrl: {
        calPrice: csAPI.expenseManager.supplierComparePrice.calPrice,      // 确认提交计算
        selectAllPaged: csAPI.expenseManager.supplierComparePrice.getMatchList
      }
    }
  },
  methods: {
    /**
     * 确认提交计算
     */
    handleConfirm() {
      let me = this
      if (!me.gridConfig.selectRows.length) {
        me.$Message.warning('请选择数据')
        return
      }
      me.btnLoading = true
      me.$http.post(`${me.ajaxUrl.calPrice}/${me.editConfigH.editData.sid}`, me.gridConfig.selectRows).then(() => {
        me.$emit('update:show', false)
        me.$emit('onConfirm', true)
        me.$Message.success('计算成功!')
      }).catch(() => {
      }).finally(() => {
        me.btnLoading = false
      })
    },
    /**
     * 执行查询  重写
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.tableloading = true
      me.$nextTick(() => {
        // 被改变
        me.$http.get(`${searchUrl}/${me.editConfigH.editData.sid}`).then(res => {
          me.tableloading = false
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
        })
      })
    }
  }
}
