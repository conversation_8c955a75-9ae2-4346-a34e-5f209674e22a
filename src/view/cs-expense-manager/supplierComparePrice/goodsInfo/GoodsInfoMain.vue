<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <GoodsInfoSearch ref="headSearch"></GoodsInfoSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <SupplierComparePriceTabs v-if="!showList" :editConfig="editConfig"
                              @onEditBack="editBack"></SupplierComparePriceTabs>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
    <DialogConfirmSupplier :show.sync="isShowDialogConfirmSupplier" :headId="this.gridConfig.selectRows.length === 1? this.gridConfig.selectRows[0].sid : ''"
                           @onConfirm="handleConQuotationConfirm"></DialogConfirmSupplier>
  </section>
</template>

<script>
  import { mainJS } from './js/goodsInfoMain'
  import { columns } from './js/goodsInfoColumns'
  import GoodsInfoSearch from './GoodsInfoSearch'
  import DialogConfirmSupplier from './DialogConfirmSupplier'
  import SupplierComparePriceTabs from '../SupplierComparePriceTabs'

  export default {
    name: 'GoodsInfoMain',
    moduleName: '货物信息',
    components: {
      GoodsInfoSearch,
      DialogConfirmSupplier,
      SupplierComparePriceTabs
    },
    mixins: [columns, mainJS]
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
