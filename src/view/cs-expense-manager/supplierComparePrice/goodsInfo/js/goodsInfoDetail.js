import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      buttons: [
        {...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'},
        {...btnComm, click: this.handleSaveClose, icon: 'dc-btn-save', label: '保存返回'},
        {...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'}
      ],
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        goodsNo: '',              // 货物单号
        ieType: '',               // 进出口类型
        trafMode: '',             // 运输方式
        tradeTerms: '',           // 贸易条款
        despPort: '',             // 启运港
        destPort: '',             // 目的港
        tradeCountry: '',         // 启运国
        destinationCountry: '',   // 目的国
        goodsType: '',            // 物品种类
        serviceType: '',          // 服务类型
        weight: null,             // 计费重量
        cabinetType: '',          // 车柜型
        cabinetAmount: null,      // 车柜数
        note: '',                 // 备注
        confirmNo: '',            // 确认报价单编号
        insertTime: null,         // 创建时间
        goodsName: '',            // 货物名称
        codeTS: '',               // 商品编码
        // status :'',               // 状态
      }
    }
  }
}
