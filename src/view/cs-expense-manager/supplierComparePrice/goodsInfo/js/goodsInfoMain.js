import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus } from '@/view/cs-common'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 4,
      gridConfig: {
        exportTitle: '货物信息'
      },
      isShowDialogConfirmSupplier: false,
      toolbarEventMap: {
        'add': this.handleAdd,                   // '新增'
        'edit': this.handleEdit,                 // '编辑'
        'delete': this.handleDelete,             // '删除'
        'export': this.handleDownload,           // '导出'
        'conQuotation': this.handleConQuotation  // 确认供应商
      },
      ajaxUrl: {
        delete: csAPI.expenseManager.supplierComparePrice.delete,
        exportUrl: csAPI.expenseManager.supplierComparePrice.exportUrl,
        conQuotation: csAPI.expenseManager.supplierComparePrice.conQuotation,              // 确认供应商
        selectAllPaged: csAPI.expenseManager.supplierComparePrice.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 列表中点击数据编辑 重写
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (row.status === '1') {
        me.$Message.warning('不允许编辑，状态为已确认')
        return
      }
      if (me.customCheck([row], '编辑')) {
        me.editConfig.editStatus = editStatus.EDIT
        me.editConfig.editData = row
        me.showList = false
      }
    },
    /**
     * 确认供应商
     */
    handleConQuotation() {
      let me = this
      if (!me.checkRowSelected('确认供应商', true)) {
        return
      }
      me.isShowDialogConfirmSupplier = true
    },
    /**
     * 确认供应商 确认事件
     */
    handleConQuotationConfirm() {
      this.handleSearchSubmit()
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      if (me.gridConfig.selectRows.filter(x => x.status === '1').length > 0) {
        me.$Message.warning('删除失败: 存在状态为已确认的数据!')
        return
      }
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
