<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="货物信息">
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="goodsNo" label="货物单号">
            <XdoIInput type="text" v-model="frmData.goodsNo"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="ieType" label="进出口类型">
            <xdo-select v-model="frmData.ieType"
                        :options="this.expenseManager.ieType"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="goodsName" label="货物品名">
            <XdoIInput type="text" v-model="frmData.goodsName"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="codeTS" label="商品编码">
            <XdoIInput type="text" v-model="frmData.codeTS"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="10"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="trafMode" label="运输方式">
            <xdo-select v-model="frmData.trafMode" meta="TRANSF_OUTDATED"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeTerms" label="贸易条款">
            <xdo-select v-model="frmData.tradeTerms"
                        :options="this.importExportManage.tradeTermList"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="despPort" label="启运港">
            <xdo-select v-model="frmData.despPort" meta="PORT_LIN"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="destPort" label="目的港">
            <xdo-select v-model="frmData.destPort" meta="PORT_LIN"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeCountry" label="启运国">
            <xdo-select v-model="frmData.tradeCountry" meta="COUNTRY_OUTDATED"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="destinationCountry" label="目的国">
            <xdo-select v-model="frmData.destinationCountry" meta="COUNTRY_OUTDATED"
                        :disabled="showDisable" :clearable="!showDisable"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="goodsType" label="物品种类">
            <xdo-select v-model="frmData.goodsType"
                        :options="this.expenseManager.goodsType"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="serviceType" label="服务类型">
            <xdo-select v-model="frmData.serviceType"
                        :options="this.expenseManager.serviceType"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="weight" label="计费重量">
            <xdo-input type="text" v-model="frmData.weight" decimal
                       int-length="11" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="cabinetType" label="车柜型">
            <xdo-select v-model="frmData.cabinetType" :options="this.cabinetTypeData"
                        :disabled="showDisable" :clearable="!showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="cabinetAmount" label="车柜数量">
            <xdo-input type="text" v-model="frmData.cabinetAmount" decimal notConvertNumber
                       int-length="13" precision="0" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="500"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { detailJS } from './js/goodsInfoDetail'
  import { expenseManager, importExportManage } from '@/view/cs-common/constant'

  export default {
    name: 'GoodsInfoDetail',
    mixins: [detailJS],
    data() {
      return {
        cabinetTypeData: [],
        formName: 'dataForm',
        expenseManager: expenseManager,
        importExportManage: importExportManage,
        ajaxUrl: {
          insert: csAPI.expenseManager.supplierComparePrice.insert,
          update: csAPI.expenseManager.supplierComparePrice.update,
        },
        rulesHeader: {
          ieType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          goodsNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    created() {
      let me = this
      /**
       * 获取自定义参数
       */
      me.$http.post(csAPI.enterpriseParamsLib.carCabinetType.selectAllPaged, {}).then(res => {
        // 车辆类型
        me.cabinetTypeData = res.data.data.map(item => {
          return {
            value: item.cabinetType,
            label: item.cabinetName,
            cabinetType: item.cabinetAttribute
          }
        })
      }).catch(() => {
        me.cabinetTypeData = []
      })
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
