<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="goodsNo" label="货物单号">
        <XdoIInput type="text" v-model="searchParam.goodsNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="状态">
        <xdo-select v-model="searchParam.status"
                    :options="this.expenseManager.statusGoods"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="进出口类型">
        <xdo-select v-model="searchParam.ieType"
                    :options="this.expenseManager.ieType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" meta="TRANSF_OUTDATED"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeTerms" label="贸易条款">
        <xdo-select v-model="searchParam.tradeTerms"
                    :options="this.importExportManage.tradeTermList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="despPort" label="启运港">
        <xdo-select v-model="searchParam.despPort" meta="PORT_LIN"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="destPort" label="目的港">
        <xdo-select v-model="searchParam.destPort" meta="PORT_LIN"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeCountry" label="启运国">
        <xdo-select v-model="searchParam.tradeCountry" meta="COUNTRY_OUTDATED"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="destinationCountry" label="目的国">
        <xdo-select v-model="searchParam.destinationCountry" meta="COUNTRY_OUTDATED"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="goodsType" label="物品种类">
        <xdo-select v-model="searchParam.goodsType"
                    :options="this.expenseManager.goodsType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="serviceType" label="服务类型">
        <xdo-select v-model="searchParam.serviceType"
                    :options="this.expenseManager.serviceType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="insertTime" label="制单日期">
        <XdoDatePicker type="date" format="yyyy-MM-dd" v-model="searchParam.insertTime" transfer></XdoDatePicker>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { expenseManager, importExportManage } from '@/view/cs-common/constant'

  export default {
    name: 'GoodsInfoSearch',
    data() {
      return {
        searchParam: {
          goodsNo: '',              // 货物单号
          ieType: '',               // 进出口类型
          trafMode: '',             // 运输方式
          tradeTerms: '',           // 贸易条款
          despPort: '',             // 启运港
          destPort: '',             // 目的港
          tradeCountry: '',         // 启运国
          destinationCountry: '',   // 目的国
          goodsType: '',            // 物品种类
          serviceType: '',          // 服务类型
          status: '',               // 状态
          insertTime: null          // 创建时间
        },
        expenseManager: expenseManager,
        importExportManage: importExportManage
      }
    }
  }
</script>

<style scoped>
</style>
