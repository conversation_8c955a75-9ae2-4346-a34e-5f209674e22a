<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <div class="custom-bread-crumb">
          <div class="ivu-breadcrumb" style="font-size: 12px;">
          </div>
          <div class="custom-search-buttons" style="padding: 0">
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            <XdoButton type="text" @click="handleBack">
              <XdoIcon type="ios-undo" size="22" style="color: green;" />
            </XdoButton>
          </div>
        </div>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" height="300" checkboxSelection
                   :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                   :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                   @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div style="text-align: right; padding: 0 8px 0 0;">
        <div> 共 {{ listConfig.data.length }} 条 </div>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { quoNoChoose } from './js/quoNoChoose'

  export default {
    name: 'quoNoChoose',
    mixins: [quoNoChoose],
    created: function () {
      let me = this
      /**
       * 收款单位名称(货代、报关行)
       */
      me.$http.post(csAPI.ieParams.selectComboxByCode + '/FOD,CUT').then(res => {
        me.$set(me.cmbSource, 'exchequer', res.data.data.map(item => {
          return {
            label: item['LABEL'],
            value: item['VALUE'],
            type: item['TYPE']
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'exchequer', [])
      })
    },
    methods: {
      unSelectedSearch() {
        let me = this
        if (me.ieMark === 'I') {
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.expenseManager.quoNoAbout.UnSelectQuoNoI + '/3721')
        } else {
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.expenseManager.quoNoAbout.UnSelectQuoNoE + '/3721')
        }
        me.handleSearchSubmit()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
