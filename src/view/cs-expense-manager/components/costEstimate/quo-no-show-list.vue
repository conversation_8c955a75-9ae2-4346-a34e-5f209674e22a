<template>
  <section>
    <div class="action" v-if="toolBarShow">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" :checkboxSelection="grdCheckboxSelection" height="204"
                   :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                   :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                   @selectionChanged="handleSelectionChange"></xdo-ag-grid>
      <div style="text-align: right; padding: 0 8px 0 0;">
        <div> 共 {{ queCount }} 条 </div>
      </div>
    </XdoCard>
    <quoNoSelectPop :show.sync="quoNoSelectPop.show" :ie-mark="ieMark" :head-id="parentConfig.headId"
                    @doQuoNoChoose="doQuoNoChoose"></quoNoSelectPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import quoNoSelectPop from './quo-no-select-pop'
  import { editStatus, importExportManage } from '@/view/cs-common'
  import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
  import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

  export default {
    name: 'quoNoShowList',
    props: {
      hasCommData: {
        type: Boolean,
        default: false
      },
      parentConfig: {
        headId: '',
        editData: {
          sid: ''
        },
        editStatus: editStatus.SHOW
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    components: {
      quoNoSelectPop
    },
    mixins: [columnRender, listDataProcessing],
    data() {
      return {
        quoNoSelectPop: {
          show: false
        },
        pageParam: {
          limit: 1000
        },
        actionsComm: {
          needed: true,
          loading: false,
          disabled: false
        },
        pmsLevel: 'quoNo',
        grdCheckboxSelection: false,
        listConfig: {
          operationColumnShow: false
        },
        toolbarEventMap: {
          'add': this.handleAdd,
          'delete': this.handleDelete,
          'estimate': this.handleEstimate
        },
        ajaxUrl: {
          costCal: '',
          deleteUrl: csAPI.expenseManager.quoNoAbout.delete,
          selectAllPaged: csAPI.expenseManager.quoNoAbout.selectQuoNo,
          confirmSelect: csAPI.expenseManager.quoNoAbout.confirmSelect
        }
      }
    },
    created: function () {
      let me = this
      if (me.ieMark === 'I') {
        me.$set(me.ajaxUrl, 'costCal', csAPI.expenseManager.costEstimateI.head.costCal)
      } else {
        me.$set(me.ajaxUrl, 'costCal', csAPI.expenseManager.costEstimateE.head.costCal)
      }
      me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.expenseManager.quoNoAbout.selectQuoNo + '/' + me.parentConfig.headId)
    },
    computed: {
      queCount() {
        let me = this
        return me.listConfig.data.length
      },
      toolBarShow() {
        let me = this
        if (!me.hasCommData) {
          return false
        }
        return (me.parentConfig.editStatus === editStatus.EDIT)
      }
    },
    watch: {
      toolBarShow: {
        immediate: true,
        handler: function (show) {
          let me = this
          me.$set(me, 'grdShow', false)
          me.$set(me, 'grdCheckboxSelection', show)
          me.$nextTick(() => {
            me.$set(me, 'grdShow', true)
          })
        }
      }
    },
    methods: {
      actionLoaded() {
        let me = this
        if (me.parentConfig.editStatus !== editStatus.ADD
          || me.parentConfig.editStatus !== editStatus.EDIT) {
          me.actions = [{
            ...me.actionsComm,
            label: '新增',
            command: 'add',
            icon: 'ios-add',
            key: 'xdo-btn-add'
          }, {
            ...me.actionsComm,
            label: '删除',
            command: 'delete',
            key: 'xdo-btn-delete',
            icon: 'ios-trash-outline'
          }, {
            ...me.actionsComm,
            label: '预估费用',
            command: 'estimate',
            key: 'xdo-btn-delete',
            icon: 'ios-calculator-outline'
          }]
        } else {
          me.actions = []
        }
      },
      /**
       * 设置可配置列字段
       */
      setConfigColumns() {
        let me = this
        me.$set(me.listConfig, 'settingColumns', me.getFields())
      },
      /**
       * 显示列表
       * @returns {({width: number, title: string, key: string}|{flex: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
       */
      getFields() {
        let me = this
        return [{
          width: 120,
          key: 'quoNo',
          title: '报价单编号'
        }, {
          flex: 1,
          key: 'quoName',
          title: '报价单名称'
        }, {
          width: 120,
          title: '收款单位名称',
          key: 'exchequerName',
          cellRendererFramework: me.baseCellRenderer(null, true)
        }, {
          width: 130,
          title: '贸易条款',
          key: 'tradeTerms',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, importExportManage.tradeTermList)
          }, true)
        }, {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.transf)
          })
        }, {
          width: 120,
          title: '启运港',
          key: 'despPort',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.port_lin)
          })
        }, {
          width: 120,
          title: '指运港',
          key: 'destPort',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.port_lin)
          })
        }, {
          width: 120,
          title: '备注',
          key: 'quoNote',
          cellRendererFramework: me.baseCellRenderer(null, true)
        }]
      },
      /**
       * 选中
       * @param type
       * @param data
       */
      doQuoNoChoose(type, data) {
        let me = this
        me.$http.post(me.ajaxUrl.confirmSelect, data).then(() => {
          me.handleSearchSubmit()
          me.$Message.success('选择成功!')
        }).catch(() => {
        }).finally(() => {
          me.$set(me.quoNoSelectPop, 'show', false)
        })
      },
      /**
       * 添加选择
       */
      handleAdd() {
        let me = this
        me.$set(me.quoNoSelectPop, 'show', true)
      },
      afterSearch() {
        let me = this
        me.$emit('onAfterSearch')
      },
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.deleteUrl, 'delete', function () {
          me.handleSearchSubmit()
        })
      },
      /**
       * 费用预估
       */
      handleEstimate() {
        let me = this
        if (!me.hasCommData) {
          me.$Message.success('请先录入公共信息!')
          return
        }
        if (me.listConfig.data.length < 1) {
          me.$Message.success('请先添加报价单信息!')
          return
        }
        me.setToolbarLoading('estimate', true)
        me.$http.post(me.ajaxUrl.costCal + '/' + me.parentConfig.headId).then(() => {
          me.$Message.success('预估完成!')
          me.$emit('onEstimateSuccess', true)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('estimate')
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ag-header-cell-label {
    justify-content: center;
  }
</style>
