<template>
  <section>
    <div class="demo-split">
      <Split v-model="split1">
        <div slot="left" class="demo-split-pane">
          <quoNoShowList :parent-config="editConfig" :ie-mark="ieMark" :has-comm-data="hasCommData"
                         @onAfterSearch="onAfterSearch" @onEstimateSuccess="onEstimateSuccess"></quoNoShowList>
        </div>
        <div slot="right" class="demo-split-pane">
          <isFixedEdit></isFixedEdit>
        </div>
      </Split>
    </div>
  </section>
</template>

<script>
  import isFixedEdit from './is-fixed-edit'
  import { editStatus } from '@/view/cs-common'
  import quoNoShowList from './quo-no-show-list'

  export default {
    name: 'quoNoMain',
    components: {
      isFixedEdit,
      quoNoShowList
    },
    props: {
      /**
       * 传入的编辑信息
       */
      editConfig: {
        type: Object,
        default: () => ({
          headId: '',
          editData: {
            sid: ''
          },
          exists: false,
          editStatus: editStatus.SHOW
        })
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        split1: 0.99,
        hasCommData: false
      }
    },
    watch: {
      'editConfig.exists': {
        immediate: true,
        handler: function (exists) {
          let me = this
          me.$set(me, 'hasCommData', exists)
        }
      }
    },
    methods: {
      onAfterSearch() {
        let me = this
        me.$emit('onAfterSearch')
      },
      onEstimateSuccess() {
        let me = this
        me.$emit('onEstimateSuccess')
      }
    }
  }
</script>

<style lang="less" scoped>
  .demo-split {
    height: 260px;
    border: 1px solid #dcdee2;
  }

  .demo-split-pane {
    margin: 0;
    padding: 0;
  }
</style>
