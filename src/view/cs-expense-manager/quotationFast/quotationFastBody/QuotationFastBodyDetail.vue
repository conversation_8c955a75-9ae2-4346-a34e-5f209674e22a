<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="报价单详细信息">
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="upperWeight" label="重量上限">
            <xdo-input type="text" v-model="frmData.upperWeight" decimal notConvertNumber
                       int-length="11" precision="5" :disabled="showDisable || editConfig.editStatus === editStatus.EDIT"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="priceType" label="价格类型">
            <xdo-select v-model="frmData.priceType" :disabled="showDisable" :clearable="!showDisable"
                        :options="this.expenseManager.priceType" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="onePrice" label="一区价格">
            <xdo-input type="text" v-model="frmData.onePrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="twoPrice" label="二区价格">
            <xdo-input type="text" v-model="frmData.twoPrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="threePrice" label="三区价格">
            <xdo-input type="text" v-model="frmData.threePrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="fourPrice" label="四区价格">
            <xdo-input type="text" v-model="frmData.fourPrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="fivePrice" label="五区价格">
            <xdo-input type="text" v-model="frmData.fivePrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="sixPrice" label="六区价格">
            <xdo-input type="text" v-model="frmData.sixPrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="sevenPrice" label="七区价格">
            <xdo-input type="text" v-model="frmData.sevenPrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="eightPrice" label="八区价格">
            <xdo-input type="text" v-model="frmData.eightPrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="ninePrice" label="九区价格">
            <xdo-input type="text" v-model="frmData.ninePrice" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="au5Price" label="AU-5区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.au5Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="ca6Price" label="CA-6区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.ca6Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="eu17Price" label="EU1-7区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.eu17Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="eu27Price" label="EU2-7区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.eu27Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="hk1Price" label="HK-1区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.hk1Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="jp3Price" label="JP-3区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.jp3Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="kr2Price" label="KR-2区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.kr2Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="mx6Price" label="MX-6区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.mx6Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="my4Price" label="MY-4区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.my4Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="ph4Price" label="PH-4区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.ph4Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="sg4Price" label="SG-4区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.sg4Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="th4Price" label="TH-4区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.th4Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="tw2Price" label="TW-2区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.tw2Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="us6Price" label="US-6区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.us6Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="vn4Price" label="VN-4区价格" v-if="isUpsShow">
            <xdo-input type="text" v-model="frmData.vn4Price" decimal notConvertNumber
                       int-length="12" precision="5" :disabled="showDisable" :clearable="!showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="frmData.note"
                       :disabled="showDisable" :clearable="!showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { detailJS } from './js/quotationFastBodyDetail'
  import { expenseManager, editStatus } from '@/view/cs-common/constant'

  export default {
    name: 'QuotationFastBodyDetail',
    mixins: [detailJS],
    data() {
      return {
        formName: 'dataForm',
        editStatus: editStatus,
        expenseManager: expenseManager,
        ajaxUrl: {
          insert: csAPI.expenseManager.quotationFastBody.insert,
          update: csAPI.expenseManager.quotationFastBody.update
        },
        isUpsShow:false,
        rulesHeader: {
          priceType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          upperWeight: [{required: true, pattern: /^\d{1,11}(\.\d{1,5})?$/, message: '不能为空！', trigger: 'blur'}]
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>












