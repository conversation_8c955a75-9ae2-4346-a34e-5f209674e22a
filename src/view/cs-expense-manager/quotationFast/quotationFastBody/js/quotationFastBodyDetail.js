import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  props: {
    editConfigH: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      disabled: false,
      type: 'primary'
    }
    return {
      buttons: [
        { ...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave },
        { ...btnComm, label: '保存返回', icon: 'dc-btn-save', click: this.handleSaveClose },
        { ...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack }
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function(val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    },
    editConfigH: {
      deep: true,
      immediate: true,
      handler: function() {
        let me = this
        if(me.editConfigH.editData.countryType && me.editConfigH.editData.countryType === '2'){
          me.isUpsShow = true
        }else{
          me.isUpsShow = false
        }
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      let me = this
      me.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        upperWeight: null,                        // 重量上限
        priceType: '',                            // 价格类型
        chargeType: '999',                          // 计费类型  // 1 票数；2 计费重量 ；3 车柜型 4 快件(代表快件的)
        headId: this.editConfigH.editData.sid,    // 表头主键
        onePrice: null,                           // 一区价格
        twoPrice: null,                           // 二区价格
        threePrice: null,                         // 三区价格
        fourPrice: null,                          // 四区价格
        fivePrice: null,                          // 五区价格
        sixPrice: null,                           // 六区价格
        sevenPrice: null,                         // 七区价格
        eightPrice: null,                         // 八区价格
        ninePrice: null,                          // 九区价格
        au5Price:null,
        ca6Price:null,
        eu17Price:null,
        eu27Price:null,
        hk1Price:null,
        jp3Price:null,
        kr2Price:null,
        mx6Price:null,
        my4Price:null,
        ph4Price:null,
        sg4Price:null,
        th4Price:null,
        tw2Price:null,
        us6Price:null,
        vn4Price:null,
        note: ''                                  // 备注
      }
    }
  }
}
