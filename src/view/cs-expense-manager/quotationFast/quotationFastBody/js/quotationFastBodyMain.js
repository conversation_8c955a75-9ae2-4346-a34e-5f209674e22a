import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus, expenseManager } from '@/view/cs-common/constant'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  /**
   * 表头选择的数据
   */
  props: {
    editConfigH: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let commImportConfig = this.getCommImportConfig('QUOTATION_LIST', {
      headId: this.editConfigH.editData.sid
    })
    return {
      importKey: 'QUOTATION_LIST',
      isLoadActoinsBody: this.editConfigH.editStatus !== editStatus.SHOW, // true 加载   false 不加载
      p_group: 'body',
      ajaxUrl: {
        delete: csAPI.expenseManager.quotationFastBody.delete,
        exportUrl: csAPI.expenseManager.quotationFastBody.exportUrl,
        selectAllPaged: csAPI.expenseManager.quotationFastBody.selectAllPaged
      },
      gridConfig: {
        exportTitle: '报价单表体'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload,   // '导出'
        'import': this.handleImport      // '导入'
      },
      // 是否显示导入页面
      modelImportShow: false,
      importConfig: commImportConfig
    }
  },
  watch: {
    editConfigH: {
      deep: true,
      immediate: true,
      handler: function() {
        let me = this
        if(me.editConfigH.editData.countryType && me.editConfigH.editData.countryType === '2'){
          me.importConfig = this.getCommImportConfig('QUOTATION_LIST_UPS', {
            headId: this.editConfigH.editData.sid
          })
          me.totalColumns = [{
            title: '重量区间（KG）', width: 150, key: 'weighSection',
            render: (h, params) => {
              console.log(params)
              return h('span', params.row.lowWeight + '-' + params.row.upperWeight)
            }
          },//不维护
            {
              title: '价格类型', width: 150, key: 'priceType',
              render: (h, params) => {
                return this.cmbShowRender(h, params, expenseManager.priceType)
              }
            },
            {title: '一区价格', width: 150, key: 'onePrice'},
            {title: '二区价格', width: 150, key: 'twoPrice'},
            {title: '三区价格', width: 150, key: 'threePrice'},
            {title: '四区价格', width: 150, key: 'fourPrice'},
            {title: '五区价格', width: 150, key: 'fivePrice'},
            {title: '六区价格', width: 150, key: 'sixPrice'},
            {title: '七区价格', width: 150, key: 'sevenPrice'},
            {title: '八区价格', width: 150, key: 'eightPrice'},
            {title: '九区价格', width: 150, key: 'ninePrice'},
            {title: 'AU-5区价格', width: 150, key: 'au5Price'},
            {title: 'CA-6区价格', width: 150, key: 'ca6Price'},
            {title: 'EU1-7区价格', width: 150, key: 'eu17Price'},
            {title: 'EU2-7区价格', width: 150, key: 'eu27Price'},
            {title: 'HK-1区价格', width: 150, key: 'hk1Price'},
            {title: 'JP-3区价格', width: 150, key: 'jp3Price'},
            {title: 'KR-2区价格', width: 150, key: 'kr2Price'},
            {title: 'MX-6区价格', width: 150, key: 'mx6Price'},
            {title: 'MY-4区价格', width: 150, key: 'my4Price'},
            {title: 'PH-4区价格', width: 150, key: 'ph4Price'},
            {title: 'SG-4区价格', width: 150, key: 'sg4Price'},
            {title: 'TH-4区价格', width: 150, key: 'th4Price'},
            {title: 'TW-2区价格', width: 150, key: 'tw2Price'},
            {title: 'US-6区价格', width: 150, key: 'us6Price'},
            {title: 'VN-4区价格', width: 150, key: 'vn4Price'},
            {title: '备注', width: 150, key: 'note'},
          ]
        }else{
          me.totalColumns = [{
            title: '重量区间（KG）', width: 150, key: 'weighSection',
            render: (h, params) => {
              console.log(params)
              return h('span', params.row.lowWeight + '-' + params.row.upperWeight)
            }
          },//不维护
            {
              title: '价格类型', width: 150, key: 'priceType',
              render: (h, params) => {
                return this.cmbShowRender(h, params, expenseManager.priceType)
              }
            },
            {title: '一区价格', width: 150, key: 'onePrice'},
            {title: '二区价格', width: 150, key: 'twoPrice'},
            {title: '三区价格', width: 150, key: 'threePrice'},
            {title: '四区价格', width: 150, key: 'fourPrice'},
            {title: '五区价格', width: 150, key: 'fivePrice'},
            {title: '六区价格', width: 150, key: 'sixPrice'},
            {title: '七区价格', width: 150, key: 'sevenPrice'},
            {title: '八区价格', width: 150, key: 'eightPrice'},
            {title: '九区价格', width: 150, key: 'ninePrice'},
            {title: '备注', width: 150, key: 'note'}]
        }

        me.handleUpdateColumn(me.totalColumns)
        if (me.editConfigH.sid) {
          me.getList()
        }
      }
    }
  },
  methods: {
    /**
     * 弹出导入窗体
     */
    handleImport() {
      let me = this
      if(me.editConfigH.editData.countryType === ''){
        me.$Message.warning("请先选择表头栏位国别类型")
        return
      }
      me.modelImportShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.modelImportShow = false
      me.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 重写：获取查询参数
     * @returns {{billNo: *}}
     */
    getSearchParams() {
      let me = this
      return { headId: me.editConfigH.editData.sid }
    },
    loadFunctionsAfterOne() {
      let me = this
      if (!me.isLoadActoinsBody) {
        me.actions = []
      }
    }
  }
}
