import { expenseManager } from '@/view/cs-common/constant'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '重量区间（KG）', width: 150, key: 'weighSection',
        render: (h, params) => {
          console.log(params)
          return h('span', params.row.lowWeight + '-' + params.row.upperWeight)
        }
      },//不维护
      {
        title: '价格类型', width: 150, key: 'priceType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, expenseManager.priceType)
        }
      },
      {title: '一区价格', width: 150, key: 'onePrice'},
      {title: '二区价格', width: 150, key: 'twoPrice'},
      {title: '三区价格', width: 150, key: 'threePrice'},
      {title: '四区价格', width: 150, key: 'fourPrice'},
      {title: '五区价格', width: 150, key: 'fivePrice'},
      {title: '六区价格', width: 150, key: 'sixPrice'},
      {title: '七区价格', width: 150, key: 'sevenPrice'},
      {title: '八区价格', width: 150, key: 'eightPrice'},
      {title: '九区价格', width: 150, key: 'ninePrice'},
      {title: '备注', width: 150, key: 'note'}
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}
export {
  columns
}
