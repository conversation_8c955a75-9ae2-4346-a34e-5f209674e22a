<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions" v-if="isLoadActoinsBody">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight-450"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <QuotationFastBodyDetail v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" :editConfigH="this.editConfigH"></QuotationFastBodyDetail>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import ImportPage from 'xdo-import'
  import { mainJS } from './js/quotationFastBodyMain'
  import { editStatus } from '@/view/cs-common/constant'
  import { columns } from './js/quotationFastBodyColumns'
  import QuotationFastBodyDetail from './QuotationFastBodyDetail'

  export default {
    name: 'QuotationFastBodyMain',
    moduleName: '报价单表体',
    components: {
      QuotationFastBodyDetail,
      ImportPage
    },
    mixins: [columns, mainJS],
    data() {
      return {
        editStatus: editStatus
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
