import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 3,
      isShowSetting: true,
      gridConfig: {
        exportTitle: '报价单表头信息'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload    // '导出'
      },
      ajaxUrl: {
        delete: csAPI.expenseManager.quotationFast.delete,
        selectComboxByCode: csAPI.ieParams.selectComboxByCode,
        exportUrl: csAPI.expenseManager.quotationFast.exportUrl,
        selectAllPaged: csAPI.expenseManager.quotationFast.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
