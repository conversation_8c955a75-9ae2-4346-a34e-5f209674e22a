<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="quoNo" label="报价单编号">
        <XdoIInput type="text" v-model="searchParam.quoNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="quoName" label="报价单名称">
        <XdoIInput type="text" v-model="searchParam.quoName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="exchequerType" label="收款单位类型">
        <xdo-select v-model="searchParam.exchequerType"
                    :options="this.expenseManager.exchequerType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>

      <XdoFormItem prop="exchequerName" label="收款单位名称">
        <xdo-select v-model="searchParam.exchequerName"
                    :options="this.exchequerNameComData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieType" label="进出口类型">
        <xdo-select v-model="searchParam.ieType"
                    :options="this.expenseManager.ieType"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="effDateFromStr" label="有效开始日期">
        <XdoDatePicker type="date" placeholder="请选择日期" v-model="searchParam.effDateFromStr" transfer></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem prop="effDateToStr" label="有效结束日期">
        <XdoDatePicker type="date" placeholder="请选择日期" v-model="searchParam.effDateToStr" transfer></XdoDatePicker>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { expenseManager } from '@/view/cs-common/constant'

  export default {
    name: 'QuotationFastHeadSearch',
    data() {
      return {
        exchequerNameComData: [],
        expenseManager: expenseManager,
        searchParam: {
          quoNo: '',              // 报价单编号
          quoName: '',            // 报价单名称
          exchequerType: '',      // 收款单位类型
          exchequerName: '',      // 收款单位名称
          ieType: '',             // 进出口类型
          effDateFromStr: null,   // 有效开始日期
          effDateToStr: null,     // 有效结束日期
          quoType: '2'            // 报价单类型  // 报价单类型 (1 一般报价单 2 快件报价单)
        }
      }
    },
    created() {
      /**
       * 获取货代和报关行
       */
      let me = this
      me.$http.post(csAPI.ieParams.selectComboxByCode + '/FOD,CUT').then(res => { //收款单位名称
        me.exchequerNameComData = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
      })
    }
  }
</script>

<style scoped>
</style>
