<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions" v-if="isLoadActoinsBody">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight-450"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <QuotationFuelSurchargeDetail v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" :editConfigH="this.editConfigH"></QuotationFuelSurchargeDetail>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { mainJS } from './js/quotationFuelSurchargeMain'
  import { editStatus } from '@/view/cs-common/constant'
  import { columns } from './js/quotationFuelSurchargeColumns'
  import QuotationFuelSurchargeDetail from './QuotationFuelSurchargeDetail'

  export default {
    name: 'QuotationFuelSurchargeMain',
    moduleName: '燃油率',
    components: {
      QuotationFuelSurchargeDetail
    },
    mixins: [columns, mainJS],
    data() {
      return {
        editStatus: editStatus
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
