import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { editStatus } from '@/view/cs-common/constant'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  /**
   * 表头选择的数据
   */
  props: {
    editConfigH: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      isLoadActoinsBody: this.editConfigH.editStatus !== editStatus.SHOW, // true 加载   false 不加载
      p_group: 'fuelSurcharge',
      ajaxUrl: {
        delete: csAPI.expenseManager.costFuelSurcharge.delete,
        exportUrl: csAPI.expenseManager.costFuelSurcharge.export,
        selectAllPaged: csAPI.expenseManager.costFuelSurcharge.selectAllPaged
      },
      gridConfig: {
        exportTitle: '燃油附加费'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload,   // '导出'
      }
    }
  },
  methods: {
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 重写：获取查询参数
     * @returns {{billNo: *}}
     */
    getSearchParams() {
      let me = this
      return { headId: me.editConfigH.editData.sid }
    },
    loadFunctionsAfterOne() {
      let me = this
      if (!me.isLoadActoinsBody) {
        me.actions = []
      }
    }
  }
}
