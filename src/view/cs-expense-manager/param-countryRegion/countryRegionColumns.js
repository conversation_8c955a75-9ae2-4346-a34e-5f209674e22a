import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      {
        title: '国家代码',
        width: 180,
        align: 'center',
        key: 'paramsCode',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '国家名称',
        width: 220,
        align: 'center',
        key: 'paramsName',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '地区',
        minWidth: 150,
        align: 'center',
        key: 'note',
        ellipsis: true,
        tooltip: true
      }
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}

export {
  columns
}
