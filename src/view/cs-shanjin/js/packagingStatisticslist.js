import { isNullOrEmpty } from '@/libs/util'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
import { interimVerification, sition } from '@/view/cs-common'

export const packagingStatisticslist = {
  name: 'packagingStatisticslist',
  mixins: [baseSearchConfig, baseListConfig, dynamicExport],
  props: {
    commSource: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let params = this.getHeadParams()
    let fields = this.getHeadFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      hasChildTabs: true,
      toolbarEventMap: {
        'export': this.handleDownload,
        'statistics': this.handleStatistics,
        'setting': this.handleTableColumnSetup
      },
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      cmbSource: {
        pendingReturn:sition.status,
        resellPendingReturn:sition.status,
        remainingEmpty:sition.status
      },
      reportStatus: interimVerification.REPORT_STATUS_MAP
    }
  },
  computed: {
    extendParams() {
      return {}
    },
    /**
     * 动态数据源
     * @returns {*}
     */
    dynamicSource() {
      return {
        ...this.cmbSource,
        ...this.commSource
      }
    },
    ieDefaultDates() {
      let today = new Date(),
        dateTo = today.toLocaleDateString(),
        dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
      return [dateFrom, dateTo]
    },
    jobStatusName() {
      let me = this,
        theStatus = me.reportStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }

  },
  methods: {
    beforeFirstSearch() {
      let me = this
      me.$set(me.searchConfig.model, 'insertTimeFrom', me.ieDefaultDates[0])
      me.$set(me.searchConfig.model, 'insertTimeTo', me.ieDefaultDates[1])
    },
    actionLoaded() {
      let me = this
      me.actions = []
      me.actions.push({
        ...me.actionsComm,
        needed: true,
        loading: false,
        disabled: false,
        label: '数据统计',
        command: 'statistics',
        key: 'xdo-btn-delete',
        icon: 'ios-calculator-outline'
      })

      me.actions.push({
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      })
      me.actions.push({
        ...me.actionsComm,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting',
        key: 'xdo-btn-setting'
      })
    },
    getHeadParams() {
      let params = [{
        range: true,
        title: '统计日期',
        key: 'insertTime'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'pendingReturn',
        title: '2600待退运',
        type: 'select'
      }, {
        key: 'resellPendingReturn',
        title: '转卖待退运',
        type: 'select'
      }, {
        key: 'remainingEmpty',
        title: '还空剩余',
        type: 'select'
      }]
      return params
    },
    getHeadFields() {
      let columns = [{
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 150,
        key: 'temporaryEntry',
        title: '暂时进镜'
      }, {
        width: 120,
        key: 'outbound',
        title: '退运出境'
      }, {
        width: 150,
        key: 'resellIn',
        title: '转卖进'
      }, {
        width: 160,
        title: '转卖出',
        key: 'resellOut'
      }, {
        width: 150,
        key: 'payITax',
        title: '缴税进口'
      }, {
        width: 120,
        key: 'pendingReturn',
        title: '2600待退运'
      }, {
        width: 120,
        key: 'resellPendingReturn',
        title: '转卖待退运'
      }, {
        width: 140,
        key: 'totalEmpty',
        title: '还空总量'
      }, {
        width: 130,
        key: 'remainingEmpty',
        title: '还空剩余'
      }]
      return columns
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.$set(me.listConfig, 'exportTitle', '包材统计表')
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        if (res.data.data){
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
          me.handleSearchSubmit()
        }}
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
        me.handleSearchSubmit()
      })
    },
    /**
     * 数据统计
     */
    handleStatistics() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    }
  },
  beforeDestroy: function() {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
