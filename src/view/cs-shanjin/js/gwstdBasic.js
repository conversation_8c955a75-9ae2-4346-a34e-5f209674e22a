import pms from '@/libs/pms'
import CommUpload from '@/components/comm-upload/comm-upload'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const gwstdBasic = {
  components: {
    CommUpload
  },
  mixins: [pms, commList],
  data() {
    return {
      p_group: 'default',
      hasActions: true,
      importShow: false, // 是否显示导入
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete
      }
    }
  },
  methods: {
    actionLoaded() {
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    }
  }
}
