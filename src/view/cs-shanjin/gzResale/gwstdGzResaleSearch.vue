<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="iemark" label="进出标志">
        <xdo-select v-model="searchParam.iemark" :options="this.sition.MARK" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { sition } from '@/view/cs-common'

  export default {
    name: 'gwstdGzResaleSearch',
    data() {
      return {
        searchParam: {
          facGNo: '',
          gname: '',
          iemark: '',
          insertTimeFrom: '',
          insertTimeTo: '',
        },
        searchLines: 2,
        sition: sition
      }
    },
    methods: {
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
