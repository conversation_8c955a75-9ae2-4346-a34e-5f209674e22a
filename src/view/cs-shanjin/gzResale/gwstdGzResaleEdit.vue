<template>
  <section class='xdo-enter-root' v-focus>
    <XdoForm ref='dataForm' class='dc-form xdo-enter-form' :model='frmData' :rules='rulesHeader' label-position='right'
             :label-width='150'>
      <XdoCard :bordered='false' class='dc-merge-1-4 ieLogisticsTrackingCard' title='基础信息'>
        <div class='dc-form' style='padding-right: 10px;'>
          <XdoFormItem prop='iemark' label='进出标志'>
            <xdo-select v-model='frmData.iEMark' :options='this.sition.MARK' :asyncOptions='pcodeList'
                        :optionLabelRender='pcodeRender' :disabled='showDisable'></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop='iedate' label='进出厂日期'>
            <XdoDatePicker type='date' v-model='frmData.ieDate' style='width: 100%;'
                           :disabled='showDisable'></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop='facGNo' label='企业料号'>
            <xdo-select v-model='frmData.facGNo' :options='this.cmbSource.facGNoSource' :disabled='showDisable'
                        @on-change='facGNoEnter'></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop='gname' label='商品名称'>
            <XdoIInput type='text' v-model='frmData.gname' disabled></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop='qty' label='数量'>
            <xdo-input v-model='frmData.qty' decimal int-length="13" precision="5"
                       :disabled='showDisable'></xdo-input>
          </XdoFormItem>

          <XdoFormItem prop='unit' label='单位'>
            <xdo-select v-model='frmData.unit' :asyncOptions='pcodeList' :meta='pcode.unit'
                        :optionLabelRender='pcodeRender' :disabled='showDisable'></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop='note' label='备注'>
            <XdoIInput type='text' v-model='frmData.note' :disabled='showDisable' :maxlength='255'></XdoIInput>
          </XdoFormItem>

        </div>
      </XdoCard>
    </XdoForm>
    <div class='xdo-enter-action action' style='text-align: center; margin-top: 10px;'>
      <template v-for='item in buttons'>
        <Button v-if='item.needed' :type='item.type' :disabled='item.disabled' :loading='item.loading'
                @click='item.click' :icon='item.icon' :key='item.icon'>{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
import { csAPI } from '@/api'
import { gwstdEditBasic } from '../js/gwstdEditBasic'

export default {
  name: 'gwstdGzResaleEdit',
  mixins: [gwstdEditBasic],
  data() {
    return {
      isRead: false,
      cmbSource: {
        facGNoSource: []
      },
      ajaxUrl: {
        insert: csAPI.packaging.packag.insert,
        update: csAPI.packaging.packag.update,
        getcopGNo: csAPI.packaging.packag.getcopGNo,
        getFacGNoData: csAPI.packaging.packag.copGNoList
      },
      rulesHeader: {
        ieDate: [{required: true, message: '不能为空!', trigger: 'blur'}],
        facGNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
        gname: [{required: true, message: '不能为空!', trigger: 'blur'}],
        qty: [{required: true, type: 'number', message: '不能为空!', trigger: 'blur'}],
      }
    }
  },
  created() {
    this.getFacGNoData()
  },
  methods: {
    getDefaultData() {
      return {
        sid: '',
        iemark: '',
        ieDate: '',
        facGNo: '',
        gname: '',
        qty: null,
        unit: '',
        note: ''
      }
    },
    getFacGNoData() {
      let me = this
      me.$http.post(csAPI.packaging.packag.copGNoList).then(res => {
        me.$set(me.cmbSource, 'facGNoSource', res.data.map(item => {
          return {
            value: item,
            label: item
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'facGNoSource', [])
      })
    },
    facGNoEnter() {
      let me = this
      me.$http.post(csAPI.packaging.packag.getcopGNo, {
        copGNo: me.frmData.facGNo                                // 企业料号
      }).then(res => {
        me.$set(me.frmData, 'gname', res.data.gname)
        me.$set(me.frmData, 'unit', res.data.unit)
        me.fillUnitAfterShow()
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang='less' scoped>
.ieLogisticsTrackingCard .ivu-card-head {
  padding: 5px 10px !important;
}

.ieLogisticsTrackingCard .ivu-card-body {
  padding: 8px 8px 2px 8px;
}
</style>
