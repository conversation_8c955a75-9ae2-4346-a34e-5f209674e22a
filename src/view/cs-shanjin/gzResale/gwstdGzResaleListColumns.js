import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'


// 通用列
const commColumns = [
  'iEMark'
  , 'ieDate'
  , 'ieDateOut'
  , 'facGNo'
  , 'qty'
  , 'unit'
  , 'note'
  , 'insertUser'
  , 'insertTime'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]
const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 108,
          key: 'iEMark',
          title: '进出标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.sition.MARK)
          }
        },
        {
          width: 120,
          title: '进出厂日期',
          key: 'ieDateOut'
        },
        {
          width: 120,
          title: '企业料号',
          key: 'facGNo'
        },
        {
          width: 120,
          title: '数量',
          key: 'qty'
        },
        {
          title: '单位',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'unit',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          align: 'center',
          key: 'note',
          title: '备注'
        },
        {
          width: 80,
          title: '制单人',
          key: 'insertUser'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'insertTime'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
