import { namespace } from '@/project'
import gzResalelog from './gzResale/gwstdGzResaleList'
import requisitionManagementList from './requisitionManagement/gwstdRequisitionManagementList'
import emptyManagement from './empty/EmptyManagementList'
import packagingStatistics from './statistics/packagingStatisticslist'

export default [
  {
    path: '/' + namespace + '/packagingMaterial/gwstdGzResale',
    name: 'gwstdGzResaleList',
    meta: {
      title: '转卖登记'
    },
    component: gzResalelog
  },
  {
    path: '/' + namespace + '/packagingMaterial/requisitionManagement',
    name: 'gwstdRequisitionManagementList',
    meta: {
      title: '领用管理'
    },
    component: requisitionManagementList
  },
  {
    path: '/' + namespace + '/packagingMaterial/emptyManagement',
    name: 'gwstdEmptyManagement',
    meta: {
      title: '领用管理'
    },
    component: emptyManagement
  },
  {
    path: '/' + namespace + '/packagingMaterial/packagingStatistics',
    name: 'statistics',
    meta: {
      title: '领用管理'
    },
    component: packagingStatistics
  }
]
