<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <dc-dateRange label="还空日期" @onDateRangeChanged="handlevacantDateChange"></dc-dateRange>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>

  export default {
    name: 'EmptyManagementSearch',
    data() {
      return {
        searchParam: {
          facGNo: '',
          gname: '',
          iemark: '',
          insertTimeFrom: '',
          insertTimeTo: '',
        },
        searchLines: 2
      }
    },
    methods: {
      handlevacantDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "vacantDateFrom", values[0])
          this.$set(this.searchParam, "vacantDateTo", values[1])
        } else {
          this.$set(this.searchParam, "vacantDateFrom", '')
          this.$set(this.searchParam, "vacantDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
