<template>
  <section>
    <div v-show='showList'>
      <XdoCard :bordered='false'>
        <div ref='area_head'>
          <XdoBreadCrumb show-icon>
            <XdoButton type='primary' class='dc-margin-right' @click='handleSearchSubmit'>查询</XdoButton>
            <XdoButton type='warning' class='dc-margin-right' @click='handleShowSearch'>查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref='area_search'>
          <div v-show='showSearch'>
            <div class='separateLine'></div>
            <EmptyManagementSearch ref='headSearch'></EmptyManagementSearch>
          </div>
        </div>
      </XdoCard>
      <div class='action' ref='area_actions'>
        <xdo-toolbar @click='handleToolbarClick' :action-source='actions'></xdo-toolbar>
      </div>
      <XdoCard :bordered='false'>
        <DcAgGrid ref='table' :columns='gridConfig.gridColumns' :data='gridConfig.data' :height='dynamicHeight'
                  @onAgCellOperation='onAgCellOperation' @on-selection-change='handleSelectionChange'></DcAgGrid>
        <div ref='area_page'>
          <XdoPage class='dc-page' show-total show-sizer :page-size-opts='pageSizeOpts'
                   :current='pageParam.page' :page-size='pageParam.limit' :total='pageParam.dataTotal'
                   @on-change='pageChange' @on-page-size-change='pageSizeChange' />
        </div>
      </XdoCard>
    </div>
    <EmptyManagementEdit v-if='!showList' :editConfig='editConfig' :parentConfig='parentConfig'
                       @onEditBack='editBack'></EmptyManagementEdit>

  </section>
</template>

<script>
import { csAPI } from '@/api'
import { gwstdBasic } from '../js/gwstdBasic'
import EmptyManagementEdit from './EmptyManagementEdit'
import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
import EmptyManagementSearch from './EmptyManagementSearch'
import { columnsConfig, excelColumnsConfig, columns } from './EmptyManagementColumns'
import { editStatus,sition } from '@/view/cs-common'

export default {
  name: 'EmptyManagementList',
  components: {
    EmptyManagementEdit,
    EmptyManagementSearch
  },
  mixins: [gwstdBasic, columns],
  props: {
    parentConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      checkboxSelection: true,
      sition:sition,
      ajaxUrl: {
        selectAllPaged: csAPI.packaging.emptyManagement.selectAllPaged,
        delete: csAPI.packaging.emptyManagement.delete
      },
      gridConfig: {
        exportTitle: '转卖登记'
      },
      searchLines: 1,
      hasChildTabs: true
    }
  },
  mounted: function() {
    let me = this
    me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    me.loadFunctions('default')
  },
  methods: {
    handleEditByRow(row) {
      if (this.isSelectBySid) {
        this.$http.get(this.ajaxUrl.selectBySid + `/${row.sid}`).then(res => {
          row = res.data.data
        })
      }
      this.editConfig.editStatus = editStatus.EDIT
      this.editConfig.editData = row
      this.showList = false
    },

    /**
     * 行内 查看
     */
    handleViewByRow(row) {
      if (this.isSelectBySid) {
        this.$http.get(this.ajaxUrl.selectBySid + `/${row.sid}`).then(res => {
          row = res.data.data
        })
      }
      this.editConfig.editStatus = editStatus.SHOW
      this.editConfig.editData = row
      this.showList = false
    }
  }
}
</script>

<style lang='less' scoped>
.ivu-form-item {
  margin-bottom: 5px;
}

.separateLine {
  height: 10px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}
</style>
