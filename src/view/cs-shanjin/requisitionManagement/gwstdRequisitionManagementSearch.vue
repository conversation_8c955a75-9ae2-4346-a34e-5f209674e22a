<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="useType" label="领用类型">
        <xdo-select v-model="searchParam.useType" :options="this.sition.TYPE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="领用日期" @onDateRangeChanged="handleuseDateTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
import { sition } from '@/view/cs-common'

  export default {
    name: 'gwstdRequisitionManagementSearch',
    data() {
      return {
        searchParam: {
          facGNo: '',
          gname: '',
          iemark: '',
          insertTimeFrom: '',
          insertTimeTo: '',
        },
        searchLines: 2,
        sition:sition,
      }
    },
    methods: {
      handleuseDateTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "useDateFrom", values[0])
          this.$set(this.searchParam, "useDateTo", values[1])
        } else {
          this.$set(this.searchParam, "useDateFrom", '')
          this.$set(this.searchParam, "useDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
