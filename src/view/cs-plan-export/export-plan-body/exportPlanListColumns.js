import {erpInterfaceData, importExportManage, productClassify} from '@/view/cs-common'
import {baseColumns} from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = ['selection', 'operation'
  , 'sid'
  , 'headId'
  , 'tradeCode'
  , 'linkedNo'
  , 'lineNo'
  , 'bondMark'
  , 'factory'
  , 'facGNo'
  , 'qty'
  , 'decPrice'
  , 'decTotal'
  , 'systemPrice'
  , 'systemTotal'
  , 'unitErp'
  , 'curr'
  , 'grossWt'
  , 'netWt'
  , 'supplierCode'
  , 'supplierName'
  , 'customerGNo'
  , 'customerPoNo'
  , 'decPriceProcess'
  , 'decTotalProcess'
  , 'note1'
  , 'note2'
  , 'gmark'
  , 'outWay'
  , 'inOutRelNo'
  , 'buyerSupplierCode'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'insertUserName'
  , 'updateUserName'
]
const excelColumnsConfig = [
  'sid',
  'headId',
  'tradeCode',
  'linkedNo',
  'lineNo',
  'bondMark',
  'factory',
  'facGNo',
  'qty',
  'decPrice',
  'decTotal',
  'systemPrice',
  'systemTotal',
  'unitErp',
  'curr',
  'grossWt',
  'netWt',
  'supplierCode',
  'supplierName',
  'customerGNo',
  'customerPoNo',
  'decPriceProcess',
  'decTotalProcess',
  'note1',
  'note2',
  'gmark',
  'outWay',
  'inOutRelNo',
  'buyerSupplierCode',
  'insertUser',
  'insertTime',
  'updateUser',
  'updateTime',
  'insertUserName',
  'updateUserName',
]
const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '单据号',
          minWidth: 120,
          align: 'center',
          key: 'linkedNo',
        },
        {
          title: '单据序号',
          minWidth: 120,
          align: 'center',
          key: 'lineNo',
        },
        {
          title: '保完税标识',
          minWidth: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, importExportManage.bondedFlagMap)
          }
        },
        {
          title: '工厂',
          minWidth: 120,
          align: 'center',
          key: 'factory',
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
        },
        {
          title: '申报数量',
          minWidth: 120,
          align: 'center',
          key: 'qty',
        },
        {
          title: '申报单价',
          minWidth: 120,
          align: 'center',
          key: 'decPrice',
        },
        {
          title: '申报总价',
          minWidth: 120,
          align: 'center',
          key: 'decTotal',
        },
        {
          title: '系统单价',
          minWidth: 120,
          align: 'center',
          key: 'systemPrice',
        },
        {
          title: '系统总价',
          minWidth: 120,
          align: 'center',
          key: 'systemTotal',
        },
        {
          title: 'ERP交易单位',
          minWidth: 120,
          align: 'center',
          key: 'unitErp',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '币制',
          minWidth: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '毛重',
          minWidth: 120,
          align: 'center',
          key: 'grossWt',
        },
        {
          title: '净重',
          minWidth: 120,
          align: 'center',
          key: 'netWt',
        },
        {
          title: '客户编码(收货方)',
          minWidth: 120,
          align: 'center',
          key: 'supplierCode',
        },
        {
          title: '客户名称',
          minWidth: 120,
          align: 'center',
          key: 'supplierName',
        },
        {
          title: '客户料号',
          minWidth: 120,
          align: 'center',
          key: 'customerGNo',
        },
        {
          title: '客户订单号',
          minWidth: 120,
          align: 'center',
          key: 'customerPoNo',
        },
        {
          title: '加工费单价',
          minWidth: 120,
          align: 'center',
          key: 'decPriceProcess',
        },
        {
          title: '加工费总价',
          minWidth: 120,
          align: 'center',
          key: 'decTotalProcess',
        },
        {
          title: '物料类型',
          minWidth: 120,
          align: 'center',
          key: 'gmark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, productClassify.GMARK_MRP_SELECT)
          }
        },
        {
          title: '出口方式',
          minWidth: 120,
          align: 'center',
          key: 'outWay',
          render: (h, params) => {
            return this.cmbShowRender(h, params, erpInterfaceData.EXPORT_METHOD_MAP)
          }
        },
        {
          title: '出入库关联单号',
          minWidth: 120,
          align: 'center',
          key: 'inOutRelNo',
        },
        {
          title: '买方客户代码',
          minWidth: 120,
          align: 'center',
          key: 'buyerSupplierCode',
        },
        {
          title: '备注1',
          minWidth: 120,
          align: 'center',
          key: 'note1',
        },
        {
          title: '备注2',
          minWidth: 120,
          align: 'center',
          key: 'note2',
        },
      ]
    }
  }
}
export {
  columns,
  excelColumnsConfig,
  columnsConfig
}
