<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton class="dc-margin-right" type="primary" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton class="dc-margin-right" type="warning" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show='showSearch'>
          <div class='separateLine'></div>
          <!--查询条件-->
          <ExportPlanPackingListSearch ref='headSearch'></ExportPlanPackingListSearch>
        </div>
      </XdoCard>

      <XdoCard :bordered='false'>
        <div ref='area_actions' class='action'>
          <!--工具栏-->
          <xdo-toolbar :action-source='actions' @click='handleToolbarClick'></xdo-toolbar>
        </div>
      </XdoCard>

      <XdoCard :bordered="false">
        <xdo-ag-grid ref='ref_agGrid' :GridReady='handleGridReady'
                     :checkboxSelection="checkboxSelection"
                     :columns='gridConfig.gridColumns'
                     :data='gridConfig.data'
                     :height='dynamicHeight'
                     rowSelection='multiple'
                     @selectionChanged='handleSelectionChanged'>
        </xdo-ag-grid>
        <div ref='area_page'>
          <!--分页-->
          <XdoPage :current='pageParam.page' :page-size='pageParam.limit' :page-size-opts='pageSizeOpts'
                   :total='pageParam.dataTotal'
                   class='dc-page'
                   show-sizer
                   show-total
                   @on-change='pageChange'
                   @on-page-size-change='pageSizeChange'/>
        </div>
      </XdoCard>
    </div>
    <ExportPlanPackingEdit v-if="!showList" :editConfig="editConfig" :headId="this.parentConfig.editData.sid"
                           @onEditBack="editBack"></ExportPlanPackingEdit>
  </section>
</template>
<script>
import ExportPlanPackingEdit from './ExportPlanPackingEdit'
import ExportPlanPackingListSearch from './ExportPlanPackingListSearch'
import {csAPI} from '@/api'
import {columns} from './exportPlanPackingColumns'
import {commonMain} from "@/view/cs-acustomization/common/comm/commonMain";

export default {
  name: 'ExportPlanPackingList',
  components: {
    ExportPlanPackingListSearch,
    ExportPlanPackingEdit
  },
  mixins: [columns, commonMain],
  props: {
    parentConfig: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      p_group: 'pack',
      toolbarEventMap: {
        'add': this.handleCommonAdd,
        'edit': this.handleCommonEdit,
        'delete': this.handleDelete,
        'export': this.handleExport,
        'updatePackingStatus': this.handleUpdatePackingStatus,
        'setting': this.handleTableColumnSetup,
      },
      ajaxUrl: {
        // insert: csAPI.plan.export.pack.insert,
        // update: csAPI.plan.export.pack.update,
        delete: csAPI.plan.export.pack.delete,
        exportUrl: csAPI.plan.export.pack.exportUrl,
        list: csAPI.plan.export.pack.selectAllPaged,
        updatePackingStatus: csAPI.plan.export.head.updatePackingStatus,
      },
      showList: true,
      showEdit: false,

    }
  },
  mounted() {
  },
  methods: {
    // handleUpdateColumn(columns) {
    //   if (this.parentConfig.editStatus !== editStatus.ADD && this.parentConfig.editStatus !== editStatus.EDIT) {
    //     this.actions = this.actions.filter(item => ['export', 'setting'].includes(item.command))
    //     this.$set(this.gridConfig, 'disable', true)
    //   }
    //
    //   let edit = this.actions.filter(item => {
    //     return item.command === 'edit'
    //   })
    //
    //   let bascol = []
    //   if (edit && edit.length > 0) {
    //     if (this.checkboxSelection) {
    //       bascol = [{
    //         title: '操作',
    //         fixed: 'left',
    //         width: 120,
    //         align: 'center',
    //         key: 'operation',
    //         cellRendererFramework: operationRenderer(this)
    //       }]
    //     }
    //   } else {
    //     bascol = this.isShowOpView ? [{
    //       title: '操作',
    //       fixed: 'left',
    //       width: 90,
    //       align: 'center',
    //       key: 'operation',
    //       cellRendererFramework: operationRenderer(this, [{title: '查看', handle: 'handleViewByRow', marginRight: '0'}])
    //     }] : []
    //   }
    //   this.gridConfig.gridColumns = [...bascol, ...columns]
    //   this.gridConfig.exportColumns = columns.map(col => {
    //     return {
    //       key: col.key,
    //       value: col.title
    //     }
    //   })
    // },
    handleCommonGetSearchParams() {
      this.$refs.headSearch.searchParam.headId = this.parentConfig.editData.sid
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
    },

    handleUpdatePackingStatus() {
      this.$http.post(`${this.ajaxUrl.updatePackingStatus}/${this.parentConfig.editData.sid}`).then(res => {
        if (res.data.success) {
          this.$Message.success(res.data.message)
        } else {
          this.$Message.error(res.data.message)
        }
      }).catch(() => {
      }).finally(() => {

      })
    },
    handleDelete() {
      this.handleCommonDelete(this.ajaxUrl.delete, 'delete')
    },
    editBack(val) {
      if (val) {
        this.showList = true
        this.gridConfig.selectRows = []
        this.getList()
      }
    },
    // 同步导出
    handleExport() {
      this.handleCommonExport(this.ajaxUrl.export, 'export')
    },
  }
}
</script>
<style lang="less" scoped>
</style>
