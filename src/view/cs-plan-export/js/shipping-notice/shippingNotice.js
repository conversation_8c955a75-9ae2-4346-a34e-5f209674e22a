import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const shippingNotice = {
  name: 'shippingNotice',
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      cmbSource: {},
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = []
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '新增',
        command: 'add',
        icon: 'ios-add',
        key: 'xdo-btn-add'
      }, {
        ...me.actionsComm,
        label: '编辑',
        command: 'edit',
        key: 'xdo-btn-edit',
        icon: 'ios-create-outline'
      })
    },
    getCommParams() {
      return []
    },
    getCommFields() {
      return []
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.columns = [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, ...columns]
      me.listSetupShow = false
    },
    handleAdd() {
    },
    handleEdit() {
    },
    /**
     * 导出
     */
    handleDownload() {
      // this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
