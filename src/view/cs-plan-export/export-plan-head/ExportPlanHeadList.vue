<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton class="dc-margin-right" type="primary" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton class="dc-margin-right" type="warning" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show='showSearch'>
          <div class='separateLine'></div>
          <!--查询条件-->
          <ExportPlanHeadListSearch ref='headSearch'></ExportPlanHeadListSearch>
        </div>
      </XdoCard>

      <XdoCard :bordered='false'>
        <div ref='area_actions' class='action'>
          <!--工具栏-->
          <xdo-toolbar :action-source='actions' @click='handleToolbarClick'></xdo-toolbar>
        </div>
      </XdoCard>

      <XdoCard :bordered="false">
        <xdo-ag-grid ref='ref_agGrid' :GridReady='handleGridReady'
                     :checkboxSelection="checkboxSelection"
                     :columns='gridConfig.gridColumns'
                     :data='gridConfig.data'
                     :height='dynamicHeight'
                     rowSelection='multiple'
                     @selectionChanged='handleSelectionChanged'>
        </xdo-ag-grid>
        <div ref='area_page'>
          <!--分页-->
          <XdoPage :current='pageParam.page' :page-size='pageParam.limit' :page-size-opts='pageSizeOpts'
                   :total='pageParam.dataTotal'
                   class='dc-page'
                   show-sizer
                   show-total
                   @on-change='pageChange'
                   @on-page-size-change='pageSizeChange'/>
        </div>
      </XdoCard>
    </div>
    <ExportPlanTab v-if="!showList" :edit-config="editConfig" @onEditBack="editBack" :isPack="isPack"></ExportPlanTab>
    <TableColumnSetup v-model="listSetupShow" :columns="baseFields" :resId="listId"
                      class="height:500px" @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>
<script>
import ExportPlanHeadListSearch from './ExportPlanHeadListSearch'
import ExportPlanTab from './ExportPlanTab'
import {csAPI} from '@/api'
import {columns} from './exportPlanHeadColumns'
import {commonMain} from "@/view/cs-acustomization/common/comm/commonMain";
import {operationRenderer} from "@/view/cs-acustomization/common/agGridJs/operation_renderer";

export default {
  name: 'ExportPlanHeadList',
  components: {
    ExportPlanHeadListSearch,
    // ExportPlanHeadEdit,
    ExportPlanTab
  },
  mixins: [columns, commonMain],
  data() {
    return {
      isPack: false,
      p_group: 'default',
      showList: true,
      listSetupShow: true,
      showEdit: false,
      gridConfig: {
        exportTitle: '出口计划表头'
      },
      ajaxUrl: {
        delete: csAPI.plan.export.head.delete,
        export: csAPI.plan.export.head.exportUrl,
        customsGenerated: csAPI.plan.export.head.customsGenerated,
        list: csAPI.plan.export.head.selectAllPaged
      },
      toolbarEventMap: {
        'add': this.handleCommonAdd,
        'edit': this.handleCommonEdit,
        'delete': this.handleDelete,
        'export': this.handleExport,
        'customsGenerated': this.handleCustomsGenerated,
        'setting': this.handleTableColumnSetup,
      },
    }
  },
  mounted() {
  },
  methods: {
    handleUpdateColumn(columns) {
      this.actions.map(item => {
        if (item.command === 'packingData') {
          this.isPack = true
          this.actions = this.actions.filter(item => ['export', 'setting'].includes(item.command))
          this.$set(this.gridConfig, 'disable', true)
        }
      })

      let edit = this.actions.filter(item => {
        return item.command === 'edit'
      })

      let bascol = []
      if (edit && edit.length > 0) {
        if (this.checkboxSelection) {
          bascol = [{
            title: '操作',
            fixed: 'left',
            width: 120,
            align: 'center',
            key: 'operation',
            cellRendererFramework: operationRenderer(this)
          }]
        }
      } else {
        bascol = this.isShowOpView ? [{
          title: '操作',
          fixed: 'left',
          width: 90,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this, [{title: '查看', handle: 'handleViewByRow', marginRight: '0'}])
        }] : []
      }
      this.gridConfig.gridColumns = [...bascol, ...columns]
      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },
    /**
     * 自定义配置
     */
    handleTableColumnSetup() {
      let me = this
      me.listSetupShow = true
    },
    handleCustomsGenerated() {
      if (this.handleCommonCheckRowSelected('报关申请', true)) {
        if (!['2'].includes(this.gridConfig.selectRows[0].status)) {
          this.$Message.warning('只有装箱完成的单据可以生成报关数据!')
        } else {
          this.$http.post(`${this.ajaxUrl.customsGenerated}/${this.gridConfig.selectRows[0].sid}`).then(res => {
            if (res.data.success) {
              this.$Message.success(res.data.message)
              this.getList()
            } else {
              this.$Message.error(res.data.message)
            }
          }).catch(() => {
          }).finally(() => {

          })
        }
      }
    },
    handleDelete() {
      this.handleCommonDelete(this.ajaxUrl.delete, 'delete')
    },
    editBack(val) {
      if (val) {
        this.showList = true
        this.gridConfig.selectRows = []
        this.getList()
      }
    },
    // 同步导出
    handleExport() {
      this.handleCommonExport(this.ajaxUrl.export, 'export')
    },
  }
}
</script>
<style lang="less" scoped>
</style>
