<template>
  <section>
    <XdoForm ref="formInline" :label-width="100" :model="searchParam" class="dc-form" inline label-position="right">
      <XdoFormItem label="企业内部编号" prop="emsListNo">
        <XdoIInput v-model="searchParam.emsListNo" type="text" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="状态" prop="status">
        <xdo-select v-model="searchParam.status" :optionLabelRender="pcodeRender" :options="cmbSource.status" type="text" clearable></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="要求出口速度" prop="exitSpeed">
        <xdo-select v-model="searchParam.exitSpeed" :optionLabelRender="pcodeRender" :options="cmbSource.export_speed" type="text" clearable></xdo-select>
      </XdoFormItem>
      <XdoFormItem label="客户代码" prop="supplierCode">
        <XdoIInput v-model="searchParam.supplierCode" type="text" clearable></XdoIInput>
      </XdoFormItem>
      <dcDateRange label="制单日期" @onDateRangeChanged="handleDateChange"></dcDateRange>
    </XdoForm>
  </section>
</template>
<script>
import {plan} from "@/view/cs-common/constant";
import dcDateRange from '@/components/dc-date-range/dc-date-range'
export default {
  name: 'ExportPlanHeadListSearch',
  components: {
    dcDateRange
  },
  data() {
    return {
      searchParam: {
        emsListNo: '',
        supplierCode: '',
        insertTimeFrom: '',
        insertTimeTo: '',
        status: '0',
        exitSpeed: '',
      },
      cmbSource: {
        status: plan.status,
        export_speed: plan.export_speed,
      }
    }
  },
  mounted() {
  },
  methods: {
    handleDateChange(values) {
      this.searchParam.insertTimeFrom = values[0]
      this.searchParam.insertTimeTo = values[1]
    }
  }
}
</script>
<style scoped>
</style>
