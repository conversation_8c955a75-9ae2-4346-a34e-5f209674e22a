import { namespace } from '@/project'
import UnitOfRecordList from './unit-of-record/unit-of-record-list'
import CurrOfInvoiceList from './curr-of-invoice/curr-of-invoice-list'
import StateOfInvoiceList from './state-of-invoice/state-of-invoice-list'
import TrafModeOfInvoiceList from './traf-mode-of-invoice/traf-mode-of-invoice-list'
import WrapTypeOfInvoiceList from './wrap-type-of-invoice/wrap-type-of-invoice-list'

export default [
  {
    path: '/' + namespace + '/invoiceParamLibs/unitOfRecord',
    name: 'unitOfRecordList',
    meta: {
      icon: 'ios-document',
      title: '计量单位'
    },
    component: UnitOfRecordList
  },
  {
    path: '/' + namespace + '/invoiceParamLibs/currOfInvoiceList',
    name: 'currOfInvoiceList',
    meta: {
      icon: 'ios-document',
      title: '币制'
    },
    component: CurrOfInvoiceList
  },
  {
    path: '/' + namespace + '/invoiceParamLibs/stateOfInvoiceList',
    name: 'stateOfInvoiceList',
    meta: {
      icon: 'ios-document',
      title: '国别'
    },
    component: StateOfInvoiceList
  },
  {
    path: '/' + namespace + '/invoiceParamLibs/trafModeOfInvoiceList',
    name: 'trafModeOfInvoiceList',
    meta: {
      icon: 'ios-document',
      title: '运输方式'
    },
    component: TrafModeOfInvoiceList
  },
  {
    path: '/' + namespace + '/invoiceParamLibs/wrapTypeOfInvoiceList',
    name: 'wrapTypeOfInvoiceList',
    meta: {
      icon: 'ios-document',
      title: '包装种类'
    },
    component: WrapTypeOfInvoiceList
  }
]
