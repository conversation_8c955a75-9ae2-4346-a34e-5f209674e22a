<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ParamSearch ref="headSearch" :params-type="paramsType"></ParamSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import { paramList } from '@/view/cs-enterprise-params-lib/js/paramList'

  export default {
    name: 'stateOfInvoiceList',
    mixins: [paramList],
    data() {
      return {
        gridConfig: {
          exportTitle: '国别'
        },
        paramsCodeLabel: '发票国别',
        customParamCodeLabel: '海关国别',
        paramsType: 'INV_STATE'
      }
    },
    methods: {
      columnsSort(columns) {
        let me = this
        let formId = me.$route.path + '/' + me.$options.name
        let defaultCols = ['selection', 'operation', 'customParamCode', 'paramsCode', 'note', 'userName', 'insertTime']
        return me.$bom3.userCustom('form', formId, columns, defaultCols)
      },
      exportColsSort(columns) {
        let defaultCols = ['customParamCode', 'paramsCode', 'note', 'userName', 'insertTime']
        let result = []
        defaultCols.forEach(field => {
          let theFields = columns.filter(col => {
            return col.key === field
          })
          if (Array.isArray(theFields) && theFields.length > 0) {
            result.push(theFields[0])
          }
        })
        return result
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
