<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DeliveryBodySearch ref="bodySearch"></DeliveryBodySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard>
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" @onAgCellOperation="onAgCellOperation"
                  :height="dynamicHeight" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <DeliveryInfo v-if="!showList" @closeInfo="closeInfo" :emsNo="emsNo" :typeNo="typeNo" :searchData="searchData" :headid="headId" :gridConfig="gridConfig" type="false"></DeliveryInfo>
    <ImportPage :importKey="importKey" :importShow.sync="ImportShow" :importConfig="importConfig" @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import ImportPage from 'xdo-import'
  import DeliveryInfo from './deliveryInfo'
  import { editStatus } from '@/view/cs-common'
  import { commonMethods } from './../../../common'
  import DeliveryBodySearch from './deliveryBodySearch'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { columnsConfig, excelColumnsConfig, columns } from './deliveryBodyColumns'

  export default {
    name: 'deliveryBody',
    components: {
      ImportPage,
      DeliveryInfo,
      DeliveryBodySearch
    },
    props: {
      typeno: {
        type: String,
        default: () => ({})
      },
      headId: {
        type: String,
        default: () => ({})
      },
      emsNo: {
        type: String,
        default: () => ({})
      }
    },
    mixins: [columns, commonMethods, pms, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('SEND-LIST', {
        headId: this.headId,
        insertUserName: this.$store.state.user.userName
      })
      return {
        typeNo: '',
        searchData: {},
        displayStyle: '',
        gridConfig: {
          selectData: [],
          editStatus: ''
        },
        initSearch: false,
        hasChildTabs: true,
        toolbarEventMap: {
          'edit': this.editInfor,
          'add': this.templateAdd,
          'export': this.exportExcel,
          'import': this.importExcel,
          'delete': this.handleDelete
        },
        ImportShow: false,
        importKey: 'sendlist',
        importConfig: importConfig
      }
    },
    mounted() {
      let me = this
      me.loadFunctions('body').then()
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
      me.handleSearchSubmit()
      me.checkType(me.typeno)
    },
    methods: {
      //类型判断
      checkType(e) {
        if (e === 2) {
          let me = this
          me.actions.splice(1, 1)
          me.displayStyle = 'none'
        }
      },
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
      },
      templateAdd() {
        let me = this
        me.gridConfig.editStatus = editStatus.ADD
        me.showList = !me.showList
        me.typeNo = 0
      },
      //查询
      handleSearchSubmit() {
        let me = this
        me.$refs.bodySearch.wfSendListParam.headId = me.headId
        me.$http.post(csAPI.wfManage.wfsend.body.bodysearch, me.$refs.bodySearch.wfSendListParam, {
          params: {
            page: me.pageParam.page,
            limit: me.pageParam.limit
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.dataTotal = res.data.total
        }).catch(() => {
        })
      },
      //分页触发
      pageChange(val) {
        let me = this
        me.pageParam.page = val
        me.handleSearchSubmit()
      },
      pageSizeChange(val) {
        let me = this
        me.pageParam.limit = val
        if (me.pageParam.page === 1) {
          me.handleSearchSubmit()
        }
      },
      //选中赋值
      handleSelectionChange(e) {
        let me = this
        me.gridConfig.selectRows = e
      },
      //编辑
      editInfor() {
        let me = this
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要编辑的数据!')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('一次仅能编辑一条数据!')
        } else {
          me.searchData = me.gridConfig.selectRows[0]
          me.showList = !me.showList
          me.showTabs = !me.showTabs
          me.compData = me.dataList
          me.typeNo = 1
          me.gridConfig.selectRows = []
          me.gridConfig.editStatus = editStatus.EDIT
        }
      },
      //表格编辑
      handleEditByRow(val) {
        let me = this
        me.searchData = val
        me.gridConfig.editStatus = editStatus.EDIT
        me.showList = !me.showList
        me.showTabs = !me.showTabs
        me.compData = me.dataList
        me.typeNo = 1
      },
      //查看
      handleViewByRow(val) {
        let me = this
        me.gridConfig.editStatus = editStatus.SHOW
        me.showList = !me.showList
        me.showTabs = !me.showTabs
        me.searchData = val
        me.compData = me.dataList
        me.typeNo = 2 //查看
      },
      //导出
      exportExcel() {
        let me = this
        let date = new Date()
        let year = date.getFullYear()
        let month = (date.getMonth() + 1).toString().padStart(2, '0')
        let day = (date.getDate()).toString().padStart(2, "0")
        me.gridConfig.exportTitle = `外发加工发货单表体${year}-${month}-${day}`
        const url = csAPI.wfManage.wfsend.body.bodyexport
        me.$refs.bodySearch.wfSendListParam.headId = me.headId
        me.doExport(url, 2, me.$refs.bodySearch.wfSendListParam)
      },
      //删除
      handleDelete() {
        let me = this
        if (me.gridConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              const sids = me.gridConfig.selectRows.map(item => {
                return item.sid
              })
              me.$http.delete(csAPI.wfManage.wfsend.body.bodydelete + `/${sids}`).then(() => {
                me.$Message.success('删除成功!')
                me.gridConfig.selectRows = []
                me.handleSearchSubmit()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }
      },
      closeInfo(e) {
        if (e) {
          let me = this
          me.showList = !me.showList
          me.gridConfig.selectRows = []
          me.handleSearchSubmit()
        }
      },
      // 导入
      importExcel() {
        let me = this
        me.ImportShow = true
      },
      onAfterImport() {
        let me = this
        me.ImportShow = false
        me.handleSearchSubmit()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
