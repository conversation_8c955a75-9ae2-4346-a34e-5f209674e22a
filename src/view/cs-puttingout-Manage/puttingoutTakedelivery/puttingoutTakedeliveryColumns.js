import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  ,'billNo'
  ,'createDate'
  ,'receiveDate','emsCopNo','declareDate','applyNo','contractorTradeCode','contractorTradeName','entrustEmsNo','remark'
]
const excelColumnsConfig = [
  'billNo','createDate','receiveDate','emsCopNo','declareDate','applyNo','contractorTradeCode','contractorTradeName','entrustEmsNo','remark'
]
const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          key: 'billNo',
          title: '收货单编号'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'createDate'
        },
        {
          width: 120,
          title: '收货日期',
          key: 'receiveDate'
        },
        {
          width: 120,
          key: 'emsCopNo',
          title: '企业内部编号'
        },
        {
          width: 120,
          title: '申报日期',
          key: 'declareDate'
        },
        {
          width: 120,
          key: 'applyNo',
          title: '申报表编号'
        },
        {
          width: 120,
          title: '承揽者海关编码',
          key: 'contractorTradeCode'
        },
        {
          width: 120,
          title: '承揽者名称',
          key: 'contractorTradeName',
          render: (h, params) => {
            return h('span', `${params.row.contractorCompanyCode}  ${params.row.contractorTradeName}`)
          }
        },
        {
          width: 120,
          key: 'entrustEmsNo',
          title: '委托方手册账号'
        },
        {
          width: 120,
          title: '备注',
          key: 'remark',
          tooltip: true
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
