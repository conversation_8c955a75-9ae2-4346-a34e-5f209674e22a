<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="收货单编号">
        <XdoIInput v-model="wfReceiveHeadParam.billNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="企业内部编号">
        <XdoIInput v-model="wfReceiveHeadParam.emsCopNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="承揽者名称">
        <xdo-select v-model="wfReceiveHeadParam.contractorCompanyCode" clearable :options="inDataSource.contractorTradeName" dataValue="key" dataLabel="value"
                    :optionLabelRender="(item) => item.key + ' ' + item.value"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="status" label="委托方手账册号">
        <xdo-select v-model="wfReceiveHeadParam.entrustEmsNo" clearable :options="entrustEmsNo" dataValue="key" dataLabel="value"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import {commonMethods} from './../common'

  export default {
    name: "puttingoutTakedeliverySearch",
    mixins: [commonMethods],
    data() {
      return {
        wfReceiveHeadParam: {
          billNo: '',
          emsCopNo: '',
          contractorCompanyCode: '',
          createDateFrom: '',
          createDateTo: '',
          entrustEmsNo: ''
        },
        inDataSource: {}
      }
    },
    mounted() {
      this.getBusparter()
      this.getentrustEmsNo()
    },
    methods: {
      handleDMDateChange(e) {
        this.wfReceiveHeadParam.createDateFrom = e[0]
        this.wfReceiveHeadParam.createDateTo = e[1]
      }
    }
  }
</script>

<style scoped>
</style>
