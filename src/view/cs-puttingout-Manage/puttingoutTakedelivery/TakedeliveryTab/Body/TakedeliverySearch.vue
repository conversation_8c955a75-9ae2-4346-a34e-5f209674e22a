<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="gmark" label="物料类型">
        <xdo-select v-model="wfReceiveListParam.gmark" clearable :options="productClassify.GMARK_SELECT_List" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput v-model="wfReceiveListParam.facGNo" :maxlength="50"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput  v-model="wfReceiveListParam.copGNo" :maxlength="50"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gno" label="备案序号">
        <XdoIInput v-model="wfReceiveListParam.gno"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="curr" label="币值">
        <xdo-select v-model="wfReceiveListParam.curr" :asyncOptions="pcodeList" meta="CURR_OUTDATED" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput v-model="wfReceiveListParam.gname"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { productClassify } from '@/view/cs-common'

  export default {
    name: "TakedeliverySearch",
    data() {
      return {
        wfReceiveListParam: {
          headId: '',
          gno: '',
          gname: '',
          curr: '',
          gmark: '',
          facGNo: '',
          copGNo: ''
        },
        productClassify: productClassify
      }
    }
  }
</script>

<style scoped>
</style>
