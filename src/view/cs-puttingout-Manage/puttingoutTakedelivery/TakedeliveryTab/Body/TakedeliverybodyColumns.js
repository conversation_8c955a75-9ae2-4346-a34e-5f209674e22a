import { productClassify } from '@/view/cs-common'
import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  ,'gno','codeTS','gname','unit','qty','amount','curr','remark','gmark','facGNo','copGNo'
]
const excelColumnsConfig = [
  'gno','codeTS','gname','unit','qty','amount','curr','remark','gmark','facGNo','copGNo'
]
const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          key: 'gmark',
          tooltip: true,
          title: '物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, productClassify.GMARK_SELECT_List)
          }
        },
        {
          width: 120,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          tooltip: true,
          title: '备案料号'
        },
        {
          width: 120,
          key: 'gno',
          title: '备案序号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 120,
          key: 'gname',
          title: '商品名称'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'qty',
          title: '收货数量'
        },
        {
          width: 120,
          key: 'amount',
          title: '收货金额'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          title: '备注',
          key: 'remark',
          tooltip: true
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
