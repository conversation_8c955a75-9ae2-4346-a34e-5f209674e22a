<template>
  <section v-focus>
    <XdoCard :bordered="false">
      <XdoForm ref="formInline" :model="wfReceiveHeadParam" class="dc-form dc-form-3 xdo-enter-form" label-position="right" :label-width="120" :rules="rulesHeader" inline>
        <XdoFormItem prop="billNo" label="收货单编号">
          <XdoIInput type="text" :maxlength="30" v-model="wfReceiveHeadParam.billNo" :disabled="isRead"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="createDate" label="制单日期">
          <XdoDatePicker v-model="wfReceiveHeadParam.createDate" :disabled="isRead"></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem label="收货日期">
          <XdoDatePicker v-model="wfReceiveHeadParam.receiveDate" :disabled="isRead"></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="emsCopNo" label="企业内部编号">
          <xdo-select v-model="wfReceiveHeadParam.emsCopNo" clearable :options="inDataSource.emsCopNoList"  @on-change="changeemsCopNo" :disabled="isRead"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="status" label="申报表编号">
          <XdoIInput disabled v-model="wfReceiveHeadParam.applyNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="申报日期">
          <XdoIInput disabled v-model="wfReceiveHeadParam.declareDate"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="承揽者海关编码">
          <XdoIInput disabled v-model="wfReceiveHeadParam.contractorTradeCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="承揽者社会信用代码">
          <XdoIInput disabled v-model="wfReceiveHeadParam.contractorCreditCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="承揽者名称">
          <XdoIInput disabled v-model="wfReceiveHeadParam.contractorTradeName"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="承揽者主管海关">
          <xdo-select disabled v-model="wfReceiveHeadParam.contractorMasterCustoms" clearable :asyncOptions="pcodeList" meta="CUSTOMS_REL" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="status" label="委托方手账册号">
          <XdoIInput disabled v-model="wfReceiveHeadParam.entrustEmsNo"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="委托方海关编码">
          <XdoIInput disabled v-model="wfReceiveHeadParam.tradeCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="委托方信用代码">
          <XdoIInput disabled v-model="wfReceiveHeadParam.entrustCreditCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="委托方企业名称">
          <XdoIInput disabled v-model="wfReceiveHeadParam.entrustTradeName"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="委托方主管海关">
          <xdo-select disabled v-model="wfReceiveHeadParam.entrustMasterCustoms" clearable :asyncOptions="pcodeList" meta="CUSTOMS_REL" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="status" label="收货申报人">
          <XdoIInput type="text" :maxlength="30" v-model="wfReceiveHeadParam.receiver" :disabled="isRead"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="申报单位">
          <XdoIInput type="text" :maxlength="10" v-model="wfReceiveHeadParam.declareTradeCode" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="申报单位信用代码">
          <XdoIInput disabled v-model="wfReceiveHeadParam.declareCreditCode"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="status" label="备注" class="dc-merge-1-4">
          <XdoIInput type="text" :maxlength="255" v-model="wfReceiveHeadParam.remark" :disabled="isRead"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="dc-container-center">
      <XdoButton class="btntool" type="warning"  @click="templateSave()" v-show="!isRead">保存</XdoButton>&nbsp;
      <XdoButton class="btntool" type="warning"  @click="templateSaveClose()" v-show="!isRead">保存关闭</XdoButton>&nbsp;
      <XdoButton class="btntool"  type="primary" @click="closeAdd">返回</XdoButton>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { commonMethods } from './../../../common'

  export default {
    name: "Takedeliveryhead",
    mixins: [commonMethods],
    props: {
      typeno: {
        type: String,
        default: () => ({})
      },
      searchdata: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        sid: '',
        isRead: false,
        wfReceiveHeadParam: {
          //dataSource:'1',
          billNo: '',
          createDate: '',
          receiveDate: '',
          emsCopNo: '',
          applyNo: '',
          declareDate: '',
          contractorTradeCode: '',
          contractorCreditCode: '',
          contractorCompanyCode: '',
          contractorTradeName: '',
          entrustEmsNo: '',
          tradeCode: '',
          entrustCreditCode: '',
          entrustTradeName: '',
          entrustMasterCustoms: '',
          receiver: '',
          declareTradeCode: '',
          declareCreditCode: '',
          declareTradeName: '',
          remark: '',
          contractorMasterCustoms: ''
        },
        rulesHeader: {
          billNo: [{required: true, message: '不能为空！', trigger: 'blur'}],
          emsCopNo: [{required: true, message: '不能为空！', trigger: 'blur'}],
          createDate: [{required: true, message: '不能为空！', trigger: 'blur', pattern: /.+/}],
        },
        inDataSource: {}
      }
    },
    mounted() {
      this.getemsCopNoList()
      this.typeCheck(this.typeno)
    },
    methods: {
      getNowtime() {
        this.wfReceiveHeadParam.createDate = new Date().toLocaleDateString()
      },
      typeCheck(val) {
        if (val === 0) {
          this.isRead = false
          this.getNowtime()
        } else if (val === 1) {
          this.isRead = false
          this.checkInfo(this.searchdata)
        } else if (val === 2) {
          this.isRead = true
          this.checkInfo(this.searchdata)
        }
      },
      //查看+编辑赋值
      checkInfo(val) {
        this.sid = val.sid
        Object.keys(val).map(item => {
          if (val[item] !== null) {
            this.wfReceiveHeadParam[item] = val[item]
          }
        })
      },
      changeemsCopNo() {
        if (this.wfReceiveHeadParam.emsCopNo !== '') {
          this.isSend = false
          this.getApplyInfo(this.wfReceiveHeadParam.emsCopNo)
        }
      },
      templateSave() {
        this.$refs['formInline'].validate(valid => {
          if (valid) {
            let oid
            if (this.sid !== '') {
              oid = this.sid
            } else {
              oid = undefined
            }
            this.wfReceiveHeadParam.declareTradeCode = this.wfReceiveHeadParam.declareTradeCode.slice(0, this.wfReceiveHeadParam.declareTradeCode.indexOf('-'))
            this.wfReceiveHeadParam.contractorTradeName = this.wfReceiveHeadParam.contractorTradeName.slice(this.wfReceiveHeadParam.contractorTradeName.indexOf('-') + 1)
            if (this.typeno === 0) {
              this.$http.post(csAPI.wfManage.wfreceive.wfreceivecheck + `/${this.wfReceiveHeadParam.billNo}/${oid}`).then(res => {
                if (res.data.data === null) {
                  this.$http.post(csAPI.wfManage.wfreceive.wfreceiveadd, this.wfReceiveHeadParam).then(res => {
                    if (res.data.success) {
                      this.sid = res.data.data.sid
                      this.$emit('showBody', this.sid)
                      this.$emit('showEmsno', res.data.data.entrustEmsNo)
                      this.wfReceiveHeadParam.declareTradeCode = `${this.wfReceiveHeadParam.declareTradeCode}-${this.wfReceiveHeadParam.declareTradeName}`
                      this.wfReceiveHeadParam.contractorTradeName = `${this.wfReceiveHeadParam.contractorCompanyCode}-${this.wfReceiveHeadParam.contractorTradeName}`
                      this.$Message.success('新增成功')
                    }
                  })
                } else {
                  this.$Message.warning('收货单已存在!')
                }
              })
            } else if (this.typeno === 1) {
              this.$http.post(csAPI.wfManage.wfreceive.wfreceivecheck + `/${this.wfReceiveHeadParam.billNo}/${oid}`).then(res => {
                if (res.data.data === null) {
                  this.$http.put(csAPI.wfManage.wfreceive.wfreceiveadd + `/${this.sid}`, this.wfReceiveHeadParam).then(res => {
                    if (res.data.success) {
                      this.sid = res.data.data.sid
                      this.$emit('showBody', true)
                      this.wfReceiveHeadParam.declareTradeCode = `${this.wfReceiveHeadParam.declareTradeCode}-${this.wfReceiveHeadParam.declareTradeName}`
                      this.wfReceiveHeadParam.contractorTradeName = `${this.wfReceiveHeadParam.contractorCompanyCode}-${this.wfReceiveHeadParam.contractorTradeName}`
                      this.$Message.success('编辑成功')
                    }
                  })
                } else {
                  this.$Message.warning('收货单已存在!')
                }
              })
            }
          }
        })
      },
      templateSaveClose() {
        this.$refs['formInline'].validate().then(valid => {
          if (valid) {
            this.templateSave() //调用保存方法
            setTimeout(this.closeAdd, 1000)
          }
        })
      },
      closeAdd() {
        this.$emit('closeHead', true)
      }
    }
  }
</script>

<style scoped>
</style>
