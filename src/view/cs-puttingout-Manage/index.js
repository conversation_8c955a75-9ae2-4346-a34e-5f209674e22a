import { namespace } from '@/project'
import puttingoutDeliveryList from './puttingoutDelivery/puttingoutDeliveryList'
import puttingoutDeclarationList from './puttingoutDeclaration/puttingoutDeclarationList'
import puttingoutTakedeliveryList from './puttingoutTakedelivery/puttingoutTakedeliveryList'
import puttingoutDeliveryHeadListReport from '@/view/cs-puttingout-Manage/puttingoutDeliveryReport/puttingoutDeliveryHeadListReport'
import puttingoutTakedeliveryHeadListReport from '@/view/cs-puttingout-Manage/puttingoutTakedeliveryReport/puttingoutTakedeHeadListReport'

export default [
  {
    path: '/' + namespace + '/puttingoutManage/puttingoutDeclaration',
    name: 'puttingoutDeclarationList',
    meta: {
      icon: 'ios-document',
      title: '外发加工申报表'
    },
    component: puttingoutDeclarationList
  },
  {
    path: '/' + namespace + '/puttingoutManage/puttingoutDelivery',
    name: 'puttingoutDeliveryList',
    meta: {
      icon: 'ios-document',
      title: '外发加工发货单'
    },
    component: puttingoutDeliveryList
  },
  {
    path: '/' + namespace + '/puttingoutManage/puttingoutTakedelivery',
    name: 'puttingoutTakedeliveryList',
    meta: {
      icon: 'ios-document',
      title: '外发加工收货单'
    },
    component: puttingoutTakedeliveryList
  },
  {
    path: '/' + namespace + '/puttingoutManage/puttingoutDeliveryReport',
    name: 'puttingDeliveryHeadListReport',
    meta: {
      icon: 'ios-document',
      title: '发货单报表'
    },
    component: puttingoutDeliveryHeadListReport
  }
  ,
  {
    path: '/' + namespace + '/puttingoutManage/puttingoutTakedeliveryReport',
    name: 'puttingoutRedeliveryHeadListReport',
    meta: {
      icon: 'ios-document',
      title: '收货单报表'
    },
    component: puttingoutTakedeliveryHeadListReport
  }
]
