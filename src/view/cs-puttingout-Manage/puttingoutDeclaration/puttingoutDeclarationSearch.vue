<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="申报表编号">
        <XdoIInput v-model="searchParams.applyNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="企业内部编号">
        <XdoIInput v-model="searchParams.emsCopNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="承揽者名称">
        <xdo-select v-model="searchParams.contractorCompanyCode" clearable :options="inDataSource.contractorTradeName" dataValue="key" dataLabel="value"
                    :optionLabelRender="(item) => item.key + ' ' + item.value"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="申报日期" @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="status" label="委托合同号">
        <XdoIInput v-model="searchParams.entrustContrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="委托方手账册号">
        <xdo-select v-model="searchParams.entrustEmsNo" clearable :options="inDataSource.addentrustEmsNo" dataValue="key" dataLabel="value"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="全工序外发">
        <xdo-select :options="inDataSource.allPutout" dataValue="key" dataLabel="value"
                    :optionLabelRender="(item) => item.key + ' ' + item.value" v-model="searchParams.outSendFlag"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { commonMethods } from './../common'
  import { putoutManage } from '../../cs-common'

  export default {
    name: 'puttingoutDeclarationSearch',
    mixins: [commonMethods],
    data() {
      return {
        id: false,
        searchParams: {
          applyNo: '', //申报表编号
          emsCopNo: '',//企业内部编号
          contractorCompanyCode: '', //承揽者名称
          entrustContrNo: '',//委托合同号
          outSendFlag: '',//全工序外发标志
          entrustEmsNo: '',//委托方手册号
          declareDateFrom: '',//申报日期开始
          declareDateTo: '',//申报日期结束
        },
        inDataSource: {
          allPutout: putoutManage.allPutout
        }
      }
    },
    created() {
      this.getBusparter()
      this.getaddentrustEmsNo()
    },
    methods: {
      handleDMDateChange(e) {
        this.searchParams.declareDateFrom = e[0]
        this.searchParams.declareDateTo = e[1]
      }
    },
    watch: {
      id(newVal) {
        if (newVal) {
          this.getentrustEmsNo()
          this.id = false
        }
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
