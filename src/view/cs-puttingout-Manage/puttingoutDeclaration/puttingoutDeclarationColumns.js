import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow,
  'declareDate',
  'emsCopNo',
  'applyNo',
  'applyVaildDate',
  'contractorTradeName',
  'contractorMasterCustoms',
  'contractorDistrictCode',
  'contractorTradeCode',
  'contractorCreditCode',
  'contractorMobilePhone',
  'contractorTelephoneNo',
  'entrustEmsNo',
  'tradeCode',
  'entrustMasterCustoms',
  'entrustCreditCode',
  'entrustTradeName',
  'entrustMobilePhone',
  'entrustContrNo',
  'outSendFlag',
  'outSendDeclareAmount',
  'declareTradeCode',
  'outSendGoods',
  'declareCreditCode',
  'receiveGoods',
  'remark'

]
const excelColumnsConfig = [
  'declareDate',
  'emsCopNo',
  'applyNo',
  'applyVaildDate',
  'contractorTradeName',
  'contractorMasterCustoms',
  'contractorDistrictCode',
  'contractorTradeCode',
  'contractorCreditCode',
  'contractorMobilePhone',
  'contractorTelephoneNo',
  'entrustEmsNo',
  'tradeCode',
  'entrustMasterCustoms',
  'entrustCreditCode',
  'entrustTradeName',
  'entrustMobilePhone',
  'entrustContrNo',
  'outSendFlag',
  'outSendDeclareAmount',
  'declareTradeCode',
  'outSendGoods',
  'declareCreditCode',
  'receiveGoods',
  'remark'
]
const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          title: '申报日期',
          key: 'declareDate'
        },
        {
          width: 120,
          key: 'emsCopNo',
          title: '企业内部编号'
        },
        {
          width: 120,
          key: 'applyNo',
          title: '申报表编号'
        },
        {
          width: 120,
          title: '申报表有效期',
          key: 'applyVaildDate'
        },
        {
          width: 240,
          title: '承揽者名称',
          key: 'contractorTradeName',
          render: (h, params) => {
            return h('span', `${params.row.contractorCompanyCode}  ${params.row.contractorTradeName}`)
          }
        },{
          width: 120,
          title: '承揽者主管海关',
          key: 'contractorMasterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },{
          width: 120,
          title: '承揽者地区代码',
          key: 'contractorDistrictCode',
          render: (h, params) => {
            return h('span', `${params.row.contractorDistrictCode}  ${params.row.contractorDistrictName}`)
          }
        },
        {
          width: 120,
          title: '承揽者海关编码',
          key: 'contractorTradeCode'
        },
        {
          width: 120,
          title: '承揽者社会信用代码',
          key: 'contractorCreditCode'
        },
        {
          width: 150,
          title: '承揽者联系人/电话',
          key: 'contractorMobilePhone'
        },
        {
          width: 150,
          title: '承揽者法人/电话',
          key: 'contractorTelephoneNo'
        },
        {
          width: 120,
          key: 'entrustEmsNo',
          title: '委托方手册账号'
        },
        {
          width: 120,
          key: 'tradeCode',
          title: '委托方海关编码'
        },
        {
          width: 120,
          title: '委托方主管海关',
          key: 'entrustMasterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 120,
          title: '委托方信用代码',
          key: 'entrustCreditCode'
        },
        {
          width: 120,
          title: '委托方企业名称',
          key: 'entrustTradeName'
        },
        {
          width: 120,
          title: '委托方申报人/电话',
          key: 'entrustMobilePhone'
        },
        {
          width: 120,
          title: '委托方合同号',
          key: 'entrustContrNo'
        },
        {
          width: 120,
          key: 'outSendFlag',
          title: '全工序外发标志',
          render: (h, params) => {
            return h('span', this.codeConvertName(params.row.outSendFlag))
          }
        },
        {
          width: 120,
          title: '外发申报金额(人民币)',
          key: 'outSendDeclareAmount'
        },
        {
          width: 120,
          key: 'declareTradeCode',
          title: '申报单位',
          render: (h, params) => {
            return h('span', `${params.row.declareTradeCode}  ${params.row.declareTradeName}`)
          }
        },
        {
          width: 120,
          title: '外发主要货物',
          key: 'outSendGoods'
        },
        {
          width: 120,
          title: '申报单位信用代码',
          key: 'declareCreditCode'
        },
        {
          width: 120,
          title: '收回主要货物',
          key: 'receiveGoods'
        },
        {
          width: 120,
          title: '备注',
          key: 'remark'
        }
      ],
      exportColumns: [
      {
        width: 120,
        title: '申报日期',
        key: 'declareDate'
      },
      {
        width: 120,
        key: 'emsCopNo',
        title: '企业内部编号'
      },
      {
        width: 120,
        key: 'applyNo',
        title: '申报表编号'
      },
      {
        width: 120,
        title: '申报表有效期',
        key: 'applyVaildDate'
      },
      {
        width: 240,
        title: '承揽者名称',
        key: 'contractorTradeName',
        render: (h, params) => {
          return h('span', `${params.row.contractorCompanyCode}  ${params.row.contractorTradeName}`)
        }
      },{
        width: 120,
        title: '承揽者主管海关',
        key: 'contractorMasterCustoms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      },{
        width: 120,
        title: '承揽者地区代码',
        key: 'contractorDistrictCode',
        render: (h, params) => {
          return h('span', `${params.row.contractorDistrictCode}  ${params.row.contractorDistrictName}`)
        }
      },
      {
        width: 120,
        title: '承揽者海关编码',
        key: 'contractorTradeCode'
      },
      {
        width: 120,
        title: '承揽者社会信用代码',
        key: 'contractorCreditCode'
      },
      {
        width: 150,
        title: '承揽者联系人/电话',
        key: 'contractorMobilePhone'
      },
      {
        width: 150,
        title: '承揽者法人/电话',
        key: 'contractorTelephoneNo'
      },
      {
        width: 120,
        key: 'entrustEmsNo',
        title: '委托方手册账号'
      },
      {
        width: 120,
        key: 'tradeCode',
        title: '委托方海关编码'
      },
      {
        width: 120,
        title: '委托方主管海关',
        key: 'entrustMasterCustoms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      },
      {
        width: 120,
        title: '委托方信用代码',
        key: 'entrustCreditCode'
      },
      {
        width: 120,
        title: '委托方企业名称',
        key: 'entrustTradeName'
      },
      {
        width: 120,
        title: '委托方申报人/电话',
        key: 'entrustMobilePhone'
      },
      {
        width: 120,
        title: '委托方合同号',
        key: 'entrustContrNo'
      },
      {
        width: 120,
        key: 'outSendFlag',
        title: '全工序外发标志',
        render: (h, params) => {
          return h('span', this.codeConvertName(params.row.outSendFlag))
        }
      },
      {
        width: 120,
        title: '外发申报金额(人民币)',
        key: 'outSendDeclareAmount'
      },
      {
        width: 120,
        key: 'declareTradeCode',
        title: '申报单位',
        render: (h, params) => {
          return h('span', `${params.row.declareTradeCode}  ${params.row.declareTradeName}`)
        }
      },
      {
        width: 120,
        title: '外发主要货物',
        key: 'outSendGoods'
      },
      {
        width: 120,
        title: '申报单位信用代码',
        key: 'declareCreditCode'
      },
      {
        width: 120,
        title: '收回主要货物',
        key: 'receiveGoods'
      },
      {
        width: 120,
        title: '备注',
        key: 'remark'
      }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
