// import { certificate } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { productClassify } from '@/view/cs-common'

export const takedeHeadListReport = {
  name: 'takedeHeadListReport',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        checkColumnShow: false,           // 显示选择列
        operationColumnShow: false        // 显示操作列
      },
      cmbSource: {},
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  computed: {
    defaultDates() {
      let today = new Date(),
        dateTo = today.toLocaleDateString(),
        dateFrom = new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString()
      return [dateFrom, dateTo]
    }
  },
  methods: {
    /**
     * 首次查询前赋值
     */
    beforeFirstSearch() {
      let me = this
      me.$set(me.searchConfig.model, 'insertTimeFrom', me.defaultDates[0])
      me.$set(me.searchConfig.model, 'insertTimeTo', me.defaultDates[1])
    },
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'billNo',
        title: '收货单编号'
      },
      {
        range: true,
        key: 'insertTime',
        title: '制单日期'
      },
      {
        key: 'applyNo',
        title: '申报表编号'
      },
      {
        key: 'facGNo',
        title: '企业料号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      return [
        {
          width: 120,
          key: 'billNo',
          title: '收货单编号'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'createDate'
        },
        {
          width: 120,
          key: 'receiveDate',
          title: '收货日期'
        },
        {
          width: 120,
          key: 'emsCopNo',
          title: '企业内部编号'
        },
        {
          width: 120,
          key: 'applyNo',
          title: '申报表编号'
        },
        {
          width: 120,
          title: '申报日期',
          key: 'declareDate'
        },
        {
          width: 120,
          title: '承揽者海关编码',
          key: 'contractorTradeCode'
        },
        {
          width: 120,
          title: '承揽者社会信用代码',
          key: 'contractorCreditCode'
        },
        {
          width: 120,
          title: '承揽者名称',
          key: 'contractorTradeName',
          render: (h, params) => {
            return h('span', `${params.row.contractorCompanyCode}  ${params.row.contractorTradeName}`)
          }
        },
        {
          width: 120,
          title: '承揽者主管海关',
          key: 'contractorMasterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 120,
          key: 'entrustEmsNo',
          title: '委托方手册账号'
        },
        {
          width: 120,
          key: 'declareTradeCode',
          title: '委托方海关编码'
        },
        {
          width: 120,
          key: 'entrustCreditCode',
          title: '委托方信用代码'
        },
        {
          width: 120,
          key: 'entrustTradeName',
          title: '委托方企业名称'
        },
        {
          width: 120,
          key: 'entrustMasterCustoms',
          title: '委托方主管海关',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 120,
          key: 'receiver',
          title: '收货申报人'
        },
        {
          width: 120,
          key: 'declareTradeName',
          title: '申报单位',
          render: (h, params) => {
            return h('span', `${params.row.declareTradeCode}  ${params.row.declareTradeName}`)
          }
        },
        {
          width: 120,
          key: 'declareCreditCode',
          title: '申报单位信用代码'
        },
        {
          width: 120,
          title: '备注',
          key: 'remark',
          tooltip: true
        },{
          width: 120,
          key: 'gmark',
          tooltip: true,
          title: '物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, productClassify.GMARK_SELECT_List)
          }
        },{
          width: 120,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          tooltip: true,
          title: '备案料号'
        },
        {
          width: 120,
          key: 'gno',
          title: '备案序号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 120,
          key: 'gname',
          title: '商品名称'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'qty',
          title: '收货数量'
        },
        {
          width: 120,
          key: 'amount',
          title: '收货金额'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          title: '表体备注',
          key: 'listRemark',
          tooltip: true
        }
      ]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}

