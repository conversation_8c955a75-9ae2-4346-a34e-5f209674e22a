import pms from '@/libs/pms'
import { dynamicHeight } from '@/common'

export const commonMethod = {
  mixins: [dynamicHeight, pms],
  data() {
    return {
      actions: [],
      // 分页相关
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1
      },
      tableloading: false,
      gridConfig: {
        data: [],
        selectRows: [],
        gridColumns: [],
        exportColumns: [],
        exportTitle: '导出文件名称'
      },
      pageSizeOpts: [10, 20, 50, 100],
      toolbarEventMap: {
        'export': this.handleDownload
      }
    }
  },
  mounted() {
    let me = this
    me.refreshDynamicHeight(130, !me.showSearch ? ['area_search'] : null)
    me.loadFunctions().then(() => {
      me.actions.push({
        key: '',
        needed: true,
        label: '导出',
        loading: false,
        disabled: false,
        command: 'export',
        icon: 'ios-cloud-download-outline'
      })
    })
  },
  methods: {
    handleShowSearch() {
      let me = this
      me.showSearch = !me.showSearch
      me.refreshDynamicHeight(130, !me.showSearch ? ['area_search'] : null)
    },
    handleDownload() {
      console.log(123)
    }
  }
}
