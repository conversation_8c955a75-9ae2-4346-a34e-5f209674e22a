import { isNullOrEmpty, getKeyValue } from "@/libs/util"
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'erpEmsListNo'
  , '业务实体'
  , '发货业务实体'
  , '发货单号'
  , '发货日期'
  , '仓库'
  , 'apprStatusName'
  , 'erpInsertTime'
  , 'emsListNo'
  , 'listNo'
  , 'declareDate'
  , 'seqNo'
  , 'entryNo'
  , 'entryDeclareDate'
  , 'emsNo'
  , '出货日期'
  , 'invoiceNo'
  , 'contrNo'
  , 'overseasShipperName'
  , 'tradeName'
  , 'tradeMode'
  , 'masterCustoms'
  , 'trafMode'
  , 'forwardCode'
  , 'trafName'
  , 'hawb'
  , 'warehouse'
  , 'gmark'
  , 'wrapTypeName'
  , 'netWtTotal'
  , 'grossWtTotal'
  , 'packNum'
  , 'volumeTotal'
  , 'qtyAll'
  , 'totalAll'
  , 'transMode'
  , 'feeMark'
  , 'feeRate'
  , 'feeCurr'
  , 'insurMark'
  , 'insurRate'
  , 'insurCurr'
  , 'otherMark'
  , 'otherRate'
  , 'otherCurr'
  , 'licenseNo'
  , 'declareName'
  , 'tradeNation'
  , 'tradeTerms'
  , 'tradeCountry'
  , 'destPortName'
  , 'shipTo'
  , 'note'
  , 'erpInsertUser'
  , 'apprUserFull'
  , 'passDate'
  , 'facGNo'
  , 'copGNo'
  , 'gno'
  , 'serialNo'
  , 'entryGNo'
  , 'listGMark'
  , 'listBondMark'
  , 'listEmsNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'copGName'
  , 'copGModel'
  , 'unit'
  , 'qty'
  , 'unit1'
  , 'qty1'
  , 'unit2'
  , 'qty2'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'netWt'
  , 'grossWt'
  , 'volume'
  , 'customerOrderNo'
  , 'customerGNo'
  , 'originCountry'
  , 'destinationCountry'
  , 'listTradeMode'
  , 'orderNo'
  , 'supplierName'
  , 'exgVersion'
  , 'districtCode'
  , 'linkedNo'
  , 'costCenter'
  , 'note1'
  , '原产国/目的国'
  , '表体发票号码'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '提单内部编号',
          width: 160,
          align: 'center',
          key: 'erpEmsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '业务实体',
          width: 160,
          align: 'center',
          key: '业务实体',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '发货业务实体',
          width: 160,
          align: 'center',
          key: '发货业务实体',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '发货单号',
          width: 160,
          align: 'center',
          key: '发货单号',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '发货日期',
          width: 160,
          align: 'center',
          key: '发货日期',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '仓库',
          width: 160,
          align: 'center',
          key: '仓库',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '提单状态',
          key: 'apprStatusName',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.APPR_STATUS_MAP)
          }
        },
        {
          title: '制单日期',
          width: 150,
          align: 'center',
          key: 'erpInsertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '清单内部编号',
          width: 160,
          align: 'center',
          key: 'emsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '核注清单编号',
          width: 160,
          align: 'center',
          key: 'listNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '清单申报日期',
          width: 100,
          align: 'center',
          key: 'declareDate',
          // render: (h, params) => {
          //   return this.dateTimeShowRender(h, params)
          // }
        },
        {
          title: '报关单统一编号',
          width: 150,
          align: 'center',
          key: 'seqNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单号',
          width: 150,
          align: 'center',
          key: 'entryNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单申报日期',
          width: 100,
          align: 'center',
          key: 'entryDeclareDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 140,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '出货日期',
          minWidth: 130,
          align: 'center',
          key: '出货日期',
        },
        {
          title: '发票号',
          minWidth: 120,
          align: 'center',
          key: 'invoiceNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '合同协议号',
          minWidth: 120,
          align: 'center',
          key: 'contrNo'
        },
        {
          title: '境外收货人',
          minWidth: 150,
          align: 'center',
          key: 'overseasShipperName',
        },
        {
          title: '境内发货人',
          key: 'tradeName',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '申报地海关',
          minWidth: 120,
          align: 'center',
          key: 'masterCustoms',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
          }
        },
        {
          title: '货运代理',
          minWidth: 120,
          align: 'center',
          key: 'forwardCode',
        },
        {
          title: '运输方式',
          minWidth: 120,
          align: 'center',
          key: 'trafMode',
          render: (h, params) => {
            return h('span', this.pcodeGet(this.pcode.transf, params.row.trafMode))
          }
        },
        {
          title: '运输工具及航次',
          minWidth: 120,
          align: 'center',
          key: 'trafName',
        },
        {
          title: '提运单号',
          minWidth: 150,
          align: 'center',
          key: 'hawb'
        },
        {
          title: '货物存放地点',
          minWidth: 200,
          align: 'center',
          key: 'warehouse',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '物料类型',
          minWidth: 120,
          align: 'center',
          key: 'gmark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          title: '包装种类',
          key: 'wrapTypeName',
          minWidth: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总净重',
          key: 'netWtTotal',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总毛重',
          key: 'grossWtTotal',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总件数',
          key: 'packNum',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总体积',
          width: 160,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'volumeTotal'
        },
        {
          title: '总数量',
          width: 160,
          align: 'center',
          key: 'qtyAll'
        },
        {
          title: '总金额',
          width: 160,
          align: 'center',
          key: 'totalAll'
        },
        {
          title: '成交方式',
          key: 'transMode',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        },
        {
          title: '运费-类型',
          key: 'feeMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
          }
        },
        {
          title: '运费-费率',
          key: 'feeRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费-币制',
          key: 'feeCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '保费-类型',
          key: 'insurMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
          }
        },
        {
          title: '保费-费率',
          key: 'insurRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '保费-币制',
          key: 'insurCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '杂费-类型',
          key: 'otherMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
          }
        },
        {
          title: '杂费-费率',
          key: 'otherRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '杂费-币制',
          key: 'otherCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '许可证号',
          key: 'licenseNo',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '报关行',
          key: 'declareName',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'center',
        },
        {
          title: '贸易国(地区)',
          key: 'tradeNation',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '贸易条款',
          key: 'tradeTerms',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', this.getTrade(params.row.tradeTerms))
          }
        },
        {
          title: '集装箱类型',
          key: 'containerType',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.containerType)
          }
        },
        {
          title: '集装箱数量',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          key: 'containerNum'
        },
        {
          title: '抵运国',
          key: 'tradeCountry',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '指运港',
          minWidth: 100,
          align: 'center',
          key: 'destPortName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: 'shipTo',
          minWidth: 100,
          align: 'center',
          key: 'shipTo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '备注',
          key: 'note',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '制单员',
          key: 'erpInsertUser',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审员',
          key: 'apprUserFull',
          align: 'left',
          ellipsis: true,
          tooltip: true,
          width: 100
        },
        {
          title: '原产国/目的国',
          minWidth: 120,
          align: 'center',
          key: '原产国/目的国',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
          }
        },
        {
          title: '放行日期',
          minWidth: 120,
          align: 'center',
          key: 'passDate'
        },
        {
          title: '企业料号',
          minWidth: 150,
          align: 'center',
          key: 'facGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备案料号',
          minWidth: 150,
          align: 'center',
          key: 'copGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备案序号',
          width: 120,
          align: 'center',
          key: 'gno'
        },
        {
          title: '清单商品序号',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'serialNo'
        },
        {
          title: '报关单商品序号',
          key: 'entryGNo',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '表体物料类型',
          key: 'listGMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.G_MARK_STATUS_MAP)
          },
          align: 'center',
          width: 100
        },
        {
          title: '表体保完税标记',
          minWidth: 120,
          align: 'center',
          key: 'listBondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          title: '表体备案号',
          minWidth: 120,
          align: 'center',
          key: 'listEmsNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '商品编码',
          key: 'codeTS',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'gname',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'gmodel',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '中文名称',
          minWidth: 120,
          align: 'center',
          key: 'copGName',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '料号申报要素',
          minWidth: 120,
          align: 'center',
          key: 'copGModel',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '计量单位',
          key: 'unit',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '申报数量',
          key: 'qty',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法一单位',
          key: 'unit1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '法一数量',
          key: 'qty1',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法二单位',
          key: 'unit2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '法二数量',
          key: 'qty2',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报单价',
          key: 'decPrice',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报总价',
          key: 'decTotal',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '净重',
          minWidth: 120,
          align: 'center',
          key: 'netWt',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '毛重',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'grossWt'
        },
        {
          title: '体积',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'volume'
        },
        {
          title: '客户订单号',
          minWidth: 120,
          align: 'center',
          key: 'customerOrderNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '客户料号',
          minWidth: 120,
          align: 'center',
          key: 'customerGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '表体原产国',
          minWidth: 120,
          align: 'center',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '表体目的国',
          minWidth: 120,
          align: 'center',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '表体监管方式',
          key: 'listTradeMode',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '销售订单号',
          minWidth: 120,
          align: 'center',
          key: 'orderNo',
        },
        {
          title: '表体发票号码',
          minWidth: 120,
          align: 'center',
          key: '表体发票号码',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '客户',
          minWidth: 120,
          align: 'center',
          key: 'supplierName',
        },
        {
          title: '单耗版本号',
          minWidth: 120,
          align: 'center',
          key: 'exgVersion',
        },
        {
          title: '境内货源地',
          key: 'districtCode',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          title: '提取单号',
          minWidth: 120,
          align: 'center',
          key: 'linkedNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '成本中心',
          minWidth: 120,
          align: 'center',
          key: 'costCenter',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备注',
          key: 'note1',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    },
    getTrade(val) {
      let me = this
      let tradeData = ''
      let tradedata = [
        {value: "1", label: "CIF"},
        {value: "2", label: "C&F"},
        {value: "3", label: "FOB"},
        {value: "4", label: "C&I"},
        {value: "5", label: "市场价"},
        {value: "6", label: "垫仓"},
        {value: "7", label: "EXW"}
      ]
      let data = [...tradedata, ...me.importExportManage.tradeTermList]
      data.forEach(item => {
        if (item.value === val) {
          tradeData = item.label
        }
      })
      return tradeData
    }
  }
}

export {
  commColumns,
  columns
}
