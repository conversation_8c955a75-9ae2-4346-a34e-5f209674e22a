import { isNullOrEmpty, getKeyValue } from "@/libs/util"
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'erpEmsListNo'
  ,'业务实体'
  ,'入库实体'
  ,'仓库编码'
  ,'仓库描述'
  ,'入库日期'
  ,'入库单号'
  ,'apprStatusName'
  ,'erpInsertTime'
  ,'emsListNo'
  ,'listNo'
  ,'declareDate'
  ,'qty'
  ,'unit1'
  ,'qty1'
  ,'unit2'
  ,'qty2'
  ,'curr'
  ,'netWt'
  ,'grossWt'
  ,'volume'
  ,'originCountry'
  ,'destinationCountry'
  ,'tradeMode'
  ,'orderNo'
  ,'invoiceNo'
  ,'supplierName'
  ,'exgVersion'
  ,'districtCode'
  ,'linkedNo'
  ,'costCenter'
  ,'note'
  ,'decPrice'
  ,'decTotal'
  ,'buyer'
]

const columns = {
  mixins:[baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '提单内部编号',
          width: 160,
          align: 'center',
          key: 'erpEmsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '业务实体',
          width: 160,
          align: 'center',
          key: '业务实体',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '入库实体',
          width: 160,
          align: 'center',
          key: '入库实体',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '仓库编码',
          width: 160,
          align: 'center',
          key: '仓库编码',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '仓库描述',
          width: 160,
          align: 'center',
          key: '仓库描述',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '入库日期',
          width: 160,
          align: 'center',
          key: '入库日期',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '入库单号',
          width: 160,
          align: 'center',
          key: '入库单号',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '提单状态',
          key: 'apprStatusName',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.APPR_STATUS_MAP)
          }
        },
        {
          title: '制单日期',
          width: 150,
          align: 'center',
          key: 'erpInsertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          title: '清单内部编号',
          width: 160,
          align: 'center',
          key: 'emsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '核注清单编号',
          width: 160,
          align: 'center',
          key: 'listNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '清单申报日期',
          width: 100,
          align: 'center',
          key: 'declareDate',
          // render: (h, params) => {
          //   return this.dateTimeShowRender(h, params)
          // }
        },
        {
          title: '申报数量',
          width: 100,
          align: 'center',
          key: 'qty',
        },
        {
          title: '法一单位',
          key: 'unit1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit1), params.row.unit1))
          }
        },
        {
          title: '法一数量',
          key: 'qty1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法二单位',
          key: 'unit2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit2), params.row.unit2))
          }
        },
        {
          title: '法二数量',
          key: 'qty2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报单价',
          key: 'decPrice',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报总价',
          key: 'decTotal',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '净重',
          key: 'netWt',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '毛重',
          key: 'grossWt',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '体积',
          key: 'volume',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '采购人员',
          key: 'buyer',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '原产国',
          minWidth: 120,
          align: 'center',
          key: 'originCountry',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
          }
        },
        {
          title: '目的国',
          minWidth: 120,
          align: 'center',
          key: 'destinationCountry',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.destinationCountry), params.row.destinationCountry))
          }
        },
        {
          title: '表体监管方式',
          key: 'tradeMode',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '采购订单号',
          minWidth: 120,
          align: 'center',
          key: 'orderNo',
        },
        {
          title: '表体发票号码',
          minWidth: 120,
          align: 'center',
          key: 'invoiceNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '供应商',
          minWidth: 120,
          align: 'center',
          key: 'supplierName',
        },
        {
          title: '单耗版本号',
          minWidth: 120,
          align: 'center',
          key: 'exgVersion',
        },
        {
          title: '境内目的地',
          key: 'districtCode',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          title: '提取单号',
          minWidth: 120,
          align: 'center',
          key: 'linkedNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '成本中心',
          minWidth: 120,
          align: 'center',
          key: 'costCenter',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备注',
          key: 'note',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ]
    }
  },
  methods: {
    keyValueRender (h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

export {
  commColumns,
  columns
}
