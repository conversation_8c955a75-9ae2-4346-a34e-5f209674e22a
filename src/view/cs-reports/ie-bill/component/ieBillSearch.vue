<template>
  <section>
    <Form ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleErpInsertTimeChange"></dc-dateRange>
    </Form>
  </section>
</template>

<script>
  export default {
    name: 'ieBillSearch',
    data() {
      return {
        searchParam: {
          erpInsertTimeFrom: '',
          erpInsertTimeTo: ''
        }
      }
    },
    methods: {
      handleErpInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "erpInsertTimeFrom", values[0])
          this.$set(this.searchParam, "erpInsertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "erpInsertTimeFrom", '')
          this.$set(this.searchParam, "erpInsertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
