<template>
  <section>
    <Card :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <Button type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</Button>
          <Button type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</Button>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <IeBillSearch ref="headSearch"></IeBillSearch>
        </div>
      </div>
    </Card>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <Card :bordered="false">
      <xdo-ag-grid class="dc-table" ref="table" :loading="tableloading" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                   :data="gridConfig.data"></xdo-ag-grid>
      <div ref="area_page">
        <Page class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
              :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
              @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </Card>
  </section>
</template>

<script>
  import { getColumnsByConfig } from '@/common'
  import { commonMethod } from './common/common'
  import { exportdata } from './component/exportData'
  import IeBillSearch from './component/ieBillSearch'
  import { columns, commColumns } from './component/exportBillColumns'
  import { importExportManage, productClassify, interimVerification, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'exportBill',
    components: {IeBillSearch},
    mixins: [commonMethod, columns],
    data() {
      return {
        showSearch: false,
        productClassify: productClassify,
        erpInterfaceData: erpInterfaceData,
        importExportManage: importExportManage,
        interimVerification: interimVerification
      }
    },
    mounted() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, commColumns)
      me.gridConfig.data = exportdata
      me.pageParam.dataTotal = exportdata.length
    }
  }
</script>

<style scoped>
</style>
