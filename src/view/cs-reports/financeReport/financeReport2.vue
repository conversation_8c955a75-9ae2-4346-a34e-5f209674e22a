<template>
  <section>
    <div v-show="showHead">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <financeReportSearch ref="headSearch"></financeReportSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                       style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </XdoButton>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import { dynamicHeight } from '@/common'
  import { csAPI, excelExport } from '@/api'
  import { pageParam } from '../../cs-common'
  import financeReportSearch from './financeReportSearch2'
  import { columns, excelColumns } from './financeReportColumns'

  export default {
    name: 'financeReport2',
    mixins: [dynamicHeight, columns, excelColumns],
    components: {
      financeReportSearch
    },
    data() {
      return {
        showHead: true,
        showSearch: false,
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          gridColumns: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1,
          pageSizeOpts: [10, 20, 50, 100]
        },
        actions: [{
          type: 'text',
          needed: true,
          label: '导出',
          loading: false,
          disabled: false,
          key: 'xdo-btn-export',
          click: this.handleDownload,
          icon: 'ios-cloud-download-outline'
        }]
      }
    },
    mounted() {
      let me = this
      me.totalColumns.push(...me.totalColumns2)
      me.gridConfig.gridColumns = me.totalColumns
      me.handleSearchSubmit()
      me.refreshDynamicHeight(122, !me.showSearch ? ['area_search'] : null)
    },
    methods: {
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(122, !me.showSearch ? ['area_search'] : null)
      },
      getList() {
        let me = this
        pageParam.page = me.pageParam.page
        pageParam.limit = me.pageParam.limit
        const data = me.$refs.headSearch.searchParam
        me.$http.post(csAPI.reportCenter.financeReport.list2, data, {params: pageParam}).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
        }).catch(() => {
        })
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        if (me.pageParam.page === 1) {
          me.getList()
        }
      },
      handleDownload() {
        let me = this
        me.actions[0].loading = true
        me.totalExcelColumns.push(...me.totalExcelColumns2)
        excelExport(csAPI.reportCenter.financeReport.export2, {
          name: '财务报表2',
          header: me.totalExcelColumns,
          exportColumns: Object.assign({}, me.$refs.headSearch.searchParam)
        }).finally(() => {
          me.actions[0].loading = false
        })
      }
    }
  }
</script>
