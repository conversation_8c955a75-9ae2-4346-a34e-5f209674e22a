<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="linkedNo" label="关联编号">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号码">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="发票日期" @onDateRangeChanged="handleInvoiceDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'financeReportSearch1',
    data() {
      return {
        searchParam: {
          linkedNo: '',
          invoiceNo: '',
          invoiceDateFrom: '',
          invoiceDateTo: ''
        }
      }
    },
    methods: {
      handleInvoiceDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "invoiceDateFrom", values[0])
          this.$set(this.searchParam, "invoiceDateTo", values[1])
        } else {
          this.$set(this.searchParam, "invoiceDateFrom", '')
          this.$set(this.searchParam, "invoiceDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
