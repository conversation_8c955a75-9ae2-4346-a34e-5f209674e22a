import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const columnsConfig = [
  'entryNo'
  , 'linkedNo'
  , 'invoiceNo'
  , 'invoiceDate'
  , 'decTotal'
  , 'curr'
  , 'customerGNo'
  , 'codeTS'
  , 'tradeMode'
  , 'insertTime'
  , 'buyAddress'
  , 'customerName'
]

const excelColumnsConfig = [
  ...columnsConfig
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '关联编号',
          key: 'linkedNo',
          align: 'center'
        },
        {
          title: '发票号码',
          key: 'invoiceNo',
          align: 'center'
        },
        {
          title: '发票日期',
          key: 'invoiceDate',
          align: 'center'
        },
        {
          title: '申报总价',
          align: 'center',
          key: 'decTotal'
        },
        {
          title: '币制',
          key: 'curr',
          align: 'center',
          render: (h, params) => {
            return h('span', this.pcodeGet(this.pcode.curr_outdated, params.row.curr))
          }
        }
      ],
      totalColumns1: [
        {
          title: '客户料号',
          key: 'customerGNo',
          align: 'center'
        },
        {
          title: '商品编码',
          key: 'codeTS',
          align: 'center'
        }
      ],
      totalColumns2: [
        {
          title: '报关单号',
          key: 'entryNo',
          align: 'center'
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '制单日期',
          key: 'insertTime',
          align: 'center'
        },
        {
          title: '买方地址',
          key: 'buyAddress',
          align: 'center'
        },
        {
          title: '客户名称',
          key: 'customerName',
          align: 'center'
        }
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

const excelColumns = {
  mixins: [baseColumns],
  data() {
    return {
      totalExcelColumns: [
        {
          value: '报关单号',
          key: 'entryNo'
        },
        {
          value: '关联编号',
          key: ' linkedNo'
        },
        {
          value: '发票号码',
          key: ' invoiceNo'
        },
        {
          value: '发票日期',
          key: 'invoiceDate'
        },
        {
          value: '申报总价',
          key: 'decTotal'
        },
        {
          value: '币制',
          key: ' curr'
        }
      ],
      totalExcelColumns1: [
        {
          value: '客户料号',
          key: 'customerGNo'
        },
        {
          value: '商品编码',
          key: 'codeTS'
        }
      ],
      totalExcelColumns2: [
        {
          value: '监管方式',
          key: 'tradeMode'
        },
        {
          value: '制单日期',
          key: 'insertTime'
        },
        {
          value: '买方地址',
          key: 'buyAddress'
        },
        {
          value: '客户名称',
          key: 'customerName'
        }
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
