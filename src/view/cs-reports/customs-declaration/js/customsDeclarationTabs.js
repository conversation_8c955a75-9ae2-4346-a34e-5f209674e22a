import { csAPI } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { importExportManage } from '@/view/cs-common'
import HeadReport from '../components/customs-declaration-head-list'
import ComplexReport from '../components/customs-declaration-all-list'

export const customsDeclarationTabs = {
  name: 'customsDeclarationTabs',
  components: {
    HeadReport,
    ComplexReport
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        complexTab: false
      },
      cmbSource: {
        emsNo: [],
        overseasShipper: [],
        feeMarkList: importExportManage.feeTypeMap
      }
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
    }
  },
  created: function () {
    let me = this
    if (me.bondMark === '0') {
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        me.$set(me.cmbSource, 'emsNo', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'emsNo', [])
      })
    }
    // 境外收(发)货人
    let overseasShipperUrl = ''
    if (me.ieMark === 'I') {
      overseasShipperUrl = csAPI.ieParams.PRD
    } else {
      overseasShipperUrl = csAPI.ieParams.CLI
    }
    me.$http.post(overseasShipperUrl).then(res => {
      me.$set(me.cmbSource, 'overseasShipper', ArrayToLocaleLowerCase(res.data.data))
    }).catch(() => {
      me.$set(me.cmbSource, 'overseasShipper', [])
    })
  }
}
