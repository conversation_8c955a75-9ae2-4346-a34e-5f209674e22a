import BillErrMsg from '@/view/cs-common/components/bill-err-msg'
import { customsDeclarationHeadList } from './customsDeclarationHeadList'

export const customsDeclarationAllList = {
  name: 'customsDeclarationAllList',
  mixins: [customsDeclarationHeadList],
  components: {
    BillErrMsg
  },
  data() {
    let headParams = this.getHeadParams()
    let headFields = this.getHeadFields()
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...headParams,
        ...params
      ],
      baseFields: [
        ...headFields,
        ...fields
      ],
      pmsLevel: 'headList'
    }
  },
  methods: {
    getParams() {
      let me = this,
        params = [{
          key: 'gname',
          title: '商品名称'
        }, {
          key: 'codeTS',
          title: '商品编码'
        }, {
          key: 'gmodel',
          title: '申报规格型号'
        }]
      if (me.ieMark === 'I') {
        params.push({
          key: 'originCountry',
          title: '原产国',
          type: 'pcode',
          props: {
            meta: 'COUNTRY_OUTDATED' // this.pcode.country_outdated
          }
        })
      } else {
        params.push({
          key: 'destinationCountry',
          title: '目的国',
          type: 'pcode',
          props: {
            meta: 'COUNTRY_OUTDATED' // this.pcode.country_outdated
          }
        })
      }
      /*params.push({
        key: 'exgVersion',
        title: '单耗版本号'
      })*/
      return params
    },
    getFields() {
      let resultFields = [{
          width: 120,
          tooltip: true,
          key: 'serialNo',
          title: '报关单商品序号'
        }, {
          width: 100,
          tooltip: true,
          key: 'codeTS',
          title: '商品编码'
        }, {
          width: 150,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        }, {
          width: 160,
          tooltip: true,
          key: 'gmodel',
          title: '规格型号'
        }, {
          width: 150,
          tooltip: true,
          title: '原产国(地区)',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }, {
          width: 150,
          tooltip: true,
          title: '最终目的国(地区)',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }, {
          width: 150,
          key: 'curr',
          title: '币制',
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        }, {
          width: 150,
          tooltip: true,
          key: 'decPrice',
          title: '申报单价'
        }, {
          width: 130,
          key: 'qty',
          tooltip: true,
          title: '申报数量'
        }, {
          width: 120,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        }, {
          width: 150,
          tooltip: true,
          key: 'decTotal',
          title: '申报总价'
        }, {
          width: 130,
          key: 'qty1',
          tooltip: true,
          title: '法一数量'
        }, {
          width: 120,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        }, {
          width: 130,
          key: 'qty2',
          tooltip: true,
          title: '法二数量'
        }, {
          width: 120,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        }, {
          width: 120,
          tooltip: true,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        }
        /*, {
          width: 100,
          tooltip: true,
          key: 'exgVersion',
          title: '单耗版本号'
        }*/
        ]
      //if (me.ieMark === 'I') {
        resultFields.push({
          width: 200,
          tooltip: true,
          title: '境内目的地',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        }, {
          width: 200,
          tooltip: true,
          title: '境内目的地(行政)',
          key: 'districtPostCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        })
      //}
      resultFields.push({
        width: 150,
        key: 'sendApiStatus',
        title: '报关单发送状态',
        render: (h, params) => {
          if (params.row.sendApiStatus === '-1') {
            return h('span', {
              style: {
                fontWeight: 'bold'
              }
            }, '未发送')
          } else if (params.row.sendApiStatus === '0') {
            return h('Button', {
              props: {
                type: 'error'
              },
              style: {
                fontWeight: 'bold'
              },
              on: {
                click: () => {
                  this.showErrMsg(params.row)
                }
              }
            }, '发送失败')
          } else if (params.row.sendApiStatus === '1') {
            return h('span', {
              style: {
                color: '#19BE6B',
                fontWeight: 'bold'
              }
            }, '发送成功')
          } else {
            return h('span', '')
          }
        }
      })
      return resultFields
    }
  }
}
