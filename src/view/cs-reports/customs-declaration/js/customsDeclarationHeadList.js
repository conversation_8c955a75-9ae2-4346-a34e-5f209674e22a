import { isNullOrEmpty } from '@/libs/util'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
import { importExportManage, interimVerification } from '@/view/cs-common'

export const customsDeclarationHeadList = {
  name: 'customsDeclarationHeadList',
  mixins: [baseSearchConfig, baseListConfig, dynamicExport],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1'].includes(value)
      }
    },
    commSource: {
      type: Object,
      default: () => ({
        emsNo: [],
        overseasShipper: []
      })
    }
  },
  data() {
    let params = this.getHeadParams()
    let fields = this.getHeadFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      pmsLevel: 'head',
      hasChildTabs: true,
      toolbarEventMap: {
        'export': this.handleDownload,
        'statistics': this.handleStatistics,
        'setting': this.handleTableColumnSetup
      },
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      cmbSource: {
        entryStatus: importExportManage.entryStatus,
        billStatus:importExportManage.BILL_STATUS_MAP
      },
      interimVerification: interimVerification,
      importExportManage: importExportManage,
      reportStatus: interimVerification.REPORT_STATUS_MAP
    }
  },
  watch: {
    'dynamicSource.emsNo': {
      deep: true,
      handler: function () {
        let me = this,
          param = me.searchConfig.fields.find(p => p.key === 'emsNo')
        if (param) {
          me.fieldOptimization(param)
        }
      }
    }
  },
  computed: {
    extendParams() {
      return {
        iemark: this.ieMark,
        bondMark: this.bondMark
      }
    },
    /**
     * 动态数据源
     * @returns {*}
     */
    dynamicSource() {
      return {
        ...this.cmbSource,
        ...this.commSource
      }
    },
    entryPortLabel() {
      if (this.ieMark === 'I') {
        return '进口口岸'//'入境口岸'
      } else if (this.ieMark === 'E') {
        return '出口口岸'//'离境口岸'
      } else {
        return '进/出口口岸'//'离/入境口岸'
      }
    },
    iEPortLabel() {
      if (this.ieMark === 'I') {
        return '进境关别'
      } else if (this.ieMark === 'E') {
        return '出境关别'
      } else {
        return '进/出境关别'
      }
    },
    overseasShipperLabel() {
      if (this.ieMark === 'I') {
        return '境外发货人'
      } else if (this.ieMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收(发)货人'
      }
    },
    tradeCountryLabel() {
      if (this.ieMark === 'I') {
        return '启运国(地区)'
      } else if (this.ieMark === 'E') {
        return '运抵国(地区)'
      } else {
        return '启运(运抵)国(地区)'
      }
    },
    inOutNoLabel() {
      if (this.ieMark === 'I') {
        return '入库单据号'
      } else if (this.ieMark === 'E') {
        return '出库单据号'
      } else {
        return '出/入库单据号'
      }
    },
    tradeNameLabel() {
      if (this.ieMark === 'I') {
        return '境内收货人'
      } else if (this.ieMark === 'E') {
        return '境内发货人'
      } else {
        return '境内收/发货人'
      }
    },
    districtCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内目的地'
      } else if (this.ieMark === 'E') {
        return '境内货源地'
      } else {
        return '境内目的(货源)地'
      }
    },
    districtPostCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内目的地(行政)'
      } else if (this.ieMark === 'E') {
        return '境内货源地(行政)'
      } else {
        return '境内目的(货源)地(行政)'
      }
    },
    inviteDateLabel() {
      if (this.ieMark === 'I') {
        return '进口申请日期'
      } else if (this.ieMark === 'E') {
        return '出口申请日期'
      } else {
        return '进出口申请日期'
      }
    },
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {
        ieport: this.iEPortLabel,
        inOutNo: this.inOutNoLabel,
        tradeName: this.tradeNameLabel,
        entryPort: this.entryPortLabel,
        inviteDate: this.inviteDateLabel,
        tradeCountry: this.tradeCountryLabel,
        districtCode: this.districtCodeLabel,
        overseasShipper: this.overseasShipperLabel,
        districtPostCode: this.districtPostCodeLabel
      }
    },
    ieDefaultDates() {
      let today = new Date(),
        dateTo = today.toLocaleDateString(),
        dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
      return [dateFrom, dateTo]
    },
    jobStatusName() {
      let me = this,
        theStatus = me.reportStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }



  },
  methods: {
    beforeFirstSearch() {
      let me = this
      me.$set(me.searchConfig.model, 'insertTimeFrom', me.ieDefaultDates[0])
      me.$set(me.searchConfig.model, 'insertTimeTo', me.ieDefaultDates[1])
    },
    actionLoaded() {
      let me = this
      me.actions = []
      // if (me.ieMark === 'E') {
        me.actions.push({
          ...me.actionsComm,
          needed: true,
          loading: false,
          disabled: false,
          label: '数据统计',
          command: 'statistics',
          key: 'xdo-btn-delete',
          icon: 'ios-calculator-outline'
        })
      // }

      me.actions.push({
        ...me.actionsComm,
        label: "导出",
        command: "export",
        key: 'xdo-btn-download',
        icon: "ios-cloud-download-outline"
      })
      me.actions.push({
        ...me.actionsComm,
        icon: "ios-cog",
        label: "自定义配置",
        command: "setting",
        key: 'xdo-btn-setting'
      })
    },
    getHeadParams() {
      let me = this,
        params = [{
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          range: true,
          title: '制单日期',
          key: 'insertTime'
        }, {
          key: 'hawb',
          title: '提运单号'
        }, {
          key: 'trafMode',
          title: '运输方式',
          type: 'pcode',
          props: {
            meta: 'TRANSF' // this.pcode.transf
          }
        }, {
          key: 'grossWt',
          title: '总毛重',
          type: 'xdoInput',
          props: {
            intDigits: 11,
            precision: 5
          }
        }, {
          key: 'packNum',
          title: '件数',
          type: 'xdoInput',
          props: {
            intDigits: 11,
            precision: 0
          }
        }, {
          key: 'tradeMode',
          title: '监管方式',
          type: 'pcode',
          props: {
            meta: 'TRADE' // this.pcode.trade
          }
        }, {
          key: 'entryPort',
          title: '进出口口岸',
          type: 'pcode',
          props: {
            meta: 'CUSTOMS_REL' // this.pcode.customs_rel
          }
        }, {
          key: 'entryInsertUser',
          title: '制单员'
        }, {
          range: true,
          title: '报关单申报日期',
          key: 'ddate'
        }]
      if (me.ieMark==='I'){
        params.push({
            range: true,
            title: '进口日期',
            key: 'iedate'
        })
      }else {
        params.push({
          range: true,
          title: '出口日期',
          key: 'iedate'
        })
      }
      if (me.bondMark === '0') {
        params.push({
          key: 'emsNo',
          title: '备案号',
          type: 'select',
          props: {
            optionLabelRender: (opt) => opt.label
          }
        }, {
          range: true,
          title: '清单申报日期',
          key: 'billDeclareDate'
        },{
          key: 'billStatus',
          title: '清单状态',
          type: 'select',
        })
        // params.push({
        //   key: 'gno',
        //   title: '备案序号',
        //   type: 'xdoInput',
        //   props: {
        //     intDigits: 10,
        //     precision: 0
        //   }
        // })
      }
      params.push({
        key: 'entryNo',
        title: '报关单号'
      })
      params.push({
        key: 'transMode',
        title: '成交方式',
        type: 'pcode',
        props: {
          meta: 'TRANSAC' // this.pcode.transac
        }
      })
      params.push({
        key: 'entryStatus',
        title: '报关单状态',
        type: 'select',
      })
      return params
    },
    getHeadFields() {
      let me = this,
        columns = [{
          width: 150,
          key: 'entryNo',
          title: '报关单号'
        }, {
          width: 150,
          key: 'emsListNo',
          title: '清单内部编号'
        }, {
          width: 120,
          key: 'listNo',
          title: '核注清单编号'
        }, {
          title: '提单状态',
          key: 'apprStatusName',
          width: 120,
          tooltip: true,
          render: (h, params) => {
            return h('span', `${params.row.apprStatus}  ${params.row.apprStatusName}`)
          }
        }, {
          width: 150,
          key: 'erpEmsListNo',
          title: '提单内部编号'
        }, {
          width: 160,
          title: '制单日期',
          key: 'erpInsertTime'
        }, {
          width: 150,
          key: 'billDeclareDate',
          title: '清单申报日期'
        }, {
          width: 120,
          key: 'entryStatus',
          title: '报关单状态',
          render: (h, params) => {
            if (isNullOrEmpty(params.row.entryStatus) || isNullOrEmpty(params.row['entryStatusName'])) {
              return ''
            } else {
              return h('span', `${params.row.entryStatus}  ${params.row['entryStatusName']}`)
            }
          }
        }, {
          width: 120,
          key: 'seqNo',
          title: '报关单统一编号'
        }, {
          width: 120,
          key: 'ddate',
          title: '报关单申报日期'
        }, {
          width: 140,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
          // }, {
          //   width: 120,
          //   key: 'gmark',
          //   title: '物料类型'
        }, {
          width: 130,
          tooltip: true,
          key: 'tradeMode',
          title: '监管方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        }, {
          width: 110,
          tooltip: true,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        }, {
          width: 120,
          key: 'trafName',
          title: '运输工具及航次'
        }, {
          width: 120,
          key: 'hawb',
          title: '提运单号'
        }, {
          width: 100,
          tooltip: true,
          key: 'transMode',
          title: '成交方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        }, {
          width: 200,
          tooltip: true,
          title: '申报单位',
          key: 'declareName'
        }, {
          width: 150,
          title: '社会信用代码',
          key: 'declareCreditCode'
        }, {
          width: 130,
          tooltip: true,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        }, {
          width: 250,
          tooltip: true,
          title: '境外收货人',
          key: 'overseasShipper',
          render: (h, params) => {
            // return this.cmbShowRender(h, params, this.dynamicSource.overseasShipper)
            return this.keyValueRender(h, params, 'overseasShipperName')
          }
        }, {
          width: 100,
          tooltip: true,
          key: 'tradeCountry',
          title: '运抵国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }, {
          width: 130,
          key: 'ieport',
          tooltip: true,
          title: '出境关别',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        }, {
          width: 120,
          tooltip: true,
          key: 'contrNo',
          title: '合同协议号'
        }, {
          width: 100,
          tooltip: true,
          title: '许可证号',
          key: 'licenseNo'
        }, {
          width: 120,
          key: 'relEmsNo',
          title: '关联备案号'
        }, {
          width: 120,
          key: 'relListNo',
          title: '关联清单编号'
        }, {
          width: 100,
          tooltip: true,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        }, {
          width: 100,
          tooltip: true,
          key: 'wrapType2',
          title: '其他包装',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        }, {
          width: 80,
          title: '件数',
          tooltip: true,
          key: 'packNum'
        }, {
          width: 80,
          key: 'netWt',
          tooltip: true,
          title: '总净重'
        }, {
          width: 80,
          tooltip: true,
          title: '总毛重',
          key: 'grossWt'
        }, {
          width: 80,
          key: 'qtyAll',
          tooltip: true,
          title: '总数量'
        }, {
          width: 80,
          tooltip: true,
          title: '总金额',
          key: 'totalAll'
        }, {
          title: '运费-类型',
          key: 'feeMark',
          width: 80,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.feeMarkList)
          }
        }, {
          title: '运费-费率',
          key: 'feeRate',
          width: 80,
          tooltip: true
        }, {
          title: '运费-币制',
          key: 'feeCurr',
          width: 150,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        }, {
          title: '保费-类型',
          key: 'insurMark',
          width: 80,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.feeMarkList)
          }
        }, {
          title: '保费-费率',
          key: 'insurRate',
          width: 80,
          tooltip: true,
        }, {
          title: '保费-币制',
          key: 'insurCurr',
          width: 150,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        }, {
          title: '杂费-类型',
          key: 'otherMark',
          width: 80,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.dynamicSource.feeMarkList)
          }
        }, {
          title: '杂费-费率',
          key: 'otherRate',
          width: 80,
          tooltip: true
        }, {
          title: '杂费-币制',
          key: 'otherCurr',
          width: 150,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        }, {
          width: 180,
          key: 'note',
          tooltip: true,
          title: '表头备注'
        }, {
          title: '邀请日期',
          key: 'inviteDate',
          width: 90,
          tooltip: true,
        }, {
          title: '贸易条款',
          key: 'tradeTerms',
          width: 90,
          tooltip: true,
        }, {
          title: '发票号',
          key: 'invoiceNo',
          width: 90,
          tooltip: true,
        }, {
          title: '承运人',
          key: 'carrier',
          width: 90,
          tooltip: true,
        }, {
          title: '计费重量',
          key: 'cweight',
          width: 90,
          tooltip: true,
        }]
      if (me.bondMark === '0') {
        columns.push({
          title: '清单状态',
          key: 'billStatus',
          width: 120,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.BILL_STATUS_MAP)
          }
        })
      }
      if (me.ieMark === 'I') {
        columns.push({
          width: 150,
          tooltip: true,
          key: 'noticeDate',
          title: '到货通知日期'
        }, {
          width: 110,
          tooltip: true,
          title: '车牌号',
          key: 'plateNum'
        }, {
          width: 150,
          title: '送货人',
          tooltip: true,
          key: 'deliveryPerson'
        }, {
          width: 130,
          tooltip: true,
          title: '送货单号',
          key: 'deliveryNo'
        }, {
          width: 120,
          tooltip: true,
          title: '到厂日期',
          key: 'deliveryDate'
        }, {
          width: 90,
          tooltip: true,
          title: '破损标记',
          key: 'damageMark'
        }, {
          width: 120,
          tooltip: true,
          title: '物流费用',
          key: 'customsFee'
        }, {
          width: 120,
          tooltip: true,
          title: '报关费用',
          key: 'logisticsFee'
        },{
          width: 150,
          key: 'iedate',
          title: '进口日期'
        })
      } else {
        columns.push({
          width: 120,
          tooltip: true,
          title: '委托日期',
          key: 'entrustDate'
        }, {
          width: 150,
          tooltip: true,
          title: '委托人',
          key: 'entrustPerson'
        },{
          width: 150,
          key: 'iedate',
          title: '出口日期'
        })
      }
      columns.push({
        width: 120,
        tooltip: true,
        title: '海关查验日期',
        key: 'customsCheckDate'
      }, {
        width: 180,
        tooltip: true,
        title: '海关查验原因',
        key: 'customsCheckReason'
      }, {
        width: 180,
        tooltip: true,
        title: '海关查验结果',
        key: 'customsCheckResult'
      }, {
        width: 120,
        tooltip: true,
        key: 'dutyDate',
        title: '完税日期'
      }, {
        width: 110,
        tooltip: true,
        key: 'dutyType',
        title: '缴税方式',
      }, {
        width: 120,
        tooltip: true,
        title: '完税总价',
        key: 'dutyTotal'
      }, {
        width: 120,
        title: '关税',
        tooltip: true,
        key: 'dutyPrice'
      }, {
        width: 120,
        tooltip: true,
        title: '增值税',
        key: 'taxPrice'
      }, {
        width: 120,
        tooltip: true,
        key: 'passDate',
        title: '放行日期'
      }, {
        width: 120,
        tooltip: true,
        key: 'inOutNo',
        title: '出/入库单据号'
      }, {
        width: 120,
        tooltip: true,
        key: 'financeNo',
        title: '财务单据号'
      }, {
        width: 200,
        tooltip: true,
        key: 'tradeName',
        title: '境内发货人'
      })
      return columns
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          me.$set(me.listConfig, 'exportTitle', '进口保税报关单')
        } else {
          me.$set(me.listConfig, 'exportTitle', '进口非保税报关单')
        }
      } else {
        if (me.bondMark === '0') {
          me.$set(me.listConfig, 'exportTitle', '出口保税报关单')
        } else {
          me.$set(me.listConfig, 'exportTitle', '出口非保税报关单')
        }
      }
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
          me.handleSearchSubmit()
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
        me.handleSearchSubmit()
      })
    },
    /**
     * 数据统计
     */
    handleStatistics() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    }
  },
  beforeDestroy: function () {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
