<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>

        <div style="white-space: nowrap; display: inline-flex;" >
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 50px; color: red;">当前页面为上次统计结果，如需更新请点击[数据统计]按钮！</div>
          <!--          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div>-->
          <div style="white-space: nowrap; font-weight: bold; width: 340px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后统计时间: {{jobInfo.lastTime}}
          </div>
        </div>

      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { customsDeclarationHeadList } from '../js/customsDeclarationHeadList'

  export default {
    name: 'customsDeclarationHeadList',
    mixins: [customsDeclarationHeadList],
    data() {
      return {
        totalContent: '',
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: '',
          getDecTotal: '',
          insertJob: csAPI.reportCenter.jobComm.insertJob,
          getLastJob: csAPI.reportCenter.jobComm.getLastJob
        },
        taskInfo: {
          taskCode: ''     // 添加任务使用的taskCode
        }
      }
    },
    created: function () {
      let me = this
      me.getLastJobInfo()
      me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
      if (me.ieMark === 'I') {
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.iEntry.exportHead)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.iEntry.selectAllPagedhead)
        if (me.bondMark === '0') {
          me.$set(me.listConfig, 'exportTitle', '进口保税报关单表头信息')
          me.$set(me.taskInfo, 'taskCode', 'I_ENTRY_HEAD_0')
        } else {
          me.$set(me.listConfig, 'exportTitle', '进口非保税报关单表头信息')
          me.$set(me.taskInfo, 'taskCode', 'I_ENTRY_HEAD_1')
        }
      } else {
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.eEntry.exportHead)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.eEntry.selectAllPagedhead)
        if (me.bondMark === '0') {
          me.$set(me.listConfig, 'exportTitle', '出口保税报关单表头信息')
          me.$set(me.taskInfo, 'taskCode', 'E_ENTRY_HEAD_0')
        } else {
          me.$set(me.listConfig, 'exportTitle', '出口非保税报关单表头信息')
          me.$set(me.taskInfo, 'taskCode', 'E_ENTRY_HEAD_1')
        }
      }
    },
    computed: {
      jobType() {
        let me = this
        if (me.ieMark === 'I') {
          return 'REPORT_ENTRY_I_HEAD'
        }
        return 'REPORT_ENTRY_E_HEAD'
      },

    }

  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
