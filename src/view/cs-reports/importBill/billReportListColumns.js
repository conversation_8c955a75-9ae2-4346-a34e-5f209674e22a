import { getKeyValue } from '@/libs/util'
import { formatDate } from '@/libs/datetime'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columns = {
  mixins:[baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '清单表头',
          align: 'center',
          children: [
            {
              title: '内审状态',
              key: 'apprStatusName',
              width: 130,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render:(h,params) => {
                return h ('span',`${params.row.apprStatus}  ${params.row.apprStatusName}`)
              }
            },
            {
              title: '清单内部编号',
              key: 'emsListNo',
              width: 180,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              minWidth: 120,
              align: 'center',
              title: '制单日期',
              key: 'erpInsertTime',
              render: (h, params) => {
                return h('span', formatDate(params.row.erpInsertTime, 'yyyy-MM-dd'))
              }
            },
            {
              title: '报关单号',
              key: 'entryNo',
              width: 150,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              minWidth: 120,
              align: 'center',
              title: '报关单申报日期',
              key: 'entryDeclareDate',
              render: (h, params) => {
                return h('span', formatDate(params.row.entryDeclareDate, 'yyyy-MM-dd'))
              }
            },
            {
              title: '备案号',
              key: 'emsNo',
              width: 140,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '监管方式',
              key: 'tradeMode',
              width: 130,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
              }
            },
            {
              title: '运输方式',
              key: 'trafMode',
              width: 110,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.transf, params.row.trafMode), params.row.trafMode))
              }
            },
            {
              title: '成交方式',
              key: 'transMode',
              width: 70,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.transac, params.row.transMode), params.row.transMode))
              }
            },
            {
              title: '申报单位',
              key: 'declareName',
              width: 160,
              ellipsis: true,
              tooltip: true,
              align:'center',
            },
            {
              title: '主管海关',
              key: 'masterCustoms',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
              }
            },
            {
              title: '境外发货人',
              key: 'overseasShipper',
              width: 250,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.cmbDataSource.overseasShipperList,params.row.overseasShipper))
              }
            },
            {
              title: '提运单号',
              key: 'hawb',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '运输工具及航次',
              key: 'trafName',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '启运国（地区）',
              key: 'tradeCountry',
              width: 180,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.tradeCountry), params.row.tradeCountry))
              }
            },
            {
              title: '进境关别',
              key: 'ieport',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.ieport), params.row.ieport))
              }
            },
            {
              title: '合同协议号',
              key: 'contrNo',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '许可证号',
              key: 'licenseNo',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '包装种类',
              key: 'wrapType',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align:'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.wrapType, params.row.wrap), params.row.wrap))
              }
            },
            {
              title: '件数',
              key: 'packNum',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '总净重',
              key: 'netWt',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '总毛重',
              key: 'grossWt',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align:'center'
            },
            {
              title: '运费',
              align: 'center',
              children: [
                {
                  title: '类型',
                  key: 'feeMark',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.feeMark))
                  }
                },
                {
                  title: '费率',
                  key: 'feeRate',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center'
                },
                {
                  title: '币制',
                  key: 'feeCurr',
                  width: 150,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.feeCurr), params.row.feeCurr))
                  }
                }
              ]
            },
            {
              title: '保费',
              align: 'center',
              children: [
                {
                  title: '类型',
                  key: 'insurMark',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.cmbDataSource.feeMarkList,params.row.insurMark))
                  }
                },
                {
                  title: '费率',
                  key: 'insurRate',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center'
                },
                {
                  title: '币制',
                  key: 'insurCurr',
                  width: 150,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.insurCurr), params.row.insurCurr))
                  }
                }
              ]
            },
            {
              title: '杂费',
              align: 'center',
              children: [
                {
                  title: '类型',
                  key: 'otherMark',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.cmbDataSource.feeMarkList,params.row.otherMark))
                  }
                },
                {
                  title: '费率',
                  key: 'otherRate',
                  width: 80,
                  ellipsis: true,
                  tooltip: true,
                  align:'center'
                },
                {
                  title: '币制',
                  key: 'otherCurr',
                  width: 150,
                  ellipsis: true,
                  tooltip: true,
                  align:'center',
                  render: (h, params) => {
                    return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.otherCurr), params.row.otherCurr))
                  }
                }
              ]
            },
            {
              title: '备注',
              key: 'note',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align:'center'
            }
          ]
        },
        {
          title: '清单明细',
          align: 'center',
          children: [
            //明细栏位
            {
              title: '商品序号',
              key: 'serialNo',
              width: 50,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '报关单商品序号',
              key: 'entryGNo',
              width: 70,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '备案序号',
              key: 'gno',
              width: 50,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '备案料号',
              key: 'copGNo',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '企业料号',
              key: 'facGNo',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '商品编码',
              key: 'codeTS',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '商品名称',
              key: 'gname',
              width: 130,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '规格型号',
              key: 'gmodel',
              width: 130,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '原产国(地区)',
              key: 'originCountry',
              width: 100,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
              }
            },
            {
              title: '最终目的国(地区)',
              key: 'destinationCountry',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.destinationCountry), params.row.destinationCountry))
              }
            },
            {
              title: '币制',
              key: 'curr',
              width: 150,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
              }
            },
            {
              title: '申报单价',
              key: 'decPrice',
              width: 70,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '申报数量',
              key: 'qty',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '申报计量单位',
              key: 'unit',
              width: 60,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit), params.row.unit))
              }
            },
            {
              title: '申报总价',
              key: 'decTotal',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '法定数量',
              key: 'qty1',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '法定计量单位',
              key: 'unit1',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit1), params.row.unit1))
              }
            },
            {
              title: '第二法定数量',
              key: 'qty2',
              width: 70,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '法定第二计量单位',
              key: 'unit2',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit2), params.row.unit2))
              }
            },
            {
              title: '征免方式',
              key: 'dutyMode',
              width: 90,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return h('span', getKeyValue(this.pcodeGet(this.pcode.levymode, params.row.dutyMode), params.row.dutyMode))
              }
            },
            {
              title: '单耗版本号',
              key: 'exgVersion',
              width: 60,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '发票号',
              minWidth: 120,
              align: 'center',
              key: 'invoiceNo'
            },
            {
              title: '备注',
              minWidth: 120,
              align: 'center',
              key: 'note',
              ellipsis: true,
              tooltip: true
            },
            {
              title: 'Remark1',
              minWidth: 120,
              align: 'center',
              key: 'note1',
              ellipsis: true,
              tooltip: true
            },
            {
              title: 'Remark2',
              minWidth: 120,
              align: 'center',
              key: 'note2',
              ellipsis: true,
              tooltip: true
            },
            {
              title: 'Remark3',
              minWidth: 120,
              align: 'center',
              key: 'note3',
              ellipsis: true,
              tooltip: true
            }
          ]
        }
      ]
    }
  },
}
export {
  // columnsConfig,
  // excelColumnsConfig,
  columns
}
