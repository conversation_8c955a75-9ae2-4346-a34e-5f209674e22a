import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { erpInterfaceData, importExportManage, taxPreference } from '@/view/cs-common'


export const statisticalReportGroupList = {
  name: 'statisticalReportList',
  mixins: [baseSearchConfig, listDataProcessing],
  components: {},
  props: {
    reportType: {
      type: String,
      default: () => ('')
    },
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    }
  },
  data() {
    let confirmSource = this.getConfirmSource()
    return {
      cmbSource: {
        tradeCode:[],
        agentCode: [],
        forwardCode: [],
        cusRemarkAuto: [{
          value: '1', label: '是'
        }, {
          value: '0', label: '否'
        }, {
          value: '2', label: '空'
        }],
        checkMark: [{
          value: '1', label: '是'
        }, {
          value: '0', label: '否'
        }],
        customsCheckMark: [{
          value: '1', label: '是'
        }, {
          value: '0', label: '否'
        }],
        abnormalType: [{
          value: '1', label: '破损'
        }, {
          value: '2', label: '商检查验'
        }, {
          value: '3', label: '海关查验'
        }],
        confirmPrice: confirmSource,
        confirmSpecial: confirmSource,
        confirmRoyalties: confirmSource,
        iemark: taxPreference.IE_MARK_MAP,
        gmark: erpInterfaceData.MAT_FLAG_MAP,
        damageMark: importExportManage.damageMarkMap,
        bondMark: importExportManage.bondedFlagMap4Erp
      },
      configAutoLoad: false,
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created() {

  } ,
  watch: {
    reportType: {
      immediate: true,
      handler: function () {
        let me = this
        me.loadConfigByType()
      }
    }
  },
  methods: {
    getConfirmSource() {
      return [{
        value: '0', label: '是'
      }, {
        value: '1', label: '否'
      }, {
        value: '2', label: '空'
      }]
    },
    /**
     * 返回列表
     */
    backToList() {
      let me = this
      me.$emit('backToMain')
    },
    /**
     * 获取设置key
     */
    getListId() {
      let me = this,
        rootId = me.$route.path + '/' + me.$options.name + '/' + me.reportType
      me.$set(me, 'listId', rootId + '/' + me.pmsLevel + '/listId')
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
