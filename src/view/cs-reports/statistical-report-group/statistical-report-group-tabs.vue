<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="importTab" label="进口">
        <StatisticalReportGroupList :report-type="reportType" ie-mark="I"></StatisticalReportGroupList>
      </TabPane>
      <TabPane name="exportTab" label="出口">
        <StatisticalReportGroupList :report-type="reportType" ie-mark="E"></StatisticalReportGroupList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { statisticalReportGroupTabs } from './js/statisticalReportGroupTabs'

  export default {
    name: 'statisticalReportGroupTabs',
    mixins: [statisticalReportGroupTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
