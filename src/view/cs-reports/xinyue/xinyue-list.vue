<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch" class="xdo-enter-root" v-focus>
            <div class="separateLine"></div>
            <FormBuilder ref="headSearch" :schema="schema" :items="searchFields" :model="searchParam">
              <template v-slot:diffPrecent>
                <DcNumberInput v-model="searchParam.diffPrecent" integer-digits="3" precision="0" append="%">
                </DcNumberInput>
              </template>
            </FormBuilder>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseListFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ImportPage :importShow.sync="importShow" :importKey="importKey" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { xinYueList } from './js/xinyueList'
  import DcNumberInput from '@/components/dc-number-input/dc-number-input'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

  export default {
    name: 'xinYueList',
    mixins: [xinYueList, dynamicImport],
    components: {
      DcNumberInput
    },
    data() {
      let importConfig = this.getCommImportConfig('XY_SZC_REPORT_IMPORT')
      return {
        gridConfig: {
          exportTitle: '信越受注残报表'
        },
        hasOptionColumn: false,
        importConfig: importConfig,
        importKey: 'XY_SZC_REPORT_IMPORT',
        ajaxUrl: {
          exportUrl: csAPI.csImportExport.xinYue.exportUrl,
          selectAllPaged: csAPI.csImportExport.xinYue.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
