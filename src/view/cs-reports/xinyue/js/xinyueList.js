import pms from '@/libs/pms'
import ImportPage from 'xdo-import'
import { commFields } from '@/view/cs-expense-manage/js/comm/commFields'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const xinYueList = {
  mixins: [commFields, baseColumns, pms],
  components: {
    ImportPage
  },
  data() {
    return {
      // 通用字段
      baseFields: [
        {
          width: 180,
          key: 'facGNo',
          tooltip: true,
          title: '企业成品料号'
        },
        {
          width: 180,
          tooltip: true,
          title: '订单总量',
          key: 'orderTotal'
        },
        {
          width: 180,
          tooltip: true,
          title: '订单已到货量',
          key: 'quantityArrived'
        },
        {
          width: 180,
          tooltip: true,
          title: '订单未到货量',
          key: 'quantityNoArrived'
        },
        {
          width: 180,
          tooltip: true,
          key: 'erpUnit',
          title: 'ERP计量单位'
        },
        {
          width: 130,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: 'ERP比例因子',
          key: 'erpScaleFactor'
        },
        {
          width: 180,
          tooltip: true,
          key: 'noArrivedConvert',
          title: '订单未到货量(转换后)'
        },
        {
          width: 180,
          key: 'copGNo',
          tooltip: true,
          title: '备案成品料号'
        },
        {
          width: 180,
          tooltip: true,
          key: 'copGName',
          title: '备案成品名称'
        },
        {
          width: 100,
          tooltip: true,
          key: 'copLineNo',
          title: '备案成品序号'
        },
        {
          width: 180,
          tooltip: true,
          title: '备案数量',
          key: 'copAmount'
        },
        {
          width: 180,
          tooltip: true,
          title: '已出口量',
          key: 'exportVolume'
        },
        {
          width: 180,
          tooltip: true,
          title: '手册余量',
          key: 'handAllowance'
        },
        {
          width: 180,
          tooltip: true,
          type: 'xdoInput',
          key: 'handAllowanceDiff',
          title: '手册余量与未到货差异'
        },
        {
          width: 120,
          tooltip: true,
          title: '差异百分比',
          key: 'diffPrecent'
        },
        {
          width: 120,
          type: 'input',
          tooltip: true,
          title: '操作员',
          key: 'insertUser'
        },
        {
          width: 160,
          tooltip: true,
          type: 'input',
          title: '操作员',
          key: 'insertUserName',
          keyField: 'insertUser',
          valueField: 'insertUserName'
        },
        {
          width: 140,
          title: '操作时间',
          key: 'insertTime',
          type: 'datePicker',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 250,
          type: 'input',
          tooltip: true,
          title: '维护人',
          key: 'updateUser'
        },
        {
          width: 150,
          tooltip: true,
          type: 'input',
          title: '维护人',
          key: 'updateUserName',
          keyField: 'updateUser',
          valueField: 'updateUserName'
        },
        {
          width: 100,
          title: '维护日期',
          key: 'updateTime',
          type: 'datePicker',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }
      ],
      toolbarEventMap: {
        'import': this.handleImport,
        'export': this.handleDownload,
        'setting': this.handleListSetupShow
      },
      schema: {
        titleWidth: 140,
        class: 'dc-form dc-form-4'
      },
      dynamicSource: {},
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      importShow: false
    }
  },
  mounted: function () {
    let me = this
    me.actionLoadMethods()
    me.$nextTick(() => {
      me.searchFields.forEach(field => {
        if (field.key === 'diffPrecent') {
          field.title = '差异百分比小于'
        }
      })
    })
  },
  computed: {
    /**
     * 数据源
     */
    cmbSource() {
      return {}
    }
  },
  methods: {
    actionLoadMethods() {
      let me = this
      me.loadFunctions().then(() => {
        if (typeof me.actionLoad === "function") {
          me.actionLoad()
        }
      })
    },
    actionLoad() {
      let me = this
      me.actions = []
      me.actions.push({
        ...me.actionsComm,
        label: "导入",
        command: "import",
        key: 'xdo-btn-take',
        icon: "ios-cloud-upload-outline"
      }, {
        ...me.actionsComm,
        label: "导出",
        command: "export",
        key: 'xdo-btn-download',
        icon: "ios-cloud-download-outline"
      // }, {
      //   ...me.actionsComm,
      //   label: "自定义配置",
      //   command: "setting",
      //   icon: "ios-cog",
      //   key: 'xdo-btn-setting'
      })
    },
    /**
     * 获取基础查询条件字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseSearchFields() {
      return ['facGNo', 'copGNo', 'copGName', 'handAllowanceDiff', 'diffPrecent']
    },
    // /**
    //  * 获取默认查询条件字段(需外部覆盖)
    //  * @returns {Array}
    //  */
    // getDefaultSearchFields() {
    //   return ['facGNo', 'copGNo', 'copGName', 'handAllowanceDiff', 'diffPrecent']
    // },
    /**
     * 获取基础显示列字段(需外部覆盖)
     * @returns {Array}
     */
    getBaseListFields() {
      return ['facGNo', 'orderTotal', 'quantityArrived', 'quantityNoArrived', 'erpUnit', 'unit', 'erpScaleFactor', 'noArrivedConvert',
        'copGNo', 'copGName', 'copLineNo', 'copAmount', 'exportVolume', 'handAllowance', 'handAllowanceDiff', 'diffPrecent',
        'insertUser', 'insertTime']
    },
    // /**
    //  * 获取默认显示列字段(需外部覆盖)
    //  * @returns {Array}
    //  */
    // getDefaultListFields() {
    //   return [{key: 'facGNo'}, {key: 'orderTotal'}, {key: 'quantityArrived'}, {key: 'quantityNoArrived'}, {key: 'erpUnit'}, {key: 'unit'},
    //     {key: 'erpScaleFactor'}, {key: 'noArrivedConvert'},
    //     {key: 'copGNo'}, {key: 'copGName'}, {key: 'copLineNo'}, {key: 'copAmount'}, {key: 'exportVolume'}, {key: 'handAllowance'},
    //     {key: 'handAllowanceDiff'}, {key: 'diffPrecent'},
    //     {key: 'insertUserName'}, {key: 'insertTime'}]
    // },
    handleImport() {
      this.importShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      this.importShow = false
      this.getList()
    },
    /**
     * Excel导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
