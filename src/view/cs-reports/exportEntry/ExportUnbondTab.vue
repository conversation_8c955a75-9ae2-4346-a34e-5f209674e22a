<template>
    <section>
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon></XdoBreadCrumb>
        </div>
      </XdoCard>
      <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
        <TabPane name="headTab" label="表头">
          <ExportUnbondHead v-if="tabName === 'headTab'" :tabName="tabName"></ExportUnbondHead>
        </TabPane>
        <TabPane name="bodyTab" label="表头和表体">
          <ExportUnbondBody v-if="tabName === 'bodyTab'" :tabName="tabName"></ExportUnbondBody>
        </TabPane>
      </XdoTabs>
    </section>
</template>

<script>
  import ExportUnbondHead from './ExportUnbondHead'
  import ExportUnbondBody from './ExportUnbondBody'

  export default {
    name: 'ExportUnbondTab',
    components: {
      ExportUnbondBody,
      ExportUnbondHead
    },
    data() {
      return {
        tabName: 'headTab'
      }
    }
  }
</script>

<style scoped>
</style>
