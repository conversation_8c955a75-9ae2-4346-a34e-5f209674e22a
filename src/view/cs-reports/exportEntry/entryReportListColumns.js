import { getKeyValue } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      headColumns: [
        {
          width: 120,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 120,
          key: 'emsListNo',
          title: '清单内部编号'
        },
        {
          width: 120,
          key: 'listNo',
          title: '核注清单编号'
        },
        {
          width: 130,
          tooltip: true,
          title: '提单状态',
          key: 'apprStatusName',
          render: (h, params) => {
            return h('span', `${params.row.apprStatus}  ${params.row.apprStatusName}`)
          }
        },
        {
          width: 120,
          key: 'erpEmsListNo',
          title: '提单内部编号'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'erpInsertTime'
        },
        {
          width: 120,
          key: 'declareDate',
          title: '清单申报日期'
        },
        {
          width: 120,
          title: '报关单状态',
          key: 'entryStatus',
          render: (h, params) => {
            if (params.row.entryStatus === null || params.row.entryStatusName === null) {
              return ''
            } else {
              return h('span', `${params.row.entryStatus}  ${params.row.entryStatusName}`)
            }
          }
        },
        {
          width: 120,
          key: 'seqNo',
          title: '报关单统一编号'
        },
        {
          width: 120,
          key: 'ddate',
          title: '报关单申报日期'
        },
        {
          width: 140,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型'
        },
        {
          width: 130,
          tooltip: true,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 110,
          tooltip: true,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 120,
          key: 'trafName',
          title: '运输工具及航次'
        },
        {
          width: 120,
          key: 'hawb',
          title: '提运单号'
        },
        {
          width: 70,
          tooltip: true,
          title: '成交方式',
          key: 'transMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        },
        {
          width: 160,
          tooltip: true,
          title: '申报单位',
          key: 'declareName'
        },
        {
          width: 150,
          title: '社会信用代码',
          key: 'declareCreditCode'
        },
        {
          width: 100,
          tooltip: true,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 250,
          tooltip: true,
          title: '境外收货人',
          key: 'overseasconsigneeEname',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.overseasShipperList, params.row.overseasShipper))
          }
        },
        {
          width: 100,
          tooltip: true,
          key: 'tradeCountry',
          title: '运抵国(地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 100,
          key: 'ieport',
          tooltip: true,
          title: '出境关别',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 90,
          tooltip: true,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 100,
          tooltip: true,
          title: '许可证号',
          key: 'licenseNo'
        },
        {
          width: 120,
          key: 'relEmsNo',
          title: '关联备案号'
        },
        {
          width: 120,
          key: 'relListNo',
          title: '关联清单编号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 100,
          tooltip: true,
          title: '其他包装',
          key: 'wrapType2',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 80,
          title: '件数',
          tooltip: true,
          key: 'packNum'
        },
        {
          width: 80,
          key: 'netWt',
          tooltip: true,
          title: '总净重'
        },
        {
          width: 80,
          tooltip: true,
          key: 'grossWt',
          title: '总毛重'
        },
        {
          width: 80,
          tooltip: true,
          key: 'qtyAll',
          title: '总数量'
        },
        {
          width: 80,
          tooltip: true,
          title: '总金额',
          key: 'totalAll'
        },
        {
          width: 80,
          tooltip: true,
          key: 'feeMark',
          title: '运费-类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.feeMarkList)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'feeRate',
          title: '运费-费率'
        },
        {
          width: 150,
          tooltip: true,
          key: 'feeCurr',
          title: '运费-币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'insurMark',
          title: '保费-类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.feeMarkList)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'insurRate',
          title: '保费-费率'
        },
        {
          width: 150,
          tooltip: true,
          key: 'insurCurr',
          title: '保费-币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'otherMark',
          title: '杂费-类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.feeMarkList)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'otherRate',
          title: '杂费-费率'
        },
        {
          width: 150,
          tooltip: true,
          key: 'otherCurr',
          title: '杂费-币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 90,
          key: 'note',
          tooltip: true,
          title: '表头备注'
        },
        {
          width: 90,
          tooltip: true,
          title: '委托日期',
          key: 'entrustDate'
        },
        {
          width: 90,
          tooltip: true,
          title: '委托人',
          key: 'entrustPerson'
        },
        {
          width: 90,
          tooltip: true,
          title: '海关查验日期',
          key: 'customsCheckDate'
        },
        {
          width: 90,
          tooltip: true,
          title: '海关查验原因',
          key: 'customsCheckReason'
        },
        {
          width: 90,
          tooltip: true,
          title: '海关查验结果',
          key: 'customsCheckResult'
        },
        {
          width: 90,
          tooltip: true,
          key: 'checkDate',
          title: '商检查验日期'
        },
        {
          width: 90,
          tooltip: true,
          key: 'checkReason',
          title: '商检查验原因'
        },
        {
          width: 90,
          tooltip: true,
          key: 'checkResult',
          title: '商检查验结果'
        },
        {
          width: 90,
          tooltip: true,
          key: 'dutyDate',
          title: '完税日期'
        },
        {
          width: 90,
          tooltip: true,
          key: 'dutyType',
          title: '缴税方式'
        },
        {
          width: 90,
          tooltip: true,
          title: '完税总价',
          key: 'dutyTotal'
        },
        {
          width: 90,
          title: '关税',
          tooltip: true,
          key: 'dutyPrice'
        },
        {
          width: 90,
          tooltip: true,
          title: '增值税',
          key: 'taxPrice'
        },
        {
          width: 90,
          tooltip: true,
          key: 'passDate',
          title: '放行日期'
        },
        {
          width: 90,
          tooltip: true,
          key: 'inOutNo',
          title: '出库关联编号'
        },
        {
          width: 90,
          tooltip: true,
          key: 'financeNo',
          title: '财务单据号'
        },
        {
          width: 90,
          tooltip: true,
          title: '邀请日期',
          key: 'inviteDate'
        },
        {
          width: 90,
          tooltip: true,
          title: '贸易条款',
          key: 'tradeTerms'
        },
        {
          width: 90,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 90,
          tooltip: true,
          title: '承运人',
          key: 'carrier'
        },
        {
          width: 90,
          tooltip: true,
          key: 'cweight',
          title: '计费重量'
        }
      ],
      bodyColumns: [
        {
          width: 120,
          tooltip: true,
          key: 'serialNo',
          title: '报关单商品序号'
        },
        {
          width: 100,
          tooltip: true,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 130,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 130,
          key: 'gmodel',
          tooltip: true,
          title: '规格型号'
        },
        {
          width: 100,
          tooltip: true,
          title: '原产国(地区)',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '最终目的国(地区)',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 150,
          key: 'curr',
          title: '币制',
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 70,
          tooltip: true,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 80,
          key: 'qty',
          tooltip: true,
          title: '申报数量'
        },
        {
          width: 90,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 80,
          key: 'qty1',
          tooltip: true,
          title: '法一数量'
        },
        {
          width: 90,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 70,
          key: 'qty2',
          tooltip: true,
          title: '法二数量'
        },
        {
          width: 90,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 90,
          tooltip: true,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 100,
          tooltip: true,
          key: 'exgVersion',
          title: '单耗版本号'
        },
        {
          width: 100,
          tooltip: true,
          title: '境内货源地',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        }
      ],
      exColumns: [
        {
          width: 90,
          tooltip: true,
          key: 'tradeName',
          title: '境内发货人'
        }
      ],
      bodyexColumns: [
        {
          width: 100,
          tooltip: true,
          key: 'districtPostCode',
          title: '境内货源地(行政)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        }, {
          width: 100,
          title: '报关单发送状态',
          key: 'sendApiStatus',
          render: (h, params) => {
            if (params.row.sendApiStatus === '-1') {
              return h('span', {
                style: {
                  fontWeight: 'bold'
                }
              }, '未发送')
            } else if (params.row.sendApiStatus === '0') {
              return h('Button', {
                props: {
                  type: 'error'
                },
                style: {
                  fontWeight: 'bold'
                },
                on: {
                  click: () => {
                    this.showErrMsg(params.row)
                  }
                }
              }, '发送失败')
            } else if (params.row.sendApiStatus === '1') {
              return h('span', {
                style: {
                  color: '#19BE6B',
                  fontWeight: 'bold'
                }
              }, '发送成功')
            } else {
              return h('span', '')
            }
          }
        }
      ]
    }
  }
}
