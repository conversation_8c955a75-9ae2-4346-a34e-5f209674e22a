import { csAPI } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import HeadReport from '../../dec-report-ft/components/list-panel/dec-report-head-list'
import HeadListReport from '../../dec-report-ft/components/list-panel/dec-report-headlist-list'

export const decReportTabs = {
  components: {
    HeadReport,
    HeadListReport
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        complexTab: false
      },
      cmbSource: {
        userData: [],
        emsNoData: [],
        entryStatusData: [],
        declareCodeData: [],
        forwardCodeData: [],
        overseasShipperData: []
      },
      ajaxUrl: {
        getSupplierCodeUrl: '',
        selectInsertUser: csAPI.csImportExport.template.selectInsertUser
      }
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
    }
  },
  created: function () {
    // 备案号
    let me = this
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.cmbSource.emsNoData = res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE
        }
      })
    }).catch(() => {
      me.cmbSource.emsNoData = []
    })
    if (me.ieMark === 'I') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.PRD)
    } else if (me.ieMark === 'E') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.CLI)
    } else {
      console.info('请为提单报表设置进出口标记【ieMark】!')
    }
    // 境外收/发货人
    me.$http.post(me.ajaxUrl.getSupplierCodeUrl).then(res => {
      me.cmbSource.overseasShipperData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.overseasShipperData = []
    })
    // 货代
    me.$http.post(csAPI.ieParams.FOD).then(res => {
      me.cmbSource.forwardCodeData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.forwardCodeData = []
    })
    // 申报单位
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.cmbSource.declareCodeData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.declareCodeData = []
    })
    // 用户/制单员/内审员
    me.$http.post(me.ajaxUrl.selectInsertUser + '/' + me.ieMark + '/' + "2").then(res => {
      me.$set(me.cmbSource, 'userData', res.data.data.map(item => {
        return {
          value: item['INSERT_USER'],
          label: item['INSERT_USER']
        }
      }))
    }).catch(() => {
      me.$set(me.cmbSource, 'userData', [])
    })
    // 报关单状态
    me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/ENTRY_STATUS').then(res => {
      me.cmbSource.entryStatusData = res.data.data.map((item) => {
        return {
          label: item.value,
          value: item.key
        }
      })
    }).catch(() => {
      me.cmbSource.entryStatusData = []
    })
  }
}
