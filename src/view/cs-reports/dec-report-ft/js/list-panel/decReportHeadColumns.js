import { isNullOrEmpty, getKeyValue } from "@/libs/util"
import { baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  ...baseColumnsExport
  , 'erpEmsListNo'
  , 'apprStatusName'
  , 'billStatus'
  , 'entryStatus'
  // , 'entryStatusName'
  , 'insertTime'
  , 'billEmsListNo'
  , 'listNo'
  , 'billDeclareDate'
  , 'seqNo'
  , 'entryNo'
  , 'entryDeclareDate'
  , 'emsNo'
  , 'iedate'
  , 'invoiceNo'
  , 'contrNo'
  , 'overseasShipperName'
  , 'receiveName'
  , 'tradeMode'
  , 'cutMode'
  , 'ieport'
  , 'entryPort'
  , 'relEmsNo'
  , 'relListNo'
  , 'applyNo'
  , 'masterCustoms'
  , 'forwardName'
  , 'trafMode'
  , 'trafName'
  , 'hawb'
  , 'warehouse'
  , 'gmark'
  , 'wrapTypeName'
  , 'netWtTotal'
  , 'grossWtTotal'
  , 'packNum'
  , 'volumeTotal'
  , 'qtyAll'
  , 'totalAll'
  , 'headCurr'
  , 'transMode'

  , 'feeMark'
  , 'feeRate'
  , 'feeCurr'
  , 'insurMark'
  , 'insurRate'
  , 'insurCurr'
  , 'otherMark'
  , 'otherRate'
  , 'otherCurr'

  , 'licenseNo'
  , 'declareName'
  , 'tradeNation'
  , 'tradeCountry'
  , 'destPort'
  , 'billType'
  , 'mergeType'
  , 'note'
  , 'insertUser'
  , 'apprUserFull'
  , 'carrier'
  , 'plateNum'
  , 'deliveryPerson'
  , 'shipDate'
  , 'transNo'
  , 'customsFee'

  , 'tradeTerms'
  , 'containerType'
  , 'containerNum'
  , 'taxTotal'
  , 'otherTax'
  , 'taxPrice'
  , 'dutyPrice'
  , 'insertWeek'
  , 'headDestinationCountry'
  , 'passDate'
  , 'financeNo'
  , 'gnameAll'
  , 'supplierCode'
  , 'supplierName'
  , 'cweight'
  , 'confirmSpecial'
  , 'confirmPrice'
  , 'confirmRoyalties'
  , 'confirmFormulaPrice'
  , 'confirmTempPrice'
  , 'dutySelf'
  , 'dutyInterest'
  , 'exciseInterest'
  , 'rateTotal'
]

// 进口提单
const colsIConfig = [
  ...commColumns
  , 'voyageDate'
  , 'despPort'
  , 'arrivalPortDate'
  , 'portContacts'
  , 'noticeDate'
  , 'damageMark'
  , 'linkManTel'
  , 'transPort'
  , 'freight'
  , 'shipFromName'
  , 'addTaxPrice'
  , 'deliveryDate'
  , 'notePass'
  , 'remark'
  , 'exciseTax'
  , 'logisticsFee'
  , 'dutyDate'
  , 'checkDate'
  , 'customsCheckDate'
  , 'taxInterest'
]

// 出口提单
const colsEConfig = [
  ...commColumns
  , 'overseasShipper'
  , 'shipToName'
  , 'bookingDate'
  , 'shipmentNo'
  , 'storageNo'
  , 'internationalCost'
  , 'domesticCost'
  , 'freightNo'
  , 'financeNo'
  , 'deliveryDate'
  , 'remark'
  , 'payTotal'
  , 'decTotalProcess'
  , 'mawb'
  // , 'entryStatus'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [{
        width: 160,
        tooltip: true,
        key: 'erpEmsListNo',
        title: '提单内部编号'
      }, {
        width: 100,
        tooltip: true,
        title: '提单状态',
        key: 'apprStatusName',
        render: (h, params) => {
          return this.keyValueRender(h, params, 'apprStatus', 'apprStatusName')
        }
      }, {
        width: 150,
        title: '制单日期',
        key: 'insertTime',
        // render: (h, params) => {
        //   return this.dateTimeShowRender(h, params)
        // }
      }, {
        width: 160,
        tooltip: true,
        title: '清单内部编号',
        key: 'billEmsListNo'
      }, {
        width: 160,
        tooltip: true,
        key: 'listNo',
        title: '核注清单编号'
      }, {
        width: 100,
        title: '清单申报日期',
        key: 'billDeclareDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 150,
        key: 'seqNo',
        tooltip: true,
        title: '报关单统一编号'
      }, {
        width: 150,
        tooltip: true,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 100,
        title: '报关单申报日期',
        key: 'entryDeclareDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 140,
        key: 'emsNo',
        tooltip: true,
        title: '备案号'
      }, {
        width: 100,
        key: 'iedate',
        title: '进口日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 100,
        title: '航班日期',
        key: 'voyageDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        tooltip: true,
        key: 'gnameAll',
        title: '主要商品名称'
      }, {
        width: 120,
        tooltip: true,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 120,
        tooltip: true,
        key: 'contrNo',
        title: '合同协议号'
      }, {
        width: 200,
        tooltip: true,
        title: '境外收货人代码',
        key: 'overseasShipper'
      }, {
        width: 200,
        tooltip: true,
        title: '境外收发货人',
        key: 'overseasShipperName'
      }, {
        width: 200,
        tooltip: true,
        key: 'receiveName',
        title: '境内收发货人',
        // render: (h, params) => {
        //   return this.keyValueRender(h, params, 'receiveCode', 'receiveName')
        // }
      }, {
        width: 150,
        tooltip: true,
        title: '监管方式',
        key: 'tradeMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      }, {
        width: 110,
        tooltip: true,
        key: 'cutMode',
        title: '征免性质',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.levytype)
        }
      }, {
        width: 135,
        tooltip: true,
        key: 'ieport',
        title: '进/出境关别',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 160,
        tooltip: true,
        key: 'entryPort',
        title: '入/离境口岸',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
        }
      }, {
        width: 120,
        tooltip: true,
        key: 'relEmsNo',
        title: '关联备案号'
      }, {
        width: 120,
        tooltip: true,
        key: 'relListNo',
        title: '关联清单编号'
      }, {
        width: 120,
        tooltip: true,
        key: 'applyNo',
        title: '申报表编号'
      }, {
        width: 135,
        title: '申报地海关',
        key: 'masterCustoms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 200,
        tooltip: true,
        title: '货运代理',
        key: 'forwardName',
        render: (h, params) => {
          return this.keyValueRender(h, params, 'forwardCode', 'forwardName')
        }
      }, {
        width: 110,
        tooltip: true,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 150,
        tooltip: true,
        key: 'trafName',
        title: '运输工具及航次'
      }, {
        width: 150,
        key: 'hawb',
        tooltip: true,
        title: '提运单号'
      }, {
        width: 200,
        tooltip: true,
        key: 'warehouse',
        title: '货物存放地点'
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
        }
      }, {
        width: 180,
        tooltip: true,
        title: '包装种类',
        key: 'wrapTypeName'
      }, {
        width: 160,
        tooltip: true,
        title: '总净重',
        key: 'netWtTotal'
      }, {
        width: 160,
        tooltip: true,
        title: '总毛重',
        key: 'grossWtTotal'
      }, {
        width: 160,
        tooltip: true,
        key: 'packNum',
        title: '总件数'
      }, {
        width: 160,
        tooltip: true,
        title: '总体积',
        key: 'volumeTotal'
      }, {
        width: 160,
        key: 'qtyAll',
        title: '总数量'
      }, {
        width: 160,
        title: '总金额',
        key: 'totalAll'
      }, {
        width: 160,
        title: '币制',
        key: 'headCurr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 80,
        tooltip: true,
        title: '成交方式',
        key: 'transMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transac)
        }
      }, {
        title: '运费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'feeMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'feeRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'feeCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        title: '保费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'insurMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'insurRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'insurCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        title: '杂费',
        children: [
          {
            width: 80,
            title: '类型',
            tooltip: true,
            key: 'otherMark',
            render: (h, params) => {
              return this.cmbShowRender(h, params, this.importExportManage.feeTypeMap)
            }
          },
          {
            width: 80,
            title: '费率',
            tooltip: true,
            key: 'otherRate'
          },
          {
            width: 150,
            title: '币制',
            tooltip: true,
            key: 'otherCurr',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
            }
          }
        ]
      }, {
        width: 120,
        tooltip: true,
        title: '许可证号',
        key: 'licenseNo'
      }, {
        width: 200,
        tooltip: true,
        title: '报关行',
        key: 'declareName',
        render: (h, params) => {
          return this.keyValueRender(h, params, 'declareCode', 'declareName')
        }
      }, {
        width: 120,
        key: 'tradeNation',
        title: '贸易国(地区)',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        title: '贸易条款',
        key: 'tradeTerms',
        render: (h, params) => {
          return h('span', this.getTrade(params.row.tradeTerms))
        }
      }, {
        width: 120,
        title: '集装箱类型',
        key: 'containerType',
        render: (h, params) => {
          return h('span', getKeyValue(this.importExportManage.containerType, params.row.containerType))
        }
      }, {
        width: 120,
        title: '集装箱数量',
        key: 'containerNum'
      }, {
        width: 180,
        tooltip: true,
        title: '启/抵运国',
        key: 'tradeCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 180,
        tooltip: true,
        title: '启运港',
        key: 'despPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.port_lin)
        }
      }, {
        width: 180,
        tooltip: true,
        title: '指运港',
        key: 'destPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.port_lin)
        }
      }, {
        width: 150,
        tooltip: true,
        title: 'Ship To',
        key: 'shipToName'
      }, {
        width: 200,
        key: 'note',
        tooltip: true,
        title: '表头备注'
      }, {
        width: 120,
        tooltip: true,
        title: '制单员',
        key: 'insertUser'
      }, {
        width: 150,
        tooltip: true,
        title: '内审员',
        key: 'apprUserFull'
      }, {
        width: 130,
        title: '订舱日期',
        key: 'bookingDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh')
        }
      }, {
        width: 120,
        key: 'shipDate',
        title: '发货日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params, 'yyyy-MM-dd hh')
        }
      }, {
        width: 130,
        title: '到货通知日期',
        key: 'noticeDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 100,
        tooltip: true,
        title: '破损标记',
        key: 'damageMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.damageMarkMap)
        }
      }, {
        width: 120,
        tooltip: true,
        title: '联系电话',
        key: 'linkManTel'
      }, {
        width: 120,
        tooltip: true,
        key: 'shipmentNo',
        title: '出货通知书号'
      }, {
        width: 120,
        tooltip: true,
        key: 'transNo',
        title: '转关单号'
      }, {
        width: 120,
        title: '转入口岸',
        key: 'transPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 120,
        tooltip: true,
        title: '进仓编号',
        key: 'storageNo'
      }, {
        width: 120,
        tooltip: true,
        title: '国际费用',
        key: 'internationalCost'
      }, {
        width: 120,
        tooltip: true,
        title: '国内费用',
        key: 'domesticCost'
      }, {
        width: 120,
        tooltip: true,
        title: '物流费用',
        key: 'customsFee'
      }, {
        width: 120,
        tooltip: true,
        key: 'freightNo',
        title: '运费凭据号'
      }, {
        width: 120,
        tooltip: true,
        key: 'freight',
        title: '国际运费',
        render: (h, params) => {
          if (params.row.freight) {
            return h('span', `${params.row.freight} ${this.pcodeGet(this.pcode.curr_outdated, params.row.freightCurr)}`)
          }
        }
      }, {
        width: 90,
        title: '递送日期',
        key: 'deliveryDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 90,
        tooltip: true,
        key: 'financeNo',
        title: '财务单据号'
      },{
        width: 90,
        title: '完税日期',
        key: 'dutyDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },{
        width: 120,
        tooltip: true,
        title: '完税价格',
        key: 'taxTotal'
      }, {
        width: 120,
        tooltip: true,
        key: 'otherTax',
        title: '其他进/出口环节税'
      }, {
        width: 120,
        tooltip: true,
        title: '增值税总额',
        key: 'taxPrice'
      }, {
        width: 120,
        title: '关税总额',
        tooltip: true,
        key: 'dutyPrice'
      }, {
        width: 90,
        title: '到港日期',
        key: 'arrivalPortDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        tooltip: true,
        title: '制单周次',
        key: 'insertWeek'
      }, {
        width: 120,
        title: '表头原产国/目的国',
        key: 'headDestinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        tooltip: true,
        title: 'ship from',
        key: 'shipFromName'
      }, {
        width: 120,
        tooltip: true,
        key: 'passDate',
        title: '放行日期'
      }, {
        width: 120,
        tooltip: true,
        title: '附加税',
        key: 'addTaxPrice'
      }, {
        width: 150,
        tooltip: true,
        key: 'notePass',
        title: '放行备注'
      }, {
        width: 120,
        tooltip: true,
        title: '供应商/客户',
        key: 'supplierName'
      }, {
        width: 90,
        key: 'remark',
        tooltip: true,
        title: '内部备注'
      }, {
        width: 160,
        tooltip: true,
        key: 'cweight',
        title: '计费重量'
      }, {
        width: 90,
        tooltip: true,
        title: '消费税',
        key: 'exciseTax'
      }, {
        width: 120,
        title: '特殊关系确认',
        key: 'confirmSpecial'
      }, {
        width: 120,
        key: 'confirmPrice',
        title: '价格影响确认'
      }, {
        width: 160,
        key: 'confirmRoyalties',
        title: '支付特许权使用费确认'
      }, {
        width: 160,
        key: 'confirmFormulaPrice',
        title: '公式定价确认'
      }, {
        width: 160,
        key: 'confirmTempPrice',
        title: '暂定价格确认'
      }, {
        width: 120,
        key: 'dutySelf',
        title: '自报自缴'
      }, {
        width: 136,
        title: '报关费',
        key: 'logisticsFee'
      }, {
        width: 136,
        title: '实收汇',
        key: 'payTotal',
        render: (h, params) => {
          let total = params.row[params.column.key],
            curr = params.row['payTotalCurr'],
            currName = this.pcodeGet('CURR_OUTDATED', curr)
          if (!isNullOrEmpty(total) && !isNullOrEmpty(currName)) {
            return h('span', total + ' ' + currName)
          }
          return h('span', '')
        }
      }, {
        width: 136,
        title: '系统加工费',
        key: 'decTotalProcess',
        render: (h, params) => {
          let total = params.row[params.column.key],
            curr = params.row['decTotalProcessCurr'],
            currName = this.pcodeGet('CURR_OUTDATED', curr)
          if (!isNullOrEmpty(total) && !isNullOrEmpty(currName)) {
            return h('span', total + ' ' + currName)
          }
          return h('span', '')
        }
      }, {
        width: 130,
        key: 'mawb',
        title: '主提运单号'
      }, {
        width: 120,
        title: '报关单状态',
        key: 'entryStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.entryStatus)
        }
      }, {
        width: 160,
        tooltip: true,
        title: '关税缓息',
        key: 'dutyInterest'
      }, {
        width: 160,
        tooltip: true,
        title: '增值税缓息',
        key: 'taxInterest'
      }, {
        width: 160,
        tooltip: true,
        title: '消费税缓息',
        key: 'exciseInterest'
      },{
        width: 120,
        title: '商检查验日期',
        key: 'checkDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },{
        width: 120,
        title: '海关查验日期',
        key: 'customsCheckDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },{
        width: 120,
        title: '费用总额',
        key: 'rateTotal'
      }]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    },
    getTrade(val) {
      let me = this
      let tradeDataStr = ''
      let tradeData = [
        {value: "1", label: "CIF"},
        {value: "2", label: "C&F"},
        {value: "3", label: "FOB"},
        {value: "4", label: "C&I"},
        {value: "5", label: "市场价"},
        {value: "6", label: "垫仓"},
        {value: "7", label: "EXW"}
      ]
      let data = [...tradeData, ...me.importExportManage.tradeTermList]
      data.forEach(item => {
        if (item.value === val) {
          tradeDataStr = item.label
        }
      })
      return tradeDataStr
    }
  }
}

export {
  colsIConfig,
  colsEConfig,
  columns
}
