import pms from '@/libs/pms'
import { isNullOrEmpty } from '@/libs/util'
import { getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
import { interimVerification, erpInterfaceData, importExportManage, productClassify } from '@/view/cs-common'

export const decReportList = {
  mixins: [commList, pms, dynamicExport],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    cmbSource: {
      type: Object,
      required: true,
      default: () => ({
        userData: [],
        emsNoData: [],
        entryStatusData: [],
        declareCodeData: [],
        forwardCodeData: [],
        overseasShipperData: []
      })
    }
  },
  data() {
    return {
      tableId: '',
      gridConfig: {
        exportTitle: ''
      },
      ajaxUrl: {
        exportUrl: '',
        getSumData: '',
        selectAllPaged: ''
      },
      configColumns: [],
      orginShowColumns: [],
      showtableColumnSetup: false,
      toolbarEventMap: {
        'export': this.handleDownload,
        'statistics': this.handleStatistics,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {},
      searchLines: 4,
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      hasChildTabs: true,
      productClassify: productClassify,
      erpInterfaceData: erpInterfaceData,
      importExportManage: importExportManage,
      interimVerification: interimVerification,
      reportStatus: interimVerification.REPORT_STATUS_MAP
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then(() => {
      me.actions.push({
        needed: true,
        loading: false,
        disabled: false,
        label: '数据统计',
        key: 'xdo-btn-delete',
        command: 'statistics',
        icon: 'ios-calculator-outline'
      }, {
        key: '',
        needed: true,
        label: '导出',
        loading: false,
        disabled: false,
        command: 'export',
        icon: 'ios-cloud-download-outline'
      }, {
        key: '',
        needed: true,
        loading: false,
        disabled: false,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting'
      })
    })
  },
  methods: {
    beforeFirstSearch() {
      let me = this
      if (me.searchParam.hasOwnProperty('insertTimeFrom')) {
        me.$set(me.searchParam, 'insertTimeFrom', me.ieDefaultDates[0])
      }
      if (me.searchParam.hasOwnProperty('insertTimeTo')) {
        me.$set(me.searchParam, 'insertTimeTo', me.ieDefaultDates[1])
      }
    },
    /**
     * 修改列标题
     * @param gCol
     */
    loadColumnTitle(gCol) {
      let me = this
      if (gCol.key === 'iedate') {
        gCol.title = me.iedateLabel
      } else if (gCol.key === 'overseasShipperName') {
        gCol.title = me.overseasShipperLabel
      } else if (gCol.key === 'receiveName') {
        gCol.title = me.receiveCodeLabel
      } else if (gCol.key === 'ieport') {
        gCol.title = me.ieportLabel
      } else if (gCol.key === 'entryPort') {
        gCol.title = me.entryPortLabel
      } else if (gCol.key === 'tradeCountry') {
        gCol.title = me.tradeCountryLabel
      } else if (gCol.key === 'destPort') {
        gCol.title = me.destPortLabel
      } else if (gCol.key === 'deliveryPerson') {
        gCol.title = me.deliveryPersonLabel
      } else if (gCol.key === 'shipDate') {
        gCol.title = me.shipDateLabel
      } else if (gCol.key === 'customsFee') {
        gCol.title = me.customsFeeLabel
      } else if (gCol.key === 'supplierName') {
        gCol.title = me.supplierNameLabel
      } else if (gCol.key === 'districtCode') {
        gCol.title = me.districtCodeLabel
      } else if (gCol.key === 'districtPostCode') {
        gCol.title = me.districtPostCodeLabel
      } else if (gCol.key === 'orderNo') {
        gCol.title = me.orderNoLabel
      } else if (gCol.key === 'otherTax') {
        gCol.title = me.otherTaxLabel
      } else if (gCol.key === 'deliveryDate') {
        gCol.title = me.deliveryDateLabel
      } else if (gCol.key === 'inOutNo') {
        gCol.title = me.inOutNoLabel
      }
    },
    /**
     * 根据配置信息重置列标题
     */
    resetColumns() {
      let me = this
      for (let mainCol of me.configColumns) {
        if (Array.isArray(mainCol.children) && mainCol.children.length > 0) {
          for (let gCol of mainCol.children) {
            me.loadColumnTitle(gCol)
          }
        } else {
          me.loadColumnTitle(mainCol)
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleTableColumnSetup() {
      let me = this
      me.showtableColumnSetup = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.gridConfig.gridColumns = columns
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
          me.handleSearchSubmit()
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
        me.handleSearchSubmit()
      })
    },
    /**
     * 数据统计
     */
    handleStatistics() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    }
  },
  computed: {
    iedateLabel() {
      if (this.ieMark === 'I') {
        return '进口日期'
      } else if (this.ieMark === 'E') {
        return '出口日期'
      } else {
        return '进/出口日期'
      }
    },
    overseasShipperLabel() {
      if (this.ieMark === 'I') {
        return '境外发货人'
      } else if (this.ieMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收/发货人'
      }
    },
    receiveCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内收货人'
      } else if (this.ieMark === 'E') {
        return '境内发货人'
      } else {
        return '境内收/发货人'
      }
    },
    ieportLabel() {
      if (this.ieMark === 'I') {
        return '进境关别'
      } else if (this.ieMark === 'E') {
        return '出境关别'
      } else {
        return '进/出境关别'
      }
    },
    entryPortLabel() {
      if (this.ieMark === 'I') {
        return '入境口岸'
      } else if (this.ieMark === 'E') {
        return '离境口岸'
      } else {
        return '离/入境口岸'
      }
    },
    tradeCountryLabel() {
      if (this.ieMark === 'I') {
        return '启运国(地区)'
      } else if (this.ieMark === 'E') {
        return '抵运国(地区)'
      } else {
        return '启/抵运国(地区)'
      }
    },
    destPortLabel() {
      if (this.ieMark === 'I') {
        return '经停港'
      } else if (this.ieMark === 'E') {
        return '指运港'
      } else {
        return '经停/指运港'
      }
    },
    deliveryPersonLabel() {
      if (this.ieMark === 'I') {
        return '送货人'
      } else if (this.ieMark === 'E') {
        return '提货人'
      } else {
        return '提/送货人'
      }
    },
    shipDateLabel() {
      if (this.ieMark === 'I') {
        return '发货日期'
      } else if (this.ieMark === 'E') {
        return '出货日期'
      } else {
        return '发/出货日期'
      }
    },
    customsFeeLabel() {
      if (this.ieMark === 'I') {
        return '物流费用'
      } else if (this.ieMark === 'E') {
        return '报关费'
      } else {
        return '物流/报关费'
      }
    },
    supplierNameLabel() {
      if (this.ieMark === 'I') {
        return '表体供应商'
      } else if (this.ieMark === 'E') {
        return '表体客户'
      } else {
        return '表体供应商/客户'
      }
    },
    districtCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内目的地'
      } else if (this.ieMark === 'E') {
        return '境内货源地'
      } else {
        return '境内(货源/目的)地'
      }
    },
    districtPostCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内目的地(行政区划)'
      } else if (this.ieMark === 'E') {
        return '境内货源地(行政区划)'
      } else {
        return '境内(货源/目的)地(行政区划)'
      }
    },
    supplierCodeLabel() {
      if (this.ieMark === 'I') {
        return '表体供应商'
      } else if (this.ieMark === 'E') {
        return '表体客户'
      } else {
        return '表体供应商/客户'
      }
    },
    orderNoLabel() {
      if (this.ieMark === 'I') {
        return '采购订单号'
      } else if (this.ieMark === 'E') {
        return '销售订单号'
      } else {
        return '(销售)订单号码'
      }
    },
    otherTaxLabel() {
      if (this.ieMark === 'I') {
        return '其他进口环节税'
      } else if (this.ieMark === 'E') {
        return '其他出口环节税'
      } else {
        return '其他进/出口环节税'
      }
    },
    deliveryDateLabel() {
      if (this.ieMark === 'I') {
        return '到厂日期'
      } else if (this.ieMark === 'E') {
        return '递送日期'
      } else {
        return '到厂/递送日期'
      }
    },
    inOutNoLabel() {
      if (this.ieMark === 'I') {
        return '入库关联单号'
      } else if (this.ieMark === 'E') {
        return '出库关联单号'
      } else {
        return '入/出库关联单号'
      }
    },
    ieDefaultDates() {
      let today = new Date(),
        dateTo = today.toLocaleDateString(),
        dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
      return [dateFrom, dateTo]
    },
    jobStatusName() {
      let me = this,
        theStatus = me.reportStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }
  },
  beforeDestroy: function () {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
