import { decReportList } from '../list-panel/decReportList'

export const decReportSearch = {
  mixins: [decReportList],
  data() {
    let elements = this.getElements()
    let formResId = this.getFormResId()
    let defaultParams = this.getDefaultParams()
    let showElementsAndParams = this.getShowElements(formResId, elements, defaultParams)
    return {
      showFormSetup: false,
      formResId: formResId,
      schema: {
        titleWidth: 100,
        class: 'dc-form dc-form-4'
      },
      showElements: showElementsAndParams.showElements,
      searchParam: showElementsAndParams.searchParam,
      defaultParams: defaultParams,
      elements: elements,
      searchLines: Math.ceil(showElementsAndParams.showElements.length / 4),
      keySource: {
        emsNo: 'emsNoData',
        apprUser: 'userData',
        listEmsNo: 'emsNoData',
        insertUser: 'userData',
        entryStatus: 'entryStatusData',
        forwardCode: 'forwardCodeData',
        declareCodeCustoms: 'declareCodeData',
        supplierCode: 'overseasShipperData',
        overseasShipper: 'overseasShipperData'
      },
      promiseItems: [{
        label: '是', value: '0'
      }, {
        label: '否', value: '1'
      }, {
        label: '空', value: '2'
      }]
    }
  },
  watch: {
    cmbSource: {
      deep: true,
      immediate: true,
      handler: function (source) {
        let me = this
        me.$nextTick(() => {
          me.showElements.forEach(item => {
            if (me.keySource.hasOwnProperty(item.key)) {
              item.props.options = source[me.keySource[item.key]]
            }
          })
        })
      }
    }
  },
  mounted: function () {
    let me = this
    me.$nextTick(() => {
      let optionsSource = me.getOptionsSource()
      let dynamicLabels = me.getDynamicLabels()
      me.showElements.forEach(item => {
        if (optionsSource.hasOwnProperty(item.key)) {
          item.props.options = optionsSource[item.key]
        }
        if (dynamicLabels.hasOwnProperty(item.key)) {
          item.title = dynamicLabels[item.key]
        }
      })
      me.elements.forEach(item => {
        if (dynamicLabels.hasOwnProperty(item.key)) {
          item.title = dynamicLabels[item.key]
        }
      })
    })
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return this.searchParam
    },
    handleShowSet() {
      let me = this
      me.$set(me, 'showFormSetup', true)
    },
    /**
     * 获取要显示的查询条件
     * @param formResId
     * @param elements
     * @param defaultParams
     */
    getShowElements(formResId, elements, defaultParams) {
      let me = this
      let showElements = me.$bom3.userCustom('form', formResId, elements, defaultParams) || []
      let params = {}
      if (Array.isArray(showElements)) {
        showElements.forEach(item => {
          if (Array.isArray(item.fields)) {
            item.fields.forEach(field => {
              params[field.key] = ''
            })
          } else {
            params[item.key] = ''
          }
        })
      }
      return {
        showElements: showElements,
        searchParam: params
      }
    },
    /**
     * 更新查询条件
     */
    handleUpdateSearch() {
      let me = this
      let showElementsAndParams = me.getShowElements(me.formResId, me.elements, me.defaultParams)
      me.$set(me, 'showElements', showElementsAndParams.showElements)
      me.$set(me, 'searchParam', showElementsAndParams.searchParam)
      if (me.showElements) {
        me.searchLines = Math.ceil(me.showElements.length / 4)
      }
      // 重新为数据源赋值
      me.$nextTick(() => {
        let optionsSource = me.getOptionsSource()
        me.showElements.forEach(item => {
          if (me.keySource.hasOwnProperty(item.key)) {
            item.props.options = me.cmbSource[me.keySource[item.key]]
          }
          if (optionsSource.hasOwnProperty(item.key)) {
            item.props.options = optionsSource[item.key]
          }
        })
      })
    },
    /**
     * 表头通用条件
     * @returns {*[]}
     */
    getHeadCommParams() {
      return [
        {key: 'erpEmsListNo', title: '提单内部编号', type: 'input'},
        {
          key: 'emsNo', title: '备案号', type: 'select',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        {
          type: 'dateRange', title: '制单日期', key: 'insertTime',
          fields: [{key: 'insertTimeFrom'}, {key: 'insertTimeTo'}]
        },
        {
          key: 'apprStatus', title: '提单状态', type: 'select',
          props: {options: []/*this.importExportManage.auditStatusMap*/}
        },
        // {key: 'listNo', title: '核注清单号', type: 'input'},
        {
          type: 'dateRange', title: '清单申报日期', key: 'billDeclareDate',
          fields: [{key: 'billDeclareDateFrom'}, {key: 'billDeclareDateTo'}]
        },
        // {key: 'billStatus', title: '清单状态', type: 'select', props: {options: []/*this.importExportManage.entryStatus*/}},
        {key: 'entryStatus', title: '报关单状态', type: 'select', props: {options: []}},
        {key: 'entryNo', title: '报关单号', type: 'input'},
        {key: 'seqNo', title: '报关单统一编号', type: 'input'},
        {
          type: 'dateRange', title: '报关单申报日期', key: 'entryDeclareDate',
          fields: [{key: 'entryDeclareDateFrom'}, {key: 'entryDeclareDateTo'}]
        },
        {type: 'dateRange', title: this.iedateLabel, key: 'iedate', fields: [{key: 'iedateFrom'}, {key: 'iedateTo'}]},
        // {type: 'dateRange', title: '航班日期', key: 'flightDate', fields: [{key: 'flightDateFrom'}, {key: 'flightDateTo'}]},
        {key: 'invoiceNo', title: '发票号', type: 'input'},
        {key: 'contrNo', title: '合同协议号', type: 'input'},
        {
          key: 'overseasShipper', title: this.overseasShipperLabel, type: 'select',
          props: {options: []/*this.cmbSource.overseasShipperData*/}
        },
        {key: 'tradeMode', title: '监管方式', type: 'pcode', props: {meta: 'TRADE'/*this.pcode.trade*/}},
        {key: 'cutMode', title: '征免性质', type: 'pcode', props: {meta: 'LEVYTYPE'/*this.pcode.levytype*/}},
        {key: 'ieport', title: this.ieportLabel, type: 'pcode', props: {meta: 'CUSTOMS_REL'/*this.pcode.customs_rel*/}},
        {key: 'entryPort', title: this.entryPortLabel, type: 'pcode', props: {meta: 'CIQ_ENTY_PORT'}},
        {key: 'relEmsNo', title: '关联备案号', type: 'input'},
        {key: 'relListNo', title: '关联清单编号', type: 'input'},
        {key: 'masterCustoms', title: '申报地海关', type: 'pcode', props: {meta: 'CUSTOMS_REL'/*this.pcode.customs_rel*/}},
        {key: 'forwardCode', title: '货运代理', type: 'select', props: {options: []/*this.cmbSource.forwardCodeData*/}},
        {key: 'trafMode', title: '运输方式', type: 'pcode', props: {meta: 'TRANSF'/*this.pcode.transf*/}},
        {key: 'hawb', title: '提运单号', type: 'input'},
        {key: 'wrapType', title: '包装种类', type: 'pcode', props: {meta: 'WRAP'/*this.pcode.wrap*/}},
        {key: 'gmark', title: '物料类型', type: 'select', props: {options: []/*this.erpInterfaceData.MAT_FLAG_MAP*/}},
        {key: 'transMode', title: '成交方式', type: 'pcode', props: {meta: 'TRANSAC'/*this.pcode.transac*/}},
        {key: 'licenseNo', title: '许可证号', type: 'input'},
        {key: 'declareName', title: '报关行'/*, type: 'select', props: {options: []this.cmbSource.declareCodeData}*/},
        {
          key: 'tradeNation', title: '贸易国(地区)', type: 'pcode',
          props: {meta: 'COUNTRY_OUTDATED'/*this.pcode.country_outdated*/}
        },
        {
          key: 'tradeCountry', title: this.tradeCountryLabel, type: 'pcode',
          props: {meta: 'COUNTRY_OUTDATED'/*this.pcode.country_outdated*/}
        },
        {key: 'destPort', title: this.destPortLabel, type: 'pcode', props: {meta: 'PORT_LIN'/*this.pcode.port_lin*/}},
        // {key: 'billType', title: '清单归并类型', type: 'select', props: {options: []/*this.importExportManage.billTypeMap*/}},
        // {
        //   key: 'mergeType', title: '报关单归并类型', type: 'select',
        //   props: {options: []/*this.importExportManage.mergeTypeMapFull*/}
        // },
        {key: 'note', title: '表头备注', type: 'input'},
        {key: 'insertUser', title: '制单员', type: 'select', props: {options: []/*this.cmbSource.userData*/}},
        {key: 'apprUser', title: '内审员', type: 'select', props: {options: []/*this.cmbSource.userData*/}},
        {
          type: 'dateRange', title: this.shipDateLabel, key: 'shipDate',
          fields: [{key: 'shipDateFrom'}, {key: 'shipDateTo'}]
        },
        // {key: 'carrier', title: '承运人', type: 'input'},
        // {key: 'plateNum', title: '车牌号', type: 'input'},
        // {key: 'deliveryPerson', title: this.deliveryPersonLabel, type: 'input'}
        {
          type: 'xdoInput', title: '总税款', key: 'taxTotal',
          props: {intDigits: 11, precision: 5}
        },
        {
          type: 'xdoInput', title: '增值税', key: 'taxPrice',
          props: {intDigits: 11, precision: 5}
        },
        /*{
          type: 'xdoInput', title: '关税', key: 'dutyPrice',
          props: {intDigits: 11, precision: 5}
        },*/
        //{key: 'insertWeek', title: '制单周次', type: 'input'}
        {
          key: 'confirmSpecial', title: '特殊关系确认', type: 'select',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        {
          key: 'confirmPrice', title: '价格影响确认', type: 'select',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        {
          key: 'confirmRoyalties', title: '支付特许权使用费确认', type: 'select',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        {
          key: 'dutySelf', title: '自报自缴', type: 'select',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        {
          type: 'xdoInput', title: '总金额', key: 'totalAll',
          props: {intDigits: 11, precision: 5}
        }
      ]
    },
    /**
     * 进口表头条件
     * @returns {*[]}
     */
    getHeadIParams() {
      return [
        {key: 'despPort', title: '启运港', type: 'pcode', props: {meta: 'PORT_LIN'/*this.pcode.port_lin*/}},
        // {
        //   type: 'dateRange', title: '到港日期', key: 'arrivalPortDate',
        //   fields: [{key: 'arrivalPortDateFrom'}, {key: 'arrivalPortDateTo'}]
        // },
        // {key: 'portContacts', title: '口岸联系人', type: 'input'},
        {
          type: 'dateRange', title: '到货通知日期', key: 'noticeDate',
          fields: [{key: 'noticeDateFrom'}, {key: 'noticeDateTo'}]
        },
        {
          key: 'damageMark', title: '破损标记', type: 'select',
          props: {options: []/*this.importExportManage.damageMarkMap*/}
        },
        {key: 'transNo', title: '转关单号', type: 'input'},
        {key: 'transPort', title: '转入口岸', type: 'pcode', props: {meta: 'CUSTOMS_REL'/*this.pcode.customs_rel*/}},
        {
          type: 'xdoInput', title: '其他进口环节税', key: 'otherTax',
          props: {intDigits: 11, precision: 5}
        },
        {
          type: 'dateRange', title: '到港日期', key: 'arrivalPortDate',
          fields: [{key: 'arrivalPortDateFrom'}, {key: 'arrivalPortDateTo'}]
        },
        {
          type: 'dateRange', title: '到厂日期', key: 'deliveryDate',
          fields: [{key: 'deliveryDateFrom'}, {key: 'deliveryDateTo'}]
        },
        {key: 'remark', title: '内部备注', type: 'input'},

        {
          type: 'dateRange', title: '完税日期', key: 'dutyDate',
          fields: [{key: 'dutyDateFrom'}, {key: 'dutyDateTo'}]
        },
        {
          type: 'dateRange', title: '商检查验日期', key: 'checkDate',
          fields: [{key: 'checkDateFrom'}, {key: 'checkDateTo'}]
        },
        {
          type: 'dateRange', title: '海关查验日期', key: 'customsCheckDate',
          fields: [{key: 'customsCheckDateFrom'}, {key: 'customsCheckDateTo'}]
        }
      ]
    },
    /**
     * 出口表头条件
     * @returns {*[]}
     */
    getHeadEParams() {
      return [
        /*{
          type: 'dateRange', title: '订舱日期', key: 'bookingDate',
          fields: [{key: 'bookingDateFrom'}, {key: 'bookingDateTo'}]
        },*/
        // {key: 'orderNo', title: '销售订单号', type: 'input'},
        //{key: 'shipmentNo', title: '出货通知书号', type: 'input'},
        //{key: 'inOutNo', title: '转关单号', type: 'input'},
        //{key: 'storageNo', title: '进仓编号', type: 'input'},
        //{key: 'freightNo', title: '运费凭据号', type: 'input'},
        {
          type: 'dateRange', title: '递送日期', key: 'deliveryDate',
          fields: [{key: 'deliveryDateFrom'}, {key: 'deliveryDateTo'}]
        },
        {key: 'financeNo', title: '财务单据号', type: 'input'},
        {key: 'shipTo', title: 'shipTo', type: 'input'},
        {
          type: 'xdoInput', title: '其他出口环节税', key: 'otherTax',
          props: {intDigits: 11, precision: 5}
        },
        {key: 'remark', title: '内部备注', type: 'input'},
        {key: 'mawb', title: '主提运单号', type: 'input'}
      ]
    },
    /**
     * 表头查询字段默认值(进口)
     * @returns {string[]}
     */
    getHeadIDefault() {
      return ['erpEmsListNo', 'emsNo', 'erpInsertTime', 'taxTotal', 'otherTax', 'taxPrice', 'dutyPrice', 'arrivalPortDate', 'insertWeek']
    },
    /**
     * 表头查询字段默认值(出口)
     * @returns {string[]}
     */
    getHeadEDefault() {
      return ['erpEmsListNo', 'emsNo', 'erpInsertTime', 'taxTotal', 'otherTax', 'taxPrice', 'dutyPrice', 'insertWeek']
    },
    /**
     * 表体通用条件
     * @returns {Array}
     */
    getBodyCommParams() {
      return [
        {key: 'facGNo', title: '企业料号', type: 'input'},
        {key: 'copGNo', title: '备案料号', type: 'input'},
        {key: 'gno', title: '备案序号', type: 'input'},
        {
          key: 'listBondMark', title: '保完税标识', type: 'select',
          props: {options: []/*this.importExportManage.bondedFlagMap*/}
        },
        {
          type: 'select', key: 'listGMark', title: '表体物料类型',
          props: {options: []/*this.productClassify.GMARK_SELECT*/}
        },
        {
          type: 'select', key: 'listEmsNo', title: '表体备案号',
          props: {options: [], optionLabelRender: (opt) => opt.label}
        },
        // {
        //   key: 'entryStatus', title: '报关单状态', type: 'select',
        //   props: {options: []}
        // },
        {key: 'codeTS', title: '商品编码', type: 'input'},
        {key: 'gname', title: '商品名称', type: 'input'},
        {key: 'gmodel', title: '申报规格型号', type: 'input'},
        {key: 'copGName', title: '中文名称', type: 'input'},
        {key: 'copGModel', title: '料号申报要素', type: 'input'},
        {key: 'unit', title: '计量单位', type: 'pcode', props: {meta: 'UNIT'/*this.pcode.unit*/}},
        {key: 'unit1', title: '法一单位', type: 'pcode', props: {meta: 'UNIT'/*this.pcode.unit*/}},
        {key: 'unit2', title: '法二单位', type: 'pcode', props: {meta: 'UNIT'/*this.pcode.unit*/}},
        {key: 'curr', title: '币制', type: 'pcode', props: {meta: 'CURR_OUTDATED'/*this.pcode.curr_outdated*/}},
        {key: 'listTradeMode', title: '表体监管方式', type: 'pcode', props: {meta: 'TRADE'/*this.pcode.trade*/}},
        {key: 'dutyMode', title: '征免方式', type: 'pcode', props: {meta: 'LEVYMODE'/*this.pcode.levymode*/}},
        {key: 'listInvoiceNo', title: '表体发票号码', type: 'input'},
        {
          type: 'select', key: 'supplierCode', title: this.supplierCodeLabel,
          props: {options: []}
        },
        {key: 'exgVersion', title: '单耗版本号', type: 'input'},
        {key: 'linkedNo', title: '提取单号', type: 'input'},
        {key: 'lineNo', title: '提取单序号', type: 'input'},
        {key: 'costCenter', title: '成本中心', type: 'input'},
        {key: 'note1', title: 'Remark1', type: 'input'},
        {key: 'note2', title: 'Remark2', type: 'input'},
        {key: 'note3', title: 'Remark3', type: 'input'},
        {
          type: 'select', title: this.districtCodeLabel, key: 'districtCode',
          fields: [{key: 'districtCode'}, {key: 'districtPostCode'}]
        }
      ]
    },
    /**
     * 进口表体条件
     * @returns {Array}
     */
    getBodyIParams() {
      return [
        {
          key: 'originCountry', title: '原产国', type: 'pcode',
          props: {meta: 'COUNTRY_OUTDATED'/*this.pcode.country_outdated*/}
        },
        {key: 'buyer', title: '采购人员', type: 'input'},
        {key: 'orderNo', title: '采购订单号', type: 'input'},
        {key: 'inOutNo', title: '入库关联单号', type: 'input'}
      ]
    },
    /**
     * 出口表体条件
     * @returns {Array}
     */
    getBodyEParams() {
      return [
        {key: 'customerOrderNo', title: '客户订单号', type: 'input'},
        {key: 'customerGNo', title: '客户料号', type: 'input'},
        {
          key: 'destinationCountry', title: '最终目的国', type: 'pcode',
          props: {meta: 'COUNTRY_OUTDATED'/*this.pcode.country_outdated*/}
        },
        {key: 'shipTo', title: 'shipTo', type: 'input'},
        {key: 'orderNo', title: '销售订单号', type: 'input'},
        {key: 'inOutNo', title: '出库关联单号', type: 'input'}
      ]
    },
    /**
     * 表体查询字段默认值(进口)
     * @returns {string[]}
     */
    getBodyIDefault() {
      return ['listGMark', 'listEmsNo', 'unit', 'curr', 'supplierCode', 'districtCode']
    },
    /**
     * 表体查询字段默认值(出口)
     * @returns {string[]}
     */
    getBodyEDefault() {
      return ['listGMark', 'listEmsNo', 'unit', 'curr', 'supplierCode', 'districtCode']
    },
    /**
     * 获取查询条件默认值
     * @returns {*|string[]}
     */
    getDefaultParams() {
      let me = this
      if (me.ieMark === 'I') {
        if (me.isHead()) {
          return me.getHeadIDefault()
        } else {
          return me.getBodyIDefault()
        }
      } else {
        if (me.isHead()) {
          return me.getHeadEDefault()
        } else {
          return me.getBodyEDefault()
        }
      }
    },
    /**
     * 获取表头表体组合时需要排除的条件
     */
    getExceptFieldsInBody() {
      return ['headCurr']
    },
    /**
     * 获取完整的查询条件(根据条件)
     */
    getElements() {
      let me = this
      // 表头通用条件
      let headCommParams = me.getHeadCommParams()
      // 进口表头条件
      let headIParams = me.getHeadIParams()
      // 出口表头条件
      let headEParams = me.getHeadEParams()
      // 表体通用条件
      let bodyCommParams = me.getBodyCommParams()
      // 进口表体条件
      let bodyIParams = me.getBodyIParams()
      // 出口表体条件
      let bodyEParams = me.getBodyEParams()
      // 获取表头表体组合时需要排除的条件
      let exceptFieldsInBody = me.getExceptFieldsInBody()
      // 分析处理
      if (me.ieMark === 'I') {
        if (me.isHead()) {
          return [...headCommParams, ...headIParams]
        } else {
          return [...headCommParams, ...headIParams, ...bodyCommParams, ...bodyIParams].filter(item => {
            return !exceptFieldsInBody.includes(item.key)
          })
        }
      } else {
        if (me.isHead()) {
          return [...headCommParams, ...headEParams]
        } else {
          return [...headCommParams, ...headEParams, ...bodyCommParams, ...bodyEParams].filter(item => {
            return !exceptFieldsInBody.includes(item.key)
          })
        }
      }
    },
    /**
     * 获取具有固定数据源的数据源对象
     * @returns {{listBondMark: (importExportManage.bondedFlagMap|{value, label}), apprStatus: (importExportManage.auditStatusMap|{value, label}), billStatus: (importExportManage.entryStatus|{value, label}), entryStatus: (importExportManage.entryStatus|{value, label}), gmark: (erpInterfaceData.MAT_FLAG_MAP|{value, label}), billType: (importExportManage.billTypeMap|{value, label}), mergeType: (importExportManage.mergeTypeMapFull|{value, label}), damageMark: (importExportManage.damageMarkMap|{value, label}), listGMark: (productClassify.GMARK_SELECT|{value, label}|*[])}}
     */
    getOptionsSource() {
      return {
        'gmark': this.erpInterfaceData.MAT_FLAG_MAP,
        'listGMark': this.productClassify.GMARK_SELECT,
        'billType': this.importExportManage.billTypeMap,
        'billStatus': this.importExportManage.entryStatus,
        // 'entryStatus': this.importExportManage.entryType,
        'entryStatus': this.cmbSource.entryStatusData,
        'damageMark': this.importExportManage.damageMarkMap,
        'apprStatus': this.importExportManage.auditStatusMap,
        'mergeType': this.importExportManage.mergeTypeMapFull,
        'listBondMark': this.importExportManage.bondedFlagMap,

        'confirmSpecial': this.promiseItems,
        'confirmPrice': this.promiseItems,
        'confirmRoyalties': this.promiseItems,
        'dutySelf': this.promiseItems
      }
    },
    /**
     * 获取动态标签
     */
    getDynamicLabels() {
      return {
        iedate: this.iedateLabel,
        ieport: this.ieportLabel,
        destPort: this.destPortLabel,
        shipDate: this.shipDateLabel,
        entryPort: this.entryPortLabel,
        tradeCountry: this.tradeCountryLabel,
        supplierCode: this.supplierCodeLabel,
        districtCode: this.districtCodeLabel,
        deliveryPerson: this.deliveryPersonLabel,
        overseasShipper: this.overseasShipperLabel
      }
    }
  }
}
