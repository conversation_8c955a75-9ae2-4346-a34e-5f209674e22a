<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" disabled labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'shipmentEdit',
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        cmbSource: {
          bondMark: [{value: '0', label: '免'}, {value: '1', label: '课'}]
        },
        buttons: [
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    methods: {
      getFields() {
        return [{
          key: 'no',
          title: 'NO'
        }, {
          key: 'copGNameEn',
          title: 'DESCRIPTION'
        }, {
          key: 'facGNo',
          title: '料号'
        }, {
          key: 'invoiceNo',
          title: 'INV NO'
        }, {
          key: 'trafMode',
          title: '运输方式',
          type: 'pcode',
          props: {
            meta: 'TRANSF' // this.pcode.transf
          }
        }, {
          key: 'orderNo',
          title: 'PO NO'
        }, {
          key: 'model',
          title: '机种'
        }, {
          type: 'select',
          title: '课/免',
          key: 'bondMark'
        }, {
          title: '序号',
          key: 'serialNo'
        }, {
          key: 'gname',
          title: '中文名称'
        }, {
          key: 'gmodel',
          title: 'TYPE'
        }, {
          key: 'decPrice',
          title: 'Unit Price(JPY)'
        }, {
          key: 'qty',
          title: 'QTY'
        }, {
          key: 'unit',
          title: 'UNIT',
          type: 'pcode',
          props: {
            meta: 'UNIT' // this.pcode.unit
          }
        }, {
          key: 'decTotal',
          title: 'Total Price(JPY)'
        }, {
          key: 'netWt',
          title: 'Net Weight(KG)'
        }, {
          key: 'caseNo',
          title: 'CASE_NO'
        }, {
          key: 'originCountry',
          title: 'Original',
          type: 'pcode',
          props: {
            meta: 'COUNTRY_OUTDATED' // this.pcode.country_outdated
          }
        }, {
          key: 'singleWt',
          title: '单重(KG)'
        }, {
          title: 'A/L',
          key: 'regulatoryConditions'
        }, {
          key: 'unit1',
          title: '法定第一单位',
          type: 'pcode',
          props: {
            meta: 'UNIT' // this.pcode.unit
          }
        }, {
          key: 'qty1',
          title: '法定第一数量'
        }, {
          title: '特殊关系确认',
          key: 'confirmSpecial'
        }, {
          key: 'confirmPrice',
          title: '价格影响确认'
        }, {
          key: 'confirmRoyalties',
          title: '支付特许权使用费确认'
        }]
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
