import { isNullOrEmpty } from '@/libs/util'
import ShipmentEdit from '../shipment-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const shipmentList = {
  name: 'shipmentList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    ShipmentEdit
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      cmbSource: {
        copEmsNo: []
      },
      toolbarEventMap: {
        'export1': this.handleDownload1,
        'export2': this.handleDownload2,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 设置显示罗列
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this,
        newColumns = [...me.getDefaultColumns(), ...columns]
      me.$set(me.listConfig, 'columns', newColumns.filter(col => {
        return col.type !== 'selection'
      }))
      me.listSetupShow = false
    },
    getParams() {
      return [{
        key: 'invoiceNo',
        title: '发票号码'
      }]
    },
    getFields() {
      return [{
        width: 80,
        key: 'no',
        title: 'NO'
      }, {
        width: 150,
        tooltip: true,
        key: 'copGNameEn',
        title: 'DESCRIPTION'
      }, {
        width: 150,
        key: 'facGNo',
        title: '料号'
      }, {
        width: 150,
        key: 'invoiceNo',
        title: 'INV NO'
      }, {
        width: 110,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 150,
        key: 'orderNo',
        title: 'PO NO'
      }, {
        width: 150,
        key: 'model',
        title: '机种'
      }, {
        width: 100,
        title: '课/免',
        key: 'bondMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [{value: '0', label: '免'}, {value: '1', label: '课'}])
        }
      }, {
        width: 100,
        title: '序号',
        key: 'serialNo'
      }, {
        width: 150,
        key: 'gname',
        tooltip: true,
        title: '中文名称'
      }, {
        width: 150,
        key: 'gmodel',
        tooltip: true,
        title: 'TYPE'
      }, {
        width: 160,
        key: 'decPrice',
        title: 'Unit Price(JPY)'
      }, {
        width: 150,
        key: 'qty',
        title: 'QTY'
      }, {
        width: 150,
        key: 'unit',
        title: 'UNIT',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 160,
        key: 'decTotal',
        title: 'Total Price(JPY)'
      }, {
        width: 150,
        key: 'netWt',
        title: 'Net Weight(KG)'
      }, {
        width: 150,
        key: 'caseNo',
        title: 'CASE_NO'
      }, {
        width: 150,
        key: 'originCountry',
        title: 'Original',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 150,
        key: 'singleWt',
        title: '单重(KG)'
      }, {
        width: 150,
        title: 'A/L',
        tooltip: true,
        key: 'regulatoryConditions'
      }, {
        width: 150,
        key: 'unit1',
        title: '法定第一单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 150,
        key: 'qty1',
        title: '法定第一数量'
      }, {
        width: 120,
        title: '特殊关系确认',
        key: 'confirmSpecial'
      }, {
        width: 120,
        key: 'confirmPrice',
        title: '价格影响确认'
      }, {
        width: 160,
        key: 'confirmRoyalties',
        title: '支付特许权使用费确认'
      }]
    },
    handleTableColumnSetup() {
      let me = this
      me.listSetupShow = true
    },
    /**
     * 导出1
     */
    handleDownload1() {
      let me = this,
        params = me.getSearchParams()
      me.doExport(me.ajaxUrl.export1, {'invoiceNo': params.invoiceNo}, me.actions.findIndex(it => it.command === 'export1'))
    },
    /**
     * 导出2
     */
    handleDownload2() {
      let me = this,
        params = me.getSearchParams()
      if (isNullOrEmpty(params.invoiceNo)) {
        me.$Message.warning('导出前请输入查询条件【发票号码】!')
        return
      }
      me.$http.post(me.ajaxUrl.checkData + '/' + params.invoiceNo).then(() => {
        me.doExport(me.ajaxUrl.export2 + '/' + params.invoiceNo, me.actions.findIndex(it => it.command === 'export2'))
      }).catch(() => {
      })
    }
  }
}
