import { colsIConfig, colsEConfig } from './billOfLadingHeadColumns'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const commOtherCols = [
  'batchNo'
  ,'facGNo'
  , 'copGNo'
  , 'gno'
  , 'serialNo'
  , 'entryGNo'
  , 'listGMark'
  , 'copMark'
  , 'listBondMark'
  , 'listEmsNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'copGName'
  , 'copGModel'
  , 'unit'
  , 'qty'
  , 'unit1'
  , 'qty1'
  , 'unit2'
  , 'qty2'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'netWt'
  , 'grossWt'
  , 'volume'
  , 'originCountry'
  , 'destinationCountry'
  , 'listTradeMode'
  , 'dutyMode'
  , 'orderNo'
  , 'listInvoiceNo'
  , 'supplierName'
  , 'exgVersion'
  , 'districtCode'
  , 'districtPostCode'
  , 'linkedNo'
  , 'lineNo'
  , 'costCenter'
  , 'note1'
  , 'note2'
  , 'note3'
  , 'listNote'
  , 'itemNo'
  , 'customsCheckResult'
  , 'passDate'
  , 'inOutNo'
  // , 'entryStatus'
]

// 进口提单
const colsIAllConfig = [
  ...colsIConfig
  , ...commOtherCols
  , 'buyer'
  , 'financeNo'
  , 'decTaxPrice'
  , 'dutyValue'
  , 'taxValue'
  , 'exciseTax'
  , 'dutyValueInterest'
  , 'taxValueInterest'
  , 'decOtherTax'
  , 'decTaxTotal'
]

// 出口提单
const colsEAllConfig = [
  ...colsEConfig
  , ...commOtherCols
  , 'customerOrderNo'
  , 'customerGNo'
  , 'invoiceDate'
]

const columnsAll = {
  mixins: [columnRender],
  data() {
    return {
      totalColumnsOther: [
        {
          width: 120,
          key: 'batchNo',
          tooltip: true,
          title: '批次号'
        },{
          width: 120,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 100,
          key: 'copGNo',
          tooltip: true,
          title: '备案料号'
        },
        {
          width: 70,
          key: 'gno',
          tooltip: true,
          title: '备案序号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'serialNo',
          title: '清单商品序号'
        },
        {
          width: 130,
          tooltip: true,
          key: 'entryGNo',
          title: '报关单商品序号'
        }, {
          width: 120,
          key: 'copMark',
          title: '企业物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.copMarkList)
          }
        },
        {
          width: 100,
          key: 'listGMark',
          title: '表体物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.copMarkMap)
          }
        },
        {
          width: 120,
          key: 'listBondMark',
          title: '表体保完税标记',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'listEmsNo',
          title: '表体备案号'
        },
        {
          width: 100,
          key: 'codeTS',
          tooltip: true,
          title: '商品编码'
        },
        {
          width: 130,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 130,
          key: 'gmodel',
          tooltip: true,
          title: '规格型号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'copGName',
          title: '中文名称'
        },
        // {
        //   width: 120,
        //   title: '报关单状态',
        //   key: 'entryStatus',
        //   render: (h, params) => {
        //     return this.cmbShowRender(h, params, this.cmbSource.entryStatusData)
        //   }
        // },
        {
          width: 120,
          tooltip: true,
          key: 'copGModel',
          title: '料号申报要素'
        },
        {
          width: 130,
          key: 'unit',
          tooltip: true,
          title: '计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          key: 'qty',
          width: 120,
          tooltip: true,
          title: '申报数量'
        },
        {
          width: 90,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'qty1',
          tooltip: true,
          title: '法一数量'
        },
        {
          width: 90,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'qty2',
          tooltip: true,
          title: '法二数量'
        },
        {
          width: 120,
          tooltip: true,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 120,
          tooltip: true,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 150,
          key: 'curr',
          title: '币制',
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'netWt',
          title: '净重',
          tooltip: true
        },
        {
          width: 120,
          title: '毛重',
          tooltip: true,
          key: 'grossWt'
        },
        {
          width: 120,
          title: '体积',
          key: 'volume',
          tooltip: true
        },
        {
          width: 120,
          key: 'buyer',
          tooltip: true,
          title: '采购人员'
        },
        {
          width: 120,
          tooltip: true,
          title: '客户订单号',
          key: 'customerOrderNo'
        },
        {
          width: 120,
          tooltip: true,
          title: '客户料号',
          key: 'customerGNo'
        },
        {
          width: 120,
          title: '表体原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          title: '表体目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 130,
          tooltip: true,
          title: '表体监管方式',
          key: 'listTradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 120,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'orderNo',
          title: '订单号码'
        },
        {
          width: 120,
          tooltip: true,
          title: '表体发票号码',
          key: 'listInvoiceNo'
        },
        {
          width: 120,
          tooltip: true,
          key: 'exgVersion',
          title: '单耗版本号'
        },
        {
          width: 200,
          tooltip: true,
          key: 'districtCode',
          title: '境内(货源/目的)地(国内地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 200,
          tooltip: true,
          key: 'districtPostCode',
          title: '境内(货源/目的)地(行政区划)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'linkedNo',
          title: '提取单号'
        },
        {
          width: 120,
          key: 'lineNo',
          tooltip: true,
          title: '提取单序号'
        },
        {
          width: 120,
          tooltip: true,
          title: '成本中心',
          key: 'costCenter'
        },
        {
          width: 120,
          key: 'note1',
          tooltip: true,
          title: 'Remark1'
        },
        {
          width: 120,
          key: 'note2',
          tooltip: true,
          title: 'Remark2'
        },
        {
          width: 120,
          key: 'note3',
          tooltip: true,
          title: 'Remark3'
        },
        {
          width: 120,
          tooltip: true,
          key: 'listNote',
          title: '表体备注'
        },
        {
          width: 100,
          key: 'itemNo',
          tooltip: true,
          title: '模拟项号'
        },
        {
          width: 120,
          tooltip: true,
          title: '海关查验结果',
          key: 'customsCheckResult'
        },
        {
          width: 150,
          title: '完税价格',
          key: 'decTaxPrice'
        }, {
          width: 150,
          title: '关税',
          key: 'dutyValue'
        }, {
          width: 150,
          title: '增值税',
          key: 'taxValue'
          // }, {
          //   width: 150,
          //   title: '消费税',
          //   key: 'exciseTax'
        }, {
          width: 150,
          title: '关税缓税利息',
          key: 'dutyValueInterest'
        }, {
          width: 150,
          title: '增值税缓税利息',
          key: 'taxValueInterest'
        }, {
          width: 150,
          key: 'decOtherTax',
          title: '其他进口环节税'
        }, {
          width: 160,
          key: 'decTaxTotal',
          title: '税金汇总'
        }, {
          width: 160,
          key: 'inOutNo',
          title: '入/出库关联单号'
        }, {
          width: 120,
          tooltip: true,
          key: 'invoiceDate',
          title: '发票日期'
        }
        // , {
        //   width: 200,
        //   tooltip: true,
        //   title: '境外发货人代码',
        //   key: 'overseasShipper',
        //   render: (h, params) => {
        //     return this.keyValueRender(h, params, 'overseasShipper', 'overseasShipperName')
        //   }
        // }
      ]
    }
  }
}

export {
  colsIAllConfig,
  colsEAllConfig,
  columnsAll
}
