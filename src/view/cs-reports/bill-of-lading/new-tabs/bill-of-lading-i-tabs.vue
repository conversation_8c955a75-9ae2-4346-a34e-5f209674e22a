<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <HeadReport ref="head" :ie-mark="ieMark" :cmb-source="cmbSource"></HeadReport>
      </TabPane>
      <TabPane name="complexTab" label="表头和表体">
        <ComplexReport ref="body" :ie-mark="ieMark" :cmb-source="cmbSource"></ComplexReport>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { billOfLadingTabs } from './js/billOfLadingTabs'

  export default {
    name: 'billOfLadingITabs',
    mixins: [ billOfLadingTabs ],
    data () {
      return {
        ieMark: 'I'
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
