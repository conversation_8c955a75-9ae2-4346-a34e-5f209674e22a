<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSet">设置</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm class="dc-form-4" :model="searchParam" :fields="showElements" labelWidth="100"></DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
        <div style="white-space: nowrap; display: inline-flex;">
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 50px; color: red;">当前页面为上次统计结果，如需更新请点击[数据统计]按钮！</div>
<!--          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div>-->
          <div style="white-space: nowrap; font-weight: bold; width: 340px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后统计时间: {{jobInfo.lastTime}}
          </div>
        </div>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <FormSetup v-model="showFormSetup" :resId="formResId" :columns="elements"
               @updateColumns="handleUpdateSearch"></FormSetup>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="configColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { billOfLadingSearch4Config } from '../../js/search-panel/billOfLadingSearch4Config'
  import { colsIConfig, colsEConfig, columns } from '../../js/list-panel/billOfLadingHeadColumns'

  export default {
    name: 'billOfLadingHeadList',
    mixins: [columns, billOfLadingSearch4Config],
    data() {
      return {
        taskInfo: {
          taskCode: ''     // 添加任务使用的taskCode
        },
        ajaxUrl: {
          insertJob: csAPI.reportCenter.jobComm.insertJob,
          getLastJob: csAPI.reportCenter.jobComm.getLastJob
        }
      }
    },
    created: function () {
      let me = this
      me.getLastJobInfo()
      me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
      if (me.ieMark === 'I') {
        me.$set(me.gridConfig, 'exportTitle', '进口提单表头信息')
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.iBillOfLading.exportHeadUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.iBillOfLading.selectAllHeadPaged)
        me.$set(me.taskInfo, 'taskCode', 'I_DEC_ERP_HEAD')        // 添加任务使用的taskCode
      } else {
        me.$set(me.gridConfig, 'exportTitle', '出口提单表头信息')
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.eBillOfLading.exportHeadUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.eBillOfLading.selectAllHeadPaged)
        me.$set(me.taskInfo, 'taskCode', 'E_DEC_ERP_HEAD')        // 添加任务使用的taskCode
      }
    },
    mounted: function () {
      let me = this
      if (me.ieMark === 'I') {
        me.orginShowColumns = JSON.parse(JSON.stringify(colsIConfig))
        me.configColumns = getColumnsByConfig(me.totalColumns, colsIConfig)
      } else {
        me.orginShowColumns = JSON.parse(JSON.stringify(colsEConfig))
        me.configColumns = getColumnsByConfig(me.totalColumns, colsEConfig)
      }
      me.resetColumns()
      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = me.$bom3.showTableColumns(me.tableId, me.configColumns)
      me.gridConfig.gridColumns = columns
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    },
    computed: {
      jobType() {
        let me = this
        if (me.ieMark === 'I') {
          return 'REPORT_DEC_I_HEAD'
        }
        return 'REPORT_DEC_E_HEAD'
      }
    },
    methods: {
      isHead() {
        return true
      },
      getFormResId() {
        return this.$route.fullPath + '_head'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
