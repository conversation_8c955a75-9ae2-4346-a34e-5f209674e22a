import ImportPage from 'xdo-import'
import { importExportManage } from '@/view/cs-common'
import FreightManagementEdit from '../freight-management-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

export const freightManagementList = {
  name: 'freightManagementList',
  components: {
    ImportPage,
    FreightManagementEdit
  },
  mixins: [baseSearchConfig, baseListConfig, dynamicImport],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [],
      cmbSource: {
        copEmsNo: [],
        tradeTerms: []
      },
      listConfig: {
        colOptions: true
      },
      importShow: false,
      toolbarEventMap: {
        'import': this.handleImport,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  mounted: function () {
    let me = this,
      fields = me.getFields(),
      commPreFields = me.getCommPreFields(),
      commAfterFields = me.getCommAfterFields()
    me.$set(me, 'baseFields', [...commPreFields, ...fields, ...commAfterFields])
  },
  computed: {
    importConfig() {
      let me = this,
        importConfig = me.getCommImportConfig(me.importKeys.taskCode, {
          accessToken: me.$store.state.token
        })
      return {
        config: importConfig,
        key: me.importKeys.key
      }
    }
  },
  methods: {
    /**
     * 加载额外的数据源
     */
    loadCmbSource() {
      let me = this
      // 贸易条款
      me.pcodeList(me.pcode.transac).then(res => {
        me.$set(me.cmbSource, 'tradeTerms', [...importExportManage.tradeTermList, ...deepClone(res)])
      }).catch(() => {
        me.$set(me.cmbSource, 'tradeTerms', importExportManage.tradeTermList)
      }).finally(() => {
        // me.resetDynamicSource('tradeTerms', me.dynamicSource.tradeTerms)
      })
    },
    /**
     * 设置显示罗列
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this,
        newColumns = [...me.getDefaultColumns(), ...columns]
      me.$set(me.listConfig, 'columns', newColumns.filter(col => {
        return col.type !== 'selection'
      }))
      me.listSetupShow = false
    },
    getParams() {
      return [{
        key: 'invoiceNo',
        title: '发票号码'
      }]
    },
    getCommPreFields() {
      return [{
        width: 150,
        key: 'invoiceNo',
        title: '发票号码'
      }, {
        width: 150,
        key: 'sumDecTotal',
        title: '发票总金额'
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 150,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 150,
        title: '体积',
        key: 'volume'
      }, {
        width: 130,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 130,
        title: '贸易条款',
        key: 'tradeTerms'//,
        // render: (h, params) => {
        //   return this.cmbShowRender(h, params, this.dynamicSource.tradeTerms)
        // }
        // width: 120,
        // key: 'transMode',
        // title: '成交方式',
        // render: (h, params) => {
        //   return this.cmbShowRender(h, params, [], this.pcode.transac)
        // }
      }]
    },
    getFields() {
      let me = this
      if (me.ieMark === 'I') {
        return [{
          width: 150,
          title: '国际段物流费',
          key: 'intLogisticsCost'
        }, {
          width: 150,
          key: 'intFreight',
          title: '国际运费'
        }, {
          width: 150,
          key: 'shCost',
          title: '上海段抽单费'
        }, {
          width: 150,
          title: '上海～苏州物流费',
          key: 'shSzLogisticsCost'
        }, {
          width: 150,
          key: 'szCost',
          title: '苏州段物流费'
        }]
      } else {
        return [{
          width: 150,
          title: '集卡费',
          key: 'cardCost'
        }, {
          width: 150,
          title: '报关费',
          key: 'customsCost'
        }, {
          width: 150,
          title: '订舱费',
          key: 'bookingCost'
        }, {
          width: 150,
          title: '操作费',
          key: 'operationCost'
        }, {
          width: 150,
          title: '磁检费',
          key: 'magneticCost'
        }]
      }
    },
    getCommAfterFields() {
      return [{
        width: 150,
        title: '查验费',
        key: 'checkCost'
      }, {
        width: 150,
        title: '其它物流费',
        key: 'otherLogisticsCost'
      }, {
        width: 150,
        key: 'freightTotal',
        title: '运费合计(CNY)'
      }]
    },
    handleTableColumnSetup() {
      let me = this
      me.listSetupShow = true
    },
    /**
     * 导入
     */
    handleImport() {
      let me = this
      me.$set(me, 'importShow', true)
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.$set(me, 'importShow', false)
      me.getList()
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
