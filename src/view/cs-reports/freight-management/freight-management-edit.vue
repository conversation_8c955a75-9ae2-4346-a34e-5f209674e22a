<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'freightManagementEdit',
    mixins: [baseDetailConfig],
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        ajaxUrl: {
          update: csAPI.reportCenter.freightManagement
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    created: function () {
      let me = this
      if (me.iEMark === 'I') {
        me.$set(me.ajaxUrl, 'update', csAPI.reportCenter.freightManagement.imports.update)
      } else {
        me.$set(me.ajaxUrl, 'update', csAPI.reportCenter.freightManagement.exports.update)
      }
    },
    methods: {
      getFields() {
        let me = this,
          commPreFields = [{
            key: 'invoiceNo',
            title: '发票号码',
            props: {
              disabled: true
            }
          }, {
            key: 'sumDecTotal',
            title: '发票总金额',
            props: {
              disabled: true
            }
          }, {
            key: 'curr',
            title: '币制',
            type: 'pcode',
            props: {
              disabled: true,
              meta: 'CURR_OUTDATED' // this.pcode.curr_outdated
            }
          }, {
            key: 'grossWt',
            title: '毛重',
            props: {
              disabled: true
            }
          }, {
            key: 'volume',
            title: '体积',
            props: {
              disabled: true
            }
          }, {
            key: 'trafMode',
            title: '运输方式',
            type: 'pcode',
            props: {
              disabled: true,
              meta: 'TRANSF' // this.pcode.transf
            }
          }, {
            // key: 'transMode',
            // title: '成交方式',
            // type: 'pcode',
            // props: {
            //   disabled: true,
            //   meta: 'TRANSAC' // this.pcode.transac
            // }
            type: 'select',
            title: '贸易条款',
            key: 'tradeTerms',
            props: {
              disabled: true,
              optionLabelRender: (it) => it.label
            }
          }]
        let specialFields = []
        if (me.iEMark === 'I') {
          specialFields = [{
            type: 'xdoInput',
            title: '国际段物流费',
            key: 'intLogisticsCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '国际运费',
            key: 'intFreight',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            key: 'shCost',
            title: '上海段抽单费',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '上海～苏州物流费',
            key: 'shSzLogisticsCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            key: 'szCost',
            title: '苏州段物流费',
            props: {
              intDigits: 13,
              precision: 5
            }
          }]
        } else {
          specialFields = [{
            type: 'xdoInput',
            title: '集卡费',
            key: 'cardCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '报关费',
            key: 'customsCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '订舱费',
            key: 'bookingCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '操作费',
            key: 'operationCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }, {
            type: 'xdoInput',
            title: '磁检费',
            key: 'magneticCost',
            props: {
              intDigits: 13,
              precision: 5
            }
          }]
        }
        let commAfterFields = [{
          type: 'xdoInput',
          title: '查验费',
          key: 'checkCost',
          props: {
            intDigits: 13,
            precision: 5
          }
        }, {
          type: 'xdoInput',
          title: '其它物流费',
          key: 'otherLogisticsCost',
          props: {
            intDigits: 13,
            precision: 5
          }
        }, {
          type: 'xdoInput',
          key: 'freightTotal',
          title: '运费合计(CNY)',
          props: {
            intDigits: 13,
            precision: 5
          }
        }]
        return [...commPreFields, ...specialFields, ...commAfterFields]
      },
      afterModelLoaded(isNew) {
        if (!isNew) {
          let me = this
          me.$set(me.detailConfig, 'model', deepClone(me.editConfig.editData))
        }
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
