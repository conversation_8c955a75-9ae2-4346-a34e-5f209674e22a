import { isNullOrEmpty } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const exportDocumentStatisticsList = {
  name: 'exportDocumentStatisticsList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'export': this.handleDownload,
        'statistics': this.handleStatistics,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'emsNo',
        type: 'select',
        title: '备案号',
        props: {
          optionLabelRender: (opt) => opt.label
        }
      }, {
        type: 'pcode',
        props: {
          meta: 'TRANSF'
        },
        key: 'trafMode',
        title: '运输方式'
      }, {
        type: 'select',
        title: '货运代理',
        key: 'forwardCode'
      }, {
        title: '客户',
        type: 'select',
        key: 'overseasShipper'
      }, {
        title: '英文品名',
        key: 'copGNameEn'
      }, {
        title: '发票号',
        key: 'invoiceNo'
      }, {
        range: true,
        title: '出货日期',
        key: 'shipDate'
      }, {
        type:'select',
        title: '出货日期是否为空',
        key: 'shipDateFlag',
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 显示列表
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 128,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 168,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 156,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 168,
        key: 'voyageNo',
        title: '船名/航次',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.keyValueRender(h, params, 'trafName', 'voyageNo')
        })
      }, {
        width: 136,
        key: 'hawb',
        title: '提运单号'
      }, {
        width: 156,
        title: '货运代理',
        key: 'forwardCode',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.keyValueRender(h, params, 'forwardCode', 'forwardName')
        })
      }, {
        width: 136,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 156,
        title: '客户',
        key: 'overseasShipper',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.overseasShipper)
        })
      }, {
        width: 120,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 156,
        title: '英文品名',
        key: 'copGNameEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 156,
        key: 'gname',
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'qty',
        title: '数量'
      }, {
        width: 136,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 136,
        title: '总价',
        key: 'decTotal'
      }, {
        width: 136,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 136,
        key: 'netWt',
        title: '净重'
      }, {
        width: 136,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 136,
        title: '体积',
        key: 'volume'
      }, {
        width: 136,
        title: '箱数',
        key: 'cartonNum'
      }, {
        width: 136,
        title: '托数',
        key: 'palletNum'
      }, {
        width: 128,
        key: 'etd',
        title: 'ETD',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 128,
        key: 'eta',
        title: 'ETA',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 88,
        key: 'shipDate',
        title: '出货日期',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 150,
        key: 'tradeMode',
        title: '监管方式',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 200,
        title: '贸易条款',
        key: 'tradeTerms'
      }]
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
          me.handleSearchSubmit()
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
        me.handleSearchSubmit()
      })
    },
    /**
     * 数据统计
     */
    handleStatistics() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    }
  }
}
