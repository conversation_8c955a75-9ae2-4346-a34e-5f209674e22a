<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </XdoCard>
      <div class="action"
           style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader"
                         :customBaseUri="customBaseUri"/>
          </template>
        </xdo-toolbar>
        <div style="white-space: nowrap; display: inline-flex;">
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 36px; color: red;">
            当前页面为上次统计结果，如需更新请点击[数据统计]按钮
          </div>
          <!-- <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div> -->
          <div style="white-space: nowrap; font-weight: bold; width: 340px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span> 最后统计时间: {{jobInfo.lastTime}}
          </div>
        </div>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data"
                     :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { documentManage } from '@/view/cs-common/constant'
  import { interimVerification } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase, isNullOrEmpty } from '@/libs/util'
  import { dynamicExport } from '../../cs-common/dynamic-export/dynamicExport'
  import { exportDocumentStatisticsList } from './js/exportDocumentStatisticsList'

  export default {
    name: 'exportDocumentStatisticsList',
    mixins: [exportDocumentStatisticsList, dynamicExport],
    data() {
      return {
        jobInfo: {
          status: '',
          lastTime: '',
          interval: '',
          paramsStr: ''
        },
        cmbSource: {
          emsNo: [],
          forwardCode: [],
          overseasShipper: [],
          shipDateFlag: documentManage.isEmpty
        },
        jobType: 'DOCUMENTS_REPORT',
        listConfig: {
          checkColumnShow: false,
          operationColumnShow: false,
          exportTitle: '出口单证统计表'
        },
        taskInfo: {
          taskCode: 'TMK_DOCUMENTS_REPORT'
        },
        reportStatus: interimVerification.REPORT_STATUS_MAP,
        ajaxUrl: {
          insertJob: csAPI.reportCenter.jobComm.insertJob,
          getLastJob: csAPI.reportCenter.jobComm.getLastJob,
          getEEmsNo: csAPI.csProductClassify.bonded.getZtythEmsListNo,
          selectAllPaged: csAPI.reportCenter.exportDocumentStatistics.selectAllPaged
        }
      }
    },
    computed: {
      /**
       * 导出列头信息
       * @returns {{value: *, key: *}[]}
       */
      exportHeader() {
        let me = this
        return me.listConfig.columns.filter(column => {
          return !['selection', 'operation'].includes(column.key) && !isNullOrEmpty(column.key)
        }).map(theCol => {
          return {
            key: theCol.key,
            value: theCol.title
          }
        })
      },
      jobStatusName() {
        let me = this,
          theStatus = me.reportStatus.filter(item => {
            return item.value === me.jobInfo.status
          })
        if (Array.isArray(theStatus) && theStatus.length > 0) {
          return theStatus[0].label
        }
        return ''
      },
      jobStatusStyle() {
        let me = this
        if (['0', '1'].includes(me.jobInfo.status)) {
          return 'color: blue'
        } else if (me.jobInfo.status === '2') {
          return 'color: green'
        } else if (me.jobInfo.status === '3') {
          return 'color: red'
        }
        return  ''
      }
    },
    created() {
      let me = this
      me.getLastJobInfo()
      me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
      // emsNo  备案号
      me.$http.post(me.ajaxUrl.getEEmsNo).then(res => {
        me.$set(me.cmbSource, 'emsNo', res.data.data.filter(it => !isNullOrEmpty(it.emsNo)).map(item => {
          return {
            value: item.emsNo,
            label: item.emsNo
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'emsNo', [])
      }).finally(() => {
        me.searchFieldsReLoad('emsNo')
      })
      // forwardCode  货运代理
      me.$http.post(csAPI.ieParams.FOD).then(res => {
        me.$set(me.cmbSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'forwardCode', [])
      }).finally(() => {
        me.searchFieldsReLoad('forwardCode')
      })
      // overseasShipper  境外收货人
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.$set(me.cmbSource, 'overseasShipper', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'overseasShipper', [])
      }).finally(() => {
        me.searchFieldsReLoad('overseasShipper')
      })
    },
    beforeDestroy: function() {
      let me = this
      clearInterval(me.jobInfo.interval)
      me.$set(me.jobInfo, 'interval', '')
    }
  }
</script>

<style scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
