<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecBillHeadSearch ref="headSearch" :ie-mark="ieMark" :bond-mark="bondMark" :cmb-source="cmbSource"></DecBillHeadSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
        <div style="white-space: nowrap; display: inline-flex;">
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 50px; color: red;">当前页面为上次统计结果，如需更新请点击[数据统计]按钮！</div>
<!--          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div>-->
          <div style="white-space: nowrap; font-weight: bold; width: 340px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后统计时间: {{jobInfo.lastTime}}
          </div>
        </div>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" v-if="!showMerge" rowSelection="multiple"
                  :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 26px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="configColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { decBillList } from '../../js/list-panel/decBillList'
  import DecBillHeadSearch from '../search-panel/dec-bill-head-search'
  import { colsIBondedConfig, colsIUnBondedConfig,
           colsEBondedConfig, colsEUnBondedConfig,
           columns } from '../../js/list-panel/decBillHeadColumns'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'

  export default {
    name: 'decBillHeadList',
    components: {
      DecBillHeadSearch
    },
    mixins: [decBillList, columns],
    data() {
      return {
        isHead: true,
        gridConfig: {
          exportTitle: ''
        },
        ajaxUrl: {
          exportUrl: '',
          getSumData: '',
          selectAllPaged: '',
          insertJob: csAPI.reportCenter.jobComm.insertJob,
          getLastJob: csAPI.reportCenter.jobComm.getLastJob
        },
        taskInfo: {
          taskCode: ''     // 添加任务使用的taskCode
        },
        defaultIColumns: [
          {
            key: 'emsListNo',
            fixed: '-'
          }, {
            key: 'invoiceNo',
            fixed: '-'
          }, {
            key: 'entryNo',
            fixed: '-'
          }, {
            key: 'tradeMode',
            fixed: '-'
          }, {
            key: 'trafMode',
            fixed: '-'
          }, {
            key: 'transMode',
            fixed: '-'
          }, {
            key: 'entryDeclareDate',
            fixed: '-'
          }, {
            key: 'arrivalPortDate',
            fixed: '-'
          }, {
            key: 'gnameAll',
            fixed: '-'
          }, {
            key: 'qtyAll',
            fixed: '-'
          }, {
            key: 'totalAll',
            fixed: '-'
          }, {
            key: 'currAll',
            fixed: '-'
          }, {
            key: 'grossWtTotal',
            fixed: '-'
          }, {
            key: 'netWtTotal',
            fixed: '-'
          }, {
            key: 'volumeTotal',
            fixed: '-'
          }, {
            key: 'packNum',
            fixed: '-'
          }, {
            key: 'tradeCountry',
            fixed: '-'
          }, {
            key: 'overseasShipperName',
            fixed: '-'
          }, {
            key: 'declareName',
            fixed: '-'
          }, {
            key: 'forwardName',
            fixed: '-'
          }, {
            key: 'hawb',
            fixed: '-'
          }, {
            key: 'trafName',
            fixed: '-'
          }, {
            key: 'dutyPrice',
            fixed: '-'
          }, {
            key: 'taxPrice',
            fixed: '-'
          }, {
            key: 'wrapTypeName',
            fixed: '-'
          }, {
            key: 'containerType',
            fixed: '-'
          }, {
            key: 'containerNum',
            fixed: '-'
          }, {
            key: 'erpInsertUser',
            fixed: '-'
          }, {
            key: 'apprUserFull',
            fixed: '-'
          }, {
            key: 'note',
            fixed: '-'
          }, {
            key: 'costCenter',
            fixed: '-'
          },
          {
            key: 'headSupplierName',
            fixed: '-'
          }
        ],
        defaultEColumns: [
          {
            key: 'emsListNo',
            fixed: '-'
          }, {
            key: 'invoiceNo',
            fixed: '-'
          }, {
            key: 'entryNo',
            fixed: '-'
          }, {
            key: 'tradeMode',
            fixed: '-'
          }, {
            key: 'trafMode',
            fixed: '-'
          }, {
            key: 'entryPort',
            fixed: '-'
          }, {
            key: 'entryDeclareDate',
            fixed: '-'
          }, {
            key: 'transMode',
            fixed: '-'
          }, {
            key: 'wrapTypeName',
            fixed: '-'
          }, {
            key: 'gnameAll',
            fixed: '-'
          }, {
            key: 'qtyAll',
            fixed: '-'
          }, {
            key: 'totalAll',
            fixed: '-'
          }, {
            key: 'currAll',
            fixed: '-'
          }, {
            key: 'grossWtTotal',
            fixed: '-'
          }, {
            key: 'netWtTotal',
            fixed: '-'
          }, {
            key: 'volumeTotal',
            fixed: '-'
          }, {
            key: 'packNum',
            fixed: '-'
          }, {
            key: 'tradeCountry',
            fixed: '-'
          }, {
            key: 'overseasShipperName',
            fixed: '-'
          }, {
            key: 'declareName',
            fixed: '-'
          }, {
            key: 'forwardName',
            fixed: '-'
          }, {
            key: 'hawb',
            fixed: '-'
          }, {
            key: 'trafName',
            fixed: '-'
          }, {
            key: 'containerType',
            fixed: '-'
          }, {
            key: 'containerNum',
            fixed: '-'
          }, {
            key: 'erpInsertUser',
            fixed: '-'
          }, {
            key: 'apprUserFull',
            fixed: '-'
          }, {
            key: 'note',
            fixed: '-'
          },
          {
            key: 'headSupplierName',
            fixed: '-'
          }
        ]
      }
    },
    created: function () {
      let me = this
      me.getLastJobInfo()
      me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
      if (me.ieMark === 'I') {
        if (me.bondMark === '1') {
          me.$set(me.gridConfig, 'exportTitle', '进口非保税清单表头信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'I_BILL_HEAD_1')        // 添加任务使用的taskCode
        } else {
          me.$set(me.gridConfig, 'exportTitle', '进口保税清单表头信息')
          me.$set(me, 'searchLines', 6)
          me.$set(me.taskInfo, 'taskCode', 'I_BILL_HEAD_0')        // 添加任务使用的taskCode
        }
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.iBill.exportHead)
        me.$set(me.ajaxUrl, 'getSumData', csAPI.reportCenter.iBill.getHeadSum)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.iBill.selectAllHeadPaged)
      } else {
        if (me.bondMark === '1') {
          me.$set(me.gridConfig, 'exportTitle', '出口非保税清单表头信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'E_BILL_HEAD_1')        // 添加任务使用的taskCode
        } else {
          me.$set(me.gridConfig, 'exportTitle', '出口保税清单表头信息')
          me.$set(me, 'searchLines', 6)
          me.$set(me.taskInfo, 'taskCode', 'E_BILL_HEAD_0')        // 添加任务使用的taskCode
        }
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.eBill.exportHead)
        me.$set(me.ajaxUrl, 'getSumData', csAPI.reportCenter.eBill.getHeadSum)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.eBill.selectAllHeadPaged)
      }
    },
    mounted: function () {
      let me = this
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsIBondedConfig))
          me.configColumns = getColumnsByConfig(me.totalColumns, colsIBondedConfig)
        } else {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsIUnBondedConfig))
          me.configColumns = getColumnsByConfig(me.totalColumns, colsIUnBondedConfig)
        }
      } else {
        if (me.bondMark === '0') {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsEBondedConfig))
          me.configColumns = getColumnsByConfig(me.totalColumns, colsEBondedConfig)
        } else {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsEUnBondedConfig))
          me.configColumns = getColumnsByConfig(me.totalColumns, colsEUnBondedConfig)
        }
      }
      me.resetColumns()

      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = []
      if (me.ieMark === 'I') {
        columns = me.$bom3.showTableColumns(me.tableId, me.configColumns, me.defaultIColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, me.configColumns, me.defaultEColumns)
      }
      me.gridConfig.gridColumns = columns
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    },
    computed: {
      jobType() {
        let me = this
        if (me.ieMark === 'I') {
          return 'REPORT_BILL_I_HEAD'
        }
        return 'REPORT_BILL_E_HEAD'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
