<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            <XdoButton type="primary" class="dc-margin-right" v-if="showMerge" @click="handleMerge">{{mergeTitle}}</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecBillAllSearch ref="headSearch" :ie-mark="ieMark" :bond-mark="bondMark" :cmb-source="cmbSource"></DecBillAllSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="gridConfig.exportColumns" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
        <div style="white-space: nowrap; display: inline-flex;">
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 50px; color: red;">当前页面为上次统计结果，如需更新请点击[数据统计]按钮！</div>
<!--          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div>-->
          <div style="white-space: nowrap; font-weight: bold; width: 340px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后统计时间: {{jobInfo.lastTime}}
          </div>
        </div>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  v-if="!showMerge" rowSelection="multiple" :suppressRowTransform="suppressRowTransform"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <dc-merge-grid v-if="showMerge" :height="dynamicHeight" :columns="gridConfig.gridColumns" :data="gridConfig.data"
                       :in-source="cmbSource" :rowMerge="suppressRowTransform" :spanRows="rowSpanConfig.spanRows"></dc-merge-grid>
        <div ref="area_page" style="height: 26px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="configColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNumber } from '@/libs/util'
  import { decBillList } from '../../js/list-panel/decBillList'
  import DecBillAllSearch from '../search-panel/dec-bill-all-search'
  import { columns } from '../../js/list-panel/decBillHeadColumns'
  import { colsIBondedAllConfig, colsIUnBondedAllConfig,
           colsEBondedAllConfig, colsEUnBondedAllConfig,
           columnsAll } from '../../js/list-panel/decBillAllColumns'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'

  export default {
    name: 'decBillAllList',
    components: {
      DecBillAllSearch
    },
    mixins: [decBillList, columnsAll, columns],
    data() {
      return {
        isHead: false,
        suppressRowTransform: true,
        gridConfig: {
          exportTitle: ''
        },
        ajaxUrl: {
          exportUrl: '',
          getSumData: '',
          selectAllPaged: '',
          insertJob: csAPI.reportCenter.jobComm.insertJob,
          getLastJob: csAPI.reportCenter.jobComm.getLastJob
        },
        taskInfo: {
          taskCode: ''     // 添加任务使用的taskCode
        },
        defaultIColumns: [
          {
            key: 'emsListNo',
            fixed: '-'
          }, {
            key: 'invoiceNo',
            fixed: '-'
          }, {
            key: 'entryNo',
            fixed: '-'
          }, {
            key: 'entryPort',
            fixed: '-'
          }, {
            key: 'tradeMode',
            fixed: '-'
          }, {
            key: 'transMode',
            fixed: '-'
          }, {
            key: 'iedate',
            fixed: '-'
          }, {
            key: 'facGNo',
            fixed: '-'
          }, {
            key: 'gno',
            fixed: '-'
          }, {
            key: 'qty',
            fixed: '-'
          }, {
            key: 'unit',
            fixed: '-'
          }, {
            key: 'decTotal',
            fixed: '-'
          }, {
            key: 'decPrice',
            fixed: '-'
          }, {
            key: 'curr',
            fixed: '-'
          }, {
            key: 'netWt',
            fixed: '-'
          }, {
            key: 'codeTS',
            fixed: '-'
          }, {
            key: 'gname',
            fixed: '-'
          }, {
            key: 'originCountry',
            fixed: '-'
          }, {
            key: 'supplierName',
            fixed: '-'
          }, {
            key: 'declareName',
            fixed: '-'
          }, {
            key: 'dutyMode',
            fixed: '-'
          }, {
            key: 'erpInsertUser',
            fixed: '-'
          }
        ],
        defaultEColumns: [
          {
            key: 'emsListNo',
            fixed: '-'
          }, {
            key: 'invoiceNo',
            fixed: '-'
          }, {
            key: 'entryNo',
            fixed: '-'
          }, {
            key: 'entryPort',
            fixed: '-'
          }, {
            key: 'tradeMode',
            fixed: '-'
          }, {
            key: 'transMode',
            fixed: '-'
          }, {
            key: 'iedate',
            fixed: '-'
          }, {
            key: 'facGNo',
            fixed: '-'
          }, {
            key: 'exgVersion',
            fixed: '-'
          }, {
            key: 'gno',
            fixed: '-'
          }, {
            key: 'qty',
            fixed: '-'
          }, {
            key: 'unit',
            fixed: '-'
          }, {
            key: 'decTotal',
            fixed: '-'
          }, {
            key: 'decPrice',
            fixed: '-'
          }, {
            key: 'curr',
            fixed: '-'
          }, {
            key: 'netWt',
            fixed: '-'
          }, {
            key: 'codeTS',
            fixed: '-'
          }, {
            key: 'gname',
            fixed: '-'
          }, {
            key: 'destinationCountry',
            fixed: '-'
          }, {
            key: 'supplierName',
            fixed: '-'
          }, {
            key: 'declareName',
            fixed: '-'
          }, {
            key: 'dutyMode',
            fixed: '-'
          }, {
            key: 'erpInsertUser',
            fixed: '-'
          }
        ]
      }
    },
    created: function () {
      let me = this
      me.getLastJobInfo()
      me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
      if (me.ieMark === 'I') {
        if (me.bondMark === '1') {
          me.$set(me.gridConfig, 'exportTitle', '进口非保税清单综合信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'I_BILL_HEAD_LIST_1')        // 添加任务使用的taskCode
        } else {
          me.$set(me.gridConfig, 'exportTitle', '进口保税清单综合信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'I_BILL_HEAD_LIST_0')        // 添加任务使用的taskCode
        }
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.iBill.exportAll)
        me.$set(me.ajaxUrl, 'getSumData', csAPI.reportCenter.iBill.getListSum)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.iBill.selectAllPaged)
      } else {
        if (me.bondMark === '1') {
          me.$set(me.gridConfig, 'exportTitle', '出口非保税清单综合信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'E_BILL_HEAD_LIST_1')        // 添加任务使用的taskCode
        } else {
          me.$set(me.gridConfig, 'exportTitle', '出口保税清单综合信息')
          me.$set(me, 'searchLines', 5)
          me.$set(me.taskInfo, 'taskCode', 'E_BILL_HEAD_LIST_0')        // 添加任务使用的taskCode
        }
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.eBill.exportAll)
        me.$set(me.ajaxUrl, 'getSumData', csAPI.reportCenter.eBill.getListSum)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.eBill.selectAllPaged)
      }
    },
    mounted: function () {
      let me = this
      let allColumns = [...(me.totalColumns || []), ...(me.totalColumnsOther || [])].filter(item => {
        return !['gnameAll', 'currAll', 'costCenter', 'headSupplierName'].includes(item.key)
      })
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsIBondedAllConfig))
          me.configColumns = getColumnsByConfig(allColumns, colsIBondedAllConfig)
        } else {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsIUnBondedAllConfig))
          me.configColumns = getColumnsByConfig(allColumns, colsIUnBondedAllConfig)
        }
      } else {
        if (me.bondMark === '0') {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsEBondedAllConfig))
          me.configColumns = getColumnsByConfig(allColumns, colsEBondedAllConfig)
        } else {
          me.orginShowColumns = JSON.parse(JSON.stringify(colsEUnBondedAllConfig))
          me.configColumns = getColumnsByConfig(allColumns, colsEUnBondedAllConfig)
        }
      }
      me.resetColumns()

      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = []
      if (me.ieMark === 'I') {
        columns = me.$bom3.showTableColumns(me.tableId, me.configColumns, me.defaultIColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, me.configColumns, me.defaultEColumns)
        if (me.bondMark === '1' && me.suppressRowTransform) {
          columns.forEach(column => {
            if (me.rowSpanConfig.spanRows.includes(column.key)) {
              column.rowSpan = (params) => {
                if (params.data && isNumber(params.data['$rowSpan'])) {
                  return Number(params.data['$rowSpan'])
                }
                return 1
              }
              column['cellClassRules'] = {
                'show-cell': (params) => {
                  return params.data && isNumber(params.data['$rowSpan']) && Number(params.data['$rowSpan']) > 1
                }
              }
            }
          })
        }
      }
      me.gridConfig.gridColumns = columns
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    },
    computed: {
      showMerge() {
        let me = this
        return me.ieMark === 'E' && me.bondMark === '1'
      },
      mergeTitle() {
        let me = this
        if (me.suppressRowTransform) {
          return '栏位恢复'
        } else {
          return '栏位合并'
        }
      },
      jobType() {
        let me = this
        if (me.ieMark === 'I') {
          return 'REPORT_BILL_I_HEAD_LIST'
        }
        return 'REPORT_BILL_E_HEAD_LIST'
      }
    },
    methods: {
      handleMerge() {
        let me = this
        me.$set(me, 'suppressRowTransform', !me.suppressRowTransform)
        me.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .show-cell {
    background-color: white;
    border-bottom: 1px solid lightgrey;
  }
</style>
