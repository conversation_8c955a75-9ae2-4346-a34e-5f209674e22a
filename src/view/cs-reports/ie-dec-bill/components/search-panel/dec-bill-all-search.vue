<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="emsListNo" label="清单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号码">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :meta="pcode.trade"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="transMode" label="成交方式">
        <xdo-select v-model="searchParam.transMode" :meta="pcode.transac"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange :label="IEDateLabel" @onDateRangeChanged="handleIEDateDateChange"></dc-dateRange>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text"  v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text"  v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="supplierCode" :label="supplierCodeLabel">
        <xdo-select  v-model="searchParam.supplierCode" :options="this.cmbSource.supplierCodeData"
                     :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareCode" label="报关行">
        <XdoIInput type="text"  v-model="searchParam.declareName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="erpInsertUser" label="制单员">
        <XdoIInput type="text"  v-model="searchParam.erpInsertUser"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleErpInsertTimeChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="tradeTerms" label="贸易条款">
        <xdo-select v-model="searchParam.tradeTerms" :options="this.cmbSource.tradeTermNewData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="bondMark==='0'" prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <dc-dateRange v-if="bondMark==='1'" label="报关单申报日期" @onDateRangeChanged="handleEntryDeclareDateChange" ></dc-dateRange>

      <dc-dateRange v-if="bondMark==='0'" label='清单申报日期' @onDateRangeChanged='handleErpDeclareDateChange'></dc-dateRange>
<!--      <dc-dateRange v-if="bondMark==='1'" label='进出口日期' @onDateRangeChanged='handleIEDateChange'></dc-dateRange>-->
      <XdoFormItem v-if="bondMark==='0'" prop='billStatus' label='清单状态'>
        <xdo-select v-model='searchParam.billStatus' :options='this.cmbSource2.sendApiStatusData'
                    :optionLabelRender='pcodeRender'></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop='entryStatus' label='报关单状态'>
        <xdo-select v-model='searchParam.entryStatus' :options='this.cmbSource2.entryStatusData'
                    :optionLabelRender='pcodeRender'></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="bondMark==='0'" prop='copGNo' label='备案料号'>
        <XdoIInput type='text' v-model='searchParam.copGNo'></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { decBillSearch } from '../../js/search-panel/decBillSearch'
  import { importExportManage } from '@/view/cs-common'

  export default {
    name: 'decBillAllSearch',
    mixins: [decBillSearch],
    data() {
      return {
        searchParam: {
          bondMark: this.bondMark,
          iemark: this.ieMark,

          emsListNo: '',
          invoiceNo: '',
          entryNo: '',
          tradeMode: '',
          transMode: '',
          iedateFrom: '',
          iedateTo: '',
          facGNo: '',
          codeTS: '',
          gname: '',
          supplierCode: '',
          // declareCode: '',
          declareName: '',
          erpInsertUser: '',
          erpInsertTimeFrom: '',
          erpInsertTimeTo: '',
          tradeTerms: '',
          emsNo: '',
          declareDateFrom:'',
          declareDateTo:'',
          billStatus:'',
          entryStatus:'',
          copGNo:''
        },
        cmbSource2:{
          entryStatusData:importExportManage.entryStatus,
          sendApiStatusData: importExportManage.BILL_STATUS_MAP
        }
      }
    },
    methods: {
      handleIEDateDateChange (values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "iedateFrom", values[0])
          this.$set(this.searchParam, "iedateTo", values[1])
        } else {
          this.$set(this.searchParam, "iedateFrom", '')
          this.$set(this.searchParam, "iedateTo", '')
        }
      },
      handleErpInsertTimeChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "erpInsertTimeFrom", values[0])
          this.$set(this.searchParam, "erpInsertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "erpInsertTimeFrom", '')
          this.$set(this.searchParam, "erpInsertTimeTo", '')
        }
      },
      handleEntryDeclareDateChange (values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "entryDeclareDateFrom", values[0])
          this.$set(this.searchParam, "entryDeclareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "entryDeclareDateFrom", '')
          this.$set(this.searchParam, "entryDeclareDateTo", '')
        }
      },
      handleErpDeclareDateChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      },
      handleIEDateChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "iEDateFrom", values[0])
          this.$set(this.searchParam, "iEDateTo", values[1])
        } else {
          this.$set(this.searchParam, "iEDateFrom", '')
          this.$set(this.searchParam, "iEDateTo", '')
        }
      }
    },
    computed: {
      IEDateLabel () {
        if (this.ieMark === 'I') {
          return '进口日期'
        } else if (this.ieMark === 'E') {
          return '出口日期'
        } else {
          return '进出口日期'
        }
      },
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style scoped>
</style>
