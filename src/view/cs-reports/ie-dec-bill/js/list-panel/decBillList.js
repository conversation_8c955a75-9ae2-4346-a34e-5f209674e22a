import pms from '@/libs/pms'
import { isNullOrEmpty } from '@/libs/util'
import { getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { dcAgRowSpan } from '@/components/dc-ag-grid/js/dc-ag-row-span'
import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'
import { interimVerification, erpInterfaceData, importExportManage } from '@/view/cs-common'

export const decBillList = {
  mixins: [commList, pms, dcAgRowSpan, dynamicExport],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1'].includes(value)
      }
    },
    cmbSource: {
      type: Object,
      require: true,
      default: () => ({
        emsNoData: [],
        tradeTermNewData: []
      })
    }
  },
  data() {
    return {
      tableId: '',
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      tableShow: true,
      configColumns: [],
      hasChildTabs: true,
      totalAllContent: '',
      orginShowColumns: [],
      suppressRowTransform: false,
      showtableColumnSetup: false,
      rowSpanConfig: {
        pks: ['emsListNo'],
        spanRows: ['packNum', 'grossWtTotal']
      },
      erpInterfaceData: erpInterfaceData,
      importExportManage: importExportManage,
      interimVerification: interimVerification,
      toolbarEventMap: {
        'export': this.handleDownload,
        'statistics': this.handleStatistics,
        'setting': this.handleTableColumnSetup
      },
      reportStatus: interimVerification.REPORT_STATUS_MAP
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then(() => {
      me.actions.push({
        needed: true,
        loading: false,
        disabled: false,
        label: '数据统计',
        key: 'xdo-btn-delete',
        command: 'statistics',
        icon: 'ios-calculator-outline'
      }, {
        key: '',
        needed: true,
        label: '导出',
        loading: false,
        disabled: false,
        command: 'export',
        icon: 'ios-cloud-download-outline'
      }, {
        key: '',
        needed: true,
        loading: false,
        disabled: false,
        icon: 'ios-cog',
        command: 'setting',
        label: '自定义配置'
      })
    })
  },
  methods: {
    /**
     * 修改列标题
     * @param gCol
     */
    loadColumnTitle(gCol) {
      let me = this
      if (gCol.key === 'iedate') {
        gCol.title = me.iedateLabel
      } else if (gCol.key === 'overseasShipperName') {
        gCol.title = me.overseasShipperLabel
      } else if (gCol.key === 'receiveName') {
        gCol.title = me.receiveCodeLabel
      } else if (gCol.key === 'ieport') {
        gCol.title = me.ieportLabel
      } else if (gCol.key === 'entryPort') {
        gCol.title = me.entryPortLabel
      } else if (gCol.key === 'tradeCountry') {
        gCol.title = me.tradeCountryLabel
      } else if (gCol.key === 'destPort') {
        gCol.title = me.destPortLabel
      } else if (gCol.key === 'deliveryPerson') {
        gCol.title = me.deliveryPersonLabel
      } else if (gCol.key === 'shipDate') {
        gCol.title = me.shipDateLabel
      } else if (gCol.key === 'customsFee') {
        gCol.title = me.customsFeeLabel
      } else if (gCol.key === 'districtCode') {
        gCol.title = me.districtCodeLabel
      } else if (gCol.key === 'districtPostCode') {
        gCol.title = me.districtPostCodeLabel
      } else if (gCol.key === 'supplierName') {
        gCol.title = me.supplierNameLabel
      } else if (gCol.key === 'headSupplierName') {
        gCol.title =  me.headSupplierNameLabel
      }
    },
    /**
     * 根据配置信息重置列标题
     */
    resetColumns() {
      let me = this
      for (let mainCol of me.configColumns) {
        if (Array.isArray(mainCol.children) && mainCol.children.length > 0) {
          for (let gCol of mainCol.children) {
            me.loadColumnTitle(gCol)
          }
        } else {
          me.loadColumnTitle(mainCol)
        }
      }
    },
    /**
     * 执行查询
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.tableloading = true
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          if (me.ieMark === 'E' && me.bondMark === '1' && me.suppressRowTransform) {
            me.gridConfig.data = me.setMerge(res.data.data)
          } else {
            me.gridConfig.data = res.data.data
          }
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
          me.tableloading = false
        })
      })
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleTableColumnSetup() {
      let me = this
      me.showtableColumnSetup = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.gridConfig.exportColumns = []
      me.gridConfig.gridColumns = columns
      // 解决iView table 的问题
      me.tableShow = false
      me.$nextTick(() => {
        me.tableShow = true
      })
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      let params = me.getSearchParams()
      me.$http.post(me.ajaxUrl.getSumData, params).then(res => {
        let totalContentAll = ''
        if (me.isHead) {
          let netWtTotal = Number(isNullOrEmpty(res.data.data.netWtTotal) ? 0 : res.data.data.netWtTotal.toFixed(8)).toString()
          let grossWtTotal = Number(isNullOrEmpty(res.data.data.grossWtTotal) ? 0 : res.data.data.grossWtTotal.toFixed(5)).toString()
          totalContentAll = `总净重: ${netWtTotal}    总毛重: ${grossWtTotal}`
        } else {
          let netWt = Number(isNullOrEmpty(res.data.data.netWt) ? 0 : res.data.data.netWt.toFixed(8)).toString()
          let qty = Number(isNullOrEmpty(res.data.data.qty) ? 0 : res.data.data.qty.toFixed(5)).toString()
          let decTotal = Number(isNullOrEmpty(res.data.data.decTotal) ? 0 : res.data.data.decTotal.toFixed(2)).toString()
          totalContentAll = `净重: ${netWt}    申报数量: ${qty}    申报总价: ${decTotal}`
        }
        me.$set(me, 'totalAllContent', totalContentAll)
      }).catch(() => {
      })
    },
    handleSelectionChange(agGrd) {
      let me = this
      me.$set(me.gridConfig, 'selectRows', agGrd.api.getSelectedRows())
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
          me.handleSearchSubmit()
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
        me.handleSearchSubmit()
      })
    },
    /**
     * 数据统计
     */
    handleStatistics() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    }
  },
  computed: {
    iedateLabel() {
      if (this.ieMark === 'I') {
        return '进口日期'
      } else if (this.ieMark === 'E') {
        return '出口日期'
      } else {
        return '进出口日期'
      }
    },
    overseasShipperLabel() {
      if (this.ieMark === 'I') {
        return '境外发货人'
      } else if (this.ieMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收/发货人'
      }
    },
    supplierNameLabel() {
      if (this.ieMark === 'I') {
        return '供应商'
      } else if (this.ieMark === 'E') {
        return '客户'
      } else {
        return '供应商/客户'
      }
    },
    headSupplierNameLabel() {
      if (this.ieMark === 'I') {
        return '表体供应商'
      } else if (this.ieMark === 'E') {
        return '表体客户'
      } else {
        return '表体供应商/客户'
      }
    },
    receiveCodeLabel() {
      if (this.ieMark === 'I') {
        return '境内收货人'
      } else if (this.ieMark === 'E') {
        return '境内发货人'
      } else {
        return '境内收/发货人'
      }
    },
    ieportLabel() {
      if (this.ieMark === 'I') {
        return '进境关别'
      } else if (this.ieMark === 'E') {
        return '出境关别'
      } else {
        return '进/出境关别'
      }
    },
    entryPortLabel() {
      if (this.ieMark === 'I') {
        return '进口口岸'//'入境口岸'
      } else if (this.ieMark === 'E') {
        return '出口口岸'//'离境口岸'
      } else {
        return '进/出口口岸'//'离/入境口岸'
      }
    },
    tradeCountryLabel() {
      if (this.ieMark === 'I') {
        return '启运国(地区)'
      } else if (this.ieMark === 'E') {
        return '运抵国(地区)'
      } else {
        return '启/运抵国(地区)'
      }
    },
    destPortLabel() {
      if (this.ieMark === 'I') {
        return '经停港'
      } else if (this.ieMark === 'E') {
        return '指运港'
      } else {
        return '经停/指运港'
      }
    },
    deliveryPersonLabel() {
      if (this.ieMark === 'I') {
        return '送货人'
      } else if (this.ieMark === 'E') {
        return '提货人'
      } else {
        return '提/送货人'
      }
    },
    shipDateLabel() {
      if (this.ieMark === 'I') {
        return '发货日期'
      } else if (this.ieMark === 'E') {
        return '出货日期'
      } else {
        return '发/出货日期'
      }
    },
    customsFeeLabel() {
      if (this.ieMark === 'I') {
        return '物流费用'
      } else if (this.ieMark === 'E') {
        return '报关费'
      } else {
        return '物流/报关费'
      }
    },
    districtCodeLabel() {
      if (this.iemark === 'I') {
        return '境内目的地(国内地区)'
      } else if (this.iemark === 'E') {
        return '境内货源地(国内地区)'
      } else {
        return '境内(货源/目的)地(国内地区)'
      }
    },
    districtPostCodeLabel() {
      if (this.iemark === 'I') {
        return '境内目的地(行政区划)'
      } else if (this.iemark === 'E') {
        return '境内货源地(行政区划)'
      } else {
        return '境内(货源/目的)地(行政区划)'
      }
    },
    totalContent() {
      let me = this
      if (me.gridConfig.selectRows.length > 0) {
        let result = `汇总条数: ${me.gridConfig.selectRows.length}    `
        if (me.isHead) {
          let netWt = 0
          let grossWt = 0
          me.gridConfig.selectRows.forEach(item => {
            netWt = netWt + (isNullOrEmpty(item.netWtTotal) ? 0 : parseFloat(item.netWtTotal))
            grossWt = grossWt + (isNullOrEmpty(item.grossWtTotal) ? 0 : parseFloat(item.grossWtTotal))
          })
          netWt = Number(netWt.toFixed(8)).toString()
          grossWt = Number(grossWt.toFixed(5)).toString()
          result = result + `总净重: ${netWt}    总毛重: ${grossWt}`
        } else {
          let qty = 0
          let netWt = 0
          let decTotal = 0
          me.gridConfig.selectRows.forEach(item => {
            qty = qty + (isNullOrEmpty(item.qty) ? 0 : parseFloat(item.qty))
            netWt = netWt + (isNullOrEmpty(item.netWt) ? 0 : parseFloat(item.netWt))
            decTotal = decTotal + (isNullOrEmpty(item.decTotal) ? 0 : parseFloat(item.decTotal))
          })

          netWt = Number(netWt.toFixed(8)).toString()
          qty = Number(qty.toFixed(5)).toString()
          decTotal = Number(decTotal.toFixed(2)).toString()
          result = result + `净重: ${netWt}    申报数量: ${qty}    申报总价: ${decTotal}`
        }
        return result
      }
      return me.totalAllContent
    },
    jobStatusName() {
      let me = this,
        theStatus = me.reportStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }
  },
  beforeDestroy: function () {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
