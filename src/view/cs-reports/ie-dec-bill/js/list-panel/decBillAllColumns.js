import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { colsIBondedConfig, colsIUnBondedConfig,
         colsEBondedConfig, colsEUnBondedConfig } from './decBillHeadColumns'

const commOtherCols = [
  'facGNo'
  , 'serialNo'
  , 'entryGNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'originCountry'
  , 'destinationCountry'
  , 'curr'
  , 'decPrice'
  , 'netWt'
  , 'qty'
  , 'unit'
  , 'decTotal'
  , 'unit1'
  , 'qty1'
  , 'unit2'
  , 'qty2'
  , 'dutyMode'
  , 'exgVersion'
  , 'districtCode'
  , 'districtPostCode'
  , 'supplierName'
  , 'listNote'
]

// 进口保税
const colsIBondedAllConfig = [
  ...colsIBondedConfig
  , ...commOtherCols
  , 'copGNo'
  , 'gno'
]

// 进口非保税
const colsIUnBondedAllConfig = [
  ...colsIUnBondedConfig
  , ...commOtherCols
]

// 出口保税
const colsEBondedAllConfig = [
  ...colsEBondedConfig
  , ...commOtherCols
  , 'copGNo'
  , 'gno'
]

// 出口非保税
const colsEUnBondedAllConfig = [
  ...colsEUnBondedConfig
  , ...commOtherCols
]

const columnsAll = {
  mixins: [columnRender],
  data() {
    return {
      totalColumnsOther: [
        {
          width: 120,
          key: 'facGNo',
          tooltip: true,
          title: '企业料号'
        },
        {
          width: 100,
          key: 'copGNo',
          tooltip: true,
          title: '备案料号'
        },
        {
          width: 70,
          key: 'gno',
          tooltip: true,
          title: '备案序号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'serialNo',
          title: '清单商品序号'
        },
        {
          width: 130,
          tooltip: true,
          key: 'entryGNo',
          title: '报关单商品序号'
        },
        {
          width: 100,
          key: 'codeTS',
          tooltip: true,
          title: '商品编码'
        },
        {
          width: 130,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 130,
          key: 'gmodel',
          tooltip: true,
          title: '规格型号'
        },
        {
          width: 100,
          tooltip: true,
          title: '原产国(地区)',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 90,
          tooltip: true,
          title: '目的国(地区)',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 150,
          key: 'curr',
          title: '币制',
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 70,
          tooltip: true,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 160,
          key: 'netWt',
          title: '净重',
          tooltip: true
        },
        {
          key: 'qty',
          width: 120,
          tooltip: true,
          title: '申报数量'
        },
        {
          width: 130,
          key: 'unit',
          tooltip: true,
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 90,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 80,
          key: 'qty1',
          tooltip: true,
          title: '法一数量'
        },
        {
          width: 90,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 70,
          key: 'qty2',
          tooltip: true,
          title: '法二数量'
        },
        {
          width: 90,
          tooltip: true,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 80,
          tooltip: true,
          key: 'exgVersion',
          title: '单耗版本号'
        },
        {
          width: 180,
          tooltip: true,
          key: 'districtCode',
          title: '境内(货源/目的)地(国内地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 180,
          tooltip: true,
          key: 'districtPostCode',
          title: '境内(货源/目的)地(行政区划)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '供应商',
          key: 'supplierName'
        },
        {
          width: 200,
          tooltip: true,
          key: 'listNote',
          title: '表体备注'
        }
      ]
    }
  }
}

export {
  colsIBondedAllConfig,
  colsIUnBondedAllConfig,
  colsEBondedAllConfig,
  colsEUnBondedAllConfig,
  columnsAll
}
