export const decBillSearch = {
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1'].includes(value)
      }
    },
    cmbSource: {
      type: Object,
      require: true,
      default: () => ({
        emsNoData: []
      })
    }
  },
  data() {
    return {
      supplierCodeList: []
    }
  },
  computed: {
    supplierCodeLabel() {
      if (this.ieMark === 'I') {
        return '表体供应商'
      } else {
        return '表体客户'
      }
    },
    overseasShipperLabel() {
      if (this.ieMark === 'I') {
        return '境外发货人'
      } else if (this.ieMark === 'E') {
        return '境外收货人'
      } else {
        return '境外收/发货人'
      }
    }
  }
}
