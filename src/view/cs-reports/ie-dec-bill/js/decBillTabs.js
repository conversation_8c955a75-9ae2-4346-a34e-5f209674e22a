import { csAPI } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { importExportManage } from '@/view/cs-common'
import HeadReport from '@/view/cs-reports/ie-dec-bill/components/list-panel/dec-bill-head-list'
import ComplexReport from '@/view/cs-reports/ie-dec-bill/components/list-panel/dec-bill-all-list'

export const decBillTabs = {
  components: {
    HeadReport,
    ComplexReport
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        complexTab: false
      },
      showComplex: false,
      cmbSource: {
        cutData: [],
        emsNoData: [],
        forwardCodeData: [],
        supplierCodeData: [],
        tradeTermNewData: []
      },
      ajaxUrl: {
        getFodUrl: csAPI.ieParams.FOD,
        getCutUrl: csAPI.ieParams.CUT,
        getSupplierCode: csAPI.ieParams.PRD
      },
      tradeTermList: importExportManage.tradeTermList
    }
  },
  watch: {
    tabName(value) {
      let me = this
      me.tabs[value] = true
      me.showComplex = value === 'complexTab'
    }
  },
  created: function () {
    let me = this
    // 备案号
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.cmbSource.emsNoData = res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE
        }
      })
    }).catch(() => {
      me.cmbSource.emsNoData = []
    })
    if (me.ieMark === 'I') {
      me.$set(me.ajaxUrl, 'getSupplierCode', csAPI.ieParams.PRD)
    } else {
      me.$set(me.ajaxUrl, 'getSupplierCode', csAPI.ieParams.CLI)
    }
    // 客户/供应商
    me.$http.post(me.ajaxUrl.getSupplierCode).then(res => {
      me.cmbSource.supplierCodeData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.supplierCodeData = []
    })
    // 报关行
    me.$http.post(me.ajaxUrl.getCutUrl).then(res => {
      me.cmbSource.cutData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.cutData = []
    })
    // 货代
    me.$http.post(me.ajaxUrl.getFodUrl).then(res => {
      me.cmbSource.forwardCodeData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbSource.forwardCodeData = []
    })
    // 贸易条款
    me.pcodeList(me.pcode.transac).then(res => {
      let data = JSON.parse(JSON.stringify(res || {}))
      me.cmbSource.tradeTermNewData = [...me.tradeTermList, ...data]
    }).catch(() => {
      me.cmbSource.tradeTermNewData = me.tradeTermList
    })
  }
}
