<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <HeadReport ref="head" :ie-mark="ieMark" :bond-mark="bondMark" :cmb-source="cmbSource"></HeadReport>
      </TabPane>
      <TabPane name="complexTab" label="表头和表体">
        <ComplexReport ref="body" v-if="showComplex" :ie-mark="ieMark" :bond-mark="bondMark" :cmb-source="cmbSource"></ComplexReport>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import { decBillTabs } from './js/decBillTabs'

  export default {
    name: 'decBillEUnBonded',
    mixins: [decBillTabs],
    data() {
      return {
        ieMark: 'E',
        bondMark: '1'
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
