<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <div class="custom-bread-crumb">
            <div class="ivu-breadcrumb" style="font-size: 12px;">
              <span>
                <span class="ivu-breadcrumb-item-link">
                  关务管理
                </span>
                <span class="ivu-breadcrumb-item-separator">/</span>
              </span>
              <span>
                <span class="ivu-breadcrumb-item-link">
                  报表中心
                </span>
                <span class="ivu-breadcrumb-item-separator">/</span>
              </span>
              <span>
                <span class="ivu-breadcrumb-item-link">
                  统计报表
                </span>
                <span class="ivu-breadcrumb-item-separator">/</span>
              </span>
              <span>
                <span class="ivu-breadcrumb-item-link">
                  {{ listConfig.exportTitle }}
                </span>
                <span class="ivu-breadcrumb-item-separator">/</span>
              </span>
            </div>
            <div class="custom-search-buttons" style="padding-right: 5px;">
              <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
              <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
              <XdoButton v-if="btnBackShow" type="text" @click="backToList">
                <XdoIcon type="ios-undo" size="22" style="color: green;" />
              </XdoButton>
            </div>
          </div>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { columns } from './js/statisticalReportColumns'
  import { statisticalReportList } from './js/statisticalReportList'

  export default {
    name: 'statisticalReportList',
    mixins: [columns, statisticalReportList],
    data() {
      return {
        initSearch: false,
        pmsLevel: 'report',
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: ''
        },
        btnBackShow: true
      }
    },
    methods: {
      /**
       * 加载额外的数据源
       */
      loadCmbSource() {
        let me = this
        // 报关行
        me.$http.post(csAPI.ieParams.CUT).then(res => {
          me.$set(me.cmbSource, 'agentCode', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
          me.$set(me.cmbSource, 'agentCode', [])
        }).finally(() => {
          me.searchFieldsReLoad('agentCode')
        })
        // 货代
        me.$http.post(csAPI.ieParams.FOD).then(res => {
          me.$set(me.cmbSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
          me.$set(me.cmbSource, 'forwardCode', [])
        }).finally(() => {
          me.searchFieldsReLoad('forwardCode')
        })
      },
      /**
       * 获取查询条件
       * @returns {*[]}
       */
      getParams() {
        let me = this,
          resultParams = []
        switch (me.reportType) {
          case 'item-number':
            resultParams = me.itemNumber.params
            break
          case 'hs-involved-no':
            resultParams = me.hsInvolvedNo.params
            break
          case 'top-10-of-total-price':
            resultParams = me.top10OfTotalPrice.params
            break
          case 'top-10-unit-price':
            resultParams = me.top10UnitPrice.params
            break
          case 'top-10-statistics':
            resultParams = me.top10Statistics.params
            break
          case 'shipping-agent':
            resultParams = me.shippingAgent.params
            break
          case 'customs-broker':
            resultParams = me.customsBroker.params
            break
          case 'port-of-entry':
            resultParams = me.portOfEntry.params
            break
          case 'supervision-method':
            resultParams = me.supervisionMethod.params
            break
          case 'traf-mode':
            resultParams = me.trafMode.params
            break
          case 'promise-import':
            resultParams = me.promiseImport.params
            break
          case 'promise-export':
            resultParams = me.promiseExport.params
            break
          case 'logistics-import':
            resultParams = me.logisticsImport.params
            break
          case 'logistics-export':
            resultParams = me.logisticsExport.params
            break
          case 'checklist':
            resultParams = me.checklist.params
            break
          case 'customs-declaration':
            resultParams = me.customsDeclaration.params
            break
          case 'import-maker':
            resultParams = me.importMaker.params
            break
          case 'export-maker':
            resultParams = me.exportMaker.params
            break
          default:
            resultParams = []
        }
        return resultParams
      },
      /**
       * 获取列字段
       * @returns {*[]}
       */
      getFields() {
        let me = this,
          resultFields = []
        switch (me.reportType) {
          case 'item-number':
            resultFields = me.itemNumber.fields
            break
          case 'hs-involved-no':
            resultFields = me.hsInvolvedNo.fields
            break
          case 'top-10-of-total-price':
            resultFields = me.top10OfTotalPrice.fields
            break
          case 'top-10-unit-price':
            resultFields = me.top10UnitPrice.fields
            break
          case 'top-10-statistics':
            resultFields = me.top10Statistics.fields
            break
          case 'shipping-agent':
            resultFields = me.shippingAgent.fields
            break
          case 'customs-broker':
            resultFields = me.customsBroker.fields
            break
          case 'port-of-entry':
            resultFields = me.portOfEntry.fields
            break
          case 'supervision-method':
            resultFields = me.supervisionMethod.fields
            break
          case 'traf-mode':
            resultFields = me.trafMode.fields
            break
          case 'promise-import':
            resultFields = me.promiseImport.fields
            break
          case 'promise-export':
            resultFields = me.promiseExport.fields
            break
          case 'logistics-import':
            resultFields = me.logisticsImport.fields
            break
          case 'logistics-export':
            resultFields = me.logisticsExport.fields
            break
          case 'checklist':
            resultFields = me.checklist.fields
            break
          case 'customs-declaration':
            resultFields = me.customsDeclaration.fields
            break
          case 'import-maker':
            resultFields = me.importMaker.fields
            break
          case 'export-maker':
            resultFields = me.exportMaker.fields
            break
          default:
            resultFields = []
        }
        return resultFields
      },
      /**
       * 配置加载
       */
      loadConfigByType() {
        let me = this,
          params = me.getParams()
        me.$set(me, 'btnBackShow', true)
        me.$set(me, 'baseParams', params)
        me.$set(me, 'hasChildTabs', false)
        switch (me.reportType) {
          case 'item-number':
            me.$set(me.listConfig, 'exportTitle', me.itemNumber.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.itemNumber.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.itemNumber.selectAllPaged)
            break
          case 'hs-involved-no':
            me.$set(me.listConfig, 'exportTitle', me.hsInvolvedNo.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.hsInvolvedNo.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.hsInvolvedNo.selectAllPaged)
            break
          case 'top-10-of-total-price':
            me.$set(me, 'btnBackShow', false)
            me.$set(me, 'hasChildTabs', true)
            me.$set(me.listConfig, 'exportTitle', me.top10OfTotalPrice.exportTitle)
            if (me.ieMark === 'I') {
              me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.top10OfTotalPrice.imports.exportUrl)
              me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.top10OfTotalPrice.imports.selectAllPaged)
            } else {
              me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.top10OfTotalPrice.exports.exportUrl)
              me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.top10OfTotalPrice.exports.selectAllPaged)
            }
            break
          case 'top-10-unit-price':
            me.$set(me, 'btnBackShow', false)
            me.$set(me, 'hasChildTabs', true)
            me.$set(me.listConfig, 'exportTitle', me.top10UnitPrice.exportTitle)
            if (me.ieMark === 'I') {
              me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.top10UnitPrice.imports.exportUrl)
              me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.top10UnitPrice.imports.selectAllPaged)
            } else {
              me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.top10UnitPrice.exports.exportUrl)
              me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.top10UnitPrice.exports.selectAllPaged)
            }
            break
          case 'top-10-statistics':
            me.$set(me.listConfig, 'exportTitle', me.top10Statistics.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.top10Statistics.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.top10Statistics.selectAllPaged)
            break
          case 'shipping-agent':
            me.$set(me.listConfig, 'exportTitle', me.shippingAgent.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.shippingAgent.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.shippingAgent.selectAllPaged)
            break
          case 'customs-broker':
            me.$set(me.listConfig, 'exportTitle', me.customsBroker.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.customsBroker.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.customsBroker.selectAllPaged)
            break
          case 'port-of-entry':
            me.$set(me.listConfig, 'exportTitle', me.portOfEntry.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.portOfEntry.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.portOfEntry.selectAllPaged)
            break
          case 'supervision-method':
            me.$set(me.listConfig, 'exportTitle', me.supervisionMethod.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.supervisionMethod.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.supervisionMethod.selectAllPaged)
            break
          case 'traf-mode':
            me.$set(me.listConfig, 'exportTitle', me.trafMode.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.trafMode.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.trafMode.selectAllPaged)
            break
          case 'promise-import':
            me.$set(me.listConfig, 'exportTitle', me.promiseImport.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.promiseImport.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.promiseImport.selectAllPaged)
            break
          case 'promise-export':
            me.$set(me.listConfig, 'exportTitle', me.promiseExport.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.promiseExport.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.promiseExport.selectAllPaged)
            break
          case 'logistics-import':
            me.$set(me.listConfig, 'exportTitle', me.logisticsImport.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.logisticsImport.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.logisticsImport.selectAllPaged)
            break
          case 'logistics-export':
            me.$set(me.listConfig, 'exportTitle', me.logisticsExport.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.logisticsExport.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.logisticsExport.selectAllPaged)
            break
          case 'checklist':
            me.$set(me.listConfig, 'exportTitle', me.checklist.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.checklist.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.checklist.selectAllPaged)
            break
          case 'customs-declaration':
            me.$set(me.listConfig, 'exportTitle', me.customsDeclaration.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.customsDeclaration.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.customsDeclaration.selectAllPaged)
            break
          case 'import-maker':
            me.$set(me.listConfig, 'exportTitle', me.importMaker.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.importMaker.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.importMaker.selectAllPaged)
            break
          case 'export-maker':
            me.$set(me.listConfig, 'exportTitle', me.exportMaker.exportTitle)
            me.$set(me.ajaxUrl, 'exportUrl', csAPI.reportCenter.statisticalReport.exportMaker.exportUrl)
            me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.reportCenter.statisticalReport.exportMaker.selectAllPaged)
            break
          default:
            me.$set(me.listConfig, 'exportTitle', '')
            me.$set(me.ajaxUrl, 'exportUrl', '')
            me.$set(me.ajaxUrl, 'selectAllPaged', '')
        }
        me.loadCmbSource()
        me.loadListConfig()
        me.$nextTick(() => {
          me.handleSearchSubmit()
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
