<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="importTab" label="进口">
        <StatisticalReportList :report-type="reportType" ie-mark="I"></StatisticalReportList>
      </TabPane>
      <TabPane name="exportTab" label="出口">
        <StatisticalReportList :report-type="reportType" ie-mark="E"></StatisticalReportList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { statisticalReportTabs } from './js/statisticalReportTabs'

  export default {
    name: 'statisticalReportTabs',
    mixins: [statisticalReportTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
