import StatisticalReportList from '../statistical-report-list'

export const statisticalReportTabs = {
  components: {
    StatisticalReportList
  },
  props: {
    reportType: {
      type: String,
      default: () => ('')
    }
  },
  data() {
    return {
      tabName: 'importTab',
      tabs: {
        importTab: true,
        exportTab: false
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (tabName) {
        let me = this
        me.tabs[tabName] = true
      }
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.$emit('backToMain')
    }
  }
}
