import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const columns = {
  mixins: [columnRender],
  data() {
    let me = this
    return {
      /**
       * 物料号数量
       */
      itemNumber: {
        exportTitle: '物料号数量',
        params: [
          {
            type: 'select',
            key: 'bondMark',
            title: '保完税标记'
          }, {
            key: 'gmark',
            type: 'select',
            title: '物料类型'
          }],
        fields: [
          {
            width: 150,
            key: 'bondMark',
            title: '保完税标记',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
            })
          }, {
            width: 150,
            title: '物料个数',
            key: 'facGNoNumber'
          }, {
            width: 150,
            key: 'gmark',
            title: '物料类型',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.gmark)
            })
          }]
      },
      /**
       * 涉及HS数量
       */
      hsInvolvedNo: {
        exportTitle: '涉及HS数量',
        params: [
          {
            type: 'select',
            key: 'bondMark',
            title: '保完税标记'
          }, {
            key: 'gmark',
            type: 'select',
            title: '物料类型'
          }],
        fields: [
          {
            width: 150,
            key: 'bondMark',
            title: '保完税标记',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
            })
          }, {
            width: 150,
            key: 'gmark',
            title: '物料类型',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.gmark)
            })
          }, {
            width: 150,
            title: 'hsCode数量',
            key: 'hsCodeNumber'
          }]
      },
      /**
       * 总价前10排名
       */
      top10OfTotalPrice: {
        exportTitle: '总价前10排名',
        params: [
          {
            type: 'select',
            key: 'bondMark',
            title: '保完税标记'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }, {
            type: 'pcode',
            key: 'tradeMode',
            title: '监管方式',
            props: {
              meta: 'TRADE_OUTDATED'
            }
          }],
        fields: [
          {
            width: 150,
            key: 'bondMark',
            title: '保完税标记',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
            })
          }, {
            width: 120,
            key: 'curr',
            title: '币制',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
            })
          }, {
            width: 160,
            key: 'decTotal',
            title: '申报总金额'
          }, {
            width: 160,
            key: 'facGNo',
            title: '企业料号'
          }, {
            width: 80,
            key: 'seqNo',
            title: '序号'
          }]
      },
      /**
       * 单价前10排名
       */
      top10UnitPrice: {
        exportTitle: '单价前10排名',
        params: [
          {
            type: 'select',
            key: 'bondMark',
            title: '保完税标记'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }, {
            type: 'pcode',
            key: 'tradeMode',
            title: '监管方式',
            props: {
              meta: 'TRADE_OUTDATED'
            }
          }],
        fields: [
          {
            width: 150,
            key: 'bondMark',
            title: '保完税标记',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
            })
          }, {
            width: 120,
            key: 'curr',
            title: '币制',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
            })
          }, {
            width: 160,
            key: 'decPrice',
            title: '申报单价(平均单价)'
          }, {
            width: 120,
            key: 'emsListNo',
            title: '预录入单号'
          }, {
            width: 160,
            key: 'facGNo',
            title: '企业料号'
          }, {
            width: 80,
            key: 'seqNo',
            title: '序号'
          }]
      },
      /**
       * 税金前10统计
       */
      top10Statistics: {
        exportTitle: '税金前10统计',
        params: [
          {
          //   type: 'select',
          //   key: 'bondMark',
          //   title: '保完税标记'
          // }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }, {
            type: 'pcode',
            key: 'tradeMode',
            title: '监管方式',
            props: {
              meta: 'TRADE_OUTDATED'
            }
          }],
        fields: [
          {
            width: 120,
            key: 'codeTS',
            title: '商品编码'
          }, {
            width: 180,
            key: 'gname',
            title: '商品名称'
          }, {
            width: 120,
            key: 'curr',
            title: '币制',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
            })
          }, {
            width: 80,
            key: 'seqNo',
            title: '序号'
          }, {
            width: 150,
            key: 'tariff',
            title: '关税税金'
          }, {
            width: 150,
            key: 'taxAll',
            title: '税金合计'
          }, {
            width: 150,
            key: 'theVAT',
            title: '增值税税金'
          }]
      },
      /**
       * 货运代理
       */
      shippingAgent: {
        exportTitle: '货运代理',
        params: [
          {
            type: 'select',
            title: '货运代理',
            key: 'forwardCode'
          }, {
            key: 'iemark',
            type: 'select',
            title: '进出口标志'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }],
        fields: [
          {
            width: 150,
            key: 'bgdCnt',
            title: '总票数'
          }, {
            width: 150,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 280,
            title: '货运代理',
            key: 'forwardCode',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.keyValueRender(h, params, 'forwardCode', 'forwardName')
            }, true)
          }, {
            width: 120,
            title: '票数占比',
            key: 'percentOfbgdCnt'
          }, {
            width: 120,
            title: '金额占比',
            key: 'percentOfdecTotal'
          }]
      },
      /**
       * 报关行
       */
      customsBroker: {
        exportTitle: '报关行',
        params: [
          {
            type: 'select',
            title: '报关行',
            key: 'agentCode'
          }, {
            key: 'iemark',
            type: 'select',
            title: '进出口标志'
          }, {
            range: true,
            title: '申报日期',
            key: 'declareDate'
          }],
        fields: [
          {
            width: 280,
            title: '报关行',
            key: 'agentCode',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.keyValueRender(h, params, 'agentCode', 'agentName')
            }, true)
          }, {
            width: 150,
            key: 'bgdCnt',
            title: '总票数'
          }, {
            width: 150,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 120,
            title: '票数占比',
            key: 'percentOfbgdCnt'
          }, {
            width: 120,
            title: '金额占比',
            key: 'percentOfdecTotal'
          }]
      },
      /**
       * 入境口岸
       */
      portOfEntry: {
        exportTitle: '入境口岸',
        params: [
          {
            key: 'iemark',
            type: 'select',
            title: '进出口标志'
          }, {
            key: 'ieport',
            type: 'pcode',
            title: '出入境口岸',
            props: {
              meta: 'CUSTOMS_REL'
            }
          }, {
            range: true,
            title: '申报日期',
            key: 'declareDate'
          }],
        fields: [
          {
            width: 150,
            key: 'bgdCnt',
            title: '总票数'
          }, {
            width: 160,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 150,
            key: 'ieport',
            title: '进出口口岸',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
            })
          }, {
            width: 120,
            title: '票数占比',
            key: 'percentOfbgdCnt'
          }, {
            width: 120,
            title: '金额占比',
            key: 'percentOfdecTotal'
          }]
      },
      /**
       * 监管方式
       */
      supervisionMethod: {
        exportTitle: '监管方式',
        params: [
          {
            key: 'iemark',
            type: 'select',
            title: '进出口标志'
          }, {
            range: true,
            title: '申报日期',
            key: 'declareDate'
          }, {
            type: 'pcode',
            key: 'tradeMode',
            title: '监管方式',
            props: {
              meta: 'TRADE_OUTDATED'
            }
          }],
        fields: [
          {
            width: 150,
            key: 'bgdCnt',
            title: '总票数'
          }, {
            width: 160,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 120,
            title: '票数占比',
            key: 'percentOfbgdCnt'
          }, {
            width: 120,
            title: '金额占比',
            key: 'percentOfdecTotal'
          }, {
            width: 130,
            key: 'tradeMode',
            title: '监管方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.trade)
            })
          }]
      },
      /**
       * 运输方式
       */
      trafMode: {
        exportTitle: '运输方式',
        params: [
          {
            type: 'pcode',
            key: 'trafMode',
            title: '运输方式',
            props: {
              meta: 'TRANSF'
            }
          }, {
            range: true,
            title: '申报日期',
            key: 'declareDate'
          }, {
            key: 'iemark',
            type: 'select',
            title: '进出口标志'
          }],
        fields: [
          {
            width: 120,
            key: 'trafMode',
            title: '运输方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.transf)
            })
          }, {
            width: 150,
            key: 'bgdCnt',
            title: '总票数'
          }, {
            width: 120,
            title: '票数占比',
            key: 'percentOfbgdCnt'
          }, {
            width: 160,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 120,
            title: '金额占比',
            key: 'percentOfdecTotal'
          }]
      },
      /**
       * 价格说明 - 进口
       */
      promiseImport: {
        exportTitle: '价格说明 - 进口',
        params: [
          {
          type: 'select',
          key: 'bondMark',
          title: '保完税标记'
        }, {
          type: 'select',
          key: 'confirmPrice',
          title: '价格影响确认'
        }, {
          type: 'select',
          key: 'confirmRoyalties',
          title: '支付特许权使用费确认'
        }, {
          type: 'select',
          title: '特殊关系确认',
          key: 'confirmSpecial'
        }, {
          type: 'select',
          title: '自保自缴',
          key: 'cusRemarkAuto'
        }, {
          range: true,
          title: '制单日期',
          key: 'insertTime'
        }],
        fields: [
          {
          width: 120,
          key: 'billNo',
          title: '提运单号'
        }, {
          width: 120,
          key: 'curr',
          title: '币制',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
          })
        }, {
          width: 110,
          key: 'ddate',
          title: '报关单申报日期',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.dateTimeShowRender(h, params)
          })
        }, {
          width: 160,
          title: '总金额',
          key: 'decTotal'
        }, {
          width: 120,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          width: 160,
          key: 'emsNo',
          title: '备案号'
        }, {
          width: 160,
          key: 'entryId',
          title: '报关单号'
        }, {
          width: 220,
          key: 'gname',
          title: '主要商品名称',
          cellRendererFramework: me.baseCellRenderer(null, true)
        }, {
          width: 180,
          key: 'ieport',
          title: '进出境关别',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
          })
        }, {
          width: 100,
          title: '制单日期',
          key: 'insertTime',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.dateTimeShowRender(h, params)
          })
        }, {
          width: 150,
          key: 'listNo',
          title: '核注清单编号'
        }, {
          width: 150,
          title: '境外收发货人',
          key: 'overseasTradeCode',
          cellRendererFramework: me.baseCellRenderer(null, true)
        }, {
          width: 168,
          key: 'seqNo',
          title: '报关单统一编号',
          cellRendererFramework: me.baseCellRenderer(null, true)
        }, {
          width: 130,
          key: 'tradeMode',
          title: '监管方式',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.trade)
          })
        }, {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.cmbShowRender(h, params, [], me.pcode.transf)
          })
        }]
      },
      /**
       * 价格说明 - 出口
       */
      promiseExport: {
        exportTitle: '价格说明 - 出口',
        params: [
          {
            type: 'select',
            key: 'bondMark',
            title: '保完税标记'
          }, {
            type: 'select',
            key: 'confirmPrice',
            title: '价格影响确认'
          }, {
            type: 'select',
            key: 'confirmRoyalties',
            title: '支付特许权使用费确认'
          }, {
            type: 'select',
            title: '特殊关系确认',
            key: 'confirmSpecial'
          }, {
            type: 'select',
            title: '自保自缴',
            key: 'cusRemarkAuto'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }],
        fields: [
          {
            width: 120,
            key: 'billNo',
            title: '提运单号'
          }, {
            width: 120,
            key: 'curr',
            title: '币制',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
            })
          }, {
            width: 110,
            key: 'ddate',
            title: '报关单申报日期',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 160,
            title: '总金额',
            key: 'decTotal'
          }, {
            width: 120,
            key: 'emsListNo',
            title: '单据内部编号'
          }, {
            width: 160,
            key: 'emsNo',
            title: '备案号'
          }, {
            width: 160,
            key: 'entryId',
            title: '报关单号'
          }, {
            width: 220,
            key: 'gname',
            title: '主要商品名称',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 180,
            key: 'ieport',
            title: '进出境关别',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
            })
          }, {
            width: 100,
            title: '制单日期',
            key: 'insertTime',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 150,
            key: 'listNo',
            title: '核注清单编号'
          }, {
            width: 150,
            title: '境外收发货人',
            key: 'overseasTradeCode',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 168,
            key: 'seqNo',
            title: '报关单统一编号',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 130,
            key: 'tradeMode',
            title: '监管方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.trade)
            })
          }, {
            width: 120,
            key: 'trafMode',
            title: '运输方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.transf)
            })
          }]
      },
      /**
       * 物流异常 - 进口
       */
      logisticsImport: {
        exportTitle: '物流异常 - 进口',
        params: [
          {
            type: 'select',
            key: 'checkMark',
            title: '商检查验'
          }, {
            type: 'select',
            title: '海关查验',
            key: 'customsCheckMark'
          }, {
            type: 'select',
            title: '破损标记',
            key: 'damageMark'
          }, {
            key: 'emsListNo',
            title: '单据内部编号'
          }, {
            key: 'entryNo',
            title: '报关单号'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }, {
            key: 'listNo',
            title: '核注清单号'
          }],
        fields: [
          {
            width: 120,
            title: '异常类型',
            key: 'abnormalType',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.abnormalType)
            })
          }, {
            width: 110,
            key: 'ddate',
            title: '报关单申报日期',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 120,
            key: 'emsListNo',
            title: '单据内部编号'
          }, {
            width: 160,
            key: 'emsNo',
            title: '备案号'
          }, {
            width: 160,
            key: 'entryNo',
            title: '报关单号'
          }, {
            width: 220,
            key: 'gname',
            title: '主要商品名称',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 180,
            key: 'ieport',
            title: '进出境关别',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
            })
          }, {
            width: 100,
            title: '制单日期',
            key: 'insertTime',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 150,
            key: 'listNo',
            title: '核注清单编号'
          }, {
            width: 150,
            title: '境外收发货人',
            key: 'overseasShipper',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 130,
            key: 'tradeMode',
            title: '监管方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.trade)
            })
          }, {
            width: 120,
            key: 'trafMode',
            title: '运输方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.transf)
            })
          }]
      },
      /**
       * 物流异常 - 出口
       */
      logisticsExport: {
        exportTitle: '物流异常 - 出口',
        params: [
          {
            type: 'select',
            key: 'checkMark',
            title: '商检查验'
          }, {
            type: 'select',
            title: '海关查验',
            key: 'customsCheckMark'
          }, {
            type: 'select',
            title: '破损标记',
            key: 'damageMark'
          }, {
            key: 'emsListNo',
            title: '单据内部编号'
          }, {
            key: 'entryNo',
            title: '报关单号'
          }, {
            range: true,
            title: '制单日期',
            key: 'insertTime'
          }, {
            key: 'listNo',
            title: '核注清单号'
          }],
        fields: [
          {
            width: 120,
            title: '异常类型',
            key: 'abnormalType',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, me.dynamicSource.abnormalType)
            })
          }, {
            width: 110,
            key: 'ddate',
            title: '报关单申报日期',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 120,
            key: 'emsListNo',
            title: '单据内部编号'
          }, {
            width: 160,
            key: 'emsNo',
            title: '备案号'
          }, {
            width: 160,
            key: 'entryNo',
            title: '报关单号'
          }, {
            width: 220,
            key: 'gname',
            title: '主要商品名称',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 180,
            key: 'ieport',
            title: '进出境关别',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
            })
          }, {
            width: 100,
            title: '制单日期',
            key: 'insertTime',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.dateTimeShowRender(h, params)
            })
          }, {
            width: 150,
            key: 'listNo',
            title: '核注清单编号'
          }, {
            width: 150,
            title: '境外收发货人',
            key: 'overseasShipper',
            cellRendererFramework: me.baseCellRenderer(null, true)
          }, {
            width: 130,
            key: 'tradeMode',
            title: '监管方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.trade)
            })
          }, {
            width: 120,
            key: 'trafMode',
            title: '运输方式',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.cmbShowRender(h, params, [], me.pcode.transf)
            })
          }]
      },
      /**
       * 核注清单
       */
      checklist: {
        exportTitle: '核注清单',
        params: [],
        fields: []
      },
      /**
       * 报关单
       */
      customsDeclaration: {
        exportTitle: '报关单',
        params: [],
        fields: []
      },
      /**
       * 进口制单
       */
      importMaker: {
        exportTitle: '进口制单',
        params: [],
        fields: []
      },
      /**
       * 出口制单
       */
      exportMaker: {
        exportTitle: '出口制单',
        params: [],
        fields: []
      }
    }
  }
}
