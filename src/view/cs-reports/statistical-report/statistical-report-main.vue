<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
          </XdoBreadCrumb>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div style="width: 100%; overflow: auto;">
          <table border="0" cellspacing="0" cellpadding="0" style="width: 100%;">
            <tbody>
              <template v-for="(row, rIndex) in realData">
                <tr :key="rIndex">
                  <template v-for="cell in row">
                    <td v-if="cell.level === 0" colspan="8" :key="cell.key" class="ivu-form-item ivu-card-head firstTd">{{cell.title}}</td>
                    <td v-else-if="cell.level === 1" :key="cell.key">{{cell.title}}</td>
                    <td v-else-if="cell.level === 2" :key="cell.key">
                      <a style="cursor: pointer; color: blue;" :router="cell.key" @click.prevent="cellClicked">{{cell.title}}</a>
                    </td>
                    <td v-else :key="cell.key" style="width: 100px;"></td>
                  </template>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </XdoCard>
    </div>
    <StatisticalReportList v-if="!showList && !showTabs" :report-type="reportType" @backToMain="backToMain"></StatisticalReportList>
    <StatisticalReportTabs v-if="!showList && showTabs" :report-type="reportType" @backToMain="backToMain"></StatisticalReportTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import StatisticalReportList from './statistical-report-list'
  import StatisticalReportTabs from './statistical-report-tabs'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'statisticalReportMain',
    mixins: [pms],
    components: {
      StatisticalReportList,
      StatisticalReportTabs
    },
    data() {
      return {
        realData: [],
        showList: true,
        reportType: '',
        showTabs: false
      }
    },
    created() {
      let me = this
      me.loadFunctions().then(res => {
        let fullMenus = me.getFullMenus(res),
          realData = fullMenus.filter(item => item.key === 'levelOne').map(it => {
            return {
              level: 0,
              key: it.command,
              title: it.label,
              items: fullMenus.filter(subItem => subItem.key === it.command).map(subIt => {
                return {
                  level: 1,
                  key: subIt.command,
                  title: subIt.label,
                  items: fullMenus.filter(subItem2 => subItem2.key === subIt.command).map(subIt2 => {
                    return {
                      level: 2,
                      key: subIt2.command,
                      title: subIt2.label
                    }
                  })
                }
              })
            }
          })
        me.$set(me, 'realData', me.dataAnalysis(realData))
      })
    },
    methods: {
      /**
       * 数据分析
       * @param originalData
       */
      dataAnalysis(originalData) {
        let grdData = []
        originalData.forEach(dataOne => {
          let usedData = []
          dataOne.items.forEach(dataTwo => {
            let thirdData = []
            if (dataTwo.items.length > 0) {
              thirdData.push({
                title: ' ',
                key: 'empty'
              })
              thirdData.push({
                level: 1,
                key: dataTwo.key,
                title: dataTwo.title
              })
              dataTwo.items.forEach(dataThree => {
                thirdData.push({
                  level: 2,
                  key: dataThree.key,
                  title: dataThree.title
                })
              })
            }
            if (thirdData.length > 0) {
              usedData.push(thirdData)
            }
          })
          if (usedData.length > 0) {
            grdData.push([{
              level: 0,
              key: dataOne.key,
              title: dataOne.title
            }])
            Array.prototype.push.apply(grdData, usedData)
          }
        })
        return grdData
      },
      /**
       * 行选中
       * @param e
       */
      cellClicked(e) {
        let me = this,
          td = e.currentTarget
        if (td) {
          if (td.attributes['router']) {
            me.$set(me, 'reportType', td.attributes['router'].value)
            if (['top-10-of-total-price', 'top-10-unit-price'].includes(td.attributes['router'].value)) {
              me.$set(me, 'showTabs', true)
            } else {
              me.$set(me, 'showTabs', false)
            }
            me.$set(me, 'showList', false)
          }
        }
      },
      /**
       * 返回主界面
       */
      backToMain() {
        let me = this
        me.$set(me, 'reportType', '')
        me.$set(me, 'showList', true)
        me.$set(me, 'showTabs', false)
      },
      /**
       * 获取完整版菜单(针对pms勾选时未勾选上级菜单的情况)
       * @param pmsMenus
       * @returns {({label: string, key: string, command: string}|{label: string, key: string, command: string}|{label: string, key: string, command: string}|{label: string, key: string, command: string}|{label: string, key: string, command: string})[]}
       */
      getFullMenus(pmsMenus) {
        let usedMenus = [],
          validCommands = [],
          resultMenus = deepClone(pmsMenus)
        pmsMenus.forEach(menu => {
          if (!['levelOne', 'material-statistics', 'dec-data-statistics'].includes(menu.key) && !usedMenus.includes(menu.key)) {
            usedMenus.push(menu.key)
          }
        })
        // 物料统计
        if (usedMenus.includes('material-quantity')) {
          if (!validCommands.includes('material-statistics')) {
            validCommands.push('material-statistics')
          }
          if (!validCommands.includes('material-quantity')) {
            validCommands.push('material-quantity')
          }
        }
        if (usedMenus.includes('value-statistics')) {
          if (!validCommands.includes('material-statistics')) {
            validCommands.push('material-statistics')
          }
          if (!validCommands.includes('value-statistics')) {
            validCommands.push('value-statistics')
          }
        }
        if (usedMenus.includes('tax-related-statistics')) {
          if (!validCommands.includes('material-statistics')) {
            validCommands.push('material-statistics')
          }
          if (!validCommands.includes('tax-related-statistics')) {
            validCommands.push('tax-related-statistics')
          }
        }
        // 报关数据统计
        if (usedMenus.includes('percent-of-clearance')) {
          if (!validCommands.includes('dec-data-statistics')) {
            validCommands.push('dec-data-statistics')
          }
          if (!validCommands.includes('percent-of-clearance')) {
            validCommands.push('percent-of-clearance')
          }
        }
        if (usedMenus.includes('commitment')) {
          if (!validCommands.includes('dec-data-statistics')) {
            validCommands.push('dec-data-statistics')
          }
          if (!validCommands.includes('commitment')) {
            validCommands.push('commitment')
          }
        }
        if (usedMenus.includes('logistics-exception')) {
          if (!validCommands.includes('dec-data-statistics')) {
            validCommands.push('dec-data-statistics')
          }
          if (!validCommands.includes('logistics-exception')) {
            validCommands.push('logistics-exception')
          }
        }
        if (usedMenus.includes('clearance-abnormal')) {
          if (!validCommands.includes('dec-data-statistics')) {
            validCommands.push('dec-data-statistics')
          }
          if (!validCommands.includes('clearance-abnormal')) {
            validCommands.push('clearance-abnormal')
          }
        }
        if (usedMenus.includes('billing-performance')) {
          if (!validCommands.includes('dec-data-statistics')) {
            validCommands.push('dec-data-statistics')
          }
          if (!validCommands.includes('billing-performance')) {
            validCommands.push('billing-performance')
          }
        }
        // 层级菜单
        let menus = [{
          key: 'levelOne',
          label: '物料统计',
          command: 'material-statistics'
        }, {
          label: '物料数量',
          key: 'material-statistics',
          command: 'material-quantity'
        }, {
          label: '货值统计',
          key: 'material-statistics',
          command: 'value-statistics'
        }, {
          label: '涉税统计',
          key: 'material-statistics',
          command: 'tax-related-statistics'
        }, {
          key: 'levelOne',
          label: '报关数据统计',
          command: 'dec-data-statistics'
        }, {
          label: '通关占比',
          key: 'dec-data-statistics',
          command: 'percent-of-clearance'
        }, {
          label: '价格说明',
          command: 'commitment',
          key: 'dec-data-statistics'
        }, {
          label: '物流异常',
          key: 'dec-data-statistics',
          command: 'logistics-exception'
        }, {
          label: '通关异常',
          key: 'dec-data-statistics',
          command: 'clearance-abnormal'
        }, {
          label: '制单绩效',
          key: 'dec-data-statistics',
          command: 'billing-performance'
        }]
        validCommands.forEach(command => {
          if (resultMenus.filter(p => p.command === command).length === 0) {
            if (menus.find(m => m.command === command) && resultMenus.filter(rm => rm.command === command).length === 0) {
              resultMenus.push(deepClone(menus.find(m => m.command === command)))
            }
          }
        })
        return resultMenus
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px;
  }

  /deep/ .ivu-form-item {
    margin-bottom: 5px;
  }

  .firstTd {
    font-weight: bold;
    margin-bottom: 2px;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
