import { getKeyValue } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columns = {
  mixins: [baseColumns],
  data () {
    return {
      headColumnstest: [
        // {
        // title: '清单表头',
        //align: 'center',
        // children: [
        {
          title: '报关单号',
          key: 'entryNo',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '内审状态',
          key: 'apprStatusName',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', `${params.row.apprStatus}  ${params.row.apprStatusName}`)
          }
        },
        {
          title: '清单内部编号',
          key: 'emsListNo',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 140,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
          }
        },
        {
          title: '运输方式',
          key: 'trafMode',
          width: 110,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transf, params.row.trafMode), params.row.trafMode))
          }
        },
        {
          title: '成交方式',
          key: 'transMode',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transac, params.row.transMode), params.row.transMode))
          }
        },
        {
          title: '申报单位',
          key: 'declareName',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center',
        },
        {
          title: '主管海关',
          key: 'masterCustoms',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
          }
        },
        {
          title: '境外发货人',
          key: 'overseasShipperName',
          width: 250,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.overseasShipperList, params.row.overseasShipper))
          }
        },
        {
          title: '启运国（地区）',
          key: 'tradeCountry',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.tradeCountry), params.row.tradeCountry))
          }
        },
        {
          title: '进境关别',
          key: 'ieport',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.ieport), params.row.ieport))
          }
        },
        {
          title: '合同协议号',
          key: 'contrNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '许可证号',
          key: 'licenseNo',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '包装种类',
          key: 'wrapType',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.wrapType, params.row.wrap), params.row.wrap))
          }
        },
        {
          title: '其他包装',
          key: 'wrapType2',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.wrapType, params.row.wrap), params.row.wrap))
          }
        },
        {
          title: '件数',
          key: 'packNum',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总净重',
          key: 'netWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总毛重',
          key: 'grossWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费-类型',
          key: 'feeMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.feeMark))
          }
        },
        {
          title: '运费-费率',
          key: 'feeRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费-币制',
          key: 'feeCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.feeCurr), params.row.feeCurr))
          }
        },
        {
          title: '保费-类型',
          key: 'insurMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.insurMark))
          }
        },
        {
          title: '保费-费率',
          key: 'insurRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '保费-币制',
          key: 'insurCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.insurCurr), params.row.insurCurr))
          }
        },
        {
          title: '杂费-类型',
          key: 'otherMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.otherMark))
          }
        },
        {
          title: '杂费-费率',
          key: 'otherRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '杂费-币制',
          key: 'otherCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.otherCurr), params.row.otherCurr))
          }
        },
        {
          title: '备注',
          key: 'note',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ],
      bodyColumnstest: [
        //{
        //title: '清单明细',
        //align: 'center',
        //children: [
        //明细栏位
        // {
        //   title: '商品序号',
        //   key: 'serialNo',
        //   width: 50,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        {
          title: '报关单商品序号',
          key: 'serialNo',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        // {
        //   title: '备案序号',
        //   key: 'gno',
        //   width: 50,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '备案料号',
        //   key: 'copGNo',
        //   width: 100,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        {
          title: '商品编码',
          key: 'codeTS',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'gname',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'gmodel',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '原产国(地区)',
          key: 'originCountry',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
          }
        },
        {
          title: '最终目的国(地区)',
          key: 'destinationCountry',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.destinationCountry), params.row.destinationCountry))
          }
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
          }
        },
        {
          title: '申报单价',
          key: 'decPrice',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报数量',
          key: 'qty',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报计量单位',
          key: 'unit',
          width: 60,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit), params.row.unit))
          }
        },
        {
          title: '申报总价',
          key: 'decTotal',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法定数量',
          key: 'qty1',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法定计量单位',
          key: 'unit1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit1), params.row.unit1))
          }
        },
        {
          title: '第二法定数量',
          key: 'qty2',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法定第二计量单位',
          key: 'unit2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit2), params.row.unit2))
          }
        },
        {
          title: '征免方式',
          key: 'dutyMode',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.levymode, params.row.dutyMode), params.row.dutyMode))
          }
        },
        {
          title: '单耗版本号',
          key: 'exgVersion',
          width: 60,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
        //]
        //}
      ],
      headColumns: [
        {
          title: '报关单号',
          minWidth: 120,
          align: 'center',
          key: 'entryNo',
        },
        {
          title: '清单内部编号',
          minWidth: 120,
          align: 'center',
          key: 'emsListNo',
        },
        {
          title: '核注清单编号',
          minWidth: 120,
          align: 'center',
          key: 'listNo',
        },
        {
          title: '提单状态',
          key: 'apprStatus',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', `${params.row.apprStatus}  ${params.row.apprStatusName}`)
          }
        },
        {
          title: '提单内部编号',
          minWidth: 120,
          align: 'center',
          key: 'erpEmsListNo',
        },
        {
          title: '制单日期',
          minWidth: 120,
          align: 'center',
          key: 'erpInsertTime',
        },
        {
          title: '清单申报日期',
          minWidth: 120,
          align: 'center',
          key: 'declareDate',
        },
        {
          title: '报关单状态',
          minWidth: 120,
          align: 'center',
          key: 'entryStatus',
          render: (h, params) => {
            if (params.row.entryStatus === null || params.row.entryStatusName === null) {
              return ''
            } else {
              return h('span', `${params.row.entryStatus}  ${params.row.entryStatusName}`)
            }
          }
        },
        {
          title: '报关单统一编号',
          minWidth: 120,
          align: 'center',
          key: 'seqNo',
        },
        {
          title: '报关单申报日期',
          minWidth: 120,
          align: 'center',
          key: 'ddate',
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 140,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '物料类型',
          minWidth: 120,
          align: 'center',
          key: 'gmark',
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
          }
        },
        {
          title: '运输方式',
          key: 'trafMode',
          width: 110,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transf, params.row.trafMode), params.row.trafMode))
          }
        },
        {
          title: '运输工具及航次',
          minWidth: 120,
          align: 'center',
          key: 'trafName',
        },
        {
          title: '提运单号',
          minWidth: 120,
          align: 'center',
          key: 'hawb',
        },
        {
          title: '成交方式',
          key: 'transMode',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transac, params.row.transMode), params.row.transMode))
          }
        },
        {
          title: '申报单位',
          key: 'declareName',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center',
        },
        {
          title: '社会信用代码',
          minWidth: 150,
          align: 'center',
          key: 'declareCreditCode',
        },
        {
          title: '申报地海关',
          key: 'masterCustoms',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
          }
        },
        {
          title: '境外发货人',
          key: 'overseasShipper',
          width: 250,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.overseasShipperList, params.row.overseasShipper))
          }
        },
        {
          title: '启运国（地区）',
          key: 'tradeCountry',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.tradeCountry), params.row.tradeCountry))
          }
        },
        {
          title: '进境关别',
          key: 'ieport',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.ieport), params.row.ieport))
          }
        },
        {
          title: '合同协议号',
          key: 'contrNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '许可证号',
          key: 'licenseNo',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '关联备案号',
          minWidth: 120,
          align: 'center',
          key: 'relEmsNo',
        },
        {
          title: '关联清单编号',
          minWidth: 120,
          align: 'center',
          key: 'relListNo',
        },
        {
          title: '包装种类',
          key: 'wrapType',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
            //return h('span', getKeyValue(this.pcodeGet(this.pcode.wrapType, params.row.wrap), params.row.wrap))
          }
        },
        {
          title: '其他包装',
          key: 'wrapType2',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
            //return h('span', getKeyValue(this.pcodeGet(this.pcode.wrapType, params.row.wrap), params.row.wrap))
          }
        },
        {
          title: '件数',
          key: 'packNum',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总净重',
          key: 'netWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总毛重',
          key: 'grossWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总数量',
          key: 'qtyAll',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总金额',
          key: 'totalAll',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费-类型',
          key: 'feeMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.feeMark))
          }
        },
        {
          title: '运费-费率',
          key: 'feeRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费-币制',
          key: 'feeCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.feeCurr), params.row.feeCurr))
          }
        },
        {
          title: '保费-类型',
          key: 'insurMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.insurMark))
          }
        },
        {
          title: '保费-费率',
          key: 'insurRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '保费-币制',
          key: 'insurCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.insurCurr), params.row.insurCurr))
          }
        },
        {
          title: '杂费-类型',
          key: 'otherMark',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbDataSource.feeMarkList, params.row.otherMark))
          }
        },
        {
          title: '杂费-费率',
          key: 'otherRate',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '杂费-币制',
          key: 'otherCurr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.otherCurr), params.row.otherCurr))
          }
        },
        {
          title: '表头备注',
          key: 'note',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '海关查验日期',
          key: 'customsCheckDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '海关查验原因',
          key: 'customsCheckReason',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '海关查验结果',
          key: 'customsCheckResult',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商检查验日期',
          key: 'checkDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商检查验原因',
          key: 'checkReason',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商检查验结果',
          key: 'checkResult',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '完税日期',
          key: 'dutyDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '缴税方式',
          key: 'dutyType',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '完税总价',
          key: 'dutyTotal',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '放行日期',
          key: 'passDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '到货通知日期',
          key: 'noticeDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '车牌号',
          key: 'plateNum',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '送货人',
          key: 'deliveryPerson',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '送货单号',
          key: 'deliveryNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '到厂日期',
          key: 'deliveryDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '入库单据号',
          key: 'inOutNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '破损标记',
          key: 'damageMark',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '财务单据号',
          key: 'financeNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '物流费用',
          key: 'customsFee',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '报关费用',
          key: 'logisticsFee',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '邀请日期',
          key: 'inviteDate',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '贸易条款',
          key: 'tradeTerms',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
        },
        {
          title: '发票号',
          key: 'invoiceNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '承运人',
          key: 'carrier',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '计费重量',
          key: 'cweight',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
      ],
      bodyColumns: [
        {
          title: '报关单商品序号',
          key: 'serialNo',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商品编码',
          key: 'codeTS',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'gname',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'gmodel',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '原产国(地区)',
          key: 'originCountry',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
          }
        },
        {
          title: '最终目的国(地区)',
          key: 'destinationCountry',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.destinationCountry), params.row.destinationCountry))
          }
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
          }
        },
        {
          title: '申报单价',
          key: 'decPrice',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报数量',
          key: 'qty',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报计量单位',
          key: 'unit',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit), params.row.unit))
          }
        },
        {
          title: '申报总价',
          key: 'decTotal',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法一数量',
          key: 'qty1',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法一单位',
          key: 'unit1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit1), params.row.unit1))
          }
        },
        {
          title: '法二数量',
          key: 'qty2',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法二单位',
          key: 'unit2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit2), params.row.unit2))
          }
        },
        {
          title: '征免方式',
          key: 'dutyMode',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.levymode, params.row.dutyMode), params.row.dutyMode))
          }
        },
        {
          title: '单耗版本号',
          key: 'exgVersion',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '境内目的地',
          key: 'districtCode',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        }
      ],
      exColumns: [
        {
          title: '境内收货人',
          key: 'tradeName',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        // {
        //   title: '报关单发送状态',
        //   key: 'sendApiStatus',
        //   render: (h, params) => {
        //     if (params.row.sendApiStatus === '-1') {
        //       return h('span', {
        //         style: {
        //           fontWeight: 'bold'
        //         }
        //       }, '未发送')
        //     } else if (params.row.sendApiStatus === '0') {
        //       return h('Button', {
        //         props: {
        //           type: 'error'
        //         },
        //         style: {
        //           fontWeight: 'bold'
        //         },
        //         on: {
        //           click: () => {
        //             this.showErrMsg(params.row)
        //           }
        //         }
        //       }, '发送失败')
        //     } else if (params.row.sendApiStatus === '1') {
        //       return h('span', {
        //         style: {
        //           color: '#19BE6B',
        //           fontWeight: 'bold'
        //         }
        //       }, '发送成功')
        //     } else {
        //       // if (isNullOrEmpty(params.row.sendApiStatus)) {
        //       return h('span', '')
        //       // } else {
        //       //   return h('span', getKeyValue(this.importExportManage.BILL_STATUS_MAP, params.row.sendApiStatus))
        //       // }
        //     }
        //   },
        //   align: 'center',
        //   width: 100
        // },
      ],
      bodyexColumns: [
        {
          title: '境内目的地(行政)',
          key: 'districtPostCode',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },{
          title: '报关单发送状态',
          key: 'sendApiStatus',
          render: (h, params) => {
            if (params.row.sendApiStatus === '-1') {
              return h('span', {
                style: {
                  fontWeight: 'bold'
                }
              }, '未发送')
            } else if (params.row.sendApiStatus === '0') {
              return h('Button', {
                props: {
                  type: 'error'
                },
                style: {
                  fontWeight: 'bold'
                },
                on: {
                  click: () => {
                    this.showErrMsg(params.row)
                  }
                }
              }, '发送失败')
            } else if (params.row.sendApiStatus === '1') {
              return h('span', {
                style: {
                  color: '#19BE6B',
                  fontWeight: 'bold'
                }
              }, '发送成功')
            } else {
              return h('span', '')
            }
          },
          align: 'center',
          width: 100
        },
      ],
      bondtaxColumns:[
        {
          title: '关税',
          key: 'dutyPrice',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '关税缓税利息',
          minWidth: 120,
          align: 'center',
          key: 'dutyInterest',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '增值税',
          key: 'taxPrice',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '增值税缓税利息',
          minWidth: 120,
          align: 'center',
          key: 'taxInterest',
          ellipsis: true,
          tooltip: true
        },
      ],
      unbondtaxColumns:[
        {
          title: '关税',
          key: 'dutyPrice',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '增值税',
          key: 'taxPrice',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        }
      ]
    }
  }
}
export {
  // columnsConfig,
  // excelColumnsConfig,
  columns
}
