export default {
  data() {
    return {
      showFormSetup: false,
      formResId: '',
      schema: {
        titleWidth: 100,
        class: 'dc-form dc-form-4'
      },
      showElements: [],
      searchParam: {
        entryNo: '',
        emsListNo: '',
        listNo: '',
        erpEmsListNo: '',
        erpInsertTimeFrom: '',
        erpInsertTimeTo: '',
        declareDateFrom: '',
        declareDateTo: '',
        seqNo: '',
        status: '',
        dDateFrom: '',
        dDateTo: '',
        emsNo: '',
        gmark: '',
        tradeMode: '',
        trafMode: '',
        trafName: '',
        hawb: '',
        transMode: '',
        declareName: '',
        declareCreditCode: '',
        masterCustoms: '',
        overseasShipper: '',
        tradeCountry: '',
        ieport: '',
        contrNo: '',
        licenseNo: '',
        relEmsNo: '',
        relListNo: '',
        wrapType: '',
        note: '',
        customsCheckDateFrom: '',
        customsCheckDateTo: '',
        customsCheckReason: '',
        customsCheckResult: '',
        checkDateFrom: '',
        checkDateTo: '',
        checkReason: '',
        checkResult: '',
        dutyDateFrom: '',
        dutyDateTo: '',
        dutyType: '',
      },
      elements: [
        {type: "input", key: "entryNo", title: '报关单号'},
        {type: "input", key: "emsListNo", title: '清单内部编号'},
        {type: "input", key: "listNo", title: '核注清单编号'},
        {type: "input", key: "erpEmsListNo", title: '提单内部编号'},
        {
          type: 'dateRange', title: '制单日期', key: 'erpInsertTime',
          fields: [{key: 'erpInsertTimeFrom'}, {key: 'erpInsertTimeTo'}]
        },
        {
          type: 'dateRange', title: '清单申报日期', key: 'declareDate',
          fields: [{key: 'declareDateFrom'}, {key: 'declareDateTo'}]
        },
        {type: 'input', title: '报关单统一编号', key: 'seqNo'},
        {type: 'input', title: '报关单状态编码', key: 'status'},
        {
          type: 'dateRange', title: '报关单申报日期', key: 'ddate',
          fields: [{key: 'ddateFrom'}, {key: 'ddateTo'}]
        },
        {type: 'input', title: '备案号', key: 'emsNo',},
        {type: 'input', title: '物料类型', key: 'gmark'},
        {
          type: 'pcode', title: '监管方式', key: 'tradeMode',
          props: {
            meta: 'TRADE'
          }
        },
        {
          type: 'pcode', title: '运输方式', key: 'trafMode',
          props: {
            meta: 'TRANSF'
          }
        },
        {type: 'input', title: '运输工具及航次', key: 'trafName'},
        {type: 'input', title: '提运单号', key: 'hawb'},
        {
          type: 'pcode', title: '成交方式', key: 'transMode',
          props: {
            meta: 'TRANSAC'
          }
        },
        {type: 'input', title: '申报单位', key: 'declareName'},
        {type: 'input', title: '申报单位社会信用代码', key: 'declareCreditCode'},
        {
          type: 'pcode', title: '主管海关', key: 'masterCustoms',
          props: {
            meta: 'CUSTOMS_REL'
          }
        },
        {type: 'input', title: '境外发货人', key: 'overseasShipper'},
        {
          type: 'pcode', title: '启运国（地区）', key: 'tradeCountry',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        },
        {
          type: 'pcode', title: '进境关别', key: 'ieport',
          props: {
            meta: 'CUSTOMS_REL'
          }
        },
        {type: 'input', title: '合同协议号', key: 'contrNo'},
        {type: 'input', title: '许可证号', key: 'licenseNo'},
        {type: 'input', title: '关联备案号', key: 'relEmsNo'},
        {type: 'input', title: '关联清单编号', key: 'relListNo'},
        {
          type: 'pcode', title: '包装种类', key: 'wrapType',
          props: {
            meta: 'WRAP'
          }
        },
        {type: 'input', title: '表头备注', key: 'note'},
        {
          type: 'dateRange', title: '海关查验日期', key: 'customsCheckDate',
          fields: [{key: 'customsCheckDateFrom'}, {key: 'customsCheckDateTo'}]
        },
        {type: 'input', title: '海关查验原因', key: 'customsCheckReason'},
        {type: 'input', title: '海关查验结果', key: 'customsCheckResult'},
        {
          type: 'dateRange', title: '商检查验日期', key: 'checkDate',
          fields: [{key: 'checkDateFrom'}, {key: 'checkDateTo'}]
        },
        {type: 'input', title: '商检查验原因', key: 'checkReason'},
        {type: 'input', title: '商检查验结果', key: 'checkResult'},
        {
          type: 'dateRange', title: '完税日期', key: 'dutyDate',
          fields: [{key: 'dutyDateFrom'}, {key: 'dutyDateTo'}]
        },
        {type: 'input', title: '缴税方式', key: 'dutyType'},
        // {
        //   title: '完税总价',
        //   key: 'dutyTotal',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '关税',
        //   key: 'dutyPrice',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '增值税',
        //   key: 'taxPrice',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '放行日期',
        //   key: 'passDate',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '到货通知日期',
        //   key: 'noticeDate',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '车牌号',
        //   key: 'plateNum',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '送货人',
        //   key: 'deliveryPerson',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '送货单号',
        //   key: 'deliveryNo',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '到厂日期',
        //   key: 'deliveryDate',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '入库单据号',
        //   key: 'inOutNo',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '破损标记',
        //   key: 'damageMark',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '财务单据号',
        //   key: 'financeNo',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '物流费用',
        //   key: 'customsFee',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '报关费用',
        //   key: 'logisticsFee',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '报关单商品序号',
        //   key: 'entryGNo',
        //   width: 70,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '商品编码',
        //   key: 'codeTS',
        //   width: 100,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '商品名称',
        //   key: 'gname',
        //   width: 130,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '规格型号',
        //   key: 'gmodel',
        //   width: 130,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '原产国(地区)',
        //   key: 'originCountry',
        //   width: 100,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.originCountry), params.row.originCountry))
        //   }
        // },
        // {
        //   title: '最终目的国(地区)',
        //   key: 'destinationCountry',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.country_outdated, params.row.destinationCountry), params.row.destinationCountry))
        //   }
        // },
        // {
        //   title: '币制',
        //   key: 'curr',
        //   width: 150,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
        //   }
        // },
        // {
        //   title: '申报计量单位',
        //   key: 'unit',
        //   width: 60,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit), params.row.unit))
        //   }
        // },
        // {
        //   title: '法定计量单位',
        //   key: 'unit1',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit1), params.row.unit1))
        //   }
        // },
        // {
        //   title: '法定第二计量单位',
        //   key: 'unit2',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit2), params.row.unit2))
        //   }
        // },
        // {
        //   title: '征免方式',
        //   key: 'dutyMode',
        //   width: 90,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('span', getKeyValue(this.pcodeGet(this.pcode.levymode, params.row.dutyMode), params.row.dutyMode))
        //   }
        // },
        // {
        //   title: '单耗版本号',
        //   key: 'exgVersion',
        //   width: 60,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '境内目的地',
        //   key: 'districtPostCode',
        //   width: 60,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // },
        // {
        //   title: '表体备注',
        //   key: 'listNote',
        //   width: 60,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center'
        // }
      ]
    }
  },
  methods: {
    handleShowSet() {
      let me = this
      me.showFormSetup = true
    }
  },
  mounted: function () {
    let me = this,
      val = me.tabName
    if (val === undefined) {
      me.formResId = me.$route.fullPath
    } else if (val === 'headTab') {
      me.formResId = me.$route.fullPath + `/headTab`
    } else {
      me.formResId = me.$route.fullPath + `/bodyTab`
    }
  }
}
