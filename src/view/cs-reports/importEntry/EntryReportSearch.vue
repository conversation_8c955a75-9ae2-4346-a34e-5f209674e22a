<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange"></dc-dateRange>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="grossWt" label="总毛重">
        <xdo-input v-model="searchParam.grossWt" decimal int-length="11" precision="5" ></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="packNum" label="件数">
        <xdo-input v-model="searchParam.packNum" number int-length="11"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieport" label="进出口口岸">
        <xdo-select v-model="searchParam.ieport" :asyncOptions="pcodeList" :meta="pcode.customs_rel"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="insertUser" label="制单员">
        <XdoIInput type="text" v-model="searchParam.entryInsertUser"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-show="isBonded" prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-show="isBonded" prop="gno" label="备案序号">
        <xdo-input v-model="searchParam.gno" number int-length="10" ></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称" v-if="tabName !== 'headTab'">
        <XdoIInput type="text"  v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码" v-if="tabName !== 'headTab'">
        <XdoIInput type="text"  v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gmodel"  label="申报规格型号" v-if="tabName !== 'headTab'">
        <XdoIInput type="text"  v-model="searchParam.gmodel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="originCountry" label="原产国" v-if="tabName !== 'headTab'">
        <xdo-select v-model="searchParam.originCountry" :asyncOptions="pcodeList"
                    :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="exgVersion" label="单耗版本号" v-if="tabName !== 'headTab'">
        <XdoIInput type="text" v-model="searchParam.exgVersion"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="transMode" label="成交方式">
        <xdo-select v-model="searchParam.transMode" :asyncOptions="pcodeList" :meta="pcode.transac" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'

  export default {
    name: 'BillReportSearch',
    props: {
      tabName: {
        type: String,
        default: () => ({})
      }
    },
    data() {
      return {
        searchParam: {
          //0：保税；1：非保税
          bondMark: this.$route.fullPath.endsWith('/iEntryInbond') ? '0' : '1',
          //I：进口；E：出口
          iemark: 'I',

          //表头条件
          emsListNo: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          hawb: '',
          trafMode: '',
          grossWt: '',
          packNum: '',
          tradeMode: '',
          ieport: '',
          entryInsertUser: '',

          //表体
          copGNo: '',
          gno: '',
          emsNo: '',
          gname: '',
          codeTS: '',
          gmodel: '',
          originCountry: '',
          exgVersion: '',
          entryNo: ''
        },
        cmbDataSource: {
          emsNoList: []
        }
      }
    },
    created: function () {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbDataSource.emsNoList = tmpArr
      }).catch(() => {
      })
    },
    methods: {
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    },
    computed: {
      isBonded() {
        return this.$route.fullPath.endsWith('/iEntryInbond')
      }
    }
  }
</script>

<style scoped>
</style>
