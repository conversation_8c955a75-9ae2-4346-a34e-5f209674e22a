<template>
  <section>
      <div v-show="showHead">
        <XdoCard :bordered="false">
          <div ref="area_head">
            <XdoBreadCrumb show-icon >
              <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
              <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            </XdoBreadCrumb>
          </div>
          <div ref="area_search">
            <div v-show="showSearch">
              <div class="separateLine"></div>
              <EntryReportSearch ref="headSearch"></EntryReportSearch>
            </div>
          </div>
        </XdoCard>
        <XdoCard :bordered="false">
          <div class="action" ref="area_actions">
            <template v-for="item in actions">
              <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                         style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
                <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
                {{ item.label }}
              </XdoButton>&nbsp;
            </template>
          </div>
        </XdoCard>
        <XdoCard :bordered="false">
          <xdo-ag-grid class="dc-table" ref="table" v-if="tableShow" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                       :data="gridConfig.data" stripe border></xdo-ag-grid>
          <div ref="area_page">
            <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                     :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                     @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          </div>
        </XdoCard>
      </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI, excelExport } from '@/api'
  import { columns } from './entryReportListColumns'
  import EntryReportSearch from './EntryReportSearch'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { dynamicHeight, getColumnsByConfig } from '@/common'
  import { getGridExportColumns } from '../../cs-common/function'
  import { pageParam, importExportManage } from '../../cs-common'

  export default {
    name: 'EntryUnbondBody',
    components: {
      EntryReportSearch
    },
    mixins: [dynamicHeight, columns],
    props: {
      tabName: {
        type: String,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        type: 'text',
        loading: false,
        disabled: false
      }
      return {
        tableId: '',
        showHead: true,
        tableShow: true,
        totalColumns: [],
        columnsConfig: [],
        showSearch: false,
        alltotalColumns: [],
        showtableColumnSetup: false,
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          gridColumns: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1,
          pageSizeOpts: [10, 20, 50, 100]
        },
        importExportManage: importExportManage,
        cmbDataSource: {
          overseasShipperList: [],
          feeMarkList: importExportManage.feeTypeMap
        },
        actions: [{
          ...btnComm, label: '导出', key: 'xdo-btn-export', icon: 'ios-cloud-download-outline', click: this.handleDownload
        }, {
          ...btnComm, label: '自定义配置', key: 'xdo-btn-export', icon: 'ios-cog', click: this.handleTableColumnSetup
        }]
      }
    },
    created: function () {
      let me = this
      me.$http.post(csAPI.ieParams.PRD).then(res => {
        me.$set(me.cmbDataSource, 'overseasShipperList', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbDataSource, 'overseasShipperList', [])
      })
    },
    mounted: function () {
      let me = this
      me.refreshDynamicHeight(190, !me.showSearch ? ['area_search'] : null)
      me.handleSearchSubmit()
      me.tableId = me.$route.path + `/body`
      me.totalColumns = [...me.headColumns, ...me.unbondtaxColumns, ...me.bodyColumns, ...me.exColumns, ...me.bodyexColumns]
      me.totalColumns.forEach(item => {
        me.columnsConfig.push(item.key)
      })
      let columns = me.$bom3.showTableColumns(me.tableId, me.totalColumns)
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, me.columnsConfig)
    },
    methods: {
      handleUpdateSearch() {
        let me = this
        me.showElements = me.$bom3.userCustom('form', me.formResId, me.elements)
        if (me.showElements) {
          me.searchLines = Math.ceil(me.showElements.length / 3)
        }
      },
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(190, !me.showSearch ? ['area_search'] : null)
      },
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      getList() {
        let me = this
        pageParam.page = me.pageParam.page
        pageParam.limit = me.pageParam.limit
        const data = me.$refs.headSearch.searchParam
        me.$http.post(csAPI.reportCenter.iEntry.selectAllPaged, data, {params: pageParam}).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
        }).catch(() => {
        })
      },
      handleDownload() {
        let me = this,
          header = []
        me.actions[0].loading = true
        getGridExportColumns(me.alltotalColumns).forEach(item => {
          if (item.key !== 'selection' && item.key !== 'operation') {
            header.push(item)
          }
        })
        excelExport(csAPI.reportCenter.iEntry.export, {
          header: header,
          name: me.$route.meta.title,
          exportColumns: Object.assign({}, me.$refs.headSearch.searchParam)
        }).finally(() => {
          me.actions[0].loading = false
        })
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        if (me.pageParam.page === 1) {
          me.getList()
        }
      },
      handleTableColumnSetup() {
        let me = this
        me.showtableColumnSetup = true
      },
      handleUpdateColumn(columns) {
        let me = this
        // 解决iView table 的问题
        me.tableShow = false
        me.$nextTick(() => {
          me.tableShow = true
        })
        me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, me.columnsConfig)
      }
    }
  }
</script>

<style scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
