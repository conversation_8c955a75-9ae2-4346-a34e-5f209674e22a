<template>
    <section>
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon></XdoBreadCrumb>
        </div>
      </XdoCard>
      <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
        <TabPane name="headTab" label="表头">
          <EntryUnbondHead v-if="tabName === 'headTab'" :tabName="tabName"></EntryUnbondHead>
        </TabPane>
        <TabPane name="bodyTab" label="表头和表体">
          <EntryUnbondBody v-if="tabName === 'bodyTab'" :tabName="tabName"></EntryUnbondBody>
        </TabPane>
      </XdoTabs>
    </section>
</template>

<script>
  import EntryUnbondBody from './EntryUnbondBody'
  import EntryUnbondHead from './EntryUnbondHead'

  export default {
    name: 'EntryUnbondTab',
    components: {
      EntryUnbondHead,
      EntryUnbondBody
    },
    data() {
      return {
        tabName: 'headTab'
      }
    }
  }
</script>

<style scoped>
</style>
