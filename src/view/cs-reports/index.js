import { namespace } from '@/project'
import xinYueList from './xinyue/xinyue-list'
import ImportBill from './ie-bill/importBill'
import ExportBill from './ie-bill/exportBill'
import ShipmentList from './shipment-list/shipment-list'
import financeReport1 from './financeReport/financeReport1'
import financeReport2 from './financeReport/financeReport2'
import ImBondedBillReport from './ie-dec-bill/dec-bill-i-bonded'
import ExBondedBillReport from './ie-dec-bill/dec-bill-e-bonded'
import ImUnBondedBillReport from './ie-dec-bill/dec-bill-i-unbonded'
import ExUnBondedBillReport from './ie-dec-bill/dec-bill-e-unbonded'
import StatisticalReportMain from './statistical-report/statistical-report-main'
import StatisticalReportGroupMain from './statistical-report-group/statistical-report-group-main'
import IBillOfLadingReport from './bill-of-lading/new-tabs/bill-of-lading-i-tabs'
import EBillOfLadingReport from './bill-of-lading/new-tabs/bill-of-lading-e-tabs'
import FreightManagementIList from './freight-management/freight-management-i-list'
import FreightManagementEList from './freight-management/freight-management-e-list'
import CustomsDeclarationIBondedTabs from './customs-declaration/customs-declaration-i-bonded-tabs'
import CustomsDeclarationEBondedTabs from './customs-declaration/customs-declaration-e-bonded-tabs'
import exportDocumentStatisticsList from './export-document-statistics/export-document-statistics-list'
import CustomsDeclarationINonBondedTabs from './customs-declaration/customs-declaration-i-non-bonded-tabs'
import CustomsDeclarationENonBondedTabs from './customs-declaration/customs-declaration-e-non-bonded-tabs'
import DecIReportFt from './dec-report-ft/dec-i-report-ft'
import DecEReportFt from './dec-report-ft/dec-e-report-ft'

export default [
  {
    path: '/' + namespace + '/reports/iBillInbond',
    name: 'decBillIBonded',
    meta: {
      icon: 'ios-document',
      title: '进口保税清单明细'
    },
    component: ImBondedBillReport
  },
  {
    path: '/' + namespace + '/reports/iBillUnbond',
    name: 'decBillIUnBonded',
    meta: {
      icon: 'ios-document',
      title: '进口非保税清单明细'
    },
    component: ImUnBondedBillReport
  },
  {
    path: '/' + namespace + '/reports/eBillInbond',
    name: 'decBillEBonded',
    meta: {
      icon: 'ios-document',
      title: '出口保税清单明细'
    },
    component: ExBondedBillReport
  },
  {
    path: '/' + namespace + '/reports/eBillUnbond',
    name: 'decBillEUnBonded',
    meta: {
      icon: 'ios-document',
      title: '出口非保税清单明细'
    },
    component: ExUnBondedBillReport
  },
  {
    path: '/' + namespace + '/reports/iEntryInbond',
    name: 'customsDeclarationIBondedTabs',
    meta: {
      icon: 'ios-document',
      title: '进口保税报关单'
    },
    component: CustomsDeclarationIBondedTabs
  },
  {
    path: '/' + namespace + '/reports/iEntryUnbond',
    name: 'customsDeclarationINonBondedTabs',
    meta: {
      icon: 'ios-document',
      title: '进口非保税报关单'
    },
    component: CustomsDeclarationINonBondedTabs
  },
  {
    path: '/' + namespace + '/reports/eEntryInbond',
    name: 'customsDeclarationEBondedTabs',
    meta: {
      icon: 'ios-document',
      title: '出口保税报关单'
    },
    component: CustomsDeclarationEBondedTabs
  },
  {
    path: '/' + namespace + '/reports/eEntryUnbond',
    name: 'customsDeclarationENonBondedTabs',
    meta: {
      icon: 'ios-document',
      title: '出口非保税报关单'
    },
    component: CustomsDeclarationENonBondedTabs
  },
  {
    path: '/' + namespace + '/reports/iBillOfLadingReport',
    name: 'billOfLadingITabs',
    meta: {
      icon: 'ios-document',
      title: '进口预录入单'
    },
    component: IBillOfLadingReport
  },
  {
    path: '/' + namespace + '/reports/eBillOfLadingReport',
    name: 'billOfLadingETabs',
    meta: {
      icon: 'ios-document',
      title: '出口预录入单'
    },
    component: EBillOfLadingReport
  },
  {
    path: '/' + namespace + '/reports/ImportBill',
    name: 'importBill',
    meta: {
      icon: 'ios-document',
      title: '进口报表'
    },
    component: ImportBill
  },
  {
    path: '/' + namespace + '/reports/ExportBill',
    name: 'exportBill',
    meta: {
      icon: 'ios-document',
      title: '出口报表'
    },
    component: ExportBill
  },
  {
    path: '/' + namespace + '/reports/financeReport1',
    name: 'financeReport1',
    meta: {
      icon: 'ios-document',
      title: '财务报表1'
    },
    component: financeReport1
  },
  {
    path: '/' + namespace + '/reports/financeReport2',
    name: 'financeReport2',
    meta: {
      icon: 'ios-document',
      title: '财务报表2'
    },
    component: financeReport2
  },
  {
    path: '/' + namespace + '/reports/XinYue',
    name: 'xinYueList',
    meta: {
      icon: 'ios-document',
      title: '信越受注残报表'
    },
    component: xinYueList
  },
  {
    path: '/' + namespace + '/reports/ShipmentList',
    name: 'shipmentList',
    meta: {
      icon: 'ios-document',
      title: 'Shipment List 报表'
    },
    component: ShipmentList
  },
  {
    path: '/' + namespace + '/reports/FreightManagementIList',
    name: 'freightManagementIList',
    meta: {
      icon: 'ios-document',
      title: '进口运费管理'
    },
    component: FreightManagementIList
  },
  {
    path: '/' + namespace + '/reports/FreightManagementEList',
    name: 'freightManagementEList',
    meta: {
      icon: 'ios-document',
      title: '出口运费管理'
    },
    component: FreightManagementEList
  },
  {
    path: '/' + namespace + '/reports/statisticalReportMain',
    name: 'statisticalReportMain',
    meta: {
      icon: 'ios-document',
      title: '统计报表'
    },
    component: StatisticalReportMain
  },
  {
    path: '/' + namespace + '/reports/statisticalReportMainGroup',
    name: 'StatisticalReportGroupMain',
    meta: {
      icon: 'ios-document',
      title: '集团统计报表'
    },
    component: StatisticalReportGroupMain
  },
  {
    path: '/' + namespace + '/reports/exportDocumentStatisticsList',
    name: 'exportDocumentStatisticsList',
    meta: {
      icon: 'ios-document',
      title: '出口单证统计表'
    },
    component: exportDocumentStatisticsList
  },
  {
    path: '/' + namespace + '/reports/decIReportFt',
    name: 'decIReport',
    meta: {
      icon: 'ios-document',
      title: '进口报表(丰田)'
    },
    component: DecIReportFt
  },
  {
    path: '/' + namespace + '/reports/decEReportFt',
    name: 'decEReport',
    meta: {
      icon: 'ios-document',
      title: '出口报表(丰田)'
    },
    component: DecEReportFt
  }
]
