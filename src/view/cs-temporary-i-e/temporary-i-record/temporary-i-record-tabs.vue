<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <TemporaryIRecordEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></TemporaryIRecordEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <TemporaryIRecordBodyList :head-id="currConfig.editData.sid"></TemporaryIRecordBodyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { temporaryIETabs } from '../comm/temporaryIETabs'
  import TemporaryIRecordEdit from './temporary-i-record-edit'
  import TemporaryIRecordBodyList from './temporary-i-record-body-list'

  export default {
    name: 'temporaryIRecordTabs',
    components: {
      TemporaryIRecordEdit,
      TemporaryIRecordBodyList
    },
    mixins: [temporaryIETabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
