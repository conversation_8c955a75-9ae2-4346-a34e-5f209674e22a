<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110" class="dc-form-4"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 4px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'reShipmentEManagementEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        cmbSource: {
          status: [{
            value: '0', label: '暂存'
          }, {
            value: '1', label: '报关生成'
          }]
        },
        ajaxUrl: {
          insert: csAPI.temporaryIE.reShipmentE.head.insert,
          update: csAPI.temporaryIE.reShipmentE.head.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.fieldsReset()
          if (me.buttons[me.buttons.findIndex(btn => btn.key === 'save')]) {
            me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
          }
          me.setCompleteDateDisable()
        }
      },
      'editConfig.editData.status': {
        immediate: true,
        handler: function () {
          let me = this
          me.setCompleteDateDisable()
        }
      }
    },
    methods: {
      /**
       * 编辑字段
       */
      getFields() {
        return [{
          isCard: true,
          title: '表头信息',
          key: '1212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-5'
        }, {
          title: '状态',
          key: 'status',
          type: 'select',
          props: {
            disabled: true
          }
        }, {
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          type: 'datePicker',
          key: 'declareDate',
          title: '报关单申报日期',
          props: {
            disabled: true
          }
        }, {
          key: 'entryNo',
          title: '报关单号',
          props: {
            disabled: true
          }
        }, {
          type: 'datePicker',
          key: 'completeDate',
          title: '复运完成日期'
        }]
      },
      /**
       * 设置复运完成日期(是否可编辑)
       */
      setCompleteDateDisable() {
        let me = this
        me.$nextTick(() => {
          let eStatus = me.editConfig.editStatus,
            status = me.editConfig.editData.status
          if (status === '1' && eStatus === editStatus.EDIT) {
            me.setDisable('completeDate', false)
          } else {
            me.setDisable('completeDate', true)
          }
          if (status !== '1' && eStatus !== editStatus.SHOW) {
            me.setDisable('emsListNo', false)
          } else {
            me.setDisable('emsListNo', true)
          }
        })
      },
      /**
       * 返回主界面
       */
      handleBack() {
        let me = this
        me.$emit('backToList')
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        me.doSave(res => {
          me.$emit('onHeadSaved', {
            editData: res.data.data,
            editStatus: editStatus.EDIT
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
