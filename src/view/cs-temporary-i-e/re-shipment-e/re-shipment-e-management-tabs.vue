<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <ReShipmentEManagementEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></ReShipmentEManagementEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <ReShipmentEManagementBodyList :head-id="currConfig.editData.sid"></ReShipmentEManagementBodyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { temporaryIETabs } from '../comm/temporaryIETabs'
  import ReShipmentEManagementEdit from './re-shipment-e-management-edit'
  import ReShipmentEManagementBodyList from './re-shipment-e-management-body-list'

  export default {
    name: 'reShipmentEManagementTabs',
    components: {
      ReShipmentEManagementEdit,
      ReShipmentEManagementBodyList
    },
    mixins: [temporaryIETabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
