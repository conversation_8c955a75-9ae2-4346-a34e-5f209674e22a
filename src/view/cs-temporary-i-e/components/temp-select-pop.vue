<template>
  <XdoModal width="600" mask v-model="show" title="报关生成"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm ref="dataForm" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="templateId" label="模版">
          <xdo-select v-model="frmData.templateId" :options="this.cmbSource.templateData" :optionLabelRender="item => item.label"></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'

  export default {
    name: 'tempSelectPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {
          templateId: ''
        },
        rulesHeader: {},
        cmbSource: {
          templateData: []
        },
        buttons: [
          {...btnComm, click: this.handleConfirm, icon: 'dc-btn-save', label: '确定'},
          {...btnComm, click: this.handleClose, icon: 'dc-btn-cancel', label: '关闭'}
        ]
      }
    },
    created: function () {
      let me = this
      me.$http.post(csAPI.csImportExport.template.selectAllPaged, {
        tempName: '',
        bondMark: '1',
        iemark: me.ieMark
      }).then(res => {
        me.$set(me.cmbSource, 'templateData', res.data.data.map(item => {
          return {
            value: item.sid,
            label: item.tempName
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'templateData', [])
      })
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (!show) {
            if (me.$refs['dataForm']) {
              me.$refs['dataForm'].resetFields()
            }
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$refs['dataForm'].validate().then(isValid => {
          if (isValid) {
            me.buttons[0].loading = true
            me.$emit('doDecGeneration', me.frmData.templateId)
            setTimeout(function () {
              me.buttons[0].loading = false
            }, 500)
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
