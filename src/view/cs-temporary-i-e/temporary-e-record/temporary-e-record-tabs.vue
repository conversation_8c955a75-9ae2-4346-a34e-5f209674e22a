<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <TemporaryERecordEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></TemporaryERecordEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <TemporaryERecordBodyList :head-id="currConfig.editData.sid"></TemporaryERecordBodyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { temporaryIETabs } from '../comm/temporaryIETabs'
  import TemporaryERecordEdit from './temporary-e-record-edit'
  import TemporaryERecordBodyList from './temporary-e-record-body-list'

  export default {
    name: 'temporaryERecordTabs',
    components: {
      TemporaryERecordEdit,
      TemporaryERecordBodyList
    },
    mixins: [temporaryIETabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
