<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110" class="dc-form-4"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 4px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'temporaryERecordEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        cmbSource: {
          assureType: [{
            value: '0', label: '保金'
          }, {
            value: '1', label: '保函'
          }]
        },
        ajaxUrl: {
          insert: csAPI.temporaryIE.temporaryERecord.head.insert,
          update: csAPI.temporaryIE.temporaryERecord.head.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.fieldsReset()
          if (me.buttons[me.buttons.findIndex(btn => btn.key === 'save')]) {
            me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
          }
        }
      }
    },
    methods: {
      getDeclareFullName() {
        let me = this,
          result = ''
        if (!isNullOrEmpty(me.detailConfig.model['declareCode'])) {
          result = me.detailConfig.model['declareCode'].trim()
        }
        if (!isNullOrEmpty(me.detailConfig.model['declareName'])) {
          result += ' ' + me.detailConfig.model['declareName'].trim()
        }
        return result.trim()
      },
      /**
       * 输入框
       * @returns {({title: string, key: string, props: {disabled: boolean}}|{title: string, key: string, props: {disabled: boolean}}|{title: string, key: string, props: {disabled: boolean}}|{specificValue: (function(): string), title: string, key: string, props: {disabled: boolean}})[]}
       */
      getFields() {
        let me = this
        return [{
          isCard: true,
          title: '表头信息',
          key: '1212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-5'
        }, {
          key: 'emsListNo',
          props: {
            disabled: true
          },
          title: '单据内部编号'
        }, {
          key: 'entryNo',
          title: '报关单号',
          props: {
            disabled: true
          }
        }, {
          props: {
            disabled: true
          },
          key: 'declareDate',
          type: 'datePicker',
          title: '报关单申报日期'
        }, {
          props: {
            disabled: true
          },
          key: 'declareCode',
          title: '报关单申报单位',
          specificValue: me.getDeclareFullName
        }, {
          type: 'select',
          title: '担保方式',
          key: 'assureType'
        }, {
          props: {
            intDigits: 10,
            precision: 2
          },
          type: 'xdoInput',
          key: 'assurePrice',
          title: '担保金额(RMB)'
        }, {
          key: 'endDate',
          type: 'datePicker',
          title: '最迟复运日期'
        }, {
          props: {
            disabled: true
          },
          type: 'datePicker',
          key: 'completeDate',
          title: '复运完成日期'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 255
          },
          itemClass: 'dc-merge-1-5'
        }]
      },
      /**
       * 返回主界面
       */
      handleBack() {
        let me = this
        me.$emit('backToList')
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.$emit('onHeadSaved', {
            editData: res.data.data,
            editStatus: editStatus.EDIT
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
