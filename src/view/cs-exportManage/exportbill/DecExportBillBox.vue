<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecExportBillBoxSearch ref="headSearch" :showSearch="showSearch"></DecExportBillBoxSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:print-invoice>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="downloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载发票 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(0,1)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(1,1)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:print-box>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="boxDownloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载箱单 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(0,2)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(1,2)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:print-contract>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="contractDownloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载合同 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(0,3)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(1,3)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:print-entrust>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="entrustLoading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载托书 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(0,4)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBase(1,4)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard>
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <DecExportTabs v-if="!showList" :edit-config="editConfig" :dataList="dataList" :compData="compData"
                   :templatecodelist="templateCodeList" @oneditback="backToList"></DecExportTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI, zipExport } from '@/api'
  import DecExportTabs from './DecExportTabs'
  import { editStatus } from '@/view/cs-common'
  import { getColumnsByConfig } from '@/common'
  import DecExportBillBoxSearch from './DecExportBillBoxSearch'
  import { columnsConfig, columns } from './DecExportBillBoxColumns'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { isNullOrEmpty, ArrayToLocaleLowerCase, getHttpHeaderFileName, blobSaveFile } from '@/libs/util'

  export default {
    name: 'DecExportBillBox',
    components: {
      DecExportTabs,
      DecExportBillBoxSearch
    },
    mixins: [commList, columns, pms],
    data() {
      return {
        isPdf: true,
        dataList: [],
        compData: [],
        downloading: false,
        templateCodeList: [],
        boxDownloading: false,
        entrustLoading: false,
        contractDownloading: false,
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'lock': this.handleLock,
          'print-box': this.printBox,
          'unlock': this.handleUnLock,
          'delete': this.handleDelete,
          'print-invoice': this.printBill,
          'print-contract': this.printContract
        },
        ajaxUrl: {
          lock: csAPI.exportBill.clockbill.clockBill,
          delete: csAPI.exportBill.deletebill.deleteBill,
          selectAllPaged: csAPI.exportBill.search.searchInfor,
          getTemplateList: csAPI.exportBill.templatelist.templateList
        }
      }
    },
    created: function() {
      let me = this
      me.getClientForwarder()
    },
    mounted: function() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.loadFunctions().then()
    },
    methods: {
      /**
       * 获取客户信息 + 货代
       */
      getClientForwarder() {
        let me = this
        me.$http.post(csAPI.exportBill.custumer.custumerInfor + `/CLI`).then(res => {
          me.$set(me, 'dataList', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
        me.$http.post(csAPI.exportBill.custumer.custumerInfor + `/FOD`).then(res => {
          me.$set(me, 'compData', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
      },
      afterSearch() {
        let me = this
        me.$set(me, 'dataList', [])
        // me.$set(me, 'compData', [])
        me.$set(me, 'templateCodeList', [])
      },
      /**
       * 点击编辑按钮执行
       */
      handleEdit() {
        let me = this
        if (me.checkRowSelected('编辑', true)) {
          let selData = me.gridConfig.selectRows[0]
          if (selData.status === '1') {
            me.$Message.warning('锁定数据不可编辑!')
          } else {
            me.$http.post(me.ajaxUrl.getTemplateList, null, {
              params: {
                iemark: 'E',
                customerCode: selData.customerCode
              }
            }).then(res => {
              me.$set(me, 'templateCodeList', res.data.data)
              me.handleEditByRow(selData)
            }).catch(() => {
            })
          }
        }
      },
      /**
       * 列表中点击数据展示
       * @param row
       */
      handleViewByRow(row) {
        let me = this
        me.$http.post(me.ajaxUrl.getTemplateList, null, {
          params: {
            iemark: 'E',
            customerCode: row.customerCode
          }
        }).then(res => {
          me.$set(me, 'templateCodeList', res.data.data)
          me.$set(me.editConfig, 'editData', row)
          me.$set(me.editConfig, 'editStatus', editStatus.SHOW)
          me.$set(me, 'showList', false)
        }).catch(() => {
        })
      },
      /**
       * 数据删除
       */
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 锁定
       */
      handleLock() {
        let me = this
        if (me.checkRowSelected('锁定')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认锁定所选项吗',
            onOk: () => {
              me.setToolbarLoading('lock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lock}/1/${params}`).then(() => {
                me.$Message.success('锁定成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('lock', false)
              })
            }
          })
        }
      },
      /**
       * 解锁
       */
      handleUnLock() {
        let me = this
        if (me.checkRowSelected('解锁')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认解锁所选项吗',
            onOk: () => {
              me.setToolbarLoading('unlock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lock}/0/${params}`).then(() => {
                me.$Message.success('锁定成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('unlock', false)
              })
            }
          })
        }
      },
      downloadStreamFile(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], { type: `application/pdf` })
        blobSaveFile(blob, filename)
      },
      /**
       * 获取Blob
       * @param base64 base64字符串
       * @param contentType 导出格式 MIME 类型
       * @param sliceSize 分割大小
       * @returns {Blob}
       */
      getBlob(base64, contentType, sliceSize) {
        contentType = contentType || ''
        sliceSize = sliceSize || 512
        let byteArrays = [],
          byteCharacters = atob(base64)
        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          let slice = byteCharacters.slice(offset, offset + sliceSize)
          let byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          let byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        return new Blob(byteArrays, { type: contentType })
      },
      /**
       * 基础打印
       * @param isPdf
       * @param billType(1:发票 2:箱单 3:合同 4:托书)
       */
      printBase(isPdf, billType) {
        let me = this,
          contentType = ''
        if (isPdf === 0) {
          me.$set(me, 'isPdf', true)
          contentType = 'application/pdf'
        } else if (isPdf === 1) {
          me.$set(me, 'isPdf', false)
          contentType = 'application/vnd.ms-excel'
        } else {
          return
        }
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要打印的数据')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('只能选择一条数据打印')
        } else {
          if (billType === 1) {
            me.setToolbarLoading('print-invoice', true)
            me.$set(me, 'downloading', true)
          } else if (billType === 2) {
            me.setToolbarLoading('print-box', true)
            me.$set(me, 'boxDownloading', true)
          } else if (billType === 3) {
            me.setToolbarLoading('print-contract', true)
            me.$set(me, 'contractDownloading', true)
          } else if (billType === 4) {
            me.setToolbarLoading('print-entrust', true)
            me.$set(me, 'entrustLoading', true)
          }
          me.$http.post(csAPI.exportBill.printbill.printBill, '', {
            params: {
              isPdf: me.isPdf,
              billType: billType,
              headId: me.gridConfig.selectRows[0].sid
            }
          }).then(res => {
            res.data.data.forEach(item => {
              const blob = me.getBlob(item.key, contentType)
              blobSaveFile(blob, item.value)
            })
          }).catch(() => {
          }).finally(() => {
            if (billType === 1) {
              me.$set(me, 'downloading', false)
              me.setToolbarLoading('print-invoice')
            } else if (billType === 2) {
              me.$set(me, 'boxDownloading', false)
              me.setToolbarLoading('print-box')
            } else if (billType === 3) {
              me.$set(me, 'contractDownloading', false)
              me.setToolbarLoading('print-contract')
            } else if (billType === 4) {
              me.$set(me, 'entrustLoading', false)
              me.setToolbarLoading('print-entrust')
            }
          })
        }
      },
      // 打印发票
      printBill(e) {
        let me = this
        me.printBase(e, 1)
      },
      // 打印箱单
      printBox(e) {
        let me = this
        me.printBase(e, 2)
      },
      // 打印合同
      printContract(e) {
        let me = this
        me.printBase(e, 3)
      },
      statusName(val) {
        if (val === '0') {
          return '未锁定'
        } else if (val === '1') {
          return '锁定'
        }
      },
      codeConvertName(val) {
        if (val === null) {
          return val
        } else {
          let item = this.compData.find(p => p.key === val)
          if (!item) {
            return val
          }
          return item.value
        }
      },
      // 如果选择使用电子签章
      handleDownloadPdf(row) {
        if (!isNullOrEmpty(row.sid)) {
          zipExport(csAPI.importBill.downloadPdf.downloadPdf, {
            ieMark: 'E',
            headId: row.sid,
            emsListNo:row.emsListNo,
            invoiceNo:row.invoiceNo
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
