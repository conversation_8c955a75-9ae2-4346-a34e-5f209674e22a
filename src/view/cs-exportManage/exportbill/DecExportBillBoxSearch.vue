<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem label="内部编号">
        <XdoIInput v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="发票号">
        <XdoIInput v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="合同号">
        <XdoIInput v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="客户名称">
        <XdoIInput v-model="searchParam.consigneeName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="目的地">
        <XdoIInput v-model="searchParam.destination"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="运输方式">
        <xdo-select :asyncOptions="pcodeList" :meta="pcode.transf" v-model="searchParam.trafMode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="货运代理">
        <xdo-select v-model="searchParam.forwardCode" clearable :options="inDataSource.forwardCode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'DecExportBillBoxSearch',
    props: {
      showSearch: {
        type: Boolean,
        default: () => false
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',    // 关联编号
          invoiceNo: '',    // 发票号
          contrNo: '',      // 合同号
          customerCode: '', // 客户代码
          destination: '',  // 目的地
          trafMode: '',     // 运输方式
          forwardCode: '',  // 货运代理
        },
        inDataSource: {}
      }
    },
    watch: {
      showSearch: {
        immediate: true,
        handler: function (val) {
          if (val) {
            this.custumerInfor()
            this.forWardInfor()
          }
        }
      }
    },
    methods: {
      // 获取客户信息+货代
      custumerInfor() {
        let me = this
        me.$http.post(csAPI.exportBill.custumer.custumerInfor + `/CLI`).then(res => {
          me.$set(me.inDataSource, 'custumerType', ArrayToLocaleLowerCase(res.data.data))
        }, () => {
        })
      },
      forWardInfor() {
        let me = this
        me.$http.post(csAPI.exportBill.custumer.custumerInfor + `/FOD`).then(res => {
          me.$set(me.inDataSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
        }, () => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
