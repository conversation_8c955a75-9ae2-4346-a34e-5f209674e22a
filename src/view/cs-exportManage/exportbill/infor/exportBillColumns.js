
const columnsConfig = ['templateName', 'billType', 'billNo', 'sid']

const columns = {
  data() {
    return {
      totalColumns: [
        {
          minWidth: 120,
          align: 'center',
          title: '模版名称',
          key: 'templateName'
        },
        {
          minWidth: 120,
          align: 'center',
          key: 'billType',
          title: '单据类型',
          render: (h, params) => {
            return h('span', this.covername(params.row.billType))
          }
        },
        {
          minWidth: 120,
          key: 'billNo',
          title: '单据号',
          align: 'center'
        },
        {
          key: 'sid',
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
