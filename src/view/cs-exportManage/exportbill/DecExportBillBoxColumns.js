import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'templateCode'
  , 'status'
  , 'emsListNo'
  , 'shipmentDate'
  , 'invoiceNo'
  , 'contrNo'
  , 'customerCode'
  , 'customerName'
  , 'consigneeName'
  , 'trafMode'
  , 'forwardCode'
  , 'destination'
  , 'grossWt'
  , 'netWt'
  , 'wrapType'
  , 'packNum'
  , 'insertTime'
  , 'note'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          key: 'selection',
          type: 'selection'
        },
        {
          width: 116,
          title: '操作',
          customize: true,
          key: 'operation',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                },
                on: {
                  click: () => {
                    this.handleDownloadPdf(params.row)
                  }
                }
              }, '下载'),
              h('a', {
                props: {
                  type: 'primary'
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleViewByRow(params.row)
                  }
                }
              }, '查看')
            ])
          }
        },
        {
          width: 150,
          tooltip: true,
          title: '模板编号',
          key: 'templateCode'
        },
        {
          width: 120,
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return h('span', this.statusName(params.row.status))
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'emsListNo',
          title: '内部编号'
        },
        {
          width: 100,
          title: '出货日期',
          key: 'shipmentDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 120,
          key: 'contrNo',
          title: '合同号'
        },
        {
          width: 120,
          title: '客户名称',
          key: 'consigneeName'
        },
        {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 120,
          title: '货运代理',
          key: 'forwardCode',
          render: (h, params) => {
            return h('span', this.codeConvertName(params.row.forwardCode))
          }
        },
        {
          width: 120,
          title: '目的地',
          key: 'destination'
        },
        {
          width: 120,
          key: 'grossWt',
          title: '总毛重'
        },
        {
          width: 120,
          key: 'netWt',
          title: '总净重'
        },
        {
          width: 120,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 120,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 100,
          title: '录入时间',
          key: 'insertTime'
        },
        {
          width: 120,
          key: 'note',
          title: '备注'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
