<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="headerEditFrom" class="dc-form xdo-enter-form" label-position="right" :label-width="130"
             :model="headerData" :rules="rulesHeader" style="padding: 0; grid-column-gap: 0;">
      <Card :bordered="false" class="dc-merge-1-4">
        <p slot="title">基础信息</p>
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem ref="fiEmsListNo" prop="emsListNo" label="单据内部编号" class="btnInput">
            <Input type="text" v-model="headerData.emsListNo" :disabled="!emsListNoDisable" :maxlength="32">
              <Button slot="append" type="dashed" @click="openSelectTemplate" :disabled="!isNew">选择模板</Button>
            </Input>
          </XdoFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="shipDate" label="出货日期">
            <XdoDatePicker type="date" v-model="headerData.shipDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.forwardCode" prop="forwardCode" ref="fiForwardCode" label="货运代理">
            <xdo-select v-model="headerData.forwardCode" :options="this.cmbDataSource.forwardCodeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.invoiceNo" prop="invoiceNo" ref="fiInvoiceNo" label="发票号">
            <XdoIInput type="text" v-model="headerData.invoiceNo" :maxlength="100" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.contrNo" prop="contrNo" ref="fiContrNo" label="合同协议号">
            <XdoIInput type="text" v-model="headerData.contrNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="trafMode" label="运输方式">
            <xdo-select v-model="headerData.trafMode" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.transf" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.wrapType" prop="wrapType" ref="fiWrapType" label="包装种类">
            <xdo-select v-model="headerData.wrapType" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.wrap" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="wrapType2" label="其他包装" class="dc-merge-2-4">
            <Input v-model="wrapType2View" placeholder="请选择包装种类..." disabled>
              <XdoButton slot="append" type="primary" :disabled="showDisable" @click="onWrapType2Search" style="width: 100%;">请选择</XdoButton>
            </Input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.packNum" prop="packNum" ref="fiPackNum" label="件数">
            <xdo-input v-model="headerData.packNum" number int-length="9" precision="0" notConvertNumber :disabled="showDisable"></xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.netWt" prop="netWt" ref="fiNetWt" label="总净重">
            <div class="shortAppend">
              <xdo-input v-model="headerData.netWt" decimal int-length="13" :precision="precisionsConfig.netWtE" notConvertNumber :disabled="showDisable">
                <span slot="append">KG</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.grossWt" prop="grossWt" ref="fiGrossWt" label="总毛重">
            <div class="shortAppend">
              <xdo-input v-model="headerData.grossWt" decimal int-length="13" :precision="precisionsConfig.grossWtE" notConvertNumber :disabled="showDisable">
                <span slot="append">KG</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="volume" label="体积">
            <div class="shortAppend">
              <xdo-input v-model="headerData.volume" decimal int-length="11" :precision="precisionsConfig.headVolumeDigitE" :disabled="showDisable">
                <span slot="append">m³</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.overseasShipper" prop="overseasShipper" label="境外收货人">
            <xdo-select v-model="headerData.overseasShipper" tooltip :options="this.cmbDataSource.overseasShipperList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable" @on-change="overseasShipperChange"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="shipTo" label="Ship To">
            <xdo-select v-model="headerData.shipTo" :options="this.cmbSource.shipToData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="tradeCountry" label="运抵国(地区)">
            <xdo-select v-model="headerData.tradeCountry" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="destPort" label="指运港">
            <xdo-select v-model="headerData.destPort" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.port_lin" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="ieport" label="出境关别">
            <xdo-select v-model="headerData.ieport" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="entryPort" label="离境口岸">
            <xdo-select v-model="headerData.entryPort" :disabled="showDisable" :asyncOptions="pcodeList" meta="CIQ_ENTY_PORT" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="destinationCountry" label="最终目的国">
            <xdo-select v-model="headerData.destinationCountry" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.tradeNation" prop="tradeNation" ref="fiTradeNation" label="贸易国(地区)">
            <xdo-select v-model="headerData.tradeNation" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="remark" label="内部备注">
            <XdoIInput type="text" v-model="headerData.remark" :disabled="showDisable"></XdoIInput>
          </DcFormItem>

          <!--          /**LG定制 start*/-->
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="inviteDate" label="申请出口日期">
            <XdoDatePicker type="date" v-model="headerData.inviteDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="cweight" label="计费重量">
            <xdo-input v-model="headerData.cweight" decimal int-length="5" precision="2" notConvertNumber :disabled="showDisable"></xdo-input>
          </DcFormItem>
          <!--          /**LG定制 end*/-->

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="ownerCreditCode" label="生产销售单位代码">
            <xdo-select v-model="headerData.ownerCreditCode" :disabled="showDisable" :options="this.cmbDataSource.ownerCode" :optionLabelRender="pcodeRender"
                        @on-change="onOwnerCodeEnter"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="ownerName" label="生产销售单位名称">
            <XdoIInput type="text" v-model="headerData.ownerName" disabled></XdoIInput>
          </DcFormItem>
        </div>
      </Card>
      <Card :bordered="false" class="dc-merge-1-4" style="margin-top: 0;">
        <p slot="title">申报信息</p>
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="declareCodeCustoms" label="报关行简码及名称">
            <xdo-select v-model="headerData.declareCodeCustoms" :disabled="declareDisable"
                        :options="this.cmbDataSource.cutData" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareCode" label="海关十位代码">
            <XdoIInput type="text" v-model="headerData.declareCode" :disabled="declareDisable" @on-enter="declareCodeEnter"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareCreditCode" label="社会信用代码">
            <XdoIInput type="text" v-model="headerData.declareCreditCode" disabled @on-blur="declareCreditCodeEnter"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="emsNo" label="备案号">
            <xdo-select v-model="headerData.emsNo" :options="this.cmbDataSource.emsNoList" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="tradeModePreRequire" ref="fiTradeMode" prop="tradeMode" label="监管方式">
            <xdo-select v-model="headerData.tradeMode" :disabled="showDisable"
                        :options="this.filterTradeMode" :optionLabelRender="pcodeRender" @on-change="tradeModeChange"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="cutMode" label="征免性质">
            <xdo-select v-model="headerData.cutMode" :disabled="cutModeDisabled" :asyncOptions="pcodeList" :meta="pcode.levytype" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.transMode" prop="transMode" ref="transMode" label="成交方式">
            <xdo-select v-model="headerData.transMode" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.transac" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="licenseNo" label="许可证号">
            <XdoIInput type="text" v-model="headerData.licenseNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billType" label="清单归并类型">
            <xdo-select v-model="headerData.billType" :options="this.cmbDataSource.billTypeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="mergeType" label="报关单归并类型">
            <xdo-select v-model="headerData.mergeType" :options="this.cmbDataSource.mergeTypeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="voyageDate" label="航班日期">
            <XdoDatePicker type="date" v-model="headerData.voyageDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="mawb" label="主提运单号">
            <XdoIInput type="text" v-model="headerData.mawb" :maxlength="50" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="hawb" label="提运单号">
            <XdoIInput type="text" v-model="headerData.hawb" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.trafName" prop="trafName" ref="fiTrafName" label="运输工具及航次">
            <XdoIInput type="text" v-model="headerData.trafName" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="masterCustomsRequired" prop="masterCustoms" label="申报地海关">
            <xdo-select v-model="headerData.masterCustoms" :asyncOptions="pcodeList" transfer
                        :meta="pcode.customs_rel" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.feeMark" prop="feeMark" label="运费" ref="feeMark">
            <FeeCascader :options="feeOptions" @onFeeDataChanged="onFeeDataChanged" :disabled="transportDisabled"></FeeCascader>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.insurMark" prop="insurMark" label="保费" ref="insurMark">
            <FeeCascader :options="insurOptions" @onFeeDataChanged="onInsurDataChanged" :disabled="insuranceDisabled"></FeeCascader>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="otherMark" label="杂费">
            <FeeCascader :options="otherOptions" @onFeeDataChanged="onOtherDataChanged" can-minus :disabled="showDisable"></FeeCascader>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.tradeTerms" prop="tradeTerms" ref="fiTradeTerms" label="贸易条款">
            <xdo-select v-model="headerData.tradeTerms" :disabled="showDisable" clearable :options="this.cmbDataSource.tradeTermNewList"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.districtCode" prop="districtCode" ref="fiDistrictCode" label="境内货源地" class="dc-merge-2-4">
            <AreaPostCascader :options="areaOptions" @onAreaDataChanged="onAreaDataChanged" :disabled="showDisable"></AreaPostCascader>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="receiveName" label="境内发货人名称">
            <XdoIInput type="text" v-model="receiveCodeName"  disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dclcusMark" ref="fiDclcusMark" label="报关标志">
            <xdo-select v-model="headerData.dclcusMark" :options="this.cmbDataSource.dclcusMarkData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dclcusType" ref="fiDclcusType" label="报关类型">
            <xdo-select v-model="headerData.dclcusType" :options="this.cmbDataSource.dclcusTypeData"
                        :optionLabelRender="pcodeRender" :disabled="clearanceDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="entryType" label="报关单类型">
            <xdo-select v-model="headerData.entryType" :options="this.cmbDataSource.entryTypeData"
                        :optionLabelRender="pcodeRender" :disabled="clearanceDisable"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billListType" label="清单类型">
            <xdo-select v-model="headerData.billListType" :options="importExportManage.BILL_TYPE_MAP"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="relEmsNoRequiredTips" prop="relEmsNo" ref="fiRelEmsNo" label="关联备案号">
            <XdoIInput type="text" v-model="headerData.relEmsNo" :disabled="showDisable" :maxlength="12"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="relListNo" label="关联清单编号">
            <XdoIInput type="text" v-model="headerData.relListNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" class="dc-merge-1-3 eye-catching" prop="promiseItems" label="价格说明">
            <div style="display: grid; grid-template-columns: auto 116px;">
              <CheckboxGroup v-model="promiseItemsList" @on-change="promiseItemsChange">
                <XdoCheckbox label="1" :disabled="showDisable" :indeterminate="promiseItems.pi1Im">
                  <span>特殊关系确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="2" :disabled="showDisable" :indeterminate="promiseItems.pi2Im">
                  <span>价格影响确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="3" :disabled="showDisable" :indeterminate="promiseItems.pi3Im">
                  <span>支付特许权使用费确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="4" :disabled="showDisable" :indeterminate="promiseItems.pi4Im">
                  <span>自报自缴</span>
                </XdoCheckbox>
              </CheckboxGroup>
              <XdoIInput type="text" value="注: '✔'为是; '━'为否" disabled></XdoIInput>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareName" label="申报单位名称">
            <XdoIInput type="text" v-model="headerData.declareName" disabled></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="sumQty" label="总数量">
            <xdo-input v-model="headerData.sumQty" decimal int-length="10" precision="5" :disabled="showDisable">
              <span slot="append">{{statistics.sumQty}}</span>
            </xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="sumDecTotal" label="总金额">
            <xdo-input v-model="headerData.sumDecTotal" decimal int-length="13" :precision="precisionsConfig.headDecTotalDigitE" :disabled="showDisable">
              <span slot="append">{{statistics.sumDecTotal}}</span>
            </xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="curr" label="币制">
            <XdoIInput type="text" v-model="statistics.currName" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="containerType" label="集装箱类型">
            <xdo-select :disabled="showDisable" v-model="headerData.containerType" clearable :options="this.cmbDataSource.containerList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="agentCode" label="清单申报单位">
            <Input type="text" v-model="headerData.agentCode" :disabled="declareDisable" @on-enter="agentCodeEnter">
              <span slot="append">{{headerData.agentCreditCode}}</span>
            </Input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="agentName" label="清单申报单位名称">
            <XdoIInput type="text" v-model="headerData.agentName" disabled></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="containerNum" label="集装箱数量">
            <xdo-input :disabled="showDisable" v-model="headerData.containerNum" number int-length="5" ></xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billTo" label="billTo代码">
            <xdo-select :disabled="showDisable" v-model="headerData.billTo" clearable :options="this.cmbSource.billToList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="notify" label="notify代码">
            <xdo-select :disabled="showDisable" v-model="headerData.notify" clearable :options="this.cmbSource.notifyList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note" label="备注" class="dc-merge-1-4">
            <XdoIInput type="text" v-model="headerData.note" :disabled="showDisable" :maxlength="256"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="invoiceDate" label="发票日期">
            <XdoDatePicker type="date" v-model="headerData.invoiceDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
        </div>
      </Card>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
    <XdoModal v-model="showSelectTemplate" title="选择模板"
              :footer-hide="true" :mask-closable="false" :width="900">
      <selectTemplate iemark="E" ref="selTemplate" @selectTemplate:success="selectTemplateSuccess" @selectTemplate:close="closeSelectTemplateModel"></selectTemplate>
    </XdoModal>
    <TemplateConfirm :show.sync="tempConfirmShow" @confirm:success="afterConfirm"></TemplateConfirm>
    <WrapTypeSelectPop :show.sync="wrapType2PopShow" :wrap-type="headerData.wrapType2" @doWrapTypeFill="doChangeWrapType2"></WrapTypeSelectPop>
    <decErpHeadCheckPop :show.sync="headCheckPop.show" :error-data="headCheckPop.errData" @doContinue="doSaveContinue"></decErpHeadCheckPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { decErpHeadEdit } from '@/view/cs-ie-manage-mixins/head/decErpHeadEdit'
  import AreaPostCascader from '@/view/cs-ie-manage-mixins/components/area-post-cascader'

  export default {
    name: 'DecErpEHeadNHeadEdit',
    components: {
      AreaPostCascader
    },
    mixins: [decErpHeadEdit],
    data() {
      return {
        cmbSource: {
          billToList: [],
          notifyList: [],
          shipToData: []
        },
        moduleName: 'LE',
        ajaxUrl: {
          uniqueCheck: csAPI.csImportExport.decErpComm.uniqueCheck,
          getEntityByKey: csAPI.csImportExport.decErpEHeadN.getEntityByKey,
          getSumListContent: csAPI.csImportExport.decErpEHeadN.getSumListContent
        }
      }
    },
    created: function () {
      let me = this
      // 境外收货人
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.cmbDataSource.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
      }).catch(() => {
        me.cmbDataSource.overseasShipperList = []
      })
      me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.decErpEHeadN.insert)
      me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.decErpEHeadN.update)
    },
    watch: {
      'headerData.overseasShipper': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (isNullOrEmpty(val)) {
            me.$set(me.cmbSource, 'shipToData', [])
            me.$set(me.cmbSource, 'billToList', [])
            me.$set(me.cmbSource, 'notifyList', [])
          } else {
            me.$http.post(csAPI.csBaseInfo.clientInfo.shipTo.getShipToCodeName + '/' + val).then(res => {
              me.$set(me.cmbSource, 'shipToData', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'shipToData', [])
            })
            // billTo
            me.$http.post(csAPI.csBaseInfo.clientInfo.billTo.getBillTo + '/' + val).then(res => {
              me.$set(me.cmbSource, 'billToList', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'billToList', [])
            })
            // notify
            me.$http.post(csAPI.csBaseInfo.clientInfo.notify.getNotify + '/' + val).then(res => {
              me.$set(me.cmbSource, 'notifyList', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'notifyList', [])
            })
          }
        }
      }
    },
    methods: {
      /**
       * 获取初始值
       */
      getDefaultData() {
        return {
          sid: '',
          emsListNo: '',
          shipDate: '',
          voyageDate: '',
          trafMode: '',
          forwardCode: '',
          mawb: '',
          hawb: '',
          trafName: '',
          overseasShipper: '',
          overseasShipperName: '',
          invoiceNo: '',
          contrNo: '',
          ieport: '',
          wrapType: '',
          wrapType2: '',
          packNum: '',
          netWt: '',
          grossWt: '',
          tradeNation: '',
          tradeCountry: '',
          destPort: '',
          volume: '',
          destinationCountry: '',
          districtCode: '',
          districtPostCode: '',
          receiveCode: '',
          receiveName: '',
          declareCodeCustoms: '',
          declareCode: '',
          declareName: '',
          declareCreditCode: '',
          entryPort: '',
          transMode: '',
          emsNo: this.$store.getters[`${namespace}/defaultEmsNo`],
          tradeMode: this.$store.getters[`${namespace}/defaultTradeMode`],
          cutMode: '',
          warehouse: '',
          licenseNo: '',
          billType: '1',
          billListType: '0',
          mergeType: '0',
          note: '',
          relEmsNo: '',
          feeMark: '',
          feeRate: null,
          feeCurr: '',
          insurMark: '',
          insurRate: null,
          insurCurr: '',
          otherMark: '',
          otherRate: null,
          otherCurr: '',
          promiseItems: '',
          masterCustoms: '',
          iedate: '',
          decType: '0',
          shipTo: '',
          sumQty: null,
          sumDecTotal: null,
          curr: '',
          containerType: '',
          containerNum: '',
          tradeTerms: '',
          relListNo: '',
          dclcusMark: '1',
          dclcusType: '2',
          entryType: '2',
          // 清单申报单位信息
          agentCode: '',
          agentName: '',
          agentCreditCode: '',
          agentCodeCustoms: '',
          agentNameCustoms: '',
          // 消费使用/生产销售单位
          ownerCode: '',
          ownerCreditCode: '',
          ownerName: '',

          billTo: '',
          notify: '',
          invoiceDate: '',

          /**LG定制 start*/
          inviteDate: null, //计划 进/出口日期
          cweight: null//计费重量
          /**LG定制 end*/
        }
      },
      /**
       * 成交方式改变
       * @param value
       */
      transactionChange(value) {
        let me = this
        me.feeInit(value)
        me.onTransactionChangeBase(value, null)
      },
      /**
       * 重新计算总毛、净重（从表体汇总而来--暂不启用）
       */
      reCaculateWt() {
        let me = this
        me.$http.post(me.ajaxUrl.getEntityByKey + '/' + me.headerData.sid).then(res => {
          // 总净重
          me.$set(me.headerData, 'netWt', res.data.data.netWt)
          // 总毛重
          me.$set(me.headerData, 'grossWt', res.data.data.grossWt)
        }).catch(() => {
          // 总净重
          me.$set(me.headerData, 'netWt', 0)
          // 总毛重
          me.$set(me.headerData, 'grossWt', 0)
        })
        // console.info('重新汇总总毛、净重')
      },
      /**
       * 境内货源地/境内目的地
       * @param areaObj
       */
      onAreaDataChanged(areaObj) {
        this.$set(this.headerData, 'districtCode', areaObj['districtCode'])
        this.$set(this.headerData, 'districtPostCode', areaObj['districtPostCode'])
      }
    },
    computed: {
      areaOptions() {
        return {
          area: {
            field: 'districtCode',
            value: this.headerData.districtCode
          },
          post: {
            field: 'districtPostCode',
            value: this.headerData.districtPostCode
          }
        }
      },
      receiveCodeName() {
        return this.headerData.receiveCode + ' ' + this.headerData.receiveName
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .btnInput .ivu-form-item-error-tip {
    right: 70px;
  }

  .preRequired:before {
    color: blue;
    content: '*';
    font-size: 12px;
    line-height: 1px;
    margin-right: 4px;
    display: inline-block;
    font-family: 'SimSun';
  }

  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }

  /deep/ .ivu-input-group-append {
    width: 100px;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  .shortAppend {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  /deep/ .shortAppend > .ivu-input-group-with-append > .ivu-input-group-append {
    width: 40px !important;
  }

  /deep/ .audit-view label.ivu-form-item-label {
    background-color: greenyellow;
  }

  /deep/ .audit-error label.ivu-form-item-label {
    color: white;
    background-color: red;
  }

  /deep/ .eye-catching label {
    color: orangered;
    font-weight: bold;
  }

  /deep/ .eye-catching .ivu-form-item-content span {
    color: orangered !important;
  }
</style>
