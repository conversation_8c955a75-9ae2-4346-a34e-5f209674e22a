import { addEvent, isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

function showCellRenderer() {
}

showCellRenderer.prototype.init = function(params) {
  let vm = undefined,
    vmParent = undefined,
    viewMarginLeft = '15px',
    divContainer = document.createElement('div'),
    fwComWrapper = params['frameworkComponentWrapper']
  if (fwComWrapper) {
    vmParent = fwComWrapper.parent
  }
  while (vmParent.$parent && typeof vmParent.$parent['cellEditStyle'] !== 'function') {
    vmParent = vmParent.$parent
  }
  if (vmParent.$parent) {
    vm = vmParent.$parent
  }

  let viewA = document.createElement('a')
  viewA.innerHTML = params.data['hawb']
  viewA.setAttribute('type', 'primary')
  viewA.style.marginLeft = viewMarginLeft
  if (vm) {
    if (vm.canManiFestShow) {
      addEvent(viewA, 'click', function () {
        if (vm && typeof vm.showWarehouseReceiptInfo === 'function') {
          vm.showWarehouseReceiptInfo(params.data)
        }
      })
    } else {
      viewA.setAttribute('style', 'color: black')
    }
  }
  divContainer.appendChild(viewA)
  this.eGui = divContainer
}

showCellRenderer.prototype.getGui = function() {
  return this.eGui
}

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      components: {
        showCellRenderer: showCellRenderer
      },
      totalColumns: [
        {
          width: 120,
          title: '内审状态',
          key: 'apprStatusName',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'apprStatus', 'apprStatusName')
          }
        },
        {
          width: 180,
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 200,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 200,
          key: 'entryDeclareDate',
          title: '申报日期'
        },
        {
          width: 120,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 150,
          title: '境外收货人',
          key: 'overseasShipperName'
        },
        {
          width: 120,
          key: 'hawb',
          title: '提运单号',
          cellRenderer: 'showCellRenderer'
        },
        {
          width: 120,
          key: 'trafName',
          title: '运输工具名称'
        },
        {
          width: 120,
          title: '航次号',
          key: 'voyageNo'
        },
        {
          width: 120,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 150,
          title: '毛重',
          key: 'grossWt'
        },
        {
          width: 150,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 150,
          title: '体积',
          key: 'volume'
        },
        {
          width: 150,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 150,
          title: '离境口岸',
          key: 'entryPort',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
          }
        },
        {
          width: 80,
          title: '随附单据',
          key: 'attachName'
        },
        {
          width: 120,
          title: '制单员',
          key: 'userName'
        },
        {
          width: 120,
          title: '内审员',
          key: 'apprUserFull'
        },
        {
          width: 150,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 100,
          key: 'iedate',
          title: '出口日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 120,
          key: 'remark',
          title: '内部备注'
        },
        {
          width: 120,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        /*****LG定制************/
        {
          width: 150,
          key: 'inviteDate',
          title: '申请出口日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          key: 'cweight',
          title: '计费重量'
        },
        /*****LG定制*******end*****/
        {
          width: 136,
          title: '系统加工费',
          key: 'decTotalProcess',
          render: (h, params) => {
            let total = params.row[params.column.key],
              curr = params.row['decTotalProcessCurr'],
              currName = this.pcodeGet('CURR_OUTDATED', curr)
            if (!isNullOrEmpty(total) && !isNullOrEmpty(currName)) {
              return h('span', total + ' ' + currName)
            }
            return h('span', '')
          }
        }, {
          width: 126,
          title: '内包装',
          key: 'nootherPack'
        }, {
          width: 136,
          title: '实收汇',
          key: 'payTotal',
          render: (h, params) => {
            let total = params.row[params.column.key],
              curr = params.row['payTotalCurr'],
              currName = this.pcodeGet('CURR_OUTDATED', curr)
            console.log(total)
            console.log(currName)
            if (!isNullOrEmpty(total) && !isNullOrEmpty(currName)) {
              return h('span', total + ' ' + currName)
            }
            return h('span', '')
          }
        }, {
          width: 136,
          title: '客供',
          key: 'clientTotal',
          render: (h, params) => {
            let total = params.row[params.column.key],
              curr = params.row['clientTotalCurr'],
              currName = this.pcodeGet('CURR_OUTDATED', curr)
            if (!isNullOrEmpty(total) && !isNullOrEmpty(currName)) {
              return h('span', total + ' ' + currName)
            }
            return h('span', '')
          }
        }, {
          width: 60,
          title: '封装',
          key: 'packages',
          render: (h, params) => {
            let packages = params.row[params.column.key],
              packagesName = ''
            if (packages === '1') {
              packagesName = '是'
            } else if (packages === '0') {
              packagesName = '否'
            }
            return h('span', {
              style: {
                paddingLeft: '23px'
              }
            }, packagesName)
          }
        }, {
          width: 128,
          key: 'billTo',
          title: 'billTo代码'
        }, {
          width: 128,
          key: 'notify',
          title: 'notify代码'
        }, {
          width: 110,
          key: 'emsNo',
          title: '备案号'
        }, {
          width: 150,
          key: 'ieport',
          title: '出境关别',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },{
          width: 120,
          key: 'transMode',
          title: '成交方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        }
        , {
          width: 100,
          key: 'invoiceDate',
          title: '发票日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }, {
          width: 100,
          key: 'shipToName',
          title: 'Ship To'
        }]
    }
  },
  /**
   * 方法
   */
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
