<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CustomsTrackSearch ref="headSearch" iemark="E" :showSearch="showSearch"></CustomsTrackSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:batchUpdate>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                    <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:batchSchedule>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>报关达成<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="doSetSchedule('1')">
                    进行中
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="doSetSchedule('2')">
                    延迟
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="doSetSchedule('3')">
                    完成
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" @onAgCellOperation="onAgCellOperation"
                  :height="dynamicHeight" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <customsTrackETabs v-if="!showList" :edit-config="editConfig"
                       @onEditBack="editBack"></customsTrackETabs>
    <DcImport v-model="batchUpdateShow" :bizParam="updateConfig.params"
              :startRow="updateConfig.startRow" :config="updateConfig.config"
              @importSuccess="afterImport.importSuccess()" ></DcImport>
    <financeSetPop :show.sync="financeConfig.show" :show-finance-no="showFinanceNo" ie-mark="E" :source-data="financeConfig.source"
                   @doFinance="goToFinance"></financeSetPop>
    <customsSubmitVerifyPop :show.sync="submitVerifyConfig.show" :err-data="submitVerifyConfig.data"
                            @onContinue="onSubmitContinue"></customsSubmitVerifyPop>
    <TableColumnSetup v-model="trackESetupShow" :resId="trackId" :columns="trackHeadListColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { importExportManage } from '@/view/cs-common'
  import customsTrackETabs from './customs-track-e-tabs'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { customsTrackList } from '@/view/cs-ie-manage-mixins/entry/customsTrackList'
  import { columnsConfig, excelColumnsConfig, columns } from './decECustomsTrackHeadListColumns'

  export default {
    name: 'DecECustomsTrackHeadList',
    components: {
      customsTrackETabs
    },
    mixins: [customsTrackList, columns],
    data() {
      return {
        iEMark: 'E',
        trackId:'',
        trackESetupShow:false,
        trackHeadListColumns:[],
        gridConfig: {
          exportTitle: '出口报关追踪'
        },
        cmbDataSource: {
          statusList: importExportManage.entryTrackStateMap
        },
        toolbarEventMap:{
          'settings': this.handleTableColumnSetup
        },
        ajaxUrl: {
          exportUrl: csAPI.csImportExport.eCustomsTracking.exportUrl,
          batchUpdate: csAPI.csImportExport.eCustomsTracking.batchUpdate,
          selectAllPaged: csAPI.csImportExport.eCustomsTracking.selectAllPaged,
          setCompleteSchedule: csAPI.csImportExport.eCustomsTracking.setCompleteSchedule,
          // 财务确认
          finance: csAPI.csImportExport.eCustomsTracking.finance,
          confirmCheck: csAPI.csImportExport.eCustomsTracking.financeCheck
        }
      }
    },
    // mounted: function () {
    //   let me = this
    //   me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    // },
    created:function (){
      let me = this
      me.trackId = me.$route.path+'/'+me.$options.name
      me.$set(me,'trackHeadListColumns',getColumnsByConfig(me.totalColumns,columnsConfig))
      let columns = me.$bom3.showTableColumns(me.trackId,me.trackHeadListColumns)
      me.handleUpdateColumn(columns)
    },
    methods: {
      handleTableColumnSetup() {
        this.trackESetupShow = true
      },
      /**
       * 设置列
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(columns => {
          return {
            key: columns.key,
            value: columns.title
          }
        })
      },
      /**
       * 下载前操作
       * @param type
       */
      beforeDownload(type) {
        let me = this
        if (type === '0') {
          me.$set(me.gridConfig, 'exportColumns', getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig))
        } else {
          me.$set(me.gridConfig, 'exportColumns', me.modifyExportColumns)
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .dc-select-selection {
    height: 24px;
    overflow: hidden;
  }

  /deep/ .dc-select-multiple .dc-select-input {
    height: 22px;
  }
</style>
