import customsTrack from '../CustomsTrack'
import { editStatus } from '@/view/cs-common'
import attach from '@/view/cs-ie-manage/attached-document/attached-document'

export const customsTrackETabs = {
  components: {
    attach,
    customsTrack
  },
  props: {
    editConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      collapsed: true,
      attachLock: true,
      tabName: 'headTab',
      tabs: {
        headTab: true,
        attachTab: false
      },
      parentConfig: {
        editData: {},
        editStatus: editStatus.SHOW
      }
    }
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function (config) {
        let me = this
        me.parentConfig.editStatus = config.editStatus
        if (config && config.editStatus === editStatus.ADD) {
          me.parentConfig.editData = {sid: ''}
        } else if (config && config.editStatus === editStatus.EDIT) {
          me.parentConfig.editData = config.editData
        } else if (config && config.editStatus === editStatus.SHOW) {
          me.parentConfig.editData = config.editData
        }
      }
    },
    tabName: {
      immediate: true,
      handler: function (value) {
        let me = this
        me.tabs[value] = true
      }
    }
  },
  computed: {
    aeoShow() {
      return this.editConfig.editStatus === editStatus.SHOW
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    },
    /**
     * Layout
     * @param e
     */
    handleRightSliderClick(e) {
      let me = this
      me.$set(me, 'collapsed', !me.collapsed)
      if (me.collapsed) {
        e.target.style.transform = 'rotate(90deg)'
        e.target.style['-webkit-transform'] = 'rotate(90deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 10px'
        }
      } else {
        e.target.style.transform = 'rotate(0deg)'
        e.target.style['-webkit-transform'] = 'rotate(0deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 22px'
        }
      }
    }
  }
}
