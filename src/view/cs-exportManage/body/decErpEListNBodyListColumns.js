import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          title: '顺序号',
          key: 'serialNo'
        },
        {
          width: 80,
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.billOfLadingListStatusMap)
          }
        },
        {
          width: 100,
          key: 'bondMark',
          title: '保完税标识',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.bondMarkList)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'linkedNo',
          title: '提取单号'
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型标识',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.gmarkList)
          }
        },
        {
          width: 120,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 120,
          key: 'gno',
          title: '备案序号'
        },
        {
          width: 180,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 100,
          key: 'ciqNo',
          title: 'CIQ代码'
        },
        {
          width: 120,
          key: 'gmodel',
          tooltip: true,
          title: '申报规格型号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'copGName',
          title: '中文名称'
        },
        {
          width: 120,
          tooltip: true,
          key: 'copGModel',
          title: '料号申报要素'
        },
        {
          width: 120,
          key: 'unit',
          title: '计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'unit1',
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'unit2',
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          title: '原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          title: '目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'qty',
          title: '申报数量'
        },
        {
          width: 120,
          key: 'qty1',
          title: '法一数量'
        },
        {
          width: 120,
          key: 'qty2',
          title: '法二数量'
        },
        {
          width: 120,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 120,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 120,
          key: 'orderNo',
          title: '销售订单号'
        },
        {
          width: 120,
          title: '发票号码',
          key: 'invoiceNo'
        },
        {
          width: 120,
          key: 'netWt',
          title: '净重'
        },
        {
          width: 120,
          key: 'entryGNo',
          title: '报关单商品序号'
        },
        {
          width: 120,
          key: 'billGNo',
          title: '清单归并序号'
        },
        {
          width: 120,
          title: '客户',
          key: 'supplierCode',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'supplierCode', 'supplierName')
          }
        },
        {
          width: 120,
          key: 'exgVersion',
          title: '单耗版本号'
        },
        {
          width: 120,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 120,
          title: '毛重',
          key: 'grossWt'
        },
        {
          width: 120,
          title: '体积',
          key: 'volume'
        },
        {
          width: 120,
          key: 'lineNo',
          tooltip: true,
          title: '提取单序号'
        },
        {
          width: 120,
          tooltip: true,
          title: '成本中心',
          key: 'costCenter'
        },
        {
          width: 100,
          tooltip: true,
          title: '数据来源',
          key: 'dataSource',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.DATA_SOURCE_MAP)
          }
        },
        {
          width: 120,
          title: '工费',
          key: 'laborCost'
        },
        {
          width: 120,
          title: '料费',
          key: 'matCost'
        },
        {
          width: 120,
          key: 'orderLineNo',
          title: '销售订单行号'
        },
        {
          width: 120,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        {
          width: 120,
          key: 'note1',
          tooltip: true,
          title: 'Remark1'
        },
        {
          width: 120,
          key: 'note2',
          tooltip: true,
          title: 'Remark2'
        },
        {
          width: 120,
          key: 'note3',
          tooltip: true,
          title: 'Remark3'
        },
        {
          width: 180,
          key: 'districtCode',
          title: '境内货源地(国内地区)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.area)
          }
        },
        {
          width: 150,
          key: 'districtPostCode',
          title: '境内货源地(行政区域)',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'POST_AREA')
          }
        },
        {
          width: 100,
          key: 'itemNo',
          tooltip: true,
          title: '模拟项号'
        },
        {
          width: 90,
          tooltip: true,
          key: 'inOutNo',
          title: '出库关联编号'
        },
        {
          width: 120,
          title: '客户料号',
          key: 'customerGNo'
        },
        {
          width: 120,
          tooltip: true,
          title: '客户订单号',
          key: 'customerOrderNo'
        },
        {
          width: 120,
          title: '特许权关联单号',
          key: 'confirmRoyaltiesNo'
        },
        {
          width: 120,
          key: 'cutMode',
          title: '征免性质',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levytype)
          }
        },
        {
          width: 150,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 150,
          title: '批次号',
          key: 'batchNo'
        },
        {
          width: 120,
          key: 'orderQty',
          title: '订单数量'
        },
        {
          width: 120,
          key: 'orderPrice',
          title: '订单单价'
        }
      ],
      batchModelExcel: [
        'serialNo',
        'facGNo',
        'gmodel',
        'qty',
        'decPrice',
        'qty1',
        'curr',
        'originCountry',
        'destinationCountry',
        'dutyMode',
        'exgVersion',
        'copGModel',
        'qty2',
        'decTotal',
        'entryGNo',
        'billGNo',
        'netWt',
        'grossWt',
        'volume',
        'orderNo',
        'invoiceNo',
        'supplierCode',
        'exgVersion',
        'districtCode',
        'districtPostCode',
        'costCenter',
        'ciqNo',
        'laborCost',
        'matCost',
        'note',
        'note1',
        'note2',
        'note3',
        'confirmRoyaltiesNo',
        'inOutNo',
        'packNum',
        'batchNo',
        'orderQty',
        'orderPrice'
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
