<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecErpEListNBodySearch ref="headSearch"></DecErpEListNBodySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" style="display: flex; align-items: center; justify-content: space-between;" ref="area_actions">
          <xdo-toolbar :card="false" @click="handleToolbarClick" :action-source="actions">
            <template v-slot:batchUpdate>
              <Dropdown trigger="click">
                <XdoButton type="text" style="font-size: 12px; width: 95px;">
                  <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
                </XdoButton>
                <DropdownMenu slot="list">
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                      <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                    </XdoButton>
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                    </XdoButton>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </xdo-toolbar>
          <span style="font-weight: bold;">单据内部编号 / 发票号：{{headData.emsListNo}} / {{headData.invoiceNo}}</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" :disable="listDisable"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 28px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <BodyEdit ref="bodyEdit" v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" :aeo-show="aeoShow" :can-aeo-audit="canAeoAudit"
              :saved-audit-data="currAuditData" @onBodyAuditChanged="onBodyAuditChanged" :head-data="headData"></BodyEdit>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig4E" @onImportSuccess="afterImport"></ImportPage>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportGRShow" :importConfig="importConfigGR" @onImportSuccess="afterImport"></ImportPage>
    <DcImport v-model="updateImportShow" :startRow="updateConfig.startRow" :config="updateConfig.config" :bizParam="updateConfig.params"
              @importSuccess="afterImport" :hint="hint"></DcImport>
    <AlertReminder ref="bills" :show.sync="showDialog" :options="alertOptions" :errData="errData" @onContinueProduct="continueProduct" :isContinue="isContinue"></AlertReminder>
    <FilingsNoExceedReminder :show.sync="showQtyRemind" :sid="headId" :can-continue="createBillContinue" @onCreateContinue="doBuildBill"></FilingsNoExceedReminder>
    <ErpExtract :show.sync="modelExtractShow" :head-id="headData.sid" :ems-list-no="headData.emsListNo" iemark="E" bondMark="" @import:success="afterImport"></ErpExtract>
    <InformationExtractionExtractPop :show.sync="erpExtractShow" transferType="E" :head-id="headData.sid"
                                     @onReload="handleSearchSubmit" IeEntry="IeEntry"></InformationExtractionExtractPop>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <DcImport :startRow="importExtractConfig.startRow" :config="importExtractConfig.config" :bizParam="importExtractConfig.params"
              v-model="importExtractShow" @importSuccess="afterImport" ></DcImport>
    <DcImport :startRow="importCustomMadConfig.startRow" :config="importCustomMadConfig.config" :bizParam="importCustomMadConfig.params"
              v-model="importCustomMadShow" @importSuccess="afterImport" ></DcImport>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import BodyEdit from './DecErpEListNBodyEdit'
  import { columns } from './decErpEListNBodyListColumns'
  import DecErpEListNBodySearch from './DecErpEListNBodySearch'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { decErpBodyList } from '@/view/cs-ie-manage-mixins/body/decErpBodyList'

  export default {
    name: 'DecErpEListNBodyList',
    components: {
      BodyEdit,
      DecErpEListNBodySearch
    },
    mixins: [decErpBodyList, columns, dynamicImport],
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return [''].includes(value)
      }
    },
    data() {
      let commImportConfig4E = this.getCommImportConfig('ERP-E-LIST', {
          headId: this.headId
        }, this.headId),
        commImportConfigGR = this.getCommImportConfig('ERP-E-LIST', {
          importGR: '1',
          headId: this.headId
        }, this.headId)
      return {
        hint: '',
        iemark: 'E',
        importKey: 'decErpEList',
        gridConfig: {
          exportTitle: '出口预录入单表体'
        },
        importConfig4E: commImportConfig4E,
        importConfigGR: commImportConfigGR,
        toolbarEventMap: {
          'create-packing-info': this.handleCreatePackingInfo
        },
        ajaxUrl: {
          total: csAPI.csImportExport.decErpEListN.total,
          delete: csAPI.csImportExport.decErpEListN.delete,
          createBill: csAPI.csImportExport.eBill.createBill,
          exportUrl: csAPI.csImportExport.decErpEListN.exportUrl,
          createBillsJg: csAPI.csImportExport.eBill.createBillsJg,
          createBillTest: csAPI.csImportExport.eBill.createBillTest,
          batchUpdate: csAPI.csImportExport.decErpEListN.batchUpdate,
          moveUp: csAPI.csImportExport.decErpListMove.exports.moveUp,
          moveDown: csAPI.csImportExport.decErpListMove.exports.moveDown,
          importExtract: csAPI.csImportExport.decErpEListN.importExtract,
          selectAllPaged: csAPI.csImportExport.decErpEListN.selectAllPaged,
          erpBodyImportTemplate: csAPI.importFilePath.erpBodyImportTemplate,
          getMoveFlag: csAPI.csImportExport.decErpListMove.exports.getMoveFlag,
          listPickUpExport: csAPI.csImportExport.decErpEListN.listPickUpExport,
          listCustomMadeExport: csAPI.csImportExport.decErpEListN.listCustomMadeExport,
          importCustomMadeExport: csAPI.csImportExport.decErpEListN.importCustomMadeExport,
          createPackingInfo: csAPI.csImportExport.decErpEListN.createPackingInfo
        }
      }
    },
    mounted: function () {
      let me = this
      me.$set(me, 'tableId', `${me.$route.path}/body`)
      let columns = me.$bom3.showTableColumns(me.tableId, me.totalColumns)
      me.handleUpdateColumn(columns)
    },
    methods: {
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(col => {
          return {
            key: col.key,
            value: col.title
          }
        })
      },
      /**
       * 导出批量修改数据
       */
      downLoadModify() {
        let me = this,
          modelCols = []
        me.batchModelExcel.forEach(field => {
          let theCols = me.totalColumns.filter(col => {
            return col.key === field
          })
          if (Array.isArray(theCols) && theCols.length === 1) {
            modelCols.push({
              key: theCols[0].key,
              value: theCols[0].title
            })
          }
        })
        me.gridConfig.exportColumns = modelCols.filter(col => {
          return !['ciqNo'].includes(col.key)
        })
        me.doExport(me.ajaxUrl.exportUrl + '/1', me.actions.findIndex(it => it.command === 'batchUpdate'))
      },
      afterImport() {
        let me = this
        me.modelImportShow = false
        me.modelImportGRShow = false
        me.updateImportShow = false
        me.importResult = false
        me.getList()
      },
      /**
       * 生成箱单信息
       */
      handleCreatePackingInfo() {
        let me = this
        me.$http.post(me.ajaxUrl.createPackingInfo, {
          headId: me.headId
        }).then(() => {
          me.$Message.success('生成箱单信息成功!')
          me.getList()
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
