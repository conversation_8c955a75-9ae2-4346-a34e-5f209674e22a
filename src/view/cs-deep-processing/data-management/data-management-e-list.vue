<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DataManagementSearch ref="headSearch" transferType="E" :cmb-source="cmbSource"></DataManagementSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <DataManagementTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                        transferType="E" :cmb-source="cmbSource"></DataManagementTabs>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
    <EntryNoFillBackPop :show.sync="entryFillPopShow" :transfer-type="transferType" :entryNos="popEntryNos"
                        @onConfirm="entryNosFillBack"></EntryNoFillBackPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { dataManagementList } from '../js/data-management/dataManagementList'

  export default {
    name: 'dataManagementEList',
    mixins: [dataManagementList],
    data() {
      return {
        // 查询条件行数
        searchLines: 4,
        transferType: 'E',
        gridConfig: {
          exportTitle: '转出数据管理表头'
        },
        ajaxUrl: {
          deleteUrl: csAPI.deepProcessing.dataManagement.transferOut.head.delete,
          exportUrl: csAPI.deepProcessing.dataManagement.transferOut.head.exportUrl,
          selectAllPaged: csAPI.deepProcessing.dataManagement.transferOut.head.selectAllPaged,
          entryNoFillBackUrl: csAPI.deepProcessing.dataManagement.transferOut.head.entryNoFillBackUrl
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
