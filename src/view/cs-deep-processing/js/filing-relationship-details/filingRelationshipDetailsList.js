import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { deepProcessingList } from '../comm/deepProcessingList'
import { columns } from '../../js/filing-relationship-details/filingRelationshipDetailsColumns'
import FilingRelationshipDetailsSearch from '../../components/filing-relationship-details/filing-relationship-details-search'

export const filingRelationshipDetailsList = {
  mixins: [deepProcessingList, columns],
  components: {
    FilingRelationshipDetailsSearch
  },
  data() {
    return {
      searchLines: 4,
      cmbSource: {
        emsNoData: [],
        tradeCodeData: [],
        customerCodeData: []
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this,
      prdOrCliUrl = ''
    if (me.transferType === 'I') {
      // 供应商
      prdOrCliUrl = csAPI.ieParams.PRD
    } else {
      // 客户
      prdOrCliUrl = csAPI.ieParams.CLI
    }
    // 供应商
    me.$http.post(prdOrCliUrl).then(res => {
      res.data.data.forEach(item => {
        if (!isNullOrEmpty(item['CODE'])) {
          me.cmbSource.tradeCodeData.push({
            label: item['LABEL'],
            value: item['CODE']
          })
          me.cmbSource.customerCodeData.push({
            label: item['LABEL'],
            value: item['VALUE']
          })
        }
      })
    }).catch(() => {
      me.$set(me.cmbSource, 'tradeCodeData', [])
      me.$set(me.cmbSource, 'customerCodeData', [])
    })
    // 审核后的账册号
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.cmbSource.emsNoData = res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE,
          copEmsNo: item['COP_EMS_NO']
        }
      })
    }).catch(() => {
      me.cmbSource.emsNoData = []
    })
  },
  mounted: function () {
    let me = this
    if (me.transferType === 'I') {
      me.$set(me, 'realTotalColumns', me.totalInColumns)
    } else if (me.transferType === 'E') {
      me.$set(me, 'realTotalColumns', me.totalOutColumns)
    } else {
      console.error('请设置深加工转入、转出类型transferType')
      return
    }
    me.setShowFields(me.realTotalColumns, true)
  },
  methods: {
    actionLoad() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        icon: 'ios-cloud-download-outline'
      }, {
        ...me.actionsComm,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting'
      })
    }
  }
}
