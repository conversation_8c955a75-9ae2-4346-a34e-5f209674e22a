import { editStatus } from '@/view/cs-common'
import { deepProcessingList } from './deepProcessingList'

export const deepProcessingBodyList = {
  mixins: [deepProcessingList],
  props: {
    transferType: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {
          sid: ''
        },
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      defaultColumns: [],
      realTotalColumns: []
    }
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return Object.assign({
        headId: this.parentConfig.editData.sid
      }, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
    },
    actionLoadMethods() {
      let me = this
      me.loadFunctions('body').then(() => {
        if (typeof me.resetActions === 'function') {
          me.resetActions()
        }
      })
    }
  }
}
