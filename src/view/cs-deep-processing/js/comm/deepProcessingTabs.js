import { editStatus } from '@/view/cs-common'

export const deepProcessingTabs = {
  props: {
    transferType: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    editConfig: {
      type: Object,
      default: () => ({
        editData: {
          sid: ''
        },
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        billTab: false
      }
    }
  },
  watch: {
    tabName(value) {
      this.tabs[value] = true
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  },
  computed: {
    showBody() {
      return this.editConfig.editStatus !== editStatus.ADD
    },
    parentConfig() {
      return {
        editData: this.editConfig.editData,
        editStatus: this.editConfig.editStatus
      }
    }
  }
}
