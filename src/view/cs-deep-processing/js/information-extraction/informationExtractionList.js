import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { formatDate, convertToDate } from '@/libs/datetime'
import { deepProcessingList } from '../comm/deepProcessingList'
import { columns } from '../../js/information-extraction/informationExtractionColumns'
import ExtractConfirmPop from '../../components/information-extraction/extract-confirm-pop'
import { delList, deepProcessing, erpInterfaceData, interimVerification } from '@/view/cs-common'
import InformationExtractionSearch from '../../components/information-extraction/information-extraction-search'
import InformationExtractionSplitPop from '../../components/information-extraction/information-extraction-split-pop'
import InformationExtractionExtractPop from '../../components/information-extraction/information-extraction-extract-pop'

export const informationExtractionList = {
  mixins: [deepProcessingList, columns],
  components: {
    ExtractConfirmPop,
    InformationExtractionSearch,
    InformationExtractionSplitPop,
    InformationExtractionExtractPop
  },
  data() {
    return {
      // 查询条件行数
      searchLines: 4,
      toolbarEventMap: {
        'split': this.handleSplit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'extract': this.handleExtract,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup,
        'confirmForQuery': this.handleConfirmForQuery,
        'confirmForChoose': this.handleConfirmForChoose
      },
      confirmType: '', // '0': '确认勾选'; '1': '确认全部'
      delList: delList,
      totalExtract: '',
      splitPopShow: false,
      erpExtractShow: false,
      carryOverCycle: ['', ''],
      extractConfirmShow: false,
      erpInterfaceData: erpInterfaceData,
      interimVerification: interimVerification
    }
  },
  mounted: function () {
    let me = this
    if (me.transferType === 'I') {
      if (me.confirmed) {
        me.$set(me, 'realTotalColumns', me.totalIColumns)
      } else {
        me.$set(me, 'realTotalColumns', me.totalIColumns.filter(item => {
          return item.key !== 'confirmStatus'
        }))
      }
    } else if (me.transferType === 'E') {
      if (me.confirmed) {
        me.$set(me, 'realTotalColumns', me.totalEColumns)
      } else {
        me.$set(me, 'realTotalColumns', me.totalEColumns.filter(item => {
          return item.key !== 'confirmStatus'
        }))
      }
    } else {
      console.error('请设置深加工转入、转出类型transferType')
      return
    }
    me.setShowFields(me.realTotalColumns, true)
  },
  watch: {
    'gridConfig.selectRows': {
      handler: function (val) {
        let me = this
        if (val.length > 0) {
          let allQty = 0
          let allTotal = 0
          val.forEach(item => {
            allQty = allQty + parseFloat(item.qty)
            allTotal = allTotal + parseFloat(item.decTotal)
          })
          me.totalExtract = `汇总条数${val.length}    总数量${allQty.toFixed(5)}    总金额${allTotal.toFixed(2)}`
        } else if (!isNullOrEmpty(me.ajaxUrl.getTotalContent)) {
          if (me.$refs.headSearch.searchParam) {
            let params = me.$refs.headSearch.searchParam
            me.$http.post(me.ajaxUrl.getTotalContent, params).then(res => {
              me.totalExtract = `总数量${res.data.data.decQty}    总金额${res.data.data.decTotal}`
            }).catch(() => {
            })
          }
        }
      }
    }
  },
  methods: {
    getGrdColumns(columns) {
      return [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, ...columns]
    },
    actionLoad() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        command: 'extract',
        label: '提取ERP数据',
        icon: 'ios-checkmark'
      }, {
        ...me.actionsComm,
        label: '确认勾选',
        icon: 'ios-create-outline',
        command: 'confirmForChoose'
      }, {
        ...me.actionsComm,
        label: '确认全部',
        icon: 'md-done-all',
        command: 'confirmForQuery'
      }, {
        ...me.actionsComm,
        label: '删除',
        command: 'delete',
        icon: 'ios-trash-outline'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        icon: 'ios-cloud-download-outline'
      }, {
        ...me.actionsComm,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting'
      })
    },
    handleExtract() {
      this.erpExtractShow = true
    },
    handleSplit() {
      let me = this
      if (me.checkRowSelected('拆分', true)) {
        me.splitPopShow = true
      }
    },
    doRowSplit(splitQty) {
      let me = this
      if (splitQty.length === 1) {
        me.$Message.warning('不需要拆分')
        return
      }
      me.$Modal.confirm({
        title: '提醒',
        okText: '确认',
        loading: true,
        cancelText: '取消',
        content: '确认要拆分所选项吗',
        onOk: () => {
          me.setToolbarProperty('split', 'loading', true)
          let theRow = me.gridConfig.selectRows[0]
          const param = {
            sid: theRow.sid,
            qtyList: splitQty,
          }
          me.$http.post(me.ajaxUrl.splitUrl, param).then(() => {
            me.handleSearchSubmit()
          }).catch(() => {
          }).finally(() => {
            me.setToolbarProperty('split', 'loading', false)
            me.$set(me, 'splitPopShow', false)
          })
          setTimeout(() => {
            me.$Modal.remove()
          }, 150)
        }
      })
    },
    /**
     * 获取结转周期并弹出对话框
     */
    fillCarryOverCycle(confirmType) {
      let me = this
      me.$set(me, 'confirmType', confirmType)
      let dateRangeBegin = ''
      let dateRangeEnd = ''
      if ('0' === confirmType) {
        me.gridConfig.selectRows.forEach(row => {
          if (!isNullOrEmpty(row.deliveryDate)) {
            let theDate = convertToDate(row.deliveryDate)
            if (theDate instanceof Date) {
              // 开始日期
              if (dateRangeBegin === '') {
                dateRangeBegin = formatDate(theDate, 'yyyy-MM-dd')
              } else if (theDate < dateRangeBegin) {
                dateRangeBegin = formatDate(theDate, 'yyyy-MM-dd')
              }
              // 截止日期
              if (dateRangeEnd === '') {
                dateRangeEnd = formatDate(theDate, 'yyyy-MM-dd')
              } else if (theDate > dateRangeEnd) {
                dateRangeEnd = formatDate(theDate, 'yyyy-MM-dd')
              }
            }
          }
        })
        me.$set(me, 'carryOverCycle', [dateRangeBegin, dateRangeEnd])
        me.$set(me, 'extractConfirmShow', true)
      } else {
        let params = me.getSearchParams()
        me.$http.post(me.ajaxUrl.getMaxMinDate, params).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length === 2) {
            let theDate
            // 开始日期
            if (!isNullOrEmpty(res.data.data[0])) {
              theDate = convertToDate(res.data.data[0])
              if (theDate instanceof Date) {
                dateRangeBegin = formatDate(theDate, 'yyyy-MM-dd')
              }
            }
            // 截止日期
            if (!isNullOrEmpty(res.data.data[1])) {
              theDate = convertToDate(res.data.data[1])
              if (theDate instanceof Date) {
                dateRangeEnd = formatDate(theDate, 'yyyy-MM-dd')
              }
            }
            me.$set(me, 'carryOverCycle', [dateRangeBegin, dateRangeEnd])
            me.$set(me, 'extractConfirmShow', true)
          }
        }).catch(() => {
        })
      }
    },
    handleConfirmForChoose() {
      let me = this
      if (me.checkRowSelected('对账确认')) {
        me.fillCarryOverCycle('0')
      }
    },
    handleConfirmForQuery() {
      let me = this
      me.fillCarryOverCycle('1')
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnIndexOrKey
     */
    doDelete(delUrl, btnIndexOrKey) {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.gridConfig.selectRows, '删除')) {
          let content = '确认删除所选项吗'
          if (me.gridConfig.selectRows.filter(it => it.splitSerialNo).length > 0) {
            content = '存在多条数据将一同删除'
          }
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            content: content,
            cancelText: '取消',
            onOk: () => {
              me.setButtonLoading(btnIndexOrKey, true)
              let params = me.getSelectedParams()
              me.$http.delete(`${delUrl}/${params}`).then(() => {
                me.$Message.success('删除成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading(btnIndexOrKey, false)
              })
            }
          })
        }
      }
    },
    /**
     * 执行对账确认
     * @param checkNo
     */
    onConfirm(checkNo) {
      let me = this
      me.$http.get(csAPI.deep.confirmCheckNo + `/${checkNo}`).then(res => {
        if (res.data.data) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '继续',
            cancelText: '取消',
            content: '对账编号已存在是否继续',
            onOk: () => {
              me.confirmCheckNo(checkNo)
            }
          })
        } else {
          me.confirmCheckNo(checkNo)
        }
      }, () => {
      })
    },
    confirmCheckNo(checkNo) {
      let me = this
      if ('0' === me.confirmType) {
        me.setButtonLoading('confirmForChoose', true)
        let params = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.confirmForChoose + '/' + checkNo, params).then(() => {
          me.$Message.success('确认成功!')
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setButtonLoading('confirmForChoose', false)
        })
      } else if ('1' === me.confirmType) {
        me.setButtonLoading('confirmForQuery', true)
        let params = me.getSearchParams()
        me.$http.post(me.ajaxUrl.confirmForQuery + '/' + checkNo, params).then(() => {
          me.$Message.success('确认成功!')
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setButtonLoading('confirmForQuery', false)
        })
      }
    },
    handleImport() {
      this.modelImportShow = true
    },
    onAfterImport() {
      this.modelImportShow = false
    }
  },
  computed: {
    ieTypeData() {
      if (this.transferType === 'I') {
        return deepProcessing.IE_TYPE_IN_MAP
      } else {
        return deepProcessing.IE_TYPE_OUT_MAP
      }
    },
    originalDecQty() {
      if (this.gridConfig.selectRows.length > 0) {
        return this.gridConfig.selectRows[0].qty
      }
      return null
    }
  }
}
