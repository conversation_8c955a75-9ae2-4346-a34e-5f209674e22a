import { isNullOrEmpty } from '@/libs/util'
import { formatDate } from '@/libs/datetime'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data () {
    return {
      // 表头
      headCommCols: [
        {
          title: '状态',
          width: 120,
          key: 'status',
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.deepProcessing.DATA_STATUS_MAP)
          }
        },
        {
          title: '转入/出单据内部编号',
          key: 'emsListNo',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '结转周期',
          key: 'beginDate',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            let dateRange = ''
            let beginDateVal = params.row['beginDate']
            if (!isNullOrEmpty(beginDateVal)) {
              dateRange = formatDate(beginDateVal, 'yyyy-MM-dd')
            }
            let endDateVal = params.row['endDate']
            if (!isNullOrEmpty(endDateVal)) {
              if (!isNullOrEmpty(dateRange)) {
                dateRange += ' ~ ' + formatDate(endDateVal, 'yyyy-MM-dd')
              } else {
                dateRange = formatDate(endDateVal, 'yyyy-MM-dd')
              }
            }
            return h('span', dateRange)
          }
        },
        {
          title: '转出方海关十位代码',
          key: 'tradeCodeOut',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '转入方海关十位代码',
          key: 'tradeCodeIn',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '转出方备案号',
          key: 'emsNoOut',
          width: 220,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '转入方备案号',
          key: 'emsNoIn',
          width: 220,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申请表编号',
          key: 'applyNo',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '收发货单编号',
          key: 'rsBillNo',
          minWidth: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '发票号',
          key: 'invoiceNo',
          minWidth: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '转入报关单号',
          key: 'entryNoIn',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '转出报关单号',
          key: 'entryNoOut',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '备注',
          key: 'remark',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        // {
        //   title: '数据来源',
        //   key: 'dataSource',
        //   width: 120,
        //   ellipsis: true,
        //   tooltip: true,
        //   align: 'center',
        //   render: (h, params) => {
        //     return this.cmbShowRender(h, params, this.deepProcessing.DATA_SOURCE_MAP)
        //   }
        // },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        // {
        //   title: '深加工转入备案表头ID',
        //   key: 'headId',
        //   width: 120,
        //   align: 'center'
        // },
        {
          title: '录入人',
          key: 'userName',
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '录入日期',
          key: 'insertTime',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        // {
        //   title: '变更人',
        //   key: 'updateUser',
        //   width: 120,
        //   tooltip: true,
        //   ellipsis: true,
        //   align: 'center'
        // },
        // {
        //   title: '变更日期',
        //   key: 'updateTime',
        //   width: 100,
        //   align: 'center',
        //   render: (h, params) => {
        //     return this.dateTimeShowRender(h, params)
        //   }
        // },
        // {
        //   title: '提单模板表头',
        //   key: 'templeteId',
        //   width: 120,
        //   tooltip: true,
        //   ellipsis: true,
        //   align: 'center'
        // },
        {
          title: '总净重',
          key: 'netWt',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '总毛重',
          key: 'grossWt',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '件数',
          key: 'packNum',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '数量汇总',
          key: 'sumQty',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '金额汇总',
          key: 'sumDecTotal',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        }
      ],
      headInCols: [
        {
          title: '转出方企业名称',
          key: 'tradeNameOut',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'//,
          // render: (h, params) => {
          //   return this.keyValueRender(h, params, 'customerCodeOut', 'tradeNameOut')
          // }
        }
      ],
      headInDefaultCols: [{key: 'status'}, {key: 'emsListNo'}, {key: 'beginDate'}, {key: 'tradeCodeOut'}, {key: 'tradeNameOut'},
        {key: 'emsNoOut'}, {key: 'emsNoIn'}, {key: 'applyNo'}, {key: 'rsBillNo'}, {key: 'invoiceNo'}, {key: 'entryNoIn'}, {key: 'entryNoOut'},
        {key: 'tradeMode'}, {key: 'remark'}],
      headOutCols: [
        {
          title: '转入方企业名称',
          key: 'tradeNameIn',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'//,
          // render: (h, params) => {
          //   return this.keyValueRender(h, params, 'customerCodeIn', 'tradeNameIn')
          // }
        }
      ],
      headOutDefaultCols: [{key: 'status'}, {key: 'emsListNo'}, {key: 'beginDate'}, {key: 'tradeCodeIn'}, {key: 'tradeNameIn'},
        {key: 'emsNoIn'}, {key: 'emsNoOut'}, {key: 'applyNo'}, {key: 'rsBillNo'}, {key: 'invoiceNo'}, {key: 'entryNoOut'}, {key: 'entryNoIn'},
        {key: 'tradeMode'}, {key: 'remark'}],
      // 表体
      bodyCommCols: [
        {
          title: '匹配结果',
          key: 'matchResult',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.deepProcessing.MATCH_RESULT_MAP)
          }
        },
        {
          title: '转入备案料号',
          key: 'copGNoIn',
          width: 220,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '转出备案料号',
          key: 'copGNoOut',
          width: 220,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '转入备案序号',
          key: 'gnoIn',
          width: 90,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '转出备案序号',
          key: 'gnoOut',
          width: 90,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '原始清单序号',
          width: 90,
          align: 'center',
          tooltip: true,
          ellipsis: true,
          key: 'entryGNo'
        },
        {
          title: '数量',
          key: 'decQty',
          width: 100,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '单价',
          key: 'decPrice',
          width: 100,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '总价',
          key: 'decTotal',
          width: 100,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '币制',
          minWidth: 100,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '申报计量单位',
          key: 'unit',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '供应商名称/客户名称',
          key: 'supplierName',
          width: 180,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'supplierCode', 'supplierName')
          }
        },
        {
          title: 'PO料号',
          key: 'poNo',
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '收/发货日期',
          key: 'arrivalDate',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: 'ERP交易单位',
          key: 'unitErp',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '成本中心',
          key: 'costCenter',
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '对账编号',
          key: 'checkNo',
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '数据来源',
          key: 'dataSource',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.deepProcessing.DATA_SOURCE_MAP)
          }
        },
        {
          title: '录入日期',
          key: 'insertTime',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '录入人',
          key: 'userName',
          width: 120,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        }
      ],
      bodyInCols: [
        {
          title: '转入企业料号',
          key: 'facGNoIn',
          width: 220,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        }
      ],
      bodyInDefaultCols: [{key: 'matchResult'}, {key: 'facGNoIn'}, {key: 'copGNoIn'}, {key: 'gnoIn'}, {key: 'gnoOut'}, {key: 'copGNoOut'},
        {key: 'entryGNo'}, {key: 'decQty'}, {key: 'decPrice'}, {key: 'decTotal'}, {key: 'curr'}, {key: 'unit'}, {key: 'supplierName'},
        {key: 'poNo'}, {key: 'arrivalDate'}, {key: 'unitErp'}, {key: 'costCenter'}, {key: 'checkNo'}, {key: 'dataSource'}, {key: 'insertTime'}, {key: 'userName'}],
      bodyOutCols: [
        {
          title: '转出企业料号',
          key: 'facGNoOut',
          width: 220,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '单耗版本号',
          minWidth: 120,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          key: 'bomVersion'
        }
      ],
      bodyOutDefaultCols: [{key: 'matchResult'}, {key: 'facGNoOut'}, {key: 'copGNoOut'}, {key: 'gnoOut'}, {key: 'gnoIn'}, {key: 'copGNoIn'},
        {key: 'entryGNo'}, {key: 'decQty'}, {key: 'decPrice'}, {key: 'decTotal'}, {key: 'curr'}, {key: 'unit'}, {key: 'supplierName'},
        {key: 'poNo'}, {key: 'arrivalDate'}, {key: 'unitErp'}, {key: 'costCenter'}, {key: 'bomVersion'}, {key: 'checkNo'}, {key: 'dataSource'}, {key: 'insertTime'}, {key: 'userName'}]
    }
  },
  methods: {
    keyValueRender (h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
