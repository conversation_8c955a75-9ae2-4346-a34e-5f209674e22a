<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <!--转入-->
      <XdoFormItem v-if="transferType === 'I'" prop="facGNoIn" label="转入企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNoIn" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="copGNoIn" label="转入备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoIn" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoOut" label="转出备案序号">
        <xdo-input v-model="searchParam.gnoOut" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoIn" label="转入备案序号">
        <xdo-input v-model="searchParam.gnoIn" number int-length="10"></xdo-input>
      </XdoFormItem>
      <!--转出-->
      <XdoFormItem v-if="transferType === 'E'" prop="facGNoOut" label="转出企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNoOut" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="copGNoOut" label="转出备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoOut" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoIn" label="转入备案序号">
        <xdo-input v-model="searchParam.gnoIn" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoOut" label="转出备案序号">
        <xdo-input v-model="searchParam.gnoOut" number int-length="10"></xdo-input>
      </XdoFormItem>
      <!--通用-->
      <XdoFormItem prop="entryGNo" label="报关序号">
        <XdoIInput type="text" v-model="searchParam.entryGNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessingSearch } from '../../js/comm/deepProcessingSearch'

  export default {
    name: 'dataManagementBillSearch',
    mixins: [deepProcessingSearch],
    data () {
      return {
        searchParam: {
          // 转入
          facGNoIn: '',
          copGNoIn: '',
          // 转出
          facGNoOut: '',
          copGNoOut: '',
          // 通用
          gnoOut: null,
          gnoIn: null,
          entryGNo: ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
