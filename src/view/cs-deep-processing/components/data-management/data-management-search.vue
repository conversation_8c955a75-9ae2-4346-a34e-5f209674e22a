<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="status" label="状态">
        <xdo-select v-model="searchParam.status" :options="this.deepProcessing.DATA_STATUS_MAP" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" :label="emsListNoLabel">
        <XdoIInput type="text" v-model="searchParam.emsListNo" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="结转周期" @onDateRangeChanged="handleCarryOverCycleChange"></dc-dateRange>
      <XdoFormItem v-if="transferType === 'I'" prop="tradeCodeOut" label="转出方代码名称">
        <xdo-select v-model="searchParam.tradeCodeOut"
                    :options="this.cmbSource.tradeCodeData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="tradeCodeIn" label="转入方代码名称">
        <xdo-select v-model="searchParam.tradeCodeIn"
                    :options="this.cmbSource.tradeCodeData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="emsNoOut" label="转出方备案号">
        <XdoIInput type="text" v-model="searchParam.emsNoOut" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="emsNoIn" label="转入方备案号">
        <XdoIInput type="text" v-model="searchParam.emsNoIn" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="emsNoIn" label="转入方备案号">
        <xdo-select v-model="searchParam.emsNoIn" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="emsNoOut" label="转出方备案号">
        <xdo-select v-model="searchParam.emsNoOut" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="applyNo" label="申请表编号">
        <XdoIInput type="text" v-model="searchParam.applyNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="rsBillNo" label="收发货单编号">
        <XdoIInput type="text" v-model="searchParam.rsBillNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <xdo-select v-model="searchParam.invoiceNo" :options="this.cmbSource.checkNoData" mixer></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="entryNoIn" label="转入报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoIn" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="entryNoOut" label="转出报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoOut" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="entryNoOut" label="转出报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoOut" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="entryNoIn" label="转入报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNoIn" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :options="this.deepProcessing.TRADE_MODE_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessing } from '@/view/cs-common'
  import { deepProcessingSearch } from '../../js/comm/deepProcessingSearch'

  export default {
    name: 'dataManagementSearch',
    mixins: [deepProcessingSearch],
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          emsNoData: [],
          checkNoData: [],
          tradeCodeData: []
        })
      }
    },
    data() {
      return {
        searchParam: {
          status: '',
          emsListNo: '',
          beginDateFrom: '',
          endDateTo: '',
          tradeCodeOut: '',
          tradeCodeIn: '',
          emsNoOut: '',
          emsNoIn: '',
          applyNo: '',
          rsBillNo: '',
          invoiceNo: '',
          entryNoIn: '',
          entryNoOut: '',
          tradeMode: ''
        },
        deepProcessing: deepProcessing
      }
    },
    methods: {
      handleCarryOverCycleChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "beginDateFrom", values[0])
          this.$set(this.searchParam, "endDateTo", values[1])
        } else {
          this.$set(this.searchParam, "beginDateFrom", '')
          this.$set(this.searchParam, "endDateTo", '')
        }
      }
    },
    computed: {
      emsListNoLabel() {
        let me = this
        if (me.transferType === 'I') {
          return '转入单据内部编号'
        } else if (me.transferType === 'E') {
          return '转出单据内部编号'
        } else {
          return '转入(出)单据内部编号'
        }
      }
    }
  }
</script>

<style scoped>
</style>
