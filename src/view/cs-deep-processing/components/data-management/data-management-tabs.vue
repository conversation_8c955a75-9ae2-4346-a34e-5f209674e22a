<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <Head ref="head" @onEditBack="editBack" :transfer-type="transferType"
              :edit-config="editConfig" :cmb-source="cmbSource"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体">
        <Body ref="body" v-if="tabs.bodyTab" :transfer-type="transferType"
              :parent-config="parentConfig" :cmb-source="cmbSource"></Body>
      </TabPane>
      <TabPane name="billTab" v-if="showBody" label="清单">
        <Bill ref="bill" v-if="tabs.billTab" :transfer-type="transferType"
              :parent-config="parentConfig"></Bill>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './data-management-edit'
  import Body from './data-management-body-list'
  import Bill from './data-management-bill-list'
  import { deepProcessingTabs } from '../../js/comm/deepProcessingTabs'

  export default {
    name: 'dataManagementTabs',
    components: {
      Head,
      Body,
      Bill
    },
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          emsNoData: [],
          tradeCodeObj: {},
          tradeCodeData: [],
          emsNoPartner: [],
          emsNoPartnerData: {},
          checkNoData: [],
          relationshipData: []
        })
      }
    },
    mixins: [deepProcessingTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
