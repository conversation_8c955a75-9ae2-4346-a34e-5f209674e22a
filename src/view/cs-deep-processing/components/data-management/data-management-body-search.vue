<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <!--转入-->
      <XdoFormItem v-if="transferType === 'I'" prop="facGNoIn" label="转入企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNoIn" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="copGNoIn" label="转入备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoIn" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoIn" label="转入备案序号">
        <xdo-input v-model="searchParam.gnoIn" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoOut" label="转出备案序号">
        <xdo-input v-model="searchParam.gnoOut" number int-length="10"></xdo-input>
      </XdoFormItem>
      <!--转出-->
      <XdoFormItem v-if="transferType === 'E'" prop="facGNoOut" label="转出企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNoOut" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="copGNoOut" label="转出备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoOut" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoOut" label="转出备案序号">
        <xdo-input v-model="searchParam.gnoOut" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoIn" label="转入备案序号">
        <xdo-input v-model="searchParam.gnoIn" number int-length="10"></xdo-input>
      </XdoFormItem>

      <XdoFormItem v-if="transferType === 'E'" prop="bomVersion" label="单耗版本号">
        <XdoIInput type="text" v-model="searchParam.bomVersion" clearable></XdoIInput>
      </XdoFormItem>
      <!--通用-->
      <XdoFormItem prop="entryGNo" label="报关单序号">
        <xdo-input v-model="searchParam.entryGNo" decimal int-length="2" precision="0"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="matchResult" label="匹配结果">
        <xdo-select v-model="matchResultStr" :options="this.localCmbSource.matchResultData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="checkNo" label="对账编号">
        <xdo-select v-model="searchParam.checkNo" :options="this.cmbSource.checkNoData" mixer></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessing } from '@/view/cs-common'
  import { deepProcessingSearch } from '../../js/comm/deepProcessingSearch'

  export default {
    name: 'dataManagementBodySearch',
    mixins: [deepProcessingSearch],
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          checkNoData: []
        })
      }
    },
    data() {
      return {
        searchParam: {
          // 转入
          facGNoIn: '',
          copGNoIn: '',
          gnoOut: '',
          gnoIn: '',
          // 转出
          facGNoOut: '',
          copGNoOut: '',
          // 通用
          entryGNo: '',
          matchResult: null,
          checkNo: '',
          bomVersion: ''
        },
        matchResultStr: '',
        deepProcessing: deepProcessing,
        ajaxUrl: {
          // 获取对账单号列表
          getCheckNoList: ''
        },
        localCmbSource: {
          matchResultData: []
        }
      }
    },
    created: function () {
      let me = this
      me.$set(me.localCmbSource, 'matchResultData', deepProcessing.MATCH_RESULT_MAP.map(item => {
        return {
          value: item.value.toString(),
          label: item.label
        }
      }))
    },
    watch: {
      matchResultStr: {
        immediate: true,
        handler: function (result) {
          let me = this
          if (result === '0') {
            me.$set(me.searchParam, 'matchResult', 0)
          } else if (result === '1') {
            me.$set(me.searchParam, 'matchResult', 1)
          } else {
            me.$set(me.searchParam, 'matchResult', null)
          }
        }
      }
    }
  }
</script>

<style scoped>
</style>
