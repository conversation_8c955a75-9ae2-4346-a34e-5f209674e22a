<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DataManagementBillSearch ref="headSearch" :transfer-type="transferType"></DataManagementBillSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" v-if="tableShow" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border :row-class-name="rowClassName"
                  @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page" style="height: 25px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalExtract}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
    <DataManagementBillSplitPop :show.sync="splitPopShow" :original-dec-qty="originalDecQty"
                                @confirm:success="doRowSplit"></DataManagementBillSplitPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import DataManagementBillSearch from './data-management-bill-search'
  import DataManagementBillSplitPop from './data-management-bill-split-pop'
  import { columns } from '../../js/data-management/dataManagementBillColumns'
  import { deepProcessingBodyList } from '../../js/comm/deepProcessingBodyList'
  import { getHttpHeaderFileName, blobSaveFile, isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'dataManagementBodyList',
    components: {
      DataManagementBillSearch,
      DataManagementBillSplitPop
    },
    mixins: [deepProcessingBodyList, columns],
    data() {
      return {
        toolbarEventMap: {
          'export': this.handleDownload,
          'printInvBox': this.handlePrintInvBox,
          'generateCustomsInfo': this.handleGenerateCustomsInfo,
          'rowSplit': this.handleRowSplit,
          'moveUp': this.handleRowMoveUp,
          'moveDown': this.handleRowMoveDown,
          'clear': this.handleClear,
          'setting': this.handleTableColumnSetup
        },
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: '',
          // 打印发票清单
          printInvBox: '',
          // 生成报关信息
          generateCustomsInfo: '',
          // 获取汇总数据
          getTotalContent: '',
          // 更新
          update: '',
          // 行拆分
          rowSplit: '',
          // 序号变更
          serialNoChange: '',
          // 数据清空
          dataClear: ''
        },
        // 查询条件行数
        searchLines: 2,
        // 有子tab组件
        hasChildTabs: true,
        totalExtract: '',
        splitPopShow: false,
        currSid: '',
        currCommand: ''
      }
    },
    created: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me.gridConfig, 'exportTitle', '转入数据清单')
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.dataManagement.transferIn.bill.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.dataManagement.transferIn.bill.selectAllPaged)
        // 导出发票清单
        me.$set(me.ajaxUrl, 'printInvBox', csAPI.deepProcessing.dataManagement.transferIn.bill.printInvBox)
        // 生成报关信息
        me.$set(me.ajaxUrl, 'generateCustomsInfo', csAPI.deepProcessing.dataManagement.transferIn.bill.generateCustomsInfo)
        // 获取汇总数据
        me.$set(me.ajaxUrl, 'getTotalContent', csAPI.deepProcessing.dataManagement.transferIn.bill.getTotalContent)
        // 更新
        me.$set(me.ajaxUrl, 'update', csAPI.deepProcessing.dataManagement.transferIn.bill.update)
        // 行拆分
        me.$set(me.ajaxUrl, 'rowSplit', csAPI.deepProcessing.dataManagement.transferIn.bill.rowSplit)
        // 序号变更
        me.$set(me.ajaxUrl, 'serialNoChange', csAPI.deepProcessing.dataManagement.transferIn.bill.serialNoChange)
        // 数据清空
        me.$set(me.ajaxUrl, 'dataClear', csAPI.deepProcessing.dataManagement.transferIn.bill.dataClear)
      } else {
        me.$set(me.gridConfig, 'exportTitle', '转出数据清单')
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.dataManagement.transferOut.bill.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.dataManagement.transferOut.bill.selectAllPaged)
        // 导出发票清单
        me.$set(me.ajaxUrl, 'printInvBox', csAPI.deepProcessing.dataManagement.transferOut.bill.printInvBox)
        // 生成报关信息
        me.$set(me.ajaxUrl, 'generateCustomsInfo', csAPI.deepProcessing.dataManagement.transferOut.bill.generateCustomsInfo)
        // 获取汇总数据
        me.$set(me.ajaxUrl, 'getTotalContent', csAPI.deepProcessing.dataManagement.transferOut.bill.getTotalContent)
        // 更新
        me.$set(me.ajaxUrl, 'update', csAPI.deepProcessing.dataManagement.transferOut.bill.update)
        // 行拆分
        me.$set(me.ajaxUrl, 'rowSplit', csAPI.deepProcessing.dataManagement.transferOut.bill.rowSplit)
        // 序号变更
        me.$set(me.ajaxUrl, 'serialNoChange', csAPI.deepProcessing.dataManagement.transferOut.bill.serialNoChange)
        // 数据清空
        me.$set(me.ajaxUrl, 'dataClear', csAPI.deepProcessing.dataManagement.transferOut.bill.dataClear)
      }
    },
    mounted: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me, 'realTotalColumns', [...me.billCommCols, ...me.billInCols])
        me.$set(me, 'defaultColumns', me.billInDefaultCols)
      } else if (me.transferType === 'E') {
        me.$set(me, 'realTotalColumns', [...me.billCommCols, ...me.billOutCols])
        me.$set(me, 'defaultColumns', me.billOutDefaultCols)
      } else {
        console.error('请设置深加工转入、转出类型transferType')
        return
      }
      me.realTotalColumns.forEach(item => {
        if (item.key === 'arrivalDate') {
          item.title = me.arrivalDateLabel
        } else if (item.key === 'supplierName') {
          item.title = me.supplierNameLabel
        }
      })
      me.setShowFields(me.realTotalColumns, me.parentConfig.editStatus !== editStatus.EDIT)
    },
    watch: {
      'gridConfig.selectRows': {
        handler: function (selRows) {
          let me = this
          if (selRows.length > 0) {
            let allQty = 0
            let allTotal = 0
            selRows.forEach(item => {
              allQty = allQty + parseFloat(item.decQty)
              allTotal = allTotal + parseFloat(item.decTotal)
            })
            me.totalExtract = `汇总条数${selRows.length}    总数量${allQty.toFixed(5)}    总金额${allTotal.toFixed(2)}`
          } else if (!isNullOrEmpty(me.ajaxUrl.getTotalContent)) {
            if (me.$refs.headSearch.searchParam) {
              let params = me.getSearchParams()
              me.$http.post(me.ajaxUrl.getTotalContent, params).then(res => {
                me.totalExtract = `总数量${res.data.data[0]}    总金额${res.data.data[1]}`
              }, () => {
              })
            }
          }
        }
      }
    },
    methods: {
      actionLoadMethods() {
        let me = this
        me.loadFunctions('bill').then(() => {
          if (typeof me.actionExtend === "function") {
            me.actionExtend()
          }
          if (typeof me.resetActions === "function") {
            me.resetActions()
          }
        })
      },
      actionLoad() {
        let me = this
        me.actions.push({
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          icon: 'ios-cloud-download-outline'
        }, {
          ...me.actionsComm,
          label: '打印发票箱单',
          command: 'printInvBox',
          icon: 'ios-print-outline'
        }, {
          ...me.actionsComm,
          label: '生成报关信息',
          icon: 'ios-basket-outline',
          command: 'generateCustomsInfo'
        }, {
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting'
        })
      },
      actionExtend() {
        let me = this
        // 清单拆分
        let tmpActions = me.actions.filter(item => {
          return item.command === 'rowSplit'
        })
        if (tmpActions.length === 0) {
          me.actions.splice(me.actions.length - 1, 0, {
            ...me.actionsComm,
            label: '清单拆分',
            command: 'rowSplit',
            key: 'xdo-btn-edit',
            icon: 'ios-list-box-outline'
          })
        }
        // 上移
        tmpActions = me.actions.filter(item => {
          return item.command === 'moveUp'
        })
        if (tmpActions.length === 0) {
          me.actions.splice(me.actions.length - 1, 0, {
            ...me.actionsComm,
            label: '上移',
            command: 'moveUp',
            key: 'xdo-btn-edit',
            icon: 'ios-arrow-dropup'
          })
        }
        // 下移
        tmpActions = me.actions.filter(item => {
          return item.command === 'moveDown'
        })
        if (tmpActions.length === 0) {
          me.actions.splice(me.actions.length - 1, 0, {
            ...me.actionsComm,
            label: '下移',
            command: 'moveDown',
            key: 'xdo-btn-edit',
            icon: 'ios-arrow-dropdown'
          })
        }
        // 清空
        tmpActions = me.actions.filter(item => {
          return item.command === 'clear'
        })
        if (tmpActions.length === 0) {
          me.actions.splice(me.actions.length - 1, 0, {
            ...me.actionsComm,
            label: '清空',
            command: 'clear',
            key: 'xdo-btn-edit',
            icon: 'ios-arrow-dropdown'
          })
        }
      },
      /**
       * 重置按钮
       */
      resetActions() {
        let me = this
        if (me.parentConfig.editStatus === editStatus.SHOW) {
          me.actions = me.actions.filter(item => {
            return !['generateCustomsInfo', 'rowSplit', 'moveUp', 'moveDown', 'clear'].includes(item.command)
          })
        }
      },
      rowClassName(row) {
        if (row['splitStatus'] === '1') {
          return 'table-row-red'
        }
        return ''
      },
      /**
       * 下载并打印
       */
      downloadStreamFile(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: 'application/vnd.ms-excel'})//  pdf'})
        blobSaveFile(blob, filename)
      },
      /**
       * 打印发票清单
       */
      handlePrintInvBox() {
        let me = this
        me.setToolbarLoading('printInvBox', true)
        me.$http.get(me.ajaxUrl.printInvBox + '/' + me.parentConfig.editData.sid, {
          responseType: 'blob'
        }).then(res => {
          me.downloadStreamFile(res.data, res.headers)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('printInvBox')
        })
      },
      /**
       * 生成报关信息
       */
      handleGenerateCustomsInfo() {
        let me = this
        me.setToolbarLoading('generateCustomsInfo', true)
        me.$http.get(me.ajaxUrl.generateCustomsInfo + '/' + me.parentConfig.editData.sid).then(() => {
          me.$Message.warning('生成报关信息成功!')
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('generateCustomsInfo')
        })
      },
      /**
       * 行拆分
       */
      handleRowSplit() {
        let me = this
        if (me.checkRowSelected('拆分', true)) {
          me.$set(me, 'splitPopShow', true)
        }
      },
      /**
       * 执行拆分
       * @param splitDecQty
       */
      doRowSplit(splitDecQty) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确认',
          cancelText: '取消',
          content: '确认要拆分所选项吗',
          onOk: () => {
            me.setToolbarLoading('rowSplit', true)
            let theRow = me.gridConfig.selectRows[0]
            me.$http.post(me.ajaxUrl.rowSplit + '/' + theRow.sid, splitDecQty).then(() => {
              me.handleSearchSubmit()
              me.$Message.success('拆分成功')
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('rowSplit')
              me.$set(me, 'splitPopShow', false)
            })
            setTimeout(() => {
              me.$Modal.remove()
            }, 150)
          }
        })
      },
      /**
       * 行移动(上下移动)
       * @param command
       * @param moveType
       */
      handleRowMove(command, moveType) {
        let me = this
        if (me.checkRowSelected('移动', true)) {
          let theRow = me.gridConfig.selectRows[0]
          if (moveType === -1) {
            if (theRow.entryGNo < 2) {
              return
            }
          } else if (moveType === 1) {
            let maxNo = 0
            me.gridConfig.data.forEach(row => {
              if (row.entryGNo > maxNo) {
                maxNo = row.entryGNo
              }
            })
            if (theRow.entryGNo === maxNo) {
              return
            }
          } else {
            me.$Message.warning('移动类型不存在!')
            return
          }
          me.setToolbarLoading(command, true)
          let currSid = theRow.sid
          me.$http.post(me.ajaxUrl.serialNoChange + '/' + theRow.sid + '/' + moveType).then(() => {
            me.$set(me, 'currSid', currSid)
            me.getList()
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'currCommand', command)
          })
        }
      },
      /**
       * 查询执行完成后的默认操作
       */
      afterSearch() {
        let me = this
        if (!isNullOrEmpty(me.currSid)) {
          let rowIndex = me.gridConfig.data.findIndex(it => it.sid === me.currSid)
          if (rowIndex > -1) {
            me.$nextTick(() => {
              if (me.$refs.table) {
                if (typeof me.$refs.table.toggleSelect === 'function') {
                  me.$refs.table.toggleSelect(rowIndex)
                }
              }
              if (!isNullOrEmpty(me.currCommand)) {
                me.$nextTick(() => {
                  me.setToolbarLoading(me.currCommand)
                })
              }
            })
            me.$set(me, 'currSid', '')
          }
        } else {
          if (!isNullOrEmpty(me.currCommand)) {
            me.setToolbarLoading(me.currCommand)
          }
        }
      },
      /**
       * 行上移
       */
      handleRowMoveUp() {
        let me = this
        me.handleRowMove('moveUp', -1)
      },
      /**
       * 行下移
       */
      handleRowMoveDown() {
        let me = this
        me.handleRowMove('moveDown', 1)
      },
      /**
       * 数据清空
       */
      handleClear() {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确认',
          cancelText: '取消',
          content: '确认要清空所选项吗',
          onOk: () => {
            me.setToolbarLoading('clear', true)
            me.$http.post(me.ajaxUrl.dataClear + '/' + me.parentConfig.editData.sid).then(() => {
              me.$Message.warning('数据已清空!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('clear')
            })
            setTimeout(() => {
              me.$Modal.remove()
            }, 150)
          }
        })
      },
      /**
       * 行编辑保存后执行
       */
      afterRowSave() {
        this.handleSearchSubmit()
      }
    },
    computed: {
      originalDecQty() {
        if (this.gridConfig.selectRows.length > 0) {
          return this.gridConfig.selectRows[0].decQty
        }
        return null
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .ivu-table .table-row-red td {
    color: #fff !important;
    background-color: #ff6600 !important;
  }
</style>
