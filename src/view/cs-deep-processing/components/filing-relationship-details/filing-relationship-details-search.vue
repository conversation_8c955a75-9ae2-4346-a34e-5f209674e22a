<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="130" inline>
      <!--转入-->
      <XdoFormItem v-if="transferType === 'I'" prop="tradeCodeOut" label="转出方海关十位代码">
        <xdo-select v-model="searchParam.tradeCodeOut" :options="this.cmbSource.tradeCodeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="customerCodeOut" label="转出方代码及名称">
        <xdo-select v-model="searchParam.customerCodeOut" :options="this.cmbSource.customerCodeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="emsNoOut" label="转出方备案号">
        <XdoIInput v-model="searchParam.emsNoOut" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange v-if="transferType === 'I'" label="转出方备案有效期" @onDateRangeChanged="handleValidDateOutChange"></dc-dateRange>
      <XdoFormItem v-if="transferType === 'I'" prop="emsNoIn" label="转入方备案号">
        <xdo-select v-model="searchParam.emsNoIn" clearable :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="copGNoOut" label="转出备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoOut" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoOut" label="转出备案序号">
        <xdo-input v-model="searchParam.gnoOut" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="copGNoIn" label="转入备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoIn" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'I'" prop="gnoIn" label="转入备案序号">
        <XdoIInput type="text" v-model="searchParam.gnoIn" clearable></XdoIInput>
      </XdoFormItem>

      <!--转出-->
      <XdoFormItem v-if="transferType === 'E'" prop="tradeCodeIn" label="转入方海关十位代码">
        <xdo-select v-model="searchParam.tradeCodeIn" :options="this.cmbSource.tradeCodeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="customerCodeIn" label="转入方代码及名称">
        <xdo-select v-model="searchParam.customerCodeIn" :options="this.cmbSource.customerCodeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="emsNoIn" label="转入方备案号">
        <XdoIInput v-model="searchParam.emsNoIn" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange v-if="transferType === 'E'" label="转入方备案有效期" @onDateRangeChanged="handleValidDateInChange"></dc-dateRange>
      <XdoFormItem v-if="transferType === 'E'" prop="emsNoOut" label="转出方备案号">
        <xdo-select v-model="searchParam.emsNoOut" clearable :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="copGNoIn" label="转入备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoIn" clearable :maxlength="20"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoIn" label="转入备案序号">
        <xdo-input v-model="searchParam.gnoIn" number int-length="10"></xdo-input>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="copGNoOut" label="转出备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNoOut" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="transferType === 'E'" prop="gnoOut" label="转出备案序号">
        <XdoIInput type="text" v-model="searchParam.gnoOut" clearable></XdoIInput>
      </XdoFormItem>

      <!--共用-->
      <dc-dateRange label="录入日期" @onDateRangeChanged="handleInsertTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { deepProcessingSearch } from '../../js/comm/deepProcessingSearch'

  export default {
    name: 'filingRelationshipDetailsSearch',
    mixins: [deepProcessingSearch],
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          emsNoData: [],
          tradeCodeData: [],
          customerCodeData: []
        })
      }
    },
    data() {
      return {
        searchParam: {
          // 转入
          tradeCodeOut: '',
          customerCodeOut: '',
          emsNoOut: '',
          validDateOutFrom: '',
          validDateOutTo: '',
          emsNoIn: '',
          copGNoOut: '',
          gnoOut: '',

          // 转出
          tradeCodeIn: '',
          customerCodeIn: '',
          // emsNoIn: '',
          validDateInFrom: '',
          validDateInTo: '',
          // emsNoOut: '',
          copGNoIn: '',
          gnoIn: '',

          // 共用
          insertTimeFrom: '',
          insertTimeTo: ''
        }
      }
    },
    methods: {
      /**
       * 转出方备案有效期
       * @param values
       */
      handleValidDateOutChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "validDateOutFrom", values[0])
          this.$set(this.searchParam, "validDateOutTo", values[1])
        } else {
          this.$set(this.searchParam, "validDateOutFrom", '')
          this.$set(this.searchParam, "validDateOutTo", '')
        }
      },
      /**
       * 转入方备案有效期
       * @param values
       */
      handleValidDateInChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "validDateInFrom", values[0])
          this.$set(this.searchParam, "validDateInTo", values[1])
        } else {
          this.$set(this.searchParam, "validDateInFrom", '')
          this.$set(this.searchParam, "validDateInTo", '')
        }
      },
      /**
       * 录入日期
       * @param values
       */
      handleInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
