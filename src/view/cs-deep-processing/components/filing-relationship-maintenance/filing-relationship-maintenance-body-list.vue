<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <FilingRelationshipMaintenanceBodySearch ref="headSearch"
                                                     :transfer-type="transferType"></FilingRelationshipMaintenanceBodySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable v-if="tableShow" class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <FilingRelationshipMaintenanceBodyEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                                           :transfer-type="transferType" :parent-config="parentConfig"></FilingRelationshipMaintenanceBodyEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalBodyColumns" class="height:500px"></TableColumnSetup>
    <ImportPage :importShow.sync="modelImportShow" :importKey="importConfig.importKey"
                :importConfig="importConfig.Config" @onImportSuccess="afterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import ImportPage from 'xdo-import'
  import { editStatus } from '@/view/cs-common'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { deepProcessingBodyList } from '../../js/comm/deepProcessingBodyList'
  import FilingRelationshipMaintenanceBodyEdit from './filing-relationship-maintenance-body-edit'
  import FilingRelationshipMaintenanceBodySearch from './filing-relationship-maintenance-body-search'
  import { columns } from '../../js/filing-relationship-maintenance/filingRelationshipMaintenanceColumns'

  export default {
    name: 'filingRelationshipMaintenanceBodyList',
    components: {
      ImportPage,
      FilingRelationshipMaintenanceBodyEdit,
      FilingRelationshipMaintenanceBodySearch
    },
    mixins: [deepProcessingBodyList, columns, dynamicImport],
    data() {
      return {
        // 查询条件行数
        searchLines: 2,
        ajaxUrl: {
          deleteUrl: '',
          exportUrl: '',
          getListCount: '',
          selectAllPaged: ''
        },
        // 有子tab组件
        hasChildTabs: true,
        totalBodyColumns: [],
        modelImportShow: false,
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'import': this.handleImport,
          'export': this.handleDownload,
          'setting': this.handleTableColumnSetup
        }
      }
    },
    created: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me.gridConfig, 'exportTitle', '转入备案关系维护表体')
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.body.delete)
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.body.exportUrl)
        me.$set(me.ajaxUrl, 'getListCount', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.body.getListCount)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.body.selectAllPaged)
      } else {
        me.$set(me.gridConfig, 'exportTitle', '转出备案关系维护表体')
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.body.delete)
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.body.exportUrl)
        me.$set(me.ajaxUrl, 'getListCount', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.body.getListCount)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.body.selectAllPaged)
      }
    },
    mounted: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me, 'totalBodyColumns', me.totalBodyInColumns)
        me.setShowFields(me.totalBodyInColumns, me.parentConfig.editStatus !== editStatus.EDIT)
      } else if (me.transferType === 'E') {
        me.$set(me, 'totalBodyColumns', me.totalBodyOutColumns)
        me.setShowFields(me.totalBodyOutColumns, me.parentConfig.editStatus !== editStatus.EDIT)
      } else {
        console.error('请设置深加工转入、转出类型transferType')
      }
    },
    methods: {
      actionLoad() {
        let me = this
        me.actions.push({
          ...me.actionsComm,
          label: '新增',
          command: 'add',
          icon: 'ios-add'
        }, {
          ...me.actionsComm,
          label: '编辑',
          command: 'edit',
          icon: 'ios-create-outline'
        }, {
          ...me.actionsComm,
          label: '删除',
          command: 'delete',
          icon: 'ios-trash-outline'
        }, {
          ...me.actionsComm,
          label: '导入',
          command: 'import',
          icon: 'ios-cloud-upload-outline'
        }, {
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          icon: 'ios-cloud-download-outline'
        }, {
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting'
        })
      },
      /**
       * 重置按钮
       */
      resetActions() {
        let me = this
        if (me.parentConfig.editStatus === editStatus.SHOW) {
          me.actions = me.actions.filter(item => {
            return !['add', 'edit', 'delete', 'import'].includes(item.command)
          })
        }
      },
      /**
       * 查询执行完成后的默认操作
       */
      afterSearch() {
        let me = this
        me.$http.post(me.ajaxUrl.getListCount + '/' + me.parentConfig.editData.sid).then(res => {
          me.$emit('onBodyListLoad', res.data.data)
        }).catch(() => {
        })
      },
      /**
       * 导入
       */
      handleImport() {
        this.modelImportShow = true
      },
      afterImport() {
        this.modelImportShow = false
        this.getList()
      }
    },
    computed: {
      importConfig() {
        let me = this,
          importKey = '',
          taskCode = '',
          emsNo = ''
        if (me.transferType === 'I') {
          importKey = 'sjgInRelationList'
          taskCode = 'SJG_IN_RELATION_LIST'
          emsNo = me.parentConfig.editData.emsNoIn
        } else if (me.transferType === 'E') {
          importKey = 'sjgOutRelationList'
          taskCode = 'SJG_OUT_RELATION_LIST'
          emsNo = me.parentConfig.editData.emsNoOut
        }
        let commConfig = me.getCommImportConfig(taskCode, {
          emsNo: emsNo,
          accessToken: me.$store.state.token,
          headId: me.parentConfig.editData.sid
        }, me.parentConfig.editData.sid)
        return {
          Config: commConfig,
          importKey: importKey
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
