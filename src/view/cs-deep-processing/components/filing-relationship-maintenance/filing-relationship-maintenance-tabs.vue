<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <Head ref="head" @onEditBack="editBack" :transfer-type="transferType" :edit-config="editConfig"
              :cmb-source="cmbSource" :has-body-list="hasBodyList"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体">
        <Body ref="body" v-if="tabs.bodyTab" :transfer-type="transferType" @onBodyListLoad="bodyListLoad"
              :parent-config="parentConfig"></Body>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { isNumber } from '@/libs/util'
  import Head from './filing-relationship-maintenance-edit'
  import Body from './filing-relationship-maintenance-body-list'
  import { deepProcessingTabs } from '../../js/comm/deepProcessingTabs'

  export default {
    name: 'filingRelationshipMaintenanceTabs',
    components: {
      Head,
      Body
    },
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          emsNoData: [],
          tradeCodeObj: {},
          tradeCodeData: []
        })
      }
    },
    mixins: [deepProcessingTabs],
    data() {
      return {
        hasBodyList: false
      }
    },
    methods: {
      bodyListLoad(bodyCount) {
        let me = this
        if (isNumber(bodyCount) && bodyCount > 0) {
          me.$set(me, 'hasBodyList', true)
        } else {
          me.$set(me, 'hasBodyList', false)
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
