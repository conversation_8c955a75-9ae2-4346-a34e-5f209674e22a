<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false" title="" class="ieLogisticsTrackingCard">
      <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
        <XdoFormItem v-if="transferType === 'I'" prop="tradeCodeOut" label="转出方海关十位代码">
          <xdo-select v-model="frmData.tradeCodeOut" :options="this.cmbSource.tradeCodeData"
                      :optionLabelRender="pcodeRender" :disabled="realShowDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="tradeCodeIn" label="转入方海关十位代码">
          <xdo-select v-model="frmData.tradeCodeIn" :options="this.cmbSource.tradeCodeData"
                      :optionLabelRender="pcodeRender" :disabled="realShowDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'I'" prop="customerCodeOut" label="转出方代码">
          <XdoIInput type="text" v-model="frmData.customerCodeOut" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="customerCodeIn" label="转入方代码">
          <XdoIInput type="text" v-model="frmData.customerCodeIn" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'I'" prop="tradeNameOut" label="转出方名称">
          <XdoIInput type="text" v-model="frmData.tradeNameOut" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="tradeNameIn" label="转入方名称">
          <XdoIInput type="text" v-model="frmData.tradeNameIn" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'I'" prop="emsNoOut" label="转出方备案号">
          <XdoIInput v-model="frmData.emsNoOut" :disabled="realShowDisable" clearable :maxlength="12"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="emsNoIn" label="转入方备案号">
          <XdoIInput v-model="frmData.emsNoIn" :disabled="realShowDisable" clearable :maxlength="12"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'I'" prop="emsNoIn" label="转入方备案号">
          <xdo-select v-model="frmData.emsNoIn" clearable :options="this.cmbSource.emsNoData" :disabled="realShowDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="emsNoOut" label="转出方备案号">
          <xdo-select v-model="frmData.emsNoOut" clearable :options="this.cmbSource.emsNoData" :disabled="realShowDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'I'" prop="validDateOut" label="转出方备案有效期">
          <XdoDatePicker type="date" v-model="frmData.validDateOut" style="width: 100%;" transfer :disabled="showDisable"></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem v-if="transferType === 'E'" prop="validDateIn" label="转入方备案有效期">
          <XdoDatePicker type="date" v-model="frmData.validDateIn" style="width: 100%;" transfer :disabled="showDisable"></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="insertTime" label="录入日期">
          <XdoDatePicker type="date" v-model="frmData.insertTime" style="width: 100%;" disabled></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="insertUser" label="录入人">
          <XdoIInput v-model="frmData.insertUser" disabled></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { deepProcessingEdit } from '../../js/comm/deepProcessingEdit'

  export default {
    name: 'filingRelationshipMaintenanceEdit',
    mixins: [deepProcessingEdit],
    props: {
      cmbSource: {
        type: Object,
        required: true,
        default: () => ({
          emsNoData: [],
          tradeCodeObj: {},
          tradeCodeData: []
        })
      },
      hasBodyList: {
        type: Boolean,
        required: true,
        default: () => false
      }
    },
    data() {
      return {
        ajaxUrl: {
          insert: '',
          update: ''
        },
        formName: 'dataForm',
        rulesHeader: {
          emsNoIn: [{required: true, message: '不能为空!', trigger: 'blur'}],
          emsNoOut: [{required: true, message: '不能为空!', trigger: 'blur'}],
          tradeCodeOut: [{required: false, message: '不能为空!', trigger: 'blur'}],
          tradeNameOut: [{required: false, message: '不能为空!', trigger: 'blur'}],
          customerCodeOut: [{required: false, message: '不能为空!', trigger: 'blur'}],
          tradeCodeIn: [{required: false, message: '不能为空!', trigger: 'blur'}],
          tradeNameIn: [{required: false, message: '不能为空!', trigger: 'blur'}],
          customerCodeIn: [{required: false, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    watch: {
      'frmData.tradeCodeOut': {
        handler: function (tradeCode) {
          let me = this
          let tradeCodeName = me.cmbSource.tradeCodeObj[tradeCode]
          if (tradeCodeName && !isNullOrEmpty(tradeCodeName.code)) {
            me.$set(me.frmData, 'customerCodeOut', tradeCodeName.code)
          } else {
            me.$set(me.frmData, 'customerCodeOut', '')
          }
          if (tradeCodeName && !isNullOrEmpty(tradeCodeName.name)) {
            me.$set(me.frmData, 'tradeNameOut', tradeCodeName.name)
          } else {
            me.$set(me.frmData, 'tradeNameOut', '')
          }
        }
      },
      'frmData.tradeCodeIn': {
        handler: function (tradeCode) {
          let me = this
          let tradeCodeName = me.cmbSource.tradeCodeObj[tradeCode]
          if (tradeCodeName && !isNullOrEmpty(tradeCodeName.code)) {
            me.$set(me.frmData, 'customerCodeIn', tradeCodeName.code)
          } else {
            me.$set(me.frmData, 'customerCodeIn', '')
          }
          if (tradeCodeName && !isNullOrEmpty(tradeCodeName.name)) {
            me.$set(me.frmData, 'tradeNameIn', tradeCodeName.name)
          } else {
            me.$set(me.frmData, 'tradeNameIn', '')
          }
        }
      }
    },
    created: function () {
      let me = this
      if (me.transferType === 'I') {
        // 必填项设置
        me.rulesHeader.tradeCodeOut[0].required = true
        me.rulesHeader.tradeNameOut[0].required = true
        me.rulesHeader.customerCodeOut[0].required = true
        me.$set(me.ajaxUrl, 'insert', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.head.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.deepProcessing.filingRelationshipMaintenance.transferIn.head.update)
      } else {
        // 必填项设置
        me.rulesHeader.tradeCodeIn[0].required = true
        me.rulesHeader.tradeNameIn[0].required = true
        me.rulesHeader.customerCodeIn[0].required = true
        me.$set(me.ajaxUrl, 'insert', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.head.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.deepProcessing.filingRelationshipMaintenance.transferOut.head.update)
      }
    },
    methods: {
      getDefaultData() {
        let me = this,
          tradeCodeOut = '',
          tradeNameOut = ''
        if (me.transferType === 'E') {
          tradeCodeOut = me.$store.state.user.company
          tradeNameOut = me.$store.state.user.companyName
        }
        let tradeCodeIn = ''
        let tradeNameIn = ''
        if (me.transferType === 'I') {
          tradeCodeIn = me.$store.state.user.company
          tradeNameIn = me.$store.state.user.companyName
        }
        return {
          sid: '',
          status: '1',
          emsNoIn: '',
          emsNoOut: '',
          validDateIn: '',
          validDateOut: '',
          customerCodeIn: '',
          customerCodeOut: '',
          tradeCodeIn: tradeCodeIn,
          tradeNameIn: tradeNameIn,
          tradeCodeOut: tradeCodeOut,
          tradeNameOut: tradeNameOut,
          insertUser: me.$store.state.user.userNo,
          insertTime: (new Date()).format('yyyy-MM-dd hh:mm:ss')
        }
      }
    },
    computed: {
      realShowDisable() {
        if (this.hasBodyList) {
          return true
        }
        return this.showDisable
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body{
    padding: 8px 8px 2px 8px;
  }
</style>
