<template>
  <XdoModal ref="erpModel" v-model="show" mask width="1024" title="提取ERP数据"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <InformationExtractionExtractList :transfer-type="transferType" :head-id="headId" :clear-data="show"
                                      @onConfirm="reloadParent" :grd-height="500" :IeEntry="IeEntry"></InformationExtractionExtractList>
  </XdoModal>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import InformationExtractionExtractList from './information-extraction-extract-list'

  export default {
    name: 'informationExtractionExtractPop',
    components: {
      InformationExtractionExtractList
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      transferType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      headId: {
        type: String,
        default: ''
      },
      IeEntry: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              let modelChildren = me.$refs['erpModel'].$el.childNodes
              let item
              for (let i = 0; i < modelChildren.length; i++) {
                item = modelChildren[i]
                if (!isNullOrEmpty(item.className)) {
                  if (item.className.indexOf('ivu-modal-mask') > -1 || item.className.indexOf('ivu-modal-wrap') > -1) {
                    item.style.zIndex = '1000'
                  }
                }
              }
            })
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      reloadParent() {
        let me = this
        me.handleClose()
        me.$emit('onReload')
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }
</style>
