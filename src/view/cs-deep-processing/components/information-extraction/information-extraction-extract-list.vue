<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InformationExtractionExtractSearch ref="headSearch" :transfer-type="transferType" :IeEntry="IeEntry" :clearData="clearData"></InformationExtractionExtractSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="theGridHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 25px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalExtractColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { deepProcessingList } from '../../js/comm/deepProcessingList'
  import { columns } from '../../js/information-extraction/informationExtractionColumns'
  import InformationExtractionExtractSearch from './information-extraction-extract-search'
  import { deepProcessing, delList, erpInterfaceData, interimVerification } from '@/view/cs-common'

  export default {
    name: 'informationExtractionExtractList',
    components: {
      InformationExtractionExtractSearch
    },
    mixins: [deepProcessingList, columns],
    props: {
      grdHeight: {
        type: Number,
        default: 0
      },
      transferType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      headId: {
        type: String,
        default: ''
      },
      clearData: {
        type: Boolean,
        default: () => false
      },
      IeEntry: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        totalContent: '',
        toolbarEventMap: {
          'export': this.handleDownload,
          'setting': this.handleTableColumnSetup,
          'extractByQuery': this.handleExtractByQuery,
          'extractByChoose': this.handleExtractByChoose
        },
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: '',
          // Erp总数量总金额
          totalExtractet: '',
          // 提取数据-条件筛选
          extractErpDataByQuery: '',
          // 提取数据-勾选
          extractErpDataByChoose: ''
        },
        // 查询条件行数
        searchLines: 5,
        totalExtract: '',
        delList: delList,
        initSearch: false,
        erpInterfaceData: erpInterfaceData,
        interimVerification: interimVerification
      }
    },
    created: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me.gridConfig, 'exportTitle', 'ERP料件入库数据')
        if (!isNullOrEmpty(me.headId)) {
          // 此处为提单表体使用的接口==============================================
          me.$set(me.ajaxUrl, 'extractErpDataByChoose', csAPI.csImportExport.decErpIListN.extractErpDataByChoose)
          me.$set(me.ajaxUrl, 'extractErpDataByQuery', csAPI.csImportExport.decErpIListN.extractErpDataByQuery)
        } else {
          me.$set(me.ajaxUrl, 'extractErpDataByChoose', csAPI.deepProcessing.informationExtraction.transferIn.extract.extractErpDataByChoose)
          me.$set(me.ajaxUrl, 'extractErpDataByQuery', csAPI.deepProcessing.informationExtraction.transferIn.extract.extractErpDataByQuery)
        }
        me.$set(me.ajaxUrl, 'totalExtractet', csAPI.deepProcessing.informationExtraction.transferIn.extract.totalExtractet)
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.informationExtraction.transferIn.extract.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.informationExtraction.transferIn.extract.selectAllPaged)
      } else {
        me.$set(me.gridConfig, 'exportTitle', 'ERP成品出库数据')
        if (!isNullOrEmpty(me.headId)) {
          // 此处为提单表体使用的接口==============================================
          me.$set(me.ajaxUrl, 'extractErpDataByChoose', csAPI.csImportExport.decErpEListN.extractErpDataByChoose)
          me.$set(me.ajaxUrl, 'extractErpDataByQuery', csAPI.csImportExport.decErpEListN.extractErpDataByQuery)
        } else {
          me.$set(me.ajaxUrl, 'extractErpDataByChoose', csAPI.deepProcessing.informationExtraction.transferOut.extract.extractErpDataByChoose)
          me.$set(me.ajaxUrl, 'extractErpDataByQuery', csAPI.deepProcessing.informationExtraction.transferOut.extract.extractErpDataByQuery)
        }
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.informationExtraction.transferOut.extract.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.informationExtraction.transferOut.extract.selectAllPaged)
        me.$set(me.ajaxUrl, 'totalExtractet', csAPI.deepProcessing.informationExtraction.transferOut.extract.totalExtractet)
      }
    },
    mounted: function () {
      let me = this
      me.showSearch = !me.showSearch
      if (me.transferType === 'I') {
        me.totalExtractColumns = [...me.totalIExtractColumns]
      } else {
        me.totalExtractColumns = [...me.totalEExtractColumns]
      }
      me.setShowFields(me.totalExtractColumns, true)
    },
    methods: {
      getGrdColumns(columns) {
        return [{
          width: 36,
          fixed: 'left',
          align: 'center',
          key: 'selection',
          type: 'selection'
        }, ...columns]
      },
      actionLoadMethods() {
        let me = this
        me.loadFunctions().then(() => {
          if (typeof me.actionLoad === "function") {
            me.actionLoad()
          }
        })
      },
      actionLoad() {
        let me = this
        me.actions = []
        me.actions.push({
          ...me.actionsComm,
          label: '提取勾选',
          key: 'xdo-btn-edit',
          icon: 'ios-checkmark',
          command: 'extractByChoose'
        }, {
          ...me.actionsComm,
          label: '全部提取',
          icon: 'md-done-all',
          key: 'xdo-btn-upload',
          command: 'extractByQuery'
        }, {
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          key: 'xdo-btn-download',
          icon: 'ios-cloud-download-outline'
        }, {
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      },
      /**
       * 提取(按勾选)
       */
      handleExtractByChoose() {
        let me = this
        if (me.checkRowSelected('提取勾选')) {
          me.setButtonLoading('extractByChoose', true)
          let params = me.getSelectedParams()
          let url = me.ajaxUrl.extractErpDataByChoose
          if (!isNullOrEmpty(me.headId)) {
            url += '/' + me.headId
          }
          me.$http.post(url, params, {
            noIntercept: true
          }).then(res => {
            if (res && res.data) {
              if (res.data.success === true) {
                me.$Message.success('数据勾选提取成功!')
                me.$emit('onConfirm')
              } else {
                if (!isNullOrEmpty(res.data.message)) {
                  me.$Message.error(res.data.message)
                } else {
                  me.$Message.error('数据勾选提取失败!')
                }
              }
            } else {
              if (res && !isNullOrEmpty(res.message)) {
                me.$Message.error(res.message)
              } else {
                me.$Message.error('数据勾选提取失败!')
              }
            }
          }).catch(() => {
          }).finally(() => {
            me.setButtonLoading('extractByChoose', false)
          })
        }
      },
      /**
       * 全部提取
       */
      handleExtractByQuery() {
        let me = this
        me.setButtonLoading('extractByQuery', true)
        let params = me.getSearchParams()
        let url = me.ajaxUrl.extractErpDataByQuery
        if (!isNullOrEmpty(me.headId)) {
          url += '/' + me.headId
        }
        me.$http.post(url, params, {
          noIntercept: true
        }).then(res => {
          if (res && res.data) {
            if (res.data.success === true) {
              me.$Message.success('数据全部提取成功!')
              me.$emit('onConfirm')
            } else {
              if (!isNullOrEmpty(res.data.message)) {
                me.$Message.error(res.data.message)
              } else {
                me.$Message.error('数据全部提取失败!')
              }
            }
          } else {
            if (res && !isNullOrEmpty(res.message)) {
              me.$Message.error(res.message)
            } else {
              me.$Message.error('数据全部提取失败!')
            }
          }
        }).catch(() => {
        }).finally(() => {
          me.setButtonLoading('extractByQuery', false)
        })
      }
    },
    watch: {
      'gridConfig.selectRows': {
        handler: function (val) {
          let me = this
          if (val.length > 0) {
            let allQty = 0
            let allTotal = 0
            val.forEach(item => {
              allQty = allQty + parseFloat(item.qty)
              allTotal = allTotal + parseFloat(item.decTotal)
            })
            me.totalContent = `汇总条数${val.length}    总数量${allQty.toFixed(5)}    总金额${allTotal.toFixed(2)}`
          } else if (!isNullOrEmpty(me.ajaxUrl.totalExtractet)) {
            if (me.$refs.headSearch.searchParam) {
              let param = me.$refs.headSearch.searchParam
              me.$http.post(me.ajaxUrl.totalExtractet, param).then(res => {
                me.totalContent = `总数量${res.data.data.decQty}    总金额${res.data.data.decTotal}`
              }, () => {
              })
            }
          }
        }
      },
      clearData: {
        immediate: true,
        handler: function (clearData) {
          let me = this
          if (clearData) {
            me.pageParam.page = 1
            me.gridConfig.data = []
            me.pageParam.dataTotal = 0
            me.gridConfig.selectRows = []
            console.info('数据清空!')
            me.$nextTick(() => {
              me.$refs.headSearch.getDefaultData()
              me.getList()
            })
          }
        }
      }
    },
    computed: {
      theGridHeight() {
        if (this.grdHeight > 0) {
          return this.grdHeight
        }
        return this.dynamicHeight
      },
      ieTypeData() {
        if (this.transferType === 'I') {
          return deepProcessing.IE_TYPE_IN_MAP
        } else {
          return deepProcessing.IE_TYPE_OUT_MAP
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
