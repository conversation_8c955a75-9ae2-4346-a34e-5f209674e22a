<template>
  <XdoModal v-model="show" mask width="380" title="对账确认"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header" style="margin: 5px;">
      <XdoForm ref="popForm" class="dc-form dc-form-3 xdo-enter-form" :model="modelData" :rules="rules" label-position="right" :label-width="100">
        <XdoFormItem prop="checkNo" class="dc-merge-1-4" :label="checkNoLabel">
          <XdoIInput type="text" v-model="modelData.checkNo" placeholder="请输入新的内部编号..."></XdoIInput>
        </XdoFormItem>
        <dc-dateRange label="结转周期" disabled :className="dcDateClass" :values="carryOverCycle"></dc-dateRange>
      </XdoForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: right; margin: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'informationExtractionExtractPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      transferType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      carryOverCycle: {
        type: Array,
        required: true,
        default: () => (['', ''])
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        modelData: {
          checkNo: ''
        },
        dcDateClass: 'dc-merge-1-4',
        rules: {
          checkNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, label: '返回', type: 'default', icon: 'dc-btn-cancel', click: this.handleClose
        }, {
          ...btnComm, label: '保存', type: 'primary', icon: 'dc-btn-save', click: this.handleSave
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (!show) {
            let me = this
            me.$nextTick(() => {
              if (me.$refs['popForm']) {
                me.$refs['popForm'].resetFields()
              }
            })
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
        me.modelData.checkNo = ''
      },
      handleSave() {
        let me = this
        me.$refs['popForm'].validate().then(isValid => {
          if (isValid) {
            me.$emit('onConfirm', me.modelData.checkNo)
            me.handleClose()
          }
        })
      }
    },
    computed: {
      checkNoLabel() {
        let me = this
        if (me.transferType === 'I') {
          return '对账编号'
        } else if (me.transferType === 'E') {
          return '对账编号'
        } else {
          return '对账编号'
        }
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
  }
</style>
