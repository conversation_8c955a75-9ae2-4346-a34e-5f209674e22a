<template>
  <XdoModal width="400" mask title="结转对账拆分" v-model="show"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <div class="header">
      <XdoForm ref="frmTemplate" class="dc-form dc-form-2" :model="templateData" :rules="templateRules" label-position="right" :label-width="80" inline>
        <XdoFormItem prop="decQty" label="原数量">
          <XdoIInput type="text" v-model="originalDecQty" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="decQty" label="未拆分数量">
          <XdoIInput type="text" v-model="leftDecQty" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="splitDecQty" class="dc-merge-1-3" label="拆分数量">
          <XdoIInput type="text" v-model="templateData.splitDecQty" placeholder="请输入拆分数量(以','隔开)" clearable></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </div>
    <div class="action" style="text-align: right;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.label">{{item.label}}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { isNullOrEmpty, isNumber } from '@/libs/util'

  export default {
    name: 'InformationExtractionSplitPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      originalDecQty: {
        type: Number,
        default: () => null
      }
    },
    data() {
      let commBtn = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        leftDecQty: null,
        templateData: {
          splitDecQty: ''
        },
        templateRules: {
          splitDecQty: [{required: true, message: '请输入拆分数量'}]
        },
        buttons: [{
          ...commBtn, label: '取消', type: 'default', icon: 'dc-btn-Cancel', click: this.handleCancel
        }, {
          ...commBtn, label: '确定', type: 'primary', icon: 'dc-btn-confirm', click: this.handleConfirm
        }]
      }
    },
    watch: {
      'templateData.splitDecQty': {
        immediate: true,
        handler: function (qty) {
          if (!isNullOrEmpty(qty)) {
            qty = qty.trim()
            while (qty.indexOf('，') > -1) {
              qty = qty.replace('，', ',')
            }
            while (qty.indexOf('。') > -1) {
              qty = qty.replace('。', '.')
            }
            while (qty.indexOf(',,') > -1) {
              qty = qty.replace(',,', ',')
            }
            while (qty.indexOf('..') > -1) {
              qty = qty.replace('..', '.')
            }
            while (qty.indexOf('.,') > -1) {
              qty = qty.replace('.,', ',')
            }
            while (qty.indexOf(',.') > -1) {
              qty = qty.replace(',.', ',0.')
            }
            let lastWord = ''
            if (qty.substring(qty.length - 1) === ',') {
              lastWord = ','
            }
            if (qty === '-') {
              lastWord = '-'
            }
            if (qty.substring(qty.length - 2) === ',-') {
              lastWord = ',-'
            }
            // 数据整理
            let tmpValues = qty.split(',')
            let newValArr = []
            let validTotal = 0
            tmpValues.forEach(item => {
              if (!isNullOrEmpty(item)) {
                let items = item.split('')
                let newItem = ''
                let hasDecimalPoint = false
                for (let i = 0; i < items.length; i++) {
                  let itemChar = items[i]
                  if (i === 0) {
                    hasDecimalPoint = false
                    if (itemChar === '.') {
                      hasDecimalPoint = true
                      newItem = '0.'
                    } else if (itemChar === '-') {
                      newItem = '-'
                    } else {
                      if (isNumber(itemChar)) {
                        newItem = itemChar
                      }
                    }
                  } else if (i === 1) {
                    if (isNullOrEmpty(newItem)) {
                      if (itemChar === '.') {
                        hasDecimalPoint = true
                        newItem = '0.'
                      } else if (itemChar === '-') {
                        newItem = '-'
                      } else {
                        if (isNumber(itemChar)) {
                          newItem = itemChar
                        }
                      }
                    } else {
                      if (hasDecimalPoint) {
                        if (isNumber(itemChar)) {
                          newItem += itemChar
                        }
                      } else {
                        if (isNumber(itemChar)) {
                          newItem += itemChar
                        } else if (itemChar === '.') {
                          hasDecimalPoint = true
                          newItem += itemChar
                        }
                      }
                    }
                  } else {
                    if (hasDecimalPoint) {
                      if (isNumber(itemChar)) {
                        newItem += itemChar
                      }
                    } else {
                      if (isNumber(itemChar)) {
                        newItem += itemChar
                      } else if (itemChar === '.') {
                        hasDecimalPoint = true
                        newItem += itemChar
                      }
                    }
                  }
                  if (newItem.indexOf('.') > -1 && newItem.length - newItem.indexOf('.') > 5) {
                    i = items.length
                  }
                }
                if (!isNullOrEmpty(newItem) && isNumber(newItem)) {
                  while (newItem.indexOf('00.') > -1) {
                    newItem = newItem.replace('00.', '0.')
                  }
                  newValArr.push(newItem)
                  validTotal += parseFloat(newItem)
                }
              }
            })
            let me = this
            me.$nextTick(() => {
              me.$set(me, 'leftDecQty', (((me.originalDecQty === null) ? 0 : me.originalDecQty) - validTotal).toFixed(5))
              me.$set(me.templateData, 'splitDecQty', newValArr.toString() + lastWord)
            })
          }
        }
      }
    },
    methods: {
      handleCancel() {
        let me = this
        me.$set(me.templateData, 'splitDecQty', '')
        me.$refs.frmTemplate.resetFields()
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$refs['frmTemplate'].validate().then(isValid => {
          if (isValid) {
            if (me.leftDecQty > 0) {
              me.$Message.warning('原数量仍有可拆分剩余!')
            } else if (me.leftDecQty < 0) {
              me.$Message.warning('拆分数量超过原数量!')
            } else {
              me.$emit('confirm:success', me.templateData.splitDecQty.split(',').map(qty => {
                return parseFloat(qty)
              }))
              me.$set(me.templateData, 'splitDecQty', '')
              me.$refs.frmTemplate.resetFields()
            }
          }
        })
      }
    }
  }
</script>

<style lang="less">
  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }
</style>
