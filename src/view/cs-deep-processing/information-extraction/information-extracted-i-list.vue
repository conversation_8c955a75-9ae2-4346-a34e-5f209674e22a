<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InformationExtractionSearch ref="headSearch" transferType="I" status="1,2" :isFacGNoList="false"></InformationExtractionSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <XdoTable v-if="tableShow" class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange" :row-class-name="rowClassName"></XdoTable>
        <div ref="area_page" style="height: 25px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalExtract}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { informationExtractionList } from '../js/information-extraction/informationExtractionList'

  export default {
    name: 'informationExtractedIList',
    mixins: [informationExtractionList],
    data() {
      return {
        confirmed: true,
        transferType: 'I',
        gridConfig: {
          exportTitle: '转出信息提取'
        },
        ajaxUrl: {
          exportUrl: csAPI.deepProcessing.informationExtraction.transferIn.main.exportUrl,
          // 删除已确认数据
          deleteUrl: csAPI.deepProcessing.informationExtraction.transferIn.main.deleteComfirmed,
          selectAllPaged: csAPI.deepProcessing.informationExtraction.transferIn.main.selectAllPaged,
          //获取汇总数据
          getTotalContent: csAPI.deepProcessing.informationExtraction.transferIn.extract.totalContent
        }
      }
    },
    methods: {
      actionLoad() {
        let me = this
        me.actions.push({
          ...me.actionsComm,
          label: '删除',
          command: 'delete',
          icon: 'ios-trash-outline'
        }, {
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          icon: 'ios-cloud-download-outline'
        }, {
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting'
        })
      },
      rowClassName(row) {
        if (row.convertMark === '1') {
          return 'classStyleChange'
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .classStyleChange td {
    background: #99CC99 !important;
  }
</style>
