<template>
  <XdoModal ref="confirmModel" v-model="show" mask width="80%" title="确认数据提取"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InformationExtractionSearch ref="headSearch" :transfer-type="transferType" status="1" forDataManage></InformationExtractionSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" height="500"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page" style="height: 25px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalExtract}}</span>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { informationExtractionList } from '../js/information-extraction/informationExtractionList'

  export default {
    name: 'informationExtractedIEPop',
    mixins: [informationExtractionList],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      transferType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      headId: {
        type: String,
        required: true,
        default: ''
      }
    },
    data() {
      return {
        totalExtract: '',
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: '',
          // 提取数据-条件筛选
          extractDataByQuery: '',
          // 提取数据-勾选
          extractDataByChoose: ''
        },
        toolbarEventMap: {
          'export': this.handleDownload,
          'setting': this.handleTableColumnSetup,
          'extractByQuery': this.handleExtractByQuery,
          'extractByChoose': this.handleExtractByChoose
        }
      }
    },
    created: function () {
      let me = this
      if (me.transferType === 'I') {
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.informationExtraction.transferIn.main.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.informationExtraction.transferIn.main.selectAllPaged)
        // 提取数据-勾选
        me.$set(me.ajaxUrl, 'extractDataByChoose', csAPI.deepProcessing.dataManagement.transferIn.head.extractDataByChoose)
        // 提取数据-条件筛选
        me.$set(me.ajaxUrl, 'extractDataByQuery', csAPI.deepProcessing.dataManagement.transferIn.head.extractDataByQuery)
        // 获取总数据
        me.$set(me.ajaxUrl, 'getTotal', csAPI.deepProcessing.informationExtraction.transferIn.extract.totalContent)
        me.$set(me.gridConfig, 'exportTitle', '可提取确认信息')
      } else {
        me.$set(me.ajaxUrl, 'exportUrl', csAPI.deepProcessing.informationExtraction.transferOut.main.exportUrl)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.deepProcessing.informationExtraction.transferOut.main.selectAllPaged)
        // 提取数据-勾选
        me.$set(me.ajaxUrl, 'extractDataByChoose', csAPI.deepProcessing.dataManagement.transferOut.head.extractDataByChoose)
        // 提取数据-条件筛选
        me.$set(me.ajaxUrl, 'extractDataByQuery', csAPI.deepProcessing.dataManagement.transferOut.head.extractDataByQuery)
        // 获取总数据
        me.$set(me.ajaxUrl, 'getTotal', csAPI.deepProcessing.informationExtraction.transferOut.extract.totalContent)
        me.$set(me.gridConfig, 'exportTitle', '可提取确认信息')
      }
    },
    mounted() {
      let me = this
      me.showSearch = true
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              let modelChildren = me.$refs['confirmModel'].$el.childNodes
              let item
              for (let i = 0; i < modelChildren.length; i++) {
                item = modelChildren[i]
                if (!isNullOrEmpty(item.className)) {
                  if (item.className.indexOf('ivu-modal-mask') > -1 || item.className.indexOf('ivu-modal-wrap') > -1) {
                    item.style.zIndex = '1000'
                  }
                }
              }
            })
          }
        }
      },
      'gridConfig.selectRows': {
        handler: function (val) {
          let me = this
          if (val.length > 0) {
            let allQty = 0
            let allTotal = 0
            val.forEach(item => {
              allQty = allQty + parseFloat(item.qty)
              allTotal = allTotal + parseFloat(item.decTotal)
            })
            me.totalExtract = `汇总条数${val.length}    总数量${allQty.toFixed(5)}    总金额${allTotal.toFixed(2)}`
          } else if (!isNullOrEmpty(me.ajaxUrl.getTotal)) {
            if (me.$refs.headSearch.searchParam) {
              let params = me.$refs.headSearch.searchParam
              me.$http.post(me.ajaxUrl.getTotal, params).then(res => {
                me.totalExtract = `总数量${res.data.data.decQty}    总金额${res.data.data.decTotal}`
              }, () => {
              })
            }
          }
        }
      }
    },
    methods: {
      actionLoadMethods() {
        let me = this
        me.loadFunctions().then(() => {
          if (typeof me.actionLoad === "function") {
            me.actionLoad()
          }
        })
      },
      actionLoad() {
        let me = this
        me.actions = []
        me.actions.push({
          ...me.actionsComm,
          label: '提取勾选',
          key: 'xdo-btn-edit',
          icon: 'ios-checkmark',
          command: 'extractByChoose'
        }, {
          ...me.actionsComm,
          label: '全部提取',
          icon: 'md-done-all',
          key: 'xdo-btn-upload',
          command: 'extractByQuery'
        }, {
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          key: 'xdo-btn-download',
          icon: 'ios-cloud-download-outline'
        }, {
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      reloadParent() {
        let me = this
        me.handleSearchSubmit()
        me.handleClose()
        me.$emit('onReload')
      },
      /**
       * 根据勾选提取
       */
      handleExtractByChoose() {
        let me = this
        if (me.checkRowSelected('提取')) {
          let params = me.getSelectedParams()
          me.setButtonLoading('extractByChoose', true)
          me.$http.post(me.ajaxUrl.extractDataByChoose + '/' + me.headId, params).then(() => {
            me.$Message.success('提取成功!')
            me.reloadParent()
          }).catch(() => {
          }).finally(() => {
            me.setButtonLoading('extractByChoose', false)
          })
        }
      },
      /**
       * 根据查询条件提取
       */
      handleExtractByQuery() {
        let me = this
        let params = me.getSearchParams()
        me.setButtonLoading('extractByQuery', true)
        me.$http.post(me.ajaxUrl.extractDataByQuery + '/' + me.headId, params).then(() => {
          me.$Message.success('提取成功!')
          me.reloadParent()
        }).catch(() => {
        }).finally(() => {
          me.setButtonLoading('extractByQuery', false)
        })
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
