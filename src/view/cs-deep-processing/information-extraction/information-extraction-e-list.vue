<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <InformationExtractionSearch ref="headSearch" transferType="E" status="0" :isFacGNoList="true"></InformationExtractionSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 25px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalExtract}}</span>
        </div>
      </XdoCard>
    </div>
    <InformationExtractionExtractPop :show.sync="erpExtractShow" transferType="E"
                                     @onReload="handleSearchSubmit"></InformationExtractionExtractPop>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
    <ExtractConfirmPop :show.sync="extractConfirmShow" :carry-over-cycle="carryOverCycle" transfer-type="E"
                       @onConfirm="onConfirm"></ExtractConfirmPop>
    <InformationExtractionSplitPop :show.sync="splitPopShow" :original-dec-qty="originalDecQty"
                                   @confirm:success="doRowSplit"></InformationExtractionSplitPop>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import ImportPage from 'xdo-import'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { informationExtractionList } from '../js/information-extraction/informationExtractionList'

  export default {
    name: 'informationExtractionEList',
    mixins: [informationExtractionList, dynamicImport],
    components: {
      ImportPage
    },
    data() {
      let commImportConfig = this.getCommImportConfig('SJG_OUT_CHECK_LIST')
      return {
        searchLines: 5,
        confirmed: false,
        transferType: 'E',
        modelImportShow: false,
        gridConfig: {
          exportTitle: '转出信息提取'
        },
        importKey: 'SJG_OUT_CHECK_LIST',
        importConfig: commImportConfig,
        ajaxUrl: {
          splitUrl: csAPI.deepProcessing.informationExtraction.transferOut.main.split,
          exportUrl: csAPI.deepProcessing.informationExtraction.transferOut.main.exportUrl,
          // 删除未确认数据
          deleteUrl: csAPI.deepProcessing.informationExtraction.transferOut.main.deleteUnComfirmed,
          // 获取结转周期范围
          getMaxMinDate: csAPI.deepProcessing.informationExtraction.transferOut.main.getMaxMinDate,
          selectAllPaged: csAPI.deepProcessing.informationExtraction.transferOut.main.selectAllPaged,
          //获取汇总数据
          getTotalContent: csAPI.deepProcessing.informationExtraction.transferOut.extract.totalContent,
          confirmForQuery: csAPI.deepProcessing.informationExtraction.transferOut.main.confirmForQuery,
          confirmForChoose: csAPI.deepProcessing.informationExtraction.transferOut.main.confirmForChoose
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
