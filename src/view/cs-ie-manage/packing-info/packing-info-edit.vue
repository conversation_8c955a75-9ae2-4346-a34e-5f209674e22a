<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="138"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { editStatus, productClassify } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'packingInfoEdit',
    mixins: [baseDetailConfig],
    props: {
      /**
       * 传入的数据源
       */
      headId: {
        type: String,
        required: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.csImportExport.packingInfo.insert,
          update: csAPI.csImportExport.packingInfo.update,
          getData: csAPI.csMaterielCenter.packingConfig.getData
        },
        buttons: [
          {...btnComm, label: '保存', command: 'save', click: this.handleSave},
          {...btnComm, label: '保存关闭', command: 'save-close', click: this.handleSaveClose},
          {...btnComm, label: '保存继续', command: 'save-continue', click: this.handleSaveContinue},
          {...btnComm, label: '返回', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.fieldsReset()
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save-close')].needed = !me.showDisable
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save-continue')].needed = !me.showDisable
        }
      }
    },
    methods: {
      getFields() {
        return [{
          isCard: true,
          title: '基础信息',
          key: '1212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          props: {
            maxlength: 30
          },
          key: 'orderNo',
          title: '订单号'
        }, {
          props: {
            maxlength: 30
          },
          title: '客户订单号',
          key: 'customerOrderNo'
        }, {
          key: 'gmark',
          type: 'select',
          required: true,
          title: '物料类型',
          defaultValue: 'E',
          props: {
            options: productClassify.GMARK_MRP_SELECT
          }
        }, {
          props: {
            maxlength: 50
          },
          key: 'facGNo',
          required: true,
          title: '企业料号'
        }, {
          props: {
            maxlength: 50
          },
          title: '客户料号',
          key: 'customerGNo'
        }, {
          props: {
            maxlength: 20
          },
          required: true,
          key: 'cartonNo',
          title: '企业包装方式'
        }, {
          key: 'qty',
          props: {
            intDigits: 11,
            precision: 5
          },
          title: '数量',
          required: true,
          type: 'xdoInput'
        }, {
          key: 'unit',
          type: 'pcode',
          props: {
            meta: 'UNIT'
          },
          required: true,
          title: '计量单位'
        }, {
          key: 'gname',
          title: '货物名称',
          props: {
            maxlength: 255
          }
        }, {
          key: 'cartonSize',
          title: '基础包装尺寸(cm)',                  // 每箱尺寸(长*宽*高) cm
          type: 'group_form_line',
          fields: [{
            title: '',
            key: 'cartonL',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'cartonW',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'cartonH',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }]
        }, {
          key: 'palletSize',
          title: '托盘尺寸(cm)',
          type: 'group_form_line',
          fields: [{
            title: '',
            key: 'palletL',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'palletW',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'palletH',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }]
        }, {
          key: 'palletQty',
          props: {
            intDigits: 10,
            precision: 0,
            disabled: true
          },
          type: 'xdoInput',
          title: '基础包装数量'
        }, {
          key: 'volume',
          props: {
            disabled: true
          },
          title: '基础包装体积(m³)'
        }, {
          key: 'cartonWt',
          props: {
            disabled: true
          },
          title: '基础包装物重量(kg)'
        }, {
          key: 'cartonQty',
          title: '基础包装物容量',
          type: 'group_form_line',
          fields: [{
            title: '',
            props: {
              disabled: true
            },
            labelWidth: 0,
            key: 'cartonQty'
          }, {
            title: '',
            type: 'pcode',
            key: 'cartonUnit',
            props: {
              meta: 'UNIT',
              disabled: true
            }
          }]
        }, {
          key: 'netWt',
          title: '净重',
          props: {
            intDigits: 10,
            precision: 5,
            disabled: true
          },
          type: 'xdoInput'
        }, {
          props: {
            intDigits: 5,
            precision: 0,
            disabled: true
          },
          type: 'xdoInput',
          title: '最高层数',
          key: 'maxLayers'
        }, {
          props: {
            intDigits: 5,
            precision: 0,
            disabled: true
          },
          type: 'xdoInput',
          title: '每层数量',
          key: 'layersNum'
        }, {
          isCard: true,
          title: '包装详情',
          key: '2212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '包装',
          key: '3241234134',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          props: {
            intDigits: 3,
            precision: 0,
            disabled: true
          },
          key: 'cartonNum',
          type: 'xdoInput',
          title: '基础包装数量'
        }, {
          props: {
            intDigits: 10,
            precision: 5,
            disabled: true
          },
          key: 'totalCartonWt',
          title: '基础包装物总重量(kg)'
        }, {
          title: '托盘数',
          props: {
            intDigits: 12,
            precision: 0,
            disabled: true
          },
          key: 'totalPalletNum',
          type: 'xdoInput'
        }, {
          key: 'totalPalletWt',
          props: {
            intDigits: 10,
            precision: 5,
            disabled: true
          },
          type: 'xdoInput',
          title: '托盘总重量'
        }, {
          title: '毛重',
          key: 'grossWt',
          props: {
            intDigits: 10,
            precision: 5,
            disabled: true
          },
          type: 'xdoInput'
        }, {
          props: {
            intDigits: 6,
            precision: 6,
            disabled: true
          },
          type: 'xdoInput',
          key: 'cartonVolume',
          title: '货物体积(m³)'
        }, {
          props: {
            intDigits: 6,
            precision: 6,
            disabled: true
          },
          type: 'xdoInput',
          key: 'totalVolume',
          title: '含托体积(m³)'
        }, {
          title: '整托',
          key: '3241234134',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          props: {
            disabled: true
          },
          key: 'cartonQty',
          type: 'xdoInput',
          title: '整箱基础包装物容量'
        }, {
          props: {
            disabled: true
          },
          key: 'palletQty',
          type: 'xdoInput',
          title: '整托箱数'
        }, {
          props: {
            disabled: true
          },
          key: 'palletNum',
          type: 'xdoInput',
          title: '整托数量'
        }, {
          title: '散托',
          key: '2452435242',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          props: {
            disabled: true
          },
          type: 'xdoInput',
          key: 'cartonQty',
          title: '整箱基础包装物容量'
        }, {
          props: {
            disabled: true
          },
          type: 'xdoInput',
          title: '整箱箱数',
          key: 'totalCartonNum'
        }, {
          props: {
            disabled: true
          },
          type: 'xdoInput',
          title: '散箱基础包装物容量',
          key: 'bciLclCartonQty'
        }, {
          props: {
            disabled: true
          },
          key: 'bcilclQty',
          type: 'xdoInput',
          title: '散箱箱数'
        }, {
          key: 'bciQty',
          props: {
            disabled: true
          },
          type: 'xdoInput',
          title: '散托数量'
        }, {
          isCard: true,
          title: '托盘详情',
          key: '3212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '整托',
          key: '45345343456',
          class: 'dc-merge-1-3',
          type: 'group_form_line',
          fields: [{
            title: '',
            key: 'totalPalletL',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'totalPalletW',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'totalPalletH',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'palletNum',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }]
        }, {
          title: '散托',
          key: '56467456756',
          class: 'dc-merge-1-3',
          type: 'group_form_line',
          fields: [{
            title: '',
            key: 'totalPalletL',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'totalPalletW',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'bciH',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }, {
            title: '',
            key: 'bciQty',
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5,
              disabled: true
            },
            type: 'xdoInput'
          }]
        }]
      },
      clearDisableData() {
        let me = this
        me.$set(me.detailConfig.model, 'cartonL', null)
        me.$set(me.detailConfig.model, 'cartonW', null)
        me.$set(me.detailConfig.model, 'cartonH', null)
        me.$set(me.detailConfig.model, 'volume', null)
        me.$set(me.detailConfig.model, 'cartonQty', null)
        me.$set(me.detailConfig.model, 'cartonUnit', '')
      },
      resetDisableData() {
        let me = this,
          gMark = me.detailConfig.model.gmark,
          facGNo = me.detailConfig.model.facGNo,
          cartonNo = me.detailConfig.model.cartonNo
        if (isNullOrEmpty(gMark) && isNullOrEmpty(facGNo) && isNullOrEmpty(cartonNo)) {
          me.clearDisableData()
        } else {
          me.$http.post(me.ajaxUrl.getData, {
            gmark: gMark,
            facGNo: facGNo,
            cartonNo: cartonNo
          }).then(res => {
            if (res.data.data) {
              if (isNumber(res.data.data['cartonL'])) {
                me.$set(me.detailConfig.model, 'cartonL', res.data.data['cartonL'])
              } else {
                me.$set(me.detailConfig.model, 'cartonL', null)
              }
              if (isNumber(res.data.data['cartonW'])) {
                me.$set(me.detailConfig.model, 'cartonW', res.data.data['cartonW'])
              } else {
                me.$set(me.detailConfig.model, 'cartonW', null)
              }
              if (isNumber(res.data.data['cartonH'])) {
                me.$set(me.detailConfig.model, 'cartonH', res.data.data['cartonH'])
              } else {
                me.$set(me.detailConfig.model, 'cartonH', null)
              }
              if (isNumber(res.data.data['volume'])) {
                me.$set(me.detailConfig.model, 'volume', res.data.data['volume'])
              } else {
                me.$set(me.detailConfig.model, 'volume', null)
              }
              if (isNumber(res.data.data['cartonQty'])) {
                me.$set(me.detailConfig.model, 'cartonQty', res.data.data['cartonQty'])
              } else {
                me.$set(me.detailConfig.model, 'cartonQty', null)
              }
              if (!isNullOrEmpty(res.data.data['cartonUnit'])) {
                me.$set(me.detailConfig.model, 'cartonUnit', res.data.data['cartonUnit'])
              } else {
                me.$set(me.detailConfig.model, 'cartonUnit', '')
              }
            } else {
              me.clearDisableData()
            }
          }).catch(() => {
            me.clearDisableData()
          })
        }
      },
      beforeSave() {
        let me = this
        if (me.editConfig.editStatus === editStatus.ADD) {
          me.$set(me.detailConfig.model, 'status', '0')
          me.$set(me.detailConfig.model, 'headId', me.headId)
        }
      },
      handleSave() {
        let me = this
        me.beforeSave()
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      },
      handleSaveClose() {
        let me = this
        me.beforeSave()
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.EDIT, res.data.data)
        })
      },
      handleSaveContinue() {
        let me = this
        me.beforeSave()
        me.doSave(() => {
          me.refreshIncomingData(false, editStatus.ADD, {})
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
