<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" :disable="grdDisable"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <PackingInfoEdit v-if="!showList" :edit-config="editConfig" :head-id="headId"
                     @onEditBack="editBack" :in-source="dynamicSource"></PackingInfoEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ImportPage :importShow.sync="importShow" :importKey="importConfig.importKey" :importConfig="importConfig.Config"
                @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { packingInfoList } from './js/packingInfoList'

  export default {
    name: 'packingInfoList',
    mixins: [packingInfoList],
    data() {
      return {
        totalContent: '',
        listConfig: {
          exportTitle: '箱单信息'
        },
        ajaxUrl: {
          total: csAPI.csImportExport.packingInfo.total,
          deleteUrl: csAPI.csImportExport.packingInfo.delete,
          exportUrl: csAPI.csImportExport.packingInfo.exportUrl,
          checkByNo: csAPI.csImportExport.packingInfo.checkByNo,
          totalCheck: csAPI.csImportExport.packingInfo.totalCheck,
          selectAllPaged: csAPI.csImportExport.packingInfo.selectAllPaged,
          exportPackDetailUrl: csAPI.csImportExport.packingInfo.exportPackDetailUrl
        }
      }
    },
    methods: {
      afterSearchSuccess() {
        let me = this
        me.$http.post(me.ajaxUrl.total, {
          headId: me.headId
        }).then(res => {
          me.$set(me, 'totalContent', res.data.data)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
