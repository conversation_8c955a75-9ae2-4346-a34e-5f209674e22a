import ImportPage from 'xdo-import'
import PackingInfoEdit from '../packing-info-edit'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
import { editStatus, importExportManage, productClassify } from '../../../cs-common'
import { getHttpHeaderFileName, blobSaveFile, isNullOrEmpty, isNumber } from '@/libs/util'

export const packingInfoList = {
  name: 'packingInfoList',
  mixins: [baseSearchConfig, baseListConfig, dynamicImport],
  components: {
    ImportPage,
    PackingInfoEdit
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1', ''].includes(value)
      }
    }
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      listConfig: {
        colOptions: true
      },
      importShow: false,
      hasChildTabs: true,
      pmsLevel: 'packing',
      cmbSource: {
        gmark: productClassify.GMARK_SELECT,
        status: importExportManage.PACKING_INFO_STATUS_MAP
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload,
        'check-by-no': this.handleCheckByNo,
        'total-check': this.handleTotalCheck,
        'setting': this.handleTableColumnSetup,
        'pack-detail-export': this.handlePackDetailDownload
      }
    }
  },
  computed: {
    headId() {
      return this.parentConfig.editData.sid
    },
    importConfig() {
      let me = this,
        commConfig = me.getCommImportConfig('PACKING-E-LIST', {
          headId: me.headId
        }, me.headId)
      return {
        importKey: 'packingEList',
        Config: commConfig
      }
    },
    extendParams() {
      return {
        headId: this.headId
      }
    },
    grdDisable() {
      return this.parentConfig.editStatus === editStatus.SHOW
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      if (me.grdDisable) {
        me.actions = me.actions.filter(item => ['export', 'setting'].includes(item.command))
      }
    },
    getParams() {
      return [{
        title: '状态',
        key: 'status',
        type: 'select'
      }, {
        key: 'orderNo',
        title: '订单号'
      }, {
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        key: 'gmark',
        type: 'select',
        title: '物料类型'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        title: '客户料号',
        key: 'customerGNo'
      }, {
        key: 'cartonNo',
        title: '企业包装方式'
      }]
    },
    getFields() {
      return [{
        width: 100,
        title: '状态',
        key: 'status',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.status)
        }
      }, {
        width: 150,
        key: 'orderNo',
        title: '订单号'
      }, {
        width: 150,
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, productClassify.GMARK_SELECT)
        }
      }, {
        width: 180,
        key: 'facGNo',
        toolTip: true,
        title: '企业料号'
      }, {
        width: 150,
        title: '客户料号',
        key: 'customerGNo'
      }, {
        width: 200,
        key: 'gName',
        toolTip: true,
        title: '货物名称'
      }, {
        width: 160,
        toolTip: true,
        key: 'cartonNo',
        title: '企业包装方式'
      }, {
        key: 'qty',
        width: 160,
        title: '数量',
        toolTip: true
      }, {
        width: 100,
        key: 'unit',
        title: '计量单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 160,
        key: 'cartonL',
        title: '长度'
      }, {
        width: 160,
        key: 'cartonW',
        title: '宽度'
      }, {
        width: 160,
        key: 'cartonH',
        title: '高度'
      }, {
        width: 150,
        key: 'volume',
        title: '包装体积(m³)'
      }, {
        width: 150,
        key: 'totalCartonWt',
        title: '包装物总重量(kg)'
      }, {
        width: 150,
        key: 'cartonQty',
        title: '包装物容量/单位',
        render: (h, params) => {
          let me = this,
            cartonQty = params.row['cartonQty'],
            cartonUnit = params.row['cartonUnit'],
            unitName = ''
          if (!isNumber(cartonQty)) {
            cartonQty = ''
          }
          if (!isNullOrEmpty(cartonUnit)) {
            unitName = me.pcodeGet('UNIT', cartonUnit)
          }
          return h('span', {}, String(cartonQty) + ' ' + unitName)
        }
      }, {
        width: 150,
        title: '托盘数',
        key: 'totalPalletNum'
      }, {
        width: 160,
        title: '含托体积(m³)',
        key: 'totalVolume'
      }, {
        width: 160,
        key: 'netWt',
        title: '净重',
        toolTip: true
      }, {
        width: 160,
        key: 'grossWt',
        title: '毛重'
      },{
        width: 200,
        key: 'remark',
        title: '备注',
        toolTip: true
      }, {
        width: 130,
        title: '托盘总重量',
        key: 'totalPalletWt'
      }, {
        width: 130,
        key: 'cartonNum',
        title: '基础包装数量'
      }, {
        width: 130,
        title: '最高层数',
        key: 'maxLayers'
      }, {
        width: 130,
        title: '每层数量',
        key: 'layersNum'
      }, {
        width: 130,
        title: '包装总高',
        key: 'totalCartonh'
      }, {
        width: 156,
        key: 'palletQty',
        title: '箱数/托盘(托盘装载量)'
      }, {
        width: 180,
        toolTip: true,
        title: '整箱详情',
        key: 'totalRemark'
      }, {
        width: 180,
        toolTip: true,
        title: '托盘详情',
        key: 'palletRemark'
      }]
    },
    /**
     * 弹出列配置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导入
     */
    handleImport() {
      let me = this
      me.$set(me, 'importShow', true)
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.$set(me, 'importShow', false)
      me.getList()
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 总量校验
     */
    handleTotalCheck() {
      let me = this
      me.setButtonLoading('total-check', true)
      me.$http.post(`${me.ajaxUrl.totalCheck}/${me.headId}`).then(() => {
        me.$Message.success('总量校验成功!')
        me.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        me.setButtonLoading('total-check', false)
      })
    },
    /**
     * 按料号校验
     */
    handleCheckByNo() {
      let me = this
      me.setButtonLoading('check-by-no', true)
      me.$http.post(`${me.ajaxUrl.checkByNo}/${me.headId}`).then(() => {
        me.$Message.success('按料号校验成功!')
        me.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        me.setButtonLoading('check-by-no', false)
      })
    },
    /**
     * 装箱明细查看
     */
    handlePackDetailDownload() {
      let me = this
      me.setButtonLoading('pack-detail-export', true)
      const param = new FormData()
      me.$http.post(`${me.ajaxUrl.exportPackDetailUrl}/${me.headId}`, param, {
        responseType: 'blob'
      }).then(res => {
        const filename = getHttpHeaderFileName(res.headers)
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'})
        blobSaveFile(blob, filename)
      }).catch(() => {
      }).finally(() => {
        me.setButtonLoading('pack-detail-export', false)
      })
    }
  }
}
