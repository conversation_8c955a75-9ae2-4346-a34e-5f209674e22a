import { namespace } from '@/project'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
import { deepClone, getComponentBy<PERSON>ey } from '@/components/dynamic/jsx/utils/element-creater'

/**
 * 动态提单表头默认值
 * @type {{name: string}}
 */
export const dynamicDefault = {
  name: 'dynamicDefault',
  mixins: [baseDetailConfig],
  props: {
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1', ''].includes(value)
      }
    }
  },
  data() {
    return {
      // 字段配置相关
      fieldsConfig: {
        fields: [],
        decId: '',
        billId: '',
        otherId: '',
        configId: '',
        popShow: false,
        decFields_all: [],
        decFields_show: [],
        otherFields_all: [],
        otherFields_show: [],
        billFields_all: [],
        billFields_show: [],
        listFields_all: [],
        fixedFields_all: []
      },
      detailConfig: {
        autoLoad: false
      },
      listCollapse: false,
      dynamicInfo: {
        required: [],      // 可变必输项
        preRequired: []
      },
      tempSource: {
        data: {},
        valid: false
      },
      beMounted: false
    }
  },
  created: function () {
    let me = this
    me.loadFieldConfig()
  },
  mounted: function () {
    let me = this
    me.$nextTick(() => {
      me.$nextTick(() => {
        me.doItemCollapse()
      })
    })
  },
  watch: {
    'dynamicInfo.required': {
      handler: function (required) {
        let me = this
        me.handleUpdateFields([])
        me.$nextTick(() => {
          if (required.includes('feeMark')) {
            if (me.$refs['fiFeeMark']) {
              me.$refs['fiFeeMark'].setRules(true)
            }
          }
          if (required.includes('insurMark')) {
            if (me.$refs['fiInsurMark']) {
              me.$refs['fiInsurMark'].setRules(true)
            }
          }
        })
      }
    },
    'dynamicInfo.preRequired': {
      handler: function () {
        this.handleUpdateFields([])
      }
    },
    'fieldsConfig.popShow': {
      handler: function (popShow) {
        let me = this
        if (popShow === false) {
          me.$nextTick(() => {
            me.$set(me.fieldsConfig, 'configId', '')
          })
        }
      }
    },
    loadIfCheck: {
      handler: function () {
        let me = this
        me.handleUpdateFields()
      }
    }
  },
  computed: {
    loadIfCheck() {
      let me = this
      if (me.ieMark === 'I') {
        if (me.bondMark === '1' && me.detailConfig.model.entryType === 'X') {
          return true
        }
        if (isNullOrEmpty(me.bondMark) && me.detailConfig.model.entryType === 'X' && isNullOrEmpty(me.detailConfig.model.emsNo)) {
          return true
        }
      }
      return false
    },
    /**
     * 通关业务设置
     */
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    /**
     * 界面每行展示组件数【待修改--通关业务设置添加配置信息？】
     * @returns {number}
     */
    fromColumns() {
      if (this.configData.columnCount === '4') {
        return 4
      }
      return 3
    },
    fromClass() {
      let me = this
      if (me.fromColumns === 4) {
        return 'dc-form-4'
      }
      return ''
    },
    splitLineClass() {
      let me = this
      if (me.fromColumns === 4) {
        return 'dc-merge-1-5'
      }
      return 'dc-merge-1-4'
    },
    districtClass() {
      let me = this
      if (me.fromColumns === 4) {
        return 'dc-merge-3-5'
      }
      return 'dc-merge-2-4'
    },
    emsListNoClass4E() {
      let me = this
      if (me.fromColumns === 4) {
        return 'dc-merge-1-4 hasSlot'
      }
      return 'dc-merge-1-3 hasSlot'
    },
    bondedNoteClass4E() {
      let me = this
      if (me.fromColumns === 4) {
        return 'dc-merge-1-5'
      }
      return 'dc-merge-1-4'
    }
  },
  methods: {
    /**
     * 数据加载完成后执行
     * 设置默认值
     * @param isNew
     * @param used
     */
    afterModelLoaded(isNew, used = false) {
      let me = this
      if (used !== true) {
        return
      }
      if (isNew) {
        if (me.ieMark === 'I') {
          if (me.bondMark === '0') {
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'dclcusMark', '1')
            me.$set(me.detailConfig.model, 'dclcusType', '2')
            me.$set(me.detailConfig.model, 'bondMark', '0')
            me.$set(me.detailConfig.model, 'entryType', '1')
            me.$set(me.detailConfig.model, 'billListType', '0')
            me.$set(me.detailConfig.model, 'emsNo', me.$store.getters[`${namespace}/defaultEmsNo`])
            me.$set(me.detailConfig.model, 'tradeMode', me.$store.getters[`${namespace}/defaultTradeMode`])
          } else if (me.bondMark === '1') {
            me.$set(me.detailConfig.model, 'bondMark', '1')
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'entryType', '0')
            me.$set(me.detailConfig.model, 'entryClearanceType', 'M')
            // me.$set(me.detailConfig.model, 'dclcusMark', '1')
            // me.$set(me.detailConfig.model, 'dclcusType', '2')
          } else {
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'entryType', '1')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'decType', '0')
            me.$set(me.detailConfig.model, 'dclcusMark', '1')
            me.$set(me.detailConfig.model, 'dclcusType', '2')
            me.$set(me.detailConfig.model, 'billListType', '0')
            me.$set(me.detailConfig.model, 'emsNo', me.$store.getters[`${namespace}/defaultEmsNo`])
            me.$set(me.detailConfig.model, 'tradeMode', me.$store.getters[`${namespace}/defaultTradeMode`])
          }
          me.$set(me.detailConfig.model, 'goodsCategory', '0')
        } else {
          if (me.bondMark === '0') {
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'dclcusMark', '1')
            me.$set(me.detailConfig.model, 'dclcusType', '2')
            me.$set(me.detailConfig.model, 'bondMark', '0')
            me.$set(me.detailConfig.model, 'entryType', '2')
            me.$set(me.detailConfig.model, 'billListType', '0')
            me.$set(me.detailConfig.model, 'emsNo', me.$store.getters[`${namespace}/defaultEmsNo`])
            me.$set(me.detailConfig.model, 'tradeMode', me.$store.getters[`${namespace}/defaultTradeMode`])
          } else if (me.bondMark === '1') {
            me.$set(me.detailConfig.model, 'bondMark', '1')
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'entryClearanceType', 'M')
            // me.$set(me.detailConfig.model, 'dclcusMark', '1')
            // me.$set(me.detailConfig.model, 'dclcusType', '2')
            // me.$set(me.detailConfig.model, 'entryType', '2')
          } else {
            me.$set(me.detailConfig.model, 'billType', '1')
            me.$set(me.detailConfig.model, 'entryType', '2')
            me.$set(me.detailConfig.model, 'mergeType', '0')
            me.$set(me.detailConfig.model, 'decType', '0')
            me.$set(me.detailConfig.model, 'dclcusMark', '1')
            me.$set(me.detailConfig.model, 'dclcusType', '2')
            me.$set(me.detailConfig.model, 'billListType', '0')
            me.$set(me.detailConfig.model, 'emsNo', me.$store.getters[`${namespace}/defaultEmsNo`])
            me.$set(me.detailConfig.model, 'tradeMode', me.$store.getters[`${namespace}/defaultTradeMode`])
          }
        }

        me.$set(me.detailConfig.model, 'agentCode', me.$store.state.user.company)
        me.$set(me.detailConfig.model, 'receiveCode', me.$store.state.user.company)
        me.$set(me.detailConfig.model, 'receiveName', me.$store.state.user.companyName)
        me.agentCodeEnter()
        me.$set(me.detailConfig.model, 'ownerCreditCode', me.$store.state.user.socialCreditCode)
        me.onOwnerCodeEnter()
        if (isNullOrEmpty(me.detailConfig.model.ownerCode)) {
          me.$set(me.detailConfig.model, 'ownerCode', me.$store.state.user.company)
        }
        if (isNullOrEmpty(me.detailConfig.model.ownerName)) {
          me.$set(me.detailConfig.model, 'ownerName', me.$store.state.user.companyName)
        }
        me.feeDisabled = false
        me.insurDisabled = false
        me.cutModeGMarkCtrl(me.detailConfig.model.emsNo, me.detailConfig.model.tradeMode)

      } else {
        me.isLoadEdit = true
        if (me.editConfig.editStatus === editStatus.EDIT) {
          me.setFeeRules('fee', me.detailConfig.model.feeMark)
          me.setFeeRules('insur', me.detailConfig.model.insurMark)
          me.setFeeRules('other', me.detailConfig.model.otherMark)
          // 锁定时不能编辑
          if (me.editConfig.editData.status === '1') {
            me.buttons.forEach(button => {
              if (['save', 'saveTemp'].includes(button.key)) {
                button.needed = false
              }
            })
          }
          me.feeDisabled = false
          me.insurDisabled = false

          me.tradeModeChange(me.editConfig.editData.tradeMode)
          me.onTransactionChangeBase(me.detailConfig.model.transMode, me.configRequiredField)
        } else if (me.editConfig.editStatus === editStatus.SHOW) {
          me.feeDisabled = true
          me.insurDisabled = true
          me.buttons.forEach(button => {
            if (!['back'].includes(button.key)) {
              button.needed = false
            }
          })
        } else {
          console.error('缺失编辑信息!')
        }
        setTimeout(() => {
          me.isLoadEdit = false
          me.$set(me.detailConfig.model, 'cutMode', me.editConfig.editData.cutMode)
          me.$set(me.detailConfig.model, 'overseasShipperAeo', me.editConfig.editData.overseasShipperAeo)
          me.$nextTick(() => {
            me.setFeeRequired(me.detailConfig.model.feeMark, 'feeRate', 'feeCurr')
            me.setFeeRequired(me.detailConfig.model.insurMark, 'insurRate', 'insurCurr')
            me.setFeeRequired(me.detailConfig.model.otherMark, 'otherRate', 'otherCurr')
          })
        }, 500)
      }

      if (me.detailConfig.model.dclcusMark === '2') {
        me.$nextTick(() => {
          me.setDisable('dclcusType', true)
          me.setDisable('entryType', true)
        })
      }

      me.afterModelLoadedExtend()
    },
    /**
     * 界面字段、规则重置后执行的事件
     */
    afterFieldsReset() {
      let me = this,
        relEmsNoRules = [],
        validateEmsNo = (rule, value, callback) => {
          if (!isNullOrEmpty(value) && value.trim() === me.detailConfig.model.emsNo) {
            callback(new Error('不能和备案号相同!'))
          } else {
            callback()
          }
        }
      if (me.detailConfig.rules.hasOwnProperty('relEmsNo')) {
        relEmsNoRules = deepClone(me.detailConfig.rules['relEmsNo'])
        relEmsNoRules.push({validator: validateEmsNo, trigger: 'blur'})
      } else {
        relEmsNoRules = [{validator: validateEmsNo, trigger: 'blur'}]
      }
      me.detailConfig.rules['relEmsNo'] = relEmsNoRules
    },
    /**
     * 设置扩展字段
     * @returns {{}}
     */
    getExtendFields() {
      return {
        curr: '',
        emsListNo: '',
        // feeMark: '',
        // feeRate: null,
        // feeCurr: '',
        // insurMark: '',
        // insurRate: null,
        // insurCurr: '',
        // otherMark: '',
        // otherRate: null,
        // otherCurr: '',
        ownerCode: '',
        declareCreditCode: ''
      }
    },
    extendRules() {
      let me = this,
        requires = me.dynamicInfo.required,
        result = {
          feeRate: [{required: false, type: 'number', message: '不能为空!'}],
          feeCurr: [{required: false, message: '不能为空!'}],
          insurRate: [{required: false, type: 'number', message: '不能为空!'}],
          insurCurr: [{required: false, message: '不能为空!'}],
          otherRate: [{required: false, type: 'number', message: '不能为空!'}],
          otherCurr: [{required: false, message: '不能为空!'}]
        }
      Object.keys(result).forEach(key => {
        result[key][0].required = requires.includes(key)
      })
      return result
    },
    /**
     * 获取from配置的键值
     */
    setFormResId() {
      let me = this,
        baseId = 'decHead_' + me.ieMark + '_' + me.$store.state.user.company,
        decId = baseId + '_' + me.bondMark,
        billId = baseId + '_bill_' + me.bondMark,
        otherId = baseId + '_other_' + me.bondMark

      me.$set(me.fieldsConfig, 'decId', decId)
      me.$set(me.fieldsConfig, 'billId', billId)
      me.$set(me.fieldsConfig, 'otherId', otherId)
    },
    /**
     * 获取报关单字段【可配置】
     * @param allFields
     */
    getCustomsDecFields(allFields) {
      let me = this,
        decIBonded = ['masterCustoms', {key: 'iedate', title: '进口日期'}, 'startShipmentDate',
          {key: 'overseasShipper', title: '境外发货人代码'}, {
            key: 'overseasShipperName',
            title: '境外发货人名称'
          }, {key: 'overseasShipperAeo', title: '境外发货人代码(AEO)'},
          'trafName', 'voyageNo', 'mawb', 'hawb', 'warehouse', 'licenseNo', 'despPort', 'contrNo', 'tradeNation', 'destPort',
          {key: 'entryPort', title: '入境口岸'}, 'grossWt', 'netWt', 'volume', 'transMode', 'feeMark', 'insurMark',
          'wrapType', 'packNum', /*'mergeType',*/ 'promiseItems','preventiveDisinfection'],
        decEBonded = ['masterCustoms', {key: 'iedate', title: '出口日期'},
          {key: 'overseasShipper', title: '境外收货人代码'}, {
            key: 'overseasShipperName',
            title: '境外收货人名称'
          }, {key: 'overseasShipperAeo', title: '境外收货人代码(AEO)'},
          'trafName', 'voyageNo', 'mawb', 'hawb', 'contrNo', 'tradeNation', 'licenseNo', {
            key: 'destPort',
            title: '指运港'
          },
          {key: 'entryPort', title: '离境口岸'}, 'wrapType', 'packNum', 'grossWt', 'netWt', 'volume',
          'transMode', 'feeMark', 'insurMark',
          /*'mergeType',*/ 'otherMark', {key: 'destinationCountry', title: '最终目的国'}, 'promiseItems'],
        decINonBonded = [{key: 'ieport', title: '进境关别'}, {key: 'iedate', title: '进口日期'}, {key: 'startShipmentDate', title: '启运日期'},
          {key: 'overseasShipper', title: '境外发货人代码'}, {
            key: 'overseasShipperName',
            title: '境外发货人名称'
          }, {key: 'overseasShipperAeo', title: '境外发货人代码(AEO)'},
          'trafMode', 'trafName', 'voyageNo', 'mawb', 'hawb', 'tradeMode', 'cutMode', 'warehouse',
          'licenseNo', 'despPort', 'contrNo', 'tradeNation', 'tradeCountry', {key: 'destPort', title: '经停港'},
          {key: 'entryPort', title: '入境口岸'}, 'wrapType', 'packNum', 'grossWt', 'netWt', 'volume',
          'transMode', 'feeMark', 'insurMark', 'mergeType', 'exemptsNo', 'note', 'declTrnrel',/*'dclcusMark', 'dclcusType',*/
          {
            key: 'entryType', title: '两步申报',
            props: {
              optionLabelRender: (opt) => opt.label
            }
          }, 'entryClearanceType', 'promiseItems', 'preventiveDisinfection'],
        decENonBonded = [{key: 'ieport', title: '出境关别'}, {key: 'iedate', title: '出口日期'},
          {key: 'overseasShipper', title: '境外收货人代码'}, {
            key: 'overseasShipperName',
            title: '境外收货人名称'
          }, {key: 'overseasShipperAeo', title: '境外收货人代码(AEO)'},
          'trafMode', 'trafName', 'voyageNo', 'mawb', 'hawb', 'tradeMode', 'cutMode', 'mergeType',
          'licenseNo', {key: 'entryPort', title: '离境口岸'}, 'contrNo',
          'tradeNation', {key: 'tradeCountry', title: '运抵国(地区)'}, {key: 'destPort', title: '指运港'},
          'transMode', 'wrapType', 'packNum', 'grossWt', 'netWt', 'volume',
          'feeMark', 'insurMark', 'otherMark', 'declTrnrel',/*'dclcusMark', 'dclcusType', 'entryType',*/
          'entryClearanceType','promiseItems', {key: 'note', itemClass: 'dc-merge-1-4'}],
        cusDecs = []
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          cusDecs = decIBonded
        } else if (me.bondMark === '1') {
          cusDecs = decINonBonded
        } else {
          cusDecs = decIBonded
        }
      } else {
        if (me.bondMark === '0') {
          cusDecs = decEBonded
        } else if (me.bondMark === '1') {
          cusDecs = decENonBonded
        } else {
          cusDecs = decEBonded
        }
      }
      return me.configIntegration(cusDecs, allFields)
    },
    /**
     * 获取可配置字段的默认配置信息
     * @returns {*[]}
     */
    getDefaultFields() {
      let me = this,
        decIBonded = ['masterCustoms', 'mawb', 'hawb', 'overseasShipper', 'overseasShipperName', 'grossWt', 'netWt', 'volume',
          'transMode', 'feeMark', 'insurMark', 'wrapType', 'packNum'/*, 'mergeType'*/],
        decEBonded = ['masterCustoms', 'mawb', 'hawb', 'overseasShipper', 'overseasShipperName', 'grossWt', 'netWt', 'volume',
          'transMode', 'feeMark', 'insurMark', 'wrapType', 'packNum'/*, 'mergeType'*/],
        decINonBonded = ['trafMode', 'mawb', 'hawb', 'overseasShipper', 'overseasShipperName', 'tradeMode', 'cutMode', 'contrNo',
          'grossWt', 'netWt', 'volume', 'transMode', 'feeMark', 'insurMark', 'wrapType', 'packNum',
          'mergeType', 'promiseItems', 'note'],
        decENonBonded = ['trafMode', 'mawb', 'hawb', 'overseasShipper', 'overseasShipperName', 'tradeMode', 'cutMode', 'contrNo',
          'grossWt', 'netWt', 'volume', 'transMode', 'feeMark', 'insurMark', 'wrapType', 'packNum',
          'mergeType', 'promiseItems', 'note'],
        defaults = []
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          defaults = decIBonded
        } else if (me.bondMark === '1') {
          defaults = decINonBonded
        } else {
          defaults = decIBonded
        }
      } else {
        if (me.bondMark === '0') {
          defaults = decEBonded
        } else if (me.bondMark === '1') {
          defaults = decENonBonded
        } else {
          defaults = decEBonded
        }
      }
      return [...defaults]
    },
    /**
     * 加载字段信息
     */
    loadFieldConfig() {
      let me = this,
        allFields = me.getFullFields(),                         // 所有字段
        defaultFields = me.getDefaultFields(),                  // 报关单默认字段
        listFields = me.getListFields(allFields),               // 清单固定字段
        fixedFields = me.getFixedFields(allFields),             // 固定通用字段
        otherFields_all = me.getOtherFields(allFields),         // 其他(可编辑)字段
        decFields_all = me.getCustomsDecFields(allFields),      // 报关单(可编辑)字段
        billFields_all = me.getConfigBillFields(allFields),     // 清单(可编辑)字段
        defaultBillFields = me.getDefaultBillFields()           // 清单默认字段

      me.setFormResId()

      me.$set(me.fieldsConfig, 'listFields_all', listFields)
      me.$set(me.fieldsConfig, 'fixedFields_all', fixedFields)

      me.$set(me.fieldsConfig, 'decFields_all', decFields_all)
      me.$set(me.fieldsConfig, 'billFields_all', billFields_all)
      me.$set(me.fieldsConfig, 'otherFields_all', otherFields_all)

      const promiseDec = me.$bom3.companyCustom('form', me.fieldsConfig.decId, decFields_all, defaultFields)
      const promiseBill = me.$bom3.companyCustom('form', me.fieldsConfig.billId, billFields_all, defaultBillFields)
      const promiseOther = me.$bom3.companyCustom('form', me.fieldsConfig.otherId, otherFields_all, [])
      Promise.all([promiseDec, promiseBill, promiseOther]).then((values) => {
        me.$set(me.fieldsConfig, 'decFields_show', values[0])
        me.$set(me.fieldsConfig, 'billFields_show', values[1])
        me.$set(me.fieldsConfig, 'otherFields_show', values[2])
        /**
         * --== 用于测试 Start ==--
         */
        let decFieldsShow = values[0].map(item => {
            return item.key
          }),
          billFieldsShow = values[1].map(item => {
            return item.key
          }),
          otherFieldsShow = values[2].map(item => {
            return item.key
          })
        console.info('decId: ' + me.fieldsConfig.decId)
        console.info('decFields: ' + decFieldsShow.toString())
        console.info('billId: ' + me.fieldsConfig.billId)
        console.info('billFields: ' + billFieldsShow.toString())
        console.info('otherId: ' + me.fieldsConfig.otherId)
        console.info('otherFields: ' + otherFieldsShow.toString())
        /**
         * --== 用于测试 End ==--
         */
        me.handleUpdateFields()
        me.loadTemplateSelected()
        me.$nextTick(() => {
          if (me.editConfig.editStatus === editStatus.ADD) {
            me.afterModelLoaded(true, true)
          } else {
            me.afterModelLoaded(false, true)
          }
          console.info('加载字段信息: loadFieldConfig')
          me.$set(me, 'beMounted', true)
        })
      })
    },
    /**
     * 显示设置界面(清单单部分)
     */
    showBillConfigPop() {
      let me = this
      me.$set(me.fieldsConfig, 'configId', me.fieldsConfig.billId)
      me.$set(me.fieldsConfig, 'fields', me.fieldsConfig.billFields_all)
      me.$set(me.fieldsConfig, 'popShow', true)
    },
    /**
     * 显示设置界面(报关单部分)
     */
    showDecConfigPop() {
      let me = this
      me.$set(me.fieldsConfig, 'configId', me.fieldsConfig.decId)
      me.$set(me.fieldsConfig, 'fields', me.fieldsConfig.decFields_all)
      me.$set(me.fieldsConfig, 'popShow', true)
    },
    /**
     * 显示设置界面(其他部分)
     */
    showOtherConfigPop() {
      let me = this
      me.$set(me.fieldsConfig, 'configId', me.fieldsConfig.otherId)
      me.$set(me.fieldsConfig, 'fields', me.fieldsConfig.otherFields_all)
      me.$set(me.fieldsConfig, 'popShow', true)
    },
    /**
     * 设置显示字段
     * @param configFields
     */
    handleUpdateFields(configFields) {
      let me = this,
        cardItemOther = me.getCardItemOther(),
        cardCustomsDeclaration = me.getCardItemCustomsDeclaration()
      if (me.fieldsConfig.configId === me.fieldsConfig.decId) {
        me.$set(me.fieldsConfig, 'decFields_show', configFields)
      } else if (me.fieldsConfig.configId === me.fieldsConfig.billId) {
        me.$set(me.fieldsConfig, 'billFields_show', configFields)
      } else if (me.fieldsConfig.configId === me.fieldsConfig.otherId) {
        me.$set(me.fieldsConfig, 'otherFields_show', configFields)
      }

      // 获取清单展示字段
      let configBillFields = me.configIntegration(me.fieldsConfig.billFields_show, me.fieldsConfig.billFields_all),
        realBillFields = [...me.fieldsConfig.listFields_all, ...configBillFields]

      // 获取报关单展示字段
      let decFieldsShow = me.fieldsConfig.decFields_show,
        decFieldsAll = me.fieldsConfig.decFields_all
      if (me.loadIfCheck) {
        decFieldsShow = [...decFieldsShow, me.getIfCheckFields()]
        decFieldsAll = [...decFieldsAll, me.getIfCheckFields()]
      }
      let realDecFields = [cardCustomsDeclaration, ...me.configIntegration(decFieldsShow, decFieldsAll)]

      // 获取其他展示字段
      let realOtherFields = me.configIntegration(me.fieldsConfig.otherFields_show, me.fieldsConfig.otherFields_all)
      if (realOtherFields.length > 0) {
        realOtherFields = [cardItemOther, ...realOtherFields]
      }
      me.$set(me, 'selfFields', me.doAnalysis([
        ...me.fieldsConfig.fixedFields_all,
        ...realBillFields,
        ...realDecFields,
        ...realOtherFields
      ]))
      me.$set(me.fieldsConfig, 'popShow', false)
      me.$set(me.fieldsConfig, 'configId', '')

      me.$nextTick(() => {
        if (me.$refs['fi_applyNo']) {
          me.$refs['fi_applyNo'].setRules()
        }
        if (me.$refs['fi_dclcusType']) {
          me.$refs['fi_dclcusType'].setRules()
        }
        if (me.$refs['fi_entryType']) {
          me.$refs['fi_entryType'].setRules()
        }
        if (me.$refs['fi_emsListNo']) {
          me.$refs['fi_emsListNo'].setRules()
        }
        if (me.$refs['fi_tradeMode']) {
          me.$refs['fi_tradeMode'].setRules()
        }
        if (me.$refs['fi_feeMark']) {
          me.$refs['fi_feeMark'].setRules()
        }
        if (me.$refs['fi_insurMark']) {
          me.$refs['fi_insurMark'].setRules()
        }
      })
      me.fillLoadBodySumInfo()
    },
    doAnalysis(fields) {
      let me = this,
        requiredFields = me.getRequiredFields(),
        preRequiredFields = me.getPreRequiredFields()
      fields.forEach(field => {
        field.required = requiredFields.includes(field.key)
        field.preRequired = preRequiredFields.includes(field.key)
        if (['agentCreditCode'].includes(field.key)) {
          field.title = ''
        }
      })
      return fields
    },
    /**
     * 将最终的配置字段与源字段整合为最终展示的字段数组
     * @param configFields
     * @param allFields
     * @returns {Array}
     */
    configIntegration(configFields, allFields) {
      let resultFields = []
      if (!Array.isArray(configFields)) {
        configFields = []
      }
      configFields.forEach(item => {
        let key = '',
          items = [],
          theItem = {},
          isObject = false
        if (typeof item === 'string') {
          key = item
          isObject = false
        } else {
          key = item.key
          isObject = true
          if (item.type === 'empty_formItem') {
            resultFields.push(deepClone(item))
          }
        }
        if (!isNullOrEmpty(key)) {
          items = allFields.filter(field => {
            return field.key === key
          })
          if (Array.isArray(items) && items.length > 0) {
            theItem = deepClone(items[0])
            if (isObject) {
              Object.keys(item).forEach(theKey => {
                theItem[theKey] = item[theKey]
              })
            }
            resultFields.push(theItem)
          }
        }
      })
      return resultFields
    },
    /**
     * 获取固定通用字段
     */
    getFixedFields(allFields) {
      let me = this,
        generals = [{
          key: 'emsListNo',
          itemClass: me.emsListNoClass4E
        }],
        generals2 = [],
        fixed = []
      if (me.ieMark === 'I') {
        generals2 = ['forwardCode', 'invoiceNo', 'voyageDate', 'arrivalPortDate']
        if (me.bondMark === '0') {
          fixed = []
        } else if (me.bondMark === '1') {
          if (me.fromColumns === 4) {
            fixed = ['declareCodeCustoms', 'declareCode', 'declareName', {
              key: 'district',
              title: '境内目的地',
              class: me.districtClass
            }, 'masterCustoms','batchStatus','batchNo']
          } else {
            fixed = ['declareCodeCustoms', 'declareCode', 'declareName', 'masterCustoms', {
              key: 'district',
              title: '境内目的地',
              class: me.districtClass
            },'batchStatus','batchNo']
          }
        } else {
          fixed = []
        }
      } else {
        if (me.fromColumns === 4) {
          generals2 = ['invoiceNo', 'voyageDate', 'forwardCode']
        } else {
          generals2 = [{
            key: '212121121212121',
            type: 'empty_formItem'
          }, 'invoiceNo', 'voyageDate', 'forwardCode']
        }
        if (me.bondMark === '0') {
          fixed = []
        } else if (me.bondMark === '1') {
          fixed = ['declareCodeCustoms', 'declareCode', 'declareName', 'masterCustoms', {
            key: 'district',
            title: '境内货源地',
            class: me.districtClass
          }, 'orderType']
        } else {
          fixed = []
        }
      }
      return me.configIntegration([...generals, ...generals2, ...fixed], allFields)
    },
    /**
     * 获取清单字段
     * @param allFields
     */
    getListFields(allFields) {
      let me = this,
        cardItemList = me.getCardItemList(),
        generals = ['declareCodeCustoms', 'declareCode', 'declareName', 'trafMode'],
        lists = []
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          lists = [...generals, {
            key: 'ieport',
            title: '进境关别'
          }, 'gmark', 'emsNo', 'tradeMode', 'cutMode', {
            key: 'tradeCountry',
            title: '启运国(地区)'
          }, {
            key: 'district',
            title: '境内目的地',
            class: me.districtClass
          }, 'billType', 'billListType', 'mergeType']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = [...generals, {
            key: 'ieport',
            title: '进境关别'
          }, {
            key: 'tradeCountry',
            title: '启运国(地区)'
          }, 'emsNo', 'tradeMode', 'cutMode', 'billType', {
            key: 'district',
            title: '境内目的地',
            class: me.districtClass
          }, 'billListType', 'mergeType', 'note']
        }
      } else {
        if (me.bondMark === '0') {
          lists = [...generals, {
            key: 'ieport',
            title: '出境关别'
          }, 'gmark', 'emsNo', 'tradeMode', 'cutMode', {
            key: 'tradeCountry',
            title: '运抵国(地区)'
          }, {
            key: 'district',
            title: '境内货源地',
            class: me.districtClass
          }, 'billType', 'billListType', 'mergeType']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = [...generals, {
            key: 'ieport',
            title: '出境关别'
          }, {
            key: 'tradeCountry',
            title: '运抵国(地区)'
          }, 'emsNo', 'tradeMode', 'cutMode', 'billType', {
            key: 'district',
            title: '境内货源地',
            class: me.districtClass
          }, 'billListType', 'mergeType', 'note']
        }
      }
      let result = lists
      if (lists.length > 0) {
        result = [cardItemList, ...result]
      }
      return me.configIntegration(result, allFields)
    },
    /**
     * 获取清单可配置字段默认字段
     * @returns {string[]}
     */
    getDefaultBillFields() {
      let me = this,
        lists = [],
        generals = ['dclcusMark', 'dclcusType', 'entryType', 'relEmsNo',
          'relListNo', 'applyNo', 'agentCode', 'agentCreditCode', 'agentName']
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          lists = [...generals, 'note']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = generals
        }
      } else {
        if (me.bondMark === '0') {
          lists = [...generals, 'note']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = generals
        }
      }
      return lists
    },
    /**
     * 获取(所有)可设置的清单字段信息
     * @param allFields
     */
    getConfigBillFields(allFields) {
      let me = this,
        lists = [],
        generals = ['dclcusMark', 'dclcusType', 'entryType', 'relEmsNo', 'relListNo', 'applyNo', 'agentCode', {
          key: 'agentCreditCode',
          title: '清单申报单位海关十位编码'
        }, 'agentName']
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          lists = [...generals, {
            key: 'note',
            itemClass: me.bondedNoteClass4E
          }, 'relEntryReceiveCode', 'relEntryReceiveCreditCode', 'relEntryReceiveName']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = generals
        }
      } else {
        if (me.bondMark === '0') {
          lists = [...generals, {
            key: 'note',
            itemClass: me.bondedNoteClass4E
          }, 'relEntryReceiveCode', 'relEntryReceiveCreditCode', 'relEntryReceiveName']
        } else if (me.bondMark === '1') {
          lists = []
        } else {
          lists = generals
        }
      }
      return me.configIntegration(lists, allFields)
    },
    /**
     * 获取其他字段
     * @param allFields
     */
    getOtherFields(allFields) {
      let me = this,
        generals = ['sumQty', 'sumDecTotal', 'curr', 'tradeAbout'],
        generals2 = ['cweight', 'containerType', 'containerNum', 'wrapType2', 'paymentMode','quoNo'],
        others = [],
        others2 = ['decTotalProcess', 'nootherPack', 'payTotal', 'clientTotal', 'packages']
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          others = ['shipFrom', ...generals2, 'otherMark', 'destinationCountry', 'inviteDate',
            {key: 'ownerCreditCode', title: '消费使用单位代码'}, {key: 'ownerName', title: '消费使用单位名称'}, 'remark',
            {key: 'receiveCode', title: '境内收货人代码'}, {key: 'receiveName', title: '境内收货人名称'}, 'goodsAttribute']
        } else if (me.bondMark === '1') {
          others = ['shipFrom', ...generals2, 'otherMark', 'destinationCountry', 'inviteDate',
            {key: 'ownerCreditCode', title: '消费使用单位代码'}, {key: 'ownerName', title: '消费使用单位名称'}, 'remark',
            {key: 'receiveCode', title: '境内收货人代码'}, {key: 'receiveName', title: '境内收货人名称'}, 'gmark',
            'preEmsListNo', 'sa', 'preStatus', 'goodsAttribute']
        } else {
          others = ['shipFrom', ...generals2, 'otherMark', 'inviteDate', 'remark',
            {key: 'ownerCreditCode', title: '消费使用单位代码'}, {key: 'ownerName', title: '消费使用单位名称'},
            {key: 'receiveCode', title: '境内收货人代码'}, {key: 'receiveName', title: '境内收货人名称'}, 'goodsAttribute']
        }
        others.push('goodsCategory')
      } else {
        if (me.bondMark === '0') {
          others = ['shipTo', ...generals2, {key: 'ownerCreditCode', title: '生产销售单位代码'},
            {key: 'ownerName', title: '生产销售单位名称'}, 'remark',
            {key: 'receiveCode', title: '境内发货人代码'}, {key: 'receiveName', title: '境内发货人名称'}, 'invoiceDate', 'billTo', 'notify', 'matTotal',
            ...others2]
        } else if (me.bondMark === '1') {
          others = ['shipTo', ...generals2, {key: 'ownerCreditCode', title: '生产销售单位代码'},
            {key: 'ownerName', title: '生产销售单位名称'}, 'remark', 'orderDate', 'warehouseCode',
            {key: 'receiveCode', title: '境内发货人代码'}, {key: 'receiveName', title: '境内发货人名称'},
            {key: 'destinationCountry', title: '最终目的国'},'invoiceDate', 'gmark', 'billTo', 'notify',
            ...others2]
        } else {
          others = ['shipTo', ...generals2, {key: 'ownerCreditCode', title: '生产销售单位代码'},
            {key: 'ownerName', title: '生产销售单位名称'}, 'remark',
            {key: 'receiveCode', title: '境内发货人代码'}, {key: 'receiveName', title: '境内发货人名称'},'invoiceDate', 'billTo', 'notify',
            ...others2]
        }
      }
      return me.configIntegration([...generals, ...others,'yqRateFlag','otherCost'], allFields)
    },
    /**
     * 获取必填项
     */
    getRequiredFields() {
      let me = this,
        generals = [/*'ownerCreditCode', 'ownerName'*/],
        requiredFields = [],
        dynamics = me.dynamicInfo.required
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          requiredFields = ['tradeMode', 'emsNo', 'gmark', 'billListType', 'agentCode', 'agentName']
        } else if (me.bondMark === '1') {
          requiredFields = ['tradeMode','entryClearanceType']
        } else {
          requiredFields = ['billListType', 'agentCode', 'agentName']
        }
      } else {
        if (me.bondMark === '0') {
          requiredFields = ['tradeMode', 'emsNo', 'gmark', 'billListType', 'agentCode', 'agentName']
        } else if (me.bondMark === '1') {
          requiredFields = ['tradeMode','entryClearanceType']
        } else {
          requiredFields = ['billListType', 'agentCode', 'agentName']
        }
      }
      return [...generals, ...requiredFields, ...dynamics]
    },
    /**
     * 添加必输项
     * @param fieldNames
     */
    addRequiredFields(fieldNames) {
      let me = this,
        requiredFields = deepClone(me.dynamicInfo.required)
      if (Array.isArray(fieldNames)) {
        fieldNames.forEach(fieldName => {
          if (!requiredFields.includes(fieldName)) {
            requiredFields.push(fieldName)
          }
        })
        me.$set(me.dynamicInfo, 'required', requiredFields)
      }
    },
    /**
     * 移除必输项
     * @param fieldNames
     */
    removeRequiredFields(fieldNames) {
      let me = this,
        requiredFields = deepClone(me.dynamicInfo.required)
      if (Array.isArray(fieldNames)) {
        fieldNames.forEach(fieldName => {
          if (requiredFields.includes(fieldName)) {
            requiredFields.splice(requiredFields.indexOf(fieldName), 1)
          }
        })
        me.$set(me.dynamicInfo, 'required', requiredFields)
      }
    },
    /**
     * 获取必填项(蓝星)
     */
    getPreRequiredFields() {
      let me = this,
        generals = ['declareCode', 'declareName', 'declareCreditCode'],
        requiredFields = [],
        dynamics = me.dynamicInfo.preRequired
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          requiredFields = ['trafMode', 'tradeCountry', 'ieport']
        } else if (me.bondMark === '1') {
          requiredFields = []
        } else {
          requiredFields = ['trafMode', 'tradeCountry', 'ieport']
        }
      } else {
        if (me.bondMark === '0') {
          requiredFields = ['trafMode', 'tradeCountry', 'ieport']
        } else if (me.bondMark === '1') {
          requiredFields = []
        } else {
          requiredFields = ['trafMode', 'tradeCountry', 'ieport']
        }
      }
      return [...generals, ...requiredFields, ...dynamics]
    },
    /**
     * 添加必输项(蓝星)
     * @param fieldNames
     */
    addPreRequiredFields(fieldNames) {
      let me = this,
        requiredFields = deepClone(me.dynamicInfo.preRequired)
      if (Array.isArray(fieldNames)) {
        fieldNames.forEach(fieldName => {
          if (!requiredFields.includes(fieldName)) {
            requiredFields.push(fieldName)
          }
        })
        me.$set(me.dynamicInfo, 'preRequired', requiredFields)
      }
    },
    /**
     * 移除必输项(蓝星)
     * @param fieldNames
     */
    removePreRequiredFields(fieldNames) {
      let me = this,
        requiredFields = deepClone(me.dynamicInfo.preRequired)
      if (Array.isArray(fieldNames)) {
        fieldNames.forEach(fieldName => {
          if (requiredFields.includes(fieldName)) {
            requiredFields.splice(requiredFields.indexOf(fieldName), 1)
          }
        })
        me.$set(me.dynamicInfo, 'preRequired', requiredFields)
      }
    },
    /**
     * 获取可折叠字段
     */
    getCollapseItems() {
      let me = this,
        listKeys = [],
        commListKeys = ['declareCodeCustoms', 'declareCode', 'declareName', 'trafMode'],
        generals = ['dclcusMark', 'dclcusType', 'entryType', 'relEmsNo', 'relListNo', 'applyNo', 'agentCode', 'agentCreditCode', 'agentName'],
        collapse = []
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          collapse = [...generals, 'relEntryReceiveCode', 'relEntryReceiveCreditCode', 'relEntryReceiveName']
        } else if (me.bondMark === '1') {
          collapse = []
        } else {
          collapse = generals
        }
      } else {
        if (me.bondMark === '0') {
          collapse = [...generals, 'relEntryReceiveCode', 'relEntryReceiveCreditCode', 'relEntryReceiveName']
        } else if (me.bondMark === '1') {
          collapse = []
        } else {
          collapse = generals
        }
      }
      if (me.ieMark === 'I') {
        if (me.bondMark === '0') {
          listKeys = ['ieport', 'gmark', 'emsNo', 'tradeMode', 'cutMode', 'tradeCountry', 'district', 'districtCode', 'districtPostCode', 'billType', 'billListType', 'mergeType', 'note']
        } else if (me.bondMark === '1') {
          listKeys = []
        } else {
          listKeys = ['ieport', 'tradeCountry', 'emsNo', 'tradeMode', 'cutMode', 'billType', 'district', 'districtCode', 'districtPostCode', 'billListType', 'mergeType', 'note']
        }
      } else {
        if (me.bondMark === '0') {
          listKeys = ['ieport', 'gmark', 'emsNo', 'tradeMode', 'cutMode', 'tradeCountry', 'district', 'districtCode', 'districtPostCode', 'billType', 'billListType', 'mergeType', 'note']
        } else if (me.bondMark === '1') {
          listKeys = []
        } else {
          listKeys = ['ieport', 'tradeCountry', 'emsNo', 'tradeMode', 'cutMode', 'billType', 'district', 'districtCode', 'districtPostCode', 'billListType', 'mergeType', 'note']
        }
      }
      return [...commListKeys, ...listKeys, ...collapse]
    },
    /**
     * 执行显示/隐藏清单字段
     */
    doItemCollapse() {
      let me = this,
        display = me.listCollapse ? 'none' : 'block',
        collapseItems = me.getCollapseItems()
      collapseItems.forEach(itemKey => {
        let theFrmItem = null,
          theCtrl = getComponentByKey(me, 'frmData', itemKey)
        if (itemKey === 'district') {
          theCtrl = me.$refs[itemKey]
          if (theCtrl) {
            theCtrl.style.display = display
          }
        }
        if (theCtrl && theCtrl.$parent) {
          theFrmItem = theCtrl.$parent
          theFrmItem.$el.style.display = display
        }
      })
    },
    /**
     * 点击清单信息标题栏以隐藏/显示部分信息
     * @param e
     */
    cardItemCollapse(e) {
      let me = this,
        currTarget = e.target

      function isFrmItem(item) {
        if (item === null || typeof item === 'undefined') {
          return true
        } else if (item.className.indexOf('ivu-form-item') > -1
          && item.className.indexOf('ivu-form-item-label') === -1
          && item.className.indexOf('ivu-form-item-content') === -1) {
          return true
        }
        return false
      }

      while (!isFrmItem(currTarget) && currTarget.parentNode) {
        currTarget = currTarget.parentNode
      }
      let icons, icon = null
      if (currTarget && currTarget.className.indexOf('ivu-form-item') > -1) {
        icons = currTarget.getElementsByTagName('I')
        if (icons.length > 0) {
          icon = icons[0]
        }
      }
      if (icon) {
        me.$set(me, 'listCollapse', !me.listCollapse)
        if (me.listCollapse) {
          icon.style.transform = 'rotate(0deg)'
        } else {
          icon.style.transform = 'rotate(90deg)'
        }
        me.doItemCollapse()
      }
    },
    /**
     * 清单信息
     * @returns {{itemClass: (function(): (string)), isCard: boolean, title: string, type: string, collapse: boolean, key: string, props: {collapse: boolean}, on: {click: dynamicDefault.methods.cardItemCollapse}}}
     */
    getCardItemList() {
      let me = this
      return {
        isCard: true,
        collapse: true,
        props: {
          collapse: me.listCollapse
        },
        on: {
          click: me.cardItemCollapse
        },
        title: '清单信息',
        key: '1212121212121',
        type: 'empty_formItem',
        itemClass: me.splitLineClass
      }
    },
    /**
     * 报关单信息
     * @returns {{itemClass: (function(): (string)), isCard: boolean, title: string, type: string, key: string}}
     */
    getCardItemCustomsDeclaration() {
      let me = this
      return {
        isCard: true,
        title: '报关单信息',
        key: '2323232323232',
        type: 'empty_formItem',
        itemClass: me.splitLineClass
      }
    },
    /**
     * 其他信息
     * @returns {{itemClass: (function(): (string)), isCard: boolean, title: string, type: string, key: string}}
     */
    getCardItemOther() {
      let me = this
      return {
        isCard: true,
        title: '其他信息',
        key: '*************',
        type: 'empty_formItem',
        itemClass: me.splitLineClass
      }
    }
  }
}
