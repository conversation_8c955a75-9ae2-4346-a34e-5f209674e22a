import { namespace } from '@/project'
import DynamicHead from '../dynamic-head-edit'

export const dynamicTabs = {
  name: 'dynamicTabs',
  components: {
    DynamicHead
  },
  computed: {
    /**
     * 通关业务设置
     */
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    dynamicHeadTabShow() {
      return this.configData.decType === '1'
    },
    /**
     * 涉证管理Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    certTabShow() {
      let me = this,
        tabCommand = 'certTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 箱单信息Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    packingTabShow() {
      let me = this,
        tabCommand = 'packingTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 箱单维护Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    packingMaintainTabShow(){
      let me = this,
        tabCommand = 'packingMaintainTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 物流追踪Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    logisticsTabShow() {
      let me = this,
        tabCommand = 'logisticsTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 报关追踪Tab是否显示
     * @returns {dynamicTabs.methods.showBody|boolean}
     */
    entryTabShow() {
      let me = this,
        tabCommand = 'entryTab'
      return me.tabShowByAction(tabCommand)
    },
    /**
     * 随附单据是否锁定
     */
    attachLock() {
      let me = this
      return !!(me.editConfig && me.editConfig.editData && me.editConfig.editData.attachLock === '1')
    }
  },
  methods: {
    /**
     * 根据tab标志判断是否显示Tab
     * @param tabCommand
     */
    tabShowByAction(tabCommand) {
      let me = this,
        tabActions = me.actions.filter(action => {
          return action.command === tabCommand
        })
      if (Array.isArray(tabActions) && tabActions.length > 0) {
        return me.showBody
      }
      return false
    },
    /**
     * tab切换到动态表头时的操作
     */
    reloadDynamicHeadTab() {
      let me = this
      me.$nextTick(() => {
        if (me.$refs.DynamicHead) {
          me.$refs.DynamicHead.reCaculateWt()
          me.$refs.DynamicHead.loadBodySumInfo()
          me.$refs.DynamicHead.setFeeDisableByTransMode(me.editConfig.editData.transMode)
          me.$emit('onDetailReload', me.editConfig.editData.sid, me.editConfig.editStatus)
        }
      })
    }
  }
}
