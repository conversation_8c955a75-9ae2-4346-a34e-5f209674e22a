<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="headerEditFrom" class="dc-form dc-form-4 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="123"
             style="padding: 0; grid-column-gap: 0;">
      <Card :bordered="false" class="dc-merge-1-5">
        <p slot="title">基础信息</p>
        <div class="dc-form dc-form-4" style="padding-right: 10px;">
          <XdoFormItem ref="fiEmsListNo" prop="emsListNo" label="单据内部编号" class="dc-merge-1-3 btnInput">
            <Input type="text" v-model="frmData.emsListNo" :disabled="!emsListNoDisable" :maxlength="32">
              <Button slot="append" type="dashed" @click="openSelectTemplate" :disabled="!isNew">选择模板</Button>
            </Input>
          </XdoFormItem>
          <XdoFormItem></XdoFormItem>
<!--          <XdoFormItem></XdoFormItem>-->
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="mawb" label="主提运单号">
            <XdoIInput type="text" v-model="frmData.mawb" :maxlength="50" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="hawb" label="提运单号">
            <XdoIInput type="text" v-model="frmData.hawb" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="voyageDate" label="航班日期">
            <XdoDatePicker type="date" v-model="frmData.voyageDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="shipDate" label="出货日期">
            <XdoDatePicker type="date" v-model="frmData.shipDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.forwardCode" prop="forwardCode" ref="fiForwardCode" label="货运代理">
            <xdo-select v-model="frmData.forwardCode" :options="this.cmbSource.forwardCodeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="trafMode" label="运输方式">
            <xdo-select v-model="frmData.trafMode" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.transf" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.trafName" prop="trafName" ref="fiTrafName" label="运输工具及航次">
            <XdoIInput type="text" v-model="frmData.trafName" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.districtCode" prop="districtCode" label="境内货源地" class="dc-merge-3-5">
            <AreaPostCascader :options="areaOptions" @onAreaDataChanged="onAreaDataChanged" :disabled="showDisable"></AreaPostCascader>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.invoiceNo" prop="invoiceNo" ref="fiInvoiceNo" label="发票号">
            <XdoIInput type="text" v-model="frmData.invoiceNo" :maxlength="100" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.contrNo" prop="contrNo" ref="fiContrNo" label="合同协议号">
            <XdoIInput type="text" v-model="frmData.contrNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.tradeNation" prop="tradeNation" ref="fiTradeNation" label="贸易国(地区)">
            <xdo-select v-model="frmData.tradeNation" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="tradeCountry" label="运抵国(地区)">
            <xdo-select v-model="frmData.tradeCountry" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.packNum" prop="packNum" ref="fiPackNum" label="件数">
            <xdo-input v-model="frmData.packNum" number int-length="9" precision="0" notConvertNumber :disabled="showDisable"></xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.netWt" prop="netWt" ref="fiNetWt" label="总净重">
            <div class="shortAppend">
              <xdo-input v-model="frmData.netWt" decimal int-length="13" :precision="digitConfig.netWt" notConvertNumber :disabled="showDisable">
                <span slot="append">KG</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.grossWt" prop="grossWt" ref="fiGrossWt" label="总毛重">
            <div class="shortAppend">
              <xdo-input v-model="frmData.grossWt" decimal int-length="13" :precision="digitConfig.grossWt" notConvertNumber :disabled="showDisable">
                <span slot="append">KG</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="volume" label="体积">
            <div class="shortAppend">
              <xdo-input v-model="frmData.volume" decimal int-length="11" :precision="digitConfig.volume" :disabled="showDisable">
                <span slot="append">m³</span>
              </xdo-input>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.wrapType" prop="wrapType" ref="fiWrapType" label="包装种类">
            <xdo-select v-model="frmData.wrapType" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.wrap" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="wrapType2" label="其他包装" class="dc-merge-2-5">
            <Input v-model="wrapType2View" placeholder="请选择包装种类..." disabled>
              <XdoButton slot="append" type="primary" :disabled="showDisable" @click="onWrapType2Search" style="width: 100%;">请选择</XdoButton>
            </Input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="destPort" label="指运港">
            <xdo-select v-model="frmData.destPort" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.port_lin" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.overseasShipper" prop="overseasShipper" label="境外收货人">
            <xdo-select v-model="frmData.overseasShipper" tooltip :options="this.cmbSource.overseasShipperList" :optionLabelRender="pcodeRender" :disabled="showDisable" @on-change="overseasShipperChange"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="shipTo" label="Ship To">
            <xdo-select v-model="frmData.shipTo" :options="this.cmbSource.shipToData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="remark" label="内部备注">
            <XdoIInput type="text" v-model="frmData.remark" :disabled="showDisable"></XdoIInput>
          </DcFormItem>

          <!-- /** LG定制 start */ -->
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="inviteDate" label="申请出口日期">
            <XdoDatePicker type="date" v-model="frmData.inviteDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"  prop="cweight"  label="计费重量">
            <xdo-input v-model="frmData.cweight" decimal int-length="5" precision="2" notConvertNumber :disabled="showDisable"></xdo-input>
          </DcFormItem>
          <!-- /** LG定制 end */ -->

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="ownerCreditCode" label="生产销售单位代码">
            <xdo-select v-model="frmData.ownerCreditCode" :disabled="showDisable" :options="this.cmbSource.ownerCode" :optionLabelRender="pcodeRender"
                        @on-change="onOwnerCodeEnter"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="ownerName" label="生产销售单位名称">
            <XdoIInput type="text" v-model="frmData.ownerName" disabled></XdoIInput>
          </DcFormItem>
        </div>
      </Card>
      <Card :bordered="false" class="dc-merge-1-5" style="margin-top: 0;">
        <p slot="title">申报信息</p>
        <div class="dc-form dc-form-4" style="padding-right: 10px;">
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="declareCodeCustoms" label="报关行简码及名称">
            <xdo-select v-model="frmData.declareCodeCustoms" :disabled="declareDisable"
                        :options="this.cmbSource.cutData" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareCode" label="海关十位代码">
            <XdoIInput type="text" v-model="frmData.declareCode" :maxlength="10" :disabled="declareDisable" @on-enter="declareCodeEnter"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareName" label="申报单位名称">
            <XdoIInput type="text" v-model="frmData.declareName" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="declareCreditCode" label="社会信用代码">
            <XdoIInput type="text" v-model="frmData.declareCreditCode" :maxlength="18" disabled @on-blur="declareCreditCodeEnter"></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="emsNo" label="备案号">
            <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoList" :disabled="emsNoDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="tradeMode" label="监管方式">
            <xdo-select v-model="frmData.tradeMode" :options="this.filterTradeMode" :disabled="showDisable"
                        :optionLabelRender="pcodeRender" @on-change="tradeModeChange"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="cutMode" label="征免性质">
            <xdo-select v-model="frmData.cutMode" :disabled="cutModeDisabled" :asyncOptions="pcodeList" :meta="pcode.levytype" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.transMode" prop="transMode" label="成交方式" ref="transMode">
            <xdo-select v-model="frmData.transMode" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.transac" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="masterCustomsRequired" prop="masterCustoms" label="申报地海关">
            <xdo-select v-model="frmData.masterCustoms" :asyncOptions="pcodeList" transfer
                        :meta="pcode.customs_rel" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" preRequired prop="ieport" label="出境关别">
            <xdo-select v-model="frmData.ieport" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="entryPort" label="离境口岸">
            <xdo-select v-model="frmData.entryPort" :disabled="showDisable" :asyncOptions="pcodeList" meta="CIQ_ENTY_PORT" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="destinationCountry" label="最终目的国">
            <xdo-select v-model="frmData.destinationCountry" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billType" label="清单归并类型">
            <xdo-select v-model="frmData.billType" :options="this.cmbSource.billTypeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="mergeType" label="报关单归并类型">
            <xdo-select v-model="frmData.mergeType" :options="this.cmbSource.mergeTypeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dclcusMark" ref="fiDclcusMark" label="报关标志">
            <xdo-select v-model="frmData.dclcusMark" :options="this.cmbSource.dclcusMarkData"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dclcusType" ref="fiDclcusType" label="报关类型">
            <xdo-select v-model="frmData.dclcusType" :options="this.cmbSource.dclcusTypeData"
                        :optionLabelRender="pcodeRender" :disabled="clearanceDisable"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="entryType" label="报关单类型">
            <xdo-select v-model="frmData.entryType" :options="this.cmbSource.entryTypeData"
                        :optionLabelRender="pcodeRender" :disabled="clearanceDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="applyNo" label="申报表编号">
            <XdoIInput type="text" v-model="frmData.applyNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="relEmsNoRequiredTips" prop="relEmsNo" ref="fiRelEmsNo" label="关联备案号">
            <XdoIInput type="text" v-model="frmData.relEmsNo" :disabled="showDisable" :maxlength="12"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="relListNo" ref="fiRelListNo" label="关联清单编号">
            <XdoIInput type="text" v-model="frmData.relListNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="iedate" label="出口日期">
            <XdoDatePicker type="date" v-model="frmData.iedate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="licenseNo" label="许可证号">
            <XdoIInput type="text" v-model="frmData.licenseNo" :disabled="showDisable"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="gmark" label="物料类型">
            <xdo-select v-model="frmData.gmark" :options="this.cmbSource.gmarkList"
                        :optionLabelRender="pcodeRender" :disabled="gMarkDisable"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billListType" label="清单类型">
            <xdo-select v-model="frmData.billListType" :options="importExportManage.BILL_TYPE_MAP"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="receiveCode" label="境内发货人代码">
            <XdoIInput type="text" v-model="frmData.receiveCode" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="receiveName" label="境内发货人名称">
            <XdoIInput type="text" v-model="frmData.receiveName" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="containerType" label="集装箱类型">
            <xdo-select :disabled="showDisable" v-model="frmData.containerType" clearable :options="this.cmbSource.containerList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="containerNum" label="集装箱数量">
            <xdo-input :disabled="showDisable" v-model="frmData.containerNum" number int-length="5" ></xdo-input>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="sumQty" label="总数量">
            <xdo-input v-model="frmData.sumQty" decimal int-length="10" precision="5" :disabled="showDisable">
              <span slot="append">{{statistics.sumQty}}</span>
            </xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="sumDecTotal" label="总金额">
            <xdo-input v-model="frmData.sumDecTotal" decimal int-length="13" :precision="digitConfig.decTotal" :disabled="showDisable">
              <span slot="append">{{statistics.sumDecTotal}}</span>
            </xdo-input>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="curr" label="币制">
            <XdoIInput type="text" v-model="statistics.currName" disabled></XdoIInput>
          </DcFormItem>

          <div class="dc-merge-1-5 dc-form" style="padding-top: 0;">
            <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.feeMark" prop="feeMark" label="运费" ref="feeMark">
              <FeeCascader :options="feeOptions" @onFeeDataChanged="onFeeDataChanged" :disabled="transportDisabled"></FeeCascader>
            </DcFormItem>
            <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.insurMark" prop="insurMark" label="保费" ref="insurMark">
              <FeeCascader :options="insurOptions" @onFeeDataChanged="onInsurDataChanged" :disabled="insuranceDisabled"></FeeCascader>
            </DcFormItem>
            <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="otherMark" label="杂费">
              <FeeCascader :options="otherOptions" @onFeeDataChanged="onOtherDataChanged" can-minus :disabled="showDisable"></FeeCascader>
            </DcFormItem>
          </div>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" class="dc-merge-1-4 eye-catching" prop="promiseItems" label="价格说明">
            <div style="display: grid; grid-template-columns: auto 116px;">
              <CheckboxGroup v-model="promiseItemsList" @on-change="promiseItemsChange">
                <XdoCheckbox label="1" :disabled="showDisable" :indeterminate="promiseItems.pi1Im">
                  <span>特殊关系确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="2" :disabled="showDisable" :indeterminate="promiseItems.pi2Im">
                  <span>价格影响确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="3" :disabled="showDisable" :indeterminate="promiseItems.pi3Im">
                  <span>支付特许权使用费确认</span>
                </XdoCheckbox>
                <XdoCheckbox label="4" :disabled="showDisable" :indeterminate="promiseItems.pi4Im">
                  <span>自报自缴</span>
                </XdoCheckbox>
              </CheckboxGroup>
              <XdoIInput type="text" value="注: '✔'为是; '━'为否" disabled></XdoIInput>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" :preRequired="requiredBlueFields.tradeTerms" prop="tradeTerms" ref="fiTradeTerms" label="贸易条款">
            <xdo-select v-model="frmData.tradeTerms" :disabled="showDisable" clearable :options="this.cmbSource.tradeTermNewList"></xdo-select>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="agentCode" label="清单申报单位">
            <XdoIInput type="text" v-model="frmData.agentCode" :disabled="declareDisable" @on-enter="agentCodeEnter">
            </XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="agentCreditCode" label="" labelWidth="0">
            <XdoIInput type="text" v-model="frmData.agentCreditCode" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="agentName" label="清单申报单位名称">
            <XdoIInput type="text" v-model="frmData.agentName" disabled></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billTo" label="billTo代码">
            <xdo-select :disabled="showDisable" v-model="frmData.billTo" clearable :options="this.cmbSource.billToList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="notify" label="notify代码">
            <xdo-select :disabled="showDisable" v-model="frmData.notify" clearable :options="this.cmbSource.notifyList"
                        dataValue="value" dataLabel="label" :optionLabelRender="(item) => item.value + ' ' + item.label"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="invoiceDate" label="发票日期">
            <XdoDatePicker type="date" v-model="frmData.invoiceDate" :disabled="showDisable" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note" class="dc-merge-2-5" label="备注">
            <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable" :maxlength="256"></XdoIInput>
          </DcFormItem>
        </div>
      </Card>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
    <TemplateSelectionList ref="selTemplate" :show.sync="tmpSelListShow" iemark="E" bond-mark="0" @selectTemplate:success="selectTemplateSuccess"></TemplateSelectionList>
    <TemplateConfirm :show.sync="tempConfirmShow" @confirm:success="afterConfirm"></TemplateConfirm>
    <WrapTypeSelectPop :show.sync="wrapType2PopShow" :wrap-type="frmData.wrapType2" @doWrapTypeFill="doChangeWrapType2"></WrapTypeSelectPop>
    <decErpHeadCheckPop :show.sync="headCheckPop.show" :error-data="headCheckPop.errData" @doContinue="doSaveContinue"></decErpHeadCheckPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { productClassify } from '@/view/cs-common'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { decErpHeadIEEdit } from '@/view/cs-ie-manage/js/dec-erp-head/decErpHeadIEEdit'

  export default {
    name: 'bondedEHeadEdit',
    mixins: [decErpHeadIEEdit],
    data() {
      const validateEmsNo = (rule, value, callback) => {
        if (!isNullOrEmpty(value) && value.trim() === this.frmData.emsNo) {
          callback(new Error('不能和备案号相同!'))
        } else {
          callback()
        }
      }
      return {
        iemark: 'E',
        bondMark: '0',
        moduleName: 'LE',
        cmbSource: {
          shipToData: [],
          billToList: [],
          notifyList: [],
          gmarkList: productClassify.GMARK_SINGLE_SELECT
        },
        ajaxUrl: {
          uniqueCheck: csAPI.csImportExport.decErpComm.uniqueCheck,
          getEntityByKey: csAPI.csImportExport.decErpEHeadN.getEntityByKey,
          getSumListContent: csAPI.csImportExport.decErpEHeadN.getSumListContent
        },
        rulesHeader: {
          emsNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gmark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          agentCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          agentName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          entryType: [{required: false, message: '不能为空!', trigger: 'blur'}],
          relListNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
          dclcusType: [{required: false, message: '不能为空!', trigger: 'blur'}],
          billListType: [{required: true, message: '不能为空!', trigger: 'blur'}],
          relEmsNo: [{required: false, message: '不能为空!', trigger: 'blur'}, {validator: validateEmsNo, trigger: 'blur'}]
        }
      }
    },
    watch: {
      /**
       * 报关标志
       */
      'frmData.dclcusMark': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === '1') {
            me.rulesHeader.dclcusType[0].required = true
            me.rulesHeader.entryType[0].required = true
          } else if (val === '2') {
            me.rulesHeader.dclcusType[0].required = false
            me.rulesHeader.entryType[0].required = false
          }
          if (me.$refs.fiDclcusMark) {
            me.$refs.fiDclcusMark.setRules()
          }
          if (me.$refs.fiDclcusType) {
            me.$refs.fiDclcusType.setRules()
          }
        }
      },
      'frmData.overseasShipper': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (isNullOrEmpty(val)) {
            me.$set(me.cmbSource, 'shipToData', [])
            me.$set(me.cmbSource, 'billToList', [])
            me.$set(me.cmbSource, 'notifyList', [])
          } else {
            me.$http.post(csAPI.csBaseInfo.clientInfo.shipTo.getShipToCodeName + '/' + val).then(res => {
              me.$set(me.cmbSource, 'shipToData', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'shipToData', [])
            })
            // billTo
            me.$http.post(csAPI.csBaseInfo.clientInfo.billTo.getBillTo + '/' + val).then(res => {
              me.$set(me.cmbSource, 'billToList', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'billToList', [])
            })
            // notify
            me.$http.post(csAPI.csBaseInfo.clientInfo.notify.getNotify + '/' + val).then(res => {
              me.$set(me.cmbSource, 'notifyList', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.cmbSource, 'notifyList', [])
            })
          }
        }
      }
    },
    mounted: function () {
      let me = this
      // 客户
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.cmbSource.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
      }).catch(() => {
        me.cmbSource.overseasShipperList = []
      })
      me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.decErpEHeadN.insert)
      me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.decErpEHeadN.update)
    },
    methods: {
      /**
       * 自定义初始化值
       */
      getCustomsData() {
        return {
          applyNo: '',
          relEmsNo: '',
          relListNo: '',
          shipDate: '',
          entryType: '2',
          bondMark: '0',
          shipTo: '',
          dclcusMark: '1',
          dclcusType: '2',
          billListType: '0',
          emsNo: this.$store.getters[`${namespace}/defaultEmsNo`],
          tradeMode: this.$store.getters[`${namespace}/defaultTradeMode`]
        }
      },
      /**
       * 成交方式改变
       * @param value
       */
      transactionChange(value) {
        let me = this
        me.feeInit(value)
        me.onTransactionChangeBase(value, null)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .btnInput .ivu-form-item-error-tip {
    right: 70px;
  }

  .preRequired:before {
    color: blue;
    content: '*';
    font-size: 12px;
    line-height: 1px;
    margin-right: 4px;
    font-family: 'SimSun';
    display: inline-block;
  }

  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }

  /deep/ .ivu-input-group-append {
    width: 100px;
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }

  .shortAppend {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  /deep/ .shortAppend > .ivu-input-group-with-append > .ivu-input-group-append {
    width: 40px !important;
  }

  /deep/ .audit-view label.ivu-form-item-label {
    background-color: greenyellow;
  }

  /deep/ .audit-error label.ivu-form-item-label {
    color: white;
    background-color: red;
  }

  /deep/ .eye-catching label {
    color: orangered;
    font-weight: bold;
  }

  /deep/ .eye-catching .ivu-form-item-content span {
    color: orangered !important;
  }
</style>
