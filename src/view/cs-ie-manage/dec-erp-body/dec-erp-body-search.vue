<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="gmark" label="物料类型">
        <xdo-select v-model="searchParam.gmark" :options="this.cmbDataSource.gmarkList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="bondMark === '0'" prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="bondMark === '0'" prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="bondMark === '0'" prop="gno" label="备案序号">
        <xdo-input v-model="searchParam.gno" number int-length="11"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gmodel" label="申报规格型号">
        <XdoIInput type="text" v-model="searchParam.gmodel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGName" label="中文名称">
        <XdoIInput type="text" v-model="searchParam.copGName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGModel" label="料号申报要素">
        <XdoIInput type="text" v-model="searchParam.copGModel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="isImport" prop="originCountry" label="原产国">
        <xdo-select v-model="searchParam.originCountry" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="!isImport" prop="originCountry" label="目的国">
        <xdo-select v-model="searchParam.destinationCountry" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="orderNo" :label="orderNoLabel">
        <XdoIInput type="text" v-model="searchParam.orderNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号码">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="supplierCode" :label="supplierCodeLabel">
        <xdo-select v-model="searchParam.supplierCode" :options="this.cmbDataSource.supplierCodeList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="exgVersion" label="单耗版本号">
        <XdoIInput type="text" v-model="searchParam.exgVersion"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="linkedNo" label="提取单号">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="inOutNo" :label="inOutNoLabel">
        <XdoIInput type="text" v-model="searchParam.inOutNo" :maxlength="100"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="itemNo" label="模拟项号">
        <XdoIInput type="text" v-model="searchParam.itemNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { importExportManage } from '@/view/cs-common'

  export default {
    name: 'decErpBodySearch',
    props: {
      headId: {
        type: String,
        default: () => ('')
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      let customData = this.getCustomData()
      return {
        searchParam: {
          headId: this.headId,
          gno: null,
          copGNo: '',
          codeTS: '',
          gname: '',
          gmodel: '',
          exgVersion: '',
          bondMark: '',
          gmark: '',
          emsNo: '',
          copGName: '',
          copGModel: '',
          orderNo: '',
          invoiceNo: '',
          supplierCode: '',
          linkedNo: '',
          inOutNo: '',
          itemNo: '',
          facGNo: '',
          ...customData
        },
        ajaxUrl: {
          getProvider: ''
        },
        cmbDataSource: {
          emsNoList: [],
          supplierCodeList: [],
          gmarkList: importExportManage.gmarkMap
        }
      }
    },
    computed: {
      isImport() {
        return this.iemark === 'I'
      },
      supplierCodeLabel() {
        if (this.iemark === 'I') {
          return '供应商'
        } else {
          return '客户'
        }
      },
      orderNoLabel() {
        if (this.iemark === 'I') {
          return '采购订单号'
        } else {
          return '销售订单号'
        }
      },
      inOutNoLabel() {
        if (this.iemark === 'I') {
          return '入库关联单号'
        } else {
          return '出库关联单号'
        }
      },
    },
    watch: {
      iemark: {
        immediate: true,
        handler: function (val) {
          if (val === 'I') {
            this.$set(this.ajaxUrl, 'getProvider', csAPI.ieParams.PRD)
          } else if (val === 'E') {
            this.$set(this.ajaxUrl, 'getProvider', csAPI.ieParams.CLI)
          }
          this.getProvider()
        }
      },
      bondMark: {
        immediate: true,
        handler: function (val) {
          if (val === '0') {
            this.getEmsNo()
          }
          this.$set(this.searchParam, 'bondMark', val)
        }
      }
    },
    methods: {
      getCustomData() {
        if (this.iemark === 'I') {
          return {
            originCountry: ''
          }
        } else if (this.iemark === 'E') {
          return {
            destinationCountry: ''
          }
        } else {
          return {}
        }
      },
      /**
       * 获取备案号
       */
      getEmsNo() {
        let me = this
        me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
          let tmpArr = []
          for (let item of res.data.data) {
            tmpArr.push({
              value: item.VALUE,
              label: item.VALUE
            })
          }
          me.cmbDataSource.emsNoList = tmpArr
        }).catch(() => {
        })
      },
      /***
       * 获取客户 / 获取供应商
       */
      getProvider() {
        let me = this
        me.$http.post(me.ajaxUrl.getProvider).then(res => {
          me.cmbDataSource.supplierCodeList = ArrayToLocaleLowerCase(res.data.data)
        }).catch(() => {
          me.cmbDataSource.supplierCodeList = []
        })
      }
    }
  }
</script>

<style scoped>
</style>
