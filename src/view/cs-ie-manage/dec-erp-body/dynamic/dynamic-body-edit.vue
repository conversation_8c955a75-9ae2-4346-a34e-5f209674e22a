<template>
  <section style="padding: 2px;">
    <div style="background-color: white; padding-right: 6px;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="136" :class="fromClass"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields"
                   :auditMark="canAeoAudit" :auditData="auditData" @onAuditChange="onAuditChange">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
    <XdoCard style="font-size: 12px; color: #333; padding: 10px;">
      表体数值自动取值，按以下先后顺序及逻辑：
      <ul style="list-style: none;">
        <li>1、法一、法二单位等于申报计量单位，法一法二数量取申报数量；</li>
        <li>2、法一、法二单位不等于申报计量单位，按物料中心维护的比例因子自动生成法一法二数量；</li>
        <li>3、法一、法二单位为035(KG)时，取净重；</li>
        <li>4、净重自动取值：用户未维护时按物料中心维护的净重转换；</li>
        <li>5、导入时，若用户填写了净重、法一、法二数量，则以企业填写的为准，若用户未填写，则按上述逻辑进行赋值</li>
      </ul>
    </XdoCard>
    <FormSetup v-model="fieldsConfig.popShow" :resId="fieldsConfig.configId" :columns="fieldsConfig.fields"
               companyLevel @updateColumns="handleUpdateFields"></FormSetup>
  </section>
</template>

<script>
  // import { csAPI } from '@/api'
  // import { editStatus } from '@/view/cs-common'
  import { importExportManage } from '@/view/cs-common'
  import { regulatoryRules } from '@/view/cs-ie-manage/js/comm/regulatoryRules'
  import { dynamicBodyDefault } from '@/view/cs-ie-manage/dec-erp-body/js/dynamicBodyDefault'
  import { auditTrailBodyDetails } from '@/view/cs-ie-manage/dec-erp-body/js/auditTrailBodyDetails'

  export default {
    components: {},
    name: 'dynamicBodyEdit',
    props: {
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1', ''].includes(value)
        }
      }
    },
    mixins: [dynamicBodyDefault, auditTrailBodyDetails, regulatoryRules],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        formName: 'frmData',
        detailConfig: {
          rules: {},
          model: {},
          fields: []
        },
        fieldsConfig: {
          fields: [],
          configId: '',
          popShow: false
        },
        cmbSource: {
          emsNo: [],
          gmark: [],
          tradeMode: [],
          bondMark: importExportManage.bondedFlagMap
        },
        ajaxUrl: {},
        buttons: [
          {...btnComm, label: '设置字段', icon: 'dc-btn-config', key: 'setFields', click: this.handleSetFields},
          {...btnComm, label: '保存', icon: 'dc-btn-save', key: 'save', click: this.handleSave},
          {...btnComm, label: '返回', icon: 'dc-btn-cancel', key: 'back', click: this.handleBack}
        ]
      }
    },
    created: function () {
      let me = this
      me.$nextTick(() => {
        if (me.bondMark === '0') {
          me.$set(me.cmbSource, 'gmark', importExportManage.gmarkMap.filter(val => {
            return ['E', 'I'].includes(val.value)
          }))
        } else {
          me.$set(me.cmbSource, 'gmark', importExportManage.gmarkMap)
        }
        let field = me.detailConfig.fields.find(p => p.key === 'gmark')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    mounted: function () {

    },
    computed: {
      fromClass() {
        return ''
      }
    },
    watch: {
      ieMark: {
        handler: function () {
          this.resetTradeMode()
        }
      },
      'detailConfig.model.emsNo': {
        handler: function () {
          this.resetTradeMode()
        }
      }
    },
    methods: {
      resetTradeMode() {
        let me = this,
          field = me.detailConfig.fields.find(p => p.key === 'tradeMode'),
          tradeModeSource = me.getTradeModeByEmsNo(me.detailConfig.model.emsNo, me.ieMark)
        me.$set(me.cmbSource, 'tradeMode', tradeModeSource)
        me.$nextTick(() => {
          if (field) {
            me.fieldOptimization(field)
          }
        })
      },
      /**
       * 获取所有字段
       */
      getFullFields() {
        return [{
          type: 'select',
          key: 'bondMark',
          title: '保完税标识'
        }, {
          key: 'emsNo',
          type: 'select',
          title: '备案号'
        }, {
          type: 'select',
          key: 'tradeMode',
          title: '监管方式'
        }, {
          key: 'gmark',
          type: 'select',
          title: '物料类型'
        }, {
          key: 'copGNo',
          title: '备案料号',
          props: {
            disabled: true
          }
        }, {
          key: 'gno',
          title: '备案序号',
          props: {
            disabled: true
          }
        }, {
          key: 'facGNo',
          title: '企业料号'
        }, {
          key: 'codeTS',
          title: '商品编码',
          props: {
            disabled: true
          }
        }, {
          key: 'gname',
          title: '商品名称',
          props: {
            disabled: true
          }
        }, {
          key: 'unit',
          type: 'pcode',
          title: '计量单位',
          props: {
            meta: 'UNIT',
            disabled: true
          }
        }, {
          key: 'gmodel',
          title: '申报规格型号'
        }, {
          key: 'qty',
          type: 'xdoInput',
          title: '申报数量',
          props: {
            intDigits: 10,
            precision: 0
          }
        }, {
          key: 'decPrice',
          type: 'xdoInput',
          title: '申报单价',
          props: {
            intDigits: 10,
            precision: 5
          }
        }, {
          key: 'decTotal',
          type: 'xdoInput',
          title: '申报总价',
          props: {
            intDigits: 12,
            precision: 0
          }
        }, {
          key: 'unit1',
          type: 'pcode',
          title: '法一单位',
          props: {
            meta: 'UNIT',
            disabled: true
          }
        }, {
          key: 'unit2',
          type: 'pcode',
          title: '法二单位',
          props: {
            meta: 'UNIT',
            disabled: true
          }
        }, {
          key: 'curr',
          title: '币制',
          type: 'pcode',
          props: {
            meta: 'CURR_OUTDATED'
          }
        }, {
          key: 'qty1',
          type: 'xdoInput',
          title: '法一数量',
          props: {
            intDigits: 10,
            precision: 0
          }
        }, {
          key: 'qty2',
          type: 'xdoInput',
          title: '法二数量',
          props: {
            intDigits: 10,
            precision: 0
          }
        }, {
          type: 'pcode',
          key: 'dutyMode',
          title: '征免方式',
          props: {
            meta: 'LEVYMODE'
          }
        }, {
          key: 'netWt',
          title: '净重',
          type: 'xdoInput',
          props: {
            intDigits: 13,
            precision: 0
          }
        }, {
          title: '毛重',
          key: 'grossWt',
          type: 'xdoInput',
          props: {
            intDigits: 13,
            precision: 5
          }
        }, {
          title: '体积',
          key: 'volume',
          type: 'xdoInput',
          props: {
            intDigits: 10,
            precision: 0
          }
        }, {
          key: 'entryGNo',
          type: 'xdoInput',
          title: '报关单商品序号',
          props: {
            intDigits: 2,
            precision: 0
          }
        }, {
          key: 'invoiceNo',
          title: '发票号码'
        }, {
          key: 'orderNo',
          props: {
            maxlength: 25
          },
          title: '采购(销售)订单号'
        }, {
          type: 'pcode',
          title: '原产国',
          key: 'originCountry',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        }, {
          type: 'select',
          title: '供应商/客户',
          key: 'supplierCode'
        }, {
          key: 'exgVersion',
          title: '单耗版本号',
          props: {
            maxlength: 8
          }
        }, {
          type: 'pcode',
          title: '目的国',
          key: 'destinationCountry',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        }, {
          key: 'district',
          title: '境内目的地',
          preRequired: true,
          type: 'group_form_line',
          fields: [{
            type: 'pcode',
            labelWidth: 0,
            props: {
              meta: 'AREA'
            },
            key: 'districtCode'
          }, {
            type: 'pcode',
            labelWidth: 0,
            props: {
              meta: 'POST_AREA'
            },
            key: 'districtPostCode'
          }]
        }, {
          key: 'copGName',
          title: '中文名称',
          props: {
            disabled: true
          }
        }, {
          key: 'copGModel',
          title: '料号申报要素'
        }, {
          key: 'linkedNo',
          props: {
            maxlength: 50
          },
          title: '提取单号'
        }, {
          key: 'lineNo',
          props: {
            maxlength: 20
          },
          title: '提取单序号'
        }, {
          props: {
            maxlength: 30
          },
          title: '成本中心',
          key: 'costCenter'
        }, {
          key: 'itemNo',
          title: '模拟项号',
          props: {
            disabled: true
          }
        }, {
          key: 'ciqNo',
          title: 'CIQ代码',
          props: {
            disabled: true
          }
        }, {
          key: 'billGNo',
          props: {
            intDigits: 5,
            precision: 0
          },
          type: 'xdoInput',
          title: '清单归并序号'
        }, {
          key: 'buyer',
          title: '采购人员',
          props: {
            maxlength: 100
          }
        }, {
          key: 'inOutNo',
          props: {
            maxlength: 100
          },
          title: '入(出)库关联单号'
        }, {
          props: {
            maxlength: 100
          },
          key: 'fsListNo',
          title: '分送编号'
        }, {
          key: 'note1',
          props: {
            maxlength: 255
          },
          title: 'Remark1'
        }, {
          key: 'note2',
          props: {
            maxlength: 255
          },
          title: 'Remark2'
        }, {
          key: 'note3',
          props: {
            maxlength: 255
          },
          title: 'Remark3'
        }, {
          title: '特许权关联单号',
          key: 'confirmRoyaltiesNo'
        }, {
          props: {
            disabled: true
          },
          title: '特许权使用费',
          key: 'confirmRoyalties'
        }, {
          props: {
            intDigits: 10,
            precision: 0
          },
          type: 'xdoInput',
          key: 'orderLineNo',
          title: '采购(销售)订单行号'
        }, {
          type: 'pcode',
          key: 'cutMode',
          title: '征免性质',
          props: {
            meta: 'LEVYTYPE'
          }
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 255
          }
        }, {
          type: 'select',
          props: {
            disabled: true
          },
          title: '数据来源',
          key: 'dataSource'
        }, {
          title: '客户料号',
          key: 'customerGNo'
        }, {
          title: '客户订单号',
          key: 'customerOrderNo'
        }, {
          title: '工费',
          props: {
            intDigits: 10,
            precision: 5
          },
          type: 'xdoInput',
          key: 'laborCost'
        }, {
          title: '料费',
          key: 'matCost',
          props: {
            intDigits: 10,
            precision: 5
          },
          type: 'xdoInput'
        }, {
          type: 'select',
          key: 'containerMd',
          title: '企业包装方式'
        }, {
          props: {
            disabled: true
          },
          title: '客供单价',
          key: 'clientPrice'
        }, {
          props: {
            disabled: true
          },
          title: '客供总价',
          key: 'clientTotal'
        }, {
          title: '件数',
          key: 'packNum',
          props: {
            intDigits: 9,
            precision: 0
          },
          type: 'xdoInput'
        }]
      },
      /**
       * 打开字段设置界面
       */
      handleSetFields() {
        let me = this
        me.$set(me.fieldsConfig, 'popShow', true)
      },
      /**
       * 更新可视字段
       // * @param fields
       */
      handleUpdateFields(/* fields */) {

      }
    }
  }
</script>

<style scoped>
  .preRequired:before {
    color: blue;
    content: '*';
    line-height: 1;
    font-size: 12px;
    margin-right: 4px;
    font-family: 'SimSun';
    display: inline-block;
  }

  /deep/ .ivu-card-head {
    text-align: left;
    padding: 3px 16px 1px 16px;
  }

  /deep/ .hasSlot .ivu-form-item-content .ivu-form-item-error-tip {
    right: 70px;
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }

  .dc-form {
    padding-top: 2px;
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
