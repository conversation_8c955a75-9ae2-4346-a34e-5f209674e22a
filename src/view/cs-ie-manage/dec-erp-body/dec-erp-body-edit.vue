<template>
  <section>
    <MerchElement :tyShow.sync="tyShow" :is-edit="isEdit"
                  :modelString="meModelString" :code-ts="frmData.codeTS" :code-name="frmData.gname"
                  @onChange="handleGModelChange"></MerchElement>
    <XdoCard :bordered="false" class="xdo-enter-root" v-focus>
      <XdoForm ref="bodyEditFrom" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isBonded" prop="emsNo" ref="fiEmsNo" label="备案号">
          <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoList" disabled></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"  prop="batchNo" ref="fiBatchNo" label="批次号">
          <XdoIInput type="text" v-model="frmData.batchNo" disabled></XdoIInput>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="gmark" label="物料种类">
          <xdo-select  v-model="frmData.gmark" :options="this.cmbSource.gmarkList"
                       :optionLabelRender="pcodeRender" :disabled="gMarkDisable || disableByTradeMode"></xdo-select>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="facGNo" label="企业料号">
          <xdo-select v-model="frmData.facGNo" :options="this.cmbSource.facGNoSource"
                      :optionLabelRender="(item) => item.label" :disabled="showDisable"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isBonded" prop="copGNo" ref="fiCopGNo" label="备案料号">
          <XdoIInput type="text" v-model="frmData.copGNo" disabled></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isBonded" prop="gno" ref="fiGNo" label="备案序号">
          <XdoIInput type="text" v-model.trim="frmData.gno" placeholder="" disabled></XdoIInput>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="codeTS" label="商品编码">
          <XdoIInput type="text" v-model="frmData.codeTS" :disabled='disabledByCompany' @on-blur="codeTSEnter"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="gname" label="商品名称">
          <XdoIInput type="text" v-model="frmData.gname" :disabled='disabledByCompany'></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isImport" prop="originCountry" label="原产国">
          <xdo-select v-model="frmData.originCountry" :disabled="disableICountry"
                      :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="!isImport" prop="destinationCountry" label="目的国">
          <xdo-select v-model="frmData.destinationCountry" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="unit" label="计量单位">
          <xdo-select v-model="frmData.unit" :disabled="frmData.unit ? (!['0844','0845','0400','0864','0865'].includes(frmData.tradeMode)) : false" :asyncOptions="pcodeList" :meta="pcode.unit" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="gmodel" label="申报规格型号" class="dc-merge-2-4">
          <Input type="text" v-model="frmData.gmodel" :disabled="showDisable || canEdit_gmodel">
            <span slot="append" style="padding-right: 12px"><span>{{ usedCountGModel }}/255</span></span>
            <XdoButton :disabled="canEdit_gmodel" class="enter_exclude" slot="append" @click="handleAddtype" style="border-left: 1px solid #dcdee2">规范申报</XdoButton>
          </Input>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="qty" label="申报数量">
          <xdo-input v-model="frmData.qty" decimal int-length="10" :precision="precisionsConfig.qtyDigit" notConvertNumber :disabled="showDisableRules" @on-enter="onQtyEnter"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="decPrice" label="申报单价">
          <xdo-input v-model="frmData.decPrice" decimal int-length="10" precision="5" :disabled="showDisable" @on-enter="calcTotal"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="decTotal" label="申报总价">
          <xdo-input v-model="frmData.decTotal" decimal int-length="12" :precision="precisionsConfig.decTotal" :disabled="showDisableRules" @on-enter="calcPrice"></xdo-input>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="unit1" label="法一单位">
          <XdoIInput type="text" disabled :value="this.pcodeGet(this.pcode.unit, frmData.unit1)"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="unit2" label="法二单位">
          <XdoIInput type="text" disabled :value="this.pcodeGet(this.pcode.unit, frmData.unit2)"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="curr" label="币制">
          <xdo-select v-model="frmData.curr" :disabled="showDisableRules" :asyncOptions="pcodeList" :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="qty1" label="法一数量" ref="fiQty1">
          <xdo-input v-model="frmData.qty1" decimal int-length="10" :precision="precisionsConfig.qty1Digit" notConvertNumber :disabled="showDisable"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="qty2" label="法二数量" ref="fiQty2">
          <dc-numberInput v-model="frmData.qty2" integerDigits="10" :precision="precisionsConfig.qty2Digit" :disabled="showQtyDisable"></dc-numberInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isImport" prop="destinationCountry" label="目的国">
          <xdo-select v-model="frmData.destinationCountry" :disabled="disableICountry" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="!isImport" prop="originCountry" label="原产国">
          <xdo-select v-model="frmData.originCountry" :disabled="showDisableRules" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="netWt"  ref="fiNetWt" label="净重">
          <xdo-input v-model="frmData.netWt" decimal int-length="13" :precision="precisionsConfig.netWtDigit" notConvertNumber :disabled="showDisableRules"
                     @on-enter="onNetWtEnter"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="grossWt" ref="fiGrossWt" label="毛重">
          <dc-numberInput v-model="frmData.grossWt" integerDigits="13" precision="5" notConvertNumber :disabled="showDisableRules"></dc-numberInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="volume" label="体积">
          <xdo-input v-model="frmData.volume" decimal int-length="10" :precision="precisionsConfig.volume" :disabled="showDisable"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="tradeMode" ref="fiTradeMode" label="监管方式">
          <xdo-select v-model="frmData.tradeMode" disabled
                      :options="this.filterTradeMode" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="copMark" label="企业物料类型">
          <xdo-select  v-model="frmData.copMark" :options="this.cmbSource.copMarkList"
                       :optionLabelRender="pcodeRender" :disabled="gMarkDisable || disableByTradeMode"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="entryGNo" label="报关单商品序号" ref="fiEntryGNo">
          <xdo-input v-model="frmData.entryGNo" decimal int-length="2" precision="0" :disabled="entryGNoDisable"></xdo-input>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="invoiceNo" label="发票号码">
          <XdoIInput type="text" v-model="frmData.invoiceNo" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="packNum" ref="fiPackNum" label="件数">
          <xdo-input v-model="frmData.packNum" number int-length="9" precision="0" :disabled="showDisable"></xdo-input>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dutyMode" label="征免方式">
          <xdo-select v-model="frmData.dutyMode" :disabled="disableByTradeMode || dutyModeDisabled" :meta="pcode.levymode"
                      :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="districtCode" :label="districtCodeLabel" ref="fiDistrictCode" class="dc-merge-2-4">
          <AreaPostCascader :options="areaOptions" @onAreaDataChanged="onAreaDataChanged" :disabled="showDisable"></AreaPostCascader>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="copGName" label="中文名称">
          <XdoIInput type="text" v-model="frmData.copGName" disabled></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="copGModel" label="料号申报要素" class="dc-merge-2-4">
          <Input type="text" v-model="frmData.copGModel" :disabled="showDisable || canEdit_copGModel">
            <span slot="append" style="padding-right: 12px;"><span>{{ usedCountCopGModel }}/255</span></span>
            <XdoButton  :disabled="canEdit_copGModel" class="enter_exclude" slot="append" @click="handleAddtypeCn" style="border-left: 1px solid #dcdee2;">规范申报</XdoButton>
          </Input>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="supplierCode" :label="supplierCodeLabel">
          <xdo-select v-model="frmData.supplierCode" :options="this.cmbSource.supplierCodeList"
                      :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="exgVersion" label="单耗版本号" ref="exgVersion">
          <XdoIInput type="text" v-model="frmData.exgVersion" :maxlength="8" :disabled="showDisable || isDisableExgVersion"></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="itemNo" label="模拟项号">
          <XdoIInput type="text" v-model="frmData.itemNo" disabled></XdoIInput>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="ciqNo" label="CIQ代码">
          <XdoIInput type="text" v-model="frmData.ciqNo" disabled></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="billGNo" ref="fiBillGNo" label="清单归并序号">
          <xdo-input v-model="frmData.billGNo" number int-length="5" :disabled="billGNoDisabled"></xdo-input>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="linkedNo" label="提取单号">
          <XdoIInput type="text" v-model="frmData.linkedNo" :disabled="showDisable" :maxlength="50"></XdoIInput>
        </DcFormItem>

        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="lineNo" label="提取单序号">
          <XdoIInput type="text" v-model="frmData.lineNo" :disabled="showDisable" :maxlength="20"></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E' || bondMark === '0'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"  prop="costCenter" label="成本中心">
          <XdoIInput type="text" v-model="frmData.costCenter" :disabled="showDisable" :maxlength="30"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isImport" prop="buyer" label="采购人员">
          <XdoIInput type="text" v-model="frmData.buyer" :disabled="showDisable" :maxlength="100"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="bondMark === '0'" prop="costCenter" label="成本中心">
          <XdoIInput type="text" v-model="frmData.costCenter" :disabled="showDisable" :maxlength="30"></XdoIInput>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="!isImport" prop="customerGNo" label="客户料号">
          <XdoIInput type="text" v-model="frmData.customerGNo" :disabled="showDisable" :maxlength="25"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="!isImport" prop="customerOrderNo" label="客户订单号">
          <XdoIInput type="text" v-model="frmData.customerOrderNo" :disabled="showDisable" :maxlength="100"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="inOutNo" :label="inOutNoLabel">
          <XdoIInput type="text" v-model="frmData.inOutNo" :disabled="showDisable" :maxlength="100"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isImportBonded" prop="fsListNo" label="分送编号">
          <XdoIInput type="text" v-model="frmData.fsListNo" :disabled="showDisable" :maxlength="100"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="dataSource" label="数据来源">
          <xdo-select v-model="frmData.dataSource" :options="this.importExportManage.DATA_SOURCE_MAP"
                      :optionLabelRender="pcodeRender" disabled></xdo-select>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" v-if="isImportUnBonded">
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note1" label="Remark1">
          <XdoIInput type="text" v-model="frmData.note1" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note2" label="Remark2">
          <XdoIInput type="text" v-model="frmData.note2" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note3" label="Remark3">
          <XdoIInput type="text" v-model="frmData.note3" :disabled="showDisable"></XdoIInput>
        </DcFormItem>

        <DcFormItem  :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="confirmRoyaltiesNo" label="特许权关联单号">
          <XdoIInput type="text" v-model="frmData.confirmRoyaltiesNo" :disabled="showDisable" @on-enter="onConfirmRoyaltiesNoEnter"></XdoIInput>
        </DcFormItem>
        <DcFormItem  :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="confirmRoyalties" label="特许权使用费">
          <xdo-select v-model="frmData.confirmRoyalties" :options="this.taxExemptionEquipment.ROYALTY_MAP"
                      :optionLabelRender="pcodeRender" disabled></xdo-select>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'I'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="orderNo" :label="orderNoLabel">
          <XdoIInput type="text" v-model="frmData.orderNo" :maxlength="100" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="containerMd" label="企业包装方式">
          <xdo-select v-model="frmData.containerMd" :options="this.cmbSource.containerMdData"
                      :optionLabelRender="(item) =>item.value" :disabled="showDisable"></xdo-select>
        </DcFormItem>

        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="laborCost" label="工费">
          <dc-numberInput v-model="frmData.laborCost" integerDigits="10" :precision="5" :disabled="showDisable"></dc-numberInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="matCost" label="料费">
          <dc-numberInput v-model="frmData.matCost" integerDigits="10" :precision="5" :disabled="showDisable"></dc-numberInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="orderNo" :label="orderNoLabel">
          <XdoIInput type="text" v-model="frmData.orderNo" :maxlength="100" :disabled="showDisable"></XdoIInput>
        </DcFormItem>

        <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note" label="备注" class="dc-merge-1-3">
          <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'I'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="orderLineNo" label="采购订单行号">
          <dc-numberInput v-model="frmData.orderLineNo" integerDigits="10" precision="0" :disabled="showDisable"></dc-numberInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="orderLineNo" label="销售订单行号">
          <dc-numberInput v-model="frmData.orderLineNo" integerDigits="10" precision="0" :disabled="showDisable"></dc-numberInput>
        </DcFormItem>

        <DcFormItem v-if="exportBonded" prop="clientPrice" label="客供单价">
          <XdoIInput type="text" v-model="frmData.clientPrice" disabled></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="exportBonded" prop="clientTotal" label="客供总价">
          <XdoIInput type="text" v-model="frmData.clientTotal" disabled></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="batchNo" label="批次号">
          <XdoIInput type="text" v-model="frmData.batchNo" :maxlength="50" :disabled="showDisable"></XdoIInput>
        </DcFormItem>

        <DcFormItem v-if="iemark === 'I'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="cylinderNo" label="钢瓶号">
          <XdoIInput v-model="frmData.cylinderNo" type="text" :maxlength="255" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
        <DcFormItem v-if="iemark === 'E'" :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="bottleNumber" label="钢瓶号">
          <XdoIInput v-model="frmData.bottleNumber" type="text" :maxlength="255" :disabled="showDisable"></XdoIInput>
        </DcFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
    <XdoCard style="font-size: 12px; color: #333; padding: 10px;">
      表体数值自动取值，按以下先后顺序及逻辑：
      <ul style="list-style: none;">
        <li>1、法一、法二单位等于申报计量单位，法一法二数量取申报数量；</li>
        <li>2、法一、法二单位不等于申报计量单位，按物料中心维护的比例因子自动生成法一法二数量；</li>
        <li>3、法一、法二单位为035(KG)时，取净重；</li>
        <li>4、净重自动取值：用户未维护时按物料中心维护的净重转换；</li>
        <li>5、导入时，若用户填写了净重、法一、法二数量，则以企业填写的为准，若用户未填写，则按上述逻辑进行赋值</li>
      </ul>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { formatDate } from '@/libs/datetime'
  import { numberRangeValid } from '@/libs/util'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import { auditTrailDetail } from '../../cs-aeoManage/base/auditTrailDetail'
  import { regulatoryRules } from '@/view/cs-ie-manage/js/comm/regulatoryRules'
  import AreaPostCascader from '@/view/cs-ie-manage-mixins/components/area-post-cascader'
  import { isNullOrEmpty, isNumber, keepDecimal, ArrayToLocaleLowerCase } from '@/libs/util'
  import { editStatus, MerchElement, importExportManage, taxExemptionEquipment } from '@/view/cs-common'

  export default {
    name: 'decErpBodyEdit',
    mixins: [commEdit, regulatoryRules, auditTrailDetail],
    components: {
      MerchElement,
      AreaPostCascader
    },
    props: {
      aeoShow: {
        type: Boolean,
        default: false
      },
      bondMark: {
        type: String,
        require: true,
        validate: function(value) {
          return ['0', '1'].includes(value)
        }
      },
      iemark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        timer: null,
        tyShow: false,
        isMounted: false,
        currModelString: '',
        rulesHeader: {
          gno: [{required: false, type: 'number', message: '不是有效的数字!', trigger: 'blur'}],
          emsNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
          copGNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
          exgVersion: [{required: false, message: '不能为空!', trigger: 'blur'}],
          originCountry: [{required: true, message: '不能为空!', trigger: 'blur'}],
          destinationCountry: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gmark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gname: [{required: true, message: '不能为空!', trigger: 'blur'}],
          // netWt: [{type: "number", message: '不是有效的数字!'}],
          // grossWt: [{type: "number", message: '不是有效的数字!'}],
          // volume: [{type: "number", message: '不是有效的数字!'}],
          tradeMode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          codeTS: [{required: true, message: '不能为空!', trigger: 'blur'}],
          gmodel: [{required: true, message: '不能为空!', trigger: 'blur'}],
          unit: [{required: true, message: '不能为空!', trigger: 'blur'}],
          unit1: [{required: true, message: '不能为空!', trigger: 'blur'}],
          curr: [{required: true, message: '不能为空!', trigger: 'blur'}],
          qty: [{type: "number", required: true, message: '不能为空!', trigger: 'blur'}],
          decTotal: [{/*type: "number", */required: true, message: '不能为空!', trigger: 'blur'}],
          decPrice: [{/*type: "number", */required: true, message: '不能为空!', trigger: 'blur'}],
          entryGNo: [{
            required: false, type: "number", message: '不能为空!', trigger: 'blur'
          }, {
            validator: numberRangeValid, min: 1, max: 50
          }],
          dutyMode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          qty1: [{required: false, type: "number", message: '不能为空!', trigger: 'blur'}],
          qty2: [{required: false, type: "number", message: '不能为空!', trigger: 'blur'}],
          netWt: [{required: false, message: '不能为空!', trigger: 'blur'}],
          packNum: [{required: false, type: 'number',message: '不能为空!', trigger: 'blur'}],
          grossWt: [{required: false, message: '不能为空!', trigger: 'blur'}],
          billGNo: [{required: false, type: 'number', message: '不能为空', trigger: 'blur'}],
          districtCode: [{required: false, message: '不能为空!', trigger: 'blur'}],
          districtPostCode: [{required: false, message: '不能为空!', trigger: 'blur'}]
        },
        ajaxUrl: {
          getProvider: '',
          getIeInfo: csAPI.csMaterielCenter.bonded.getIeInfo,
          getFacGNolist: csAPI.materialRelationship.comm.getFacGNolist,
          getEmsNoSelect: csAPI.csProductClassify.bonded.getEmsNoSelect,
          checkRoyalityNo: csAPI.enterpriseParamsLib.royalty.checkRoyalityNo,
          getContainerMd: csAPI.csMaterielCenter.packingConfig.getContainerMd
        },
        buttons: [
          {...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'},
          {...btnComm, click: this.handleSaveContinue, icon: 'dc-btn-savecontinue', label: '保存继续'},
          {...btnComm, click: this.handleSaveClose, icon: 'dc-btn-saveclose', label: '保存关闭'},
          {...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'}
        ],
        cmbSource: {
          gmarkList: importExportManage.copMarkMap,
          copMarkList: [],
          emsNoList: [],
          emsCopEmsNo: {},
          facGNoSource: [],
          containerMdData: [],
          supplierCodeList: []
        },
        formName: 'bodyEditFrom',
        gMarkDisable: false,
        netWTFromFacNo: null,
        disabledBillGNo: false,
        dutyModeDisabled: false,
        emsNoChangedByUser: false,
        importExportManage: importExportManage,
        onAuditChangeEvent: 'onBodyAuditChanged',
        taxExemptionEquipment: taxExemptionEquipment
      }
    },
    watch: {
      iemark: {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === 'I') {
            me.$set(me.ajaxUrl, 'getProvider', csAPI.ieParams.PRD)
            me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.decErpIListN.insert)
            me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.decErpIListN.update)
            me.$set(me.ajaxUrl, 'checkDecTotal', csAPI.csImportExport.decErpIListN.checkDecTotal)
          } else if (val === 'E') {
            me.$set(me.ajaxUrl, 'getProvider', csAPI.ieParams.CLI)
            me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.decErpEListN.insert)
            me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.decErpEListN.update)
            me.$set(me.ajaxUrl, 'checkDecTotal', csAPI.csImportExport.decErpEListN.checkDecTotal)
          }
          me.getProvider()
          me.bodyRequiredCtrl()
        }
      },
      // bondMark: {
      //   immediate: true,
      //   handler: function (val) {
      //     let me = this
      //     if (val === '0') {
      //       me.getEmsNo()
      //       me.cmbSource.gmarkList = importExportManage.gmarkMap.filter((val) => {
      //         return ['E', 'I'].indexOf(val.value) > -1
      //       })
      //       me.rulesHeader.gno[0].required = true
      //       me.rulesHeader.emsNo[0].required = true
      //       me.rulesHeader.copGNo[0].required = true
      //       me.rulesHeader.destinationCountry[0].required = true
      //     } else {
      //       me.cmbSource.gmarkList = importExportManage.gmarkMap
      //       me.rulesHeader.gno[0].required = false
      //       me.rulesHeader.emsNo[0].required = false
      //       me.rulesHeader.copGNo[0].required = false
      //       me.rulesHeader.destinationCountry[0].required = false
      //     }
      //     me.$set(me.frmData, 'bondMark', val)
      //     me.bodyRequiredCtrl()
      //     me.loadFacGNoData()
      //   }
      // },
      'editConfig.editStatus': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === editStatus.SHOW) {
            me.buttons[0].needed = false
            me.buttons[1].needed = false
            me.buttons[2].needed = false
            // me.$set(me, 'cutModeDisabled', true)
          } else {
            me.buttons[0].needed = true
            me.buttons[1].needed = true
            me.buttons[2].needed = true
            // me.$set(me, 'cutModeDisabled', false)
          }
        }
      },
      'editConfig.headData.mergeType': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === '1') {
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.$set(me.frmData, 'emsNo', me.editConfig.headData.emsNo)
              me.$set(me.frmData, 'tradeMode', me.editConfig.headData.tradeMode)
            }
            me.rulesHeader.entryGNo[0].required = true
          } else {
            me.rulesHeader.entryGNo[0].required = false
          }
          me.$nextTick(() => {
            if (me.$refs['fiEntryGNo']) {
              me.$refs['fiEntryGNo'].setRules()
            }
          })
        }
      },
      'editConfig.headData.billType': {
        immediate: true,
        handler: function (billType) {
          this.billTypeChange(billType)
        }
      },
      'frmData.unit': {
        handler: function (val) {
          if (!isNullOrEmpty(val)) {
            this.qtyReady('unit')
          }
        }
      },
      'frmData.unit1': {
        handler: function (val) {
          if (!isNullOrEmpty(val)) {
            this.qtyReady('unit1')
          }
        }
      },
      'frmData.unit2': {
        handler: function (val) {
          if (!isNullOrEmpty(val)) {
            this.qtyReady('unit2')
          }
          this.bodyRequiredCtrl()
        }
      },
      'frmData.qty2': {
        handler: function (val) {
          if (isNumber(val) && typeof val === 'number') {
            this.$set(this.frmData, 'qty2', val.toString())
          }
        }
      },
      'frmData.emsNo': {
        handler: function () {
          let me = this
          if (me.bondMark === '0') {
            me.$set(me.frmData, 'tradeMode', '')
            me.$set(me, 'emsNoChangedByUser', true)
            me.resetMaterialInfo()
            me.emsNoChange()
            me.tradeModeChange(me.frmData.tradeMode)
          }
          me.loadFacGNoData()
        }
      },
      'frmData.gmark': {
        handler: function () {
          this.resetMaterialInfo()
          this.emsNoChange()
          if (this.iemark === 'E' || this.bondMark === '0'){
            this.exgVersionCtrl()
          }
          this.loadFacGNoData()
        }
      },
      'frmData.facGNo': {
        handler: function (facGNo) {
          let me = this
          if (isNullOrEmpty(facGNo)) {
            me.resetMaterialInfo()
          } else {
            me.$http.post(me.ajaxUrl.getIeInfo, {
              bondedFlag: me.bondMark,      // 保税标记
              copGNo: me.frmData.copGNo,    // 备案料号
              emsNo: me.frmData.emsNo,      // 备案号
              gmark: me.frmData.gmark,      // 物料类型
              facGNo: facGNo ? facGNo : '', // 企业料号
              iemark: me.iemark,
              headId: me.editConfig.headId
            }).then(res => {
              me.$set(me.frmData, 'gno', res.data.data.serialNo)
              if (me.frmData.copGNo !== res.data.data.copGNo) {
                me.$set(me.frmData, 'copGNo', res.data.data.copGNo)
              }
              if (me.frmData.facGNo !== res.data.data.facGNo) {
                me.$set(me.frmData, 'facGNo', res.data.data.facGNo)
              }

              me.$set(me.frmData, 'gname', res.data.data.gname)
              me.$set(me.frmData, 'codeTS', res.data.data.codeTS)

              me.$set(me.frmData, 'gmodel', res.data.data.gmodel)
              me.$set(me.frmData, 'unit', res.data.data.unit)

              me.$set(me.frmData, 'unit1', res.data.data.unit1)
              me.$set(me.frmData, 'unit2', res.data.data.unit2)
              me.$set(me.frmData, 'copGName', res.data.data.copGName)
              me.$set(me.frmData, 'copGModel', res.data.data.copGModel)
              me.$set(me.frmData, 'itemNo', res.data.data.itemNo)
              me.$set(me.frmData, 'costCenter', res.data.data.costCenter)
              me.$set(me.frmData, 'ciqNo', res.data.data.ciqNo)
              if (isNumber(res.data.data.netWt)) {
                me.$set(me, 'netWTFromFacNo', Number(res.data.data.netWt))
              } else if (!isNullOrEmpty(res.data.data.netWt) && isNumber('0' + res.data.data.netWt)) {
                me.$set(me, 'netWTFromFacNo', Number('0' + res.data.data.netWt))
              } else {
                me.$set(me, 'netWTFromFacNo', null)
              }
              me.$set(me.frmData, 'singleWeight', me.netWTFromFacNo)
              me.emsNoChange()
              if (isNullOrEmpty(me.frmData.exgVersion)) {
                me.$set(me.frmData, 'exgVersion', res.data.data.exgVersion)
              }
              if (me.iemark === 'I' && me.configData.matDataI === '1') {
                me.$set(me.frmData, 'decPrice', res.data.data.decPrice)
                me.$set(me.frmData, 'curr', res.data.data.curr)
                if (!['4400', '4600'].includes(me.editConfig.headData.tradeMode)) {
                  me.$set(me.frmData, 'originCountry', res.data.data.country)
                }
              } else if (me.iemark === 'E' && me.configData.matDataE === '1') {
                me.$set(me.frmData, 'decPrice', res.data.data.decPrice)
                me.$set(me.frmData, 'curr', res.data.data.curr)
                me.$set(me.frmData, 'destinationCountry', res.data.data.country)
              }
            }).catch(() => {
              me.resetMaterialInfo()
            })
          }
          if (me.iemark === 'E') {
            me.$nextTick(() => {
              me.getContainerMd()
            })
          }
        }
      },
      'frmData.tradeMode': {
        handler: function (tradeMode) {
          this.tradeModeChange(tradeMode)
          this.loadFacGNoData()
        }
      },
      // 以下為通關業務配置相關內容
      'configRequiredField': {
        immediate: true,
        handler: function () {
          this.bodyRequiredCtrl()
        }
      }
    },
    computed: {
      /**
       * 申报 规格型号   禁用
       * @returns {boolean}
       */
      canEdit_gmodel(){
        console.log(this.iemark,this.bondMark,this.configData)
        if  (this.iemark ==='I' && this.bondMark =='0')  {
          return  (this.configData.canEditBodyIBond ||'').split(',').includes('0')
        }

        if  (this.iemark ==='I' && this.bondMark =='1')  {
          return  (this.configData.canEditBodyINobond||'').split(',').includes('0')
        }

        if  (this.iemark ==='E' && this.bondMark =='0')  {
          return  (this.configData.canEditBodyEBond||'').split(',').includes('0')
        }

        if  (this.iemark ==='E' && this.bondMark =='1')  {
          return  (this.configData.canEditBodyENobond||'').split(',').includes('0')
        }
        return  false
      },
      /**
       * 料号申报要素   禁用
       * @returns {boolean}
       */
      canEdit_copGModel(){
        if  (this.iemark ==='I' && this.bondMark =='0')  {
          return  (this.configData.canEditBodyIBond ||'').split(',').includes('1')
        }

        if  (this.iemark ==='I' && this.bondMark =='1')  {
          return  (this.configData.canEditBodyINobond||'').split(',').includes('1')
        }

        if  (this.iemark ==='E' && this.bondMark =='0')  {
          return  (this.configData.canEditBodyEBond||'').split(',').includes('1')
        }

        if  (this.iemark ==='E' && this.bondMark =='1')  {
          return  (this.configData.canEditBodyENobond||'').split(',').includes('1')
        }
        return  false
      },
      isImport() {
        return this.iemark === 'I'
      },
      isBonded() {
        return this.bondMark === '0'
      },
      isImportBonded() {
        return this.iemark === 'I' && this.bondMark === '0'
      },
      isImportUnBonded() {
        return this.iemark === 'I' && this.bondMark === '1'
      },
      /**
       * 出口报税
       */
      exportBonded() {
        return this.iemark === 'E' && this.bondMark === '0'
      },
      supplierCodeLabel() {
        if (this.iemark === 'I') {
          return '供应商'
        } else {
          return '客户'
        }
      },
      districtCodeLabel() {
        if (this.iemark === 'I') {
          return '境内目的地'
        } else {
          return '境内货源地'
        }
      },
      inOutNoLabel() {
        if (this.iemark === 'I') {
          return '入库关联单号'
        } else {
          return '出库关联单号'
        }
      },
      isDisableExgVersion() {
        if (!isNullOrEmpty(this.frmData.emsNo) && (this.frmData.emsNo.startsWith('B') || this.frmData.emsNo.startsWith('C'))) {
          return true
        }
        return !(this.frmData.gmark === 'E' && this.bondMark === '0')
      },
      emsNoRealDisable() {
        if (this.bondMark === '0') {
          return true
        }
        if (this.editConfig.headData.mergeType === '1') {
          return true
        }
        return this.showDisable
      },
      entryGNoDisable() {
        if (this.editConfig.headData.mergeType !== '1') {
          return true
        }
        return this.showDisable
      },
      showQtyDisable() {
        if (this.showDisable) {
          return true
        }
        return isNullOrEmpty(this.frmData.unit2)
      },
      facGNoOptions() {
        let me = this
        if (me.editConfig.editStatus !== editStatus.ADD && me.emsNoChangedByUser === false) {
          me.$set(me.frmData, 'facGNo', me.editConfig.editData.facGNo)
        }
        return {
          limit: 20,
          label: 'value',
          value: 'value',
          fieldName: 'facGNo',
          ajaxUrl: me.ajaxUrl.getFacGNolist,
          params: {
            bondedFlag: me.bondMark,          // 保税标记
            // copGNo: me.frmData.copGNo,     // 备案料号
            emsNo: me.frmData.emsNo,          // 备案号
            gmark: me.frmData.gmark,          // 物料类型
            tradeMode: me.frmData.tradeMode   // 监管方式
          }
        }
      },
      doQuery() {
        if (isNullOrEmpty(this.bondMark)) {
          return false
        } else if (isNullOrEmpty(this.frmData.gmark)) {
          return false
        }
        return true
      },
      areaOptions() {
        return {
          area: {
            field: 'districtCode',
            value: this.frmData.districtCode
          },
          post: {
            field: 'districtPostCode',
            value: this.frmData.districtPostCode
          }
        }
      },
      usedCountGModel() {
        let bytesCount = 0
        let strGModel = this.frmData.gmodel
        if (isNullOrEmpty(strGModel)) {
          return bytesCount
        }
        let chars = ''
        for (let i = 0; i < strGModel.length; i++) {
          chars = strGModel.charAt(i)
          /* eslint-disable no-control-regex */
          if (/^[\u0000-\u00ff]$/.test(chars)) {
            bytesCount += 1
          } else {
            bytesCount += 2
          }
        }
        return bytesCount
      },
      usedCountCopGModel() {
        let bytesCount = 0
        let strGModel = this.frmData.copGModel
        if (isNullOrEmpty(strGModel)) {
          return bytesCount
        }
        let chars = ''
        for (let i = 0; i < strGModel.length; i++) {
          chars = strGModel.charAt(i)
          /* eslint-disable no-control-regex */
          if (/^[\u0000-\u00ff]$/.test(chars)) {
            bytesCount += 1
          } else {
            bytesCount += 2
          }
        }
        return bytesCount
      },
      meModelString() {
        if (this.currModelString === 'gmodel') {
          return this.frmData.gmodel
        } else if (this.currModelString === 'copGModel') {
          return isNullOrEmpty(this.frmData.copGModel) ? '' : this.frmData.copGModel
        } else {
          return this.frmData.gmodel
        }
      },
      disableByTradeMode() {
        let disableTradeModes = []
        if (this.iemark === 'I') {
          disableTradeModes = ['4400', '4600']
        } else {
          disableTradeModes = ['0265', '0300', '0664', '0700']
        }
        if (disableTradeModes.includes(this.frmData.tradeMode)) {
          return true
        }
        return this.showDisable
      },
      disableICountry() {
        // if (this.iemark === 'I') {
        //   return this.disableByTradeMode
        // }
        return this.showDisable
      },
      isEdit() {
        return this.editConfig.editStatus === editStatus.EDIT || this.editConfig.editStatus === editStatus.ADD
      },
      disabledByCompany() {
        if (this.bondMark === '0' && !this.showDisable) {
          if (this.iemark === 'I' && ['0844', '0845', '0864','0400', '0865'].includes(this.frmData.tradeMode)) {
            return false
          } else if (this.iemark === 'E' && ['0864', '0865'].includes(this.frmData.tradeMode)) {
            return false
          }
        }
        return true
      },
      showDisableRules() {
        if (this.bondMark === '1' &&  this.editConfig.editStatus !== editStatus.ADD && this.iemark === 'I') {
          return true
        }
        return !(this.editConfig.editStatus === editStatus.ADD || this.editConfig.editStatus === editStatus.EDIT)
      },
      /**
       * 根据EmsNo及iEMark过滤后的监管方式
       * @returns {*}
       */
      filterTradeMode() {
        return this.getTradeModeByEmsNo(this.frmData.emsNo, this.iemark)
      },
      orderNoLabel() {
        if (this.iemark === 'I') {
          return '采购订单号'
        } else if (this.iemark === 'E') {
          return '销售订单号'
        } else {
          return '采购/销售订单号'
        }
      },
      configData() {
        return this.$store.state[`${namespace}`].clearanceBusinessSetting
      },
      configRequiredField() {
        if (this.iemark === 'I') {
          if (this.bondMark === '0') {
            return this.configData.listRequiredField
          } else if (this.bondMark === '1') {
            return this.configData.listReFieldINoBond
          }
        } else if (this.iemark === 'E') {
          if (this.bondMark === '0') {
            return this.configData.listReFieldE
          } else if (this.bondMark === '1') {
            return this.configData.listReFieldENoBond
          }
        }
        return ''
      },
      precisionsConfig() {
        let me = this,
          result = {
            qtyDigit: 5,
            qty1Digit: 5,
            qty2Digit: 5,
            netWtDigit: 8,

            volume: 5,
            decTotal: 4
          }
        if (isNumber(me.configData.precisionsConfig.qtyDigit)) {
          result.qtyDigit = me.configData.precisionsConfig.qtyDigit
        }
        if (isNumber(me.configData.precisionsConfig.qty1Digit)) {
          result.qty1Digit = me.configData.precisionsConfig.qty1Digit
        }
        if (isNumber(me.configData.precisionsConfig.qty2Digit)) {
          result.qty2Digit = me.configData.precisionsConfig.qty2Digit
        }
        if (isNumber(me.configData.precisionsConfig.netWtDigit)) {
          result.netWtDigit = me.configData.precisionsConfig.netWtDigit
        }
        if (me.iemark === 'I') {
          if (isNumber(me.configData.volumeDigitI)) {
            result.volume = me.configData.volumeDigitI
          }
          if (isNumber(me.configData.decTotalDigitI)) {
            result.decTotal = me.configData.decTotalDigitI
          }
        } else {
          if (isNumber(me.configData.volumeDigitE)) {
            result.volume = me.configData.volumeDigitE
          }
          if (isNumber(me.configData.decTotalDigitE)) {
            result.decTotal = me.configData.decTotalDigitE
          }
        }
        return result
      },
      billGNoDisabled() {
        if (this.disabledBillGNo) {
          return true
        }
        return this.showDisable
      }
    },
    created() {
      let me = this
      if (me.iemark === 'E') {
        me.$nextTick(() => {
          me.getContainerMd()
        })
      }
      // 企业物料类型
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/COP_MARK').then(res => {
        me.cmbSource.copMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.cmbSource.copMarkList = []
      })

    },
    mounted: function () {
      let me = this
      me.$nextTick(() => {
        if (this.iemark === 'E' || this.bondMark === '0'){
          me.exgVersionCtrl()
        }

        if (me.editConfig.editStatus === editStatus.EDIT) {
          me.tradeModeChange(me.frmData.tradeMode, true)
        }
        me.$set(me, 'isMounted', true)
        me.loadFacGNoData()
      })
    },
    methods: {
      /**
       * 获取企业包装方式
       */
      getContainerMd() {
        let me = this
        // 备案号
        me.$http.post(me.ajaxUrl.getContainerMd, {
          iemark: 'E',
          gmark: me.frmData.gmark,
          facGNo: me.frmData.facGNo
        }).then(res => {
          let cmdData = []
          if (Array.isArray(res.data) && res.data.length > 0) {
            res.data.forEach(item => {
              if (!isNullOrEmpty(item)) {
                cmdData.push({
                  value: item,
                  label: item
                })
              }
            })
          }
          me.$set(me.cmbSource, 'containerMdData', cmdData)
        }).catch(() => {
          me.$set(me.cmbSource, 'containerMdData', [])
        })
      },
      loadFacGNoData() {
        console.log(111)
        let me = this,
          params = {
            emsNo: me.frmData.emsNo,          // 备案号
            gmark: me.frmData.gmark,          // 物料类型
            bondedFlag: me.bondMark,          // 保税标记
            tradeMode: me.frmData.tradeMode   // 监管方式
          }
        if (me.isMounted === true) {
          me.$http.post(me.ajaxUrl.getFacGNolist, params).then(res => {
            me.$set(me.cmbSource, 'facGNoSource', res.data.data.map(item => {
              return {
                value: item,
                label: item
              }
            }))
          }).catch(() => {
            me.$set(me.cmbSource, 'facGNoSource', [])
          })
        }
      },
      codeTSEnter() {
        let me = this
        // 商品编码带出计量单位
        let val = me.frmData.codeTS.trim()
        if (val.length === 10) {
          me.pcodeRemote(me.pcode.complex, val).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.frmData, 'unit1', res[0]['UNIT_1'])
              me.$set(me.frmData, 'unit2', res[0]['UNIT_2'])
            } else {
              me.resetUnit()
            }
          })
        } else if (val.length > 0) {
          me.resetUnit()
        }
      },
      resetUnit() {
        let me = this
        me.$Message.warning('商品编码不存在')
        me.$set(me.frmData, 'unit1', '')
        me.$set(me.frmData, 'unit2', '')
      },
      /**
       * 获取客户 / 获取供应商
       */
      getProvider() {
        let me = this
        me.$http.post(me.ajaxUrl.getProvider).then(res => {
          me.cmbSource.supplierCodeList = ArrayToLocaleLowerCase(res.data.data)
        }).catch(() => {
          me.cmbSource.supplierCodeList = []
        })
      },
      /**
       * 获取备案号
       */
      getEmsNo() {
        let me = this
        // 备案号
        me.$http.post(me.ajaxUrl.getEmsNoSelect).then(res => {
          let tmpArr = []
          let tmpEmsCopEmsNo = {}
          for (let item of res.data.data) {
            tmpArr.push({
              label: item.VALUE,
              value: item.VALUE
            })
            if (!isNullOrEmpty(item['COP_EMS_NO']) && !isNullOrEmpty(item.VALUE) && typeof tmpEmsCopEmsNo[item.VALUE] === 'undefined') {
              tmpEmsCopEmsNo[item.VALUE] = item['COP_EMS_NO']
            }
          }
          me.cmbSource.emsNoList = tmpArr
          me.cmbSource.emsCopEmsNo = tmpEmsCopEmsNo
        })
      },
      getCountryMode() {
        if (this.configData) {
          return this.configData.countryMode || '0'
        }
        return ''
      },
      /**
       * 获取当前单耗版本号
       * @param emsNo
       * @param gMark
       */
      getCurrExgVersion(emsNo, gMark) {
        let me = this
        if (me.configData && me.configData.checkExgVersion === '1') {
          if (me.iemark === 'E' && me.bondMark === '0' && gMark === 'E') {
            if (!isNullOrEmpty(emsNo) && emsNo.startsWith('E')) {
              let yearMonth = formatDate(me.editConfig.headData.insertTime, 'yyyyMM')
              return yearMonth + '01'
            }
          }
        }
        return ''
      },
      /**
       * 获取默认数据
       */
      getDefaultData() {
        let me = this
        let customData = {
          exgVersion: ''
        }
        let gMark = (me.iemark === 'I' ? 'I' : 'E')
        let dutyMode = ''
        let originCountry = (me.iemark === 'I' ? '' : '142')
        let destinationCountry = (me.iemark === 'I' ? '142' : '')
        if (me.iemark === 'E') {
          customData = {
            customerGNo: '',
            customerOrderNo: '',
            exgVersion: '',
            laborCost: null,
            matCost: null
          }
          if (['0265', '0300', '0664', '0700'].includes(me.editConfig.headData.tradeMode)) {
            gMark = 'I'
            dutyMode = '3'
          }
          if (me.getCountryMode() === '1') {
            destinationCountry = me.editConfig.headData.destinationCountry
          }
        } else {
          if (['4400', '4600'].includes(me.editConfig.headData.tradeMode)) {
            gMark = 'E'
            originCountry = '142'
            destinationCountry = '142'
            dutyMode = '3'
          }
        }
        if (me.bondMark === '0') {
          if (!isNullOrEmpty(me.editConfig.headData.emsNo)
            && (me.editConfig.headData.emsNo.startsWith('B') || me.editConfig.headData.emsNo.startsWith('C'))) {
            customData.exgVersion = ''
          }
        }
        if (me.editConfig.editStatus === editStatus.ADD) {
          me.$nextTick(() => {
            me.gmarkValue()
            me.tradeModeChange(me.editConfig.headData.tradeMode)
          })
        }
        if (!isNullOrEmpty(me.editConfig.editData.gmark) && gMark !== me.editConfig.editData.gmark) {
          gMark = me.editConfig.editData.gmark
        }
        let currEmsNo = (me.bondMark === '0' ? me.editConfig.headData.emsNo : '')
        customData.exgVersion = me.getCurrExgVersion(currEmsNo, gMark)
        return {
          headId: me.editConfig.headId,
          sid: '',
          bondMark: me.bondMark,
          gmark: gMark,
          emsNo: currEmsNo,
          copEmsNo: '',
          facGNo: '',
          copGNo: '',
          gno: null,
          codeTS: '',
          gmodel: '',
          gname: '',
          copGModel: '',
          originCountry: originCountry,
          destinationCountry: destinationCountry,
          unit: '',
          qty: null,
          curr: '',
          decTotal: null,
          decPrice: null,
          unit1: '',
          qty1: null,
          unit2: '',
          qty2: null,
          netWt: null,
          grossWt: null,
          volume: null,
          invoiceNo: '',
          orderNo: '',
          copGName: '',
          tradeMode: me.editConfig.headData.tradeMode,
          entryGNo: null,
          supplierCode: me.editConfig.headData.overseasShipper,
          supplierName: '',
          dutyMode: dutyMode,
          ...customData,
          note: '',
          note1: '',
          note2: '',
          note3: '',
          districtCode: me.editConfig.headData.districtCode,
          districtPostCode: me.editConfig.headData.districtPostCode,
          linkedNo: '',
          lineNo: '',
          costCenter: '',
          itemNo: '',
          billGNo: null,
          ciqNo: '',
          buyer: '',
          inOutNo: '',
          fsListNo: '',
          dataSource: '',
          singleWeight: null,
          confirmRoyaltiesNo: '',
          confirmRoyalties: '',
          orderLineNo: null,
          // cutMode: ''
          containerMd: '',
          clientPrice: '',
          clientTotal: '',
          packNum: null,
          batchNo: '',
          cylinderNo: '',
          copMark: '',
        }
      },
      /**
       * 物料赋值
       */
      gmarkValue() {
        this.frmData.gmark = this.editConfig.headData.gmark  //表头带入物料类型
      },
      /**
       * 设置单耗版本号是否必输
       */
      exgVersionCtrl() {
        this.rulesHeader.exgVersion[0].required = !this.isDisableExgVersion
        this.$refs.exgVersion.setRules()
      },
      /**
       * 重置初始化物料信息
       */
      resetMaterialInfo() {
        let me = this
        me.$set(me.frmData, 'gno', null)
        me.$set(me.frmData, 'copGNo', '')
        me.$set(me.frmData, 'facGNo', '')

        me.$set(me.frmData, 'gname', '')
        me.$set(me.frmData, 'codeTS', '')
        me.$set(me.frmData, 'gmodel', '')
        me.$set(me.frmData, 'unit', '')
        me.$set(me.frmData, 'unit1', '')
        me.$set(me.frmData, 'unit2', '')
        me.$set(me.frmData, 'copGName', '')
        me.$set(me.frmData, 'copGModel', '')
        me.$set(me.frmData, 'exgVersion', me.getCurrExgVersion(me.frmData.emsNo, me.frmData.gmark))
        me.$set(me.frmData, 'ciqNo', '')
        if (me.iemark === 'I' && me.configData.matDataI === '1') {
          me.$set(me.frmData, 'decPrice', '')
          me.$set(me.frmData, 'curr', '')
          me.$set(me.frmData, 'originCountry', '')
        } else if (me.iemark === 'E' && me.configData.matDataE === '1') {
          me.$set(me.frmData, 'decPrice', '')
          me.$set(me.frmData, 'curr', '')
          me.$set(me.frmData, 'destinationCountry', '')
        }
      },
      /**
       * 弹出规格型号
       */
      handleAddtype() {
        this.currModelString = 'gmodel'
        this.tyShow = true
      },
      handleAddtypeCn() {
        this.currModelString = 'copGModel'
        this.tyShow = true
      },
      handleGModelChange(val) {
        if (this.currModelString === 'gmodel') {
          this.$set(this.frmData, 'gmodel', val)
        } else if (this.currModelString === 'copGModel') {
          this.$set(this.frmData, 'copGModel', val)
        } else {
          this.$set(this.frmData, 'gmodel', val)
        }
      },
      /**
       * 备案号变更
       */
      emsNoChange() {
        let me = this
        me.$set(me.frmData, 'exgVersion', me.getCurrExgVersion(me.frmData.emsNo, me.frmData.gmark))
      },
      /**
       * 监管方式变更
       * @param value
       * @param isInit
       */
      tradeModeChange(value, isInit) {
        let me = this
        // if (!me.showDisable) {
        //   me.$set(me, 'cutModeDisabled', false)
        // }
        // 同表头新规则
        let regulatoryRule = me.getRegulatoryRule(me.frmData.emsNo, value, me.iemark)
        if (!isNullOrEmpty(regulatoryRule.gMark)) {
          me.$set(me.frmData, 'gmark', regulatoryRule.gMark)
        }
        me.$set(me, 'gMarkDisable', regulatoryRule.gMarkDisable)
        // if (!isNullOrEmpty(regulatoryRule.cutMode)) {
        //   me.$set(me.frmData, 'cutMode', regulatoryRule.cutMode)
        // }
        // if (!me.showDisable) {
        //   me.$set(me, 'cutModeDisabled', regulatoryRule.cutModeDisable)
        // }
        // 表体扩展规则
        let bodyRule = me.getDefaultByRule(value, me.iemark)
        if (isInit !== true) {
          if (!me.gMarkDisable) {
            if (isNullOrEmpty(me.frmData.gmark)) {
              me.$set(me.frmData, 'gmark', bodyRule.gMark)
            }
          }
          me.$set(me.frmData, 'originCountry', bodyRule.originCountry)
          if (isNullOrEmpty(me.frmData.destinationCountry)) {
            me.$set(me.frmData, 'destinationCountry', bodyRule.destinationCountry)
          }
          me.$set(me.frmData, 'dutyMode', bodyRule.dutyMode)
        }
        me.$set(me, 'dutyModeDisabled', bodyRule.dutyModeDisabled)
      },
      /**
       * 设置保存按钮加载样式
       * @param loading
       */
      setBtnSaveLoading(loading) {
        this.buttons[0].loading = loading
        this.buttons[1].loading = loading
        this.buttons[2].loading = loading
        console.info('btnLoading: ' + loading)
      },
      /**
       * 根据单价计算总价
       */
      calcTotal() {
        let me = this
        if (isNumber(me.frmData.decPrice) && isNumber(me.frmData.qty)) {
          let price = Number(me.frmData.decPrice)
          let qty = Number(me.frmData.qty)
          me.$set(me.frmData, 'decTotal', keepDecimal(price * qty, me.precisionsConfig.decTotal))
        }
      },
      /**
       * 根据总价计算单价
       */
      calcPrice() {
        let me = this
        if (isNumber(me.frmData.decTotal) && isNumber(me.frmData.qty)) {
          let total = Number(me.frmData.decTotal)
          let qty = Number(me.frmData.qty)
          me.$set(me.frmData, 'decPrice', keepDecimal(total / qty, 5))
        }
      },
      /**
       * 根据序号设置法定数量的默认值
       * @param qtyIndex
       */
      qtyAutoFill(qtyIndex) {
        if (qtyIndex !== 1 && qtyIndex !== 2) {
          return
        }
        let qtyField = 'qty' + qtyIndex.toString()
        let unitField = 'unit' + qtyIndex.toString()
        let destField = 'dest' + qtyIndex.toString()
        let me = this
        if (!isNullOrEmpty(me.frmData.unit) && !isNullOrEmpty(me.frmData[unitField])) {
          if (isNumber(me.frmData.qty)) {
            const rateParams = {
              //sid: me.frmData.sid,
              bondMark: me.frmData.bondMark,
              gmark: me.frmData.gmark,
              emsNo: me.frmData.emsNo,
              facGNo: me.frmData.facGNo,
              origin: me.frmData.unit
            }
            rateParams[destField] = me.frmData[unitField]
            if (me.frmData.unit === me.frmData[unitField]) {
              if (qtyIndex === 1) {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty, me.precisionsConfig.qty1Digit))
              } else {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty, me.precisionsConfig.qty2Digit))
              }
              return
            }
            me.$http.post(csAPI.csProductClassify.bonded.getFactorRate, rateParams).then(res => {
              if (res.data.data) {
                if (qtyIndex === 1) {
                  me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data, me.precisionsConfig.qty1Digit))
                } else {
                  me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data, me.precisionsConfig.qty2Digit))
                }
                return
              }
              const params = {
                origin: me.frmData.unit,
                dest: me.frmData[unitField],
                type: 'UNIT'
              }
              me.$http.post(csAPI.ieParams.getTransformInfo, params).then(res => {
                if (res.data.data && !isNullOrEmpty(res.data.data.rate)) {
                  if (qtyIndex === 1) {
                    me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data.rate, me.precisionsConfig.qty1Digit))
                  } else {
                    me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data.rate, me.precisionsConfig.qty2Digit))
                  }
                } else {
                  if (me.frmData[unitField] === '035' && isNumber(me.frmData.netWt)) {
                    if (qtyIndex === 1) {
                      me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty1Digit))
                    } else {
                      me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty2Digit))
                    }
                  }
                }
              })
            }).catch(() => {
            })
          } else {
            if (me.frmData[unitField] === '035' && isNumber(me.frmData.netWt)) {
              if (qtyIndex === 1) {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty1Digit))
              } else {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty2Digit))
              }
            }
          }
        }

        /*if (!isNumber(this.frmData[qtyField])) {
          let unitField = 'unit' + qtyIndex.toString()
          if (this.frmData[unitField] === '035' && isNumber(this.frmData.netWt)) {
            this.$set(this.frmData, qtyField, this.frmData.netWt)
          } else if (this.frmData.unit === '007' && this.frmData[unitField] === '026' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty / 2)
          } else if (this.frmData.unit === '007' && this.frmData[unitField] === '054' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty / 1000)
          } else if (this.frmData.unit === '036' && this.frmData[unitField] === '035' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty / 1000)
          } else if (!isNullOrEmpty(this.frmData.unit) && this.frmData.unit === this.frmData[unitField] && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty)
          }
          else if (this.frmData.unit === '026' && this.frmData[unitField] === '007' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty * 2)
          } else if (this.frmData.unit === '054' && this.frmData[unitField] === '007' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty * 1000)
          } else if (this.frmData.unit === '035' && this.frmData[unitField] === '036' && isNumber(this.frmData.qty)) {
            this.$set(this.frmData, qtyField, this.frmData.qty * 1000)
          }
        }*/
      },
      /**
       * 数据准备
       * 通用规则：申报单位          法一(二)单位          法一(二)数量取值
       *           相同                相同                 申报数量
       *                               035                  净重
       *          007 个              026 对             申报数量 / 2
       *          007 个              054 千个           申报数量 / 1000
       *          036 克              035 千克           申报数量 / 1000
       *          026 对              007 个             申报数量 * 2
       *          054 千个            007 个             申报数量 * 1000
       *          035 千克            036 克             申报数量 * 1000
       */
      qtyReady(fieldName) {
        if (isNullOrEmpty(fieldName)) {
          this.qtyAutoFill(1)
          this.qtyAutoFill(2)
        } else {
          if (fieldName === 'unit') {
            this.qtyAutoFill(1)
            this.qtyAutoFill(2)
          } else if (fieldName === 'unit1') {
            this.qtyAutoFill(1)
          } else if (fieldName === 'unit2') {
            this.qtyAutoFill(2)
          } else if (fieldName === 'netWt') {
            this.qtyAutoFill(1)
            this.qtyAutoFill(2)
          } else if (fieldName === 'qty') {
            this.qtyAutoFill(1)
            this.qtyAutoFill(2)
          }
        }
      },
      debounce(fn, delay) {
        let me = this
        return function () {
          clearTimeout(me.timer)
          let call = !me.timer
          if (call) {
            fn.apply(this, ['qty'])
          }
          me.timer = setTimeout(function () {
            me.timer = false
          }, delay)
        }
      },
      /**
       * 输入申报数量后回车事件
       */
      onQtyEnter() {
        let me = this
        if (isNumber(me.frmData.qty)) {
          me.debounce(me.qtyReady, 2000)()
          //me.qtyReady('qty')
          if (isNumber(me.frmData.singleWeight)) {
            if (!isNumber(me.frmData.netWt)) {
              me.$set(me.frmData, 'netWt', keepDecimal(me.frmData.singleWeight * me.frmData.qty, me.precisionsConfig.netWtDigit)+'')
              // me.$set(me.frmData, 'netWt', keepDecimal(me.netWTFromFacNo * me.frmData.qty, 5))
            }
          }
        }
      },
      /**
       * 输入净重后回车事件
       */
      onNetWtEnter() {
        if (isNumber(this.frmData.netWt)) {
          this.qtyReady('netWt')
        }
      },
      /**
       * 境内货源地/境内目的地
       * @param areaObj
       */
      onAreaDataChanged(areaObj) {
        this.$set(this.frmData, 'districtCode', areaObj['districtCode'])
        this.$set(this.frmData, 'districtPostCode', areaObj['districtPostCode'])
      },
      onConfirmRoyaltiesNoEnter() {
        let me = this,
          royaltiesNo = me.frmData.confirmRoyaltiesNo
        if (!isNullOrEmpty(royaltiesNo)) {
          me.$http.post(me.ajaxUrl.checkRoyalityNo, {
            iemark: me.iemark,
            royalityNo: royaltiesNo
          }).then(res => {
            if (res.data.data === true) {
              me.$set(me.frmData, 'confirmRoyalties', '1')
            } else {
              me.$set(me.frmData, 'confirmRoyalties', '')//'0')
            }
          }, () => {
          })
        } else {
          me.$set(me.frmData, 'confirmRoyalties', '')
        }
      },
      /**
       * 保存前处理【客户/供应商】及copEmsNo值
       */
      onBeforeSave() {
        let me = this
        let theItems = me.cmbSource.supplierCodeList.filter(item => {
          return item.value === me.frmData.supplierCode
        })
        if (Array.isArray(theItems) && theItems.length > 0) {
          me.$set(me.frmData, 'supplierName', theItems[0].label)
        } else {
          me.$set(me.frmData, 'supplierName', '')
        }
        if (me.isBonded && !isNullOrEmpty(me.frmData.emsNo)) {
          me.$set(me.frmData, 'copEmsNo', me.cmbSource.emsCopEmsNo[me.frmData.emsNo])
        } else {
          me.$set(me.frmData, 'copEmsNo', '')
        }
        // 特殊处理数量、单价、总价
        if (isNumber(me.frmData.qty)) {
          me.$set(me.frmData, 'qty', Number(keepDecimal(me.frmData.qty, me.precisionsConfig.qtyDigit)))
        }
        if (isNumber(me.frmData.qty1)) {
          me.$set(me.frmData, 'qty1', Number(me.frmData.qty1))
        }
        if (isNumber(me.frmData.qty2)) {
          me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
        }
        if (isNumber(me.frmData.decTotal)) {
          me.$set(me.frmData, 'decTotal', me.frmData.decTotal.toString())
        }
        if (isNumber(me.frmData.decPrice)) {
          me.$set(me.frmData, 'decPrice', me.frmData.decPrice.toString())
        }
        // if (isNumber(me.frmData.qty2)) {
        //   me.$set(me.frmData, 'qty2', me.frmData.qty2.toString())
        // }
      },
      onValidQty1() {
        let me = this
        return me.bondMark === '0' && isNullOrEmpty(me.frmData.qty1)
      },
      saveConfirm(funSave) {
        let me = this
        me.onBeforeSave()
        me.$refs['bodyEditFrom'].validate().then(isValid => {
          if (isValid) {
            me.setBtnSaveLoading(true)
            me.$http.post(me.ajaxUrl.checkDecTotal, me.frmData).then(res => {
              if (res.data.message === '0') {
                if (isNumber(me.frmData.qty2)) {
                  me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
                }
                funSave.call(me)
              } else {
                me.setBtnSaveLoading(false)
                me.$Modal.confirm({
                  title: '提醒',
                  loading: true,
                  okText: '确定',
                  cancelText: '取消',
                  content: '数量*单价≠总价，是否保存',
                  onOk: () => {
                    if (isNumber(me.frmData.qty2)) {
                      me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
                    }
                    funSave.call(me)
                    setTimeout(() => {
                      me.$Modal.remove()
                    }, 150)
                  }
                })
              }
            })
          }
        })
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        me.saveConfirm(() => {
          me.doSave(res => {
            me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          })
        })
      },
      /**
       * 保存继续
       */
      handleSaveContinue() {
        let me = this
        me.saveConfirm(() => {
          me.doSave(() => {
            me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
          })
        })
      },
      /**
       * 保存关闭
       */
      handleSaveClose() {
        let me = this
        me.saveConfirm(() => {
          me.doSave(() => {
            me.refreshIncomingData(true, editStatus.SHOW, me.getDefaultData())
          })
        })
      },
      /**
       * 表头清单归并类型变更
       */
      billTypeChange(billType) {
        let me = this
        if (billType === '4') {
          me.rulesHeader.billGNo[0].required = true
          me.disabledBillGNo = false
        } else {
          me.rulesHeader.billGNo[0].required = false
          me.disabledBillGNo = true
        }
        me.$nextTick(() => {
          if (me.$refs['fiBillGNo']) {
            me.$refs['fiBillGNo'].setRules()
          }
        })
      },
      /**
       * 判断法一法二数量是否必填
       */
      bodyRequiredCtrl() {
        let me = this,
          reqFields = [],
          requires = me.configRequiredField
        if (!isNullOrEmpty(requires)) {
          reqFields = requires.split(',')
        }
        me.rulesHeader.qty1[0].required = false
        me.rulesHeader.qty2[0].required = false

        me.rulesHeader.districtCode[0].required = false
        me.rulesHeader.districtPostCode[0].required = false

        me.rulesHeader.netWt[0].required = false
        me.rulesHeader.grossWt[0].required = false
        me.rulesHeader.packNum[0].required = false

        if (me.bondMark === '0') {
          me.rulesHeader.qty1[0].required = true
          if (me.iemark === 'I') {
            if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
              me.rulesHeader.qty2[0].required = true
            }
          } else {
            if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
              me.rulesHeader.qty2[0].required = true
            }
          }
        } else {
          if (me.iemark === 'I') {
            if (reqFields.includes('qty1')) {
              me.rulesHeader.qty1[0].required = true
            }
            if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
              me.rulesHeader.qty2[0].required = true
            }
          } else {
            if (reqFields.includes('qty1')) {
              me.rulesHeader.qty1[0].required = true
            }
            if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
              me.rulesHeader.qty2[0].required = true
            }
          }
        }
        if (me.iemark === 'I') {
          if (reqFields.includes('districtCode')) {
            me.rulesHeader.districtCode[0].required = true
            me.rulesHeader.districtPostCode[0].required = true
          }
        }
        if (reqFields.includes('netWt')) {
          me.rulesHeader.netWt[0].required = true
        }
        if (reqFields.includes('grossWt')) {
          me.rulesHeader.grossWt[0].required = true
        }
        if (reqFields.includes('packNum')) {
          me.rulesHeader.packNum[0].required = true
        }
        me.$nextTick(() => {
          if (me.$refs.fiQty1) {
            me.$refs.fiQty1.setRules()
          }
          if (me.$refs.fiQty2) {
            me.$refs.fiQty2.setRules()
          }
          if (me.$refs.fiDistrictCode) {
            me.$refs.fiDistrictCode.setRules()
          }
          if (me.$refs.fiNetWt) {
            me.$refs.fiNetWt.setRules()
          }
          if (me.$refs.fiGrossWt) {
            me.$refs.fiGrossWt.setRules()
          }
          if (me.$refs.fiPackNum) {
            me.$refs.fiPackNum.setRules()
          }
        })
      },
      /**
       * 根据企业编号修改信息内容(用于表体)
       * @param message
       * @returns {*}
       */
      setMsgByFacGNo(message) {
        let me = this
        if (!isNullOrEmpty(message) && !isNullOrEmpty(me.frmData.facGNo)) {
          return '企业料号: ' + me.frmData.facGNo + ' 存在问题: ' + message
        }
        return message
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .audit-view label.ivu-form-item-label {
    background-color: greenyellow;
  }

  /deep/ .audit-error label.ivu-form-item-label {
    color: white;
    background-color: red;
  }
</style>
