import pms from '@/libs/pms'
import { csAPI, pdfExport, zipExport } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { getGridExportColumns } from '@/view/cs-common/function'
import { editStatus, importExportManage } from '@/view/cs-common'
import BillErrMsg from '@/view/cs-common/components/bill-err-msg'
import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import BillIESearch from '@/view/cs-ie-manage/bill/components/bill-ie-search'
import BillDetailSingle from '@/view/cs-ie-manage/components/bill-detail/bill-detail-single'
import { columnsConfig, excelColumnsConfig, columns } from '@/view/cs-ie-manage/js/bill/billIEColumns'
import BillSync from '@/view/cs-ie-manage/bill/components/billSync'

export const  billIEList = {
  components: {
    BillErrMsg,
    <PERSON><PERSON><PERSON>earch,
    BillDetailSing<PERSON>,
    BillSync
  },
  mixins: [commList, columns, pms],
  data() {
    return {
      searchLines: 4,
      ajaxUrl: {
        getSupplierCodeUrl: ''
      },
      errMsgInfo: {
        sid: '',
        sendApiTaskId: '',
        modelErrMsgShow: false,
        loadUrl: csAPI.emsApi.getSendErrInfo
      },
      importExportManage: importExportManage,
      cmbSource: {
        overseasShipperList: [],
        feeMarkList: importExportManage.feeTypeMap
      },
      toolbarEventMap: {
        'send-bill': this.onSendBill,
        'export': this.handleDownload,
        'revoke-bill': this.onRevokeBill,
        'table-column-setup': this.handleTableColumnSetup,
        'bill-sync': this.onBillSync
      }
    }
  },
  created() {
    let me = this
    if (me.iemark === 'I') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.PRD)
    } else if (me.iemark === 'E') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.CLI)
    } else {
      console.info('请为清单表体查询界面设置进出口标记【ieMark】!')
    }
    // 境外收/发货人
    me.$http.post(me.ajaxUrl.getSupplierCodeUrl).then(res => {
      me.cmbSource.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
    }).catch(() => {
      me.cmbSource.overseasShipperList = []
    })
  },
  mounted: function () {
    let me = this
    let grdCols = getColumnsByConfig(me.totalColumns, columnsConfig)
    let exportCols = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)

    for (let gCol of grdCols) {
      me.reSetColumnTitle(gCol, 'overseasShipper', '境外发货人', '境外收货人')
      me.reSetColumnTitle(gCol, 'ieport', '进境关别', '出境关别')
      me.reSetColumnTitle(gCol, 'tradeCountry', '启运国(地区)', '运抵国(地区)')
    }
    for (let eCol of exportCols) {
      me.reSetColumnTitle(eCol, 'overseasShipperName', '境外发货人', '境外收货人')
      me.reSetColumnTitle(eCol, 'ieport', '进境关别', '出境关别')
      me.reSetColumnTitle(eCol, 'tradeCountry', '启运国(地区)', '运抵国(地区)')
    }
    me.tableId = me.$route.path
    me.totalColumns.forEach(item => {
      if (item.key !== 'selection' && item.key !== 'operation') {
        me.columnsarr.push(item)
      }
    })
    let columns = me.$bom3.showTableColumns(me.tableId, me.totalColumns)
    me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
    me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
    getGridExportColumns(me.alltotalColumns).forEach(item => {
      if (item.key !== 'selection' && item.key !== 'operation') {
        me.gridConfig.exportColumns.push(item)
      }
    })
    me.loadFunctions().then()
  },
  methods: {
    /**
     * 重新设置列表题
     * @param column
     * @param colKey
     * @param iTitle
     * @param eTitle
     */
    reSetColumnTitle(column, colKey, iTitle, eTitle) {
      let me = this
      if (column.key === colKey) {
        if (me.iemark === 'I') {
          column.title = iTitle
        } else if (me.iemark === 'E') {
          column.title = eTitle
        }
      }
    },
    /**
     * 生成核注清单
     */
    onSendBill() {
      let me = this,
        sendApiStatus = me.gridConfig.selectRows.map(item => {
          return item.sendApiStatus
        })
      if (sendApiStatus.includes('1')) {
        me.$Message.warning('发送成功清单不允许重复发送!')
      } else if (sendApiStatus.includes('J1')) {
        me.$Message.warning('通过清单不允许重复发送!')
      } else if (sendApiStatus.includes('J3')) {
        me.$Message.warning('退单不允许发送!')
      } else if (sendApiStatus.includes('JDL')) {
        me.$Message.warning('删单不允许发送!')
      } else {
        if (me.checkRowSelected('生成核注清单')) {
          me.$Modal.confirm({
            title: '提醒',
            loading: true,
            okText: '确定',
            cancelText: '取消',
            content: '确认生成核注清单吗',
            onOk: () => {
              me.setToolbarLoading('send-bill', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.sendBills}/${params}/${me.iemark}`).then(() => {
                me.$Message.success('生成核注清单成功!')
              }).catch(() => {
              }).finally(() => {
                me.getList()
                me.setToolbarLoading('send-bill')
                setTimeout(() => {
                  me.$Modal.remove()
                }, 150)
              })
            }
          })
        }
      }
    },
    onBillSync() {
      let me = this
      me.show = true
    },
    /**
     * 清单撤回
     */
    onRevokeBill() {
      let me = this
      if (me.checkRowSelected('撤回', true)) {
        let sendApiStatus = me.gridConfig.selectRows.map(item => {
          return item.sendApiStatus
        })
        if (sendApiStatus.includes('-1')) {
          me.$Message.warning('未发送清单不允许撤回!')
        } else if (sendApiStatus.includes('2')) {
          me.$Message.warning('发送中清单不允许撤回!')
        } else if (sendApiStatus.includes('3')) {
          me.$Message.warning('撤回的清单不允许再撤回!')
        } else {
          let sendedStatus = me.gridConfig.selectRows.filter(item => {
            return ['0', '1', 'A0', 'D1', 'DL', 'Z', 'J3'].includes(item.sendApiStatus)
          })
          if (sendedStatus.length < me.gridConfig.selectRows.length) {
            me.$Message.warning('当前状态的清单不允许撤回!')
          } else {
            me.$Modal.confirm({
              title: '提醒',
              loading: true,
              okText: '确定',
              cancelText: '取消',
              content: '确认撤回清单吗',
              onOk: () => {
                me.setToolbarLoading('revoke-bill', true)
                let params = me.getSelectedParams()
                me.$http.post(`${me.ajaxUrl.sendBillCancel}/${params}/${me.iemark}`).then(() => {
                  me.$Message.success('清单撤回成功!')
                }).catch(() => {
                }).finally(() => {
                  me.getList()
                  me.setToolbarLoading('revoke-bill')
                  setTimeout(() => {
                    me.$Modal.remove()
                  }, 150)
                })
              }
            })
          }
        }
      }
    },
    /**
     * Excel导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 显示错误信息
     * @param row
     */
    showErrMsg(row) {
      let me = this
      me.$set(me.errMsgInfo, 'sid', row.sid)
      me.$set(me.errMsgInfo, 'sendApiTaskId', row.sendApiTaskId)
      me.$set(me.errMsgInfo, 'modelErrMsgShow', true)
    },
    /**
     * 隐藏错误信息
     */
    hideErrMsg() {
      let me = this
      me.errMsgInfo.modelErrMsgShow = false
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      let me = this
      me.editConfig.editStatus = editStatus.SHOW
      me.editConfig.editData = row
      me.showList = false
    },
    /**
     * 打印清单
     * @param type
     */
    billPrint(type) {
      let me = this,
        printType = ''  // 0 合并打印 1 压缩打印
      if (type === 'pdf') {
        printType = '0'
      } else if (type === 'zip') {
        printType = '1'
      }
      if (['0', '1'].includes(printType) && me.checkRowSelected('打印清单')) {
        let sids = me.getSelectedParams()
        if (printType === '0') {
          pdfExport(me.ajaxUrl.printBillList + '/' + printType + '/' + sids, {
            name: '清单'
          })
        } else if (printType === '1') {
          zipExport(me.ajaxUrl.printBillList + '/' + printType + '/' + sids, {
            name: '清单'
          })
        }
      }
    },
    /**
     * 打印草单
     * @param type
     */
    draftPrint(type) {
      let me = this,
        printType = ''  // 0 合并打印 1 压缩打印
      if (type === 'pdf') {
        printType = '0'
      } else if (type === 'zip') {
        printType = '1'
      }
      if (['0', '1'].includes(printType) && me.checkRowSelected('打印草单')) {
        let sids = me.gridConfig.selectRows.map(item => {
          return item['entrySid']
        })
        if (printType === '0') {
          pdfExport(me.ajaxUrl.printEntryList + '/' + printType + '/' + sids, {
            name: '草单'
          })
        } else if (printType === '1') {
          zipExport(me.ajaxUrl.printEntryList + '/' + printType + '/' + sids, {
            name: '草单'
          })
        }
      }
    },
    handleTableColumnSetup() {
      let me = this
      me.showtableColumnSetup = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'tableShow', false)
      me.$nextTick(() => {
        me.$set(me, 'tableShow', true)
      })
      me.$set(me.gridConfig, 'exportColumns', [])
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.$set(me.gridConfig, 'gridColumns', getColumnsByConfig(me.alltotalColumns, columnsConfig))
      getGridExportColumns(me.alltotalColumns).forEach(item => {
        if (!['selection', 'operation'].includes(item.key)) {
          me.gridConfig.exportColumns.push(item)
        }
      })
    }
  },
  computed: {
    currEditConfig() {
      let me = this
      return {
        billsCardHeight: 0,
        ...me.editConfig
      }
    }
  }
}
