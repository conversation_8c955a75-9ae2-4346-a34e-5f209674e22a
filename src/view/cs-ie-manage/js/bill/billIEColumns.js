import { isNullOrEmpty } from '@/libs/util'
import { sendApiStatusRender } from './sendApiStatusRender'
import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
   'apprStatus'
  ,'apprStatusName'
  ,'sendApiStatus'
  , 'emsListNo'
  , 'entryStatusName'
  , 'listNo'
  , 'erpInsertTime'
  , 'entryNo'
  , 'seqNo'
  , 'declareDate'
  , 'emsNo'
  , 'tradeMode'
  , 'trafMode'
  , 'transMode'
  , 'declareName'
  , 'masterCustoms'
  , 'tradeCountry'
  , 'ieport'
  , 'contrNo'
  , 'licenseNo'
  , 'wrapTypeName'
  , 'packNum'
  , 'netWt'
  , 'grossWt'
  , 'feeMark'
  , 'feeRate'
  , 'feeCurr'
  , 'insurMark'
  , 'insurRate'
  , 'insurCurr'
  , 'otherMark'
  , 'otherRate'
  , 'otherCurr'
  , 'note'
  , 'qtyAll'
  , 'decTotalAll'
  , 'currAll'
  , 'invoiceNo'
  , 'entryType'
  , 'sendApiTime'
]

const columnsConfig = [
  ...baseColumnsShow
  ,  ...commColumns
  , 'overseasShipperName'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  ,  ...commColumns
  , 'overseasShipperName'
]

const columns = {
  mixins: [baseColumns],
  data() {
    let me = this
    return {
      totalColumns: [
        {
          width: 92,
          tooltip: true,
          title: '内审状态',
          key: 'apprStatusName',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'apprStatus', 'apprStatusName')
          }
        },
        {
          width: 92,
          title: '清单状态',
          key: 'sendApiStatus',
          cellRendererFramework: sendApiStatusRender(this)
        },
        {
          width: 160,
          tooltip: true,
          key: 'emsListNo',
          title: '清单内部编号'
        },
        {
          width: 100,
          tooltip: true,
          title: '报关单状态',
          key: 'entryStatusName',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'entryStatus', 'entryStatusName')
          }
        },
        {
          width: 130,
          tooltip: true,
          key: 'listNo',
          title: '核注清单编号'
        },
        {
          width: 130,
          key: 'erpInsertTime',
          title: '预录入单制单日期',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        },
        {
          width: 168,
          tooltip: true,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 168,
          key: 'seqNo',
          tooltip: true,
          title: '报关单统一编号'
        },
        {
          width: 120,
          key: 'declareDate',
          title: '清单申报日期',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        },
        {
          width: 112,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
        },
        {
          width: 130,
          tooltip: true,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.trade)
          }
        },
        {
          width: 110,
          tooltip: true,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.transf)
          }
        },
        {
          width: 100,
          tooltip: true,
          key: 'transMode',
          title: '成交方式',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.transac)
          }
        },
        {
          width: 180,
          tooltip: true,
          title: '申报单位',
          key: 'declareName'
        },
        {
          width: 120,
          tooltip: true,
          title: '申报地海关',
          key: 'masterCustoms',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
          }
        },
        {
          width: 150,
          tooltip: true,
          title: '境外收发货人',
          key: 'overseasShipperName'
        },
        {
          width: 100,
          tooltip: true,
          key: 'tradeCountry',
          title: '运抵国(地区)',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.country_outdated)
          }
        },
        {
          width: 126,
          tooltip: true,
          key: 'ieport',
          title: '出境关别',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 120,
          tooltip: true,
          title: '许可证号',
          key: 'licenseNo'
        },
        {
          width: 180,
          tooltip: true,
          title: '包装种类',
          key: 'wrapTypeName'
        },
        {
          width: 80,
          tooltip: true,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 80,
          key: 'netWt',
          tooltip: true,
          title: '总净重'
        },
        {
          width: 80,
          tooltip: true,
          title: '总毛重',
          key: 'grossWt'
        },
        {
          title: '运费',
          children: [
            {
              width: 80,
              key: 'feeMark',
              title: '运费类型',
              render: (h, params) => {
                return me.cmbShowRender(h, params, me.cmbSource.feeMarkList)
              }
            },
            {
              width: 80,
              tooltip: true,
              key: 'feeRate',
              title: '运费费率'
            },
            {
              width: 120,
              tooltip: true,
              key: 'feeCurr',
              title: '运费币制',
              render: (h, params) => {
                return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          title: '保费',
          children: [
            {
              width: 80,
              title: '保费类型',
              key: 'insurMark',
              render: (h, params) => {
                return me.cmbShowRender(h, params, me.cmbSource.feeMarkList)
              }
            },
            {
              width: 80,
              tooltip: true,
              title: '保费费率',
              key: 'insurRate'
            },
            {
              width: 120,
              tooltip: true,
              title: '保费币制',
              key: 'insurCurr',
              render: (h, params) => {
                return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          title: '杂费',
          children: [
            {
              width: 80,
              title: '杂费类型',
              key: 'otherMark',
              render: (h, params) => {
                return me.cmbShowRender(h, params, me.cmbSource.feeMarkList)
              }
            },
            {
              width: 80,
              tooltip: true,
              title: '杂费费率',
              key: 'otherRate'
            },
            {
              width: 120,
              tooltip: true,
              title: '杂费币制',
              key: 'otherCurr',
              render: (h, params) => {
                return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          width: 120,
          tooltip: true,
          title: '总量',
          key: 'qtyAll'
        },
        {
          width: 120,
          title: '总价',
          tooltip: true,
          key: 'decTotalAll'
        },
        {
          width: 120,
          tooltip: true,
          key: 'currAll',
          title: '表体币制',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
          }
        },
        {
          width: 160,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 160,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        {
          width: 110,
          tooltip: true,
          key: 'entryType',
          title: '报关单类型',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.importExportManage.entryType)
          }
        },
        {
          width: 120,
          title: '发送日期',
          key: 'sendApiTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
