import { getKeyValue, isNullOrEmpty } from '@/libs/util'

export const sendApiStatusRender = (vm) => {
  return majesty.Vue.extend({
    render(h) {
      let me = this
      if (me.params.data.sendApiStatus === '-1') {
        return h('span', {
          style: {
            fontWeight: 'bold'
          }
        }, '未发送')
      } else if (me.params.data.sendApiStatus === '0') {
        return h('div', [
          h('Button', {
            props: {
              type: 'error'
            },
            style: {
              fontWeight: 'bold'
            },
            on: {
              click: () => {
                vm.showErrMsg(me.params.data)
              }
            }
          }, '发送失败')
        ])
      } else if (me.params.data.sendApiStatus === '1') {
        return h('span', {
          style: {
            color: '#19BE6B',
            fontWeight: 'bold'
          }
        }, '发送成功')
      } else if (me.params.data.sendApiStatus === '3') {
        return h('span', {
          style: {
            // color: '#19BE6B',
            fontWeight: 'bold'
          }
        }, '清单撤回')
      } else {
        if (isNullOrEmpty(me.params.data.sendApiStatus)) {
          return h('span', '')
        } else {
          return h('span', getKeyValue(vm.importExportManage.BILL_STATUS_MAP, me.params.data.sendApiStatus))
        }
      }
    }
  })
}
