import pms from '@/libs/pms'
import { getColumnsByConfig } from '@/common'
import { csAPI, pdfExport, zipExport } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { getGridExportColumns } from '@/view/cs-common/function'
import EntryErrMsg from '@/view/cs-common/components/entry-err-msg'
import entryTabs from '@/view/cs-ie-manage/entry/mainlist/entry-Tabs'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { importExportManage, interimVerification } from '@/view/cs-common'
import EntryIESearch from '@/view/cs-ie-manage/entry/components/entry-ie-search'
import { columns, columnsConfig } from '@/view/cs-ie-manage/js/entry/entryIEColumns'

export const entryIEList = {
  components: {
    entryTabs,
    EntryErrMsg,
    EntryIESearch
  },
  mixins: [commList, columns, pms],
  data() {
    return {
      actions: [],
      // 查询条件行数
      searchLines: 6,
      ajaxUrl: {
        getSupplierCodeUrl: ''
      },
      errMsgInfo: {
        sid: '',
        sendApiTaskId: '',
        modelErrMsgShow: false,
        loadUrl: csAPI.emsApi.getSendErrInfo
      },
      importExportManage: importExportManage,
      interimVerification: interimVerification,
      cmbSource: {
        overseasShipperList: [],
        feeMarkList: importExportManage.feeTypeMap
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'send-agent': this.onSendAgent,
        'send-manifest': this.onSendDeclaration,
        'table-column-setup': this.handleTableColumnSetup,
        'send-complete': this.handleSendComplete,
        'recall_sending': this.handleRecallSending,
      }
    }
  },
  created() {
    let me = this
    if (me.iemark === 'I') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.PRD)
    } else if (me.iemark === 'E') {
      me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csAPI.ieParams.CLI)
    } else {
      console.info('请为清单表体查询界面设置进出口标记【ieMark】!')
    }
    // 境外收/发货人
    me.$http.post(me.ajaxUrl.getSupplierCodeUrl).then(res => {
      me.cmbSource.overseasShipperList = [{ label: 'NO', value: 'NO' }, ...ArrayToLocaleLowerCase(res.data.data)]
    }).catch(() => {
      me.cmbSource.overseasShipperList = []
    })
  },
  mounted: function() {
    let me = this
    me.tableId = me.$route.path
    me.totalColumns.forEach(item => {
      if (item.key !== 'selection' && item.key !== 'operation') {
        me.columnsarr.push(item)
      }
    })
    let columns = me.$bom3.showTableColumns(me.tableId, me.filterColumns)
    me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
    me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
    getGridExportColumns(me.alltotalColumns).forEach(item => {
      if (item.key !== 'selection' && item.key !== 'operation') {
        me.gridConfig.exportColumns.push(item)
      }
    })
    me.loadFunctions().then()
  },
  methods: {
    handleRecallSending() {
      let me = this
      if (me.gridConfig.selectRows.length === 0) {
        me.$Message.warning(majesty.Vue.xdoi18nGet('请选择单条数据!'))
        return
      }
      let sendApiStatus = me.gridConfig.selectRows.map(item => {
        return item.sendApiStatus
      })
      if (sendApiStatus.includes('-1')) {
        me.$Message.warning(majesty.Vue.xdoi18nGet('未发送不允许撤回!'))
        return
      } else if (sendApiStatus.includes('0')) {
        me.$Message.warning(majesty.Vue.xdoi18nGet('发送失败不允许撤回!'))
        return
      } else if (sendApiStatus.includes('2')) {
        me.$Message.warning(majesty.Vue.xdoi18nGet('发送撤回不允许再撤回!'))
        return
      }
      if (me.gridConfig.selectRows.length > 1) {
        me.$Message.warning(majesty.Vue.xdoi18nGet('只可以选择一条数据进行撤回!'))
        return
      }
      me.$Modal.confirm({
        loading: true,
        title: majesty.Vue.xdoi18nGet('提醒'),
        okText: majesty.Vue.xdoi18nGet('确定'),
        cancelText: majesty.Vue.xdoi18nGet('取消'),
        content: majesty.Vue.xdoi18nGet('是否发送撤回!'),
        onOk: () => {
          let sid = me.gridConfig.selectRows[0].sid
          me.$http.post(`${me.ajaxUrl.recallSending}/${sid}/${me.iemark}`).then(res => {
            me.$Message.success(majesty.Vue.xdoi18nGet(res.data.message))
          }).catch(() => {
          }).finally(() => {
            setTimeout(() => {
              me.$Modal.remove()
            }, 150)
            me.handleSearchSubmit()
          })
        }
      })
    },
    /**
     * 发送报关行
     */
    onSendAgent() {
      let me = this
      if (me.checkRowSelected('发送报关行')) {
        let noAppRows = me.gridConfig.selectRows.filter(item => {
          return item.apprStatus !== '8'
        })
        if (Array.isArray(noAppRows) && noAppRows.length > 0) {
          me.$Message.success('仅内审通过的报关单才能发送!')
          return
        }
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          content: '确认发送报关行吗',
          onOk: () => {
            me.actions[me.actions.findIndex(it => it.command === 'send-agent')].loading = true
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.entry, {
              idList: params,
              ieMark: me.iemark
            }).then(() => {
              me.$Message.success('发送报关行成功!')
            }).catch(() => {
            }).finally(() => {
              me.getList()
              me.actions[me.actions.findIndex(it => it.command === 'send-agent')].loading = false
              setTimeout(() => {
                me.$Modal.remove()
              }, 150)
            })
          }
        })
      }
    },
    /**
     * 发送报关单
     */
    onSendDeclaration() {
      let me = this
      if (me.checkRowSelected('发送报关单')) {
        let noAppRows = me.gridConfig.selectRows.filter(item => {
          return item.apprStatus !== '8'
        })
        if (Array.isArray(noAppRows) && noAppRows.length > 0) {
          me.$Message.success('仅内审通过的报关单才能发送!')
          return
        }
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          content: '确认发送报关单吗',
          onOk: () => {
            me.actions[me.actions.findIndex(it => it.command === 'send-manifest')].loading = true
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.sendLjqApi}/${params}/${me.iemark}`).then(() => {
              me.$Message.success('发送报关单成功!')
            }).catch(() => {
            }).finally(() => {
              me.getList()
              me.actions[me.actions.findIndex(it => it.command === 'send-manifest')].loading = false
              setTimeout(() => {
                me.$Modal.remove()
              }, 150)
            })
          }
        })
      }
    },
    /**
     * Excel导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 1)
    },
    operationEditShow() {
      return 'none'
    },
    /**
     * 重新设置列表题
     * @param column
     * @param colKey
     * @param iTitle
     * @param eTitle
     */
    reSetColumnTitle(column, colKey, iTitle, eTitle) {
      let me = this
      if (column.key === colKey) {
        if (me.iemark === 'I') {
          column.title = iTitle
        } else if (me.iemark === 'E') {
          column.title = eTitle
        }
      }
    },
    showErrMsg(row) {
      let me = this
      me.$set(me.errMsgInfo, 'sid', row.sid)
      me.$set(me.errMsgInfo, 'sendApiTaskId', row.sendApiTaskId)
      me.$set(me.errMsgInfo, 'modelErrMsgShow', true)
    },
    hideErrMsg() {
      let me = this
      me.errMsgInfo.modelErrMsgShow = false
    },
    /**
     * 打印草单
     * @param type
     */
    draftPrint(type) {
      let me = this,
        printType = ''
      if (type === 'pdf') {
        printType = '0'
      } else if (type === 'zip') {
        printType = '1'
      }
      if (['0', '1'].includes(printType) && me.checkRowSelected('打印草单')) {
        let sids = me.getSelectedParams()
        if (printType === '0') {
          pdfExport(me.ajaxUrl.printEntryList + '/' + printType + '/' + sids, {
            name: '草单'
          })
        } else if (printType === '1') {
          zipExport(me.ajaxUrl.printEntryList + '/' + printType + '/' + sids, {
            name: '草单'
          })
        }
      }
    },
    handleTableColumnSetup() {
      let me = this
      me.showtableColumnSetup = true
    },
    handleUpdateColumn(columns) {
      let me = this
      me.gridConfig.exportColumns = []
      // 解决iView table 的问题
      // me.gridConfig.exportColumns = []
      me.tableShow = false
      me.$nextTick(() => {
        me.tableShow = true
      })
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
      getGridExportColumns(me.alltotalColumns).forEach(item => {
        if (!['selection', 'operation'].includes(item.key)) {
          me.gridConfig.exportColumns.push(item)
        }
      })
    },
    handleSendComplete() {
      let me = this
      if (this.gridConfig.selectRows.length === 0) {
        this.$Message.warning('请选择数据!')
        return
      }
      let notSendRows = me.gridConfig.selectRows.filter(item => {
        if (item.apprStatus == '8') {
          if (item.sendApiStatus == '1'){
            if (item.entryStatus == 'Y'||item.entryStatus == '-1'){
              return null
            }else {
              return item
            }
          }
        } else {
          return item
        }
      })
      if (Array.isArray(notSendRows) && notSendRows.length > 0) {
        this.$Message.warning('只有内审通过，发送状态为未发送或发送失败的数据可以进行完整申报')
        return
      }
      let params = me.getSelectedParams()
      me.$http.post(`${me.ajaxUrl.sendComplete}/${params}`).then(() => {
        me.$Message.success('发送完整申报成功!')
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    }
  },
  computed: {
    filterColumns() {
      let me = this
      if (me.iemark === 'I') {
        // 添加
        if (!columnsConfig.includes('supplierName')) {
          columnsConfig.push('supplierName')
        }
        // 删除
        if (columnsConfig.includes('clientName')) {
          columnsConfig.splice(columnsConfig.findIndex(item => item === 'clientName'), 1)
        }
      } else {
        // 添加
        if (!columnsConfig.includes('clientName')) {
          columnsConfig.push('clientName')
        }
        // 删除
        if (columnsConfig.includes('supplierName')) {
          columnsConfig.splice(columnsConfig.findIndex(item => item === 'supplierName'), 1)
        }
        if (columnsConfig.includes('entryType')) {
          columnsConfig.splice(columnsConfig.findIndex(item => item === 'entryType'), 1)
        }
      }

      let grdCols = getColumnsByConfig(me.totalColumns, columnsConfig)

      for (let gCol of grdCols) {
        me.reSetColumnTitle(gCol, 'overseasShipper', '境外发货人', '境外收货人')
        me.reSetColumnTitle(gCol, 'ieport', '进境关别', '出境关别')
        me.reSetColumnTitle(gCol, 'tradeCountry', '启运国(地区)', '运抵国(地区)')
      }
      return grdCols
    }
  }
}
