import { isNullOrEmpty } from '@/libs/util'
import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'sendApiStatus'
  , 'bondMark'
  , 'batchNo'
  , 'entryStatusName'
  , 'erpInsertTime'
  , 'emsListNo'
  , 'seqNo'
  , 'entryNo'
  , 'entryDeclareDate'
  , 'emsNo'
  , 'tradeMode'
  , 'trafMode'
  , 'transMode'
  , 'declareName'
  , 'masterCustoms'
  , 'tradeCountry'
  , 'ieport'
  , 'contrNo'
  , 'licenseNo'
  , 'wrapType'
  , 'packNum'
  , 'netWt'
  , 'grossWt'
  , 'feeMark'
  , 'feeRate'
  , 'feeCurr'
  , 'insurMark'
  , 'insurRate'
  , 'insurCurr'
  , 'otherMark'
  , 'otherRate'
  , 'otherCurr'
  , 'note'
  , 'qtyAll'
  , 'decTotalAll'
  , 'currAll'
  , 'invoiceNo'
  , 'gname'
  , 'entryType'
  , 'sendDest'
  , 'userName'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
  , 'overseasShipper'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
  , 'overseasShipperName'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '发送状态',
          key: 'sendApiStatus',
          render: (h, params) => {
            if (params.row.sendApiStatus === '-1') {
              return h('span', {
                style: {
                  fontWeight: 'bold'
                }
              }, '未发送')
            } else if (params.row.sendApiStatus === '0') {
              return h('Button', {
                props: {
                  type: 'error'
                },
                style: {
                  fontWeight: 'bold'
                },
                on: {
                  click: () => {
                    this.showErrMsg(params.row)
                  }
                }
              }, '发送失败')
            } else if (params.row.sendApiStatus === '1') {
              return h('span', {
                style: {
                  color: '#19BE6B',
                  fontWeight: 'bold'
                }
              }, '发送成功')
            } else if (params.row.sendApiStatus === '2') {
              return h('span', {
                style: {
                  color: '#19BE6B',
                  fontWeight: 'bold'
                }
              }, '发送撤回')
            } else if (params.row.sendApiStatus === '3') {
              return h('Button', {
                props: {
                  type: 'error'
                },
                style: {
                  fontWeight: 'bold'
                },
                on: {
                  click: () => {
                    this.showErrMsg(params.row)
                  }
                }
              }, '撤回失败')
            } else {
              return h('span', '')
            }
          },
          align: 'center',
          width: 100
        },
        {
          title: '批次号',
          width: 100,
          align: 'center',
          key: 'batchNo'
        },
        {
          title: '报关单状态',
          key: 'entryStatusName',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'entryStatus', 'entryStatusName')
          }
        },
        {
          title: '保完税标志',
          width: 100,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          }
        },
        {
          title: '制单日期',
          key: 'erpInsertTime',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '清单内部编号',
          key: 'emsListNo',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '报关单统一编号',
          key: 'seqNo',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '报关单号',
          key: 'entryNo',
          width: 200,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '中文品名',
          minWidth: 120,
          align: 'center',
          key: 'gname',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单申报日期',
          width: 120,
          align: 'center',
          key: 'entryDeclareDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 140,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '运输方式',
          key: 'trafMode',
          width: 110,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          title: '成交方式',
          key: 'transMode',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transac)
          }
        },
        {
          title: '申报单位',
          key: 'declareName',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报地海关',
          key: 'masterCustoms',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          title: '境外发货人',
          key: 'overseasShipper',
          width: 250,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.overseasShipperList)
          }
        },
        {
          title: '境外发(收)货人',
          width: 150,
          align: 'center',
          key: 'overseasShipperName',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '启运国（地区）',
          key: 'tradeCountry',
          width: 180,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '进境关别',
          key: 'ieport',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          title: '合同协议号',
          key: 'contrNo',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '许可证号',
          key: 'licenseNo',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '包装种类',
          key: 'wrapType',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          title: '件数',
          key: 'packNum',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总净重',
          key: 'netWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总毛重',
          key: 'grossWt',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '运费',
          align: 'center',
          children: [
            {
              title: '运费类型',
              key: 'feeMark',
              width: 80,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.cmbSource.feeMarkList)
              }
            },
            {
              title: '费率',
              key: 'feeRate',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '币制',
              key: 'feeCurr',
              width: 150,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          title: '保费',
          align: 'center',
          children: [
            {
              title: '保费类型',
              key: 'insurMark',
              width: 80,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.cmbSource.feeMarkList)
              }
            },
            {
              title: '费率',
              key: 'insurRate',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '币制',
              key: 'insurCurr',
              width: 150,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          title: '杂费',
          align: 'center',
          children: [
            {
              title: '杂费类型',
              key: 'otherMark',
              width: 80,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.cmbSource.feeMarkList)
              }
            },
            {
              title: '费率',
              key: 'otherRate',
              width: 80,
              ellipsis: true,
              tooltip: true,
              align: 'center'
            },
            {
              title: '币制',
              key: 'otherCurr',
              width: 150,
              ellipsis: true,
              tooltip: true,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            }
          ]
        },
        {
          title: '总量',
          key: 'qtyAll',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '总价',
          key: 'decTotalAll',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '币制',
          key: 'currAll',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '发票号',
          key: 'invoiceNo',
          width: 160,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '备注',
          key: 'note',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          width: 150,
          key: 'entryType',
          title: '报关单类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.entryType)
          }
        },
        {
          width: 150,
          key: 'sendDest',
          title: '发送目的地',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.sendDest)
          }
        },
        {
          width: 120,
          title: '制单员',
          key: 'userName'
        }
      ]
    }
  },
  methods: {
    keyValueRender (h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
