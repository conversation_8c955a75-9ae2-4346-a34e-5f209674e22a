import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { namespace } from '@/project'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus, importExportManage } from '@/view/cs-common'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { columns } from '@/view/cs-ie-manage/dec-erp-main/decErpHeadColumns'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import sendDataCheck from '@/view/cs-ie-manage-mixins/components/send-data-check'
import BillOfLadingCopy from '@/view/cs-ie-manage-mixins/components/bill-of-lading-copy'
import DecErpMainSearch from '@/view/cs-ie-manage/components/dec-erp-main/dec-erp-main-search'
import headBodyExtract from '@/view/cs-ie-manage/components/head-body-extract/headBodyExtract'
import warehouseReceiptInfoPop from '@/view/cs-ie-manage/components/warehouse-receipt-info-pop'
import BackFillBillOfLadingInfo from '@/view/cs-ie-manage/components/back-fill-bill-of-lading-info/back-fill-bill-of-lading-info'

export const decErpHeadIEList = {
  components: {
    sendDataCheck,
    headBodyExtract,
    DecErpMainSearch,
    BillOfLadingCopy,
    warehouseReceiptInfoPop,
    BackFillBillOfLadingInfo
  },
  mixins: [listDataProcessing, pms, columns],
  data() {
    return {
      allColumns: [],
      isContinue: false,
      showDialog: false,
      warehouseReceipt: {
        headId: '',
        show: false
      },
      emsModalShow: false,
      modelCopyShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'copy': this.handleCopy,
        'lock': this.handleLock,
        'unlock': this.handleUnLock,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'extract': this.handleExtract,
        'backDelivery': this.backFill,
        'send-audit': this.handleSendAudit,
        'table-column-setup': this.handleTableColumnSetup,
        'forceChangeStatus': this.handleForceChangeStatus    // 强制修改状态
      },
      isEmsListNoAuto: false,
      modelBackFillShow: false,
      listConfig: {
        errdata: [],
        errDataTotal: -1,
        errgridColumns: []
      },
      detailReloadConfig: {
        sid: '',
        editStatus: editStatus.SHOW
      },
      importExportManage: importExportManage,
      apprStatusList: importExportManage.auditStatusMap,
      ajaxUrl: {
        loadConfigUrl: csAPI.earlyWarning.manager.clearanceBusiness.loadConfigUrl
      },
      cmbSource: {
        orderType: [{
          value: '0',
          label: 'PO退运'
        }, {
          value: '1',
          label: 'PO换货'
        }, {
          value: '2',
          label: '修理物品'
        }, {
          value: '3',
          label: '出口加工'
        }, {
          value: '4',
          label: 'Resale'
        }]
      }
    }
  },
  created: function () {
    let me = this
    // 是否【单据内部编号】自动生成
    me.$http.get(csAPI.enterpriseParamsLib.customDocNo.judgeIsExistsRule + '/' + me.moduleName).then(res => {
      me.$set(me, 'isEmsListNoAuto', res.data.data)
    }).catch(() => {
      me.$set(me, 'isEmsListNoAuto', false)
    }).finally(() => {
      console.info('是否自动生成EmsListNo: ' + me.isEmsListNoAuto.toString())
    })
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
  },
  methods: {
    /**
     * 获取设置key
     */
    getListId() {
      let me = this
      me.$set(me, 'listId', me.$route.path)
    },
    getRealEntryType() {
      let me = this
      if (me.IEMark === 'I') {
        if (me.bondMark === '1') {
          return [{
            value: 'X', label: '是'
          }, {
            value: '0', label: '否'
          }]
        }
      }
      return importExportManage.entryType
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this,
        allColumns = []
      for (let gCol of me.totalColumns) {
        if (gCol.key === 'entryPort') {
          gCol.title = me.entryPortLabel
        }
        if (gCol.key === 'overseasShipperName') {
          gCol.title = me.overseasShipperLabel
        }
        if (gCol.key === 'iedate') {
          gCol.title = me.iedateLabel
        }
        if (gCol.key === 'ieport') {
          gCol.title = me.ieportLabel
        }
        if (gCol.key === 'entryType') {
          if (me.IEMark === 'I') {
            if (me.bondMark === '1') {
              gCol.title = '两步申报'
            }
          }
        }
        allColumns.push(deepClone(gCol))
      }
      if (me.IEMark === 'I') {
        if (me.bondMark === '1') {
          me.allColumns = allColumns.filter(col => {
            return !['decTotalProcessCurr', 'nootherPack', 'payTotalCurr', 'clientTotalCurr', 'packages',
              'billTo', 'notify', 'emsNo','orderType','oaNo'].includes(col.key)
          })
        } else {
          me.allColumns = allColumns.filter(col => {
            return !['exemptsNo', 'preStatus', 'preDate', 'preEmsListNo', 'sa',
              'decTotalProcessCurr', 'nootherPack', 'payTotalCurr', 'clientTotalCurr', 'packages',
              'billTo', 'notify','batchNo','batchStatus','orderType','oaNo'].includes(col.key)
          })
        }
      } else {
        if (me.bondMark === '1') {
          me.allColumns = allColumns.filter(col => {
            return !['entryType', 'exemptsNo', 'preStatus', 'preDate', 'preEmsListNo', 'sa', 'emsNo','batchNo','batchStatus'].includes(col.key)
          })
        } else {
          me.allColumns = allColumns.filter(col => {
            return !['entryType', 'exemptsNo', 'preStatus', 'preDate', 'preEmsListNo', 'sa','batchNo','batchStatus','orderType'].includes(col.key)
          })
        }
      }
      me.$set(me.listConfig, 'settingColumns', me.allColumns)
    },
    /**
     * 自定义列设置
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck([row], '编辑')) {
        if (['2', '8'].includes(row.apprStatus)) {
          me.$Message.warning('状态为"待审核"、"内审通过"的单据不能编辑!')
        } else {
          if (row.wrapType !== null) {
            if (row.wrapType.includes(',')) {
              row.wrapType = row.wrapType.split(',')
            }
          }
          me.$set(me.editConfig, 'editData', row)
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
          me.$set(me, 'showList', false)
        }
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, 'delete')
    },
    handleRejected() {
      let me = this
      if (me.checkRowSelected('驳回')) {
        me.$Modal.confirm({
          title: '提醒',
          content: '确认驳回所选项吗',
          okText: '驳回',
          cancelText: '取消',
          onOk: () => {
            let params = me.getSelectedParams()
            me.$http.delete(`${me.ajaxUrl.rejected}/${params}`).then(() => {
              me.$Message.success('驳回成功！')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
            })
          }
        })
      }
    },
    /**
     * 下载
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 弹出复制信息
     */
    handleCopy() {
      let me = this
      if (me.checkRowSelected('复制', true)) {
        if (me.isEmsListNoAuto) {
          me.onPopCopyDo('', true)
        } else {
          me.$set(me, 'modelCopyShow', true)
        }
      }
    },
    /**
     * 执行复制操作
     * @param emsListNo
     * @param autoCreated
     */
    onPopCopyDo(emsListNo, autoCreated) {
      let me = this
      me.$set(me, 'modelCopyShow', false)
      if (autoCreated !== true) {
        autoCreated = false
      }
      if (!isNullOrEmpty(emsListNo) || autoCreated) {
        me.$http.post(me.ajaxUrl.copyUrl, {
          sid: me.listConfig.selectRows[0].sid,
          emsListNo: emsListNo
        }).then(res => {
          me.$Message.success(res.data.message)
          me.editBack({
            showList: true,
            editData: {},
            editStatus: editStatus.SHOW
          })
        }).catch(() => {
        })
      }
    },
    /**
     * 自定义编辑、删除检查(可外部覆盖)
     * @param selRow 选中的行数组
     * @param opTitle
     * @returns {boolean}
     */
    customCheck2(selRow, opTitle) {
      let me = this
      if (selRow.apprStatus === '8') {
        return true
      } else {
        me.$Message.warning('仅有【内审通过】的数据才能' + opTitle + '!')
        return false
      }
    },
    /**
     * 回填提运单信息
     * @constructor
     */
    backFill() {
      let me = this
      if (me.checkRowSelected('数据回填', true)
        && me.customCheck2(me.listConfig.selectRows[0], '数据回填')) {
        me.modelBackFillShow = true
      }
    },
    /**
     * 执行回填
     * @param fillData
     */
    doBackFill(fillData) {
      let me = this
      me.setToolbarLoading('backDelivery', true)
      me.$http.put(`${me.ajaxUrl.update}/${fillData.sid}`, {
        hawb: fillData.hawb,
        trafName: fillData.trafName,
        voyageDate: fillData.voyageDate,
        forwardCode: fillData.forwardCode,
        volume: fillData.volume,
        invoiceNo: fillData.invoiceNo,
        iedate: fillData.iedate,
        tradeTerms: fillData.tradeTerms,
        masterCustoms: fillData.masterCustoms,
        cweight: fillData.cweight,
        grossWt: fillData.grossWt,
        packNum: fillData.packNum,
        note: fillData.note,
        remark: fillData.remark,
        despPort: fillData.despPort
      }).then(() => {
        me.getList()
        me.$Message.success('数据回填成功!')
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('backDelivery')
        me.modelBackFillShow = false
      })
    },
    /**
     * 根据主键和状态刷新编辑界面
     * @param sid
     * @param editStatus
     */
    detailReload(sid, editStatus) {
      let me = this
      me.$set(me.detailReloadConfig, 'sid', sid)
      me.$set(me.detailReloadConfig, 'editStatus', editStatus)
      me.getList()
    },
    /**
     * 查询完成后执行操作
     */
    afterSearchSuccess() {
      let me = this
      if (!isNullOrEmpty(me.detailReloadConfig.sid)) {
        let theRows = me.listConfig.data.filter(row => {
          return row.sid === me.detailReloadConfig.sid
        })
        if (Array.isArray(theRows) && theRows.length === 1) {
          me.$set(me.editConfig, 'editData', theRows[0])
        }
        me.$nextTick(() => {
          me.$set(me.detailReloadConfig, 'sid', '')
          me.$set(me.detailReloadConfig, 'editStatus', editStatus.SHOW)
        })
      }
    },
    getSendDataParams() {
      let me = this
      return me.listConfig.selectRows.map(item => {
        return {
          apprNote: '',
          apprType: me.IEMark,
          businessSid: item.sid
        }
      })
    },
    /**
     * 发送内审前先执行检查
     */
    handleSendAudit() {
      let me = this
      if (me.checkRowSelected('发送内审')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '确认将所选项发送内审吗',
          onOk: () => {
            me.setToolbarLoading('send-audit', true)
            let params = me.getSendDataParams()
            me.$http.post(csAPI.aeoManage.aeoReview.actions.sendDataMCheck, params).then(res => {
              if (Array.isArray(res.data.data) && res.data.data.length > 0) {
                me.listConfig.errDataTotal = res.data.data.length
                me.showDialog = true
                me.listConfig.errdata = res.data.data
                me.isContinue = !isNullOrEmpty(res.data['errorField'])
                me.setToolbarLoading('send-audit')
              } else {
                me.handleSendContinue(params)
              }
            }).catch(() => {
              me.setToolbarLoading('send-audit')
            })
          }
        })
      }
    },
    /**
     * 发送内审
     */
    handleSendContinue(params) {
      let me = this
      me.$http.post(csAPI.aeoManage.aeoReview.actions.sendDataM, params).then(() => {
        me.$Message.success('发送内审成功!')
      }).catch(() => {
      }).finally(() => {
        me.listConfig.selectRows = []
        me.getList()
        me.setToolbarLoading('send-audit')
      })
    },
    /**
     * 发送内审审核错误数据界面返回
     * @param val
     */
    checkBack(val) {
      let me = this
      me.showDialog = val.showDialog
      if (val.isContinue) {
        let params = me.getSendDataParams()
        me.handleSendContinue(params)
      } else {
        me.setToolbarLoading('forceChangeStatus')
      }
    },
    /**
     * 强制修改状态
     */
    handleForceChangeStatus() {
      let me = this,
        apiUrlCheck = me.IEMark === 'E' ? csAPI.csImportExport.decErpEHeadN.checkForForceChangeStatus : csAPI.csImportExport.decErpIHeadN.checkForForceChangeStatus
      let apiUrl = me.IEMark === 'E' ? csAPI.csImportExport.decErpEHeadN.forceChangeStatus : csAPI.csImportExport.decErpIHeadN.forceChangeStatus
      if (me.checkRowSelected('强制修改状态', true)) {
        let params = me.getSelectedParams()
        me.setToolbarLoading('forceChangeStatus', true)
        me.$http.post(`${apiUrlCheck}/${params}`, {}).then(() => {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确定',
            cancelText: '取消',
            content: '确认强制修改此票单据状态为暂存?',
            onOk: () => {
              me.setToolbarLoading('forceChangeStatus', true)
              me.$http.post(`${apiUrl}/${params}`, {}).then(() => {
                me.$Message.success('修改成功！')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('forceChangeStatus')
              })
            }
          })
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('forceChangeStatus')
        })
      }
    },
    /**
     * 随附单据锁定
     */
    handleLock() {
      let me = this
      if (me.checkRowSelected('锁定')) {
        if (me.customCheck(me.listConfig.selectRows, '锁定')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确定',
            cancelText: '取消',
            content: '确认锁定所选项吗',
            onOk: () => {
              me.setToolbarLoading('lock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lockDocAccompanying}/${params}/1`).then(() => {
                me.$Message.success('锁定成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('lock')
              })
            }
          })
        }
      }
    },
    /**
     * 随附单据解锁
     */
    handleUnLock() {
      let me = this
      if (me.checkRowSelected('解锁')) {
        if (me.customCheck(me.listConfig.selectRows, '解锁')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确定',
            cancelText: '取消',
            content: '确认解锁所选项吗',
            onOk: () => {
              me.setToolbarLoading('unlock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.unlockDocAccompanying}/${params}/0`).then(() => {
                me.$Message.success('解锁成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('unlock')
              })
            }
          })
        }
      }
    },
    /**
     * 显示舱单信息
     * @param row
     */
    showWarehouseReceiptInfo(row) {
      let me = this
      me.$set(me.warehouseReceipt, 'headId', row['sid'])
      me.$set(me.warehouseReceipt, 'show', true)
    }
  },
  /**
   * 计算属性
   */
  computed: {
    /**
     * 回填所需数据
     */
    backFillData() {
      let me = this
      if (me.listConfig.selectRows.length > 0) {
        let selRow = me.listConfig.selectRows[0]
        return {
          sid: selRow.sid,
          hawb: selRow.hawb,
          trafName: selRow.trafName,
          voyageDate: selRow.voyageDate,
          forwardCode: selRow.forwardCode,
          volume: selRow.volume,
          invoiceNo: selRow.invoiceNo,
          iedate: selRow.iedate,
          tradeTerms: selRow.tradeTerms,
          masterCustoms: selRow.masterCustoms,
          cweight: selRow.cweight,
          grossWt: selRow.grossWt,
          packNum: selRow.packNum,
          note: selRow.note,
          remark: selRow.remark,
          despPort: selRow.despPort
        }
      }
      return {
        sid: '',
        hawb: '',
        trafName: '',
        voyageDate: '',
        forwardCode: '',
        volume: '',
        invoiceNo: '',
        iedate: '',
        tradeTerms: '',
        masterCustoms: '',
        grossWt: null,
        packNum: null,
        note: '',
        remark: '',
        despPort: ''
      }
    },
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    /**
     * 是否显示舱单信息
     */
    canManiFestShow() {
      let me = this
      return me.configData && me.configData.getManifestInfo === '1'
    }
  }
}
