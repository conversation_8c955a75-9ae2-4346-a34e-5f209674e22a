import pms from '@/libs/pms'
import { editStatus, AeoInfoList } from '@/view/cs-common'
import Cert from '@/view/cs-ie-manage/dec-erp-cert/dec-erp-cert.vue'
import Body from '@/view/cs-ie-manage/dec-erp-body/dec-erp-body-list'
import { dynamicTabs } from '../../dec-erp-head/dynamic/js/dynamicTabs'
import Bill from '@/view/cs-ie-manage/components/bill-detail/bill-details'
import Attach from '@/view/cs-ie-manage/attached-document/attached-document'
import { auditTrailProcessing } from '@/view/cs-aeoManage/base/auditTrailProcessing'
import packingMaintainList from '@/view/cs-ie-manage/packing-maintain/packing-maintain-list'
import DecAttachedDocumentsList from '../../dec-attached-documents/dec-attached-documents-list'

export const decErpHeadIETabs = {
  components: {
    Bill,
    Body,
    Cert,
    Attach,
    AeoInfoList,
    packingMaintainList,
    DecAttachedDocumentsList
  },
  mixins: [pms, auditTrailProcessing, dynamicTabs],
  props: {
    editConfig: {
      type: Object,
      default: () => ({})
    },
    ieMark: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        packingTab: false,
        packingMaintainTab: false,
        certTab: false,
        billTab: false,
        logisticsTab: false,
        entryTab: false,
        attachTab: false,
        aeoTab: false
      },
      actions: [],
      showBody: false,
      collapsed: true,
      isHeadSaved: false,     // 是否点击了表头保存按钮
      aeoShowTitle: false,
      parentConfig: {
        editData: {},
        editStatus: editStatus.SHOW
      },
      isEntryTabShow: false,
      isLogisticsTabShow: false
    }
  },
  created: function() {
    let me = this
    me.loadFunctions('tabs').then()
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function(config) {
        let me = this
        me.parentConfig.editStatus = config.editStatus
        if (config && config.editStatus === editStatus.ADD) {
          me.parentConfig.editData = {
            sid: '',
            emsNo: '',
            gmark: '',
            bondMark: '',
            billType: '',
            emsListNo: '',
            apprStatus: '',
            districtCode: '',
            districtPostCode: ''
          }
          me.showBody = false
        } else if (config && config.editStatus === editStatus.EDIT) {
          me.parentConfig.editData = config.editData
          me.showBody = true
        } else if (config && config.editStatus === editStatus.SHOW) {
          me.parentConfig.editData = config.editData
          me.showBody = true
        }
      }
    },
    tabName(value) {
      let me = this
      me.tabs[value] = true
      if (value === 'headTab') {
        if (me.dynamicHeadTabShow) {
          me.reloadDynamicHeadTab()
        } else {
          me.$nextTick(() => {
            if (me.$refs.head) {
              me.$refs.head.reCaculateWt()
              me.$refs.head.loadBodySumInfo()
              me.$refs.head.setFeeDisableByTransMode(me.parentConfig.editData.transMode)
              me.$emit('onDetailReload', me.editConfig.editData.sid, me.editConfig.editStatus)
            }
          })
        }
      } else if (value === 'bodyTab') {
        me.$nextTick(() => {
          if (me.$refs.body) {
            if (me.isHeadSaved) {
              me.$refs.body.backToList()
              me.$refs.body.billTypeChange(me.parentConfig.editData.billType)
              me.$set(me, 'isHeadSaved', false)
            }
            me.$refs.body.handleSearchSubmit()
          }
        })
      } else if (value === 'billTab') {
        me.$nextTick(() => {
          if (me.$refs.bill) {
            me.$refs.bill.billReload()
          }
        })
      } else if (value === 'attachTab') {
        me.$nextTick(() => {
          if (me.$refs.attachInfo) {
            me.$refs.attachInfo.loadAttach()
          }
        })
      } else if (value === 'logisticsTab') {
        if (me.isLogisticsTabShow !== true) {
          me.$set(me, 'isLogisticsTabShow', true)
        } else {
          me.$nextTick(() => {
            if (me.$refs.logistics) {
              me.$refs.logistics.loadLogisticsData()
            }
          })
        }
      } else if (value === 'entryTab') {
        if (me.isEntryTabShow !== true) {
          me.$set(me, 'isEntryTabShow', true)
        } else {
          me.$nextTick(() => {
            if (me.$refs.entryTrack) {
              me.$refs.entryTrack.customsTrackDataLoad()
            }
          })
        }
      } else if (value === 'certTab') {
        me.$nextTick(() => {
          if (me.$refs.cert) {
            me.$refs.cert.getUseData()
          }
        })
      }
    }
  },
  methods: {
    afterHeadSaved() {
      let me = this
      me.$set(me, 'isHeadSaved', true)
    },
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    // 进入查看状态
    goToShowStatus() {
      let me = this
      me.editBack({
        showList: false,
        editStatus: editStatus.SHOW,
        editData: me.editConfig.editData
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    },
    /**
     * 刷新清单信息
     */
    refreshBill() {
      let me = this
      me.parentConfig.editData.apprStatus = '9'
    },
    getHeadId() {
      let me = this
      return me.editConfig.editData.sid
    },
    doExtract(rowData) {
      let me = this
      if (me.$refs.head) {
        if (typeof me.$refs.head.selectTemplateSuccess === "function") {
          me.$refs.head.selectTemplateSuccess(rowData)
        }
      } else if (me.$refs.DynamicHead) {
        if (typeof me.$refs.DynamicHead.selectTemplateSuccess === "function") {
          me.$refs.DynamicHead.selectTemplateSuccess(rowData, false)
        }
      }
    },
    /**
     * Layout
     * @param e
     */
    handleRightSliderClick(e) {
      let me = this
      me.$set(me, 'collapsed', !me.collapsed)
      if (me.collapsed) {
        e.target.style.transform = 'rotate(90deg)'
        e.target.style['-webkit-transform'] = 'rotate(90deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 10px'
        }
      } else {
        e.target.style.transform = 'rotate(0deg)'
        e.target.style['-webkit-transform'] = 'rotate(0deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 22px'
        }
      }
    }
  }
}
