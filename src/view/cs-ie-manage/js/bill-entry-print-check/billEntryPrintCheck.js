import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'

export const billEntryPrintCheck = {
  data() {
    return {
      ajaxUrl: {
        printCheck: ''
      }
    }
  },
  created: function () {
    let me = this
    if (me.iemark === 'I') {
      me.$set(me.ajaxUrl, 'printCheck', csAPI.csImportExport.iBill.printCheck)
    } else if (me.iemark === 'E') {
      me.$set(me.ajaxUrl, 'printCheck', csAPI.csImportExport.eBill.printCheck)
    }
  },
  methods: {
    /**
     * 可否打印校验
     * @param type
     * @param callback
     */
    printCheck(type, callback) {
      let me = this,
        erpHeadSid = ''
      if (!isNullOrEmpty(me.headId)) {
        erpHeadSid = me.headId
      } else if (!isNullOrEmpty(me.editConfig.editData.erpHeadId)) {
        erpHeadSid = me.editConfig.editData.erpHeadId
      } else {
        erpHeadSid = me.editConfig.editData.headId
      }
      me.$http.post(me.ajaxUrl.printCheck + '/' + erpHeadSid + '/' + type).then(res => {
        if (typeof callback === 'function') {
          callback.call(me, res)
        }
      }).catch(() => {
      })
    }
  }
}
