import { isNullOrEmpty } from '@/libs/util'

/**
 * 成交方式与运保杂费用的规则
 * @type {{name: string}}
 */
export const transModeRules = {
  name: 'transModeRules',
  data () {
    let importConfig = this.getTransIConfig()
    let exportConfig = this.getTransEConfig()
    return {
      importConfig: {
        ...importConfig
      },
      exportConfig: {
        ...exportConfig
      }
    }
  },
  methods: {
    /**
     * 获取进口配置信息(成交方式与运保杂关系)
     * @returns {*[]}
     */
    getTransIConfig() {
      return [
        {transMode: '1', feeDisable: true, insuranceDisable: true, label: 'CIF'},
        {transMode: '2', feeDisable: true, insuranceDisable: false, label: 'CFR(C&F/CNF)'},
        {transMode: '3', feeDisable: false, insuranceDisable: false, label: 'FOB'},
        {transMode: '4', feeDisable: false, insuranceDisable: true, label: 'C&I'},
        {transMode: '7', feeDisable: false, insuranceDisable: false, label: 'EXW'}
      ]
    },
    /**
     * 获取出口配置信息(成交方式与运保杂关系)
     * @returns {*[]}
     */
    getTransEConfig() {
      return [
        {transMode: '1', feeDisable: false, insuranceDisable: false, label: 'CIF'},
        {transMode: '2', feeDisable: false, insuranceDisable: true, label: 'CFR(C&F/CNF)'},
        {transMode: '3', feeDisable: true, insuranceDisable: true, label: 'FOB'},
        {transMode: '4', feeDisable: true, insuranceDisable: false, label: 'C&I'},
        {transMode: '7', feeDisable: true, insuranceDisable: true, label: 'EXW'}
      ]
    },
    /**
     * 根据成交方式获取运保费的只读与否信息
     * @param transMode
     * @param ieMark
     */
    getFeeRulesByTransMode(transMode, ieMark) {
      if (['I', 'E'].includes(ieMark) && !isNullOrEmpty(transMode)) {
        let theConfig = []
        if (ieMark === 'I') {
          theConfig = this.getTransIConfig()
        } else {
          theConfig = this.getTransEConfig()
        }
        let theValidConfigs = theConfig.filter(item => {
          return item.transMode === transMode
        })
        if (Array.isArray(theValidConfigs) && theValidConfigs.length > 0) {
          return {
            feeDisable: theValidConfigs[0].feeDisable,
            insuranceDisable: theValidConfigs[0].insuranceDisable
          }
        }
      }
      return {
        feeDisable: false,
        insuranceDisable: false
      }
    },
    /**
     * 贸易条款与成交方式的关系(进口)
     * @returns {*[]}
     */
    getTradeTermsTransModeIConfig() {
      return [
        {tradeTerms: 'CIF', transMode: '1', label: 'CIF'},

        {tradeTerms: 'DAP', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DPU', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DDP', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DDU', transMode: '1', label: 'CIF'},

        {tradeTerms: 'CFR', transMode: '2', label: 'C&F'},
        {tradeTerms: 'CPT', transMode: '2', label: 'C&F'},

        {tradeTerms: 'EXW', transMode: '7', label: 'EXW'},
        {tradeTerms: 'CIP', transMode: '4', label: 'C&I'},

        {tradeTerms: 'FCA', transMode: '3', label: 'FOB'},
        {tradeTerms: 'FOB', transMode: '3', label: 'FOB'},
        {tradeTerms: 'FAS', transMode: '3', label: 'FOB'}
      ]
    },
    /**
     * 贸易条款与成交方式的关系(出口)
     * @returns {*[]}
     */
    getTradeTermsTransModeEConfig() {
      return [
        {tradeTerms: 'CIF', transMode: '1', label: 'CIF'},

        {tradeTerms: 'DAP', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DPU', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DDP', transMode: '1', label: 'CIF'},
        {tradeTerms: 'DDU', transMode: '1', label: 'CIF'},

        {tradeTerms: 'CFR', transMode: '2', label: 'C&F'},
        {tradeTerms: 'CPT', transMode: '2', label: 'C&F'},

        {tradeTerms: 'EXW', transMode: '7', label: 'EXW'},
        {tradeTerms: 'CIP', transMode: '4', label: 'C&I'},

        {tradeTerms: 'FCA', transMode: '3', label: 'FOB'},
        {tradeTerms: 'FOB', transMode: '3', label: 'FOB'},
        {tradeTerms: 'FAS', transMode: '3', label: 'FOB'}
      ]
    },
    /**
     * 根据【贸易条款】与【进出口标识】获取【成交方式】
     * @param tradeTerms
     * @param ieMark
     * @returns {string|*}
     */
    getTransModeByTradeTerms(tradeTerms, ieMark) {
      let ttConfig = []
      if (ieMark === 'I') {
        ttConfig = this.getTradeTermsTransModeIConfig()
      } else if (ieMark === 'E') {
        ttConfig = this.getTradeTermsTransModeEConfig()
      }
      if (ttConfig.length > 0) {
        let theConfig = ttConfig.filter(item => {
          return item.tradeTerms === tradeTerms
        })
        if (Array.isArray(theConfig) && theConfig.length > 0) {
          return theConfig[0].transMode
        }
      }
      return ''
    }
  }
}
