import { isNullOrEmpty } from '@/libs/util'

/**
 * 监管方式规则
 * @type {{name: string}}
 */
export const regulatoryRules = {
  name: 'regulatoryRules',
  data() {
    let importConfig = this.getImportConfig()
    let exportConfig = this.getExportConfig()
    let importConfig2 = this.getImportConfig2()
    let exportConfig2 = this.getExportConfig2()
    let ieConfig2Comm = this.getIEConfig2Comm()
    return {
      importConfig: [
        ...importConfig
      ],
      exportConfig: [
        ...exportConfig
      ],
      // 表体进口补充规则
      importConfig2: {
        ...importConfig2
      },
      // 表体出口补充规则
      exportConfig2: {
        ...exportConfig2
      },
      // 表体通用规则
      ieConfig2Comm: [
        ...ieConfig2Comm
      ],
      // 完整的监管方式
      fullTradeModeList: []
    }
  },
  created: function() {
    let me = this
    me.pcodeList(me.pcode.trade).then(res => {
      me.$set(me, 'fullTradeModeList', res)
    }).catch(() => {
      me.$set(me, 'fullTradeModeList', [])
    })
  },
  methods: {
    getImportConfig() {
      return [
        {
          tradeMode: '0214', cutMode: '502', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0245', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0255', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0258', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0300', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0314', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0320', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0345', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0445', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0545', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0845', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0865', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '4400', cutMode: '', gMark: 'E', bookHead: ['B', 'E']
        }, {
          tradeMode: '5014', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0444', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0544', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0615', cutMode: '503', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0642', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0644', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0654', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0657', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0700', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0715', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0744', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0815', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0844', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0864', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '1215', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '4600', cutMode: '', gMark: 'E', bookHead: ['C', 'E']
        }, {
          tradeMode: '5015', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0110', cutMode: '101', gMark: '', bookHead: []
        }, {
          tradeMode: '0200', cutMode: '', gMark: '', bookHead: ['B', 'C', 'E', 'H']
        }, {
          tradeMode: '9700', cutMode: '', gMark: '', bookHead: []
        }, {
          tradeMode: '1371', cutMode: '', gMark: '', bookHead: ['B', 'C', 'E', 'H']
        }, {
          tradeMode: '0400', cutMode: '', gMark: '', bookHead: ['B', 'C', 'E', 'H']
        }
      ]
    },
    getExportConfig() {
      return [
        {
          tradeMode: '0214', cutMode: '502', gMark: 'E', bookHead: ['B', 'E']
        }, {
          tradeMode: '0255', cutMode: '', gMark: 'E', bookHead: ['B', 'E']
        }, {
          tradeMode: '0258', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0265', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0300', cutMode: '', gMark: 'I', bookHead: ['B', 'E']
        }, {
          tradeMode: '0314', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0320', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0345', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0445', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0545', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0845', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0865', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '4400', cutMode: '', gMark: 'E', bookHead: ['B', 'E']
        }, {
          tradeMode: '5014', cutMode: '', gMark: '', bookHead: ['B', 'E']
        }, {
          tradeMode: '0444', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0544', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0615', cutMode: '503', gMark: 'E', bookHead: ['C', 'E']
        }, {
          tradeMode: '0642', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0654', cutMode: '', gMark: 'E', bookHead: ['C', 'E']
        }, {
          tradeMode: '0657', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0664', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0700', cutMode: '', gMark: 'I', bookHead: ['C', 'E']
        }, {
          tradeMode: '0715', cutMode: '', gMark: 'E', bookHead: ['C', 'E']
        }, {
          tradeMode: '0744', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0815', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0844', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0864', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '1215', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '4600', cutMode: '', gMark: 'E', bookHead: ['C', 'E']
        }, {
          tradeMode: '5015', cutMode: '', gMark: '', bookHead: ['C', 'E']
        }, {
          tradeMode: '0110', cutMode: '101', gMark: '', bookHead: []
        }, {
          tradeMode: '0200', cutMode: '', gMark: '', bookHead: ['B', 'C', 'E', 'H']
        }, {
          tradeMode: '1371', cutMode: '', gMark: '', bookHead: ['B', 'C', 'E', 'H']
        }
      ]
    },
    getImportConfig2() {
      return {
        normal: {
          gMark: 'I',
          originCountry: '',
          destinationCountry: '142',
          dutyMode: '',
          dutyModeDisabled: false
        },
        special: [{
          tradeMode: '4400',
          gMark: 'E',
          originCountry: '142',
          destinationCountry: '142',
          dutyMode: '3',
          dutyModeDisabled: true
        }, {
          tradeMode: '4600',
          gMark: 'E',
          originCountry: '142',
          destinationCountry: '142',
          dutyMode: '3',
          dutyModeDisabled: true
        }]
      }
    },
    getExportConfig2() {
      return {
        normal: {
          gMark: 'E',
          originCountry: '142',
          destinationCountry: '',
          dutyMode: '',
          dutyModeDisabled: false
        },
        special: [{
          tradeMode: '0265',
          gMark: 'I',
          originCountry: '',
          destinationCountry: '',
          dutyMode: '3',
          dutyModeDisabled: true
        }, {
          tradeMode: '0300',
          gMark: 'I',
          originCountry: '',
          destinationCountry: '',
          dutyMode: '3',
          dutyModeDisabled: true
        }, {
          tradeMode: '0664',
          gMark: 'I',
          originCountry: '',
          destinationCountry: '',
          dutyMode: '3',
          dutyModeDisabled: true
        }, {
          tradeMode: '0700',
          gMark: 'I',
          originCountry: '',
          destinationCountry: '',
          dutyMode: '3',
          dutyModeDisabled: true
        }]
      }
    },
    getIEConfig2Comm() {
      return [
        {
          tradeMode: '0110', dutyMode: '1', dutyModeDisabled: false//true
        }, {
          tradeMode: '0245', dutyMode: '1', dutyModeDisabled: false
        }, {
          tradeMode: '0644', dutyMode: '1', dutyModeDisabled: false
        }, {
          tradeMode: '0214', dutyMode: '3', dutyModeDisabled: false
        }, {
          tradeMode: '0615', dutyMode: '3', dutyModeDisabled: false
        }, {
          tradeMode: '0255', dutyMode: '3', dutyModeDisabled: true
        }, {
          tradeMode: '0654', dutyMode: '3', dutyModeDisabled: true
        }, {
          tradeMode: '0258', dutyMode: '3', dutyModeDisabled: true
        }, {
          tradeMode: '0657', dutyMode: '3', dutyModeDisabled: true
        }
      ]
    },
    /**
     * 根据规则获取与账册及监管方式相对应的征免性质与物料类型
     * @param emsNo
     * @param tradeMode
     * @param ieMark
     * @returns {{cutMode: string, cutModeDisable: *, gMark: string, gMarkDisable: *}}
     */
    getRegulatoryRule(emsNo, tradeMode, ieMark) {
      let cutModeDisable = this.showDisable
      let gMarkDisable = this.showDisable
      let theConfig = []
      if (!isNullOrEmpty(tradeMode) && ['I', 'E'].includes(ieMark)) {
        if (ieMark === 'I') {
          let importConfig = this.getImportConfig()
          if (!isNullOrEmpty(emsNo)) {
            let bookHead = emsNo.substring(0, 1).toUpperCase()
            theConfig = importConfig.filter(item => {
              return (item.tradeMode === tradeMode && item.bookHead.includes(bookHead))
            })
          } else {
            theConfig = importConfig.filter(item => {
              return (item.tradeMode === tradeMode && item.bookHead.length === 0)
            })
          }
        } else {
          let exportConfig = this.getExportConfig()
          if (!isNullOrEmpty(emsNo)) {
            let bookHead = emsNo.substring(0, 1).toUpperCase()
            theConfig = exportConfig.filter(item => {
              return (item.tradeMode === tradeMode && item.bookHead.includes(bookHead))
            })
          } else {
            theConfig = exportConfig.filter(item => {
              return (item.tradeMode === tradeMode && item.bookHead.length === 0)
            })
          }
        }
      }
      if (theConfig.length > 0) {
        // 暂时在存在配置时不控制
        // if (!isNullOrEmpty(theConfig[0].cutMode)) {
        //   cutModeDisable = true
        // }
        if (!isNullOrEmpty(theConfig[0].gMark)) {
          gMarkDisable = true
        }
        return {
          cutMode: theConfig[0].cutMode,
          cutModeDisable: cutModeDisable,
          gMark: theConfig[0].gMark,
          gMarkDisable: gMarkDisable
        }
      }
      return {
        cutMode: '',
        cutModeDisable: cutModeDisable,
        gMark: '',
        gMarkDisable: gMarkDisable
      }
    },
    /**
     * 根据监管方式及进出口标志获取【料件/成品标记】、【原产国】、【目的国】、【征免方式】默认值
     * @param tradeMode
     * @param ieMark
     */
    getDefaultByRule(tradeMode, ieMark) {
      let me = this
      // 默认值
      let result = {
        gMark: '',
        originCountry: '',
        destinationCountry: '',
        dutyMode: '',
        dutyModeDisabled: me.showDisable
      }
      // 新规则
      if (!isNullOrEmpty(tradeMode) && ['I', 'E'].includes(ieMark)) {
        if (ieMark === 'I') {
          let importConfig2 = me.getImportConfig2()
          let iConfigs = importConfig2.special.filter(item => {
            return item.tradeMode === tradeMode
          })
          if (iConfigs.length > 0) {
            result.gMark = iConfigs[0].gMark
            result.originCountry = iConfigs[0].originCountry
            result.destinationCountry = iConfigs[0].destinationCountry
            result.dutyMode = iConfigs[0].dutyMode
            if (iConfigs[0].dutyModeDisabled) {
              result.dutyModeDisabled = true
              return result
            }
          } else {
            result.gMark = importConfig2.normal.gMark
            result.originCountry = importConfig2.normal.originCountry
            result.destinationCountry = importConfig2.normal.destinationCountry
            result.dutyMode = importConfig2.normal.dutyMode
          }
        } else {
          let exportConfig2 = me.getExportConfig2()
          let eConfigs = exportConfig2.special.filter(item => {
            return item.tradeMode === tradeMode
          })
          if (eConfigs.length > 0) {
            result.gMark = eConfigs[0].gMark
            result.originCountry = eConfigs[0].originCountry
            result.destinationCountry = eConfigs[0].destinationCountry
            result.dutyMode = eConfigs[0].dutyMode
            if (eConfigs[0].dutyModeDisabled) {
              result.dutyModeDisabled = true
              return result
            }
          } else {
            result.gMark = exportConfig2.normal.gMark
            result.originCountry = exportConfig2.normal.originCountry
            result.destinationCountry = exportConfig2.normal.destinationCountry
            result.dutyMode = exportConfig2.normal.dutyMode
          }
        }
      }
      // 旧规则
      let ieConfig2Comm = this.getIEConfig2Comm()
      let orgConfig = ieConfig2Comm.filter(item => {
        return item.tradeMode === tradeMode
      })
      if (orgConfig.length > 0) {
        if (isNullOrEmpty(result.dutyMode)) {
          result.dutyMode = orgConfig[0].dutyMode
        }
        if (orgConfig[0].dutyModeDisabled) {
          result.dutyModeDisabled = true
        }
      }
      return result
    },
    /**
     * 根据EmsNo及进出口标识获取需过滤的监管方式
     * @param emsNo
     * @param ieMark
     * @param fullTradeModeList
     */
    getFilterByEmsNo(emsNo, ieMark, fullTradeModeList) {
      let me = this,
        results = [],
        includes = false

      if (!Array.isArray(fullTradeModeList)) {
        fullTradeModeList = me.fullTradeModeList
      }
      if (['I', 'E'].includes(ieMark)) {
        let bookHead = ''
        if (!isNullOrEmpty(emsNo) && emsNo.trim().length > 0) {
          bookHead = emsNo.substring(0, 1).toUpperCase()
        }
        includes = ['B', 'C', 'E', 'H'].includes(bookHead)
        if (ieMark === 'I') {
          let importConfig = me.getImportConfig()
          if (includes) {
            if (bookHead === 'H') {
              results = fullTradeModeList.map(item => {
                return item.value
              })
            } else {
              results = importConfig.filter(item => {
                return item.bookHead.includes(bookHead)
              }).map(item2 => {
                return item2.tradeMode
              })
            }
            results.push('9700')
          } else {
            results = importConfig.filter(item => {
              return item.bookHead.includes('E') && !['0200'].includes(item.tradeMode)
            }).map(item2 => {
              return item2.tradeMode
            })
          }
        } else {
          let exportConfig = me.getExportConfig()
          if (includes) {
            if (bookHead === 'H') {
              results = fullTradeModeList.map(item => {
                return item.value
              })
            } else {
              results = exportConfig.filter(item => {
                return item.bookHead.includes(bookHead)
              }).map(item2 => {
                return item2.tradeMode
              })
            }
          } else {
            results = exportConfig.filter(item => {
              return item.bookHead.includes('E') && !['0200'].includes(item.tradeMode)
            }).map(item2 => {
              return item2.tradeMode
            })
          }
        }
      }
      return {
        includes: includes,
        results: results
      }
    },
    /**
     * 根据EmsNo及进出口标识获取过滤后的监管方式
     * @param emsNo
     * @param ieMark
     */
    getTradeModeByEmsNo(emsNo, ieMark) {
      let me = this
      let filterConfig = me.getFilterByEmsNo(emsNo, ieMark)
      filterConfig.results.push('AAAA')
      if (filterConfig.includes) {
        filterConfig.results.push('4500')
        return me.fullTradeModeList.filter(item => {
          return filterConfig.results.includes(item.value)
        })
      } else {
        return me.fullTradeModeList.filter(item => {
          return !filterConfig.results.includes(item.value)
        })
      }
    }
  }
}

export const getTradeModeByEmsNo = (emsNo, ieMark, fullData) => {
  let me = regulatoryRules.methods
  let filterConfig = me.getFilterByEmsNo(emsNo, ieMark, fullData)
  filterConfig.results.push('AAAA')
  if (filterConfig.includes) {
    filterConfig.results.push('4500')
    return fullData.filter(item => {
      return filterConfig.results.includes(item.value)
    })
  } else {
    return fullData.filter(item => {
      return !filterConfig.results.includes(item.value)
    })
  }
}
