import { csAPI } from '@/api'
import { dynamicHeight } from '@/common'
import columns from './dec-erp-cert-columns'
import { editStatus, importExportManage } from '@/view/cs-common'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import DocumentPreview from '../../cs-document-center/components/documentPreview'

export default {
  name: 'DecErpCert',
  mixins: [dynamicHeight, columns],
  components: {
    DocumentPreview
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    },
    iemark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    bondMark: {
      type: String,
      require: true,
      validate: function (value) {
        return ['0', '1'].includes(value)
      }
    }
  },
  data() {
    return {
      actions1: [{
        needed: true,
        loading: false,
        disabled: false,
        command: 'take',
        label: '提取证书',
        key: 'xdo-btn-apply',
        icon: 'ios-create-outline'
      }],
      actions2: [{
        needed: true,
        label: '保存',
        loading: false,
        disabled: false,
        command: 'save',
        key: 'xdo-btn-edit',
        icon: 'ios-checkmark'
      }],
      toolbarEventMap: {
        'take': this.handleTake,
        'save': this.handleSave
      },
      useData: [],
      pdfData: [],
      certData: [],
      currentRow: {},
      isChecked: false,
      showModal: false,
      selectUseRows: [],
      selectCertRows: [],
      importExportManage: importExportManage
    }
  },
  mounted() {
    let me = this
    me.refreshDynamicHeight(270, null)
    if (me.parentConfig.editStatus === editStatus.SHOW) {
      me.actions = []
    }
    me.getUseData()
  },
  methods: {
    handleTake() {
      let me = this
      // 点击提取证书，显示列表数据
      me.actions1[0].loading = true
      let decErpHeadId = me.parentConfig.editData.sid
      me.$http.post(`${csAPI.csImportExport.cert.extractCertificate}/${decErpHeadId}/${me.iemark}`).then(() => {
        me.getUseData()
      }).catch(() => {
      }).finally(() => {
        me.actions1[0].loading = false
      })
    },
    getUseData() {
      let me = this,
        decErpHeadId = me.parentConfig.editData.sid
      me.$http.get(`${csAPI.csImportExport.cert.getInvolveCerts}/${decErpHeadId}`).then(res => {
        me.useData = res.data.data
      }).catch(() => {
      })
    },
    handleSave() {
      let me = this
      // 选择一条证书数据，点击保存，将证件编号带入适用证书
      if (me.selectCertRows.length > 0) {
        if (me.selectCertRows.length === 1) {
          let deductId = me.selectUseRows[0].sid
          if (deductId) {
            let data = Object.assign({}, me.selectCertRows[0], {
              certId: me.selectCertRows[0].headId,
              qty: me.selectCertRows[0].remainQty,
              certListId: me.selectCertRows[0].sid
            })
            me.$http.post(`${csAPI.csImportExport.cert.save}/${deductId}/${me.iemark}`, data).then(() => {
              me.getUseData()
              me.selectUseChange(me.selectUseRows)
              me.$Message.success('适用证书保存成功!')
            }).catch(() => {
            }).finally(() => {
              me.selectCertRows = []
              me.currentRow = []
              me.$forceUpdate()
            })
          }
          me.selectCertRows = []
          me.currentRow = []
          me.$forceUpdate()
        } else {
          me.$Message.warning('仅可选择一条数据保存!')
        }
      } else {
        me.$Message.warning('请选择一条数据保存!')
      }
    },
    selectUseChange(val) {
      let me = this
      // 根据选择的提取证书的数据带出选择证书的列表数据
      me.selectUseRows = val
      if (val.length > 0) {
        let catalogListId = val[0]['catalogListId']
        me.$http.get(`${csAPI.csImportExport.cert.getSpecifiedCertList}/${catalogListId}`).then(res => {
          me.certData = res.data.data
          for (let i = 0; i < me.certData.length; i++) {
            if (me.certData[i].documentNo === me.selectUseRows[0].documentNo) {
              me.currentRow = me.certData[i]
              me.$forceUpdate()
            }
          }
        }).catch(() => {
        }).finally(() => {
          me.$nextTick(() => {
            let selRows = []
            for (let i = 0; i < me.certData.length; i++) {
              if (me.certData[i].sid === me.currentRow.sid) {
                selRows.push(deepClone(me.certData[i]))
              }
            }
            me.selectCertRows = selRows
          })
        })
      } else {
        me.certData = []
      }
    },
    handleCurrentChange(currentRow) {
      this.currentRow = currentRow
      this.selectCertRows[0] = currentRow
      this.$forceUpdate()
    },
    handleSelectionChange(selection, row) {
      this.currentRow = row
      this.$forceUpdate()
    },
    handleToolbarClick(command) {
      let me = this
      if (me.toolbarEventMap[command] && typeof me.toolbarEventMap[command === 'function']) {
        me.toolbarEventMap[command]()
      } else {
        console.warn(`${command}没有对应的处理方法`)
      }
    },
    handleView(sid) {
      let me = this
      me.$http.post(csAPI.attachedInfo.list, {
        businessSid: sid
      }).then(res => {
        me.pdfData = res.data.data
        me.showModal = true
      }).catch(() => {
      })
    }
  }
}
