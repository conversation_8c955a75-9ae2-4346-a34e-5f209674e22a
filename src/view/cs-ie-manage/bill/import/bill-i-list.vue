<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <BillIESearch ref="headSearch" :iemark="iemark" :bond-mark="bondMark"></BillIESearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:bill-print>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>打印清单<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="billPrint('pdf')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  合并打印(PDF)
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="billPrint('zip')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  压缩打印(ZIP)
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:draft-print>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>打印草单<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="draftPrint('pdf')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  合并打印(PDF)
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="draftPrint('zip')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  压缩打印(ZIP)
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <BillDetailSingle v-if="!showList" :editConfig="currEditConfig" :iemark="iemark" :bond-mark="bondMark"
                      @onBackToList="backToList"></BillDetailSingle>
    <BillSync :show.sync="show" :url="ajaxUrl.billSync"></BillSync>
    <BillErrMsg :show.sync="errMsgInfo.modelErrMsgShow"
                :headId="errMsgInfo.sid" :taskId="errMsgInfo.sendApiTaskId" :url="errMsgInfo.loadUrl"
                @errMessage:hide="hideErrMsg"></BillErrMsg>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" class="height:500px"
                      @updateColumns="handleUpdateColumn" :columns="columnsarr"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { billIEList } from '@/view/cs-ie-manage/js/bill/billIEList'

  export default {
    name: 'billIList',
    mixins: [billIEList],
    data() {
      return {
        iemark: 'I',
        tableId: '',
        bondMark: '0',
        columnsarr: [],
        tableShow: true,
        columnsConfig: [],
        alltotalColumns: [],
        show: false,
        gridConfig: {
          exportTitle: '进口清单'
        },
        showtableColumnSetup: false,
        ajaxUrl: {
          exportUrl: csAPI.csImportExport.iBill.export,
          billSync: csAPI.csImportExport.iBill.billSync,
          sendBills: csAPI.csImportExport.iBill.sendBills,
          printBillList: csAPI.csImportExport.iBill.printBillList,
          selectAllPaged: csAPI.csImportExport.iBill.selectBillHead,
          sendBillCancel: csAPI.csImportExport.iBill.sendBillCancel,
          printEntryList: csAPI.csImportExport.customsDeclaration.printIEntryList
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
