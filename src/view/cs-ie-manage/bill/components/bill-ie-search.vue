<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="清单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="listNo" label="核注清单编号">
        <XdoIInput type="text" v-model="searchParam.listNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="apprStatus" label="内审状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.cmbSource.apprStatusData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="sendApiStatus" label="清单状态">
        <xdo-select v-model="searchParam.sendApiStatus" :options="this.cmbSource.sendApiStatusData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryType" label="报关单类型">
        <xdo-select v-model="searchParam.entryType" :options="this.cmbSource.entryTypeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="发送日期" @onDateRangeChanged="handleSendApiTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { namespace } from '@/project'
  import { isNullOrEmpty } from '@/libs/util'
  import { importExportManage } from '@/view/cs-common'
  import { detailGeneralMethod } from '@/view/cs-productClassify/base/detailGeneralMethod'

  export default {
    name: 'billIESearch',
    mixins: [detailGeneralMethod],
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',
          listNo: '',
          entryNo: '',
          apprStatus: '',
          sendApiStatus: '',
          emsNo: '',
          trafMode: '',
          tradeMode: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          iemark: this.iemark,
          bondMark: this.bondMark,
          contrNo: '',
          entryType: '',
          sendApiTimeFrom: '',
          sendApiTimeTo: ''
        },
        cmbSource: {
          emsNoData: [],
          apprStatusData: [],
          entryTypeData: importExportManage.entryType,
          sendApiStatusData: importExportManage.BILL_STATUS_MAP
        }
      }
    },
    created: function () {
      let me = this
      me.getEmsNoList((req) => {
        if (!req.data) {
          return []
        }
        let emsNoDataArr = []
        for (let item of req.data) {
          if (!isNullOrEmpty(item.emsNo)) {
            emsNoDataArr.push({
              value: item.emsNo,
              label: item.emsNo
            })
          }
        }
        me.cmbSource.emsNoData = emsNoDataArr
      })
      me.cmbSource.apprStatusData = importExportManage.auditStatusMap.filter(item => {
        return item.value !== '0'
      })
    },
    mounted: function () {
      let me = this
      me.$set(me.searchParam, 'emsNo', me.$store.getters[`${namespace}/selectedManual`])
    },
    methods: {
      handleValidDateChange(values) {
        let me = this
        if (values instanceof Array && values.length === 2) {
          me.$set(me.searchParam, 'insertTimeFrom', values[0])
          me.$set(me.searchParam, 'insertTimeTo', values[1])
        } else {
          me.$set(me.searchParam, 'insertTimeFrom', '')
          me.$set(me.searchParam, 'insertTimeTo', '')
        }
      },
      handleSendApiTimeChange(values) {
        let me = this
        if (values instanceof Array && values.length === 2) {
          me.$set(me.searchParam, 'sendApiTimeFrom', values[0])
          me.$set(me.searchParam, 'sendApiTimeTo', values[1])
        } else {
          me.$set(me.searchParam, 'sendApiTimeFrom', '')
          me.$set(me.searchParam, 'sendApiTimeTo', '')
        }
      }
    },
    computed: {
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style scoped>
</style>
