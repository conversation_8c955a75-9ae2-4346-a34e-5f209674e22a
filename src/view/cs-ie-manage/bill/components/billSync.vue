<template>
  <srction>
    <XdoModal v-model="show" mask width="500" title="请选择手/帐册号"
              :mask-closable="false" footer-hide :closable="false">
      <a class="ivu-modal-close" @click="handleClose">
        <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
      </a>
      <XdoForm ref="formInline" class="dc-form" :model="frmData" label-position="right" :label-width="100" inline>
        <XdoFormItem prop="emsListNo" label="手/帐册号" class="dc-merge-1-4">
          <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoData"></xdo-select>
        </XdoFormItem>
      </XdoForm>
      <div class="xdo-enter-action" style="text-align: center; margin-top: 10px;">
        <Button type="primary" :loading="confirmLoading" @click="handleConfirm">清单同步</Button>
      </div>
    </XdoModal>
  </srction>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    props: {
      show: {
        type: Boolean,
        required: true
      },
      url: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        frmData: {
          emsNo: ''
        },
        cmbSource: {
          emsNoData: []
        },
        gridData: [],
        confirmLoading: false
      }
    },
    created: function() {
      let me = this
      me.$http.post(csAPI.csProductClassify.bonded.getCopEmsNoOld, {
        UserInfoToken: me.$store.state.user.userNo
      }).then(res => {
        let emsNoObj = []
        for (let item of res.data.data) {
          emsNoObj.push({
            value: item.emsNo,
            label: item.emsNo
          })
        }
        me.cmbSource.emsNoData = emsNoObj
      })
    },
    methods: {
      handleConfirm() {
        let me = this
        if (isNullOrEmpty(me.frmData.emsNo)) {
          me.$Message.warning('请选择手/帐册号！')
        } else {
          me.$http.post(me.url + '/' + me.frmData.emsNo).then(res => {
            if (res) {
              me.$Message.success('已发送同步清单请求，请等待后台任务执行')
              me.frmData.emsNo = ''
              me.$emit('update:show', false)
            }
          }).catch(() => {
          })
        }
      },
      handleClose() {
        let me = this
        me.frmData.emsNo = ''
        me.$emit('update:show', false)
      }
    }
  }
</script>
