<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <Layout>
          <Content>
            <DynamicHead v-if="dynamicHeadTabShow" ref="DynamicHead" ie-mark="I" bond-mark="0"
                         :edit-config="editConfig" :saved-audit-data="headAuditData"
                         @onEditBack="editBack" @onAfterHeadSaved="afterHeadSaved"></DynamicHead>
            <Head v-else ref="head" :edit-config="editConfig" :saved-audit-data="headAuditData"
                  @onEditBack="editBack" @onAfterHeadSaved="afterHeadSaved"></Head>
          </Content>
          <Sider hide-trigger collapsible width="380" collapsed-width="36" v-model="collapsed"
                 style="overflow: hidden; background: transparent; height: 732px;">
            <div class="right-sider">
              <XdoIcon type="md-menu" @click.native="handleRightSliderClick"/>
            </div>
            <XdoCard>
              <p ref="attachTitle" style="font-weight: bold; padding: 3px 10px; border-bottom: #dcdee2 solid 1px;">
                随附单证
              </p>
              <DecAttachedDocumentsList :parent-config="parentConfig" ie-mark="I" :aeo-show="!showBody"></DecAttachedDocumentsList>
            </XdoCard>
          </Sider>
        </Layout>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <Body ref="body" v-if="tabs.bodyTab" :parent-config="parentConfig" iemark="I" bond-mark="0" :body-audits="bodyAuditData"
              @refreshBill:success="refreshBill" @goToShowStatus="goToShowStatus"></Body>
      </TabPane>
      <TabPane name="certTab" v-if="certTabShow" label="涉证管理" index="'3'">
        <Cert ref="cert" v-if="tabs.certTab" :parent-config="parentConfig" iemark="I" bond-mark="0" @onEditBack="editBack"></Cert>
      </TabPane>
      <TabPane name="billTab" v-if="showBody" label="草单" index="'4'">
        <Bill ref="bill" v-if="tabs.billTab" :parent-config="parentConfig" iemark="I" bond-mark="0" @onEditBack="editBack"></Bill>
      </TabPane>
      <TabPane name="logisticsTab" v-if="logisticsTabShow" label="物流追踪" index="'5'">
        <Logistics ref="logistics" v-if="tabs.logisticsTab" :head-id="parentConfig.editData.sid"  @onEditBack="editBack"></Logistics>
      </TabPane>
      <TabPane name="entryTab" v-if="entryTabShow" label="报关追踪" index="'6'">
        <CustomsTrack ref="entryTrack" v-if="tabs.entryTab" :head-id="parentConfig.editData.sid"  @onEditBack="editBack"></CustomsTrack>
      </TabPane>
      <TabPane name="attachTab" v-if="showBody" label="随附单据" index="'7'">
        <Attach ref="attachInfo" v-if="tabs.attachTab" :head-id="parentConfig.editData.sid" :edit="parentConfig" iemark="I" :loaded="attachLock"></Attach>
      </TabPane>
      <TabPane name="aeoTab" v-if="showBody" label="内审情况" index="'8'">
        <AeoInfoList ref="aeoInfo" v-if="tabs.aeoTab" :sid="parentConfig.editData.sid" :show-title="aeoShowTitle" ></AeoInfoList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Logistics from '@/view/cs-importManage/logistics/Logistics'
  import CustomsTrack from '@/view/cs-importManage/entry/customs-tracking-i'
  import Head from '@/view/cs-ie-manage/dec-erp-head/import/bonded-ihead-edit'
  import { decErpHeadIETabs } from '@/view/cs-ie-manage/js/dec-erp-head/decErpHeadIETabs'

  export default {
    name: 'bondedIHeadTabs',
    components: {
      Head,
      Logistics,
      CustomsTrack
    },
    mixins: [decErpHeadIETabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  .right-sider {
    height: 38px;
    padding: 7px 0 0 6px;
    background: rgb(245, 247, 247);
    border-bottom: 1px solid rgb(214, 219, 222);

    .ivu-layout-sider-children {
      overflow-y: hidden;
      margin-right: -18px;
    }
  }

  .right-sider i {
    color: #389de9;
    cursor: pointer;
    font-size: 26px;
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    transition: transform .2s linear;
    -webkit-transition: -webkit-transform .2s linear;
  }
</style>
