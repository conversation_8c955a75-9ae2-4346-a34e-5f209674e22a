import { addEvent, isNullOrEmpty, isNumber } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

function showCellRenderer() {
}

showCellRenderer.prototype.init = function(params) {
  let vm = undefined,
    vmParent = undefined,
    viewMarginLeft = '15px',
    divContainer = document.createElement('div'),
    fwComWrapper = params['frameworkComponentWrapper']
  if (fwComWrapper) {
    vmParent = fwComWrapper.parent
  }
  while (vmParent.$parent && typeof vmParent.$parent['cellEditStyle'] !== 'function') {
    vmParent = vmParent.$parent
  }
  if (vmParent.$parent) {
    vm = vmParent.$parent
  }

  let viewA = document.createElement('a')
  viewA.innerHTML = params.data['hawb']
  viewA.setAttribute('type', 'primary')
  viewA.style.marginLeft = viewMarginLeft
  if (vm) {
    if (vm.canManiFestShow) {
      addEvent(viewA, 'click', function () {
        if (vm && typeof vm.showWarehouseReceiptInfo === 'function') {
          vm.showWarehouseReceiptInfo(params.data)
        }
      })
    } else {
      viewA.setAttribute('style', 'color: black')
    }
  }
  divContainer.appendChild(viewA)
  this.eGui = divContainer
}

showCellRenderer.prototype.getGui = function() {
  return this.eGui
}

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      components: {
        showCellRenderer: showCellRenderer
      },
      totalColumns: [{
        width: 120,
        title: '内审状态',
        key: 'apprStatusName',
        render: (h, params) => {
          return this.keyValueRender(h, params, 'apprStatus', 'apprStatusName')
        }
      }, {
        width: 180,
        tooltip: true,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 100,
        title: '制单日期',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 200,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 200,
        key: 'entryDeclareDate',
        title: '申报日期'
      }, {
        width: 120,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 210,
        tooltip: true,
        title: '境外收货人',
        key: 'overseasShipperName'
      }, {
        width: 120,
        key: 'hawb',
        title: '提运单号',
        cellRenderer: 'showCellRenderer'
      }, {
        width: 120,
        key: 'trafName',
        title: '运输工具名称'
      }, {
        width: 120,
        title: '航次号',
        key: 'voyageNo'
      }, {
        width: 120,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 150,
        key: 'netWt',
        title: '净重'
      }, {
        width: 150,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 150,
        title: '件数',
        key: 'packNum'
      }, {
        width: 150,
        title: '体积',
        key: 'volume'
      }, {
        width: 150,
        title: '监管方式',
        key: 'tradeMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      }, {
        width: 150,
        title: '入境口岸',
        key: 'entryPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
        }
      }, {
        width: 80,
        title: '随附单据',
        key: 'attachName'
      }, {
        width: 120,
        title: '制单员',
        key: 'userName'
      }, {
        width: 120,
        title: '内审员',
        key: 'apprUserFull'
      }, {
        width: 150,
        title: '申报地海关',
        key: 'masterCustoms',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      }, {
        width: 100,
        key: 'iedate',
        title: '进口日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 90,
        tooltip: true,
        key: 'contrNo',
        title: '合同协议号'
      }, {
        width: 90,
        tooltip: true,
        key: 'remark',
        title: '内部备注'
      }, {
        width: 90,
        key: 'note',
        title: '备注',
        tooltip: true
      }, {
        width: 150,
        key: 'entryType',
        title: '报关单类型',
        render: (h, params) => {
          let me = this,
            entryTypeData = me.getRealEntryType()
          if (me.IEMark === 'I') {
            if (me.bondMark === '1') {
              let showLabel = '',
                theValue = params.row['entryType'],
                theItem = entryTypeData.find(item => item.value === theValue)
              if (theItem) {
                showLabel = theItem['label']
              }
              return h('span', showLabel)
            }
          }
          return me.cmbShowRender(h, params, entryTypeData)
        }
      }, {
        /*****LG定制************/
        width: 150,
        key: 'inviteDate',
        title: '申请进/出口日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        key: 'cweight',
        title: '计费重量'
      }, {
        /*****LG定制*******end*****/
        width: 120,
        title: '免表编号',
        key: 'exemptsNo'
      }, {
        width: 100,
        key: 'preStatus',
        title: '接单状态',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.importExportManage.ORDER_STATUS_MAP)
        }
      }, {
        width: 88,
        key: 'preDate',
        title: '接单时间',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        title: '预报单编号',
        key: 'preEmsListNo'
      }, {
        key: 'sa',
        width: 120,
        title: 'SA'
      }, {
        width: 136,
        title: '系统加工费',
        key: 'decTotalProcessCurr',
        render: (h, params) => {
          let total = params.row['decTotalProcess'],
            curr = params.row[params.column.key],
            currName = this.pcodeGet(this.pcode.curr_outdated, curr)
          if (isNumber(total) && !isNullOrEmpty(currName)) {
            return h('span', String(total) + ' ' + currName)
          }
          return h('span', '')
        }
      }, {
        width: 126,
        title: '内包装',
        key: 'nootherPack'
      }, {
        width: 136,
        title: '实收汇',
        key: 'payTotalCurr',
        render: (h, params) => {
          let total = params.row['payTotal'],
            curr = params.row[params.column.key],
            currName = this.pcodeGet(this.pcode.curr_outdated, curr)
          if (isNumber(total) && !isNullOrEmpty(currName)) {
            return h('span', String(total) + ' ' + currName)
          }
          return h('span', '')
        }
      }, {
        width: 136,
        title: '客供',
        key: 'clientTotalCurr',
        render: (h, params) => {
          let total = params.row['clientTotal'],
            curr = params.row[params.column.key],
            currName = this.pcodeGet(this.pcode.curr_outdated, curr)
          if (isNumber(total) && !isNullOrEmpty(currName)) {
            return h('span', String(total) + ' ' + currName)
          }
          return h('span', '')
        }
      }, {
        width: 60,
        title: '封装',
        key: 'packages',
        render: (h, params) => {
          let packages = params.row[params.column.key],
            packagesName = ''
          if (packages === '1') {
            packagesName = '是'
          } else if (packages === '0') {
            packagesName = '否'
          }
          return h('span', {
            style: {
              paddingLeft: '23px'
            }
          }, packagesName)
        }
      }, {
        width: 128,
        key: 'billTo',
        title: 'billTo代码'
      }, {
        width: 128,
        key: 'notify',
        title: 'notify代码'
      }, {
        width: 110,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 150,
        key: 'ieport',
        title: '进境关别',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      },{
        width: 120,
        key: 'transMode',
        title: '成交方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transac)
        }
      }
      , {
        width: 100,
        key: 'invoiceDate',
        title: '发票日期',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 100,
        key: 'shipToName',
        title: 'Ship To'
      }, {
          width: 100,
          key: 'batchStatus',
          title: '批次状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.batchStatusDec)
          }
        }, {
          width: 100,
          key: 'batchNo',
          title: '批次号'
        },
        // {
        //   width: 100,
        //   key: 'subImportNo',
        //   title: '进口分运单号'
        // },{
        //   width: 100,
        //   key: 'importDeliveryNo',
        //   title: '进口海运提单号'
        // },
        {
          width: 120,
          key: 'orderType',
          title: '业务类型',
          type: 'select',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.orderType)
          }
        }, {
          width: 100,
          key: 'oaNo',
          title: 'OA流程号'
        }
      ]
    }
  },
  /**
   * 方法
   */
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
