<template>
  <XdoModal width="600" mask v-model="show" title="回填提运单信息"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm class="xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="voyageDate" label="航班日期">
          <XdoDatePicker type="date" v-model="frmData.voyageDate" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="hawb" label="提运单号">
          <XdoIInput type="text" v-model="frmData.hawb"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="trafName" label="运输工具及航次">
          <XdoIInput type="text" v-model="frmData.trafName" :maxlength="50"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="forwardCode" label="货运代理">
          <xdo-select v-model="frmData.forwardCode" :options="this.cmbSource.forwardCodeList"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="volume" label="体积">
          <xdo-input v-model="frmData.volume" decimal int-length="11" :precision="precisionsConfig.volumeDigit" notConvertNumber></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="invoiceNo" label="发票号">
          <XdoIInput type="text" v-model="frmData.invoiceNo" :maxlength="100"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="iedate" :label="iEDateLabel">
          <XdoDatePicker type="date" v-model="frmData.iedate" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="tradeTerms" label="贸易条款">
          <xdo-select v-model="frmData.tradeTerms" clearable :options="this.importExportManage.tradeTermList"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="masterCustoms" label="申报地海关">
          <xdo-select v-model="frmData.masterCustoms" :asyncOptions="pcodeList" transfer
                      :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="cweight" label="计费重量">
          <xdo-input v-model="frmData.cweight" decimal int-length="5" precision="2" notConvertNumber></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="grossWt" label="总毛重">
          <xdo-input v-model="frmData.grossWt" decimal int-length="13" precision="5" notConvertNumber></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="packNum" label="件数">
          <xdo-input v-model="frmData.packNum" number int-length="9" precision="0"></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="note" label="备注">
          <XdoIInput type="text" v-model="frmData.note" :maxlength="256"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="remark" label="内部备注">
          <XdoIInput type="text" v-model="frmData.remark" :maxlength="256"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop='despPort' v-if="iEMark==='I'" label='启运港'>
          <xdo-select v-model='frmData.despPort' :disabled='isChange' :asyncOptions='pcodeList' :meta='pcode.port_lin'
                      :optionLabelRender='pcodeRender'></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { importExportManage } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase, isNumber } from '@/libs/util'

  export default {
    props: {
      show: {
        type: Boolean,
        require: true
      },
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      originData: {
        type: Object,
        require: true,
        default: () => ({
          sid: '',
          hawb: '',
          trafName: '',
          voyageDate: '',
          forwardCode: '',
          volume: '',
          invoiceNo: '',
          iedate: '',
          tradeTerms: '',
          masterCustoms: '',
          cweight: null,
          grossWt: null,
          packNum: null,
          note: '',
          remark: '',
          despPort: ''
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        rulesHeader: {},
        frmData: {
          sid: '',
          hawb: '',
          trafName: '',
          voyageDate: '',
          forwardCode: '',
          volume: '',
          invoiceNo: '',
          iedate: '',
          tradeTerms: '',
          masterCustoms: '',
          cweight: null,
          grossWt: null,
          packNum: null,
          note: '',
          remark: '',
          despPort: ''
        },
        cmbSource: {
          forwardCodeList: []
        },
        importExportManage: importExportManage,
        buttons: [
          {...btnComm, click: this.handleConfirm, icon: 'dc-btn-save', label: '保存'},
          {...btnComm, click: this.handleClose, icon: 'dc-btn-cancel', label: '关闭'}
        ]
      }
    },
    created: function () {
      let me = this
      // 货代
      me.$http.post(csAPI.ieParams.FOD).then(res => {
        me.cmbSource.forwardCodeList = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.forwardCodeList = []
      })
    },
    computed: {
      iEDateLabel() {
        let me = this
        if (me.iEMark === 'I') {
          return '进口日期'
        } else if (me.iEMark === 'E') {
          return '出口日期'
        } else {
          return '进出口日期'
        }
      },
      /**
       * 通关业务设置
       */
      configData() {
        let me = this
        return me.$store.state[`${namespace}`].clearanceBusinessSetting
      },
      /**
       * 体积
       * @returns {any}
       */
      precisionsConfig() {
        let me = this,
          config = {
            volumeDigit: 5
          }
        if (me.iEMark === 'I') {
          if (isNumber(me.configData.headVolumeDigitI)) {
            config.volumeDigit = Number(me.configData.headVolumeDigitI)
          }
        } else {
          if (isNumber(me.configData.headVolumeDigitE)) {
            config.volumeDigit = Number(me.configData.headVolumeDigitE)
          }
        }
        return config
      }
    },
    watch: {
      originData: {
        deep: true,
        immediate: true,
        handler: function (data) {
          let me = this
          me.$set(me.frmData, 'sid', data.sid || '')
          me.$set(me.frmData, 'hawb', data.hawb || '')
          me.$set(me.frmData, 'trafName', data.trafName || '')
          me.$set(me.frmData, 'voyageDate', data.voyageDate || '')
          me.$set(me.frmData, 'forwardCode', data.forwardCode || '')
          me.$set(me.frmData, 'volume', isNumber(data.volume) ? Number(data.volume) : null)
          me.$set(me.frmData, 'invoiceNo', data.invoiceNo || '')
          me.$set(me.frmData, 'iedate', data.iedate || '')
          me.$set(me.frmData, 'tradeTerms', data.tradeTerms || '')
          me.$set(me.frmData, 'masterCustoms', data.masterCustoms || '')
          me.$set(me.frmData, 'cweight', isNumber(data.cweight) ? Number(data.cweight) : null)
          me.$set(me.frmData, 'grossWt', isNumber(data.grossWt) ? Number(data.grossWt) : null)
          me.$set(me.frmData, 'packNum', isNumber(data.packNum) ? Number(data.packNum) : null)
          me.$set(me.frmData, 'note', data.note || '')
          me.$set(me.frmData, 'remark', data.remark || '')
          me.$set(me.frmData, 'despPort', data.despPort || '')
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$emit('doBackFill', me.frmData)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
