<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="apprStatus" label="内审状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.cmbDataSource.apprStatusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafName" label="运输工具名称">
        <XdoIInput type="text" v-model="searchParam.trafName" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryPort" :label="entryPortLabel">
        <xdo-select v-model="searchParam.entryPort" :asyncOptions="pcodeList" meta="CIQ_ENTY_PORT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="showEmsCmd" prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="attach" label="随附单据">
        <xdo-select v-model="searchParam.attach" :options="this.productClassify.ATTACH_SELECT"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="insertUserParam" label="制单员">
        <XdoIInput type="text" v-model="searchParam.insertUserParam" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="declareName" label="报关单申报单位">
        <XdoIInput type="text" v-model="searchParam.declareName" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="overseasShipper" :label="overseasShipperLabel">
        <xdo-select v-model="searchParam.overseasShipper" :options="this.cmbDataSource.overseasShipperList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="apprUserParam" label="内审员">
        <XdoIInput type="text" v-model="searchParam.apprUserParam" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="remark" label="内部备注">
        <XdoIInput type="text" v-model="searchParam.remark" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="note" label="备注">
        <XdoIInput type="text" v-model="searchParam.note" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="exemptsNoShow" prop="exemptsNo" label="免表编号">
        <XdoIInput type="text" v-model="searchParam.exemptsNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'I'" prop="entryType" :label="entryTypeLabel">
        <xdo-select v-if="bondMark === '0'" v-model="searchParam.entryType" :options="this.cmbDataSource.entryTypeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
        <xdo-select v-else-if="bondMark === '1'" v-model="searchParam.entryType" :options="this.cmbDataSource.entryTypeData"
                    :optionLabelRender="item => item.label"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="exemptsNoShow" prop="preEmsListNo" label="预报单编号">
        <XdoIInput type="text" v-model="searchParam.preEmsListNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="exemptsNoShow" prop="sa" label="SA">
        <XdoIInput type="text" v-model="searchParam.sa" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="exemptsNoShow" prop="preStatus" label="接单状态">
        <xdo-select v-model="searchParam.preStatus" :options="this.cmbDataSource.preStatus"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>

      <XdoFormItem v-if="iemark === 'I' && bondMark === '1'" prop="batchStatus" label="批次状态">
        <xdo-select v-model="searchParam.batchStatus" :options="this.cmbDataSource.batchStatusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>

      <XdoFormItem v-if="iemark === 'I' && bondMark === '1'" prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'E' && bondMark === '1'"   prop="orderType" label="业务类型">
        <xdo-select  v-model="searchParam.orderType" :options="this.cmbDataSource.orderType"
                     :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>

      <XdoFormItem v-if="iemark === 'E'"   prop="oaNo" label="OA流程号">
        <XdoIInput type="text" v-model="searchParam.oaNo" clearable></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { productClassify, importExportManage } from '@/view/cs-common'

  export default {
    name: 'decErpMainSearch',
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      },
      dataSource: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      let realEntryType = this.getRealEntryType()
      return {
        searchParam: {
          emsListNo: '',
          entryPort: '',
          tradeMode: '',
          trafMode: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          trafName: '',
          hawb: '',
          grossWt: '',
          packNum: '',
          insertUserParam: '',
          apprStatus: '',
          emsNo: '',
          attach: '',
          volume: '',
          invoiceNo: '',
          bondMark: this.bondMark,
          declareName: '',
          decType: this.dataSource,
          overseasShipper: '',
          entryNo: '',
          apprUserParam: '',
          contrNo: '',
          exemptsNo: '',
          entryType: '',
          sa: '',
          preStatus: '',
          preEmsListNo: '',
          remark: '',
          note: '',
          batchNo: '',
          batchStatus: '',
          orderType: '',
          oaNo:''
        },
        productClassify: productClassify,
        cmbDataSource: {
          emsNoList: [],
          // apprUserParamData: [],
          overseasShipperList: [],
          entryTypeData: realEntryType,
          preStatus: importExportManage.ORDER_STATUS_MAP,
          apprStatusList: importExportManage.auditStatusMap,
          batchStatusList: importExportManage.batchStatusDec,
          orderType: [{
            value: '0',
            label: 'PO退运'
          }, {
            value: '1',
            label: 'PO换货'
          }, {
            value: '2',
            label: '修理物品'
          }, {
            value: '3',
            label: '出口加工'
          }, {
            value: '4',
            label: 'Resale'
          }],
        }
      }
    },
    created: function () {
      // 备案号
      let me = this
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbDataSource.emsNoList = tmpArr
      }).catch(() => {
        me.cmbDataSource.emsNoList = []
      })
      // 境外收/发货人
      let overseasShipperUrl = ''
      if (me.iemark === 'I') {
        overseasShipperUrl = csAPI.ieParams.PRD
      } else if (me.iemark === 'E') {
        overseasShipperUrl = csAPI.ieParams.CLI
      }
      if (!isNullOrEmpty(overseasShipperUrl)) {
        me.$http.post(overseasShipperUrl).then(res => {
          me.cmbDataSource.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
        }).catch(() => {
          me.cmbDataSource.overseasShipperList = []
        })
      }
      // 内审人
      // me.$http.post(csAPI.aeoManage.comm.getapprUserParam).then(res => {
      //   me.cmbDataSource.apprUserParamData = res.data.data.map(item => {
      //     return {
      //       label: item['APPR_USER'],
      //       value: item['APPR_USER']
      //     }
      //   })
      // }).catch(() => {
      //   me.cmbDataSource.apprUserParamData = []
      // })
    },
    watch: {
      bondMark: {
        immediate: true,
        handler: function (bondMark) {
          if (bondMark === '0') {
            this.$set(this.searchParam, 'emsNo', this.$store.getters[`${namespace}/selectedManual`])
          }
        }
      }
    },
    computed: {
      entryPortLabel() {
        if (this.iemark === 'I') {
          return '入境口岸'
        } else if (this.iemark === 'E') {
          return '离境口岸'
        } else {
          return '未设置进出口标志'
        }
      },
      entryTypeLabel() {
        let me = this
        if (me.iemark === 'I' && me.bondMark === '1') {
          return '两步申报'
        }
        return '报关单类型'
      },
      showEmsCmd() {
        return this.bondMark === '0'
      },
      overseasShipperLabel() {
        if (this.iemark === 'I') {
          return '境外发货人'
        } else if (this.iemark === 'E') {
          return '境外收货人'
        } else {
          return '境外收/发货人'
        }
      },
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      },
      exemptsNoShow() {
        return this.iemark === 'I' && this.bondMark === '1'
      }
    },
    methods: {
      getRealEntryType() {
        let me = this
        if (me.iemark === 'I') {
          if (me.bondMark === '1') {
            return [{
              value: 'X', label: '是'
            }, {
              value: '0', label: '否'
            }]
          }
        }
        return importExportManage.entryType
      },
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
