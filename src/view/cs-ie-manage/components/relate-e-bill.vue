<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <XdoModal v-model="getEnShow" ref="modal" @on-visible-change="handleVisible" :mask-closable="true" :closable="true"
            :footer-hide="true" width="500" title="关联复运出口">
    <XdoForm ref="popForm" class="dc-form xdo-enter-form" :rules="rules" label-position="right" :label-width="100">
      <XdoFormItem prop="oaNo" label="出口复运OA流程编号" class="dc-merge-1-4">
        <xdo-select v-model="oaNo" :options="this.cmbSource.oaNoCmb" :optionLabelRender="pcodeRender" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem class="dc-margin-right">
        <XdoButton type="primary" class="dc-margin-right" @click="handleRelate">确定</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import {csAPI} from "@/api"

  export default {
    name: 'RelateEBill',
    props: {
      enShow: {
        type: Boolean,
        require: true,
      },
      medata: {
        type: String
      },
    },
    data() {
      return {
        oaNo: '',
        cmbSource:{
          oaNoCmb:[]
        },
        ajaxUrl:{
          getRelateOaNo: csAPI.csImportExport.decErpIHeadN.getRelateOaNo,
          relateEBill: csAPI.csImportExport.decErpIHeadN.relateEBill,
        }
      }
    },
    computed: {
      getEnShow() {
        return this.enShow
      }
    },
    mounted() {
      let me = this;
      me.$http.post(me.ajaxUrl.getRelateOaNo).then(res => {
        me.cmbSource.oaNoCmb = res.data.data.map(item =>{
          return {
            label: '',
            value: item,
          }
        })
      }).catch(() => {
        me.cmbSource.oaNoCmb = []
      })
    },
    methods: {
      handleVisible(val) {
        if (!val) {
          this.$emit('update:enShow', false)
        }
      },
      handleRelate(){
        let me = this
        if (!me.oaNo){
          me.$Message.warning("请先选择指派的货代！")
          return;
        }
        me.$http.post(me.ajaxUrl.relateEBill + '/' + me.medata + '/' + me.oaNo,{}).then( ()=> {
          me.$Message.success("保存成功")
        }).finally(()=>{
          me.oaNo = ''
          this.$emit('onAfterRelate',null)
        })
      }
    }
  }
</script>

<style scoped>

</style>

