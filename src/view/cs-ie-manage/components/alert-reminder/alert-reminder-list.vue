<template>
  <XdoModal width="1024" mask v-model="show" title="预警提醒"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="300"></DcAgGrid>
      <div ref="area_page" style="text-align: right; padding-right: 20px;">
        共 {{pageParam.dataTotal}} 条
      </div>
      <div style="text-align: center;">
        <XdoButton type="primary" class="dc-margin-right" :loading="loading" @click="continueProduct" :disabled="isContinue">继续</XdoButton>
        <XdoButton type="primary" class="dc-margin-right" @click="handleClose">取消</XdoButton>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { columns } from './alertReminderColumns'
  import { commList } from '@/view/cs-interim-verification/comm/commList'

  export default {
    mixins: [commList, columns],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      options: {
        type: Object,
        require: true,
        default: () => ({
          headId: '',
          ajaxUrl: '',
          warningMark: '1,2'
        })
      },
      errData: {
        type: Array,
        require: true,
        default: () => ({})
      },
      isContinue: {
        type: Boolean,
        default: () => ({})
      }
    },
    data() {
      return {
        loading: false
      }
    },
    created() {
      let me = this
      me.$set(me.gridConfig, 'gridColumns', me.totalColumns)
    },
    watch: {
      show(val) {
        if (val) {
          let me = this,
            theData = []
          if (Array.isArray(me.errData)) {
            theData = me.errData
          }
          me.gridConfig.data = theData
          me.pageParam.dataTotal = theData.length
        }
      },
      options: {
        deep: true,
        immediate: true,
        handler: function (opts) {
          let me = this
          me.$set(me.ajaxUrl, 'selectAllPaged', opts.ajaxUrl)
        }
      }
    },
    methods: {
      /**
       * 获取查询条件(可外部覆盖)
       */
      getSearchParams() {
        let me = this
        return {
          headId: me.options.headId,
          warningMark: me.options.warningMark
        }
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      continueProduct() {
        let me = this
        me.loading = true
        me.$emit('onContinueProduct')
      },
      rowClassName(row) {
        if (row.warningMark === '2') {
          return 'demo-table-info-row'
        }
        return ''
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .ivu-table .demo-table-info-row td {
    color: red;
    font-weight: bold;
  }
</style>
