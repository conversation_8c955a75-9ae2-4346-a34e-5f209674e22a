import { getKeyValue } from '@/libs/util'

export const columns = {
  data() {
    return {
      totalColumns: [{
        width: 80,
        key: 'dataMark',
        title: '类型标记',
        render: (h, params) => {
          if (params.row['dataMark'] === 'head') {
            if (params.row.warningMark === '2') {
              return h('span', {
                style: {
                  color: 'red'
                }
              }, '表头')
            } else {
              return h('span', {}, '表头')
            }
          } else if (params.row['dataMark'] === 'list') {
            if (params.row.warningMark === '2') {
              return h('span', {
                style: {
                  color: 'red'
                }
              }, '表体')
            } else {
              return h('span', {}, '表体')
            }
          }
        }
      }, {
        width: 350,
        title: '预警信息',
        key: 'warningMsg',
        render: (h, params) => {
          if (params.row.warningMark === '2') {
            return h('span', {
              style: {
                color: 'red'
              }
            }, params.row.warningMsg)
          } else {
            return h('span', {}, params.row.warningMsg)
          }
        }
      }, {
        width: 120,
        key: 'emsNo',
        title: '备案号',
        render: (h, params) => {
          if (params.row.warningMark === '2') {
            return h('span', {
              style: {
                color: 'red'
              }
            }, params.row.emsNo)
          } else {
            return h('span', {}, params.row.emsNo)
          }
        }
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号',
        render: (h, params) => {
          if (params.row.warningMark === '2') {
            return h('span', {
              style: {
                color: 'red'
              }
            }, params.row.facGNo)
          } else {
            return h('span', {}, params.row.facGNo)
          }
        }
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型标识',
        render: (h, params) => {
          if (params.row.warningMark === '2') {
            return h('span', {
              style: {
                color: 'red'
              }
            }, getKeyValue(this.gmarkList, params.row.gmark))
          } else {
            return h('span', {}, getKeyValue(this.gmarkList, params.row.gmark))
          }
        }
      }]
    }
  }
}
