<template>
  <XdoModal width="1086" mask v-model="show" title="集装箱信息"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150" class="dc-form-1"
                 :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
    </DynamicForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { importExportManage } from '@/view/cs-common'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'containerSelectPop',
    mixins: [baseDetailConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      containerData: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        selectValues: '',
        selectFields: [],
        formName: 'frmData',
        cmbSource: {
          containerKeyLabels: {},
          containerType: importExportManage.containerType
        },
        buttons: [
          {...btnComm, label: '确认', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '取消', type: 'warning', command: 'cancel', click: this.handleClose}
        ]
      }
    },
    created: function () {
      let me = this,
        containerKeyLabels = {}
      me.cmbSource.containerType.forEach(ct => {
        containerKeyLabels[ct.value] = ct.value + ' ' + ct.label
      })
      me.$set(me.cmbSource, 'containerKeyLabels', containerKeyLabels)
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            this.loadValues()
          }
        }
      },
      'detailConfig.model': {
        deep: true,
        immediate: true,
        handler: function (model) {
          let me = this,
            fullValue = '',
            selKeys = (model.containerType || '').split(',')
          selKeys.forEach(key => {
            if (!isNullOrEmpty(key)) {
              if (model.hasOwnProperty(key)) {
                if (isNumber(model[key])) {
                  fullValue += me.cmbSource.containerKeyLabels[key] + ': ' + model[key] + '; '
                } else {
                  fullValue += me.cmbSource.containerKeyLabels[key] + ': ; '
                }
              }
            }
          })
          me.$set(me.detailConfig.model, 'containerFull', fullValue)
        }
      },
      'detailConfig.model.containerType': {
        immediate: true,
        handler: function (newValue) {
          let me = this,
            model = deepClone(me.detailConfig.model),
            selectFields = [],
            newValues = (newValue || '').split(',')
          newValues.forEach(ct => {
            if (!isNullOrEmpty(ct)) {
              selectFields.push({
                key: ct,
                props: {
                  intDigits: 5
                },
                required: true,
                type: 'xdoInput',
                title: me.cmbSource.containerKeyLabels[ct]
              })
            }
          })
          me.$set(me, 'selectFields', selectFields)
          me.$set(me, 'selectValues', newValue)
          me.$nextTick(() => {
            Object.keys(model).forEach(key => {
              if (!['sid', 'containerFull', 'containerType'].includes(key)) {
                if (me.detailConfig.model.hasOwnProperty(key)) {
                  me.$set(me.detailConfig.model, key, model[key])
                }
              }
            })
          })
        }
      }
    },
    computed: {
      /**
       * 完整字段信息
       * @returns {*[]}
       */
      fullFields() {
        return this.getFields()
      }
    },
    methods: {
      loadValues() {
        let me = this,
          selectValues = '',
          selectFields = []
        if (me.containerData.length > 0) {
          me.containerData.forEach(container => {
            let fieldLabel = me.cmbSource.containerKeyLabels[container.containerType],
              fieldValue = container.containerNum
            selectValues += container.containerType + ','
            selectFields.push({
              props: {
                intDigits: 5
              },
              required: true,
              type: 'xdoInput',
              title: fieldLabel,
              defaultValue: fieldValue,
              key: container.containerType
            })
          })
        }
        me.$set(me, 'selectValues', selectValues)
        me.$set(me, 'selectFields', selectFields)
      },
      getFields() {
        return [{
          props: {
            disabled: true
          },
          title: '集装箱信息',
          itemType: 'textarea',
          key: 'containerFull'
        }, {
          title: '集装箱信息',
          key: 'containerType',
          type: 'checkBoxGroup',
          defaultValue: this.selectValues,
          props: {
            options: this.dynamicSource.containerType.map(ct => {
              return {
                label: ct.value,
                title: ct.label
              }
            })
          }
        }, ...this.selectFields]
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleSave() {
        let me = this
        if (isNullOrEmpty(me.formName)) {
          console.error('未设置form的名称：this.formName')
          return
        }
        const data = deepClone(Object.assign({}, me.detailConfig.model))
        me.$refs[me.formName].resetFields()
        me.$set(me.detailConfig, 'model', data)
        me.$nextTick(() => {
          me.$set(me.detailConfig, 'model', data)
          me.$refs[me.formName].validate().then(isValid => {
            if (isValid) {
              let result = []
              Object.keys(data).forEach(key => {
                if (!['sid', 'containerFull', 'containerType'].includes(key)) {
                  result.push({
                    containerType: key,
                    containerNum: data[key]
                  })
                }
              })
              me.$emit('onContainerSave', result)
            }
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-1 {
    width: 100%;
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(1, 1fr);
  }

  .dc-form-1 > div {
    grid-column: 1;
  }
</style>
