<template>
  <srction>
    <XdoModal v-model="emsModalShow" width="500" title="请输入内部编号"
              :mask-closable="false" :closable="false" :footer-hide="true" mask>
      <a class="ivu-modal-close" @click="handleClose">
        <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
      </a>
      <XdoForm ref="formInline" class="dc-form" :model="frmData" label-position="right" :label-width="100" inline>
        <XdoFormItem prop="emsListNo" label="关联提单编号" class="dc-merge-1-4">
          <XdoIInput type="text" v-model="frmData.emsListNo" clearable></XdoIInput>
        </XdoFormItem>
      </XdoForm>
      <div class="xdo-enter-action" style="text-align: center; margin-top: 10px;">
        <Button type="primary" :loading="confirmLoading" @click="handleConfirm">提取</Button>
      </div>
    </XdoModal>
    <extractErr :errShow.sync="errModalShow" :gridData="gridData" :errData="errData"></extractErr>
  </srction>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import extractErr from './headBodyExtractErr'

  export default {
    props: {
      show: {
        type: Boolean,
        required: true
      },
      bondMark: {
        type: String,
        required: true
      },
      ieMark: {
        type: String,
        required: true
      }
    },
    components: {
      extractErr
    },
    data() {
      return {
        frmData: {
          emsListNo: ''
        },
        errData: {
          warningMsg: ''
        },
        gridData: [],
        errModalShow: false,
        confirmLoading: false
      }
    },
    methods: {
      handleConfirm() {
        let me = this
        if (!isNullOrEmpty(me.frmData.emsListNo)) {
          let url = ''
          if (me.ieMark === 'I') {
            url = csAPI.csImportExport.decErpIHeadN.getExtract
          } else {
            url = csAPI.csImportExport.decErpEHeadN.leadHeadList
          }
          me.$set(me, 'gridData', [])
          me.$set(me, 'confirmLoading', true)
          me.$http.post(`${url}/${me.frmData.emsListNo}/${me.bondMark}`, {}, {
            noIntercept: true
          }).then(res => {
            if (res.data.success) {
              me.$Message.success(res.data.message)
              me.handleClose()
            } else {
              if (res.data.code === 200) {
                me.errModalShow = true
                me.errData.warningMsg = res.data.message
                if (res.data.data) {
                  res.data.data.filter(item => {
                    if (item.tempFlag === 1) {
                      me.gridData.push({'tempRemark': item.tempRemark})
                    }
                  })
                }
              } else {
                me.$Message.error(res.data.message)
              }
            }
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'confirmLoading', false)
          })
        } else {
          me.$Message.warning('请输入内部编号')
        }
      },
      handleClose() {
        let me = this
        me.frmData.emsListNo = ''
        me.$emit('update:show', false)
      }
    },
    computed: {
      emsModalShow() {
        return this.show
      }
    }
  }
</script>
