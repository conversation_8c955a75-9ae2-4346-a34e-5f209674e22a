<template>
  <XdoCard :bordered="false">
    <DcAgGrid ref="table" :columns="gridConfig.columns" :data="gridConfig.data" :height="dynamicHeight"></DcAgGrid>
    <div ref="area_page" style="height: 26px; overflow: hidden;">
      <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
               :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
               @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      <span
          style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">汇总: {{ totalMessage }}</span>
    </div>
  </XdoCard>
</template>

<script>
import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { getColumnsByConfig } from '@/common'
import { columnsConfig, columns } from './billDetailBodyColumns'

export default {
  name: 'billDetailBodyList',
  mixins: [columns],
  props: {
    headId: {
      type: String,
      default: ''
    },
    iemark: {
      type: String,
      default: ''
    },
    dynamicHeight: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      gridConfig: {
        data: [],
        columns: []
      },
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1,
        sort: 'serialNo;asc' //'columnsKey;sortType,columnsKey;sortType'  // 设置默认排序   sortType : asc  || desc

      },
      totalMessage: '',
      ajaxUrl: {
        getBillTotal: '',
        selectAllPaged: ''
      },
      pageSizeOpts: [10, 20, 50, 100]
    }
  },
  created() {
    let me = this
    if (me.iemark === 'I') {
      me.$set(me.ajaxUrl, 'getBillTotal', csAPI.csImportExport.iBillList.getBillTotal)
      me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.iBillList.selectAllPaged)
    } else if (me.iemark === 'E') {
      me.$set(me.ajaxUrl, 'getBillTotal', csAPI.csImportExport.eBillList.getBillTotal)
      me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.eBillList.selectAllPaged)
    } else {
      console.info('请为清单表体查询界面设置进出口标记【ieMark】!')
    }
  },
  mounted: function() {
    let me = this
    me.gridConfig.columns = getColumnsByConfig(me.totalColumns, columnsConfig)
  },
  methods: {
    handleSearchSubmit() {
      let me = this
      me.pageParam.page = 1
      me.getList()
    },
    loadTotalMessage() {
      let me = this
      me.$http.post(me.ajaxUrl.getBillTotal, {
        headId: me.headId
      }).then(res => {
        me.$set(me, 'totalMessage', res.data.data)
      }).catch(() => {
        me.$set(me, 'totalMessage', '申报数量:0 申报总价:0 净重:0')
      })
    },
    getList() {
      let me = this
      me.$http.post(me.ajaxUrl.selectAllPaged, {
        headId: me.headId
      }, {
        params: me.pageParam
      }).then(res => {
        me.gridConfig.data = res.data.data
        me.pageParam.page = res.data.pageIndex
        me.pageParam.dataTotal = res.data.total
        me.loadTotalMessage()
      }).catch(() => {
        me.$set(me, 'totalMessage', '')
      })
    },
    pageChange(page) {
      let me = this
      me.pageParam.page = page
      me.getList()
    },
    pageSizeChange(pageSize) {
      let me = this
      me.pageParam.limit = pageSize
      if (me.pageParam.page === 1) {
        me.getList()
      }
    }
  },
  watch: {
    headId: {
      immediate: true,
      handler: function(headId) {
        let me = this
        if (isNullOrEmpty(headId)) {
          me.gridConfig.data = []
          me.pageParam.page = 1
          me.pageParam.dataTotal = 0
        } else {
          me.$nextTick(() => {
            me.handleSearchSubmit()
          })
        }
      }
    }
  }
}
</script>

<style scoped>
</style>
