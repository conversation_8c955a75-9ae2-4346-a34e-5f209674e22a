import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import  {renderGridTitleSort} from '@/common/gridTitleSort/renderGridTitleSort'
const commColumns = [
  'serialNo'
  , 'entryGNo'
  , 'gno'
  , 'copGNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'originCountry'
  , 'destinationCountry'
  , 'curr'
  , 'decPrice'
  , 'qty'
  , 'unit'
  , 'decTotal'
  // , 'qty1'
  , 'unit1'
  // , 'qty2'
  , 'unit2'
  , 'dutyMode'
  , 'exgVersion'
  , 'note'
  , 'note1'
  , 'note2'
  , 'note3'
]

const columnsConfig = [
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [columnRender],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          key: 'serialNo',
          title: '商品序号',
          headerComponentFramework: renderGridTitleSort(this)
        },
        {
          width: 120,
          key: 'entryGNo',
          title: '报关单商品序号',
          headerComponentFramework: renderGridTitleSort(this)
        },
        {
          key: 'gno',
          width: 120,
          title: '备案序号',
          headerComponentFramework: renderGridTitleSort(this)
        },
        {
          width: 120,
          key: 'copGNo',
          title: '商品料号'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 200,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 120,
          key: 'gmodel',
          tooltip: true,
          title: '规格型号'
        },
        {
          width: 120,
          title: '原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          title: '最终目的国',
          key: 'destinationCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          key: 'qty',
          width: 120,
          title: '申报数量'
        },
        {
          width: 120,
          key: 'unit',
          title: '申报计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 120,
          key: 'qty1',
          title: '法定数量'
        },
        {
          width: 120,
          key: 'unit1',
          title: '法定计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'qty2',
          title: '第二法定数量'
        },
        {
          width: 120,
          key: 'unit2',
          title: '法定第二计量单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'dutyMode',
          title: '征免方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 120,
          key: 'exgVersion',
          title: '单耗版本号'
        },
        {
          width: 120,
          key: 'note',
          title: '备注',
          tooltip: true
        },
        {
          width: 120,
          key: 'note1',
          tooltip: true,
          title: 'Remark1'
        },
        {
          width: 120,
          key: 'note2',
          tooltip: true,
          title: 'Remark2'
        },
        {
          width: 120,
          key: 'note3',
          tooltip: true,
          title: 'Remark3'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
