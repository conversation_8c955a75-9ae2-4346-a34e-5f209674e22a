<template>
  <section>
    <XdoCard v-if="showBillCard" :bordered="false" style="z-index: 1001;">
      <p slot="title">清单编号</p>
      <div style="padding: 2px; height: 28px;">
        <RadioGroup type="button" v-model="billId">
          <Radio :label="billId">{{billId}}</Radio>
        </RadioGroup>
      </div>
    </XdoCard>
    <BillDetailHead :edit-config="editConfig" :iemark="iemark" :bond-mark="bondMark" :aeo-show="aeoShow"
                    @onHeadShow="onHeadShow" @onBackToList="onBackToList"></BillDetailHead>
    <BillList :head-id="this.editConfig.editData.sid" :iemark="iemark" :dynamic-height="dynamicHeight"></BillList>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import BillList from './bill-detail-body-list'
  import BillDetailHead from './bill-detail-head'

  export default {
    name: 'billDetailSingle',
    components: {
      BillList,
      BillDetailHead
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {},
          billsCardHeight: 0,
          editStatus: editStatus.SHOW
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      },
      aeoShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        headHeight: 200
      }
    },
    computed: {
      billId() {
        return this.editConfig.editData.emsListNo
      },
      showBillCard() {
        return this.editConfig.billsCardHeight === 0
      },
      dynamicHeight() {
        let cardHeight = 45
        if (this.editConfig.billsCardHeight > 0) {
          cardHeight = this.editConfig.billsCardHeight
        }
        return window.innerHeight - this.headHeight - cardHeight - 121
      }
    },
    methods: {
      onHeadShow(headHeight) {
        let me = this
        me.$set(me, 'headHeight', headHeight)
      },
      /**
       * 返回列表界面
       */
      onBackToList() {
        let me = this
        me.$emit('onBackToList', {
          showList: true,
          editStatus: editStatus.SHOW,
          editData: me.editConfig.editData
        })
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }
</style>
