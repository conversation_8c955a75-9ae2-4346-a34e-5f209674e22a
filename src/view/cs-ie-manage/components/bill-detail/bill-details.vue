<template>
  <section>
    <XdoCard :bordered="false" style="z-index: 1001;">
      <p slot="title">清单编号</p>
      <div style="padding: 2px; overflow-y: auto; height: 52px;">
        <RadioGroup type="button" @on-change="bindHeaderData" v-model="billId">
          <Radio :label="item.sid" v-for="item in billList" :key="item.sid">{{item.emsListNo}}</Radio>
        </RadioGroup>
      </div>
    </XdoCard>
    <BillDetailSingle @onBackToList="onBackToList" :editConfig="configForHead"
                      :iemark="iemark" :bond-mark="bondMark" :aeo-show="aeoShow"></BillDetailSingle>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import BillDetailSingle from '@/view/cs-ie-manage/components/bill-detail/bill-detail-single'

  export default {
    name: 'billDetails',
    components: {
      BillDetailSingle
    },
    props: {
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      },
      aeoShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        billId: '',
        billList: [],
        headHeight: 200,
        ajaxUrl: {
          selectAllBillNo: ''
        }
      }
    },
    watch: {
      iemark: {
        immediate: true,
        handler: function (mark) {
          if (mark === 'I') {
            this.$set(this.ajaxUrl, 'selectAllBillNo', csAPI.csImportExport.iBill.selectAllBillNo)
          } else if (mark === 'E') {
            this.$set(this.ajaxUrl, 'selectAllBillNo', csAPI.csImportExport.eBill.selectAllBillNo)
          } else {
            console.info('进出口编辑设置错误!')
          }
        }
      },
      'parentConfig.editData.sid': {
        immediate: true,
        handler: function (headId) {
          this.loadBillList(headId)
        }
      }
    },
    computed: {
      configForHead() {
        return {
          editData: {
            sid: this.billId,
            headId: this.parentConfig.editData.sid,
            apprStatus: this.parentConfig.editData.apprStatus
          },
          billsCardHeight: 82 + 12,
          editStatus: this.parentConfig.editStatus
        }
      }
    },
    methods: {
      onHeadShow(headHeight) {
        let me = this
        me.$set(me, 'headHeight', headHeight)
      },
      /**
       * 返回列表界面
       */
      onBackToList() {
        let me = this
        me.$emit('onEditBack', {
          showList: true,
          editStatus: editStatus.SHOW,
          editData: me.parentConfig.editData
        })
      },
      /**
       * 加载清单编号
       */
      loadBillList(headId) {
        let me = this
        if (isNullOrEmpty(headId)) {
          me.billId = ''
          me.billList = []
        } else {
          me.$http.post(`${me.ajaxUrl.selectAllBillNo}/${headId}`).then(res => {
            if (Array.isArray(res.data.data) && res.data.data.length > 0) {
              me.billList = res.data.data
              me.billId = me.billList[0].sid
            } else {
              me.billId = ''
              me.billList = []
            }
          }).catch(() => {
            me.billId = ''
            me.billList = []
          })
        }
      },
      /**
       * 当前数据刷新
       */
      billReload() {
        let me = this
        me.loadBillList(me.parentConfig.editData.sid)
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }
</style>
