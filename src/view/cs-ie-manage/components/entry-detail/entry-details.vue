<template>
  <section>

  </section>
</template>

<script>
  export default {
    name: 'entryDetails',
    props: {
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      return {}
    },
    methods: {}
  }
</script>

<style scoped>
  /*/deep/ .ivu-card-head {*/
    /*padding: 7px 16px 5px 16px !important;*/
  /*}*/
</style>
