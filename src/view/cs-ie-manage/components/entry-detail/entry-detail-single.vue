<template>
  <section>
    <Card :bordered="false">
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <RadioGroup v-model="printType" slot="extra">
        <Radio label="excel"></Radio>
        <Radio label="pdf"></Radio>
      </RadioGroup>
    </Card>
    <EntryDetailHead :edit-config="editConfig" :iemark="iemark" :bond-mark="bondMark" @onBackToList="onBackToList"></EntryDetailHead>
    <EntryDetailBodyList ref="bodyList" :head-id="editConfig.editData.sid" :iemark="iemark" :dynamicHeight="dynamicHeight" :isShow.sync="isShow"
                         @sendBodyData="getBodyData" @sendAllBodyData="getAllBodyData"></EntryDetailBodyList>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import ImportPage from 'xdo-import'
  import { csAPI, excelExport } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import EntryDetailHead from './entry-detail-head'
  import EntryDetailBodyList from './entry-detail-body-list'
  import { columns, exportColumns } from './entryDetailBodyColumns'
  import { getHttpHeaderFileName, blobSaveFile } from '@/libs/util'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { billEntryPrintCheck } from '@/view/cs-ie-manage/js/bill-entry-print-check/billEntryPrintCheck'

  export default {
    name: 'entryDetailSingle',
    mixins: [columns, exportColumns, pms, billEntryPrintCheck, dynamicImport],
    components: {
      ImportPage,
      EntryDetailHead,
      EntryDetailBodyList
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {
            sid: ''
          }
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      let importKey = this.iemark === 'I' ? 'entryilist' : 'entryelist',
        taskCode = this.iemark === 'I' ? 'ENTRY-I-LIST' : 'ENTRY-E-LIST',
        commImportConfig = this.getCommImportConfig(taskCode, {
          iemark: this.iemark,
          headId: this.editConfig.editData.sid
        })
      return {
        headHeight: 380,
        actions: [],
        toolbarEventMap: {
          'edit': this.onEditEntry,
          'print': this.onPrintEntry,
          'import': this.onImportEntry,
          'export': this.onDownloadEntry
        },
        printType: 'pdf',       // 打印输出文件类型
        ajaxUrl: {
          printEntry: ''
        },
        isShow: false,
        bodyData: [],
        importKey: importKey,
        modelImportShow: false,
        importConfig: commImportConfig
      }
    },
    watch: {
      'editConfig.editData.sid': {
        immediate: true,
        handler: function (sid) {
          if (isNullOrEmpty(sid)) {
            // this.actions[0].disabled = true
            this.setToolbarProperty('print', 'disabled', true)
          } else {
            // this.actions[0].disabled = false
            this.setToolbarProperty('print', 'disabled', false)
          }
        }
      },
      isShow(val) {
        if (!val) {
          this.bodyData = []
        }
      }
    },
    created() {
      let me = this
      if (me.iemark === 'I') {
        me.$set(me.ajaxUrl, 'printEntry', csAPI.csImportExport.iBill.printEntry)
      } else if (me.iemark === 'E') {
        me.$set(me.ajaxUrl, 'printEntry', csAPI.csImportExport.eBill.printEntry)
      } else {
        console.info('请为清单表体查询界面设置进出口标记【ieMark】!')
      }
    },
    mounted() {
      let me = this
      me.loadFunctions('body').then()
    },
    computed: {
      dynamicHeight() {
        return window.innerHeight - this.headHeight - 40
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      onBackToList() {
        let me = this
        me.$emit('onBackToList', {
          showList: true,
          editData: me.editConfig.editData
        })
      },
      downloadStreamFile(stream, headers) {
        let me = this
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: `application/${me.printType === 'excel' ? 'vnd.ms-excel' : 'pdf'}`})
        blobSaveFile(blob, filename)
      },
      /**
       * 打印报关单草单
       */
      onPrintEntry() {
        let me = this
        me.printCheck('2', () => {
          me.actions[0].loading = true
          const param = new FormData()
          param.append('type', me.printType)
          param.append('emsListNo', me.editConfig.editData.emsListNo)
          me.$http.post(`${me.ajaxUrl.printEntry}`, param, {
            responseType: 'blob'
          }).then(res => {
            me.downloadStreamFile(res.data, res.headers)
          }).catch(() => {
          }).finally(() => {
            me.actions[0].loading = false
          })
        })
      },
      getBodyData(val) {
        let me = this
        me.bodyData = val
      },
      getAllBodyData(val) {
        let me = this
        me.$emit('sendBodyData', val)
      },
      /**
       * 点击弹出编辑检验检疫编码界面
       */
      onEditEntry() {
        let me = this
        if (me.bodyData.length > 0) {
          if (me.bodyData.length > 1) {
            me.$Message.warning('请选择一条要编辑得数据')
          } else {
            me.isShow = true
          }
        } else {
          me.$Message.warning('请选择要编辑得数据')
        }
      },
      onDownloadEntry() {
        let me = this
        me.setToolbarLoading('export', true)
        let url = ''
        if (me.iemark === 'I') {
          url = csAPI.csImportExport.iEntry.exportEntry
        } else {
          url = csAPI.csImportExport.eEntry.exportEntry
        }
        excelExport(url, {
          name: '检验信息',
          header: me.totalExcelColumns,
          exportColumns: {
            headId: me.editConfig.editData.sid
          }
        }).finally(() => {
          me.setToolbarLoading('export', false)
        })
      },
      onImportEntry() {
        let me = this
        me.modelImportShow = true
      },
      onAfterImport() {
        let me = this
        me.modelImportShow = false
        me.$refs.bodyList.getList()
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-card-extra {
    top: 7px !important;
  }
</style>
