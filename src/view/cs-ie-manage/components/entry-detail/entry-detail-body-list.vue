<template>
  <section>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="gridConfig.columns" :data="gridConfig.data" :height="dynamicHeight"
                @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
    <entryDetailBodyEdit :show.sync="isShow" :iemark="iemark" :bodyData="this.gridConfig.selectRows" :headId="headId"></entryDetailBodyEdit>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import entryDetailBodyEdit from './entry-detail-body-edit'
  import { columnsConfig, columns } from './entryDetailBodyColumns'

  export default {
    name: 'entryDetailBodyList',
    mixins: [columns],
    components: {
      entryDetailBodyEdit
    },
    props: {
      headId: {
        type: String,
        default: ''
      },
      iemark: {
        type: String,
        default: ''
      },
      dynamicHeight: {
        type: Number,
        default: 100
      },
      isShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        gridConfig: {
          data: [],
          columns: [],
          selectRows: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        ajaxUrl: {
          selectAllPaged: ''
        },
        pageSizeOpts: [10, 20, 50, 100]
      }
    },
    created() {
      let me = this
      if (me.iemark === 'I') {
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.iEntry.selectAllListPaged)
      } else if (me.iemark === 'E') {
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.eEntry.selectAllListPaged)
      } else {
        console.info('请为报关单表体查询界面设置进出口标记【ieMark】!')
      }
    },
    mounted: function () {
      let me = this,
        theColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      for (let col of theColumns) {
        me.reSetColumnTitle(col, 'districtCode', '境内目的地(国内地区)', '境内货源地(国内地区)')
        me.reSetColumnTitle(col, 'districtPostCode', '境内目的地(行政区划)', '境内货源地(行政区划)')
      }
      me.gridConfig.columns = theColumns
      me.handleSearchSubmit()
    },
    methods: {
      /**
       * 重新设置列表题
       * @param column
       * @param colKey
       * @param iTitle
       * @param eTitle
       */
      reSetColumnTitle(column, colKey, iTitle, eTitle) {
        let me = this
        if (column.key === colKey) {
          if (me.iemark === 'I') {
            column.title = iTitle
          } else if (me.iemark === 'E') {
            column.title = eTitle
          }
        }
      },
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      getList() {
        let me = this
        me.$http.post(me.ajaxUrl.selectAllPaged, {
          headId: me.headId
        }, {
          params: me.pageParam
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.$emit('sendAllBodyData', res.data.data)
        })
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        if (me.pageParam.page === 1) {
          me.getList()
        }
      },
      handleSelectionChange(selectRows) {
        let me = this
        me.gridConfig.selectRows = selectRows
        me.$emit('sendBodyData', me.gridConfig.selectRows)
      }
    },
    watch: {
      headId: {
        handler: function () {
          this.handleSearchSubmit()
        }
      },
      isShow: {
        handler: function (val) {
          if (!val) {
            this.gridConfig.selectRows = []
            this.$emit('update:isShow', false)
            this.getList()
          }
        }
      }
    }
  }
</script>

<style scoped>
</style>
