<template>
  <XdoModal v-model="showModal" width="500" title="编辑检验检疫货物规格"
            :mask-closable="false" :closable="false" :footer-hide="true" mask>
    <a class="ivu-modal-close" @click="handleCloseModal">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm ref="formInline" class="dc-form-1" :model="modelData" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="stuff" label="成分/原料/组分">
        <XdoIInput type="text" v-model="modelData.stuff"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="产品有效期">
        <XdoDatePicker type="date" placeholder="请选择开始时间" @on-change="modelData.prodValiddt=$event" :value="modelData.prodValiddt" transfer></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem label="产品保质期(天)">
        <DcNumberInput v-model="modelData.prodQgp"></DcNumberInput>
      </XdoFormItem>
      <XdoFormItem label="境外生产企业">
        <XdoIInput type="text" v-model="modelData.engmanentCnm"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem  label="货物规格">
        <XdoIInput type="text" v-model="modelData.goodsSpec"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="货物型号">
        <XdoIInput type="text" v-model="modelData.goodsModel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="货物品牌">
        <XdoIInput type="text" v-model="modelData.goodsBrand"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="生产日期">
        <XdoDatePicker type="date" placeholder="请选择开始时间" @on-change="modelData.produceDate=$event" :value="modelData.produceDate" multiple></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem label="生产批次号">
        <XdoIInput type="text" v-model="modelData.prodbatchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
    <div class="dc-container-center">
      <XdoButton type="primary" @click="btnSave">保存</XdoButton>&nbsp;
    </div>
  </XdoModal>
</template>

<script>
  import DcNumberInput from '@/components/dc-number-input/dc-number-input'

  export default {
    name: 'showModelDetail',
    props: {
      showModal: {
        type: Boolean
      },
      bodyData: {
        type: Object
      },
      iemark: {
        type: String,
        default: ''
      }
    },
    components: {
      DcNumberInput
    },
    data() {
      return {
        modelData: {
          stuff: '',
          prodValiddt: '',
          prodQgp: '',
          engmanentCnm: '',
          goodsSpec: '',
          goodsModel: '',
          goodsBrand: '',
          produceDate: '',
          prodbatchNo: ''
        },
      }
    },
    methods: {
      // 如果月、日不满10位,十位添加0
      // p(s) {
      //   return s < 10 ? '0' + s : s
      // },
      btnSave() {
        let me = this,
          arr = []
        me.modelData.produceDate = me.modelData.produceDate ? me.modelData.produceDate.split(',').join(';') : ''
        for (let key in me.modelData) {
          if (me.modelData.hasOwnProperty(key) && me.modelData[key]) {
            arr.push(me.modelData[key])
          }
        }
        let list = arr.join(';')
        let modelChangeData = Object.assign({}, me.bodyData, me.modelData, {goodsAttrAll: list})
        me.$emit('modelChange', modelChangeData)
        me.$emit('update:showModal', false)
      },
      handleCloseModal() {
        let me = this
        me.$emit('update:showModal', false)
      }
    },
    watch: {
      showModal(val) {
        if (val) {
          if (this.bodyData.goodsAttrAll) {
            this.modelData.stuff = this.bodyData.stuff
            this.modelData.prodValiddt = this.bodyData.prodValiddt
            this.modelData.prodQgp = this.bodyData.prodQgp
            this.modelData.engmanentCnm = this.bodyData.engmanentCnm
            this.modelData.goodsSpec = this.bodyData.goodsSpec
            this.modelData.goodsModel = this.bodyData.goodsModel
            this.modelData.goodsBrand = this.bodyData.goodsBrand
            this.modelData.produceDate = this.bodyData.produceDate.split(';').join(',')
            this.modelData.prodbatchNo = this.bodyData.prodbatchNo
          } else {
            this.modelData = {}
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-1 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(1, 1fr);
  }

  .dc-form-1 > div {
    grid-column: 1/2;
  }
</style>
