<template>
  <XdoModal v-model="showType" mask width="500" title="货物属性"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleCloseModal">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <CheckboxGroup v-model="typeList" class="dc-form dc-form-3">
      <Checkbox v-for="item in typeDataList" :key="item.NAME" :label="item.CODE">{{item.CODE}}-{{item.NAME}}</Checkbox>
    </CheckboxGroup>
    <div class="dc-container-center">
      <XdoButton type="primary" @click="btnSaveType">保存</XdoButton>&nbsp;
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'showTypeDetail',
    props: {
      showType: {
        type: Boolean
      },
      modelString: {
        type: String
      }
    },
    data() {
      return {
        typeList: [],
        typeDataList: []
      }
    },
    methods: {
      btnSaveType() {
        let me = this
        if (me.typeList.length > 0) {
          if (me.typeList.length < 8) {
            let type = me.typeList.join(',')
            me.$emit('onChange', type)
            me.typeList = []
            me.$emit('update:showType', false)
          } else {
            me.$Message.warning('货物属性选项过多，只能选择7项!')
          }
        } else {
          let type = ''
          me.$emit('onChange', type)
          me.typeList = []
          me.$emit('update:showType', false)
        }
      },
      handleCloseModal() {
        let me = this
        me.typeList = []
        me.$emit('update:showType', false)
      },
      getTypeList() {
        let me = this
        me.$http.get('/api/pcode?type=GOODS_ATTR').then(res => {
          me.typeDataList = res.data.data['GOODS_ATTR']
        })
      }
    },
    watch: {
      showType(val) {
        if (val) {
          let me = this
          me.getTypeList()
          if (me.modelString !== null && me.modelString.length > 1) {
            me.typeList = me.modelString.replace(/[\uff0c]/g, ",").split(',')
          }
        }
      }
    }
  }
</script>
