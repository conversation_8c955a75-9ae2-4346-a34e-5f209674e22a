import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const commColumns = [
  'selection'
  , 'sid'
  , 'serialNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'qty'
  , 'unit'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'qty1'
  , 'unit1'
  , 'qty2'
  , 'unit2'
  , 'originCountry'
  , 'destinationCountry'
  , 'districtCode'
  , 'districtPostCode'
  , 'dutyMode'
]

const columnsConfig = [
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [ columnRender ],
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          width: 80,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          key: 'serialNo',
          title: '商品序号'
        },
        {
          title: '商品编码',
          key: 'codeTS',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'gname',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'gmodel',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报数量',
          key: 'qty',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报单位',
          key: 'unit',
          width: 60,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '申报单价',
          key: 'decPrice',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '申报总价',
          key: 'decTotal',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '法一数量',
          key: 'qty1',
          width: 80,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法一单位',
          key: 'unit1',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '法二数量',
          key: 'qty2',
          width: 70,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '法二单位',
          key: 'unit2',
          width: 90,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '原产国(地区)',
          key: 'originCountry',
          width: 100,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '最终目的国(地区)',
          key: 'destinationCountry',
          width: 110,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        },
        {
          title: '境内(货源/目的)地(国内地区)',
          minWidth: 160,
          align: 'center',
          key: 'districtCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.districtCode)
          }
        },
        {
          title: '境内(货源/目的)地(行政区划)',
          minWidth: 160,
          align: 'center',
          key: 'districtPostCode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.districtPostCode)
          }
        },
        {
          title: '征免方式',
          minWidth: 120,
          align: 'center',
          key: 'dutyMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        }
      ]
    }
  }
}

const exportColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          value: '商品序号',
          key: 'serialNo',
        },
        {
          value: '商品编码',
          key: 'codeTS',
        },
        {
          value: '商品名称',
          key: 'gname',
        },
        {
          value: '原产国',
          key: 'originCountry',
        },
        {
          value: '成分/原料/组份',
          key: 'stuff',
        },
        {
          value: '产品有效期',
          key: 'prodValiddt',
        },
        {
          value: '产品保质期（天）',
          key: 'prodQgp',
        },
        {
          value: '境外生产企业',
          key: 'engmanentCnm',
        },
        {
          value: '货物规格',
          key: 'goodsSpec',
        },
        {
          value: '货物型号',
          key: 'goodsModel',
        },
        {
          value: '货物品牌',
          key: 'goodsBrand',
        },
        {
          value: '生产日期',
          key: 'produceDate',
        },
        {
          value: '生产批次号',
          key: 'prodbatchNo',
        },
        {
          key: 'goodsAttr',
          value: '货物属性代码'
        },
        {
          value: '用途',
          key: 'purPose'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  exportColumns,
  columns
}
