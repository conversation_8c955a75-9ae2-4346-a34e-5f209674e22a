<template>
  <XdoModal width="1024" mask v-model="show" title="选择模板"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <TemplateSelectionSearch ref="headSearch" :iemark="iemark" :bond-mark="bondMark"></TemplateSelectionSearch>
        </div>
      </div>
    </XdoCard>
    <XdoCard :bordered="false">
      <div class="action" ref="area_actions">
        <template v-for="item in actions">
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                  @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</Button>&nbsp;
        </template>
      </div>
    </XdoCard>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="510"
                @on-selection-change="handleSelectionChange" @onCellDoubleClicked="onRowDblClick"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { showColumns, columns } from './templateSelectionColumns'
  import TemplateSelectionSearch from './template-selection-search'
  import { commList } from '@/view/cs-interim-verification/comm/commList'

  export default {
    components: {
      TemplateSelectionSearch
    },
    mixins: [commList, columns],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      iemark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1', ''].includes(value)
        }
      }
    },
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        showSearch: true,
        ajaxUrl: {
          deleteUrl: csAPI.csImportExport.template.delete,
          selectAllPaged: csAPI.csImportExport.template.selectAllPaged
        },
        actions: [{
          ...btnComm, label: '确认', icon: 'ios-checkmark', key: 'xdo-btn-confirm', click: this.handleConfirm
        }, {
          ...btnComm, label: '删除', key: 'xdo-btn-delete', icon: 'ios-trash-outline', click: this.handleDelete
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (val) {
          if (val) {
            let me = this
            me.$nextTick(() => {
              me.handleSearchSubmit()
            })
          }
        }
      }
    },
    created() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, showColumns)
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.deleteUrl, 0)
      },
      handleConfirm() {
        let me = this
        const selectRows = me.gridConfig.selectRows
        if (selectRows.length <= 0) {
          me.$Message.warning('请选要使用的模板')
        } else if (selectRows.length > 1) {
          me.$Message.warning('一次只能选择一个模版')
        } else {
          me.$emit('selectTemplate:success', selectRows[0])
          me.handleClose()
        }
      },
      onRowDblClick(selRow) {
        let me = this
        me.$emit('selectTemplate:success', selRow)
        me.handleClose()
      },
      /**
       * 保存模板
       * @param entity
       * @param templateName
       * @param callback
       */
      saveTemplate(entity, templateName, callback) {
        let me = this
        if (entity.hasOwnProperty('tempName')) {
          entity.tempName = templateName
        } else {
          me.$set(entity, 'tempName', templateName)
        }
        !entity.hasOwnProperty('iemark') && me.$set(entity, 'iemark', me.iemark)
        me.$http.post(csAPI.csImportExport.template.insert, entity).then(res => {
          callback(res.data)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
