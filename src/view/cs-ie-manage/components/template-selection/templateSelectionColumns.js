import { baseColumnsMultiSelection, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const showColumns = [
  ...baseColumnsMultiSelection
  , 'tempName'
  , 'userName'
  , 'insertTime'
]

const columns = {
  mixins: [baseColumns],
  data() {
    let me = this
    let baseFields = me.getDefaultColumns()
    return {
      totalColumns: [...baseFields, {
        width: 663,
        key: 'tempName',
        title: '模板名称'
      }, {
        width: 205,
        title: '制单员',
        key: 'userName'
      }, {
        width: 88,
        title: '制单日期',
        key: 'insertTime',
        render: (h, params) => {
          return me.dateTimeShowRender(h, params)
        }
      }]
    }
  }
}

export {
  columns,
  showColumns
}
