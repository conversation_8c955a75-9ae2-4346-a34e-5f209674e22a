<template>
  <XdoModal width="500" mask v-model="show" title="选择包装种类"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm class="dc-form" label-position="right" :label-width="60">
        <XdoFormItem prop="wrapTypeView" label="选中项" class="dc-merge-1-4">
          <XdoIInput type="text" v-model="wrapTypeView" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="myWrapType" label="待选项" class="dc-merge-1-4">
          <CheckboxGroup v-model="wrapTypeArr" @on-change="wrapTypeChange" style="white-space: pre-wrap;">
            <template v-for="item in wrapTypes">
              <XdoCheckbox :label="item.value" :key="item.value">
                <span>{{item.label}}</span>
              </XdoCheckbox>
            </template>
          </CheckboxGroup>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="margin-top: 2px; text-align: right;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'wrapTypeSelectPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      wrapType: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {
          myWrapType: ''
        },
        wrapTypes: [],
        wrapTypeArr: [],
        buttons: [
          {...btnComm, label: '关闭', icon: 'dc-btn-cancel', click: this.handleClose},
          {...btnComm, label: '确定', icon: 'dc-btn-save', click: this.handleConfirm}
        ]
      }
    },
    watch: {
      wrapType: {
        immediate: true,
        handler: function (wrapType) {
          let me = this
          me.$set(me.frmData, 'myWrapType', wrapType || '')
          me.$set(me, 'wrapTypeArr', (wrapType || '').split(','))
        }
      }
    },
    computed: {
      wrapTypeView() {
        let result = []
        if (!isNullOrEmpty(this.frmData.myWrapType)) {
          let wrapTypes = this.frmData.myWrapType.split(',')
          if (Array.isArray(wrapTypes) && wrapTypes.length > 0) {
            wrapTypes.forEach(wrap => {
              if (!isNullOrEmpty(wrap)) {
                result.push(wrap + ' ' + this.pcodeGet('WRAP', wrap))
              }
            })
          }
        }
        return result.toString()
      }
    },
    created: function () {
      let me = this
      me.pcodeList(me.pcode.wrap).then(res => {
        me.$set(me, 'wrapTypes', res)
      })
    },
    methods: {
      wrapTypeChange(checked) {
        let me = this
        // 获取原始项
        let originalVals = (me.frmData.myWrapType || '').split(',')
        let result = []
        originalVals.forEach(item => {
          if (!isNullOrEmpty(item)) {
            result.push(item.trim())
          }
        })
        // 移除删除项
        result.forEach(item => {
          if (!checked.includes(item)) {
            result.splice(result.indexOf(item), 1)
          }
        })
        // 添加新增项
        checked.map(item => {
          if (!isNullOrEmpty(item) && !result.includes(item.trim())) {
            result.push(item.trim())
          }
        })
        me.$set(me.frmData, 'myWrapType', result.toString())
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$emit('doWrapTypeFill', me.frmData.myWrapType)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-modal-close {
    top: 1px;
    right: 1px;
  }
</style>
