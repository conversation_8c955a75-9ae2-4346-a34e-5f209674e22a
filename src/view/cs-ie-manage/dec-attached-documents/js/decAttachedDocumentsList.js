import { editStatus } from '@/view/cs-common'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import AttachedDocumentsEdit from '../dec-attached-documents-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const decAttachedDocumentsList = {
  name: 'decAttachedDocumentsList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    AttachedDocumentsEdit
  },
  props: {
    /**
     * 传入的编辑信息
     */
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      // 分页相关
      pageParam: {
        limit: 1000
      },
      pmsLevel: 'sider',
      importShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'delete': this.handleDelete
      }
    }
  },
  computed: {
    extendParams() {
      return {
        headId: (this.parentConfig.editData.sid || '')
      }
    },
    editRealShow() {
      if (this.aeoShow === true) {
        return false
      }
      return this.editShow
    }
  },
  methods: {
    actionLoaded() {
      let me = this,
        btnDisabled = me.parentConfig.editStatus === editStatus.SHOW
      me.$nextTick(() => {
        me.actions.forEach(action => {
          action.disabled = btnDisabled
        })
      })
    },
    getParams() {
      return []
    },
    getFields() {
      let me = this,
        docuWidth = 0,
        certWidth = 0,
        grdCols = []
      if (me.aeoShow) {
        docuWidth = 170
        certWidth = 199
      } else {
        grdCols.push({
          type: 'selection'
        })
        docuWidth = 150
        certWidth = 178
      }
      grdCols.push({
        width: docuWidth,
        tooltip: true,
        key: 'docuCode',
        title: '单证代码',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.docuCode)
        }
      }, {
        width: certWidth,
        tooltip: true,
        key: 'certCode',
        title: '单证编号'
      })
      return grdCols
    },
    afterSearchSuccess() {
      let me = this
      me.$set(me, 'editShow', false)
      me.$set(me.editConfig, 'editData', {})
      me.$set(me.listConfig, 'selectRows', [])
      me.$set(me.editConfig, 'editStatus', editStatus.SHOW)
      me.$nextTick(() => {
        me.$set(me, 'editShow', true)
      })
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      let me = this
      me.$set(me, 'editShow', false)
      me.$set(me.editConfig, 'editData', {})
      me.$set(me.listConfig, 'selectRows', [])
      me.$set(me.editConfig, 'editStatus', editStatus.ADD)
      me.$nextTick(() => {
        me.$set(me, 'editShow', true)
      })
    },
    /**
     * 行选中或取消选中
     * @param selectRows
     */
    handleSelectionChange(selectRows) {
      let me = this
      if (me.aeoShow) {
        return
      }
      me.listConfig.selectRows = selectRows
      me.$set(me, 'editShow', false)
      if (Array.isArray(selectRows) && selectRows.length > 0) {
        if (me.parentConfig.editStatus === editStatus.SHOW) {
          me.handleViewByRow(selectRows[0])
        } else {
          me.handleEditByRow(selectRows[0])
        }
      } else {
        me.handleViewByRow({})
      }
      me.$nextTick(() => {
        me.$set(me, 'editShow', true)
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    }
  }
}
