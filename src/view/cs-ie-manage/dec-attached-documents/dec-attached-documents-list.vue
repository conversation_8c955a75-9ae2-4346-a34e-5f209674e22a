<template>
  <section>
    <div v-if="!aeoShow" class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" height="380"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
    </XdoCard>
    <AttachedDocumentsEdit v-if="editRealShow" :edit-config="editConfig" :parent-config="parentConfig" :ie-mark="ieMark"
                           @onEditBack="editBack"></AttachedDocumentsEdit>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { importExportManage } from '@/view/cs-common'
  import { decAttachedDocumentsList } from './js/decAttachedDocumentsList'

  export default {
    name: 'decAttachedDocumentsList',
    mixins: [decAttachedDocumentsList],
    props: {
      aeoShow: {
        type: Boolean,
        default: () => false
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        editShow: true,
        cmbSource: {
          docuCode: []
        },
        ajaxUrl: {
          deleteUrl: '',
          selectAllPaged: ''
        },
        listConfig: {
          exportTitle: '随附单证'
        }
      }
    },
    created: function () {
      let me = this
      if (me.ieMark === 'I') {
        me.$set(me.cmbSource, 'docuCode', importExportManage.LICENSE_DOC)
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.csImportExport.decAttachedDocuments.imports.delete)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.decAttachedDocuments.imports.selectAllPaged)
      } else {
        me.$set(me.ajaxUrl, 'deleteUrl', csAPI.csImportExport.decAttachedDocuments.exports.delete)
        me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.csImportExport.decAttachedDocuments.exports.selectAllPaged)
        me.$set(me.cmbSource, 'docuCode', importExportManage.LICENSE_DOC.filter(x => !['自定义1', '自定义2', '自定义3', '自定义4'].includes(x.value)))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
