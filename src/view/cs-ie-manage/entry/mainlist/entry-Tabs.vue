<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头和表体">
        <EntryDetailSingle v-if="tabs.headTab" :editConfig="editConfig" :iemark="iemark" :bond-mark="bondMark"
                           @sendBodyData="getBodyData"></EntryDetailSingle>
      </TabPane>
      <TabPane name="containerTab" label="集装箱信息">
        <container v-if="tabs.containerTab" :editConfig="editConfig" :iemark="iemark" :bodyData="bodyData"></container>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { editStatus } from '@/view/cs-common'
  import container from '../components/entry-ie-container'
  import EntryDetailSingle from '@/view/cs-ie-manage/components/entry-detail/entry-detail-single'

  export default {
    name: 'entryTabs',
    components: {
      container,
      EntryDetailSingle
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {
            sid: ''
          }
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark: {
        type: String,
        require: true,
        validate: function (value) {
          return ['0', '1'].includes(value)
        }
      }
    },
    data() {
      return {
        bodyData: [],
        tabName: 'headTab',
        tabs: {
          headTab: true,
          containerTab: false
        }
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      },
      getBodyData(val) {
        let me = this
        me.bodyData = val
      }
    }
  }
</script>
