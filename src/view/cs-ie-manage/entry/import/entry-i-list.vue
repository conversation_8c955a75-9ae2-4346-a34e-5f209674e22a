<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <EntryIESearch ref="headSearch" :iemark="iemark" :bond-mark="bondMark" :supplierList="this.cmbSource.overseasShipperList"></EntryIESearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:draft-print>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>打印草单<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="draftPrint('pdf')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  合并打印(PDF)
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="draftPrint('zip')">
                    <XdoIcon type="ios-print-outline" size="22" class="xdo-icon"/>  压缩打印(ZIP)
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <entryTabs v-if="!showList" @onEditBack="backToList" :iemark="iemark" :bond-mark="bondMark" :editConfig="editConfig"></entryTabs>
    <EntryErrMsg :show.sync="errMsgInfo.modelErrMsgShow"
                 :headId="errMsgInfo.sid" :taskId="errMsgInfo.sendApiTaskId" :url="errMsgInfo.loadUrl"
                 @errMessage:hide="hideErrMsg"></EntryErrMsg>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="filterColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { entryIEList } from '@/view/cs-ie-manage/js/entry/entryIEList'

  export default {
    name: 'entryIList',
    mixins: [entryIEList],
    mounted() {
      let me = this
      me.$refs.headSearch.searchParam.entryNo=me.$route.params.entryNo
    },
    watch:{
      '$route' (to, from) {
        let me=this
        if(from.path==='/cs/planImport/batch'){
          me.$refs.headSearch.searchParam.entryNo=me.$route.params.entryNo
          this.getList()
        }
      }
    },
    data() {
      return {
        iemark: 'I',
        tableId: '',
        bondMark: '1',
        columnsarr: [],
        tableShow: true,
        columnsConfig: [],
        alltotalColumns: [],
        gridConfig: {
          exportTitle: '进口报关单'
        },
        showtableColumnSetup: false,
        ajaxUrl: {
          exportUrl: csAPI.csImportExport.iEntry.export,
          entry: csAPI.csImportExport.customsDeclaration.entry,
          selectAllPaged: csAPI.csImportExport.iEntry.selectAllPaged,
          sendLjqApi: csAPI.csImportExport.customsDeclaration.sendLjqApis,
          recallSending: csAPI.csImportExport.iEntry.recallSending,
          printEntryList: csAPI.csImportExport.customsDeclaration.printIEntryList,
          sendComplete:csAPI.csImportExport.customsDeclaration.sendComplete
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
