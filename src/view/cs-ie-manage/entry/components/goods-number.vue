<template>
  <section>
    <XdoModal v-model="tyShow" width="600" title="编辑商品项号关系"
              :mask-closable="false" :closable="false" :footer-hide="true">
      <a class="ivu-modal-close" @click="handleClose">
        <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
      </a>
      <XdoTable ref="tables" :columns="columns" :data="meData" border class="dc-table"
                @on-selection-change="handleSelectionChange"></XdoTable>
      <div class="dc-action dc-container-center">
        <XdoButton class="dc-margin-right" type="primary" @click="handleSave">确定</XdoButton>&nbsp;
      </div>
    </XdoModal>
  </section>
</template>

<script>
  export default {
    name: 'goodsNumber',
    props: {
      tyShow: {
        type: Boolean,
        require: true
      },
      bodyData: {
        type: Array
      },
      selectData: {
        type: String
      }
    },
    data() {
      return {
        columns: [{
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        }, {
          title: '序号',
          align: 'center',
          key: 'serialNo'
        }, {
          key: 'codeTS',
          align: 'center',
          title: '商品编号'
        }, {
          key: 'gname',
          align: 'center',
          title: '商品名称'
        }],
        meData: [],
        selectRows: [],
        serialNolist: []
      }
    },
    methods: {
      handleSelectionChange(rows) {
        let me = this
        me.selectRows = rows
      },
      getList() {
        let me = this
        if (me.bodyData.length > 0) {
          me.meData = me.bodyData
        }
      },
      handleSave() {
        let me = this
        if (me.selectRows.length > 0) {
          me.serialNolist = me.selectRows.map(item => item.serialNo).join(',')
          me.selectRows = []
          me.$emit('sendSerialNoList', me.serialNolist)
        } else {
          me.$Message.warning('请选择需要保存的数据')
        }
      },
      handleClose() {
        let me = this
        me.selectRows = []
        me.$emit('update:tyShow', false)
      }
    },
    watch: {
      tyShow(val) {
        if (val) {
          let me = this
          me.meData = []
          me.$nextTick(() => {
            me.getList()
          })
        }
      }
    }
  }
</script>
