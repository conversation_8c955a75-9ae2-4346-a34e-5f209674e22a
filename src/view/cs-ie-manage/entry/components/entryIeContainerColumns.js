import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  ...baseColumns
  , 'apprStatusName'
  , 'emsListNo'
  , 'center'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [{
        align: 'center',
        title: '集装箱号',
        key: 'apprStatusName'
      }, {
        tooltip: true,
        ellipsis: true,
        align: 'center',
        key: 'emsListNo',
        title: '集装箱规格'
      }, {
        align: 'center',
        title: '拼箱标识',
        key: 'insertTime'
      }]
    }
  },
  /**
   * 方法
   */
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}

export {
  columns,
  commColumns
}
