<template>
  <section>
    <XdoCard :bordered="false">
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
    </XdoCard>
    <XdoTable class="dc-table" ref="table" stripe border
              :columns="gridConfig.gridColumns" :height="dynamicHeight" :data="gridConfig.data"
              @on-selection-change="handleSelectionChange"></XdoTable>
    <XdoCard :bordered="false" class="xdo-enter-root" v-focus>
      <XdoForm ref="frmDataFrom" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" label-position="right" :label-width="120">
        <XdoFormItem prop="containerMd" label="集装箱号">
          <XdoIInput type="text" v-model="frmData.containerMd"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="containerModel" label="集装箱规格">
          <xdo-select v-model="frmData.containerModel" :options="containerModelList" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop=" containerWt" label="自重(KG)">
          <xdo-input v-model="frmData.containerWt" decimal int-length="9" precision="4"></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="containerLcl" label="拼箱标识">
          <xdo-select v-model="frmData.containerLcl" :options="containerLclList" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="containerGNo" label="商品项号关系">
          <XdoIInput type="text" v-model="frmData.containerGNo" style="width: 80%;" disabled></XdoIInput>
          <XdoButton @click="handleAddType">...</XdoButton>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <goodsNumber :tyShow.sync="tyShow" :bodyData="bodyData" :selectData="frmData.containerGNo" @sendSerialNoList="getSerialNoList"></goodsNumber>
    <ImportPage :importKey="importKey" :importShow.sync="modelImportShow" :importConfig="importConfig" @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import ImportPage from 'xdo-import'
  import goodsNumber from './goods-number'
  import { getKeyValue } from '@/libs/util'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

  export default {
    name: 'container',
    mixins: [commList, pms, dynamicImport],
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {
            sid: ''
          }
        })
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bodyData: {type: Array}
    },
    components: {
      ImportPage,
      goodsNumber
    },
    data() {
      let commImportConfig = this.getCommImportConfig('CONTAINER-I-E', {
        I_E_MARK: this.iemark,
        HEAD_ID: this.editConfig.editData.sid
      })
      return {
        gridConfig: {
          gridColumns: [{
            width: 60,
            align: 'center',
            key: 'selection',
            type: 'selection'
          }, {
            align: 'center',
            title: '集装箱号',
            key: 'containerMd'
          }, {
            align: 'center',
            title: '集装箱规格',
            key: 'containerModel',
            render: (h, params) => {
              return h('span', getKeyValue(this.containerModelList, params.row.containerModel))
            }
          }, {
            align: 'center',
            title: '拼箱标识',
            key: 'containerLcl',
            render: (h, params) => {
              return h('span', getKeyValue(this.containerLclList, params.row.containerLcl))
            }
          }, {
            align: 'center',
            title: '自重(KG)',
            key: 'containerWt'
          }, {
            align: 'center',
            title: '商品项号关系',
            key: 'containerGNo'
          }]
        },
        frmData: {
          containerMd: '',
          containerModel: '',
          containerWt: '',
          containerLcl: '',
          containerGNo: ''
        },
        toolbarEventMap: {
          'add': this.handleSave,
          'import': this.handleImport,
          'delete': this.handleDelete
        },
        containerModelList: [
          {value: '11', label: '普通2*标准箱(L)'},
          {value: '12', label: '冷藏2*标准箱(L)'},
          {value: '13', label: '罐式2*标准箱(L)'},
          {value: '21', label: '普通标准箱(S)'},
          {value: '22', label: '冷藏标准箱(S)'},
          {value: '23', label: '罐式标准箱(S)'},
          {value: '31', label: '其他标准箱(S)'},
          {value: '32', label: '其他2*标准箱(L)'}
        ],
        containerLclList: [
          {value: '0', label: '否'},
          {value: '1', label: '是'}
        ],
        tyShow: false,
        lineHeight: '0',
        headHeight: 380,
        modelImportShow: false,
        importKey: 'containerie',
        importConfig: commImportConfig
      }
    },
    mounted() {
      let me = this
      me.loadFunctions('container').then()
    },
    methods: {
      getList() {
        let me = this
        me.$http.post(csAPI.csImportExport.iEntry.list, {
          ieMark: me.iemark,
          headId: me.editConfig.editData.sid
        }, {
          params: me.pageParam
        }).then(res => {
          me.gridConfig.data = res.data.data
        }).catch(() => {
        })
      },
      handleSelectionChange(selectRows) {
        let me = this
        me.gridConfig.selectRows = selectRows
      },
      handleAddType() {
        let me = this
        me.tyShow = true
      },
      handleImport() {
        let me = this
        me.modelImportShow = true
      },
      onAfterImport() {
        let me = this
        me.modelImportShow = false
        me.getList()
      },
      handleSave() {
        let me = this
        if (me.frmData) {
          me.$http.post(`${csAPI.csImportExport.iEntry.decIEntryContainer}/${me.editConfig.editData.sid}/${me.iemark}`, me.frmData).then(res => {
            me.$Message.success(res.data.message)
            me.frmData = {}
            me.getList()
          }).catch(() => {
          })
        }
      },
      handleDelete() {
        let me = this
        me.doDelete(csAPI.csImportExport.iEntry.decIEntryContainer, me.actions.findIndex(it => it.command === 'delete'))
      },
      getSerialNoList(val) {
        if (val) {
          let me = this
          me.frmData.containerGNo = val
          me.tyShow = false
        }
      }
    },
    computed: {
      dynamicHeight() {
        return window.innerHeight - this.headHeight - 2
      }
    }
  }
</script>
