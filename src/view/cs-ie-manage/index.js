import { namespace } from '@/project'
import BillEList from './bill/export/bill-e-list'
import BillIList from './bill/import/bill-i-list'
import EntryEList from './entry/export/entry-e-list'
import EntryIList from './entry/import/entry-i-list'
import BondedEHeadList from './dec-erp-main/bonded-ehead-list'
import BondedIHeadList from './dec-erp-main/bonded-ihead-list'
import NonBondedEHeadList from './dec-erp-main/non-bonded-ehead-list'
import NonBondedIHeadList from './dec-erp-main/non-bonded-ihead-list'

export default [
  {
    path: '/' + namespace + '/ieManage/BondedDecErpEList',
    name: 'bondedEHeadList',
    meta: {
      icon: 'ios-document',
      title: '保税出口管理'
    },
    component: BondedEHeadList
  },
  {
    path: '/' + namespace + '/ieManage/BondedDecErpIList',
    name: 'bondedIHeadList',
    meta: {
      icon: 'ios-document',
      title: '保税进口管理'
    },
    component: BondedIHeadList
  },
  {
    path: '/' + namespace + '/ieManage/NonBondedDecErpEList',
    name: 'nonBondedEHeadList',
    meta: {
      icon: 'ios-document',
      title: '非保税出口管理'
    },
    component: NonBondedEHeadList
  },
  {
    path: '/' + namespace + '/ieManage/NonBondedDecErpIList',
    name: 'nonBondedIHeadList',
    meta: {
      icon: 'ios-document',
      title: '非保税进口管理'
    },
    component: NonBondedIHeadList
  },
  {
    path: '/' + namespace + '/ieManage/BillEList',
    name: 'billEList',
    meta: {
      icon: 'ios-document',
      title: '出口清单'
    },
    component: BillEList
  },
  {
    path: '/' + namespace + '/ieManage/BillIList',
    name: 'billIList',
    meta: {
      icon: 'ios-document',
      title: '进口清单'
    },
    component: BillIList
  },
  {
    path: '/' + namespace + '/ieManage/EntryEList',
    name: 'entryEList',
    meta: {
      icon: 'ios-document',
      title: '出口报关单'
    },
    component: EntryEList
  },
  {
    path: '/' + namespace + '/ieManage/EntryIList',
    name: 'entryIList',
    meta: {
      icon: 'ios-document',
      title: '进口报关单'
    },
    component: EntryIList
  }
]
