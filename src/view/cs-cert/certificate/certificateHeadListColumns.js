import { getKeyValue } from '@/libs/util'
import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'documentNo'
  , 'catalog.documentName'
  , 'catalog.documentCode'
  , 'catalog.supervisionCondition'
  , 'catalog.customsCondition'
  , 'catalog.iemark'
  , 'status'
  , 'bondMark'
  , 'authority'
  , 'chargePerson'
  , 'validDate'
  , 'attachName'
  , 'catalog.timesCtrlFlag'
  , 'totalTimes'
  , 'totalQty'
  , 'note'
  , 'qtyCtrlFlag'
  , 'catalog.ctrlColumn'
  , 'insertUser'
  , 'insertTime'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          title: '状态',
          key: 'status',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.IS_EFFECTIVE)
          }
        },
        {
          width: 120,
          title: '证件编号',
          key: 'documentNo'
        },
        {
          width: 120,
          title: '证件名称',
          key: 'catalog.documentName'
        },
        {
          width: 120,
          title: '自定义代码',
          key: 'catalog.documentCode'
        },
        {
          width: 120,
          key: 'bondMark',
          title: '保完税标识',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.bondMarkList)
          }
        },
        {
          width: 120,
          title: '检验监管条件',
          key: 'catalog.supervisionCondition'
        },
        {
          width: 120,
          title: '通关监管条件',
          key: 'catalog.customsCondition'
        },
        {
          width: 120,
          title: '进出口标志',
          key: 'catalog.iemark',
          render: (h, params) => {
            return h('span', getKeyValue(this.certificate.I_E_MARK, params.row.catalog.iemark))
          }
        },
        {
          width: 120,
          title: '原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
          }
        },
        {
          width: 120,
          title: '发证机关',
          key: 'authority'
        },
        {
          width: 120,
          key: 'chargePerson',
          title: '提供人/办理人'
        },
        {
          width: 120,
          title: '有效期',
          key: 'validDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 80,
          title: '是否控次',
          key: 'catalog.timesCtrlFlag',
          render: (h, params) => {
            return h('span', getKeyValue(this.certificate.CTRL_FLAG, params.row.catalog.timesCtrlFlag))
          }
        },
        {
          width: 120,
          title: '申请次数',
          key: 'totalTimes'
        },
        {
          width: 120,
          key: 'totalQty',
          title: '证件总量'
        },
        {
          width: 80,
          title: '是否控量',
          key: 'qtyCtrlFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.QTY_CTRL_FLAG_MAP)
          }
        },
        {
          width: 120,
          title: '管控类型',
          key: 'catalog.ctrlColumn',
          render: (h, params) => {
            return h('span', getKeyValue(this.certificate.CTRL_COLUMN_MAP, params.row.catalog.ctrlColumn))
          }
        },
        {
          width: 80,
          title: '录入人',
          key: 'insertUser'
        },
        {
          width: 120,
          title: '录入时间',
          key: 'insertTime'
        },
        {
          width: 120,
          key: 'note',
          title: '备注'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
