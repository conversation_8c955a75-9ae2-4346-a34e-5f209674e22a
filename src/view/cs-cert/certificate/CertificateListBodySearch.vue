<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'CertificateListBodySearch',
    data() {
      return {
        searchParam: {
          copGNo: '',
          gname: '',
          codeTS: ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
