<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CertificateHeadSearch ref="headSearch"></CertificateHeadSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CertificateHeadTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></CertificateHeadTabs>
    <DcImport v-model="importShow" :config="importConfig" :bizParam="importParam" @importSuccess="importSuccess"></DcImport>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import importMixin from '@/mixin/dc_import_mixin'
  import CertificateHeadTabs from './CertificateHeadTabs'
  import CertificateHeadSearch from './CertificateHeadSearch'
  import { certificate, importExportManage } from '@/view/cs-common'
  import { certificateListBasic } from '../base/certificateListBasic'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './certificateHeadListColumns'

  export default {
    name: 'CertificateHeadList',
    components: {
      CertificateHeadTabs,
      CertificateHeadSearch
    },
    mixins: [certificateListBasic, columns, importMixin],
    data() {
      return {
        searchLines: 4,
        certificate: certificate,
        gridConfig: {
          exportTitle: '证件台账-表头'
        },
        toolbarEventMap: {
          'enable': this.handleEnable,
          'disable': this.handleDisable
        },
        ajaxUrl: {
          delete: csAPI.cert.book.head.delete,
          exportUrl: csAPI.cert.book.head.export,
          selectAllPaged: csAPI.cert.book.head.list,
          enableCert: csAPI.cert.book.head.enableCert
        },
        cmbSource: {
          bondMarkList: importExportManage.bondedFlagMap
        },
        importConfig: {
          tplName: '证件台账',
          url: csAPI.cert.book.head.rest + '/import',
          errorUrl: csAPI.cert.book.head.rest + '/export',
          tplUrl: csAPI.cert.book.head.rest + '/export/tpl',
          correctUrl: csAPI.cert.book.head.rest + '/import'
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      /**
       * 导入成功后事件
       */
      importSuccess() {
        this.handleSearchSubmit()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
