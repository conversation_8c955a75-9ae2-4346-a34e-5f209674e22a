<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="状态">
        <xdo-select v-model="searchParam.status" :options="this.certificate.IS_EFFECTIVE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="documentNo" label="证件编号">
        <XdoIInput type="text" v-model="searchParam.documentNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="documentName" label="证件名称">
        <XdoIInput type="text" v-model="searchParam.documentName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="documentCode" label="自定义代码">
        <xdo-select v-model="searchParam.documentCode" :options="this.cmbSource.documentNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="bondMark" label="保完税标识">
        <xdo-select v-model="searchParam.bondMark" :options="this.cmbSource.bondMarkList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="supervisionCondition" label="检验监管条件">
        <XdoIInput type="text" v-model="searchParam.supervisionCondition"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="customsCondition" label="通关监管条件">
        <xdo-select v-model="searchParam.customsCondition" :options="this.certificate.CUSTOMS_CONDITION"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="iemark" label="进出口标志">
        <xdo-select v-model="searchParam.iemark" :options="this.certificate.I_E_MARK" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="authority" label="发证机关">
        <XdoIInput type="text" v-model="searchParam.authority"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="有效期"  @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="ctrlColumn" label="管控类型">
        <xdo-select v-model="searchParam.ctrlColumn" :options="this.certificate.CTRL_COLUMN_MAP" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { certificate, importExportManage } from '@/view/cs-common'

  export default {
    name: 'CertificateHeadSearch',
    data() {
      return {
        searchParam: {
          status: '',
          documentNo: '',
          bondMark: '',
          documentCode: '',
          documentName: '',
          supervisionCondition: '',
          customsCondition: '',
          iemark: '',
          authority: '',
          validDateFrom: '',
          validDateTo: '',
          ctrlColumn: ''
        },
        certificate: certificate,
        cmbSource: {
          documentNoData: [],
          bondMarkList: importExportManage.bondedFlagMap
        }
      }
    },
    created() {
      // 需要为【this.cmbSource.documentNoData】赋值, 此数据源来自【涉证类型】功能中录入的内容
      let me = this
      // 备案号
      me.$http.post(csAPI.cert.catalog.head.all).then(res => {
        me.cmbSource.documentNoData = res.data.data.map(item => {
          return {
            label: item.documentCode,
            value: item.documentCode
          }
        })
      })
    },
    methods: {
      handleDMDateChange(e) {
        this.searchParam.validDateFrom = e[0]
        this.searchParam.validDateTo = e[1]
      }
    }
  }
</script>

<style scoped>
</style>
