<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="documentNo" label="证件编号">
            <XdoIInput type="text" v-model="frmData.documentNo" :maxlength="40" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="documentCode" label="自定义代码">
            <xdo-select v-model="frmData.catalogId" :options="this.cmbSource.documentNoData" :disabled="showDisable"
                        @on-change="onDocumentNoChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="documentName" label="证件名称">
            <XdoIInput type="text" v-model="frmData.documentName" :maxlength="50" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="supervisionCondition" label="检验监管条件">
            <XdoIInput type="text" v-model="frmData.supervisionCondition" :maxlength="50" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="customsCondition" label="通关监管条件">
            <xdo-select v-model="frmData.customsCondition" :options="this.certificate.CUSTOMS_CONDITION" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="iemark" label="进出口标志">
            <xdo-select v-model="frmData.iemark" :options="this.certificate.I_E_MARK" :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="authority" label="发证机关">
            <XdoIInput type="text" v-model="frmData.authority" :maxlength="50" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="bondMark" label="保完税标识">
            <xdo-select v-model="frmData.bondMark" :options="this.cmbSource.bondMarkList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable" @on-change="bondMarkChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="originCountry" label="原产国">
            <xdo-select v-model="frmData.originCountry" :disabled="showDisable" :asyncOptions="pcodeList" meta="COUNTRY_OUTDATED" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="chargePerson" label="提供人/办理人">
            <XdoIInput type="text" v-model="frmData.chargePerson" :maxlength="25" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="validDate" label="有效期">
            <XdoDatePicker type="date" placeholder="请选择有效期" v-model="frmData.validDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="qtyCtrlFlag" label="是否控量">
            <xdo-select v-model="frmData.qtyCtrlFlag" :options="this.certificate.QTY_CTRL_FLAG_MAP" :optionLabelRender="pcodeRender" :disabled="showDisable"
                        @on-change="onQtyCtrlFlagChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="timesCtrlFlag" label="是否控次">
            <xdo-select v-model="frmData.timesCtrlFlag" :options="this.certificate.CTRL_FLAG" :optionLabelRender="pcodeRender" disabled
                        @on-change="onTotalTimesChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="totalTimes" ref="totalTimes" label="申请次数">
            <xdo-input v-model="frmData.totalTimes" number int-length="5" precision="0"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="ctrlColumn" label="管控类型">
            <xdo-select v-model="frmData.ctrlColumn" :options="this.certificate.CTRL_COLUMN_MAP" :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="totalQty" ref="totalQty" label="证件总量">
            <xdo-input v-model="frmData.totalQty" decimal int-length="15" precision="5" :disabled="showEdit"></xdo-input>
          </XdoFormItem>
          <XdoFormItem class="dc-merge-1-4" prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable" :maxlength="127"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div v-if="frmData.sid">
      <AcmpInfoListCustom :sid="frmData.sid" :showAction="!showDisable" :just-view="showDisable"></AcmpInfoListCustom>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { importExportManage } from '@/view/cs-common'
  import { AcmpInfoListCustom, editStatus } from '@/view/cs-common'
  import { certificateEditBasic } from '../base/certificateEditBasic'

  export default {
    name: 'CertificateHeadEdit',
    components: {
      AcmpInfoListCustom
    },
    mixins: [certificateEditBasic],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        showEdit: false,
        firstTimeChange: true,
        ajaxUrl: {
          insert: csAPI.cert.book.head.rest,
          update: csAPI.cert.book.head.rest
        },
        cmbSource: {
          documentNoObj: {},
          documentNoData: [],
          bondMarkList: importExportManage.bondedFlagMap
        },
        rulesHeader: {
          note: [],
          validDate: [],
          authority: [],
          chargePerson: [],
          customsCondition: [],
          supervisionCondition: [],
          iemark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          bondMark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          documentNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          documentCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          documentName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          totalQty: [{required: false, type: 'number', message: '不是有效的数字!', trigger: 'blur'}],
          totalTimes: [{required: false, type: 'number', message: '不是有效的数字!', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', icon: 'dc-btn-save', click: this.handleSave},
          {...btnComm, type: 'warning', label: '保存继续', icon: 'dc-btn-save-1', click: this.handleSaveContinue},
          {...btnComm, label: '返回', type: 'primary', icon: 'dc-btn-cancel', click: this.handleBack}
        ]
      }
    },
    created() {
      this.getCatalogAll()
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          documentCode: '',
          documentName: '',
          documentNo: '',
          catalogId: '',
          iemark: '',
          bondMark: '',
          authority: '',
          chargePerson: '',
          validDate: '',
          vrfdedType: '',
          totalTimes: null,
          timesCtrlFlag: '',
          qtyCtrlFlag: '',
          note: '',
          totalQty: null,
          ctrlColumn: ''
        }
      },
      getCatalogAll() {
        let me = this
        me.$http.post(csAPI.cert.catalog.head.all).then(res => {
          me.cmbSource.documentNoData = res.data.data.map(item => {
            return {
              value: item.sid,
              label: item.documentCode
            }
          })
          let docObj = {}
          for (let item of res.data.data) {
            docObj[item.sid] = item
          }
          me.$set(me.cmbSource, 'documentNoObj', docObj)
          me.onDocumentNoChange(me.frmData.catalogId)
        })
      },
      onDocumentNoChange(val) {
        let me = this
        if (me.firstTimeChange) {
          me.$set(me, 'firstTimeChange', false)
          return
        }
        let theData = me.cmbSource.documentNoObj[val]
        if (theData) {
          me.$set(me.frmData, 'catalogId', theData.sid)
          me.$set(me.frmData, 'supervisionCondition', theData.supervisionCondition)
          me.$set(me.frmData, 'customsCondition', theData.customsCondition)
          me.$set(me.frmData, 'documentCode', theData.documentCode)
          me.$set(me.frmData, 'documentName', theData.documentName)
          me.$set(me.frmData, 'iemark', theData.iemark)
          me.$set(me.frmData, 'timesCtrlFlag', theData.timesCtrlFlag)
          me.$set(me.frmData, 'ctrlColumn', theData.ctrlColumn)
          me.$set(me.frmData, 'catalog', theData)
        } else {
          me.$set(me.frmData, 'catalogId', '')
          me.$set(me.frmData, 'supervisionCondition', '')
          me.$set(me.frmData, 'customsCondition', '')
          me.$set(me.frmData, 'documentCode', '')
          me.$set(me.frmData, 'documentName', '')
          me.$set(me.frmData, 'iemark', '')
          me.$set(me.frmData, 'timesCtrlFlag', '')
          me.$set(me.frmData, 'ctrlColumn', '')
          me.$set(me.frmData, 'catalog', {})
        }
      },
      handleSave() {
        let me = this
        me.$set(me.frmData.catalog, 'ctrlColumn', me.frmData.ctrlColumn)
        me.doSave((res) => {
          if (!me.frmData.sid) { //XINZENGBAO
            me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          } else {
            me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          }
          me.$nextTick(() => {
            me.onDocumentNoChange(res.data.data.catalogId)
          })
        })
      },
      handleDMDateChange(e) {
        let me = this
        me.frmData.validDateFrom = e[0]
        me.frmData.validDateTo = e[1]
      },
      onQtyCtrlFlagChange(val) {
        let me = this
        //  根据是否控量控制表头证件总量和表体可用数量
        if (val === '1') {
          me.$set(me, 'showEdit', false)
          me.rulesHeader.totalQty[0].required = true
          me.$refs['totalQty'].setRules()
        } else {
          me.$set(me, 'showEdit', true)
          me.rulesHeader.totalQty[0].required = false
          me.$refs['totalQty'].setRules()
        }
      },
      onTotalTimesChange(val) {
        let me = this
        //  根据是否控次控制申请次数是否可编辑
        me.rulesHeader.totalTimes[0].required = val === '1'
        me.$refs['totalTimes'].setRules()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
