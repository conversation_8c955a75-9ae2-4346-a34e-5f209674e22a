import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'emsNo'
  , 'copGNo'
  , 'facGNo'
  , 'gmark'
  , 'gname'
  , 'codeTS'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'qty'
  , 'totalQty'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 120,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 120,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.G_MARK_STATUS_MAP)
          }
        },
        {
          width: 120,
          key: 'gname',
          title: '商品名称'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 120,
          title: '单价',
          key: 'decPrice'
        },
        {
          width: 120,
          title: '总价',
          key: 'decTotal'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'headId',
          title: '表头ID'
        },
        {
          width: 120,
          key: 'tradeCode',
          title: '企业代码'
        },
        {
          width: 120,
          key: 'totalQty',
          title: '证件总量'
        },
        {
          width: 120,
          key: 'qty',
          title: '已用数量'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
