<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="gmark" label="物料类型">
            <xdo-select v-model="frmData.gmark" :options="this.cmbSource.gmarkList"
                        :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="emsNo" label="备案号">
            <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoList" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="copGNo" label="备案料号">
            <XdoIInput type="text" v-model="frmData.copGNo" disabled :maxlength="32"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="facGNo" label="企业料号">
            <xdo-select v-model="frmData.facGNo" :options="this.cmbSource.facGNoList" :disabled="isEdit" @on-change="getOtherData"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="gname" label="商品名称">
            <XdoIInput type="text" v-model="frmData.gname" disabled :maxlength="127"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="codeTS" label="商品编码">
            <xdo-select v-model="frmData.codeTS" :options="this.cmbSource.codeTSList" :disabled="!isEdit" @on-change="getGNameData"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="totalQty" label="申请数量">
            <xdo-input v-model="frmData.totalQty" decimal int-length="11" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decPrice" label="单价">
            <xdo-input v-model="frmData.decPrice" decimal int-length="11" precision="5" :disabled="showDisable" @on-enter="decPriceEnter"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decTotal" label="总价">
            <xdo-input v-model="frmData.decTotal" decimal int-length="11" precision="5" :disabled="showDisable" @on-enter="decTotalEnter"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="frmData.curr" :disabled="showDisable" :asyncOptions="pcodeList"
                        :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { productClassify } from '@/view/cs-common'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { certificateEditBasic } from '../base/certificateEditBasic'

  export default {
    name: 'CertificateListBodyEdit',
    mixins: [certificateEditBasic],
    props: {
      parentConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        isEdit: false,
        cmbSource: {
          emsNoList: [],
          emsCopEmsNo: {},
          facGNoList: [],
          codeTSList: [],
          documentNoObj: [],
          gmarkList: productClassify.GMARK_SELECT
        },
        ajaxUrl: {
          insert: csAPI.cert.book.body.rest,
          update: csAPI.cert.book.body.rest,
          getCopGNo: csAPI.cert.comm.getCopGNo,
          getCatalogList: csAPI.cert.book.body.getCatalogList   // 获取料号、编码相关下拉框的值
        },
        rulesHeader: {
          codeTS: [{required: true, message: '不能为空!', trigger: 'blur'}],
          facGNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
          totalQty: [{required: false, type: 'number', message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        let tmpEmsCopEmsNo = {}
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
          if (!isNullOrEmpty(item['COP_EMS_NO']) && !isNullOrEmpty(item.VALUE) && typeof tmpEmsCopEmsNo[item.VALUE] === 'undefined') {
            tmpEmsCopEmsNo[item.VALUE] = item['COP_EMS_NO']
          }
        }
        me.cmbSource.emsNoList = tmpArr
        me.cmbSource.emsCopEmsNo = tmpEmsCopEmsNo
        me.getCatalogList()
      }).catch(() => {
      })
    },
    methods: {
      getDefaultData() {
        return {
          headId: '',
          sid: '',
          emsNo: '',
          copGNo: '',
          facGNo: '',
          gmark: '',
          gname: '',
          codeTS: '',
          totalQty: null,
          decPrice: null,
          decTotal: null,
          curr: ''
        }
      },
      facGNoEnter() {
        let me = this
        me.$http.post(csAPI.csMaterielCenter.bonded.getIeInfo, {
          bondedFlag: me.editConfig.bondMark,     // 保税标记
          copGNo: me.frmData.copGNo,              // 备案料号
          emsNo: me.frmData.emsNo,                // 备案号
          gmark: me.frmData.gmark,                // 物料类型
          facGNo: me.frmData.facGNo               // 企业料号
        }).then(res => {
          me.$set(me.frmData, 'gname', res.data.data.gname)
          me.$set(me.frmData, 'codeTS', res.data.data.codeTS)
        }).catch(() => {
        })
      },
      decPriceEnter() {
        let me = this
        if (isNumber(me.frmData.totalQty)) {
          if (isNumber(me.frmData.decPrice)) {
            let total = (me.frmData.totalQty * me.frmData.decPrice).toFixed(5)
            me.$set(me.frmData, 'decTotal', parseFloat(total))
          }
        }
      },
      decTotalEnter() {
        let me = this
        if (isNumber(me.frmData.totalQty)) {
          if (isNumber(me.frmData.decTotal)) {
            let price = (me.frmData.decTotal / me.frmData.totalQty).toFixed(4)
            me.$set(me.frmData, 'decPrice', parseFloat(price))
          }
        }
      },
      /**
       * 获取企业料号和商品编码下拉框的值
       */
      getCatalogList() {
        let me = this
        me.$http.post(`${me.ajaxUrl.getCatalogList}/${me.parentConfig.editData.catalogId}`).then(res => {
          if (res.data.success && res.data.data) {
            res.data.data.forEach(item => {
              if (item.facGNo) {
                me.cmbSource.facGNoList.push({
                  label: item.facGNo,
                  value: item.facGNo
                })
              }
              if (item.codeTS) {
                me.cmbSource.codeTSList.push({
                  label: item.codeTS,
                  value: item.codeTS
                })
              }
              me.cmbSource.documentNoObj.push(item)
            })
          }
        })
      },
      getOtherData(val) {
        let me = this
        //  根据企业料号带出其他栏位的值
        if (val) {
          let theData = me.cmbSource.documentNoObj.filter(item => item.facGNo === val)[0]
          if (theData) {
            me.$set(me.frmData, 'catalogListId', theData.sid)
            me.$set(me.frmData, 'gmark', theData.gmark)
            me.$set(me.frmData, 'emsNo', theData.emsNo)
            me.$set(me.frmData, 'copGNo', theData.copGNo)
            me.$set(me.frmData, 'gname', theData.gname)
            me.$set(me.frmData, 'codeTS', theData.codeTS)
          } else {
            me.$set(me.frmData, 'catalogListId', '')
            me.$set(me.frmData, 'gmark', '')
            me.$set(me.frmData, 'emsNo', '')
            me.$set(me.frmData, 'copGNo', '')
            me.$set(me.frmData, 'gname', '')
            me.$set(me.frmData, 'codeTS', '')
          }
        }
      },
      getGNameData(val) {
        let me = this
        // 根据商品编码带出商品名称
        if (val) {
          let theData = me.cmbSource.documentNoObj.filter(item => item.codeTS === val)[0]
          if (theData) {
            me.$set(me.frmData, 'catalogListId', theData.sid)
            me.$set(me.frmData, 'gname', theData.gname)
            me.$set(me.frmData, 'gmark', theData.gmark)
          } else {
            me.$set(me.frmData, 'catalogListId', '')
            me.$set(me.frmData, 'gname', '')
            me.$set(me.frmData, 'gmark', '')
          }
        }
      }
    },
    watch: {
      'parentConfig.editData.qtyCtrlFlag': {
        immediate: true,
        handler: function (val) {
          let me = this
          //  根据是否控量控制表头证件总量和表体可用数量
          switch (val) {
            case '0' :
              me.$set(me, 'showEdit', true)
              break;
            case '1' :
              me.$set(me, 'showEdit', false)
              break;
            case '2' :
              me.$set(me, 'showEdit', true)
              me.rulesHeader.totalQty[0].required = true
              break;
            default:
              me.$set(me, 'showEdit', false)
          }
        }
      },
      'parentConfig.editData.catalog.ctrlColumn': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === '1') {
            me.isEdit = true
            me.rulesHeader.facGNo[0].required = false
          } else if (val === '2') {
            me.isEdit = false
            me.rulesHeader.facGNo[0].required = true
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
