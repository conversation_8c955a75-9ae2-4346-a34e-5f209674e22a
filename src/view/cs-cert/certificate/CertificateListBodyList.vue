<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CertificateListBodySearch ref="headSearch"></CertificateListBodySearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" :disable="editDisable"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <BodyEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" :parentConfig="parentConfig"></BodyEdit>
    <DcImport v-model="importShow" :config="importConfig" :bizParam="importParam" @importSuccess="importSuccess"></DcImport>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { editStatus} from '@/view/cs-common'
  import BodyEdit from './CertificateListBodyEdit'
  import importMixin from '@/mixin/dc_import_mixin'
  import { productClassify } from '@/view/cs-common'
  import { certificateListBasic } from '../base/certificateListBasic'
  import CertificateListBodySearch from './CertificateListBodySearch'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './certificateListBodyListColumns'

  export default {
    name: 'CertificateListBodyList',
    components: {
      BodyEdit,
      CertificateListBodySearch
    },
    mixins: [certificateListBasic, columns, importMixin, pms],
    props: {
      parentConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        editConfig: {
          headId: '',
          bondMark: ''
        },
        searchLines: 1,
        importParam: {
          headId: ''
        },
        hasChildTabs: true,
        gridConfig: {
          exportTitle: '证件台账-表体'
        },
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'import': this.handleImport,
          'export': this.handleDownload
        },
        productClassify: productClassify,
        ajaxUrl: {
          delete: csAPI.cert.book.body.delete,
          exportUrl: csAPI.cert.book.body.export,
          selectAllPaged: csAPI.cert.book.body.list
        },
        importConfig: {
          tplName: '证件台账-表体',
          url: csAPI.cert.book.body.rest + '/import',
          tplUrl: csAPI.cert.book.body.rest + '/export/tpl',
          correctUrl: csAPI.cert.book.body.rest + '/import',
          errorUrl: csAPI.cert.book.body.rest + '/export'
        }
      }
    },
    watch: {
      'parentConfig.editData': {
        deep: true,
        immediate: true,
        handler: function (data) {
          let me = this
          me.$set(me.editConfig, 'headId', data.sid)
          me.$set(me.editConfig, 'bondMark', data.bondMark)
          me.$set(me.importParam, 'headId', data.sid)
        }
      }
    },
    computed: {
      editDisable() {
        return this.parentConfig.editStatus !== editStatus.EDIT
      }
    },
    mounted: function () {
      let me = this
      me.loadFunctions('body').then()
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      actionLoaded() {
        let me = this
        if (me.parentConfig.editStatus === editStatus.SHOW) {
          me.actions = me.actions.filter(item => item.key === 'xdo-btn-download')
        }
      },
      operationEditShow() {
        if (editStatus.SHOW === this.parentConfig.editStatus) {
          return 'none'
        } else {
          return ''
        }
      },
      getSearchParams() {
        let me = this
        return Object.assign({
          headId: me.parentConfig.editData.sid
        }, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}))
      },
      /**
       * 导入成功后事件
       */
      importSuccess() {
        this.handleSearchSubmit()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
