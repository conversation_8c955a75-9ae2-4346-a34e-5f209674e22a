import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'status'
  , 'useStatus'
  , 'documentNo'
  , 'documentName'
  , 'documentCode'
  , 'bondMark'
  , 'supervisionCondition'
  , 'customsCondition'
  , 'iemark'
  , 'authority'
  , 'chargePerson'
  , 'validDate'
  , 'ctrlColumn'
  , 'note'
  , 'timesCtrlFlag'
  , 'qtyCtrlFlag'
  , 'totalQty'
  , 'qty'
  , 'remainQty'
  , 'totalTimes'
  , 'declareTimes'
  , 'remainTimes'
]

const columnsConfig = [
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 80,
          key: 'status',
          title: '证件状态',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.STATUS_MAP)
          }
        },
        {
          width: 80,
          title: '使用状态',
          key: 'useStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.USE_STATUS_MAP)
          }
        },
        {
          width: 80,
          title: '证件编号',
          key: 'documentNo'
        },
        {
          width: 150,
          title: '证件名称',
          key: 'documentName'
        },
        {
          width: 100,
          title: '自定义代码',
          key: 'documentCode'
        },
        {
          width: 100,
          key: 'bondMark',
          title: '保完税标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.bondMarkList)
          }
        },
        {
          width: 120,
          title: '检验监管证件',
          key: 'supervisionCondition'
        },
        {
          width: 120,
          title: '通关监管条件',
          key: 'customsCondition'
        },
        {
          width: 120,
          key: 'iemark',
          title: '进出口标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.I_E_MARK)
          }
        },
        {
          width: 120,
          key: 'authority',
          title: '发证机关'
        },
        {
          width: 120,
          key: 'chargePerson',
          title: '提供人/办理人'
        },
        {
          width: 120,
          title: '有效期',
          key: 'validDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          title: '管控类型',
          key: 'ctrlColumn',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CTRL_COLUMN_MAP)
          }
        },
        {
          width: 120,
          key: 'note',
          title: '备注'
        },
        {
          width: 120,
          title: '是否控次',
          key: 'timesCtrlFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CTRL_FLAG)
          }
        },
        {
          width: 120,
          title: '是否控量',
          key: 'qtyCtrlFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.QTY_CTRL_FLAG_MAP)
          }
        },
        {
          width: 80,
          key: 'totalQty',
          title: '证件总量'
        },
        {
          width: 80,
          key: 'qty',
          title: '已用数量',
          render: (h, params) => {
            if (params.row.qty > 0) {
              return h('a', {
                on: {
                  click: () => {
                    this.handleCheckedUsed('usedQuantity', params.row.sid)
                  }
                }
              }, params.row.qty)
            } else {
              return h('span', params.row.qty)
            }
          }
        },
        {
          width: 80,
          title: '剩余数量',
          key: 'remainQty'
        },
        {
          width: 80,
          title: '申请次数',
          key: 'totalTimes'
        },
        {
          width: 80,
          title: '已用次数',
          key: 'declareTimes',
          render: (h, params) => {
            if (params.row.declareTimes > 0) {
              return h('a', {
                on: {
                  click: () => {
                    this.handleCheckedUsed('usedTimes', params.row.sid)
                  }
                }
              }, params.row.declareTimes)
            } else {
              return h('span', params.row.declareTimes)
            }
          }
        },
        {
          width: 80,
          title: '剩余次数',
          key: 'remainTimes'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
