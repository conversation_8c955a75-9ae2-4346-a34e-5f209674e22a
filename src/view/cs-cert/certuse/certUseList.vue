<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CertificateHeadSearch ref="headSearch"></CertificateHeadSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
      <usedCheck :show.sync="isShowUsedCheck" :type="type" :headId="headId"></usedCheck>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import CertificateHeadSearch from './certUseSearch'
  import { certificate, importExportManage } from '@/view/cs-common'
  import usedCheck from '@/view/cs-cert/certuse/components/usedCheck'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { columnsConfig, excelColumnsConfig, columns } from './certUseListColumns'

  export default {
    name: 'certUseList',
    components: {
      usedCheck,
      CertificateHeadSearch
    },
    mixins: [columns, commList],
    data() {
      return {
        type: '',
        headId: '',
        searchLines: 3,
        isShowUsedCheck: false,
        certificate: certificate,
        gridConfig: {
          exportTitle: '证件台账-表头'
        },
        ajaxUrl: {
          selectAllPaged: csAPI.cert.use.list
        },
        cmbSource: {
          bondMarkList: importExportManage.bondedFlagMap
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    },
    methods: {
      // getList() {
      //   let me = this,
      //     params = me.getSearchParams()
      //   me.$http.post(me.ajaxUrl.selectAllPaged, params, {params: me.pageParam}).then(res => {
      //     me.gridConfig.data = res.data.data
      //     me.pageParam.page = res.data.pageIndex
      //     me.pageParam.dataTotal = res.data.total
      //   }).catch(() => {
      //   })
      // },
      handleCheckedUsed(type, headId) {
        let me = this
        // type指是使用次数还是使用数量
        me.type = type
        me.headId = headId
        me.isShowUsedCheck = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
