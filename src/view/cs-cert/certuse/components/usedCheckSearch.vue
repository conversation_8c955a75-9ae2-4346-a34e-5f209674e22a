<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号" v-if="type === 'usedQuantity'">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称" v-if="type === 'usedQuantity'">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码" v-if="type === 'usedQuantity'">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsListNoBill" label="清单内部编号" v-if="type === 'usedTimes'">
        <XdoIInput type="text" v-model="searchParam.emsListNoBill"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange" v-if="type === 'usedTimes'"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'usedCheckSearch',
    props: {
      type: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',
          emsListNoBill: '',
          facGNo: '',
          gname: '',
          codeTS: '',
          insertTimeFrom: '',
          insertTimeTo: ''
        }
      }
    },
    methods: {
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    },
    computed: {
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style scoped>
</style>
