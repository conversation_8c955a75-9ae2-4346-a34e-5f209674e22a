<template>
  <XdoModal v-model="show" mask width="1024" :title="modalTitle"
            :mask-closable="false" footer-hide :closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
          <div class="separateLine"></div>
          <usedCheckSearch ref="headSearch" :type="type"></usedCheckSearch>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { erpInterfaceData } from '@/view/cs-common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import usedCheckSearch from '@/view/cs-cert/certuse/components/usedCheckSearch'
  import { usedQuantityConfig, usedTimesConfig, columns } from './usedCheckColumns'

  export default {
    name: 'usedCheck',
    mixins: [commList, columns],
    components: {
      usedCheckSearch
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      type: {
        type: String,
        require: true
      },
      headId: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        ajaxUrl: {
          selectAllPaged: ''
        },
        erpInterfaceData: erpInterfaceData
      }
    },
    methods: {
      getSearchParams() {
        let me = this
        return Object.assign({}, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}), {
          certificateId: me.headId
        })
      },
      handleClose() {
        let me = this
        for (let key in me.$refs.headSearch.searchParam) {
          me.$refs.headSearch.searchParam[key] = ''
        }
        me.$emit('update:show', false)
      }
    },
    computed: {
      modalTitle: function () {
        let me = this
        if (me.type === 'usedQuantity') {
          return '已用数量查询'
        } else {
          return '已用次数查询'
        }
      }
    },
    watch: {
      'show': {
        handler: function (val) {
          if (val) {
            let me = this
            if (me.type === 'usedQuantity') {
              me.ajaxUrl.selectAllPaged = csAPI.cert.use.getQtyUseDetail
              me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns.filter(item => item.title !== '清单内部编号'), usedQuantityConfig)
            } else if (me.type === 'usedTimes') {
              me.ajaxUrl.selectAllPaged = csAPI.cert.use.getTimesUseDetail
              me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, usedTimesConfig)
            } else {
              me.ajaxUrl.selectAllPaged = ''
            }
            me.getList()
          }
        }
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
    background-color: #E9EBEE !important;
  }
</style>
