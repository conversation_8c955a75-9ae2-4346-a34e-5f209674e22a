import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'emsListNo'
  , 'emsListNoBill'
  , 'insertTime'
]

// 使用数量
const usedQuantityConfig = [
  ...commColumns
  , 'facGNo'
  , 'copGNo'
  , 'gmark'
  , 'emsNo'
  , 'gname'
  , 'codeTS'
  , 'qty'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'listTotalQty'
  , 'listRemainQty'
]

// 使用次数
const usedTimesConfig = [
  ...commColumns
  , 'listNo'
  , 'entryNo'
  , 'entryDeclareDate'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          key: 'emsListNo',
          title: '清单内部编号'
        },
        {
          width: 120,
          title: '单据内部编号',
          key: 'emsListNoBill',
          render: (h, params) => {
            if (this.type === 'usedQuantity') {
              return h('span', params.row.emsListNo)
            } else {
              return h('span', params.row.emsListNoBill)
            }
          }
        },
        {
          width: 120,
          title: '制单日期',
          key: 'insertTime'
        },
        {
          width: 150,
          key: 'listNo',
          title: '清单核注编号'
        },
        {
          width: 100,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 150,
          title: '报关单申报日期',
          key: 'entryDeclareDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 120,
          key: 'gmark',
          title: '物料类型标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 120,
          key: 'gname',
          title: '商品名称'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 120,
          key: 'qty',
          title: '已用数量'
        },
        {
          width: 120,
          title: '单价',
          key: 'decPrice'
        },
        {
          width: 120,
          title: '总价',
          key: 'decTotal'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          key: 'listTotalQty',
          title: '表体可用数量'
        },
        {
          width: 120,
          title: '表体剩余数量',
          key: 'listRemainQty'
        }
      ]
    }
  }
}

export {
  usedQuantityConfig,
  usedTimesConfig,
  columns
}
