<template>
  <section>
    <div v-show="showList" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CertificateTypeHeadSearch ref="headSearch"></CertificateTypeHeadSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CertificateCatalogHeadTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></CertificateCatalogHeadTabs>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { certificate } from '@/view/cs-common'
  import { certificateListBasic } from '../base/certificateListBasic'
  import CertificateCatalogHeadTabs from './CertificateCatalogHeadTabs'
  import CertificateTypeHeadSearch from './CertificateCatalogHeadSearch'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './certificateCatalogHeadListColumns'

  export default {
    name: 'CertificateCatalogHeadList',
    components: {
      CertificateTypeHeadSearch,
      CertificateCatalogHeadTabs
    },
    mixins: [certificateListBasic, columns],
    data() {
      return {
        searchLines: 2,
        gridConfig: {
          exportTitle: '涉证目录'
        },
        certificate: certificate,
        ajaxUrl: {
          delete: csAPI.cert.catalog.head.delete,
          exportUrl: csAPI.cert.catalog.head.export,
          selectAllPaged: csAPI.cert.catalog.head.list
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
