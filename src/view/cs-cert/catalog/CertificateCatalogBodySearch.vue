<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { certificate } from '@/view/cs-common'

  export default {
    name: 'CertificateCatalogBodySearch',
    data() {
      return {
        searchParam: {
          facGNo: '',
          gname: '',
          codeTS: ''
        },
        cmbSource: {
          documentNoData: []
        },
        certificate: certificate
      }
    }
  }
</script>

<style scoped>
</style>
