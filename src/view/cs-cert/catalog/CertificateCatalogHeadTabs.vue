<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <Head :edit-config="editConfig" @onEditBack="editBack"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体">
        <Body v-if="tabs.bodyTab" :parent-config="editConfig"></Body>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import Head from './CertificateCatalogHeadEdit'
  import Body from './CertificateCatalogBodyList'

  export default {
    name: 'CertificateCatalogHeadTab',
    components: {
      Head,
      Body
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        tabName: 'headTab',
        tabs: {
          headTab: true,
          bodyTab: false
        }
      }
    },
    methods: {
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      },
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    },
    computed: {
      showBody() {
        return !isNullOrEmpty(this.editConfig.editData.sid)
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
