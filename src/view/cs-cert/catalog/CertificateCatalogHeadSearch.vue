<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="documentName" label="证件名称">
        <XdoIInput type="text" v-model="searchParam.documentName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="documentCode" label="自定义代码">
        <XdoIInput type="text" v-model="searchParam.documentCode"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="supervisionCondition" label="检验监管条件">
        <xdo-select type="text" v-model="searchParam.supervisionCondition" :options="this.certificate.INSPECTION_CONDITION" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="customsCondition" label="通关监管条件">
        <xdo-select v-model="searchParam.customsCondition" :options="this.certificate.CLEARANCE_CONDITION" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="iemark" label="进出口标志">
        <xdo-select v-model="searchParam.iemark" :options="this.certificate.I_E_MARK" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ctrlColumn" label="管控类型">
        <xdo-select v-model="searchParam.ctrlColumn" :options="this.certificate.CTRL_COLUMN_MAP" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { certificate } from '@/view/cs-common'

  export default {
    name: 'CertificateCatalogHeadSearch',
    data() {
      return {
        searchParam: {
          documentCode: '',
          documentName: '',
          iemark: '',
          supervisionCondition: '',
          customsCondition: '',
          ctrlColumn: ''
        },
        searchLines: 2,
        certificate: certificate
      }
    }
  }
</script>

<style scoped>
</style>
