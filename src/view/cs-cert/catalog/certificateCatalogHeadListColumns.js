import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'documentName'
  , 'documentCode'
  , 'supervisionCondition'
  , 'customsCondition'
  , 'timesCtrlFlag'
  , 'qtyCtrlFlag'
  , 'ctrlColumn'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'iemark'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]
const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          title: '证件名称',
          key: 'documentName'
        },
        {
          width: 120,
          title: '自定义代码',
          key: 'documentCode'
        },
        {
          width: 120,
          title: '检验监管条件',
          key: 'supervisionCondition',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.INSPECTION_CONDITION)
          }
        },
        {
          width: 120,
          title: '通关监管条件',
          key: 'customsCondition',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CLEARANCE_CONDITION)
          }
        },
        {
          width: 120,
          key: 'iemark',
          title: '进出口标志',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.I_E_MARK)
          }
        },
        {
          width: 120,
          align: 'center',
          key: 'tradeCode',
          title: '企业代码'
        },
        {
          width: 80,
          title: '是否控次',
          key: 'timesCtrlFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CTRL_FLAG)
          }
        },
        {
          width: 80,
          title: '是否控量',
          key: 'qtyCtrlFlag',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CTRL_FLAG)
          }
        },
        {
          width: 120,
          title: '管控类型',
          key: 'ctrlColumn',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.certificate.CTRL_COLUMN_MAP)
          }
        },
        {
          width: 80,
          key: 'note',
          title: '备注'
        },
        {
          width: 80,
          title: '录入人',
          key: 'insertUser'
        },
        {
          width: 120,
          title: '录入时间',
          key: 'insertTime'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
