import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const commColumns = [
  'copGNo'
  , 'gname'
  , 'codeTS'
  , 'emsNo'
  , 'facGNo'
  , 'gmark'
  , 'bondMark'
]

const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        },
        {
          width: 120,
          key: 'facGNo',
          title: '企业料号'
        },
        {
          width: 120,
          key: 'copGNo',
          title: '备案料号'
        },
        {
          width: 120,
          key: 'gname',
          title: '商品名称'
        },
        {
          width: 120,
          key: 'codeTS',
          title: '商品编码'
        },
        {
          width: 100,
          key: 'gmark',
          title: '物料类型标识',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.productClassify.G_MARK_STATUS_MAP)
          }
        },
        {
          width: 120,
          key: 'bondMark',
          title: '保完税标识',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbSource.bondMarkList)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
