<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="bondMark" label="保完税标识">
            <xdo-select v-model="frmData.bondMark" :options="this.cmbSource.bondMarkList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="gmark" label="物料类型标识">
            <xdo-select v-model="frmData.gmark" :options="this.cmbSource.gmarkList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="emsNo" label="备案号">
            <xdo-select  v-model="frmData.emsNo" :options="this.cmbSource.emsNoList" :disabled="showDisable || frmData.bondMark !== '0'"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="facGNo" label="企业料号">
            <xdo-select v-model="frmData.facGNo" :options="this.cmbSource.facGNoSource"
                        :optionLabelRender="(item) => item.label" :disabled="showFacGNo"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="copGNo" label="备案料号">
            <XdoIInput type="text" v-model="frmData.copGNo" :disabled="showDisable || frmData.bondMark !== '0'" :maxlength="32"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gname" label="商品名称">
            <XdoIInput type="text" v-model="frmData.gname" :disabled="showDisable" :maxlength="127"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="codeTS" label="商品编码">
            <XdoIInput type="text" v-model="frmData.codeTS" :disabled="showDisable" :maxlength="10"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { certificateEditBasic } from '../base/certificateEditBasic'
  import { productClassify, certificate, importExportManage } from '@/view/cs-common'

  export default {
    name: 'CertificateCatalogBodyEdit',
    mixins: [certificateEditBasic],
    props: {
      parentConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        showFacGNo: false,
        firstTimeChange: true,
        certificate: certificate,
        cmbSource: {
          emsNoList: [],
          emsCopEmsNo: {},
          facGNoSource: [],
          documentNoObj: {},
          documentNoData: [],
          gmarkList: productClassify.GMARK_SELECT,
          bondMarkList: importExportManage.bondedFlagMap
        },
        ajaxUrl: {
          insert: csAPI.cert.catalog.body.rest,
          update: csAPI.cert.catalog.body.rest,
          getCopGNo: csAPI.cert.comm.getCopGNo,
          getFacGNolist: csAPI.materialRelationship.comm.getFacGNolist
        },
        rulesHeader: {
          emsNo: [],
          copGNo: [],
          // facGNo: [],
          gmark: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          gname: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          codeTS: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          facGNo: [{ required: false, message: '不能为空!', trigger: 'blur' }],
          bondMark: [{ required: true, message: '不能为空!', trigger: 'blur' }]
        }
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        let tmpEmsCopEmsNo = {}
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
          if (!isNullOrEmpty(item['COP_EMS_NO']) && !isNullOrEmpty(item.VALUE) && typeof tmpEmsCopEmsNo[item.VALUE] === 'undefined') {
            tmpEmsCopEmsNo[item.VALUE] = item['COP_EMS_NO']
          }
        }
        me.cmbSource.emsNoList = tmpArr
        me.cmbSource.emsCopEmsNo = tmpEmsCopEmsNo
      })
      me.loadFacGNoData()
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          bondMark: '',
          gmark: '',
          emsNo: '',
          copGNo: '',
          facGNo: '',
          gname: '',
          codeTS: '',
          headId: ''
        }
      },
      facGNoEnter() {
        let me = this,
          facGNo = me.frmData.facGNo
        if (isNullOrEmpty(facGNo)) {
          return
        }
        me.$http.post(csAPI.csMaterielCenter.bonded.getIeInfo, {
          facGNo: facGNo,                   // 企业料号
          emsNo: me.frmData.emsNo,          // 备案号
          gmark: me.frmData.gmark,          // 物料类型
          copGNo: me.frmData.copGNo,        // 备案料号
          bondedFlag: me.frmData.bondMark   // 保税标记
        }).then(res => {
          me.$set(me.frmData, 'copGNo', res.data.data.copGNo)
          me.$set(me.frmData, 'gname', res.data.data.gname)
          me.$set(me.frmData, 'codeTS', res.data.data.codeTS)
        }).catch(() => {
        })
      },
      loadFacGNoData() {
        let me = this
        me.$http.post(me.ajaxUrl.getFacGNolist, {
          emsNo: me.frmData.emsNo,          // 备案号
          gmark: me.frmData.gmark,          // 物料类型
          bondedFlag: me.frmData.bondMark   // 保税标记
        }).then(res => {
          me.$set(me.cmbSource, 'facGNoSource', res.data.data.map(item => {
            return {
              value: item,
              label: item
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'facGNoSource', [])
        })
      }
    },
    watch: {
      'frmData.bondMark': function () {
        this.$set(this.frmData, 'gmark', '')
        this.$set(this.frmData, 'emsNo', '')
        this.$set(this.frmData, 'copGNo', '')
        this.$set(this.frmData, 'facGNo', '')
        this.$set(this.frmData, 'gname', '')
        this.$set(this.frmData, 'codeTS', '')
        this.loadFacGNoData()
      },
      'frmData.gmark': function () {
        this.$set(this.frmData, 'emsNo', '')
        this.$set(this.frmData, 'copGNo', '')
        this.$set(this.frmData, 'facGNo', '')
        this.$set(this.frmData, 'gname', '')
        this.$set(this.frmData, 'codeTS', '')
        this.loadFacGNoData()
      },
      'frmData.emsNo': function () {
        if (this.frmData.bondMark === '0') {
          this.$set(this.frmData, 'copGNo', '')
          this.$set(this.frmData, 'facGNo', '')
          this.$set(this.frmData, 'gname', '')
          this.$set(this.frmData, 'codeTS', '')
          this.loadFacGNoData()
        }
      },
      'parentConfig.editData': {
        immediate: true,
        handler: function (val) {
          if (val.ctrlColumn === '1') {
            this.$set(this, 'showFacGNo', true)
            this.$set(this.rulesHeader.facGNo[0], 'required', false)
          } else if (val.ctrlColumn === '2') {
            this.$set(this, 'showFacGNo', false)
            this.$set(this.rulesHeader.facGNo[0], 'required', true)
          }
        }
      },
      'frmData.facGNo': function () {
        this.facGNoEnter()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
