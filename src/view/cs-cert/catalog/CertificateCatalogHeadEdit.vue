<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form" style="padding-right: 10px;">
          <XdoFormItem prop="documentName" label="证件名称">
            <XdoIInput type="text" v-model="frmData.documentName" :maxlength="50" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="documentCode" label="自定义代码">
            <XdoIInput type="text" v-model="frmData.documentCode" :maxlength="10" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="supervisionCondition" label="检验监管条件">
            <xdo-select type="text" v-model="frmData.supervisionCondition" :options="this.certificate.INSPECTION_CONDITION" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" :maxlength="50" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="customsCondition" label="通关监管条件">
            <xdo-select v-model="frmData.customsCondition" :options="this.certificate.CLEARANCE_CONDITION" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="iemark" label="进出口标志">
            <xdo-select v-model="frmData.iemark" :options="this.certificate.I_E_MARK" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="insertTime" label="录入时间">
            <XdoDatePicker type="date" disabled v-model="frmData.insertTime" style="width: 100%;"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="timesCtrlFlag" label="是否控次">
            <xdo-select v-model="frmData.timesCtrlFlag" :options="this.certificate.CTRL_FLAG" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="qtyCtrlFlag" label="是否控量">
            <xdo-select v-model="frmData.qtyCtrlFlag" :options="this.certificate.CTRL_FLAG" :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="ctrlColumn" label="管控类型">
            <xdo-select v-model="frmData.ctrlColumn" :options="this.certificate.CTRL_COLUMN_MAP" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="warringPercent" label="预警天数">
            <xdo-input  v-model="frmData.warringDays" number int-length="3" :disabled="showDisable">
              <span slot="prepend">到期前</span>
              <span slot="append">天</span>
            </xdo-input>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { certificateEditBasic } from '../base/certificateEditBasic'

  export default {
    name: 'CertificateCatalogHeadEdit',
    mixins: [certificateEditBasic],
    data() {
      return {
        ajaxUrl: {
          insert: csAPI.cert.catalog.head.rest,
          update: csAPI.cert.catalog.head.rest
        },
        rulesHeader: {
          iemark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          ctrlColumn: [{required: true, message: '不能为空!', trigger: 'blur'}],
          documentName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          documentCode: [{required: true, message: '不能为空!', trigger: 'blur'}]
        }
      }
    },
    methods: {
      getDefaultData() {
        let nowTime = new Date()
        return {
          sid: '',
          documentCode: '',
          documentName: '',
          iemark: '',
          supervisionCondition: '',
          customsCondition: '',
          timesCtrlFlag: '0',
          qtyCtrlFlag: '0',
          insertTime: nowTime.format('yyyy-MM-dd hh:mm:ss'),
          ctrlColumn: '',
          note: '',
          warringDays:null
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
