import { editStatus, certificate } from '@/view/cs-common'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const certificateEditBasic = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      formName: 'dataForm',
      certificate: certificate,
      buttons: [
        {...btnComm, label: '保存', type: 'primary', click: this.handleSave},
        {...btnComm, type: 'warning', label: '保存继续', click: this.handleSaveContinue},
        {...btnComm, label: '返回', type: 'primary', click: this.handleBack}
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        if (val === editStatus.SHOW) {
          this.buttons[0].needed = false
          this.buttons[1].needed = false
          this.$set(this, 'showEdit', true)
        } else {
          this.buttons[0].needed = true
          this.buttons[1].needed = true
          this.$set(this, 'showEdit', false)
        }
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * @param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
      this.buttons[1].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave((res) => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存继续
     */
    handleSaveContinue() {
      let me = this
      me.doSave(() => {
        me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
      })
    }
  }
}
