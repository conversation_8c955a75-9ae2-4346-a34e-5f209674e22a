import pms from '@/libs/pms'
import CommUpload from '@/components/comm-upload/comm-upload'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const certificateListBasic = {
  components: {
    CommUpload
  },
  mixins: [pms, commList],
  data() {
    return {
      importShow: false, // 是否显示导入
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload
      }
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then(() => {
      me.actionLoaded()
    })
  },
  methods: {
    actionLoaded() {
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 下载
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    handleImport() {
      this.importShow = true
    },
    /**
     * 启用禁用逻辑代码
     * @param status
     * @param title
     */
    doIsAble(status, title) {
      let me = this
      if (me.checkRowSelected(title, false)) {
        let certIds = me.getSelectedParams()
        me.$http.post(`${me.ajaxUrl.enableCert}/${certIds}/${status}`).then(res => {
          me.$Message.success(res.data.message)
        }, () => {
        }).finally(() => {
          me.getList()
        })
      }
    },
    /**
     * 启用
     */
    handleEnable() {
      let me = this
      me.doIsAble('1', '启用')
    },
    /**
     * 禁用
     */
    handleDisable() {
      let me = this
      me.doIsAble('0', '禁用')
    }
  }
}
