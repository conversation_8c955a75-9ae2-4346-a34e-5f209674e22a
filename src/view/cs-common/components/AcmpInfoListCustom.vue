<template>
  <section class="xdo-enter-root" style="padding-bottom: 3px;">
    <p style="font-weight: bold; padding: 7px 22px 3px 22px; border-bottom: #dcdee2 solid 1px; margin-bottom: 0;">
      {{title}}
    </p>
    <div v-show="showAction" style="padding: 0; margin: 0;" class="xdo-enter-root" v-focus>
      <XdoForm ref="attachForm" :show-message="false" :model="attachForm">
        <table class="frmTable">
          <tr>
            <td>
              <XdoIInput type="text" disabled :value="fileName" style="width: 100%;"></XdoIInput>
              <input ref="file" v-if="showFileInput" @change="fileChanged" type="file" multiple="multiple" style="display: none;" />
            </td>
            <td style="width: 40px;"><Button @click="handleSelectFile">浏览</Button></td>
            <td style="width: 40px;"><Button type="warning" @click="handleSaveAttach">上传</Button></td>
          </tr>
        </table>
      </XdoForm>
    </div>
    <XdoTable :height="tableHeight" size="small" stripe :columns="grdColumns" :data="dataAcmpSource" border style="margin-top: 2px;"></XdoTable>
    <dc-file-preview-pop :show.sync="filePreview.show" :file-data="filePreview.fileData"></dc-file-preview-pop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { util } from '@/libs'
  import acmpColumns from './csAcmpInfo-columns'

  export default {
    name: 'csAcmpInfoListCustom',
    mixins: [acmpColumns],
    props: {
      sid: {
        type: String,
        require: true
      },
      title: {
        type: String,
        default: '商品资料'
      },
      businessType: {
        type: String,
        default: () => ('2')
      },
      showAction: {
        type: Boolean,
        require: true
      },
      justView: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: '-'
      }
    },
    data() {
      return {
        fileName: '',
        filePreview: {
          show: false,
          fileData: []
        },
        attachForm: {},
        grdColumns: [],
        selectedFile: [],
        dataAcmpSource: [],
        showFileInput: true,
        editForm: {
          acmpNo: '',
          acmpType: '',
          acmpFormat: '',
          businessSid: '',
          billSerialNo: '',
          businessType: this.businessType   // 单证类型：必填，字符型，长度1位（2-手册、3-报核、4－清单、5－质疑
        }
      }
    },
    watch: {
      sid: function (val) {
        let me = this
        me.editForm.businessSid = val
        me.loadData()
      },
      justView: {
        immediate: true,
        handler: function (justView) {
          let me = this
          if (justView === true) {
            me.$set(me, 'grdColumns', me.acmpColumns.viewColumns)
          } else {
            me.$set(me, 'grdColumns', me.acmpColumns.editColumns)
          }
        }
      }
    },
    mounted() {
      let me = this
      if (me.sid) {
        me.editForm.businessSid = me.sid
        me.loadData()
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$set(me, 'showFileInput', false)
        me.$http.post(csAPI.attachedInfo.list, {
          businessSid: me.sid
        }).then(res => {
          me.dataAcmpSource = res.data.data
        }).catch(() => {
        }).finally(() => {
          me.$set(me, 'showFileInput', true)
        })
      },
      fileChanged(e) {
        let me = this
        if (e.target.files && e.target.files.length > 0) {
          me.selectedFile = e.target.files
          if (me.selectedFile) {
            let fileNames = ''
            for (let i = 0; i < me.selectedFile.length; i++) {
              fileNames += me.selectedFile[i].name + '; '
            }
            me.fileName = fileNames
          } else {
            me.fileName = ''
          }
        }
      },
      handleSelectFile() {
        let me = this
        if (me.$refs['file']) {
          const event = new MouseEvent('click')
          me.$refs['file'].dispatchEvent(event)
        }
      },
      fileClear() {
        let me = this
        me.$set(me, 'fileName', '')
        me.$set(me, 'selectedFile', [])
      },
      handleSaveAttach() {
        let me = this
        if (me.selectedFile && me.selectedFile.length > 0) {
          const fd = new FormData()
          for (let i = 0; i < me.selectedFile.length; i++) {
            let fileName = me.selectedFile[i].name
            if (fileName.indexOf('.') > -1) {
              let suffix = fileName.substring(fileName.lastIndexOf('.') + 1)
              if (!['exe', 'com', 'pif', 'bat', 'scr'].includes(suffix)) {
                fd.append('file', me.selectedFile[i])
              }
            }
          }
          for (let val in me.editForm) {
            if (me.editForm.hasOwnProperty(val)) {
              fd.append(val, me.editForm[val])
            }
          }
          me.$http.post(csAPI.attachedInfo.insert, fd, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }).then(() => {
            me.loadData()
            me.fileClear()
            me.$Message.success('上传成功')
            me.$refs['attachForm'].resetFields()
          }).catch(() => {
          })
        } else {
          me.$Message.error('请选择上传的文件')
        }
      },
      removeAttach(params) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此文件吗?',
          onOk: () => {
            me.$http.delete(csAPI.attachedInfo.delete + '/' + params.row.sid).then(() => {
              me.loadData()
              me.fileClear()
              me.$Message.success('删除成功!')
              me.$refs['attachForm'].resetFields()
            }).catch(() => {
            })
          }
        })
      },
      downloadFile(sysId) {
        let me = this
        me.$http.get(csAPI.attachedInfo.get + '/' + sysId, {
          responseType: 'blob'
        }).then(res => {
          // 下载
          const name = util.getHttpHeaderFileName(res.headers)
          // const blob = new Blob([res.data], {type: 'application/octet-stream'})
          // util.blobSaveFile(blob, name)

          // 预览
          me.$set(me.filePreview, 'show', true)
          me.$set(me.filePreview, 'fileData', [{
            sid: sysId,
            originFileName: name
          }])
        })
      }
    },
    computed: {
      tableHeight() {
        let me = this
        if (typeof me.height === 'number') {
          if (me.showAction) {
            return me.height - 28
          } else {
            return me.height
          }
        }
        return '-'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-tip {
    min-height: 100px;
  }

  .frmTable {
    margin: 0;
    width: 100%;
    display: table;
    padding: 1px 0;
    border-color: grey;
    border-spacing: 1px;
    border-collapse: separate;
  }

  .frmTable tr, .frmTable td {
    margin: 0;
    padding: 0;
    width: 100%;
  }
</style>
