<template>
  <XdoFormItem :label="label">
    <XdoRow v-for="item in attachInfo.updatedFiles" :key="item.fileSid">
      <XdoCol span="18"><a @click="fileDownload(item.fileSid, item.fileName)">{{ item.fileName }}</a></XdoCol>
      <XdoCol span="6">
        <XdoIcon size="16" type="md-close-circle" color="red" @click="removeFile(item.fileSid)" v-show="isShow"/>
      </XdoCol>
    </XdoRow>
    <XdoRow v-show="isShow">
      <XdoCol span="24">
        <Upload :action="attachInfo.uploadUrl" :headers="attachInfo.headers" :data="attachInfo.data"
                multiple :show-upload-list="false" :before-upload="handleBeforeUpload"
                :on-error="handleOnUploadError" :on-success="handleOnUploadSuccess">
          <XdoButton type="primary" icon="md-cloud-upload">
            选择文件
          </XdoButton>
        </Upload>
      </XdoCol>
    </XdoRow>
  </XdoFormItem>
</template>

<script>
  import { csAPI } from '@/api'
  import { util } from '@/libs'

  export default {
    name: 'SimpleAttachment',
    props: {
      label: {
        type: String,
        default: () => ('附件信息')
      },
      // 业务类型标识
      businessType: {
        type: String,
        default: ''
      },
      // 业务单证Sid
      businessSid: {
        type: String,
        default: ''
      },
      aeoShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        attachInfo: {
          updatedFiles: [],
          data: {
            acmpNo: '',
            acmpType: '',
            acmpFormat: '',
            businessSid: this.businessSid,
            businessType: this.businessType
          },
          uploadUrl: csAPI.attachedInfo.insert,
          headers: {
            Authorization: 'Bearer ' + this.$store.state.token
          }
        }
      }
    },
    watch: {
      businessSid: function (newValue) {
        let me = this
        me.attachInfo.data.businessSid = newValue
        me.loadFile()
      }
    },
    methods: {
      loadFile() {
        let me = this
        me.$http.post(csAPI.attachedInfo.list, {
          businessSid: me.businessSid,
          businessType: me.businessType
        }).then(res => {
          me.attachInfo.updatedFiles = res.data.data.map(item => {
            return {
              fileSid: item.sid,
              fileName: item.originFileName
            }
          })
        }).catch(() => {
        })
      },
      removeFile(fileSid) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            me.$http.delete(`${csAPI.attachedInfo.delete}/${fileSid}`).then(() => {
              me.$Message.success('删除成功!')
              me.attachInfo.updatedFiles.splice(me.attachInfo.updatedFiles.findIndex(item => item.fileSid === fileSid), 1)
            }).catch(() => {
            })
          }
        })
      },
      fileDownload(fileSid, fileName) {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.get}/${fileSid}`, {
          responseType: 'blob'
        }).then(res => {
          const name = fileName
          const blob = new Blob([res.data])
          util.blobSaveFile(blob, name)
        })
      },
      /**
       * 文件校验
       * @param file
       */
      handleBeforeUpload(file) {
        let me = this
        if (file) {
          if (file.size > 10240000) {
            me.$Message.warning('文件 ' + file.name + ' 太大，不能超过 10M.')
            return false
          }
        }
        me.$emit('uploadStatus', {
          uploadFailure: false,
          uploadCompleted: false,
          uploadProgressShow: true
        })
      },
      handleOnUploadError() {
        let me = this
        me.$nextTick(() => {
          me.$emit('uploadStatus', {
            uploadFailure: true,
            uploadCompleted: false,
            uploadProgressShow: true
          })
        })
      },
      handleOnUploadSuccess(response, file) {
        let me = this
        if (response.success) {
          me.attachInfo.updatedFiles.push({
            fileName: file.name,
            fileSid: response.data.sid
          })
          me.$emit('uploadStatus', {
            uploadFailure: false,
            uploadCompleted: true,
            uploadProgressShow: true
          })
        } else {
          me.$nextTick(() => {
            me.$emit('uploadStatus', {
              uploadFailure: true,
              uploadCompleted: false,
              uploadProgressShow: true
            })
          })
          me.$Message.error(response.message)
        }
      }
    },
    computed: {
      isShow() {
        return !this.aeoShow
      }
    }
  }
</script>

<style scoped>
</style>
