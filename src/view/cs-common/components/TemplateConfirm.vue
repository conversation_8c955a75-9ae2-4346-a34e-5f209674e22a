<template>
  <XdoModal width="400" mask title="保存为模板" v-model="show"
            :closable="false" footer-hide :mask-closable="false">
    <div class="header">
      <XdoForm ref="frmTemplate" class="dc-form" :model="templateData" :rules="templateRules" label-position="right" :label-width="80" inline>
        <XdoFormItem prop="templateName" class="dc-merge-1-4" label="模板名称">
          <XdoIInput type="text" v-model="templateData.templateName" placeholder="请输入模板名称..." clearable></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </div>
    <div class="action" style="text-align: right;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.label">{{item.label}}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    data() {
      return {
        templateData: {
          templateName: ''
        },
        templateRules: {
          templateName: [{ required: true, message: '请输入模板名称' }]
        },
        buttons: [{
          needed: true,
          label: '取消',
          loading: false,
          type: 'default',
          disabled: false,
          icon: 'dc-btn-Cancel',
          click: this.handleCancel
        }, {
          needed: true,
          label: '确定',
          loading: false,
          type: 'primary',
          disabled: false,
          icon: 'dc-btn-confirm',
          click: this.handleConfirm
        }]
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    methods: {
      handleCancel() {
        let me = this
        me.$set(me.templateData, 'templateName', '')
        me.$refs.frmTemplate.resetFields()
        me.$emit('confirm:success', '')
      },
      handleConfirm() {
        let me = this
        me.$refs['frmTemplate'].validate().then(isValid => {
          if (isValid) {
            me.$emit('confirm:success', me.templateData.templateName)
            me.$set(me.templateData, 'templateName', '')
          }
        })
      }
    }
  }
</script>

<style lang="less">
</style>
