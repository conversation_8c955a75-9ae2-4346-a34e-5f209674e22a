<template>
  <section class="dc-form dc-form-inner">
    <XdoFormItem :prop="fields.mark.field" :ref="fields.mark.frmItem">
      <xdo-select v-model="data.mark" :options="this.cmbSource.markData" @on-change="onChange"
                  :optionLabelRender="pcodeRender" :disabled="fields.disabled"></xdo-select>
    </XdoFormItem>
    <XdoFormItem :prop="fields.rate.field" :ref="fields.rate.frmItem">
      <xdo-input v-model="data.rate" decimal int-length="10" precision="5" @on-change="onChange" :disabled="fields.disabled"></xdo-input>
    </XdoFormItem>
    <XdoFormItem :prop="fields.curr.field" :ref="fields.curr.frmItem">
      <xdo-select v-if="data.mark !== '1'" v-model="data.curr" :asyncOptions="pcodeList" :meta="pcode.curr_outdated"
                  :optionLabelRender="pcodeRender" :disabled="fields.disabled" @on-change="onChange"></xdo-select>
      <span v-else style="padding: 2px 0 0 5px;">%</span>
    </XdoFormItem>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { importExportManage } from '@/view/cs-common'

  export default {
    name: 'feeModule',
    props: {
      fields: {
        type: Object,
        require: true,
        default: () => ({
          mark: {
            field: '',
            frmItem: '',
            value: ''
          },
          rate: {
            field: '',
            frmItem: '',
            value: null
          },
          curr: {
            field: '',
            frmItem: '',
            value: ''
          },
          disabled: false
        })
      }
    },
    data() {
      return {
        data: {
          mark: '',
          rate: null,
          curr: ''
        },
        cmbSource: {
          markData: importExportManage.feeTypeMap
        }
      }
    },
    watch: {
      fields: {
        deep: true,
        immediate: true,
        handler: function (val) {
          let me = this
          me.$set(me.data, 'mark', val.mark.value)
          me.$set(me.data, 'rate', val.rate.value)
          me.$set(me.data, 'curr', val.curr.value)
        }
      }
    },
    methods: {
      onChange() {
        // 获取当前值组
        let me = this,
          data = {},
          rules = {}
        if (isNullOrEmpty(me.data.mark)) {
          me.$set(me.data, 'rate', null)
          me.$set(me.data, 'curr', '')
          rules[me.fields.rate.field] = []
          rules[me.fields.curr.field] = []
        } else {
          rules[me.fields.rate.field] = [{required: true, message: '不能为空！', trigger: 'blur'}]
          if (me.data.mark === '1') {
            me.$set(me.data, 'curr', '')
            rules[me.fields.curr.field] = []
          } else {
            rules[me.fields.curr.field] = [{required: true, message: '不能为空！', trigger: 'blur'}]
          }
        }
        data[me.fields.mark.field] = me.data.mark === null ? '' : me.data.mark
        data[me.fields.rate.field] = me.data.rate
        data[me.fields.curr.field] = me.data.curr === null ? '' : me.data.curr
        // 重置验证信息
        me.$refs[me.fields.mark.frmItem].setRules()
        me.$refs[me.fields.rate.frmItem].setRules()
        me.$refs[me.fields.curr.frmItem].setRules()
        // 值传递
        me.$emit('onFeeValChanged', data, rules)
      }
    }
  }
</script>

<style scoped>
  .dc-form-inner {
    padding-top: 0 !important;
    grid-column-gap: 0 !important;
  }
</style>
