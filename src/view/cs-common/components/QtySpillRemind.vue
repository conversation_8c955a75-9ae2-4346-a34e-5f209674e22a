<template>
  <section>
    <gridView ref="grdView" :limit="10" :tableHeight="300"
              :dataSource="grdConfig.data" :columns="grdConfig.columns" :dataTotal="grdConfig.dataTotal"
              @onPageChange="onPageChange">
      <div class="action" slot="actionBottomSlot">
        <XdoButton type="primary" icon="dc-btn-cancel" key="cancel" @click="handleBack">关闭</XdoButton>
        <XdoButton type="primary" icon="ios-cloud-download" key="export" @click="onExport" :loading="frmState.loading.export">导出</XdoButton>
      </div>
    </gridView>
  </section>
</template>

<script>
  import { csAPI, excelExport } from '@/api'
  import gridView from '@/components/gridView/gridView'

  export default {
    name: 'QtySpillRemind',
    components: {
      gridView
    },
    props: {
      sid: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        frmState: {
          loading: {
            export: false
          }
        },
        grdConfig: {
          data: [],
          dataTotal: -1,
          columns: [{
            key: 'emsNo',
            title: '备案号'
          }, {
            key: 'gno',
            title: '备案序号'
          }, {
            key: 'overValue',
            title: '超出数量'
          }]
        },
        searchParams: {
          erpHeadId: this.sid
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        }
      }
    },
    methods: {
      onPageChange(grdParam) {
        let me = this
        me.getDataList(grdParam)
      },
      handleBack() {
        let me = this
        me.$emit('qtySpillRemind:Close')
      },
      onSearch() {
        let me = this
        me.$nextTick(() => {
          const grdParam = me.$refs.grdView.gridParam()
          me.getDataList({
            dataTotal: -1,
            limit: grdParam.limit,
            pageIndex: grdParam.pageIndex
          })
        })
      },
      getDataList(grdParam) {
        let me = this
        me.pageParam.limit = grdParam.limit
        me.pageParam.page = grdParam.pageIndex
        me.$http.post(csAPI.csImportExport.qtySpillRemind.selectAllPaged, me.searchParams, {
          params: me.pageParam
        }).then(res => {
          me.grdConfig.data = res.data.data
          me.grdConfig.dataTotal = res.data.total
        }).catch(() => {
        })
      },
      onExport() {
        let me = this,
          columns = []
        me.grdConfig.columns.forEach(item => {
          item.key && columns.push({key: item.key, value: item.title})
        })
        excelExport(csAPI.csImportExport.qtySpillRemind.export, {
          header: columns,
          name: '超出的备案数量',
          exportColumns: me.searchParams
        })
      }
    }
  }
</script>

<style scoped>
  .action {
    text-align: center;
  }
</style>
