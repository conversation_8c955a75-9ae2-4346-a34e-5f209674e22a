export default {
  data() {
    return {
      acmpColumns: {
        editColumns: [{
          title: '文件名',
          ellipsis: true,
          tooltip: true,
          align: 'left',
          render: (h, {row}) => {
            return h('a', {
              on: {
                click: () => {
                  this.downloadFile(row.sid)
                }
              }
            }, row.originFileName)
          }
        }, {
          width: 90,
          title: '上传时间',
          align: 'center',
          key: 'insertTime'
        }, {
          width: 58,
          title: '操作',
          key: 'action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.removeAttach(params)
                  }
                }
              }, '删除')
            ]);
          }
        }],
        viewColumns: [{
          title: '文件名',
          ellipsis: true,
          tooltip: true,
          align: 'left',
          render: (h, {row}) => {
            return h('a', {
              on: {
                click: () => {
                  this.downloadFile(row.sid)
                }
              }
            }, row.originFileName)
          }
        }, {
          width: 90,
          title: '上传时间',
          align: 'center',
          key: 'insertTime'
        }]
      }
    }
  }
}
