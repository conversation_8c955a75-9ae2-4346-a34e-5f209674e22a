<template>
  <section>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="onSearch">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div v-show="showSearch" class="xdo-enter-root" v-focus>
          <div class="separateLine"></div>
          <XdoForm class="dc-form" :model="searchParams" label-position="right" :label-width="100" inline>
            <XdoFormItem prop="tempName" label="模板名称" style="margin-bottom: 0;">
              <XdoIInput type="text" v-model="searchParams.tempName" clearable></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="lister" label="制单员">
              <xdo-select v-model="searchParams.lister" :options="this.cmbSource.listerData"></xdo-select>
            </XdoFormItem>
          </XdoForm>
        </div>
      </div>
    </XdoCard>
    <gridView ref="grdView" :limit="10" :tableHeight="dynamicHeight" :dataSource="grdConfig.data"
              :columns="grdConfig.columns" :dataTotal="grdConfig.dataTotal"
              @onRowDblClick="onRowDblClick" @onPageChange="onPageChange">
      <div class="action" slot="actionBottomSlot">
        <XdoButton type="primary" @click="choiceTemplate">确认</XdoButton>
        <XdoButton icon="ios-trash" type="error" @click="deleteTemplate" key="delete">删除</XdoButton>
        <XdoButton type="primary" icon="dc-btn-cancel" key="cancel" @click="handleBack">返回</XdoButton>
      </div>
    </gridView>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { dynamicHeight } from '@/common'
  import { formatDate } from '@/libs/datetime'
  import gridView from '@/components/gridView/gridView'

  export default {
    name: 'SelectTemplate',
    components: {
      gridView
    },
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    mixins: [dynamicHeight],
    data() {
      return {
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        cmbSource: {
          listerData: []
        },
        showSearch: false,
        searchParams: {
          tempName: '',
          iemark: this.iemark,
          lister: this.$store.state.user.userNo
        },
        grdConfig: {
          data: [],
          dataTotal: -1,
          columns: [{
            width: 34,
            key: 'selection',
            type: 'selection'
          }, {
            key: 'tempName',
            title: '模板名称'
          }, {
            title: '制单员',
            key: 'userName'
          }, {
            title: '制单日期',
            key: 'insertTime',
            render: (h, params) => {
              return h('span', formatDate(params.row.insertTime, 'yyyy-MM-dd'))
            }
          }]
        }
      }
    },
    created() {
      let me = this
      me.$http.post(csAPI.csImportExport.template.selectInsertUser + '/' + me.iemark + '/' + '2').then(res => {
        if (res.data.data.length > 0) {
          me.$set(me.cmbSource, 'listerData', res.data.data.map(item => {
            return {
              value: item['INSERT_USER'],
              label: item['INSERT_USER']
            }
          }))
        } else {
          me.searchParams.lister = ''
        }
      }).catch(() => {
        me.$set(me.cmbSource, 'listerData', [])
        me.searchParams.lister = ''
      })
    },
    mounted: function() {
      let me = this
      me.refreshDynamicHeight(120, !me.showSearch ? ['area_search'] : null)
      me.onSearch()
    },
    methods: {
      onRowDblClick(grdParam) {
        let me = this
        me.$emit('selectTemplate:success', grdParam.selectRows[0])
        me.handleBack()
      },
      handleBack() {
        let me = this
        me.$emit('selectTemplate:close')
      },
      onPageChange(grdParam) {
        let me = this
        me.getDataList(grdParam)
      },
      getSelectRow() {
        let me = this
        return me.$refs.grdView.gridParam().selectRows
      },
      saveTemplate(entity, templateName, callback) {
        let me = this
        if (entity.hasOwnProperty('tempName')) {
          entity.tempName = templateName
        } else {
          me.$set(entity, 'tempName', templateName)
        }
        !entity.hasOwnProperty('iemark') && me.$set(entity, 'iemark', me.iemark)
        me.$http.post(csAPI.csImportExport.template.insert, entity).then(res => {
          callback(res.data)
        }).catch(() => {
        })
      },
      choiceTemplate() {
        let me = this
        const selectRows = me.$refs.grdView.gridParam().selectRows
        if (selectRows.length <= 0) {
          me.$Message.warning('请选要使用的模板')
        } else if (selectRows.length > 1) {
          me.$Message.warning('一次只能选择一个模版')
        } else {
          me.$emit('selectTemplate:success', selectRows[0])
          me.handleBack()
        }
      },
      deleteTemplate() {
        let me = this
        const selectRows = me.$refs.grdView.gridParam().selectRows
        if (selectRows.length <= 0) {
          me.$Message.warning('请选要删除的模板')
          return
        }
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '请确认是否删除所选模板',
          onOk: () => {
            const sids = selectRows.map(item => {
              return item.sid
            })
            me.$http.delete(`${csAPI.csImportExport.template.delete}/${sids}`).then(() => {
              me.$Message.success('删除成功!')
              me.onSearch()
            }).catch(() => {
            })
          }
        })
      },
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(120, !me.showSearch ? ['area_search'] : null)
      },
      onSearch() {
        let me = this
        me.$nextTick(() => {
          const grdParam = me.$refs.grdView.gridParam()
          me.$refs.grdView.grdConfig.pageIndex = 1
          me.getDataList({
            pageIndex: 1,
            dataTotal: -1,
            limit: grdParam.limit
          })
        })
      },
      getDataList(grdParam) {
        let me = this
        me.pageParam.limit = grdParam.limit
        me.pageParam.page = grdParam.pageIndex
        const data = me.searchParams
        me.$http.post(csAPI.csImportExport.template.selectAllPaged, data, {
          params: me.pageParam
        }).then(res => {
          me.grdConfig.data = res.data.data
          me.grdConfig.dataTotal = res.data.total
        }).catch(() => {
        })
      }
    }
  }
</script>

<style scoped>
  .action {
    text-align: center;
  }
</style>
