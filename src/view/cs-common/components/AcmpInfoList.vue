<template>
  <section class="xdo-enter-root">
    <p>商品资料:</p>
    <div v-show="isShow" class="xdo-enter-root" v-focus>
      <XdoForm class="dc-form" ref="attachForm" :show-message="false" :model="attachForm" :label-width="120">
        <XdoFormItem class="dc-merge-1-3">
          <div style="float: left; width: 300px;">
            <XdoIInput type="text" disabled :value="fileName"></XdoIInput>
            <input ref="file" v-if="showFileInput" @change="fileChanged" type="file" style="display: none;"/>
          </div>
          <div style="float: left;">
            <XdoButton class="dc-margin-left" @click="handleSelectFile">浏览</XdoButton>
          </div>
          <div style="float: left;">
            <XdoButton class="dc-margin-left" type="warning" @click="handleSaveAttach">上传</XdoButton>
          </div>
        </XdoFormItem>
      </XdoForm>
    </div>
    <XdoTable ref="aeoTable" size="small" stripe :columns="grdColumns" :data="dataAcmpSource" border></XdoTable>
    <br />
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { util } from '@/libs'
  import acmpColumns from './csAcmpInfo-columns'

  export default {
    name: 'csAcmpInfoList',
    mixins: [acmpColumns],
    props: {
      sid: {
        type: String,
        require: true
      },
      showAction: {
        type: Boolean,
        require: true
      },
      justView: {
        type: Boolean,
        default: false
      },
      businessType: {
        type: String,
        default: () => ('2')
      }
    },
    data() {
      return {
        isShow: true,
        fileName: '',
        attachForm: {},
        grdColumns: [],
        dataAcmpSource: [],
        selectedFile: null,
        showFileInput: true,
        editForm: {
          acmpNo: '',
          acmpType: '',
          acmpFormat: '',
          businessSid: '',
          billSerialNo: '',
          businessType: this.businessType  // 单证类型：必填，字符型，长度1位（2-手册、3-报核、4－清单、5－质疑
        }
      }
    },
    watch: {
      sid: function (val) {
        let me = this
        me.editForm.businessSid = val
        me.loadData()
      }
    },
    mounted() {
      let me = this
      if (me.sid) {
        me.editForm.businessSid = me.sid
        me.loadData()
      }
      me.isShow = me.showAction
      if (me.justView === true) {
        me.$set(me, 'grdColumns', me.acmpColumns.viewColumns)
      } else {
        me.$set(me, 'grdColumns', me.acmpColumns.editColumns)
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$set(me, 'showFileInput', false)
        me.$http.post(csAPI.attachedInfo.list, {
          businessSid: me.sid
        }).then(res => {
          me.dataAcmpSource = res.data.data
        }).catch(() => {
        }).finally(() => {
          me.$set(me, 'showFileInput', true)
        })
      },
      fileChanged(e) {
        let me = this
        if (e.target.files[0] === undefined)
          return
        me.selectedFile = e.target.files[0]
        me.fileName = me.selectedFile.name
      },
      handleSelectFile() {
        let me = this
        if (me.$refs['file']) {
          const event = new MouseEvent('click')
          me.$refs['file'].dispatchEvent(event)
        }
      },
      handleSaveAttach() {
        let me = this
        if (!me.selectedFile) {
          me.$Message.error('请选择上传的随附单据')
          return
        }

        const fd = new FormData()
        fd.append('file', me.selectedFile)
        for (let val in me.editForm) {
          fd.append(val, me.editForm[val])
        }

        const url = csAPI.attachedInfo.insert
        const options = {headers: {'Content-Type': 'multipart/form-data'}}
        me.$http.post(url, fd, options).then(() => {
          me.loadData()
          me.$Message.success('上传成功')
          me.$refs['attachForm'].resetFields()
          me.fileName = ''
          me.selectedFile = null
        }).catch(() => {
        })
      },
      removeAttach(params) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此随附单据吗?',
          onOk: () => {
            const sids = params.row.sid
            me.$http.delete(`${csAPI.attachedInfo.delete}/${sids}`).then(() => {
              me.$Message.success('删除成功!')
              me.loadData()
            }).catch(() => {
            })
          }
        })
      },
      downloadFile(sysId) {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.get}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          const name = util.getHttpHeaderFileName(res.headers)
          const blob = new Blob([res.data], {type: 'application/octet-stream'})
          util.blobSaveFile(blob, name)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-tip {
    min-height: 100px;
  }
</style>
