<template>
  <section>
    <XdoCard :bordered="false">
      <p v-if="showTitle">内审情况:</p>
      <XdoTable ref="aeoTable" size="small" stripe :columns="aeoColumns" :data="dataAeoSource" border></XdoTable>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import aeoColumns from './csAeoInfo-columns'

  export default {
    name: 'csAeoInfoList',
    mixins: [aeoColumns],
    data() {
      return {
        dataAeoSource: []
      }
    },
    props: {
      sid: {
        type: String,
        require: true
      },
      showTitle: {
        type: Boolean,
        default: true
      },
      apprUserTitle: {
        type: String,
        default: '操作人'
      },
      /**
       * 特殊地址
       */
      specialUrl: {
        type: String,
        default: () => ('')
      }
    },
    watch: {
      sid: function (val) {
        let me = this
        me.getAeoData(val)
      }
    },
    mounted() {
      let me = this
      if (me.sid) {
        me.getAeoData(me.sid)
      }
    },
    methods: {
      getAeoData(sid) {
        let me = this,
          url = csAPI.aeoManage.aeoReview.actions.selectListBySid
        if (!isNullOrEmpty(me.specialUrl)) {
          url = me.specialUrl
        }
        me.$http.post(url, {
          businessSid: sid
        }).then(res => {
          if (!isNullOrEmpty(me.specialUrl)) {
            me.$set(me, 'dataAeoSource', res.data.data)
          } else {
            me.$set(me, 'dataAeoSource', res.data)
          }
        }).catch(() => {
          me.$set(me, 'dataAeoSource', [])
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-tip {
    min-height: 100px;
  }
</style>
