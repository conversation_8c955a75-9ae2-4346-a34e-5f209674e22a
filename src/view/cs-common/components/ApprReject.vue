<template>
  <XdoModal v-model="show" ref="theModel" width="700" :title="title"
            :mask-closable="false" footer-hide :closable="false">
    <XdoForm ref="frmReview" class="dc-form" :model="reviewData" :rules="rulesReview" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="auditNote" class="dc-merge-1-4" label="内审意见">
        <XdoIInput type="textarea" v-model="reviewData.auditNote" clearable :maxlength="127"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
    <div class="action" style="text-align: center;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{item.label}}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'ApprReject',
    props: {
      show: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: ''
      },
      batchSid: {
        type: Array,
        default: () => ([])
      },
      businessSid: {
        type: String,
        default: ''
      },
      apprType: {
        type: String,
        default: ''
      },
      apprResult: {
        type: Boolean,
        default: false
      },
      searchData: {
        type: Object,
        default: () => ([])
      },
      returnReason: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        reviewData: {
          auditNote: ''
        },
        rulesReview: {
          auditNote: [{required: true, message: '请先维护内审意见', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, label: '确定', type: 'error', icon: 'dc-btn-refuse', click: this.handleOk
        }, {
          ...btnComm, label: '取消', type: 'primary', icon: 'dc-btn-cancel', click: this.handleCancel
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              let modelChildren = me.$refs['theModel'].$el.childNodes
              let item
              for (let i = 0; i < modelChildren.length; i++) {
                item = modelChildren[i]
                if (!isNullOrEmpty(item.className)) {
                  if (item.className.indexOf('ivu-modal-mask') > -1 || item.className.indexOf('ivu-modal-wrap') > -1) {
                    item.style.zIndex = '1000'
                  }
                }
              }
              if (!isNullOrEmpty(me.returnReason)) {
                let returnReason = me.returnReason.trim()
                if (returnReason.length > 127) {
                  returnReason = returnReason.substring(0, 124) + '...'
                }
                me.$set(me.reviewData, 'auditNote', returnReason)
              } else {
                me.$set(me.reviewData, 'auditNote', '')
              }
            })
          }
        }
      }
    },
    methods: {
      handleOk() {
        let me = this
        me.$refs['frmReview'].validate().then(isValid => {
          if (isValid) {
            if (me.apprResult) {
              /**
               * 通过审核
               */
              if (!isNullOrEmpty(me.businessSid)) {
                /**
                 * 单条数据
                 */
                console.log(me.buttons[0])
                me.buttons[0].loading = true
                me.$http.post(csAPI.aeoManage.aeoReview.actions.auditData, {
                  apprType: me.apprType,
                  businessSid: me.businessSid,
                  apprNote: me.reviewData.auditNote
                }).then(() => {
                  me.$set(me.reviewData, 'auditNote', '')
                  me.$Message.success('内审通过成功!')
                  me.$emit('return:success', true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[0].loading = false
                })
              } else if (Array.isArray(me.batchSid) && me.batchSid.length > 0) {
                /**
                 * 批量操作
                 */
                let bitchDatas = []
                for (let item of me.batchSid) {
                  bitchDatas.push({
                    businessSid: item.sid,
                    apprType: item.apprType,
                    apprNote: me.reviewData.auditNote
                  })
                }
                me.buttons[0].loading = true
                me.$http.post(csAPI.aeoManage.aeoReview.actions.auditDataM, bitchDatas).then(() => {
                  me.$set(me.reviewData, 'auditNote', '')
                  me.$Message.success('内审通过成功!')
                  me.$emit('return:success', true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[0].loading = false
                })
              } else {
                me.$Message.error('未设置需要审核的信息!')
              }
            } else {
              /**
               * 审核退回
               */
              if (!isNullOrEmpty(me.businessSid)) {
                /**
                 * 单条数据
                 */
                me.buttons[0].loading = true
                me.$http.post(csAPI.aeoManage.aeoReview.actions.returnData, {
                  apprType: me.apprType,
                  businessSid: me.businessSid,
                  apprNote: me.reviewData.auditNote
                }).then(() => {
                  me.$set(me.reviewData, 'auditNote', '')
                  me.$Message.success('内审退回成功!')
                  me.$emit('return:success', true, true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[0].loading = false
                })
              } else if (Array.isArray(me.batchSid) && me.batchSid.length > 0) {
                /**
                 * 批量操作
                 */
                let bitchDatas = []
                for (let item of me.batchSid) {
                  bitchDatas.push({
                    businessSid: item.sid,
                    apprType: item.apprType,
                    apprNote: me.reviewData.auditNote
                  })
                }
                me.buttons[0].loading = true
                me.$http.post(csAPI.aeoManage.aeoReview.actions.returnDataM, bitchDatas).then(() => {
                  me.$set(me.reviewData, 'auditNote', '')
                  me.$Message.success('内审退回成功!')
                  me.$emit('return:success', true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[0].loading = false
                })
              } else {
                let bitchDatas = Object.assign({}, me.searchData, {apprNote: me.reviewData.auditNote})
                let url = ''
                me.buttons[0].loading = true
                if (me.apprType === 'Y') {
                  url = csAPI.aeoManage.aeoReview.actions.returnDataByListM
                } else if (me.apprType === 'O') {
                  url = csAPI.aeoManage.aeoReview.actions.returnDataByListP
                }
                me.$http.post(url, bitchDatas).then(() => {
                  me.$set(me.reviewData, 'auditNote', '')
                  me.$Message.success('内审退回成功!')
                  me.$emit('return:success', true)
                }).catch(() => {
                }).finally(() => {
                  me.buttons[0].loading = false
                })
              }
            }
          }
        })
      },
      handleCancel() {
        let me = this
        me.$refs['frmReview'].resetFields()
        me.$set(me.reviewData, 'auditNote', '')
        me.$emit('return:success', false)
      }
    }
  }
</script>

<style scoped>
</style>
