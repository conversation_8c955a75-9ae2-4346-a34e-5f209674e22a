<template>
  <XdoModal width="1024" mask v-model="show" title="报关单申报异常信息"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard class="ieLogisticsTrackingCard">
      <XdoIInput v-model="errMsg" :rows="5" type="textarea" disabled></XdoIInput>
    </XdoCard>
  </XdoModal>
</template>

<script>
  export default {
    data() {
      return {
        errMsg: ''
      }
    },
    watch: {
      show(value) {
        if (value) {
          this.loadData()
        }
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        require: true
      },
      url: {
        type: String,
        require: true
      },
      taskId: {
        type: String,
        require: true
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$http.get(me.url + '/' + me.headId + '/' + me.taskId).then(res => {
          me.errMsg = res.data.data
        }).catch(() => {
        })
      },
      handleClose() {
        let me = this
        me.$emit('errMessage:hide')
      },
      rowClassName() {
        return 'myRow'
      }
    }
  }
</script>

<style lang="less">
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }
</style>
