<template>
  <XdoModal width="1024" mask v-model="show" title="清单申报异常信息"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard class="ieLogisticsTrackingCard">
      <XdoForm :label-width="100" class="model-form">
        <XdoFormItem label="表头错误信息">
          <XdoIInput v-model="head.tempRemark" :rows="5" type="textarea" disabled></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="content">
      <XdoTable :columns="list.column" :height="355" :data="list.data" :stripe="true" :row-class-name="rowClassName"></XdoTable>
      <div class="page">
        <XdoPage :total="list.total" show-total @on-change="onPageChange"/>
      </div>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    data() {
      return {
        head: {
          tempRemark: ''
        },
        list: {
          page: 1,
          limit: 10,
          total: -1,
          data: [],
          full: [],
          column: [{
            title: '料号',
            key: 'copGNo',
            align: 'center',
            width: 120
          }, {
            title: '备案序号',
            key: 'gno',
            align: 'center',
            width: 80
          }, {
            title: '规格型号',
            key: 'gmodel',
            align: 'center',
            width: 200,
            ellipsis: true,
            tooltip: true
          }, {
            title: '申报数量',
            key: 'qty',
            align: 'center',
            width: 100
          }, {
            title: '申报单价',
            key: 'decPrice',
            align: 'center',
            width: 100
          }, {
            title: '申报总价',
            key: 'decTotal',
            align: 'center',
            width: 100
          }, {
            title: '错误信息',
            key: 'tempRemark',
            align: 'center',
            ellipsis: true,
            tooltip: true
          }]
        }
      }
    },
    watch: {
      show(value) {
        if (value) {
          this.loadData()
        }
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        require: true
      },
      url: {
        type: String,
        require: true
      },
      taskId: {
        type: String,
        require: true
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$http.get(me.url + '/' + me.headId + '/' + me.taskId).then(res => {
          let resultData = {
            invthead: {
              tempMark: ''
            },
            invtlists: []
          }
          if (typeof res.data.data === 'string' && res.data.data.trim().length > 0) {
            resultData = JSON.parse(res.data.data)
          }
          if (resultData.invthead) {
            me.$set(me.head, 'tempRemark', '调用金二核注清单接口成功，但返回信息中存在错误信息：'+resultData.invthead.tempRemark)
          } else {
            me.$set(me.head, 'tempRemark', '')
          }
          me.dataDealWith(resultData.invtlists)
        }).catch(() => {
        })
      },
      dataDealWith(data) {
        let me = this,
          pageInfo,
          count = 0,
          pageData = []
        for (let item of data) {
          if (count % me.list.limit === 0) {
            if (pageInfo !== undefined) {
              pageData.push(pageInfo)
            }
            pageInfo = {
              data: [],
              pageIndex: ((count / me.list.limit) + 1)
            }
          }
          pageInfo.data.push(item)
          count++
        }
        if (count % me.list.limit > 0 || (pageInfo !== undefined && pageData.length === 0)) {
          pageData.push(pageInfo)
        }
        me.list.total = count
        me.list.full = pageData
        me.onPageChange(1)
      },
      onPageChange(page) {
        let me = this
        if (!(me.list.full && me.list.full.length >= page)) {
          page = 1
        }
        me.list.page = page
        if (me.list.full.length > 0) {
          me.list.data = me.list.full[page - 1].data
        } else {
          me.list.data = []
        }
      },
      handleClose() {
        let me = this
        me.list.total = -1
        me.list.data = []
        me.list.full = []
        me.$emit('errMessage:hide')
      },
      rowClassName() {
        return 'myRow'
      }
    }
  }
</script>

<style lang="less">
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }
</style>
