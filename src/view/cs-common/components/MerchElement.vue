<template>
  <XdoModal v-model="getTyShow" width="680" title="规范申报"
            :mask-closable="false" footer-hide :closable="false">
    <XdoForm class="dc-form-2" label-position="right" :label-width="90">
      <FormItem label="商品编码">
        <XdoIInput v-model="codeTs" disabled></XdoIInput>
      </FormItem>
      <FormItem label="商品名称">
        <XdoIInput v-model="codeName" disabled></XdoIInput>
      </FormItem>
      <hr class="dc-merge-1-3" style="border: 0 none; border-top: 1px solid #DDDDDD; width: 100%; height: 5px;" />
      <FormItem class="dc-merge-1-3" label="所有申报要素">
        <XdoIInput v-model="fullModelStr" type="text" clearable :maxlength="255"
                   @on-enter="onFullModelChange"></XdoIInput>
      </FormItem>
      <hr class="dc-merge-1-3" style="border: 0 none; border-top: 1px solid #DDDDDD; width: 100%; height: 5px;" />
    </XdoForm>
    <XdoForm label-position="top">
      <XdoFormItem>
        <p>
          此商品申报要素仅供参考，若有疑议，请以《中华人民共和国海关进出口商品规范申报目录》为准。
        </p>
      </XdoFormItem>
    </XdoForm>
    <XdoTable ref="tables" :columns="columns" :data="meData" border class="dc-table"></XdoTable>
    <div class="dc-action dc-container-center" style="padding-top: 2px;">
      <XdoButton class="dc-margin-right" type="primary" @click="handlePreservationSave" :disabled="!isEdit">确定</XdoButton>&nbsp;
      <XdoButton class="dc-margin-right" type="primary" @click="handlePreservationSubmit" :disabled="!isEdit">确定并关闭</XdoButton>&nbsp;
      <XdoButton type="primary" @click="handleClose">关闭</XdoButton>
    </div>
  </XdoModal>
</template>

<script>
  import { addEvent, isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'MerchElement',
    props: {
      modelString: {
        type: String
      },
      tyShow: {
        type: Boolean,
        require: true
      },
      codeTs: {
        type: String,
        require: true
      },
      codeName: {
        type: String,
        require: true
      },
      isEdit: {
        type: Boolean,
        require: true,
        default: () => true
      },
      /**
       * 是否需要校验必填(品牌类型、出口享惠情况)
       */
      validNames: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      return {
        meData: [],
        fullModelStr: '',
        extendCmbSource: {
          brandType: [],
          exportBenefits: []
        },
        columns: [{
          title: '序号',
          type: 'index',
          width: 100,
          align: 'center'
        }, {
          key: 'NAME',
          width: 200,
          align: 'center',
          title: '商品申报要素',
          render: (h, params) => {
            let me = this,
              name = params.row['NAME']
            if (me.valid === true && me.validNames.includes(name)) {
              return h('div', {
                class: 'ivu-form-item ivu-form-item-required'
              }, [
                h('label', {
                  class: 'ivu-form-item-label',
                  style: {
                    width: '120px'
                  }
                }, name)
              ])
            } else {
              return h('span', {}, name)
            }
          }
        }, {
          title: '规格型号',
          key: 'VALUE',
          align: 'center',
          render: (h, params) => {
            let me = this,
              p = params,
              inputOrCmb = undefined,
              name = params.row['NAME'],
              elIndex = params.row['elIndex'],
              refName = params.row['refName'],
              required = params.row['required']
            if (name === '品牌类型') {
              let options = me.brandType.map(item => {
                return h('Option', {
                  props: {
                    value: item.value
                  }
                }, item.label)
              })
              inputOrCmb = h('div', {}, [h('Select', {
                props: {
                  transfer: true,
                  clearable: true,
                  // filterable: true,
                  value: params.row.VALUE
                },
                attrs: {
                  itemIndex: elIndex
                },
                style: {
                  width: '95%'
                },
                on: {
                  input: (val) => {
                    me.meData[p.index].VALUE = val
                  }
                }
              }, options)])
              me.setSelectEnterEvent(elIndex)
            } else if (name === '出口享惠情况') {
              let options = me.exportBenefits.map(item => {
                return h('Option', {
                  props: {
                    value: item.value
                  }
                }, item.label)
              })
              inputOrCmb = h('div', {}, [h('Select', {
                props: {
                  transfer: true,
                  clearable: true,
                  // filterable: true,
                  value: params.row.VALUE
                },
                attrs: {
                  itemIndex: elIndex
                },
                style: {
                  width: '95%'
                },
                on: {
                  input: (val) => {
                    me.meData[p.index].VALUE = val
                  }
                }
              }, options)])
              me.setSelectEnterEvent(elIndex)
            } else {
              inputOrCmb = h('Input', {
                props: {
                  type: 'text',
                  maxlength: 255,
                  clearable: true,
                  value: params.row.VALUE,
                  autofocus: params.index === 0
                },
                attrs: {
                  itemIndex: elIndex
                },
                style: {
                  width: '95%'
                },
                on: {
                  'on-change': (e) => {
                    me.meData[p.index].VALUE = e.target.value
                  },
                  'on-keyup': (e) => {
                    me.onInputEnter(e, elIndex)
                  }
                }
              })
            }
            if (me.valid === true && required === true) {
              return h('div', {
                attrs: {
                  itemId: refName
                },
                class: 'ivu-form-item ivu-form-item-required'
              }, [h('div', {
                class: 'ivu-form-item-content'
              }, [h('div', {
                class: 'ivu-input-wrapper ivu-input-wrapper-small ivu-input-type'
              }, [
                h('i', {
                  class: 'ivu-icon ivu-icon-ios-loading ivu-load-loop ivu-input-icon ivu-input-icon-validate'
                }, ''), inputOrCmb]), h('div', {
                class: 'ivu-form-item-error-tip'
              }, '')])])
            } else {
              return inputOrCmb
            }
          }
        }],
        baseBrandType: [{
          value: '0',
          label: '0 无品牌'
        }, {
          value: '1',
          label: '1 境内自主品牌'
        }, {
          value: '2',
          label: '2 境内收购品牌'
        }, {
          value: '3',
          label: '3 境外品牌(贴牌生产)'
        }, {
          value: '4',
          label: '4 境外品牌(其他)'
        }],
        baseExportBenefits: [{
          value: '0',
          label: '0 出口货物在最终目的国(地区)不享受优惠关税'
        }, {
          value: '1',
          label: '1 出口货物在最终目的国(地区)享受优惠关税'
        }, {
          value: '2',
          label: '2 不能确定在最终目的国(地区)享受优惠关税'
        }, {
          value: '3',
          label: '3 不适用于进口报关'
        }]
      }
    },
    watch: {
      codeTs: {
        immediate: true,
        handler: function (newValue) {
          let me = this
          if (!isNullOrEmpty(newValue) && newValue.length === 10) {
            me.getMerchElements()
          } else {
            me.$set(me, 'meData', [])
          }
        }
      },
      tyShow: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.$set(me, 'fullModelStr', me.modelStrArrange(me.modelString))
            me.processDefaultValue()
          } else {
            me.requiredValid(true)
            me.$set(me.extendCmbSource, 'brandType', [])
            me.$set(me.extendCmbSource, 'exportBenefits', [])
          }
        }
      }
    },
    methods: {
      requiredValid(clear = false) {
        let me = this,
          valid = true
        if (me.valid === true) {
          let tables = me.$refs["tables"]
          if (tables) {
            let tbody = tables.$refs["tbody"]
            if (tbody) {
              let divItemIds = me.meData.filter(item => {
                return me.validNames.includes(item.NAME)
              }).map(data => {
                return {
                  code: data.code,
                  value: data.VALUE,
                  refName: data.refName
                }
              })
              divItemIds.forEach(item => {
                let theDiv = tbody.$el.querySelector('div[itemId="' + item.refName + '"]')
                if (theDiv) {
                  theDiv.className = theDiv.className.replace('ivu-form-item-error', '').trim()
                  if (isNullOrEmpty(item.value) && !clear) {
                    theDiv.className = theDiv.className + ' ivu-form-item-error'
                    valid = false
                  }
                  let errTipDiv = theDiv.querySelector('div[class="ivu-form-item-error-tip"]')
                  if (errTipDiv) {
                    if (isNullOrEmpty(item.value) && !clear) {
                      errTipDiv.innerText = '不能为空'
                    } else {
                      errTipDiv.innerText = ''
                    }
                  }
                }
              })
              return valid
            }
          }
        }
        return true
      },
      modelStrArrange(originStr) {
        let result = ''
        if (!isNullOrEmpty(originStr)) {
          result = originStr.trim()
          while (result.lastIndexOf('|') === result.length - 1 && result.length ) {
            result = result.substring(0, result.length - 1)
            console.log(result)

          }
          while (result.lastIndexOf(';') === result.length - 1&& result.length) {
            result = result.substring(0, result.length - 1)
            console.log(result)
          }
        }
        return result
      },
      onFullModelChange() {
        let me = this
        me.processDefaultValue(me.fullModelStr)
      },
      /**
       * 确定
       * @param showMsg
       */
      handlePreservationSave(showMsg = true) {
        let me = this
        if (me.valid === true) {
          let firstValue = undefined,
            secondValue = undefined,
            firstArray = []
          if (me.meData[0]) {
            firstValue = me.meData[0].VALUE
            if (!isNullOrEmpty(firstValue)) {
              firstValue = me.replaceAll(firstValue, ';', '|')
              firstArray = firstValue.split('|')
            }
          }
          if (me.meData[1]) {
            secondValue = me.meData[1].VALUE
          }
          if (isNullOrEmpty(secondValue) && firstArray.length > 0) {
            let tmpValue = me.meData.map(el => el.VALUE).join('|')
            tmpValue = me.replaceAll(tmpValue, ';', '|')
            while (tmpValue.indexOf('|') > -1 && tmpValue.lastIndexOf('|') === tmpValue.length - 1) {
              tmpValue = tmpValue.substring(0, tmpValue.length - 1)
            }
            firstArray = tmpValue.split('|')

            me.meData.map(el => el.VALUE = '')
            if (firstArray.length < me.meData.length) {
              for (let i = 0; i < firstArray.length; i++) {
                if (!isNullOrEmpty(firstArray[i])) {
                  me.meData[i].VALUE = firstArray[i].trim()
                } else {
                  me.meData[i].VALUE = ''
                }
              }
            } else {
              for (let i = 0; i < me.meData.length - 1; i++) {
                if (!isNullOrEmpty(firstArray[i])) {
                  me.meData[i].VALUE = firstArray[i].trim()
                } else {
                  me.meData[i].VALUE = ''
                }
              }
              let temp = ''
              for (let i = me.meData.length - 1; i < firstArray.length; i++) {
                if (!isNullOrEmpty(firstArray[i])) {
                  temp += firstArray[i].trim() + '|'
                } else {
                  me.meData[i].VALUE = '|'
                }
              }
              temp = temp.substr(0, temp.length - 1)
              if (me.meData.length > 0) {
                me.meData[me.meData.length - 1].VALUE = temp
              }
            }
          }
        }

        // let brandTypeKeys = me.baseBrandType.map(item => {
        //     return item.value
        //   }),   // 品牌类型(keys)
        //   brandTypeLabels = me.baseBrandType.map(item => {
        //     return item.label
        //   }),   // 品牌类型(labels)
        //   exportBenefitsKeys = me.baseExportBenefits.map(item => {
        //     return item.value
        //   }),   // 出口享惠情况(keys)
        //   exportBenefitsLabels = me.baseExportBenefits.map(item => {
        //     return item.label
        //   })    // 出口享惠情况(labels)

        let val = me.meData.map(el => {
          // if (el.NAME === '品牌类型') {
          //   if (brandTypeKeys.includes(el.VALUE)) {
          //     return brandTypeLabels[brandTypeKeys.indexOf(el.VALUE)]
          //   }
          // } else if (el.NAME === '出口享惠情况') {
          //   if (exportBenefitsKeys.includes(el.VALUE)) {
          //     return exportBenefitsLabels[exportBenefitsKeys.indexOf(el.VALUE)]
          //   }
          // }
          return el.VALUE
        }).join('|')

        if (!me.requiredValid()) {
          return false
        }
        let valArr = val.split('|').filter(item => {
          return !isNullOrEmpty(item)
        })
        if (valArr.length === 0) {
          val = ''
        }
        val = me.modelStrArrange(val)
        me.$emit('onChange', val)
        if (showMsg && !isNullOrEmpty(val)) {
          me.$set(me, 'fullModelStr', val)
          me.processDefaultValue(val)
        }
        if (showMsg) {
          me.$Message.info('确定完成!')
        }
        return true
      },
      /**
       * 确定并关闭
       */
      handlePreservationSubmit() {
        let me = this
        if (me.handlePreservationSave(false)) {
          me.$emit('update:tyShow', false)
        }
      },
      handleClose() {
        let me = this
        me.$emit('update:tyShow', false)
      },
      getMerchElements() {
        let me = this
        me.pcodeRemote(me.pcode.element, me.codeTs).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            let theDataArr = [],
              dataIndex = 0
            res.forEach(item => {
              theDataArr.push({
                VALUE: '',
                code: item['CODE'],
                NAME: item['NAME'],
                elIndex: dataIndex,
                refName: 'fi_code_' + String(item['CODE']),
                required: ['品牌类型', '出口享惠情况'].includes(item['NAME'])
              })
              dataIndex++
            })
            me.$set(me, 'meData', theDataArr)
            me.processDefaultValue()
          }
        })
      },
      replaceAll(originalString, oldDel, newDel) {
        if (isNullOrEmpty(originalString)) {
          return ''
        }
        originalString = originalString.trim()
        return originalString.replace(new RegExp(oldDel, 'gm'), newDel)
      },
      /**
       * 字段解析
       * @param newModelString
       */
      processDefaultValue(newModelString) {
        let me = this,
          ary = []
        if (isNullOrEmpty(newModelString)) {
          newModelString = me.modelString
        }
        if (isNullOrEmpty(newModelString)) {
          if (me.meData.length > 0) {
            me.meData.map(el => el.VALUE = '')
          }
        } else {
          let newValue = me.replaceAll(newModelString, ';', '|')
          newValue = me.replaceAll(newValue, '；', '|')
          newValue = me.replaceAll(newValue, '（', '(')
          newValue = me.replaceAll(newValue, '）', ')')
          ary = newValue.split('|')

          let brandTypeKeys = me.baseBrandType.map(item => {
              return item.value
            }),   // 品牌类型(keys)
            // brandTypeLabels = me.baseBrandType.map(item => {
            //   return item.label
            // }),   // 品牌类型(labels)
            exportBenefitsKeys = me.baseExportBenefits.map(item => {
              return item.value
            })//,   // 出口享惠情况(keys)
          // exportBenefitsLabels = me.baseExportBenefits.map(item => {
          //   return item.label
          // })    // 出口享惠情况(labels)

          let brandTypeIndex = me.meData.findIndex(item => item.NAME === '品牌类型'),
            tmpBrandType = '',
            exportBenefitsIndex = me.meData.findIndex(item => item.NAME === '出口享惠情况'),
            tmpExportBenefits = ''
          if (brandTypeIndex > -1 && ary.length > brandTypeIndex) {
            tmpBrandType = ary[brandTypeIndex].trim()
            if (brandTypeKeys.includes(tmpBrandType)/* || brandTypeLabels.includes(tmpBrandType)*/) {
              me.$set(me.extendCmbSource, 'brandType', [])
            } else {
              me.$set(me.extendCmbSource, 'brandType', [{value: tmpBrandType, label: tmpBrandType}])
            }
            // if (brandTypeLabels.includes(tmpBrandType)) {
            //   if (isNullOrEmpty(brandTypeKeys[brandTypeLabels.indexOf(tmpBrandType)])) {
            //     ary[brandTypeIndex] = ''
            //   } else {
            //     ary[brandTypeIndex] = brandTypeKeys[brandTypeLabels.indexOf(tmpBrandType)].trim()
            //   }
            // }
          }
          if (exportBenefitsIndex > -1 && ary.length > exportBenefitsIndex) {
            tmpExportBenefits = ary[exportBenefitsIndex].trim()
            if (exportBenefitsKeys.includes(tmpExportBenefits)/* || exportBenefitsLabels.includes(tmpExportBenefits)*/) {
              me.$set(me.extendCmbSource, 'exportBenefits', [])
            } else {
              me.$set(me.extendCmbSource, 'exportBenefits', [{value: tmpExportBenefits, label: tmpExportBenefits}])
            }
            // if (exportBenefitsLabels.includes(tmpExportBenefits)) {
            //   if (isNullOrEmpty(exportBenefitsKeys[exportBenefitsLabels.indexOf(tmpExportBenefits)])) {
            //     ary[exportBenefitsIndex] = ''
            //   } else {
            //     ary[exportBenefitsIndex] = exportBenefitsKeys[exportBenefitsLabels.indexOf(tmpExportBenefits)].trim()
            //   }
            // }
          }
          me.meData.map(el => el.VALUE = '')
          if (ary.length < me.meData.length) {
            for (let i = 0; i < ary.length; i++) {
              if (!isNullOrEmpty(ary[i])) {
                me.meData[i].VALUE = ary[i].trim()
              } else {
                me.meData[i].VALUE = ''
              }
            }
          } else {
            for (let i = 0; i < me.meData.length - 1; i++) {
              if (!isNullOrEmpty(ary[i])) {
                me.meData[i].VALUE = ary[i].trim()
              } else {
                me.meData[i].VALUE = ''
              }
            }
            let temp = ''
            for (let i = me.meData.length - 1; i < ary.length; i++) {
              if (!isNullOrEmpty(ary[i])) {
                temp += ary[i].trim() + '|'
              } else {
                temp += '|'
              }
            }
            temp = temp.substr(0, temp.length - 1)
            if (me.meData.length > 0) {
              me.meData[me.meData.length - 1].VALUE = temp
            }
          }
        }
      },
      setSelectEnterEvent(index) {
        let me = this
        me.$nextTick(() => {
          let tables = me.$refs["tables"]
          if (tables) {
            let tbody = tables.$refs["tbody"]
            if (tbody) {
              let theDiv = tbody.$el.querySelector('div[itemIndex="' + String(index) + '"]')
              if (theDiv) {
                let theInput = theDiv.querySelector('input[type="text"]')
                if (theInput) {
                  addEvent(theInput, 'keyup', function (e) {
                    me.onInputEnter(e, index)
                  })
                }
              }
            }
          }
        })
      },
      onInputEnter(e, index) {
        if (e.keyCode === 13) {
          let me = this,
            tables = me.$refs["tables"]
          if (tables) {
            let tbody = tables.$refs["tbody"]
            if (tbody) {
              let nextInput = tbody.$el.querySelector('div[itemIndex="' + String(index + 1) + '"]')
              if (!nextInput) {
                nextInput = tbody.$el.querySelector('div[itemIndex="0"]')
              }
              let input = nextInput.querySelector('input[type="text"]')
              if (input) {
                input.focus()
              }
            }
          }
        }
      }
    },
    computed: {
      getTyShow() {
        return this.tyShow
      },
      valid() {
        return Array.isArray(this.validNames) && this.validNames.length > 0
      },
      brandType() {
        let me = this
        return [...me.baseBrandType, ...me.extendCmbSource.brandType]
      },
      exportBenefits() {
        let me = this
        return [...me.baseExportBenefits, ...me.extendCmbSource.exportBenefits]
      }
    }
  }
</script>

<style scoped>
</style>
