<template>
  <XdoModal width="1024" mask v-model="show" title="发送失败信息"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard class="ieLogisticsTrackingCard">
      <XdoIInput v-model="tempRemark" :rows="5" type="textarea" disabled></XdoIInput>
    </XdoCard>
  </XdoModal>
</template>

<script>
  export default {
    data() {
      return {
        tempRemark: ''
      }
    },
    watch: {
      show(value) {
        if (value) {
          this.loadData()
        }
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        require: true
      },
      url: {
        type: String,
        require: true
      },
      taskId: {
        type: String,
        require: true
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$http.get(me.url + '/' + me.headId + '/' + me.taskId).then(res => {
          if (typeof res.data.data === "string" && res.data.data.trim().length > 0) {
            let resultData = JSON.parse(res.data.data)
            me.$set(me, 'tempRemark', '调用备案接口成功，但返回信息中存在错误信息：'+resultData.tempRemark)
          } else {
            me.$set(me, 'tempRemark', '')
          }
        }).catch(() => {
        })
      },
      handleClose() {
        let me = this
        me.$emit('errMessage:hide')
      }
    }
  }
</script>

<style lang="less">
</style>
