/**
 * 操作列
 * @type {({handle: string, title: string}|{marginRight: string, handle: string, title: string})[]}
 */
const defaultOperation = [
  { title: '编辑', handle: 'handleEditByRow' },
  { title: '查看', handle: 'handleViewByRow', marginRight: '0' }
]

const createChildComponent = (h, item, vm, params) => {
  return h('a', {
    style: {
      marginRight: item.marginRight ? item.marginRight : '15px'
    },
    on: {
      click: () => {
        if (vm && vm.hasOwnProperty(item.handle)) {
          vm[item.handle](params.data)
        }
      }
    }
  }, item.title)
}

export const operationRenderer = (vm, meta = defaultOperation) => {
  return majesty.Vue.extend({
    render(h) {
      const children = meta.map(it => createChildComponent(h, it, vm, this.params))
      return h('div', children)
    }
  })
}

// lp 2020年2月13日
const createChildComponentOwner = (h, item, vm, params) => {
  return h('a', {
    style: {
      marginRight: item.marginRight ? item.marginRight : '15px'
    },
    on: {
      click: () => {
        if (vm && vm.hasOwnProperty(item.handle)) {
          vm[item.handle](params, item['param'])
        }
      }
    }
  }, item.title === '' ? params.value : item.title)  // lp 2020年2月13日
}

export const operationRendererOwner = (vm, meta = defaultOperation) => {
  return majesty.Vue.extend({
    render(h) {
      const children = meta.map(it => createChildComponentOwner(h, it, vm, this.params))  //lp 2020年2月13日
      return h('div', children)
    }
  })
}

/**
 * 渲染下拉框
 * @param vm 当前组件
 * @param colKey
 * @param OptionKey
 * @returns {*}
 */
export const renderSelect = (vm, colKey, OptionKey) => {
  return majesty.Vue.extend({
    render(h) {
      return ('div', h('Select', {
        props: {
          value: this.params.data[colKey],
          transfer: true
        },
        style: {
          width: this.params.column.actualWidth - 50 + 'px'
        },
        on: {
          input: val => {
            this.params.data[colKey] = val
          }
        }
      }, vm[OptionKey].map((item) => {
        return h('Option', {
          props: {
            value: item.value,
            key: item.label
          }
        }, item.value + ' ' + item.label)
      })))
    }
  })
}
