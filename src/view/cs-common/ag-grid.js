import { snakeCase } from '@/libs/util'

export const agDataSource = (vm, load) => {
  return {
    getRows: (params) => {
      const sortModel = params.sortModel
      if (sortModel) {
        vm.pageParam.sort = sortModel.map(it => {
          return `${snakeCase(it.colId)};${it.sort}`
        })
      }
      const filterModel = params.filterModel
      if (filterModel) {
        for (const key in filterModel) {
          vm.searchParam[key] = filterModel[key].filter
        }
      }
      vm.pageParam.page = params.endRow / 100
      if (load) {
        load(params, params.endRow)
      }
    }
  }
}
