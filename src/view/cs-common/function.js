// 递归得到grid导出列
export const getGridExportColumns = (columns) => {
  let exportHeader = []
  for (let col of columns) {
    getGridExportColumnsDetail(exportHeader, col)
  }
  return exportHeader
}

const getGridExportColumnsDetail = (exportHeader, col) => {
  if (col.children && col.children.length > 0 && col.children instanceof Array) {
    for (let c of col.children) {
      getGridExportColumnsDetail(exportHeader, c)
    }
  }
  if (!col.key)
    return
  exportHeader.push({
    key: col.key,
    value: col.title
  })
}
