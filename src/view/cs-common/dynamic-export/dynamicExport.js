export const dynamicExport = {
  name: 'dynamicExport',
  mixins: [],
  data() {
    return {
      taskInfo: {
        taskName: '',                 // 任务名称
        systemCode: 'CS-STIC',             // 添加任务使用的systemCode
        exportParamJson: {},          // 查询条件
        displayTaskCode: '',          // 下载中心所使用的过滤条件，不填默认使用taskCode
        enableSharding: true,         // 启用分片
        displaySystemCode: '',        // 下载中心所使用的过滤条件，不填默认使用systemCode
        extraCode: 'extraCode1',      // 业务系统额外参数用于处理同一企业不同手帐册等情况
        fileName: 'fileName.xlsx'     // 文件名称
      },
      customBaseUri: Object.assign({}, this.$config.cs || { exportRoot: '' }).exportRoot
    }
  },
  methods: {
    onExportClick() {
      let me = this,
        exportTitle = '',
        today = (new Date()).toJSON().substr(0, 10).replace(/[-T]/g, '')
      if (me.listConfig) {
        exportTitle = me.listConfig.exportTitle
      }
      if (me.gridConfig) {
        exportTitle = me.gridConfig.exportTitle
      }
      me.$set(me.taskInfo, 'taskName', exportTitle)
      me.$set(me.taskInfo, 'fileName', exportTitle + '_' + today + '.xlsx')
      me.$set(me.taskInfo, 'exportParamJson', { ...me.getSearchParams(), ...me.extendParams })
    }
  },
  computed: {
    extendParams() {
      return {}
    }
  }
}
