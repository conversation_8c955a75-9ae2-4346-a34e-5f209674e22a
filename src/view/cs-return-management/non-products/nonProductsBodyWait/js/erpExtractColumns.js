
import { importExportManage, interimVerification } from '@/view/cs-common'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const commColumns = [
  'sid'
  , 'linkedNo'
  , 'facGNo'
  , 'unitErp'
  , 'qty'
  , 'decPrice'
  , 'decTotal'
  , 'netWt'
  , 'grossWt'
  , 'useType'
  , 'dutyMode'
  , 'remark1'
  , 'remark2'
  , 'remark3'
  , 'bondMark'
  , 'bondMarkConvert'
  , 'gmark'
  , 'gmarkConvert'
  , 'curr'
  , 'countryConvert'
]

const columnsConfig = [
  ...commColumns
]

const columnsErrConfig = [
  ...commColumns
  , 'tempRemark'
]

const excelColumnsConfig = [
  ...commColumns
  , 'tempRemark'
]
const columns = {
  mixins: [columnRender],

  data() {
    return {
      totalColumns: [
        {
          title: '提取单号',
          width: 120,
          align: 'center',
          key: 'linkedNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '转换后保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMarkConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '转换后物料类型',
          width: 120,
          align: 'center',
          key: 'gmarkConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.gmarkMap)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '物料类型',
          width: 120,
          align: 'center',
          key: 'gmark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.gmarkMap)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '企业料号',
          width: 150,
          align: 'center',
          key: 'facGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '交易单位',
          width: 120,
          align: 'center',
          key: 'unitErp',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '数量',
          width: 120,
          align: 'center',
          key: 'qty',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '单价',
          width: 120,
          align: 'center',
          key: 'decPrice',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '总价',
          width: 120,
          align: 'center',
          key: 'decTotal',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'currConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '产销国',
          width: 120,
          align: 'center',
          key: 'countryConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '产销国',
          width: 120,
          align: 'center',
          key: 'country',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '净重',
          width: 120,
          align: 'center',
          key: 'netWt',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '毛重',
          width: 120,
          align: 'center',
          key: 'grossWt',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '用途',
          width: 120,
          align: 'center',
          key: 'useType',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '征免方式',
          width: 120,
          align: 'center',
          key: 'dutyMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark1',
          width: 120,
          align: 'center',
          key: 'remark1',
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark2',
          width: 120,
          align: 'center',
          key: 'remark2',
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark3',
          width: 120,
          align: 'center',
          key: 'remark3',
          ellipsis: true,
          tooltip: true
        }
      ],
      errColumns: [
        {
          width: 250,
          title: '错误信息',
          align: 'center',
          key: 'tempRemark',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '提取单号',
          width: 120,
          align: 'center',
          key: 'linkedNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '转换后保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMarkConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.interimVerification.BONDED_FLAG_MAP)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '转换后物料类型',
          width: 120,
          align: 'center',
          key: 'gmarkConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.gmarkMap)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '物料类型',
          width: 120,
          align: 'center',
          key: 'gmark',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.importExportManage.gmarkMap)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '企业料号',
          width: 150,
          align: 'center',
          key: 'facGNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '交易单位',
          width: 120,
          align: 'center',
          key: 'unitErp',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '数量',
          width: 120,
          align: 'center',
          key: 'qty',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '单价',
          width: 120,
          align: 'center',
          key: 'decPrice',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '总价',
          width: 120,
          align: 'center',
          key: 'decTotal',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'currConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          width: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '产销国',
          width: 120,
          align: 'center',
          key: 'countryConvert',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '产销国',
          width: 120,
          align: 'center',
          key: 'country',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '净重',
          width: 120,
          align: 'center',
          key: 'netWt',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '毛重',
          width: 120,
          align: 'center',
          key: 'grossWt',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '用途',
          width: 120,
          align: 'center',
          key: 'useType',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '征免方式',
          width: 120,
          align: 'center',
          key: 'dutyMode',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark1',
          width: 120,
          align: 'center',
          key: 'note1',
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark2',
          width: 120,
          align: 'center',
          key: 'note2',
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'Remark3',
          width: 120,
          align: 'center',
          key: 'note3',
          ellipsis: true,
          tooltip: true
        }
      ],
      importExportManage: importExportManage,
      interimVerification: interimVerification
    }
  }
}

export {
  columnsConfig,
  columnsErrConfig,
  excelColumnsConfig,
  columns
}
