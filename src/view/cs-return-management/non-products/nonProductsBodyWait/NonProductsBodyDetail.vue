<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseForm } from '@/mixin/generic/form/baseForm'
  import { editStatus, returnManagement } from '@/view/cs-common'

  export default {
    name: 'NonProductsBodyDetail',
    mixins: [baseForm],
    props: {
      /**
       * 传入的表头数据
       */
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      let fields = this.getFields()
      return {
        selfFields: fields,
        cmbSource: {
          status: returnManagement.LIST_STATUS,
          facGNo: []
        },
        ajaxUrl: {
          insert: csAPI.returnManagement.nonProducts.body.insert,
          update: csAPI.returnManagement.nonProducts.body.update
        }
      }
    },
    mounted: function () {
      let me = this
      me.fieldsReorganization()
    },
    methods: {
      /**
       * 获取默认值(调用时可覆盖)
       * @returns {{}}
       */
      getDefaultData() {
        // let me = this
        return {}
      },
      /**
       * 获取编辑字段
       * @returns {*[]}
       */
      getFields() {
        let me = this
        return [{
          title: '企业料号',
          key: 'facGNo',
          type: 'select',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label,
            disabled: me.editConfig.editStatus === editStatus.EDIT
          }
        }, {
          key: 'copGName',
          title: '商品名称',
        }, {
          title: '数量',
          key: 'qty',
          type: 'xdoInput',
          props: {
            disabled: me.editConfig.editStatus === editStatus.EDIT
          }
        }, {
          title: '单位',
          key: 'unit'
        }, {
          title: '单价',
          key: 'price'
        }, {
          key: 'destinationCountry',
          title: '目的国',
          type: 'pcode',
          props: {
            meta: 'COUNTRY_OUTDATED' // this.pcode.country_outdated
          }
        }, {
          type: 'pcode', title: '币制', key: 'curr',
          props: {
            meta: 'CURR_OUTDATED'
          }
        }, {
          key: 'returnOrderNo',
          title: '销退订单号',
        }]
      },
      /**
       * 获取必填字段(数组)
       * @returns {Array}
       */
      getRequiredFields() {
        return ['facGNo', 'qty']
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        me.detailConfig.model.headId = me.parentConfig.headData.sid
        me.doSave()
      }
    },
    watch: {
      // 企业料号更改
      'detailConfig.model.facGNo': function (value) {
        let me = this,
          needClear = false
        if (!isNullOrEmpty(value)) {
          needClear = true
          me.$http.post(`${csAPI.returnManagement.feedingProducts.getIeInfo}`, {
            gmark: 'E',
            facGNo: value,
            bondedFlag: '1',
            emsNo: me.parentConfig.headData.emsNo,
            iemark: me.parentConfig.headData.ieMark
          }).then(res => {
            if (needClear) {
              me.detailConfig.model.copGName = res.data.data.gname
            } else {
              me.detailConfig.model.copGName = ''
            }
          }).catch(() => {
            me.detailConfig.model.copGName = ''
          }).finally(() => {
            let field = me.detailConfig.fields.find(p => p.key === 'copGName')
            if (field) {
              me.fieldOptimization(field)
            }
          })
        }
      }
    },
    created: function () {
      let me = this
      // 企业料号
      me.$http.post(`${csAPI.returnManagement.feedingProducts.getFacGNolist}`, {
        bondedFlag: '1',
        gmark: 'E',
        tradeMode: me.parentConfig.headData.tradeMode,
        emsNo: me.parentConfig.headData.emsNo,
      }).then(res => {
        me.$set(me.cmbSource, 'facGNo', res.data.data.map(item => {
          return {
            value: item,
            label: item
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'facGNo', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'facGNo')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .eye-catching label {
    font-weight: bold;
    color: orangered !important;
  }

  /deep/ .eye-catching .ivu-form-item-content span {
    color: orangered !important;
  }
</style>
