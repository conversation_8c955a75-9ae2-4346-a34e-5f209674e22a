<template>
  <XdoModal width="1024" mask v-model="show" title="数据提取"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <div class="dc-form-2">
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <!--            <Upload ref="upload" style="padding: 0" class="dc-margin-right"-->
            <!--                    :headers="requestHeader" :show-upload-list="false"-->
            <!--                    :format="['xls','xlsx']" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"-->
            <!--                    :action="ajaxUrl.uploadUrl"-->
            <!--                    :data="uploadOption"-->
            <!--                    :before-upload="handleBeforeUpload"-->
            <!--                    :on-success="handleSuccess">-->
            <!--              <XdoButton type="primary" icon="ios-cloud-upload-outline">导入条件</XdoButton>-->
            <!--            </Upload>-->
          </div>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <XdoForm :label-width="72" class="dc-form dc-form-3" inline style="padding-top: 0;">
          <XdoFormItem prop="linkedNo" label="提取单号" style="padding: 0;">
            <xdo-select v-model="searchParam.linkedNo" :options="cmbSource.emsListNoData"></xdo-select>
            <!-- mixer -->
          </XdoFormItem>
          <XdoFormItem prop="linkedNo2" label="" class="dc-merge-2-4" :label-width="0">
            <XdoIInput type="text" v-model="searchParam.linkedNo2" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="facGNo" label="企业料号">
            <XdoIInput type="text" v-model="searchParam.facGNo" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNo" label="备案料号">
            <XdoIInput type="text" v-model="searchParam.copGNo" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="costCenter" label="成本中心">
            <XdoIInput type="text" v-model="searchParam.costCenter" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="decPriceFlag" label="单价">
            <XdoCheckbox v-model="decPriceFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
          </XdoFormItem>
          <XdoFormItem prop="netWtFlag" label="净重">
            <XdoCheckbox v-model="netWtFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
          </XdoFormItem>
        </XdoForm>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <div class="content">
      <DcAgGrid ref="table" v-show="!isConfirm" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="360"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div v-if="!isConfirm" class="page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
      <DcAgGrid ref="table" v-show="isConfirm" :columns="grdErrConfig.gridColumns" :data="grdErrConfig.data"
                :height="360"></DcAgGrid>
      <div v-if="isConfirm" class="page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="grdErrConfig.pageSizeOpts"
                 :current="grdErrConfig.page" :page-size="grdErrConfig.limit" :total="grdErrConfig.dataTotal"
                 @on-change="pageErrChange" @on-page-size-change="pageSizeErrChange"/>
      </div>
    </div>
  </XdoModal>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { interimVerification } from '@/view/cs-common'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { columnsConfig, columnsErrConfig, excelColumnsConfig, columns } from './js/erpExtractColumns'

  export default {
    name: 'erpExtract',
    mixins: [pms, commList, columns],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        require: true
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        netWtFlag: false,
        decPriceFlag: false,
        searchParam: {
          linkedNo: '',
          linkedNo2: '',
          facGNo: '',
          copGNo: '',
          netWt: null,
          decPrice: null,
          costCenter: ''
        },
        toolbarEventMap: {
          // 'export': this.handleDownload,
          'extractByQuery': this.handleConfirm,
          'extractByChoose': this.handleExtractByChoose
        },
        actions: [],
        actionsComm: {
          needed: true,
          loading: false,
          disabled: false
        },
        cmbSource: {
          emsListNoData: []
        },
        ajaxUrl: {
          getEmsListNo: csAPI.sapErp.extract.imports.getEmsListNo,
          selectAllPaged: csAPI.returnManagement.nonProducts.body.extractIQuery,
          extractConfirm: csAPI.returnManagement.nonProducts.body.checkLinkedNo,
          extractErpDataByChoose: csAPI.returnManagement.nonProducts.body.checkLinkedNoBySid
        },
        interimVerification: interimVerification,
        isConfirm: false,
        grdErrConfig: {
          gridColumns: [],

          fullData: [],
          pagerData: {},
          page: 1,
          limit: 20,
          dataTotal: -1,
          data: [],

          exportTitle: '数据提取错误数据',
          exportColumns: [],
          pageSizeOpts: [10, 20, 50, 100]
        },
        requestHeader: {
          Authorization: 'Bearer ' + this.$store.state.token
        },
        uploadOption: {},
        uploadProps: {
          file: null
        }
      }
    },
    created() {
      let me = this
      // 提取页面提取单号
      me.$http.post(csAPI.returnManagement.feedingProducts.getLinkedNo).then(res => {
        me.cmbSource.emsListNoData = res.data.data.map(item => {
          return {
            label: item,
            value: item
          }
        })
      }).catch(() => {
      })
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            this.handleSearchSubmit()
          }
        }
      },
      'grdErrConfig.fullData': {
        handler: function () {
          this.errDataAnalysis()
        }
      },
      'grdErrConfig.limit': {
        handler: function () {
          this.errDataAnalysis()
        }
      },
      netWtFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'netWt', 1)
          } else {
            this.$set(this.searchParam, 'netWt', null)
          }
        }
      },
      decPriceFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'decPrice', 1)
          } else {
            this.$set(this.searchParam, 'decPrice', null)
          }
        }
      }
    },
    mounted() {
      let me = this
      let columns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.gridColumns = [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, ...columns]
      me.grdErrConfig.gridColumns = getColumnsByConfig(me.errColumns, columnsErrConfig)
      me.grdErrConfig.exportColumns = getExcelColumnsByConfig(me.errColumns, excelColumnsConfig)
      me.loadFunctions().then(() => {
        if (typeof me.actionLoad === "function") {
          me.actionLoad()
        }
      })
    },
    methods: {
      actionLoad() {
        let me = this
        me.actions = []
        me.actions.push({
          ...me.actionsComm,
          label: '提取勾选',
          key: 'xdo-btn-edit',
          icon: 'ios-checkmark',
          command: 'extractByChoose'
        }, {
          ...me.actionsComm,
          label: '全部提取',
          icon: 'md-done-all',
          key: 'xdo-btn-upload',
          command: 'extractByQuery'
        })
      },
      /**
       * 抓取所以可用的关联编号
       */
      loadEmsListNoData() {
        let me = this
        me.$http.post(me.ajaxUrl.getEmsListNo).then(res => {
          console.log(res.dara.data)
          let emsListNoList = res.data.data || []
          me.$set(me.cmbSource, 'emsListNoData', emsListNoList.sort())
        }).catch(() => {
          me.$set(me.cmbSource, 'emsListNoData', [])
        }).finally(() => {
          me.handleSearchSubmit()
        })
      },
      filterMethod(value, option) {
        if (isNullOrEmpty(value)) {
          value = ''
        }
        if (typeof value !== 'string') {
          value = value.toString()
        }
        let opVal = option
        if (isNullOrEmpty(opVal)) {
          opVal = ''
        }
        if (typeof opVal !== 'string') {
          opVal = opVal.toString()
        }
        return opVal.toUpperCase().indexOf(value.toUpperCase()) !== -1
      },
      /**
       * 获取查询条件
       */
      getSearchParams() {
        return this.searchParam
      },
      /**
       * 点击查询按钮
       */
      handleSearchSubmit() {
        let me = this
        me.isConfirm = false
        me.pageParam.page = 1
        me.getList()
      },
      /**
       * 根据勾选提取
       */
      handleExtractByChoose() {
        let me = this
        if (me.checkRowSelected('提取勾选')) {
          me.setButtonLoading('extractByChoose', true)
          let url = me.ajaxUrl.extractErpDataByChoose
          if (!isNullOrEmpty(me.headId)) {
            url += '/' + me.headId
          }
          let params = me.getSelectedParams()
          me.$http.post(url + '/' + params).then(res => {
            if (res && res.data) {
              if (res.data.success === true) {
                if (res.data.data && Array.isArray(res.data.data)) {
                  me.setToolbarProperty('export', 'disabled', (res.data.data.length < 1))

                  me.grdErrConfig.page = 1
                  me.grdErrConfig.dataTotal = res.data.data.length
                  me.grdErrConfig.fullData = res.data.data
                  me.isConfirm = true
                  me.$Message.success(res.data.message)
                } else {
                  me.$Message.success(res.data.message)
                  me.handleClose()
                }
              } else {
                me.grdErrDataClear()
                if (!isNullOrEmpty(res.data.message)) {
                  me.$Message.error(res.data.message)
                } else {
                  me.$Message.error('数据提取失败!')
                }
              }
            } else {
              me.grdErrDataClear()
              if (res && !isNullOrEmpty(res.message)) {
                me.$Message.success(res.message)
              } else {
                me.$Message.success('数据提取失败!')
              }
            }
          }).catch(() => {
          }).finally(() => {
            me.setButtonLoading('extractByChoose', false)
          })
        }
      },
      /**
       * 根据查询条件提取
       */
      handleConfirm() {
        let me = this
        me.setToolbarLoading('extractByQuery', true)
        let confirmData = Object.assign({}, me.searchParam)
        me.$http.post(me.ajaxUrl.extractConfirm + '/' + me.headId, confirmData, {noIntercept: true}).then(res => {
          if (res && res.data) {
            if (res.data.success === true) {
              if (res.data.data && Array.isArray(res.data.data)) {
                me.setToolbarProperty('export', 'disabled', (res.data.data.length < 1))

                me.grdErrConfig.page = 1
                me.grdErrConfig.dataTotal = res.data.data.length
                me.grdErrConfig.fullData = res.data.data
                me.isConfirm = true
                me.$Message.success(res.data.message)
              } else {
                me.$Message.success(res.data.message)
                me.handleClose()
              }
            } else {
              me.grdErrDataClear()
              if (!isNullOrEmpty(res.data.message)) {
                me.$Message.error(res.data.message)
              } else {
                me.$Message.error('数据提取失败!')
              }
            }
          } else {
            me.grdErrDataClear()
            if (res && !isNullOrEmpty(res.message)) {
              me.$Message.success(res.message)
            } else {
              me.$Message.success('数据提取失败!')
            }
          }
        }).catch(() => {
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.setToolbarLoading('extractByQuery', false)
        })
      },
      errDataAnalysis() {
        let me = this
        me.$set(me.grdErrConfig, 'pagerData', {1: []})

        let fullData = me.grdErrConfig.fullData || []
        let pageSize = me.grdErrConfig.limit
        let pageIndex = 1
        let dataIndex = 0

        for (let data of fullData) {
          if (dataIndex < pageSize) {
            dataIndex++
            me.grdErrConfig.pagerData[pageIndex].push(data)
          } else {
            dataIndex = 1
            pageIndex++
            me.grdErrConfig.pagerData[pageIndex] = [data]
          }
        }

        me.$set(me.grdErrConfig, 'data', me.grdErrConfig.pagerData[1])
      },
      pageErrChange(page) {
        let me = this
        me.grdErrConfig.page = page
        me.$set(me.grdErrConfig, 'data', me.grdErrConfig.pagerData[me.grdErrConfig.page])
      },
      pageSizeErrChange(pageSize) {
        let me = this
        me.grdErrConfig.limit = pageSize
        me.$set(me.grdErrConfig, 'data', me.grdErrConfig.pagerData[1])
      },
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
        me.$emit('import:success', false)
      },
      rowClassName() {
        return 'myRow'
      },
      handleSuccess(response) {
        let me = this
        if (response.success === true) {
          me.$set(me.searchParam, 'linkedNo2', response.data)
          me.handleSearchSubmit()
        } else {
          me.$Message.warning(response.message)
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }

  .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    border-color: #57a3f3;
    background-color: #57a3f3;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .action {
    padding: 0 !important;
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }
</style>
