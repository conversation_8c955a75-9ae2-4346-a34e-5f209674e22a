<template>
  <XdoModal width="1024" mask v-model="show" title="匹配报关数据"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm ref="headerEditFrom" class="dc-form dc-form-3 xdo-enter-form" label-position="right" :label-width="80"
             :model="configData" style="padding: 0; grid-column-gap: 0;">
      <Card :bordered="false" class="dc-merge-1-4">
        <p slot="title">待退换货物信息</p>
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="facGNo" label="企业料号">
            <XdoIInput type="text" v-model="configData.facGNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGName" label="商品名称">
            <XdoIInput type="text" v-model="configData.copGName" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="qty" label="数量">
            <XdoIInput type="text" v-model="configData.qty" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="unit" label="单位">
            <xdo-select v-model="configData.unit" :asyncOptions="pcodeList" meta="UNIT" :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="price" label="单价">
            <XdoIInput type="text" v-model="configData.price" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="destinationCountry" label="最终目的国">
            <xdo-select v-model="configData.destinationCountry" :meta="pcode.country_outdated"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select type="text" v-model="configData.curr" :asyncOptions="pcodeList" meta="CURR_OUTDATED" disabled></xdo-select>
          </XdoFormItem>
        </div>
      </Card>
    </XdoForm>
    <Card :bordered="false">
      <p slot="title">可匹配信息</p>
      <div ref="area_search">
        <div>
          <div class="separateLine"></div>
          <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
            <XdoFormItem prop="emsNo" label="保完税标志">
              <xdo-select v-model="searchParam.bondMark" :options="cmbSource.bondMarkList"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="emsNo" label="备案号" >
              <xdo-select :disabled="searchParam.bondMark === '1'" v-model="searchParam.emsNo" :options="cmbSource.emsNoList"></xdo-select>
            </XdoFormItem>
            <XdoFormItem class="dc-import-operation" :label-width="100">
              <Button type="primary" @click="handleConfirm" :loading="matching">重新匹配</Button>
            </XdoFormItem>
            <dc-dateRange label="报关单申报日期" @onDateRangeChanged="handleValidDateChange" class="dc-merge-1-3"></dc-dateRange>
          </XdoForm>
        </div>
      </div>
      <xdo-ag-grid class="dc-table" ref="table" :height="300" :rowClassRules="rowClassRules"
                   :columns="gridConfig.gridColumns" :data="gridConfig.data"
                   :checkboxSelection="checkboxSelection" rowSelection="multiple"
                   @selectionChanged="handleSelectionChanged" @grid-ready="onGridReady"
                   :suppressRowClickSelection="true"
      ></xdo-ag-grid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </Card>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { returnManagement } from '@/view/cs-common'
  import { columns } from './js/matchCustomsDataColumns'
  import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

  export default {
    name: 'MatchCustomsData',
    mixins: [columns, commColumnsCustom],
    props: {
      /**
       * 传入的查看信息
       */
      configData: {
        type: Object,
        default: () => ({})
      },
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        isShowOp: false,
        matching: false,
        p_group: 'noAction',
        checkboxSelection: true,
        searchParam: {
          emsNo: '',
          dDateFrom: '',
          dDateTo: '',
          bondMark: '1'
        },
        rowClassRules: {
          'data-split': 'data.qty!=data.lastQty'
        },
        cmbSource: {
          emsNoList: [],
          bondMarkList: returnManagement.bondedFlagList
        },
        ajaxUrl: {
          selectAllPaged: csAPI.returnManagement.nonProducts.body.matchCustoms,
          saveMatchCustoms: csAPI.returnManagement.nonProducts.body.saveMatchCustoms
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '关闭', type: 'primary', command: 'cancel', click: this.handleClose}
        ]
      }
    },
    watch: {
      show: function (val) {
        if (val) {
          this.getList()
        }
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbSource.emsNoList = tmpArr
      }).catch(() => {
      })
    },
    methods: {
      //ag-grid创建完成后执行的事件
      onGridReady(params) {
        let me = this
        // 获取gridApi
        me.gridApi = params.api
        me.columnApi = params.columnApi
        // 这时就可以通过gridApi调用ag-grid的传统方法了
        me.gridApi.sizeColumnsToFit()
      },
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
        me.$emit('import:success', false)
      },
      /**
       * 确认匹配报关数据
       */
      handleSave() {
        let me = this
        if (isNullOrEmpty(me.gridConfig.selectRows)) {
          me.$Message.warning('你还未选择任何数据!')
          return
        }
        me.$http.post(me.ajaxUrl.saveMatchCustoms + `/${me.configData.sid}`, me.gridConfig.selectRows
        ).then(() => {
          me.$Message.success('数据匹配完成!')
          me.getList()
          me.handleClose()
        }).catch(() => {
        }).finally(() => {
        })
      },
      /**
       * 获取查询条件(可外部覆盖)
       */
      getSearchParams() {
        return Object.assign({...this.configData}, {...this.searchParam})
      },
      // 日期更改
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "dDateFrom", values[0])
          this.$set(this.searchParam, "dDateTo", values[1])
        } else {
          this.$set(this.searchParam, "dDateFrom", '')
          this.$set(this.searchParam, "dDateTo", '')
        }
      },
      // 查询报关数据
      handleConfirm() {
        let me = this
        me.matching = true
        let params = me.getSearchParams()
        me.$http.post(csAPI.returnManagement.nonProducts.body.matchCustoms, params).then(res => {
          me.gridConfig.data = res.data.data
        }).catch(() => {
        }).finally(() => {
          me.matching = false
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 3px 16px 1px 16px !important;
  }

  /deep/ .ivu-card-body {
    padding: 8px 8px 2px 8px !important;
  }

  /deep/ .data-split {
    background-color: #ff9900 !important;
  }
  .dc-form-5 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(5, 1fr);
  }
</style>
