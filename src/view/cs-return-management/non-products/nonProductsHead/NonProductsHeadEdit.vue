<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { baseForm } from '@/mixin/generic/form/baseForm'
  import { editStatus, returnManagement } from '@/view/cs-common'

  export default {
    name: 'NonProductsHeadMain',
    mixins: [baseForm],
    data() {
      let fields = this.getFields()
      return {
        selfFields: fields,
        cmbSource: {
          templateHeadId: [],
          ieMark: returnManagement.I_E_MARK
        },
        ajaxUrl: {
          insert: csAPI.returnManagement.nonProducts.insert,
          update: csAPI.returnManagement.nonProducts.update,
          selectAll: csAPI.returnManagement.feedingProducts.selectAll
        }
      }
    },
    created() {
      let me = this
      // 选择模板
      me.$http.post(`${me.ajaxUrl.selectAll}/I/1`).then(res => {
        me.$set(me.cmbSource, 'templateHeadId', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'templateHeadId', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'templateHeadId')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    mounted: function () {
      let me = this
      me.fieldsReorganization()
    },
    methods: {
      /**
       * 获取必填字段(数组)
       * @returns {Array}
       */
      getRequiredFields() {
        return []
      },
      getFields() {
        let me = this
        return [{
          key: 'emsListNo',
          title: '单据内部编号',
          props: {
            disabled: me.editConfig.editStatus === editStatus.EDIT
          }
        }, {
          key: 'ieMark',
          type: 'select',
          title: '进出口标志',
          defaultValue: 'I',
          props: {
            disabled: true,
          }
        }, {
          key: 'despPort',
          title: '启运港',
          props: {
            maxlength: 100
          },
        }, {
          key: 'destPort',
          title: '目的港',
          props: {
            maxlength: 100
          }
        }, {
          type: 'select',
          title: '选择模板',
          key: 'templateHeadId',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          }
        }, {
          title: '备注1',
          key: 'note1',
          props: {
            maxlength: 225
          }
        }, {
          title: '备注2',
          key: 'note2',
          props: {
            maxlength: 225
          }
        }]
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.doSave(function (res) {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          me.$set(me.detailConfig.model, 'emsListNo', res.data.data['emsListNo'])
        })
      }
    }
  }
</script>

<style scoped>
</style>
