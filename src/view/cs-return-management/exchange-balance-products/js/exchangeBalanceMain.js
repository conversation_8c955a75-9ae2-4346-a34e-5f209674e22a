import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

export const mainJS = {
  mixins: [commColumnsCustom, pms],
  data() {
    return {
      searchLines: 3,
      isShowOp: false,
      isShowSetting: true,
      checkboxSelection: false,
      gridConfig: {
        exportTitle: '成品退换平衡'
      },
      toolbarEventMap: {
        'export': this.handleDownload   // '导出'
      },
      ajaxUrl: {
        exportUrl: csAPI.returnManagement.exchangeBalance.exportUrl,
        selectAllPaged: csAPI.returnManagement.exchangeBalance.selectAllPaged
      }
    }
  },
  methods: {
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
