import {columnRender} from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      { title: '退运进口报关单号', width: 200, key: 'entryNo' },
      { title: '退运进口单据编号', width: 200, key: 'emsListNo' },
      {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      },
      { title: '备案号', width: 150, key: 'emsNo' },
      { title: '企业料号', width: 150, key: 'facGNo' },
      { title: '商品名称', width: 200, key: 'copGName' },
      { title: '物料类型标志', width: 200, key: 'gMark' },
      { title: '退运进口数量', width: 200, key: 'iqty' },
      { title: '退运复出数量', width: 200, key: 'eqty' },
      { title: '差异', width: 200, key: 'difQty' }
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}

export {
  columns
}
