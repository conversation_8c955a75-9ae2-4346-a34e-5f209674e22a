<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <ReturnIManagementEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></ReturnIManagementEdit>
      </TabPane>
      <TabPane name="toBeMatchedTab" v-if="showBody" label="待匹配表体" index="'2'">
        <ToBeMatchedIList ref="toBeMatchedTab" :parent-config="currConfig"></ToBeMatchedIList>
      </TabPane>
      <TabPane name="matchedTab" v-if="showBody" label="已匹配表体" index="'3'">
        <MatchedIList ref="matchedTab" :head-id="currConfig.editData.sid"></MatchedIList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import MatchedIList from './matched-i-list'
  import ToBeMatchedIList from './to-be-matched-i-list'
  import ReturnIManagementEdit from './return-i-management-edit'
  import { returnManagementTabs } from '@/view/cs-return-management/return-e-management/js/returnManagementTabs'

  export default {
    name: 'repairAndReShipmentTabs',
    components: {
      MatchedIList,
      ToBeMatchedIList,
      ReturnIManagementEdit
    },
    mixins: [returnManagementTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
