import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const matchedIList = {
  name: 'matchedIList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    headId: {
      type: String,
      default: () => ('')
    }
  },
  data() {
    return {
      hasChildTabs: true,
      pmsLevel: 'matched',
      listConfig: {
        checkColumnShow: false,
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  watch: {
    headId: {
      immediate: true,
      handler: function () {
        this.handleSearchSubmit()
      }
    }
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['status'] = '1'
      paramObj['headId'] = me.headId
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 150,
        key: 'copGName',
        title: '商品名称'
      }, {
        key: 'qty',
        width: 150,
        title: '数量'
      }, {
        width: 150,
        key: 'unit',
        title: '申报单位'
      }, {
        width: 150,
        key: 'price',
        title: '申报单价'
      }, {
        width: 150,
        title: '申报总价',
        key: 'totalPrice'
      }, {
        width: 150,
        title: '原产国',
        key: 'originCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        })
      }, {
        width: 150,
        title: '目的国',
        key: 'destinationCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        })
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    }
  }
}
