import ReturnIManagementTabs from '../return-i-management-tabs'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { returnManagement, productClassify } from '@/view/cs-common'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const returnIManagementList = {
  name: 'returnIManagementList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    ReturnIManagementTabs
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup,
        'generate-dec-data': this.handleGenerateDecData
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{range: boolean, title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['type'] = '4'
      paramObj['ieMark'] = 'I'
      return paramObj
    },
    /**
     * 显示列表
     * @returns {({width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 150,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, returnManagement.STATUS_MAP)
        })
      }, {
        width: 150,
        key: 'ieMark',
        title: '进出口标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, returnManagement.I_E_MARK)
        })
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, productClassify.GMARK_SELECT)
        })
      }, {
        width: 200,
        title: '启运港',
        key: 'despPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'PORT_LIN')
        })
      }, {
        width: 200,
        title: '目的港',
        key: 'destPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], 'PORT_LIN')
        })
      }, {
        width: 150,
        key: 'agentCode',
        title: '报关单申报单位'
      }, {
        width: 150,
        title: '监管方式',
        key: 'tradeMode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 200,
        key: 'note1',
        title: '备注1'
      }, {
        width: 200,
        key: 'note2',
        title: '备注2'
      }, {
        width: 150,
        title: '制单员',
        key: 'insertUser'
      }, {
        width: 150,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param row
     * @param opTitle
     */
    extendEditCheck(row, opTitle) {
      let me = this
      if (row.status === '2') {
        me.$Message.warning('【已生成报关】状态下数据不允许再' + opTitle)
        return false
      }
      return true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param rows
     * @param opTitle
     */
    extendDeleteCheck(rows, opTitle) {
      let me = this
      rows.forEach(row => {
        if (row.status === '2') {
          me.$Message.warning('【已生成报关】状态下数据不允许再' + opTitle)
          return false
        }
      })
      return true
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 生成报关数据
     */
    handleGenerateDecData() {
      let me = this
      if (me.checkRowSelected('生成报关数据', true)) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '确认生成所选项的报关数据吗',
          onOk: () => {
            me.setToolbarLoading('generate-dec-data', true)
            me.$http.post(me.ajaxUrl.generateDecData + '/' + me.listConfig.selectRows[0].sid).then(() => {
              me.$Message.success('生成报关数据成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('generate-dec-data')
            })
          }
        })
      }
    }
  }
}
