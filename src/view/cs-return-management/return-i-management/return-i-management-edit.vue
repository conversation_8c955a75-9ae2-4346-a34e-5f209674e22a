<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 4px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { editStatus, returnManagement, productClassify } from '@/view/cs-common'

  export default {
    name: 'returnIManagementEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        cmbSource: {
          templateHeadId: [],
          ieMark: returnManagement.I_E_MARK,
          gmark: productClassify.GMARK_SELECT
        },
        ajaxUrl: {
          insert: csAPI.returnManagement.returnIManagement.head.insert,
          update: csAPI.returnManagement.returnIManagement.head.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this,
            gMarkDisable = false
          me.fieldsReset()
          if (me.buttons[me.buttons.findIndex(btn => btn.command === 'save')]) {
            me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
          }
          if (me.showDisable) {
            gMarkDisable = true
          } else {
            gMarkDisable = (me.editConfig.editStatus !== editStatus.ADD)
          }
          me.$nextTick(() => {
            let field = me.detailConfig.fields.find(p => p.key === 'gmark')
            if (field) {
              me.setDisable('gmark', gMarkDisable)
            }
          })
        }
      }
    },
    created() {
      let me = this
      // 选择模板
      me.$http.post(csAPI.returnManagement.feedingProducts.selectAll + '/I/1').then(res => {
        me.$set(me.cmbSource, 'templateHeadId', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.KEY
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'templateHeadId', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'templateHeadId')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    methods: {
      /**
       * 输入框
       * @returns {({itemClass: string, isCard: boolean, title: string, type: string, key: string}|{title: string, key: string}|{defaultValue: string, type: string, title: string, key: string, props: {disabled: boolean}}|{title: string, key: string, props: {maxlength: number}}|{title: string, key: string, props: {maxlength: number}})[]}
       */
      getFields() {
        return [{
          isCard: true,
          title: '表头信息',
          key: '1212121212121',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          required: true,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          key: 'ieMark',
          type: 'select',
          props: {
            disabled: true
          },
          defaultValue: 'I',
          title: '进出口标志'
        }, {
          key: 'gmark',
          type: 'select',
          required: true,
          title: '物料类型'
        }, {
          type: 'pcode',
          title: '启运港',
          key: 'despPort',
          props: {
            meta: 'PORT_LIN'
          }
        }, {
          type: 'pcode',
          title: '目的港',
          key: 'destPort',
          props: {
            meta: 'PORT_LIN'
          }
        }, {
          type: 'select',
          title: '选择模板',
          key: 'templateHeadId',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          }
        }, {
          key: 'note1',
          title: '备注1',
          props: {
            maxlength: 255
          },
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'note2',
          title: '备注2',
          props: {
            maxlength: 255
          },
          itemClass: 'dc-merge-1-4'
        }]
      },
      /**
       * 返回主界面
       */
      handleBack() {
        let me = this
        me.$emit('backToList')
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.$emit('onHeadSaved', {
            editData: res.data.data,
            editStatus: editStatus.EDIT
          })
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
