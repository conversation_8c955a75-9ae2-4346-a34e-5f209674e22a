import { namespace } from '@/project'
import NonProducts from './non-products/NonProductsMain'
import RepairProducts from './repair-products/RepairProductsMain'
import FeedingProducts from './feeding-products/FeedingProductsMain'
import ReturnExchange from './return-exchange-details/ReturnExchangeMain'
import ExchangeBalance from './exchange-balance-products/ExchangeBalanceMain'
import ReturnEManagementList from './return-e-management/return-e-management-list'
import ReturnIManagementList from './return-i-management/return-i-management-list'

export default [
  {
    path: '/' + namespace + '/returnManagement/FeedingProducts',
    name: 'FeedingProducts',
    meta: {
      title: '进料成品退换'
    },
    component: FeedingProducts
  },
  {
    path: '/' + namespace + '/returnManagement/NonProducts',
    name: 'NonProducts',
    meta: {
      title: '非保成品退运'
    },
    component: NonProducts
  },
  {
    path: '/' + namespace + '/returnManagement/RepairProducts',
    name: 'RepairProducts',
    meta: {
      title: '修理物品'
    },
    component: RepairProducts
  },
  {
    path: '/' + namespace + '/returnManagement/ReturnExchange',
    name: 'ReturnExchange',
    meta: {
      title: '退换明细'
    },
    component: ReturnExchange
  },
  {
    path: '/' + namespace + '/returnManagement/ExchangeBalance',
    name: 'ExchangeBalance',
    meta: {
      title: '成品退换平衡'
    },
    component: ExchangeBalance
  },
  {
    path: '/' + namespace + '/returnManagement/returnEManagementList',
    name: 'returnEManagementList',
    meta: {
      title: '退运出口管理'
    },
    component: ReturnEManagementList
  },
  {
    path: '/' + namespace + '/returnManagement/returnIManagementList',
    name: 'returnIManagementList',
    meta: {
      title: '退运进口管理'
    },
    component: ReturnIManagementList
  }
]
