import { formatDate } from '@/libs/datetime'
import { returnManagement } from '@/view/cs-common'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      { title: '单据内部编号', width: 150, key: 'emsListNo' },
      {
        title: '单据状态', width: 100, key: 'status',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.returnManagement.STATUS_MAP)
        }
      },
      {
        title: '进出口标志', width: 100, key: 'ieMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.returnManagement.I_E_MARK)
        }
      },
      { title: '备案号', width: 150, key: 'emsNo' },
      {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      },
      {
        title: '启运港', width: 130, key: 'despPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, '', 'PORT_LIN')
        }
      },
      {
        title: '目的港', width: 130, key: 'destPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, '', 'PORT_LIN')
        }
      },
      { title: '报关单号码', width: 200, key: 'entryNo' },
      { title: '备注1', width: 150, key: 'note1' },
      { title: '备注2', width: 150, key: 'note2' },
      { title: '制单员', width: 150, key: 'insertUser' },
      {
        title: '制单日期', width: 150, key: 'insertTime',
        render: (h, params) => {
          return h('span', formatDate(params.row.insertTime, 'yyyy-MM-dd'))
        }
      },
      { title: '企业料号', width: 150, key: 'facGNo' },
      { title: '商品名称', width: 200, key: 'copGName' },
      {
        title: '状态', width: 150, key: 'listStatus',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.returnManagement.LIST_STATUS)
        }
      },
      { title: '数量', width: 150, key: 'qty' },
      { title: '原核注清单号', width: 200, key: 'listNo' },
      { title: '原报关单号', width: 150, key: 'entryNos' },
      { title: '原报关单申报日期', width: 200, key: 'dDate' },
      { title: '申报单位', width: 150, key: 'unit' },
      { title: '申报单价', width: 150, key: 'decPrice' },
      { title: '申报总价', width: 150, key: 'totalPrice' },
      {
        title: '原目的国', width: 150, key: 'destinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],
      returnManagement: returnManagement
    }
  }
}

export {
  columns
}
