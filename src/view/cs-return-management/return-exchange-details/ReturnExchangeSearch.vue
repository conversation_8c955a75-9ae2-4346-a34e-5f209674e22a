<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem type="text" prop="emsListNo" label="单据内部编号">
        <XdoIInput v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="单据状态">
        <xdo-select v-model="searchParam.status" :options="returnManagement.STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="iEMark" label="进出口标志">
        <xdo-select v-model="searchParam.iEMark" :options="returnManagement.I_E_MARK"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="cmbSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="copGName" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.copGName"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { returnManagement } from '@/view/cs-common'

  export default {
    name: 'ReturnExchangeSearch',
    data() {
      return {
        searchParam: {
          emsListNo: '',
          iEMark: '',
          emsNo: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          entryNo: '',
          facGNo: '',
          copGName: ''
        },
        cmbSource: {
          emsNoList: []
        },
        returnManagement: returnManagement
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbSource.emsNoList = tmpArr
      })
    }
  }
</script>

<style scoped>
</style>
