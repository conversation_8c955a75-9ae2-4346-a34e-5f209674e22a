import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const columns = {
  mixins: [columnRender],
  data() {
    return {
      totalColumns: [{
        title: '企业料号', width: 150, key: 'facGNo'
      }, {
        title: '商品名称', width: 150, key: 'copGName'
      }, {
        title: '数量', width: 150, key: 'qty'
      }, {
        width: 120,
        key: 'unit',
        tooltip: true,
        ellipsis: true,
        align: 'center',
        title: '申报单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        title: '申报单价', width: 150, key: 'price'
      }, {
        title: '申报总价', width: 150, key: 'totalPrice'
      }, {
        width: 150,
        title: '原产国',
        key: 'originCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }
      }, {
        title: '目的国', width: 150, key: 'destinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, '', 'COUNTRY_OUTDATED')
        }
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }]
    }
  }
}
