<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="ieMark" label="进出口标志">
        <xdo-select v-model="searchParam.ieMark" :options="returnManagement.I_E_MARK"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="cmbSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange"></dc-dateRange>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { returnManagement } from '@/view/cs-common'

  export default {
    name: 'FeedingProductsSearch',
    data() {
      return {
        searchParam: {
          emsListNo: '',
          ieMark: '',
          emsNo: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          entryNo: '',
        },
        cmbSource: {
          emsNoList: []
        },
        returnManagement: returnManagement
      }
    },
    methods: {
      // 日期更改
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbSource.emsNoList = tmpArr
      }).catch(() => {
      })
    }
  }
</script>

<style scoped>
</style>
