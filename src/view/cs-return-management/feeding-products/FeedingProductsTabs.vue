<template>
  <section v-focus>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <Head ref="head" v-if="tabs.headTab" :edit-config="editConfig"
              :in-source="cmbSource" @onEditBack="editBack"></Head>
      </TabPane>
      <TabPane name="waitTab" v-if="showBody" label="待匹配表体" index="'2'">
        <WaitBody ref="wait" v-if="tabs.waitTab" :parent-config="parentConfig" @onEditBack="editBack" :operationStatus="editConfig.editStatus"></WaitBody>
      </TabPane>
      <TabPane name="matchedTab" v-if="showBody" label="已匹配表体" index="'3'">
        <MatchedTable ref="matched" v-if="tabs.matchedTab" :head-id="parentConfig.headId" :head-data="headData"
                      @onEditBack="editBack"></MatchedTable>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList">
          <XdoIcon type="ios-undo" size="22" style="color: green;"/>
        </XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './tabs/Head'
  import WaitBody from './tabs/WaitBody'
  import MatchedTable from './tabs/MatchedTable'
  import { baseTabsConfig } from '@/mixin/generic/baseTabsConfig'

  export default {
    name: 'FeedingProductsDetail',
    mixins: [baseTabsConfig],
    components: {
      Head,
      WaitBody,
      MatchedTable
    },
    watch: {
      // tab页切换
      tabName(value) {
        let me = this
        me.tabs[value] = true
        if (value === 'headTab') {
          me.$nextTick(() => {
          })
        } else if (value === 'waitTab') {
          me.$nextTick(() => {
            me.$refs['wait'].getList()
          })
        } else if (value === 'matchedTab') {
          me.$nextTick(() => {
            me.$refs['matched'].getList()
            // if (me.$refs['matchedTab']) {
            //   me.$refs['matchedTab'].loadAttach()
            // }
          })
        }
      }
    }
  }
</script>

<style scoped>
</style>
