<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <FeedingProductsSearch ref="headSearch"></FeedingProductsSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <FeedingProductsTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></FeedingProductsTabs>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { mainJS } from './js/feedingProductsMain'
  import { columns } from './js/feedingProductsColumns'
  import FeedingProductsTabs from './FeedingProductsTabs'
  import FeedingProductsSearch from './FeedingProductsSearch'

  export default {
    name: 'FeedingProductsMain',
    moduleName: '进料成品退换',
    components: {
      FeedingProductsTabs,
      FeedingProductsSearch
    },
    mixins: [columns, mainJS]
  }
</script>

<style scoped>
</style>
