<template>
  <section v-focus>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <RepairProductsHead ref="head" v-if="tabs.headTab" :edit-config="editConfig"
                         @onEditBack="editBack"
        ></RepairProductsHead>
      </TabPane>
      <TabPane name="waitTab" v-if="showBody" label="待匹配表体" index="'2'">
        <RepairProductsBodyWait ref="wait" v-if="tabs.waitTab" :parent-config="parentConfig" :operationStatus="editConfig.editStatus"
                                @onEditBack="editBack"></RepairProductsBodyWait>
      </TabPane>
      <TabPane name="matchedTab" v-if="showBody" label="已匹配表体" index="'3'">
        <RepairProductsBodyMatched ref="matched" v-if="tabs.matchedTab"  :parent-config="parentConfig"
                                   @onEditBack="editBack"></RepairProductsBodyMatched>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList">
          <XdoIcon type="ios-undo" size="22" style="color: green;"/>
        </XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import RepairProductsHead from './RepairProductsHeadEdit'
  import { baseTabsConfig } from '@/mixin/generic/baseTabsConfig'
  import RepairProductsBodyWait from './repairProductsBodyWait/RepairProductsBodyWaitMain'
  import RepairProductsBodyMatched from './repairProductsBodyMatched/RepairProductsBodyMatchedMain'

  export default {
    name: 'RepairProductsTabs',
    mixins: [baseTabsConfig],
    components: {
      RepairProductsHead,
      RepairProductsBodyWait,
      RepairProductsBodyMatched
    },
    watch: {
      // tab页切换
      tabName(value) {
        let me = this
        me.tabs[value] = true
        if (value === 'headTab') {
          me.$nextTick(() => {
          })
        } else if (value === 'waitTab') {
          me.$nextTick(() => {
            me.$refs['wait'].getList()
          })
        } else if (value === 'matchedTab') {
          me.$nextTick(() => {
            me.$refs['matched'].getList()
            // if (me.$refs['matchedTab']) {
            //   me.$refs['matchedTab'].loadAttach()
            // }
          })
        }
      }
    }
  }
</script>

<style scoped>
</style>
