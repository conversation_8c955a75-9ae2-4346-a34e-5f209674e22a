<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" rowSelection="multiple" :checkboxSelection="checkboxSelection"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { columns } from './js/repairProductsBodyMatchedColumns'
  import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

  export default {
    name: 'RepairProductsBodyMatchedMain',
    mixins: [columns, commColumnsCustom, pms],
    props: {
      parentConfig: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchLines: 0,   // 搜索行
        isShowOp: false,
        isShowSetting: true,
        p_group: 'bodyList',
        checkboxSelection: false,
        toolbarEventMap: {
          'export': this.handleDownload   // '导出'
        },
        gridConfig: {
          exportTitle: '修理物品已匹配表体数据'
        },
        ajaxUrl: {
          exportUrl: csAPI.returnManagement.repairProducts.body.exportUrl,
          selectAllPaged: csAPI.returnManagement.repairProducts.body.selectAllPaged
        }
      }
    },
    methods: {
      /**
       * 导出
       */
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      },
      /**
       * 获取列表页内容
       */
      getSearchParams() {
        let me = this
        return Object.assign({
          status: '1',
          headId: me.parentConfig.headId
        }, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}))
      }
    }
  }
</script>

<style scoped>
</style>
