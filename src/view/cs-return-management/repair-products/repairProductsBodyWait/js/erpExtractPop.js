import { csAPI } from '@/api'
import { ArrayToLocaleLowerCase, isNullOrEmpty } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const erpExtractPop = {
  name: 'erpExtractPop',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    /**
     * 传入的数据源
     */
    inSource: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        operationColumnShow: false
      },
      cmbSource: {
        clientCode: [],
        deliveryNo: []
      },
      pmsLevel: 'extract',
      toolbarEventMap: {
        'extract-select': this.handleExtractChoose
      }
    }
  },
  created: function () {
    let me = this
    // 客户
    me.$http.post(csAPI.ieParams.CLI).then(res => {
      me.$set(me.cmbSource, 'deliveryNo', [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)])
      me.$set(me.cmbSource, 'clientCode', [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)])
    }).catch(() => {
      me.$set(me.cmbSource, 'deliveryNo', [])
      me.$set(me.cmbSource, 'clientCode', [])
    }).finally(() => {
      me.searchFieldsReLoad('deliveryNo')
      me.searchFieldsReLoad('clientCode')
    })
  },
  computed: {
    /**
     * 动态数据源
     * @returns {*}
     */
    dynamicSource() {
      let me = this
      return {
        ...me.inSource,
        ...me.cmbSource
      }
    }
  },
  methods: {
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'billNo',
        title: '发货单号',
        itemClass: 'dc-merge-1-3'
      }, {
        type: 'select',
        title: '送达方',
        key: 'deliveryNo'
      }, {
        type: 'select',
        title: '售达方',
        key: 'clientCode'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'gname',
        title: '商品名称'
      }, {
        range: true,
        key: 'soDate',
        title: '销售订单日期'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 160,
        key: 'billNo',
        title: '发货单号'
      }, {
        width: 160,
        title: '单据号',
        key: 'linkedNo'
      }, {
        width: 120,
        key: 'lineNo',
        title: '单据序号'
      }, {
        width: 120,
        key: 'planLineNo',
        title: '计划行编码'
      }, {
        width: 120,
        key: 'ccetdDate',
        title: '订单订舱开船日',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '最终目的国',
        key: 'destinationCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.country_outdated)
        })
      }, {
        width: 160,
        title: '送达方',
        key: 'deliveryNo',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'deliveryNo', 'deliveryName')
        })
      }, {
        width: 160,
        title: '售达方',
        key: 'clientCode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'clientCode', 'clientName')
        })
      }, {
        width: 150,
        key: 'tradeTerms',
        title: '国际贸易条件',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.tradeTerms)
        })
      }, {
        width: 150,
        key: 'tradeArea',
        title: '国际贸易条件(部分2)'
      }, {
        width: 120,
        key: 'bondMark',
        title: '保完税标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmark)
        })
      }, {
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 200,
        key: 'gname',
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 160,
        key: 'qty',
        title: '数量'
      }, {
        width: 160,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 160,
        title: '总价',
        key: 'decTotal'
      }, {
        width: 120,
        key: 'unitErp',
        title: 'ERP交易单位'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        key: 'netWt',
        title: '净重'
      }, {
        width: 120,
        title: '毛重',
        key: 'grossWt'
      }, {
        width: 150,
        key: 'soNo',
        title: 'SO号(销售订单号)'
      }, {
        width: 150,
        key: 'soLineNo',
        title: 'SO行号(销售订单行号)'
      }, {
        width: 150,
        key: 'soDate',
        title: 'SO日期(销售订单日期)',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 110,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 120,
        title: '客户料号',
        key: 'customerGNo'
      }, {
        width: 120,
        title: '送达方PO号',
        key: 'deliveryPoNo'
      }, {
        width: 120,
        title: '送达方PO行号',
        key: 'deliveryPoLineNo'
      }, {
        width: 120,
        key: 'soldPoNo',
        title: '售达方PO号'
      }, {
        width: 120,
        key: 'soldPoLineNo',
        title: '售达方PO行号'
      }, {
        width: 100,
        title: 'ERP创建时间',
        key: 'lastModifyDate',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 250,
        title: '境内货源地',
        key: 'districtCode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.area)
        })
      }, {
        width: 120,
        key: 'marketOrderNo',
        title: '卖场订单DI PO'
      }, {
        width: 120,
        title: '台/箱',
        key: 'containerNum'
      }, {
        width: 160,
        title: '件数',
        key: 'packNum'
      }, {
        width: 150,
        title: '体积',
        key: 'volume'
      }, {
        width: 250,
        title: '拒绝原因',
        key: 'refusedReason',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 提取勾选
     */
    handleExtractChoose() {
      let me = this
      if (me.checkRowSelected('提取勾选')) {
        me.setToolbarLoading('extract-select', true)
        me.$http.post(me.ajaxUrl.extractByChoose + '/' + me.headId + '/' + me.listConfig.selectRows[0].sid).then(res => {
          if (!isNullOrEmpty(res.data.message)) {
            me.$Message.success('提取成功!')
          } else {
            me.$Message.warning(res.data.message)
          }
          me.handleClose()
          me.$emit('doSearch')
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('extract-select')
        })
      }
    }
  }
}
