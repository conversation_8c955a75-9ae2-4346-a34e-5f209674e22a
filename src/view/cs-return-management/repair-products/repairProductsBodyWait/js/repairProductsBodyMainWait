import ErpExtract from '../erp-extract'
import ErpExtractPop from '../erp-extract-pop'
import MatchCustomsData from '../MatchCustomsData'
import { editStatus, returnManagement } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import RepairProductsBodyDetail from '../RepairProductsBodyWaitDetail'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const mainJS = {
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    ErpExtract,
    ErpExtractPop,
    MatchCustomsData,
    RepairProductsBodyDetail
  },
  props: {
    /**
     * 表头数据
     */
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 传入的报关状态
     */
    operationStatus: {
      type: String,
      default: editStatus.SHOW
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        status:returnManagement.LIST_STATUS
      },
      pmsLevel: 'body',
      hasChildTabs: true,
      extractPopShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'extract': this.handleExtract,      // 提取
        'match': this.handleMatchCustoms,
        'setting': this.handleColumnSetup
      },
      // 出口提取弹出页
      erpExtractPop: {
        show: false
      },
      // 匹配报关弹出页
      matchCustomsData: {
        show: false
      },
      // 进口提取弹出页
      erpExtract: {
        show: false
      }
    }
  },
  mounted: function () {
    let me = this
    // 报关状态与查看状态 只允许查看
    this.listConfig.disable = me.operationStatus === editStatus.SHOW || me.parentConfig.headData.status === '2'
  },
  methods: {
    /**
     * 过滤查看与报关状态下的功能按钮
     */
    actionLoaded() {
      let me = this
      me.loadFunctions('body').then(() => {
        if (me.listConfig.disable) {
          me.actions = me.actions.filter(item => item.command === 'setting')
        }
      })
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck(row, '编辑')) {
        me.$set(me.editConfig, 'editData', row)
        me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        me.$set(me, 'showList', false)
      }
    },
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGName',
        title: '商品名称'
      }, {
        key: 'status',
        title: '状态',
        type: 'select',
        defaultValue: '0'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.parentConfig.headId
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 200,
        key: 'copGName',
        title: '商品名称'
      }, {
        width: 150,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.cmbSource.status)
        })
      }, {
        width: 150,
        key: 'qty',
        title: '数量',
        type: 'number'
      }, {
        width: 150,
        key: 'unit',
        title: '单位',
        tooltip: true,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 150,
        key: 'price',
        title: '单价'
      }, {
        width: 150,
        title: '目的国',
        key: 'destinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 150,
        title: '销退订单号',
        key: 'returnOrderNo'
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 数据提取界面显示
     */
    handleExtract() {
      let me = this
      if (me.parentConfig.headData.ieMark === 'E'){
        me.$set(me.erpExtractPop, 'show', true)
      } else if (me.parentConfig.headData.ieMark === 'I'){
        me.$set(me.erpExtract, 'show', true)
      }
    },
    afterImport() {
      this.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 匹配报关数据
     */
    handleMatchCustoms() {
      let me = this
      if (me.checkRowSelected('匹配报关数据', true)) {
        me.matchCustomsData.show = true
      }
    }
  }
}
