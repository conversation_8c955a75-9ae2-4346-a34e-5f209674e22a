<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts='pageSizeOpts'
                   :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ErpExtractPop :show.sync="erpExtractPop.show" :in-source="cmbSource" :head-id="parentConfig.headData.sid" @import:success="afterImport" @doSearch="getList"></ErpExtractPop>
    <ErpExtract :show.sync="erpExtract.show" :head-id="parentConfig.headData.sid"  iemark="I" @import:success="afterImport"></ErpExtract>
    <MatchCustomsData :configData="listConfig.selectRows[0]" :show.sync="matchCustomsData.show" @import:success="afterImport"></MatchCustomsData>
    <RepairProductsBodyDetail v-if="!showList" :edit-config="editConfig" :parent-config="parentConfig"
                              @onEditBack="editBack"></RepairProductsBodyDetail>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { mainJS } from './js/repairProductsBodyMainWait'

  export default {
    name: 'RepairProductsBodyWaitMain',
    mixins: [mainJS],
    data() {
      return {
        ajaxUrl: {
          deleteUrl: csAPI.returnManagement.repairProducts.body.delete,
          selectAllPaged: csAPI.returnManagement.repairProducts.body.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
