import RepairProductsTabs from '../RepairProductsTabs'
import { editStatus, returnManagement } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const mainJS = {
  name: 'nonProductsMainJS',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    RepairProductsTabs
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        exportTitle: '修理物品'
      },
      cmbSource: {
        ieMark: returnManagement.I_E_MARK
      },
      returnManagement: returnManagement,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleColumnSetup,
        'build-bill2': this.handleCustomsGenerated  // 生成报关数据
      }
    }
  },
  methods: {
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'ieMark',
        type: 'select',
        title: '进出口标志'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj.type = '3'
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 88,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.returnManagement.STATUS_MAP)
        })
      }, {
        width: 110,
        key: 'ieMark',
        title: '进出口标志',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.returnManagement.I_E_MARK)
        })
      }, {
        width: 200,
        title: '启运港',
        key: 'despPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, '', 'PORT_LIN')
        })
      }, {
        width: 200,
        title: '目的港',
        key: 'destPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, '', 'PORT_LIN')
        })
      }, {
        width: 150,
        key: 'agentCode',
        title: '报关单申报单位'
      }, {
        width: 110,
        key: 'tradeMode',
        title: '监管方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 150,
        title: '担保方式',
        key: 'assureType'
      }, {
        width: 150,
        title: '担保金额',
        key: 'assureAmount'
      }, {
        width: 200,
        key: 'note1',
        title: '备注1'
      }, {
        width: 200,
        key: 'note2',
        title: '备注2'
      }, {
        width: 150,
        title: '制单员',
        key: 'insertUser'
      }, {
        width: 88,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (me.extendEditCheck(row, '编辑')) {
        if (row.status === '2') {
          me.$Message.warning('“已生成报关”状态下数据不允许再编辑')
        } else {
          me.$set(me.editConfig, 'editData', row)
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
          me.$set(me, 'showList', false)
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      if (me.checkRowSelected('删除',)) {
        if (me.listConfig.selectRows[0].status === '2') {
          me.$Message.warning('“已生成报关”状态下数据不允许再删除')
        } else {
          me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
        }
      }
    },
    /**
     * 生成报关数据
     */
    handleCustomsGenerated() {
      let me = this
      if (me.checkRowSelected('生成报关数据', true)) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '确认生成所选项的报关数据吗',
          onOk: () => {
            me.setToolbarLoading('build-bill2', true)
            me.$http.post(me.ajaxUrl.customsGenerated + '/' + me.listConfig.selectRows[0].sid).then(() => {
              me.$Message.success('生成报关数据成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('build-bill2')
            })
          }
        })
      }
    }
  }
}
