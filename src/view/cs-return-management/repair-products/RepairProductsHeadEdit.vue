<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseForm } from '@/mixin/generic/form/baseForm'
  import { editStatus, returnManagement } from '@/view/cs-common'

  export default {
    name: 'RepairProductsHeadEdit',
    mixins: [baseForm],
    data() {
      let fields = this.getFields()
      return {
        selfFields: fields,
        cmbSource: {
          templateHeadId: [],
          ieMark: returnManagement.I_E_MARK
        },
        ajaxUrl: {
          insert: csAPI.returnManagement.repairProducts.insert,
          update: csAPI.returnManagement.repairProducts.update,
          selectAll: csAPI.returnManagement.feedingProducts.selectAll
        }
      }
    },
    watch: {
      // 进出口标志
      'detailConfig.model.ieMark': function(value) {
        let me = this,
          needClear = false
        if (isNullOrEmpty(value)) {
          needClear = true
          value = 'I'
        }
        me.$http.post(`${me.ajaxUrl.selectAll}/${value}/1`).then(res => {
          if (needClear) {
            me.$set(me.cmbSource, 'templateHeadId', [])
          } else {
            me.$set(me.cmbSource, 'templateHeadId', res.data.data.map(item => {
              return {
                label: item.VALUE,
                value: item.VALUE
              }
            }))
          }
        }).catch(() => {
          me.$set(me.cmbSource, 'templateHeadId', [])
        }).finally(() => {
          let field = me.detailConfig.fields.find(p => p.key === 'templateHeadId')
          if (field) {
            me.fieldOptimization(field)
          }
        })
      }
    },
    mounted: function() {
      let me = this
      me.fieldsReorganization()
    },
    methods: {
      /**
       * 获取必填字段(数组)
       * @returns {Array}
       */
      getRequiredFields() {
        return ['ieMark']
      },
      getFields() {
        let me = this
        return [{
          key: 'emsListNo',
          title: '单据内部编号',
          props: {
            disabled: me.editConfig.editStatus === editStatus.EDIT
          }
        }, {
          key: 'ieMark',
          type: 'select',
          title: '进出口标志',
        }, {
          type: 'select',
          title: '选择模板',
          key: 'templateHeadId',
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          }
        }, {
          key: 'despPort',
          title: '启运港',
          props: {
            maxlength: 100
          },
        }, {
          key: 'destPort',
          title: '目的港',
          props: {
            maxlength: 100
          }
        }, {
          title: '担保方式',
          key: 'assureType',
          props: {
            maxlength: 225
          }
        }, {
          title: '担保金额',
          key: 'assureAmount',
          type: 'xdoInput',
          props: {
            decimal: true, intLength: "16", precision: "5", notConvertNumber: true
          }
        }, {
          title: '备注1',
          key: 'note1',
          props: {
            maxlength: 225
          }
        }, {
          title: '备注2',
          key: 'note2',
          props: {
            maxlength: 225
          }
        }]
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.doSave(function(res) {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          me.$set(me.detailConfig.model, 'emsListNo', res.data.data['emsListNo'])
        })
      }
    }
  }
</script>

<style scoped>
</style>
