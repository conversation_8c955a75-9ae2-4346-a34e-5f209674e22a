import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const matchDecDataColumns = {
  mixins: [columnRender],
  data() {
    let me = this
    return {
      totalColumns: [{
        width: 150,
        key: 'kppQty',
        title: '可匹配数量'
      }, {
        width: 150,
        key: 'qty',
        editable: true,
        title: '本次匹配数量',
        onCellValueChanged: me.onCellValueChanged
      }, {
        width: 150,
        key: 'syQty',
        title: '剩余数量'
      }, {
        width: 150,
        key: 'listNo',
        title: '核注清单编号'
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 150,
        key: 'dDate',
        title: '报关单申报日期'
      }, {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      }, {
        width: 120,
        key: 'unit',
        title: '单位',
        tooltip: true,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 150,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 120,
        title: '目的国',
        key: 'destinationCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
        }
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }]
    }
  },
  methods: {
    /**
     * 行内编辑 本次匹配数量
     * @param event
     */
    onCellValueChanged(event) {
      let me = this,
        // 当前行的下标
        tempIndex = me.gridConfig.data.findIndex(item => {
          return item.sid === event.data.sid
        })
      if (!event.data.qty || !/^\d*\.?\d*$/.test(event.data.qty.toString())) {
        me.$Message.warning('请输入有效的数据')
        // 把值重置到正确的值
        me.gridConfig.data[tempIndex].qty = Number(event.oldValue)
        return
      }
      // 计算 剩余
      me.gridConfig.data[tempIndex].syQty = Number(event.data.syQty) + Number(event.oldValue) - Number(event.newValue)
      // 处理 重绘问题
      const obj = me.gridConfig.data
      me.gridConfig.data = []
      me.$nextTick(function () {
        me.gridConfig.data = obj
        //页面刷新勾选框
        me.$nextTick(function () {
          me.gridApi.forEachNode((node) => {
            let isSelect = me.gridConfig.selectRows.filter(x => x.sid && x.sid === node.data.sid)
            if (isSelect && isSelect.length > 0) {
              node.setSelected(true, false)
            }
          })
        })
      })
    }
  }
}
