import ToBeMatchedEEdit from '../to-be-matched-e-edit'
import { editStatus, returnManagement } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import MatchDecDataPop from '@/view/cs-return-management/return-e-management/components/match-dec-data-pop'

export const toBeMatchedEList = {
  name: 'toBeMatchedEList',
  components: {
    MatchDecDataPop,
    ToBeMatchedEEdit
  },
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    /**
     * 传入的编辑信息
     */
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        // checkColumnShow: false,
        // operationColumnShow: false
      },
      // 匹配报关数据
      matchDecData: {
        data: {},
        show: false
      },
      hasChildTabs: true,
      pmsLevel: 'toBeMatched',
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup,
        'match-dec-data': this.handleMatchDecData
      },
      cmbSource: {
        status: returnManagement.LIST_STATUS,
        bondMark: returnManagement.bondedFlagList
      }
    }
  },
  watch: {
    'parentConfig.editData.sid': {
      immediate: true,
      handler: function () {
        this.handleSearchSubmit()
      }
    }
  },
  methods: {
    /**
     * 操作按钮(特殊)设置
     */
    actionLoaded() {
      let me = this
      if (me.parentConfig.editStatus !== editStatus.ADD
        && me.parentConfig.editStatus !== editStatus.EDIT) {
        me.actions = me.actions.filter(item => ['export', 'setting'].includes(item.command))
      }
    },
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{defaultValue: string, title: string, type: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGName',
        title: '商品名称'
      }, {
        key: 'status',
        title: '状态',
        type: 'select',
        defaultValue: '0'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.parentConfig.editData.sid
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, type: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 150,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 200,
        key: 'copGName',
        title: '商品名称'
      }, {
        width: 150,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.cmbSource.status)
        })
      }, {
        width: 150,
        key: 'qty',
        title: '数量',
        type: 'number'
      }, {
        width: 120,
        key: 'unit',
        title: '单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.unit)
        })
      }, {
        width: 150,
        key: 'price',
        title: '单价'
      }, {
        width: 150,
        title: '目的国',
        key: 'destinationCountry',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.country_outdated)
        })
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 150,
        title: '销退订单号',
        key: 'returnOrderNo'
      }]
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param row
     * @param opTitle
     */
    extendEditCheck(row, opTitle) {
      let me = this
      if (row.status === '1') {
        me.$Message.warning('状态为“已匹配”时不能' + opTitle + '!')
        return false
      }
      return true
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 匹配报关数据
     */
    handleMatchDecData() {
      let me = this
      if (me.checkRowSelected('匹配报关数据', true)) {
        me.$set(me.matchDecData, 'data', me.listConfig.selectRows[0])
        me.$set(me.matchDecData, 'show', true)
      }
    }
  }
}
