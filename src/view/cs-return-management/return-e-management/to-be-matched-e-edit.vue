<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110" class="dc-form-4"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 4px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus, returnManagement } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'toBeMatchedEEdit',
    mixins: [baseDetailConfig],
    props: {
      /**
       * 传入的编辑信息
       */
      parentConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        formName: 'frmData',
        cmbSource: {
          facGNo: [],
          status: returnManagement.LIST_STATUS
        },
        ajaxUrl: {
          // 获取料件信息
          getIeInfo: csAPI.csMaterielCenter.bonded.getIeInfo,
          insert: csAPI.returnManagement.returnEManagement.toBeMatched.insert,
          update: csAPI.returnManagement.returnEManagement.toBeMatched.update
        },
        buttons: [
          {...btnComm, label: '保存', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.fieldsReset()
          if (me.buttons[me.buttons.findIndex(btn => btn.command === 'save')]) {
            me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
          }
        }
      },
      'parentConfig.editData.gmark': {
        immediate: true,
        handler: function () {
          let me = this
          me.facGNoLoad()
        }
      }
    },
    methods: {
      facGNoLoad() {
        let me = this
        // 企业料号
        me.$http.post(csAPI.returnManagement.feedingProducts.getFacGNolist, {
          bondedFlag: '1',
          gMark: me.parentConfig.editData['gmark']
        }).then(res => {
          me.$set(me.cmbSource, 'facGNo', res.data.data.map(item => {
            return {
              value: item,
              label: item
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'facGNo', [])
        }).finally(() => {
          let field = me.detailConfig.fields.find(p => p.key === 'facGNo')
          if (field) {
            me.fieldOptimization(field)
          }
        })
      },
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded() {
        let me = this
        me.$nextTick(() => {
          me.setDisable('qty', me.newDisable)
          me.setDisable('facGNo', me.newDisable)
          // me.setDisable('copGName', me.newDisable)
        })
      },
      /**
       * 获取必填字段(数组)
       * @returns {Array}
       */
      getRequiredFields() {
        return ['facGNo', 'qty']
      },
      /**
       * 根据企业料号获取改料号信息
       */
      onFacGNoChange() {
        let me = this
        me.$http.post(me.ajaxUrl.getIeInfo, {
          bondedFlag: '1',
          facGNo: me.detailConfig.model.facGNo,
          emsNo: me.parentConfig.editData.emsNo,
          gMark: me.parentConfig.editData['gmark']
        }).then(res => {
          me.$set(me.detailConfig.model, 'copGName', res.data.data.gname)
        }).catch(() => {
          me.$set(me.detailConfig.model, 'copGName', '')
        })
      },
      /**
       * 输入框
       * @returns {({itemClass: string, isCard: boolean, title: string, type: string, key: string}|{type: string, title: string, key: string, props: {optionLabelRender: (function(*): *), options: []}}|{title: string, key: string}|{title: string, type: string, key: string}|{title: string, key: string})[]}
       */
      getFields() {
        let me = this
        return [{
          isCard: true,
          key: '1212121212121',
          title: '待匹配表体信息',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-5'
        }, {
          key: 'facGNo',
          type: 'select',
          required: true,
          title: '企业料号',
          on: {
            change: me.onFacGNoChange
          },
          props: {
            options: [],
            optionLabelRender: (opt) => opt.label
          }
        }, {
          required: true,
          key: 'copGName',
          title: '商品名称',
          props: {
            disabled: true
          }
        }, {
          key: 'qty',
          title: '数量',
          required: true,
          props: {
            intDigits: 11,
            precision: 5
          },
          type: 'xdoInput'
        }, {
          key: 'unit',
          title: '单位'
        }, {
          key: 'price',
          title: '单价',
          props: {
            intDigits: 8,
            precision: 5
          },
          type: 'xdoInput'
        }, {
          type: 'pcode',
          title: '目的国',
          key: 'destinationCountry',
          props: {
            meta: 'COUNTRY_OUTDATED'
          }
        }, {
          key: 'curr',
          title: '币制',
          type: 'pcode',
          props: {
            meta: 'CURR_OUTDATED'
          }
        }, {
          title: '销退订单号',
          key: 'returnOrderNo'
        }]
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'headId', me.parentConfig.editData.sid)
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
    background-color: rgb(247, 247, 247);
    border-top: 1px solid rgb(232, 234, 236);
  }
</style>
