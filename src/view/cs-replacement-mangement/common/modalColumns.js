import { getKeyValue } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'emsListNo'
  , 'declareDate'
  , 'qty'
  , 'unit'
  , 'decPrice'
  , 'decTotal'
  , 'curr'
  , 'entryNo'
]
const iemarkColumns = [
  'listNo'
  , 'tradeMode'
  , 'gname'
  , 'codeTS'
]
const columnsConfigP = [
  ...commColumns,
  ...iemarkColumns
]

const excelColumnsConfigP = [
  ...commColumns,
  ...iemarkColumns
]
const columnsConfigM = [
  ...commColumns,
  ...iemarkColumns
]
const excelColumnsConfigM = {
  ...commColumns,
  ...iemarkColumns
}
const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '单据内部编号',
          minWidth: 120,
          align: 'center',
          key: 'emsListNo',
        },
        {
          title: '申报日期',
          minWidth: 120,
          align: 'center',
          key: 'declareDate',
        },
        {
          title: '进出口报关单',
          minWidth: 120,
          align: 'center',
          key: 'entryNo',
        },
        {
          title: '数量',
          minWidth: 120,
          align: 'center',
          key: 'qty',
        },
        {
          title: '申报计量单位',
          minWidth: 120,
          align: 'center',
          key: 'unit',
        },
        {
          title: '单价',
          minWidth: 120,
          align: 'center',
          key: 'decPrice',
        },
        {
          title: '总价',
          minWidth: 120,
          align: 'center',
          key: 'decTotal',
        },
        {
          title: '币制',
          minWidth: 120,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
      ],
      ieColumns: [
        {
          title: '核注清单编号',
          minWidth: 120,
          align: 'center',
          key: 'listNo',
        },
        {
          title: '监管方式',
          minWidth: 120,
          align: 'center',
          key: 'tradeMode',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
          }
        },
        {
          title: '商品名称',
          minWidth: 120,
          align: 'center',
          key: 'gname',
        },
        {
          title: '商品编码',
          minWidth: 120,
          align: 'center',
          key: 'codeTS',
        }
      ]
    }
  }
}

export {
  columnsConfigP,
  excelColumnsConfigP,
  columnsConfigM,
  excelColumnsConfigM,
  columns
}
