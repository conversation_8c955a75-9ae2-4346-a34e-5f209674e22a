import { getKeyValue } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
   'tradeMode'
  , 'emsNo'
  , 'facGNo'
  , 'gno'
  , 'copGNo'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'returnESum'
  , 'returnISum'
  , 'remain'
]

const columnsConfig = [
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '监管方式',
          minWidth: 120,
          align: 'center',
          key: 'tradeMode',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
          }
        },
        {
          title: '备案号',
          minWidth: 120,
          align: 'center',
          key: 'emsNo',
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo',
        },
        {
          title: '备案序号',
          minWidth: 120,
          align: 'center',
          key: 'gno',
        },
        {
          title: '备案料号',
          minWidth: 120,
          align: 'center',
          key: 'copGNo',
        },
        {
          title: '商品编码',
          minWidth: 120,
          align: 'center',
          key: 'codeTS',
        },
        {
          title: '商品名称',
          minWidth: 120,
          align: 'center',
          key: 'gname',
        },
        {
          title: '申报规格型号',
          minWidth: 120,
          align: 'center',
          key: 'gmodel',
        },
        {
          title: '退换出口总数量',
          minWidth: 120,
          align: 'center',
          key: 'returnESum',
          render: (h, params) => {
            return h('a', {
              props: {
                size: 'small',
                type: 'primary'
              },
              on: {
                click: () => {
                  this.handleEditByRow(params.row, 'E')
                }
              }
            }, params.row.returnESum)
          }
        },
        {
          title: '退换进口总数量',
          minWidth: 120,
          align: 'center',
          key: 'returnISum',
          render: (h, params) => {
            return h('a', {
              props: {
                size: 'small',
                type: 'primary'
              },
              on: {
                click: () => {
                  this.handleEditByRow(params.row, 'I')
                }
              }
            }, params.row.returnISum)
          }
        },
        {
          title: '剩余数量',
          minWidth: 120,
          align: 'center',
          key: 'remain',
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
