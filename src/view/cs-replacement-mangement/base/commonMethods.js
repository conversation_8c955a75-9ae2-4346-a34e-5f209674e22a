import { getColumnsByConfig } from '@/common'
import { columnsConfig } from './../common/replacementColumns'
import { getGridExportColumns } from '../../cs-common/function'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const commonmethods = {
  mixins: [commList],
  data() {
    return {
      iemark: '',
      model: false,
      modalData: [],
      columnsVal: {},
      modalTitle: '',
      actions: [{
        type: 'text',
        needed: true,
        label: '导出',
        loading: false,
        disabled: false,
        command: 'export',
        key: 'xdo-btn-open',
        click: this.exportExcel,
        icon: 'ios-cloud-download-outline'
      }, {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting',
        key: 'xdo-btn-export',
        click: this.handleTableColumnSetup
      }]
    }
  },
  methods: {
    handleShowSearch() {
      let me = this
      me.showSearch = !me.showSearch
      me.refreshDynamicHeight(120, !me.showSearch ? ["area_search"] : null)
    },
    handleSearchSubmit() {
      let me = this
      me.$http.post(me.ajaxUrl.getInforDetail, me.$refs.headSearch.searchParam, {
        params: {
          page: me.pageParam.page,
          limit: me.pageParam.limit
        }
      }).then(res => {
        if (res.data.total !== 0) {
          me.gridConfig.data = res.data.data
          me.pageParam.dataTotal = res.data.total
        } else {
          me.model = true
          me.modalData = me.sliceArr(res.data.data)
          me.gridConfig.data = me.modalData[me.pageParam.page - 1]
          me.pageParam.dataTotal = res.data.data.length
        }
      })
    },
    //分页触发
    pageChange(val) {
      let me = this
      me.pageParam.page = val
      if (!me.model) {
        me.handleSearchSubmit()
      } else {
        me.gridConfig.data = me.modalData[me.pageParam.page - 1]
      }
    },
    pageSizeChange(val) {
      let me = this
      me.pageParam.limit = val
      if (me.pageParam.page === 1) {
        me.handleSearchSubmit()
        me.model = false
      }
    },
    //数组切割
    sliceArr(data) {
      let num = 0,
        _data = [],
        proportion = this.pageParam.limit //按照比例切割
      for (let i = 0; i < data.length; i++) {
        if (i % proportion === 0 && i !== 0) {
          _data.push(data.slice(num, i))
          num = i
        }
        if ((i + 1) === data.length) {
          _data.push(data.slice(num, (i + 1)))
        }
      }
      return _data
    },
    /**
     * 自定义列表
     */
    handleTableColumnSetup() {
      let me = this
      me.showtableColumnSetup = true
    },
    handleUpdateColumn(columns) {
      let me = this
      // 解决iview table 的问题
      me.tableShow = false
      me.$nextTick(() => {
        me.tableShow = true
      })
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
      me.gridConfig.exportColumns = []
      getGridExportColumns(me.alltotalColumns).forEach(item => {
        if (item.key !== 'selection' && item.key !== 'operation') {
          me.gridConfig.exportColumns.push(item)
        }
      })
    },
    handleEditByRow(val, ieMark) {
      let me = this
      me.columnsVal = val
      if (ieMark === 'I') {
        me.modalTitle = '退换进口总数量'
      } else if (ieMark === 'E') {
        me.modalTitle = '退换出口总数量'
      }
      me.showModal = true
      me.iemark = ieMark
    },
    exportExcel() {
      let me = this
      me.doExport(me.ajaxUrl.exportInfor, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
