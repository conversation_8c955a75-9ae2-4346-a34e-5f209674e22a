<template>
  <section>
    <div v-show="showHead">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ReplacementSearch ref="headSearch" :typeData="typeData"></ReplacementSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                    style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </Button>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" v-if="tableShow" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
    <XdoModal ref="ddd" v-model="showModal" width="800" :title='modalTitle' :footer-hide="true" :mask-closable="false">
      <ReplaceModalList v-if="showModal" :gmark="this.$refs.headSearch.searchParam.gmark" :iemark="iemark" :columnsval="columnsVal"></ReplaceModalList>
    </XdoModal>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { commonmethods } from '../base/commonMethods'
  import ReplaceModalList from '../common/replaceModalList'
  import ReplacementSearch from '../common/replacementSearch'
  import { dynamicHeight, getColumnsByConfig } from '@/common'
  import { getGridExportColumns } from '../../cs-common/function'
  import { columnsConfig, columns } from './../common/replacementColumns'

  export default {
    name: 'MaterialReplacementList',
    components: {
      ReplaceModalList,
      ReplacementSearch
    },
    mixins: [dynamicHeight, columns, commonmethods],
    data() {
      return {
        typeNo: '',
        tableId: '',
        typeData: '',
        searchData: {},
        columnsarr: [],
        showHead: true,
        tableShow: true,
        showModal: false,
        showSearch: false,
        columnsConfig: [],
        alltotalColumns: [],
        showtableColumnSetup: false,
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          gridColumns: []
        },
        pageParam: {
          page: 1,
          limit: 10,
          dataTotal: 0,
          pageSizeOpts: [10, 20, 30, 40, 50]
        },
        ajaxUrl: {
          exportInfor: csAPI.replacementApi.exportInfo,
          getInforDetail: csAPI.replacementApi.getInforList
        }
      }
    },
    mounted() {
      let me = this
      me.typeData = 'I'
      me.$set(me.$refs.headSearch.searchParam, 'gmark', 'I')
      me.handleSearchSubmit()
      me.tableId = me.$route.path
      let columns = me.$bom3.showTableColumns(me.tableId, me.totalColumns)
      me.alltotalColumns = [...me.getDefaultColumns(), ...columns]
      me.gridConfig.gridColumns = getColumnsByConfig(me.alltotalColumns, columnsConfig)
      getGridExportColumns(me.alltotalColumns).forEach(item => {
        if (item.key !== 'selection' && item.key !== 'operation') {
          me.gridConfig.exportColumns.push(item)
        }
      })
      me.gridConfig.exportTitle = '料件退换'
      me.refreshDynamicHeight(120, !me.showSearch ? ["area_search"] : null)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
