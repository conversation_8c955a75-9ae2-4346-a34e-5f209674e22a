<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <MaterialForClassifiedSearch ref="headSearch" :fixed-values="fixedValues" :cmb-source="cmbSource"></MaterialForClassifiedSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                    @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</Button>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <MaterialForClassifiedEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></MaterialForClassifiedEdit>
    <TableColumnSetup v-model="filterSetupShow" :resId="filterId" :columns="filterMaterialsColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import pms from "@/libs/pms";
  import { isNumber } from "@/libs/util"
  import { getColumnsByConfig,  } from '@/common'
  import { materialForClassifiedBaseList } from '../js/materialForClassifiedBaseList'
  import { filterMaterialColumns, columns } from '../js/materialForClassifiedColumns'

  export default {
    name: 'FilterMaterialList',
    mixins: [pms,materialForClassifiedBaseList, columns],
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        searchLines: 4,
        filterId:'',
        filterSetupShow:false,
        filterMaterialsColumns:[],
        fixedValues: {
          gmark: '',
          bondMark: '2'
        },
        gridConfig: {
          exportTitle: '过滤物料'
        },
        // toolbarEventMap: {
        //   'settings':this.handleTableColumnSetup
        // },
        ajaxUrl: {
          restore: csAPI.csMaterialForClassified.filterMaterial.restore,
          exportUrl: csAPI.csMaterialForClassified.filterMaterial.exportUrl,
          deleteUrl: csAPI.csMaterialForClassified.filterMaterial.deleteUrl,
          selectAllPaged: csAPI.csMaterialForClassified.filterMaterial.selectAllPaged,
        },
        actions: [
          {...btnComm, label: '导出', key: 'xdo-btn-download', icon: 'ios-cloud-download-outline', click: this.handleDownload},
          {...btnComm, label: '删除', key: 'xdo-btn-delete', icon: 'ios-trash-outline', click: this.handleDelete},
          {...btnComm, label: '恢复', key: 'xdo-btn-download', icon: 'ios-undo', click: this.handleRestore},
          {...btnComm, label: '自定义设置', key: 'xdo-btn-setting', icon: 'ios-settings', click: this.handleTableColumnSetup},
        ]
      }
    },
    // mounted: function () {
    //   let me = this
    //   me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, filterMaterialColumns)
    //   me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, filterMaterialExcelColumns)
    // },
    created:function (){
      let me = this
      me.filterId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'filterMaterialsColumns', getColumnsByConfig(me.totalColumns, filterMaterialColumns))
      let columns = me.$bom3.showTableColumns(me.filterId, me.filterMaterialsColumns)
      me.handleUpdateColumn(columns)
    },
    methods: {
      handleTableColumnSetup() {
        this.filterSetupShow = true
      },
      /**
       * 保存设置列
       * @param columns
       */
      handleUpdateColumn(columns){
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(),...columns]
        me.gridConfig.exportColumns= columns.map(columns=>{
          return{
            key:columns.key,
            value:columns.title
          }
        })
      },
      /**
       * 获取查询条件
       */
      getSearchParams() {
        let me = this
        let params = JSON.parse(JSON.stringify(Object.assign((me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}), {
          status: '1'
        })))
        if (isNumber(params.handleMark)) {
          params.handleMark = Number(params.handleMark)
        }
        return params
      },
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, 0)
      },
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.deleteUrl, 1)
      },
      handleRestore() {
        let me = this
        me.doSetStatus('恢复', me.ajaxUrl.restore, '0', 2)
      },
      operationEditShow() {
        return 'none'
      }
    }

  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
