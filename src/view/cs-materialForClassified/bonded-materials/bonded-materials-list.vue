<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <MaterialForClassifiedSearch ref="headSearch" :fixed-values="fixedValues" :cmb-source="cmbSource"></MaterialForClassifiedSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:import>
              <Dropdown trigger="click">
                <XdoButton type="text" style="font-size: 12px; width: 95px;">
                  <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>导入<XdoIcon type="ios-arrow-down"></XdoIcon>
                </XdoButton>
                <DropdownMenu slot="list">
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadOrdinary">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>普通导入
                    </XdoButton>&nbsp;
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>修改导入
                    </XdoButton>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <MaterialForClassifiedEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                               :fixed-values="fixedValues" :cmb-source="cmbSource"></MaterialForClassifiedEdit>
    <ImportPage :importKey="importKey" :importShow.sync="importShowNormal" :importConfig="importConfig" @onImportSuccess="onAfterImport"></ImportPage>
    <ImportPage :importKey="updateImportKey" :importShow.sync="importShowModify" :importConfig="updateImportConfig" @onImportSuccess="onAfterImport"></ImportPage>
    <MaterialsMergeList :show.sync="marchShow" gmark="I" :head-ids="headIds" @march:success="onAfterImport"></MaterialsMergeList>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="bondedMaterialsColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { materialForClassifiedList } from '../js/materialForClassifiedList'
  import { bondedMaterialsColumns, columns } from '../js/materialForClassifiedColumns'

  export default {
    name: 'BondedMaterialsList',
    mixins: [materialForClassifiedList, columns],
    data() {
      let normalImportConfig = this.getCommImportConfig('CLASSIFY-IMG'),
        updateImportConfig = this.getCommImportConfig('CLASSIFY-IMG-UPDATE', {
          gmark: 'I',
          bondMark: '0',
          moduleType: 'classifyImgUpdate'
        })
      return {
        searchLines: 4,
        showtableColumnSetup: false,
        tableId:'',
        bondedMaterialsColumns: [],
        fixedValues: {
          gmark: 'I',
          bondMark: '0'
        },
        gridConfig: {
          exportTitle: '保税料件待归类'
        },
        convertLabel: '非保物料',
        // 以下为导入配置信息
        importKey: 'CLASSIFY-IMG',
        importConfig: normalImportConfig,
        updateImportKey: 'CLASSIFY-IMG-UPDATE',
        updateImportConfig: updateImportConfig,
        toolbarEventMap: {
          'table-column-setup':this.handleTableColumnSetup
        },
        ajaxUrl: {
          delete: csAPI.csMaterialForClassified.bondedMaterials.delete,
          getMatch: csAPI.csMaterialForClassified.bondedMaterials.getMatch,
          exportUrl: csAPI.csMaterialForClassified.bondedMaterials.exportUrl,
          addSerialNo: csAPI.csMaterialForClassified.bondedMaterials.addSerialNo,
          sendToFilter: csAPI.csMaterialForClassified.bondedMaterials.sendToFilter,
          getExtraction: csAPI.csMaterialForClassified.bondedMaterials.getExtraction,
          bondedConvert: csAPI.csMaterialForClassified.bondedMaterials.bondedConvert,
          selectAllPaged: csAPI.csMaterialForClassified.bondedMaterials.selectAllPaged
        }
      }
    },
    // mounted: function () {
    //   this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, bondedMaterialsColumns)
    //   this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, bondedMaterialsExcelColumns)
    // },
    created:function (){
      let me = this
      me.tableId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'bondedMaterialsColumns', getColumnsByConfig(me.totalColumns, bondedMaterialsColumns))
      let columns = me.$bom3.showTableColumns(me.tableId, me.bondedMaterialsColumns)
      me.handleUpdateColumn(columns)
    },
    methods:{
      handleTableColumnSetup(){
        this.showtableColumnSetup=true
      },
      /**
       * 保存设置列
       * @param columns
       */
      handleUpdateColumn(columns){
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(),...columns]
        me.gridConfig.exportColumns= columns.map(columns=>{
          return{
            key:columns.key,
            value:columns.title
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
