<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <MaterialForClassifiedSearch ref="headSearch" :fixed-values="fixedValues" :cmb-source="cmbSource"></MaterialForClassifiedSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:import>
              <Dropdown trigger="click">
                <XdoButton type="text" style="font-size: 12px; width: 95px;">
                  <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>导入<XdoIcon type="ios-arrow-down"></XdoIcon>
                </XdoButton>
                <DropdownMenu slot="list">
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadOrdinary">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>普通导入
                    </XdoButton>&nbsp;
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>修改导入
                    </XdoButton>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <MaterialForClassifiedEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"
                               :fixed-values="fixedValues" :cmb-source="cmbSource"></MaterialForClassifiedEdit>
    <ImportPage :importKey="importKey" :importShow.sync="importShowNormal" :importConfig="importConfig" @onImportSuccess="onAfterImport"></ImportPage>
    <ImportPage :importKey="updateImportKey" :importShow.sync="importShowModify" :importConfig="updateImportConfig" @onImportSuccess="onAfterImport"></ImportPage>
    <MaterialsMergeList :show.sync="marchShow" gmark="E" :head-ids="headIds" @march:success="onAfterImport"></MaterialsMergeList>
    <TableColumnSetup v-model="bondedESetupShow" :resId="bondedId" :columns="bondedEMaterialsColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { materialForClassifiedList } from '../js/materialForClassifiedList'
  import { bondedProductsColumns, columns } from '../js/materialForClassifiedColumns'

  export default {
    name: 'BondedProductsList',
    mixins: [materialForClassifiedList, columns],
    data() {
      let normalImportConfig = this.getCommImportConfig('CLASSIFY-EXG'),
        updateImportConfig = this.getCommImportConfig('CLASSIFY-EXG-UPDATE', {
          gmark: 'E',
          bondMark: '0',
          moduleType: 'classifyExgUpdate'
        })
      return {
        bondedId:'',
        searchLines: 4,
        fixedValues: {
          gmark: 'E',
          bondMark: '0'
        },
        bondedEMaterialsColumns:[],
        bondedESetupShow:false,
        gridConfig: {
          exportTitle: '保税成品待归类'
        },
        convertLabel: '非保成品',
        // 以下为导入配置信息
        importKey: 'CLASSIFY-EXG',
        importConfig: normalImportConfig,
        updateImportKey: 'CLASSIFY-EXG-UPDATE',
        updateImportConfig: updateImportConfig,
        toolbarEventMap: {
          'settings': this.handleTableColumnSetup
        },
        ajaxUrl: {
          delete: csAPI.csMaterialForClassified.bondedProducts.delete,
          getMatch: csAPI.csMaterialForClassified.bondedProducts.getMatch,
          exportUrl: csAPI.csMaterialForClassified.bondedProducts.exportUrl,
          addSerialNo: csAPI.csMaterialForClassified.bondedProducts.addSerialNo,
          sendToFilter: csAPI.csMaterialForClassified.bondedProducts.sendToFilter,
          bondedConvert: csAPI.csMaterialForClassified.bondedProducts.bondedConvert,
          getExtraction: csAPI.csMaterialForClassified.bondedProducts.getExtraction,
          selectAllPaged: csAPI.csMaterialForClassified.bondedProducts.selectAllPaged
        }
      }
    },
    // mounted: function () {
    //   this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, bondedProductsColumns)
    //   this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, bondedProductsExcelColumns)
    // }
    created:function (){
      let me = this
      me.bondedId = me.$route.path + '/' + me.$options.name
      me.$set(me, 'bondedEMaterialsColumns', getColumnsByConfig(me.totalColumns, bondedProductsColumns))
      let columns = me.$bom3.showTableColumns(me.bondedId, me.bondedEMaterialsColumns)
      me.handleUpdateColumn(columns)
    },
    methods:{
      handleTableColumnSetup(){
        this.bondedESetupShow=true
      },

      /**
       * 设置列
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(columns => {
          return {
            key: columns.key,
            value: columns.title
          }
        })
      }
    }





  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
