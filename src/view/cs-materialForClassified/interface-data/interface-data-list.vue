<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <MaterialForClassifiedSearch ref="headSearch"></MaterialForClassifiedSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                    @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</Button>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <MaterialForClassifiedEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></MaterialForClassifiedEdit>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { materialForClassifiedBaseList } from '../js/materialForClassifiedBaseList'
  import { interfaceDataColumns, interfaceDataExcelColumns, columns } from '../js/materialForClassifiedColumns'

  export default {
    name: 'InterfaceDataList',
    mixins: [materialForClassifiedBaseList, columns],
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        gridConfig: {
          exportTitle: 'ERP接口数据'
        },
        ajaxUrl: {
          exportUrl: csAPI.csMaterialForClassified.interfaceData.exportUrl,
          sendToFilter: csAPI.csMaterialForClassified.interfaceData.sendToFilter,
          selectAllPaged: csAPI.csMaterialForClassified.interfaceData.selectAllPaged,
          sendForClassifiy: csAPI.csMaterialForClassified.interfaceData.sendForClassifiy
        },
        actions: [
          {...btnComm, label: '导出', key: 'xdo-btn-download', icon: 'ios-cloud-download-outline', click: this.handleDownload},
          {...btnComm, label: '过滤', key: 'xdo-btn-delete', icon: 'ios-trash-outline', click: this.handleFilter},
          {...btnComm, label: '待归类', key: 'xdo-btn-delete', icon: 'ios-trash-outline', click: this.handleForClassfy}
        ]
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, interfaceDataColumns)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, interfaceDataExcelColumns)
    },
    methods: {
      /**
       * 获取查询条件
       */
      getSearchParams() {
        let me = this
        return Object.assign({
          status: '0'
        }, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}))
      },
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, 0)
      },
      handleFilter() {
        let me = this
        me.doSetStatus('过滤', me.ajaxUrl.sendToFilter, '1', 1)
      },
      handleForClassfy() {
        let me = this
        me.doSetStatus('待归类', me.ajaxUrl.sendForClassifiy, '2', 2)
      },
      handleView() {
        let me = this
        if (me.checkRowSelected('查看', true)) {
          me.handleViewByRow(me.gridConfig.selectRows[0])
        }
      },
      operationEditShow() {
        return 'none'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
