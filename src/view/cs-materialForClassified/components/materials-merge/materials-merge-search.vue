<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="serialNo" label="备案序号">
        <xdo-input v-model="searchParam.serialNo" number int-length="11"></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="copGNo" label="备案料号">
        <XdoIInput type="text" v-model="searchParam.copGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { productClassify } from '@/view/cs-common'
  import { detailGeneralMethod } from '@/view/cs-materielCenter/base/detailGeneralMethod.js'

  export default {
    name: 'materialsMergeSearch',
    mixins: [detailGeneralMethod],
    props: {
      gmark: {
        type: String,
        require: true
      }
    },
    data() {
      return {
        searchParam: {
          emsNo: '',
          serialNo: null,
          copGNo: '',
          codeTS: '',
          gname: '',
          gmark: '',
          apprStatus: 'JC'
        },
        productClassify: productClassify,
        cmbSource: {
          emsNoData: []
        }
      }
    },
    created() {
      let me = this
      me.getEmsNoList((req) => {
        if (!req.data) {
          return []
        }
        let emsNoDataArr = []
        for (let item of req.data) {
          if (isNullOrEmpty(item.emsNo) !== true) {
            emsNoDataArr.push({
              value: item.emsNo,
              label: item.emsNo
            })
          }
        }
        me.cmbSource.emsNoData = emsNoDataArr
      })
    },
    mounted() {
      let me = this
      me.$set(me.searchParam, 'gmark', me.gmark)
    }
  }
</script>

<style scoped>
</style>
