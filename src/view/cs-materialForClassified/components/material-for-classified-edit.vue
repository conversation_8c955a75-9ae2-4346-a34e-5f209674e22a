<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120"
             style="padding: 0; grid-column-gap: 0;">
      <Card :bordered="false" class="dc-merge-1-4">
        <p slot="title">ERP 信息</p>
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="facGNo" label="企业料号">
            <XdoIInput type="text" v-model="frmData.facGNo" :disabled="showDisable" :maxlength="50" @on-enter="onFacGNoEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGName" label="中文名称">
            <XdoIInput type="text" v-model="frmData.copGName" :disabled="showDisable" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNameEn" label="英文名称">
            <XdoIInput type="text" v-model="frmData.copGNameEn" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop="copGModel" label="料号申报要素">
            <XdoIInput type="text" v-model="frmData.copGModel" :disabled="showDisable" :maxlength="500"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGModelEn" label="英文规格型号">
            <XdoIInput type="text" v-model="frmData.copGModelEn" :disabled="showDisable" :maxlength="500"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="frmData.curr" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="decPrice" label="单价">
            <dc-numberInput v-model="frmData.decPrice" integerDigits="10" precision="5" :disabled="showDisable" @on-enter="onDecPriceEnter"></dc-numberInput>
          </XdoFormItem>
          <XdoFormItem prop="qtyWt" label="净重数量">
            <dc-numberInput v-model="frmData.qtyWt" integerDigits="10" precision="5" :disabled="showDisable"></dc-numberInput>
          </XdoFormItem>
          <XdoFormItem prop="insertTime" label="录入日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd" placeholder="请选择日期" v-model="frmData.insertTime" disabled></XdoDatePicker>
          </XdoFormItem>

          <XdoFormItem prop="unitErp" label="ERP计量单位">
            <XdoIInput type="text" v-model="frmData.unitErp" :disabled="showDisable" :maxlength="20" @on-enter="onUnitErpEnter"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="netWt" :label="netWtLabel">
            <dc-numberInput v-model="frmData.netWt" integerDigits="10" precision="8" :disabled="showDisable" @on-enter="onUnitErpEnter"></dc-numberInput>
          </XdoFormItem>
          <XdoFormItem prop="originalGNo" label="原始物料">
            <XdoIInput type="text" v-model="frmData.originalGNo" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop="remark" label="备注">
            <XdoIInput type="text" v-model="frmData.remark" :disabled="showDisable" :maxlength="500"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem v-show="showFields.bondMark" prop="bondMark" label="保完税标识">
            <xdo-select v-model="frmData.bondMark" :options="this.importExportManage.bondedFlagMap"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem v-show="showFields.gmark" prop="gmark" label="物料类型标识">
            <xdo-select v-model="frmData.gmark" :options="this.importExportManage.gmarkMap"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="copGNameConvert" label="转换后品名">
            <XdoIInput type="text" v-model="frmData.copGNameConvert" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGModelConvert" label="转换后规格">
            <XdoIInput type="text" v-model="frmData.copGModelConvert" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remarkConvert" label="转换后备注">
            <XdoIInput type="text" v-model="frmData.remarkConvert" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop="copMark" label="物料种类">
            <xdo-select v-model="frmData.copMark" :options="this.copMarkList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="outCopMark" label="外库物料标志">
            <xdo-select v-model="frmData.outCopMark" :options="this.outCopMarkList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="outMessage" label="外库信息">
            <XdoIInput type="text" v-model="frmData.outMessage" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="freeBillNo" label="免表清单序号">
            <XdoIInput type="text" v-model="frmData.freeBillNo" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="productElement" label="商品构成">
            <XdoIInput type="text" v-model="frmData.productElement" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="productExplain" label="商品用途">
            <XdoIInput type="text" v-model="frmData.productExplain" :disabled="showDisable" :maxlength="512"></XdoIInput>
          </XdoFormItem>
        </div>
      </Card>
      <Card :bordered="false" class="dc-merge-1-4" style="margin-top: 0;">
        <p slot="title">归类信息</p>
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <MerchElement :tyShow.sync="tyShow" :is-edit="isEdit"
                        :modelString="frmData.gmodel" :code-ts="frmData.codeTS" :code-name="frmData.gname"
                        @onChange="handleGModelChange"></MerchElement>
          <FormItem v-show="!showFields.gmark" prop="emsNo" label="备案号">
            <span slot="label" class="preRequired">备案号</span>
            <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoData" :disabled="showDisable"></xdo-select>
          </FormItem>
          <FormItem v-show="!showFields.gmark" prop="copGNo" label="备案料号">
            <span slot="label" class="preRequired">备案料号</span>
            <XdoIInput type="text" v-model="frmData.copGNo" :disabled="copGNoDisable" :maxlength="50" @on-enter="onCopGNoEnter"></XdoIInput>
          </FormItem>
          <XdoFormItem v-show="!showFields.gmark" prop="serialNo" label="备案序号">
            <dc-numberInput v-model="frmData.serialNo" integerDigits="11" :disabled="showDisable"></dc-numberInput>
          </XdoFormItem>

          <FormItem v-show="fixedValues.bondMark === '0'" prop="copEmsNo" label="企业内部编号">
            <span slot="label" class="preRequired">企业内部编号</span>
            <xdo-select v-model="frmData.copEmsNo" :options="this.cmbSource.copEmsNoData" :disabled="showDisable"></xdo-select>
          </FormItem>
          <FormItem prop="codeTS" label="商品编码">
            <span slot="label" class="preRequired">商品编码</span>
            <XdoIInput type="text" v-model="frmData.codeTS" :disabled="showDisable" :maxlength="10"></XdoIInput>
          </FormItem>
          <FormItem prop="gname" label="商品名称">
            <span slot="label" class="preRequired">商品名称</span>
            <XdoIInput type="text" v-model="frmData.gname" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </FormItem>

          <FormItem prop="gname2" label="商品名称(参考)">
            <XdoIInput type="text" v-model="frmData.gname2" :disabled="true" :maxlength="255"></XdoIInput>
          </FormItem>

          <XdoFormItem v-if="fixedValues.bondMark === '1'" prop="recordDateEnd" label="备案有效期">
            <XdoDatePicker type="date" v-model="frmData.recordDateEnd" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>

          <FormItem prop="gmodel" label="申报规格型号" class="dc-merge-1-3">
            <span slot="label" class="preRequired">申报规格型号</span>
            <Input type="text" v-model="frmData.gmodel" disabled :maxlength="255" style="width: 80%;"></Input>
            <span><span>{{ usedCountGModel }}</span>/255 </span>
            <XdoButton @click="handleAddtype">规范申报</XdoButton>
          </FormItem>
          <XdoFormItem prop="country" label="产销国">
            <xdo-select v-model="frmData.country" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :disabled="showDisable"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

          <FormItem prop="unit" label="申报计量单位">
            <span slot="label" class="preRequired">申报计量单位</span>
            <xdo-select v-model="frmData.unit" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </FormItem>
          <FormItem prop="unit1" label="法定计量单位">
            <span slot="label" class="preRequired">法定计量单位</span>
            <xdo-select v-model="frmData.unit1" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </FormItem>
          <XdoFormItem prop="unit2" label="法定第二计量单位">
            <xdo-select v-model="frmData.unit2" disabled :asyncOptions="pcodeList" :meta="pcode.unit"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>

          <XdoFormItem prop="factor1" label="法一比例因子">
            <div class="dc-form" style="grid-column-gap: 0; padding: 0;">
              <XdoIInput type="text" v-model="factor1ValLeft" disabled></XdoIInput>
              <dc-numberInput v-model="frmData.factor1" integerDigits="11" precision="5" :disabled="showDisable"></dc-numberInput>
              <XdoIInput type="text" v-model="factor1ValRight" disabled></XdoIInput>
            </div>
          </XdoFormItem>
          <XdoFormItem prop="factor2" label="法二比例因子">
            <div class="dc-form" style="grid-column-gap: 0; padding: 0;">
              <XdoIInput type="text" v-model="factor2ValLeft" disabled></XdoIInput>
              <dc-numberInput v-model="frmData.factor2" integerDigits="11" precision="5" :disabled="factor2Disable"></dc-numberInput>
              <XdoIInput type="text" v-model="factor2ValRight" disabled></XdoIInput>
            </div>
          </XdoFormItem>
          <XdoFormItem prop="factorErp" label="ERP比例因子">
            <div class="dc-form" style="grid-column-gap: 0; padding: 0;">
              <XdoIInput type="text" v-model="factorErpValRight" disabled></XdoIInput>
              <dc-numberInput v-model="frmData.factorErp" integerDigits="11" precision="8" :disabled="factorErpDisable" @on-enter="onUnitErpEnter"></dc-numberInput>
              <XdoIInput type="text" v-model="factorErpValLeft" disabled></XdoIInput>
            </div>
          </XdoFormItem>

          <XdoFormItem prop="netWtConvert" :label="netWtConvertLabel">
            <dc-numberInput v-model="frmData.netWtConvert" integerDigits="10" precision="8" :disabled="showDisable"></dc-numberInput>
          </XdoFormItem>
          <XdoFormItem prop="qty" label="备案数量">
            <dc-numberInput v-model="frmData.qty" integerDigits="11" precision="5" :disabled="showDisable" @on-enter="onQtyEnter"></dc-numberInput>
          </XdoFormItem>
          <XdoFormItem prop="decTotal" label="备案总价">
            <dc-numberInput v-model="frmData.decTotal" integerDigits="11" precision="5" :disabled="showDisable" @on-enter="onDecTotalEnter"></dc-numberInput>
          </XdoFormItem>

          <XdoFormItem v-if="fixedValues.bondMark === '0'" prop="recordDateEnd" label="备案有效期">
            <XdoDatePicker type="date" v-model="frmData.recordDateEnd" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <FormItem prop="supplierCode" label="客户/供应商代码">
            <XdoIInput type="text" v-model="frmData.supplierCode" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </FormItem>
          <FormItem prop="factoryCode" label="工厂代码">
            <XdoIInput type="text" v-model="frmData.factoryCode" :disabled="showDisable" :maxlength="50"></XdoIInput>
          </FormItem>
        </div>
      </Card>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, isNumber, keepDecimal } from '@/libs/util'
  import { materialForClassifiedEdit } from '../js/materialForClassifiedEdit'
  import { editStatus, importExportManage, MerchElement } from '@/view/cs-common'

  export default {
    name: 'materialForClassifiedEdit',
    components: {
      MerchElement
    },
    mixins: [materialForClassifiedEdit],
    props: {
      fixedValues: {
        type: Object,
        default: () => ({})
      },
      cmbSource: {
        type: Object,
        default: () => ({
          emsNoData: [],
          copEmsNoData: [],
          emsCopData: []
        })
      }
    },
    data() {
      return {
        tyShow: false,
        usedCountGModel: 0,
        ajaxUrl: {
          insert: csAPI.csMaterialForClassified.details.insert,
          update: csAPI.csMaterialForClassified.details.update,
          checkParam: csAPI.enterpriseParamsLib.comm.checkParam
        },
        rulesHeader: {
          bondMark: [{required: true, message: '不能为空', trigger: 'blur'}],
          facGNo: [{required: true, message: '不能为空', trigger: 'blur'}],
          gmark: [{required: true, message: '不能为空', trigger: 'blur'}]
        },
        formName: 'dataForm',
        showFields: {
          gmark: true,
          bondMark: true
        },
        importExportManage: importExportManage
        , copMarkList: []
        , outCopMarkList: []
      }
    },
    watch: {
      fixedValues: {
        deep: true,
        immediate: true,
        handler: function (values) {
          this.$set(this.showFields, 'gmark', true)
          if (values.hasOwnProperty('gmark') && !isNullOrEmpty(values['gmark'])) {
            this.$set(this.showFields, 'gmark', false)
          }
          this.$set(this.showFields, 'bondMark', true)
          if (values.hasOwnProperty('bondMark') && !isNullOrEmpty(values['bondMark'])) {
            this.$set(this.showFields, 'bondMark', false)
          }
        }
      },
      'frmData.gmodel': {
        immediate: true,
        handler: function () {
          this.calcUsedCountGModel()
        }
      },
      'frmData.emsNo': {
        handler: function (emsNo) {
          this.onEmsNoChange(emsNo)
        }
      },
      'frmData.copEmsNo': {
        handler: function (copEmsNo) {
          this.onCopEmsNoChange(copEmsNo)
        }
      },
      'frmData.codeTS': {
        immediate: true,
        handler: function (codeTS) {
          this.onCodeTSChange(codeTS)
        }
      },
      'frmData.decPrice': {
        handler: function (decPrice) {
          if (isNumber(decPrice)) {
            this.$set(this.frmData, 'decPrice', parseFloat(decPrice))
          } else {
            this.$set(this.frmData, 'decPrice', null)
          }
        }
      },
      'frmData.qty': {
        handler: function (qty) {
          if (isNumber(qty)) {
            this.$set(this.frmData, 'qty', parseFloat(qty))
          } else {
            this.$set(this.frmData, 'qty', null)
          }
        }
      },
      'frmData.decTotal': {
        handler: function (decTotal) {
          if (isNumber(decTotal)) {
            this.$set(this.frmData, 'decTotal', parseFloat(decTotal))
          } else {
            this.$set(this.frmData, 'decTotal', null)
          }
        }
      },
      'frmData.factor1': {
        handler: function (factor1) {
          if (isNumber(factor1)) {
            this.$set(this.frmData, 'factor1', parseFloat(factor1))
          } else {
            this.$set(this.frmData, 'factor1', null)
          }
        }
      },
      'frmData.factor2': {
        handler: function (factor2) {
          if (isNumber(factor2)) {
            this.$set(this.frmData, 'factor2', parseFloat(factor2))
          } else {
            this.$set(this.frmData, 'factor2', null)
          }
        }
      },
      'frmData.factorErp': {
        handler: function (factorErp) {
          if (isNumber(factorErp)) {
            this.$set(this.frmData, 'factorErp', parseFloat(factorErp))
          } else {
            this.$set(this.frmData, 'factorErp', null)
          }
        }
      },
      'frmData.qtyWt': {
        handler: function (qtyWt) {
          if (isNumber(qtyWt)) {
            this.$set(this.frmData, 'qtyWt', parseFloat(qtyWt))
          } else {
            this.$set(this.frmData, 'qtyWt', null)
          }
        }
      },
      'frmData.unit': {
        handler: function () {
          this.smartAssignment()
        }
      }
    },
    methods: {
      handleAddtype() {
        // 弹出规格型号
        this.tyShow = true
      },
      handleGModelChange(val) {
        let me = this
        me.$set(me.frmData, 'gmodel', val)
      },
      calcUsedCountGModel() {
        // 计算规格型号已使用的字数
        let bytesCount = 0
        let strGModel = this.frmData.gmodel
        if (!isNullOrEmpty(strGModel)) {
          let chars = ''
          for (let i = 0; i < strGModel.length; i++) {
            chars = strGModel.charAt(i)
            /* eslint-disable no-control-regex */
            if (/^[\u0000-\u00ff]$/.test(chars)) {
              bytesCount += 1
            } else {
              bytesCount += 2
            }
          }
        }
        this.usedCountGModel = bytesCount
      },
      getDefaultData() {
        let me = this
        let theBondMark = ''
        if (me.fixedValues.hasOwnProperty('bondMark') && !isNullOrEmpty(me.fixedValues['bondMark'])) {
          theBondMark = me.fixedValues['bondMark']
        }
        let theGMark = ''
        if (me.fixedValues.hasOwnProperty('gmark') && !isNullOrEmpty(me.fixedValues['gmark'])) {
          theGMark = me.fixedValues['gmark']
        }
        let nowTime = new Date()
        let theStatus = '0'
        if (me.fixedValues.hasOwnProperty('status') && !isNullOrEmpty(me.fixedValues['status'])) {
          theStatus = me.fixedValues['status']
        }
        let defaultEmsNo = me.$store.getters[`${namespace}/defaultEmsNo`]
        let emsCopDatas = me.cmbSource.emsCopData.filter(item => {
          return item.value === defaultEmsNo
        })
        let defaultCopEmsNo = ''
        if (Array.isArray(emsCopDatas) && emsCopDatas.length === 1) {
          defaultCopEmsNo = emsCopDatas[0].key
        }
        if (!isNullOrEmpty(defaultCopEmsNo) && me.editConfig.editStatus === editStatus.ADD) {
          me.$nextTick(() => {
            me.onCopEmsNoChange(defaultCopEmsNo)
          })
        }
        return {
          // ERP信息
          facGNo: '',
          copGName: '',
          copGNameEn: '',
          copGModel: '',
          copGNameConvert: '',
          copGModelConvert: '',
          copGModelEn: '',
          curr: '',
          decPrice: null,
          netWt: '',
          netWtConvert: '',
          unitErp: '',
          insertTime: nowTime.format('yyyy-MM-dd hh:mm:ss'),
          originalGNo: '',
          remark: '',
          remarkConvert: '',
          bondMark: theBondMark,
          gmark: theGMark,

          // 归类信息
          copGNo: '',
          emsNo: defaultEmsNo,
          billFlag: '',
          serialNo: null,
          copEmsNo: defaultCopEmsNo,
          codeTS: '',
          gname: '',
          gmodel: '',
          unit: '',
          unit1: '',
          unit2: '',
          qty: null,
          decTotal: null,
          country: '',
          recordDateEnd: '',
          factor1: null,
          factor2: null,
          factorErp: null,
          qtyWt: 1,
          status: theStatus,
          dataSource: '0',
          supplierCode: '',
          factoryCode: ''

          ,copMark: '',
          outCopMark: '',
          outMessage: '',
          freeBillNo: '',
          productElement: '',
          productExplain: '',
        }
      },
      onEmsNoChange(emsNo) {
        let me = this
        if (!isNullOrEmpty(emsNo)) {
          let emsCopDatas = me.cmbSource.emsCopData.filter(item => {
            return item.value === emsNo
          })
          if (Array.isArray(emsCopDatas) && emsCopDatas.length === 1) {
            me.$set(me.frmData, 'copEmsNo', emsCopDatas[0].key)
          } else {
            me.$set(me.frmData, 'copEmsNo', '')
          }
        } else {
          me.$set(me.frmData, 'copEmsNo', '')
        }
      },
      onCopEmsNoChange(copEmsNo) {
        let me = this
        if (isNullOrEmpty(copEmsNo) || me.frmData.bondMark !== '0') {
          me.$set(me.frmData, 'emsNo', '')
        } else {
          let emsCopDatas = me.cmbSource.emsCopData.filter(item => {
            return item.key === copEmsNo
          })
          if (Array.isArray(emsCopDatas) && emsCopDatas.length === 1) {
            me.$set(me.frmData, 'emsNo', emsCopDatas[0].value)
          } else {
            me.$set(me.frmData, 'emsNo', '')
          }
        }
      },
      onFacGNoEnter() {
        let me = this
        if (me.showFields.bondMark !== true) {
          if (isNullOrEmpty(me.frmData.copGNo)) {
            me.$set(me.frmData, 'copGNo', me.frmData.facGNo)
          }
        }
      },
      onCopGNoEnter() {
        let me = this
        me.$http.post(csAPI.csMaterielCenter.bonded.getIeInfoByCopGNo, {
          bondedFlag: '0',        // 保税标记
          copGNo: me.frmData.copGNo,              // 备案料号
          emsNo: me.frmData.emsNo,                // 备案号
          gmark: me.frmData.gmark,                // 物料类型
          facGNo: me.frmData.facGNo               // 企业料号
        }).then(res => {
          me.$set(me.frmData, 'serialNo', res.data.data.serialNo)
          me.$set(me.frmData, 'codeTS', res.data.data.codeTS)
          me.$set(me.frmData, 'gname', res.data.data.gname)
          me.$set(me.frmData, 'copGNameConvert', res.data.data.copGName)
          me.$set(me.frmData, 'copGModelConvert', res.data.data.copGModel)
          me.$set(me.frmData, 'unit', res.data.data.unit)
          me.$set(me.frmData, 'unit1', res.data.data.unit1)
          me.$set(me.frmData, 'unit2', res.data.data.unit2)
          me.$set(me.frmData, 'curr', res.data.data.curr)
          me.$set(me.frmData, 'gmodel', res.data.data.gmodel)
          me.$set(me.frmData, 'qty', res.data.data.qty)
          me.$set(me.frmData, 'decTotal', res.data.data.decTotal)
        }).catch(() => {
          me.$set(me.frmData, 'serialNo', null)
          me.$set(me.frmData, 'codeTS', '')
          me.$set(me.frmData, 'gname', '')
          me.$set(me.frmData, 'copGNameConvert', '')
          me.$set(me.frmData, 'copGModelConvert', '')
          me.$set(me.frmData, 'unit', '')
          me.$set(me.frmData, 'unit1', '')
          me.$set(me.frmData, 'unit2', '')
          me.$set(me.frmData, 'curr', '')
          me.$set(me.frmData, 'gmodel', '')
          me.$set(me.frmData, 'qty', null)
          me.$set(me.frmData, 'decTotal', null)
        })
      },
      /**
       * 商品编码带出计量单位
       * @param codeTS
       */
      onCodeTSChange(codeTS) {
        let me = this
        let val = codeTS
        if (typeof codeTS === 'object') {
          val = me.frmData.codeTS
        }
        if (!isNullOrEmpty(val)) {
          val = val.trim()
        } else {
          val = ''
        }
        if (val.length === 10) {
          me.pcodeRemote(me.pcode.complex, val).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.frmData, 'unit1', res[0]['UNIT_1'])
              me.$set(me.frmData, 'unit2', res[0]['UNIT_2'])
              if (isNullOrEmpty(res[0]['UNIT_2'])) {
                me.$set(me.frmData, 'factor2', '')
              }
              //if (isNullOrEmpty(me.frmData.gname2)) {
                me.$set(me.frmData, 'gname2', res[0]["NAME"].substr(0, 50))
              //}
            } else {
              me.resetUnit(true)
            }
          }).catch(() => {
            me.resetUnit(true)
          })
        } else if (val.length > 0) {
          me.resetUnit(false)
        } else {
          me.resetUnit(false)
        }
      },
      resetUnit(showMsg) {
        let me = this
        if (showMsg === true) {
          me.$Message.warning('商品编码不存在')
        }
        me.$set(me.frmData, 'gname', '')
        me.$set(me.frmData, 'unit1', '')
        me.$set(me.frmData, 'unit2', '')
        me.$set(me.frmData, 'factor2', '')
      },
      /**
       * 根据单价数量计算总价
       */
      onDecPriceEnter() {
        let me = this,
          price = me.frmData.decPrice
        if (isNumber(price) && isNumber(me.frmData.qty)) {
          me.$set(me.frmData, 'decTotal', parseFloat((me.frmData.qty * price).toFixed(5)))
        } else {
          me.$set(me.frmData, 'decTotal', null)
        }
      },
      /**
       * Erp计量单位回车事件
       */
      onUnitErpEnter() {
        let me = this
        me.$nextTick(() => {
          me.smartAssignment()
        })
      },
      /**
       * 根据数量单价计算总价
       */
      onQtyEnter() {
        let me = this,
          qty = me.frmData.qty
        if (isNumber(qty) && isNumber(me.frmData.decPrice)) {
          me.$set(me.frmData, 'decTotal', parseFloat((me.frmData.decPrice * qty).toFixed(5)))
        } else {
          me.$set(me.frmData, 'decTotal', null)
        }
      },
      /**
       * 根据总价单价计算数量
       */
      onDecTotalEnter() {
        let me = this,
          total = me.frmData.decTotal
        if (isNumber(total) && isNumber(me.frmData.decPrice)) {
          me.$set(me.frmData, 'qty', parseFloat((total / me.frmData.decPrice).toFixed(5)))
        }
      },
      /**
       * netWtConvert 取值逻辑如下
       * (1)交易单位与申报单位一致时，取“净重（ERP单位）”栏位数值；用户录入申报单位回车后自动生成
       * (2)交易单位与申报单位不一致时，根据ERP比例因子换算：（ERP信息净重栏位/ERP比例因子），保留8位小数；
       */
      smartAssignment() {
        let me = this,
          unit = me.frmData.unit,
          netWt = me.frmData.netWt,
          unitErp = me.frmData.unitErp,
          factorErp = me.frmData.factorErp,
          netWtConvert = me.frmData.netWtConvert
        if (isNumber(netWtConvert) && Number(netWtConvert) > 0) {
          return
        }
        if (isNullOrEmpty(unitErp)) {
          me.$set(me.frmData, 'factorErp', '')
        }
        if (!isNullOrEmpty(unit) && !isNullOrEmpty(unitErp) && isNumber(netWt)) {
          me.$http.post(me.ajaxUrl.checkParam, {
            paramsType: 'UNIT',
            paramsCode: unitErp,
            customParamCode: unit
          }).then(res => {
            if (res.data.data === true) {
              me.$set(me.frmData, 'netWtConvert', netWt)
            } else if (unit === unitErp) {
              me.$set(me.frmData, 'netWtConvert', netWt)
            } else if (isNumber(factorErp)) {
              me.$set(me.frmData, 'netWtConvert', keepDecimal(Number(netWt) / Number(factorErp), 8))
            }
          }).catch(() => {
          })
        }
      },
      /**
       * 执行保存前操作
       */
      onBeforeSave() {
        let me = this
        me.$set(me.frmData, 'billFlag', '')
        if (me.frmData.bondMark === '0' && !isNullOrEmpty(me.frmData.emsNo)) {
          let emsNoObj = me.cmbSource.emsNoData.filter(item => {
            return item.value === me.frmData.emsNo
          })
          if (Array.isArray(emsNoObj) && emsNoObj.length > 0) {
            me.$set(me.frmData, 'billFlag', emsNoObj[0].billFlag)
          }
        }
      }
    },
    created: function () {

      let me = this
      // 物料种类
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/COP_MARK').then(res => {
        me.copMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.copMarkList = []
      })

      // 外库物料标志
      me.$http.post(csAPI.csImportExport.customsParams.getParamValues + '/OUT_COP_MARK').then(res => {
        me.outCopMarkList = res.data.data.map((item) => {
          return {
            value: item.key,
            label: item.value
          }
        })
      }).catch(() => {
        me.outCopMarkList = []
      })
    },
    computed: {
      factor1ValLeft() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit1)) {
          return this.frmData.unit + '(申报单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + '='
        }
        return ''
      },
      factor1ValRight() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit1)) {
          return this.frmData.unit1 + '(法定单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unit1)
        }
        return ''
      },
      // factor1Val() {
      //   if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit1)) {
      //     return this.frmData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + ' : ' + this.frmData.unit1 + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit1)
      //   }
      //   return ''
      // },
      factor2ValLeft() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit2)) {
          return this.frmData.unit + '(申报单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + '='
        }
        return ''
      },
      factor2ValRight() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit2)) {
          return this.frmData.unit2 + '(法定单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unit2)
        }
        return ''
      },
      // factor2Val() {
      //   if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unit2)) {
      //     return this.frmData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + ' : ' + this.frmData.unit2 + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit2)
      //   }
      //   return ''
      // },
      factorErpValLeft() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unitErp)) {
          return this.frmData.unit + '(申报单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unit)
        }
        return ''
      },
      factorErpValRight() {
        if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unitErp)) {
          return this.frmData.unitErp + '(ERP单位)' + this.pcodeGet(this.pcode.unit, this.frmData.unitErp) + '='
        }
        return ''
      },
      // factorErpVal() {
      //   if (!isNullOrEmpty(this.frmData.unit) && !isNullOrEmpty(this.frmData.unitErp)) {
      //     return this.frmData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + ' : ' + this.frmData.unitErp + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unitErp)
      //   }
      //   return ''
      // },
      isEdit() {
        return this.editConfig.editStatus === editStatus.EDIT || this.editConfig.editStatus === editStatus.ADD
      },
      netWtLabel() {
        if (!isNullOrEmpty(this.frmData.unitErp)) {
          return '净重(' + this.frmData.unitErp.trim() + ')'
        }
        return '净重()'
      },
      netWtConvertLabel() {
        if (!isNullOrEmpty(this.frmData.unit)) {
          return '净重(' + this.frmData.unit + ' ' + this.pcodeGet(this.pcode.unit, this.frmData.unit) + ')'
        }
        return '净重()'
      },
      configData() {
        return this.$store.state[`${namespace}`].clearanceBusinessSetting
      },
      copGNoDisable() {
        if (this.configData.autoMat === '1') {
          return true
        }
        return this.showDisable
      },
      factor2Disable() {
        let me = this
        if (isNullOrEmpty(me.frmData.unit2)) {
          return true
        }
        return me.showDisable
      },
      factorErpDisable() {
        let me = this
        if (isNullOrEmpty(me.frmData.unitErp)) {
          return true
        }
        return me.showDisable
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }

  .preRequired:before {
    color: blue;
    content: '*';
    font-size: 12px;
    line-height: 1px;
    margin-right: 4px;
    font-family: 'SimSun';
    display: inline-block;
  }
</style>
