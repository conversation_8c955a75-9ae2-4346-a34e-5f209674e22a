import pms from '@/libs/pms'
import { importExportManage } from '@/view/cs-common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { interimVerification, productClassify,toBeClassified } from '@/view/cs-common'
import MaterialForClassifiedEdit from '@/view/cs-materialForClassified/components/material-for-classified-edit'
import MaterialForClassifiedSearch from '@/view/cs-materialForClassified/components/material-for-classified-search'

export const materialForClassifiedBaseList = {
  components: {
    MaterialForClassifiedEdit,pms,
    MaterialForClassifiedSearch
  },
  mixins: [commList],
  data() {
    return {
      cmbSource: {
        unitData: []
      },
      toolbarEventMap: {},
      myDynamicHeight: 120,
      toBeClassified: toBeClassified,
      productClassify: productClassify,
      interimVerification: interimVerification
    }
  },
  created: function () {
    let me = this
    // 申报计量单位
    me.pcodeList(me.pcode.unit).then(res => {
      me.$set(me.cmbSource, 'unitData', [...importExportManage.supplierName, ...res])
    }).catch(() => {
      me.$set(me.cmbSource, 'unitData', importExportManage.supplierName)
    })
  },
  methods: {
    /**
     * 设置数据状态
     * @param title
     * @param ajaxUrl
     * @param status
     * @param actionIndex
     */
    doSetStatus(title, ajaxUrl, status, actionIndex) {
      let me = this
      if (me.checkRowSelected(title)) {
        me.$Modal.confirm({
          title: '提醒',
          okText: title,
          cancelText: '取消',
          content: '确认' + title + '所选项吗',
          onOk: () => {
            if (typeof actionIndex === 'number' && actionIndex > -1 && actionIndex < me.actions.length) {
              me.actions[actionIndex].loading = true
            }
            let params = me.getSelectedParams()
            me.$http.post(`${ajaxUrl}/${status}`, params).then(() => {
              me.$Message.success(title + '成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              if (typeof actionIndex === 'number' && actionIndex > -1 && actionIndex < me.actions.length) {
                me.actions[actionIndex].loading = false
              }
            })
          }
        })
      }
    }
  }
}
