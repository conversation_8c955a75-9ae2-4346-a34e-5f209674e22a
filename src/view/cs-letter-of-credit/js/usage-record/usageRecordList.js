import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import UsageRecordEdit from '../../usage-record/usage-record-edit2'

export const usageRecordList = {
  name: 'usageRecordList',
  components: {
    UsageRecordEdit
  },
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      listConfig: {
        colOptions: true
      },
      cmbSource: {},
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  created: function () {
    let me = this
    let rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    let showColumns = []
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
    me.afterSearchSuccess()
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions.push({
        ...me.actionsComm,
        label: '新增',
        command: 'add',
        icon: 'ios-add',
        key: 'xdo-btn-add'
      }, {
        ...me.actionsComm,
        label: '编辑',
        command: 'edit',
        key: 'xdo-btn-edit',
        icon: 'ios-create-outline'
      }, {
        ...me.actionsComm,
        label: '删除',
        command: 'delete',
        key: 'xdo-btn-delete',
        icon: 'ios-trash-outline'
      }, {
        ...me.actionsComm,
        label: '导出',
        command: 'export',
        key: 'xdo-btn-download',
        icon: 'ios-cloud-download-outline'
      })
    },
    getCommParams() {
      return [{
        key: 'creditNo',
        title: '信用证编号'
      }, {
        range: true,
        key: 'createDate',
        title: '信用证开立日期'
      }, {
        range: true,
        key: 'validDate',
        title: '信用证有效日期'
      }, {
        key: 'curr',
        title: '币制',
        type: 'pcode',
        props: {
          meta: 'CURR_OUTDATED'
        }
      }, {
        range: true,
        key: 'usedDate',
        title: '使用日期'
      }, {
        key: 'usedDocNo',
        title: '使用单据号'
      }, {
        key: 'usedCurr',
        title: '使用币制',
        type: 'pcode',
        props: {
          meta: 'CURR_OUTDATED'
        }
      }]
    },
    getCommFields() {
      return [{
        width: 180,
        tooltip: true,
        align: 'center',
        key: 'creditNo',
        title: '信用证编号'
      }, {
        width: 120,
        key: 'createDate',
        title: '信用证开立日期',
        align: 'center',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 120,
        key: 'validDate',
        title: '信用证有效日期',
        align: 'center',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        title: '信用证金额',
        key: 'creditAmount',
        width: 180,
        align: 'center',
        tooltip: true
      }, {
        key: 'curr',
        title: '币制',
        width: 150,
        align: 'center',
        tooltip: true,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 120,
        key: 'usedDate',
        title: '使用日期',
        align: 'center',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 180,
        tooltip: true,
        align: 'center',
        key: 'usedDocNo',
        title: '使用单据号'
      }, {
        title: '使用金额',
        key: 'usedAmount',
        width: 180,
        align: 'center',
        tooltip: true
      }, {
        key: 'usedCurr',
        title: '使用币制',
        width: 150,
        align: 'center',
        tooltip: true,
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        title: '信用证余额',
        key: 'creditBalanceAmount',
        width: 180,
        align: 'center',
        tooltip: true
      }, {
        width: 120,
        tooltip: true,
        title: '录入人',
        align: 'center',
        key: 'insertUser'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    handleDelete() {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.listConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            content: '确认删除所选项吗',
            okText: '删除',
            cancelText: '取消',
            onOk: () => {
            }
          })
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      // this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      me.listConfig.data = [{
        sid: '54543554654',
        creditNo: 'CN-SDRGDGD-11',
        createDate: '2020-01-20',
        validDate: '2020-01-30',
        creditAmount: 5465469.21258,
        curr: '142',
        usedDate: '2020-03-05',
        usedDocNo: 'CU-SDGSDG-002',
        usedAmount: 125835.12587,
        usedCurr: '502',
        creditBalanceAmount: 2231582.58484,
        insertUser: 'csTest001'
      }, {
        sid: '54518713548',
        creditNo: 'CN-ADGWFGS-11',
        createDate: '2020-03-16',
        validDate: '2020-03-22',
        creditAmount: 65126546.21258,
        curr: '502',
        usedDate: '2020-04-05',
        usedDocNo: 'CU-SDGSDG-006',
        usedAmount: 1584688.12587,
        usedCurr: '300',
        creditBalanceAmount: 54868245.58484,
        insertUser: 'csTest001'
      }]
      me.pageParam.dataTotal = 2
    }
  }
}
