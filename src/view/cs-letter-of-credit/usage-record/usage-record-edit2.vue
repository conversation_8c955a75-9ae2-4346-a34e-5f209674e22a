<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'usageRecordEdit2',
    mixins: [baseDetailConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        ajaxUrl: {
          insert: '',
          update: '',
        },
        formName: 'frmData',
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      showDisable: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {}
      }
    },
    methods: {
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
      },
      getFields() {
        return [{
          isCard: true,
          title: '信用证使用记录',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        },{
          key: 'creditNo',
          title: '信用证编号'
        }, {
          key: 'createDate',
          title: '信用证开立日期',
          type: 'datePicker',
          props: {
            disabled: true
          }
        }, {
          key: 'validDate',
          title: '信用证有效日期',
          type: 'datePicker',
          props: {
            disabled: true
          }
        }, {
          title: '信用证金额',
          key: 'creditAmount',
          type: 'xdoInput',
          props: {
            intDigits: 8,
            precision: 5,
            disabled: true
          }
        }, {
          key: 'curr',
          title: '币制',
          type: 'pcode',
          props: {
            meta: 'CURR_OUTDATED',
            disabled: true
          }
        }, {
          key: 'usedDate',
          title: '使用日期',
          type: 'datePicker',
        }, {
          key: 'usedDocNo',
          title: '使用单据号'
        }, {
          title: '使用金额',
          key: 'usedAmount',
          type: 'xdoInput',
          props: {
            intDigits: 8,
            precision: 5
          }
        }, {
          key: 'usedCurr',
          title: '使用币制',
          type: 'pcode',
          props: {
            meta: 'CURR_OUTDATED'
          }
        }, {
          title: '信用证余额',
          key: 'creditBalanceAmount',
          type: 'xdoInput',
          props: {
            intDigits: 8,
            precision: 5,
            disabled: true
          }
        }, {
          title: '录入人',
          key: 'insertUser',
          props: {
            disabled: true
          }
        }]
      },
      handleSave() {
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
