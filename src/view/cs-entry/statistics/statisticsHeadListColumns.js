const columnsConfig = [
  'sid'
  , 'year'
  , 'quarter'
  , 'month'
  , 'declareName'
  , 'totalMistake'
  , 'totalDecl'
  , 'mistakePercent'
  , 'totalImp'
  , 'totalExp'
  , 'mistakeNoreason'
  , 'mistakeComp'
  , 'mistakeProxy'
]

const excelColumnsConfig = [
  'sid'
  , 'year'
  , 'quarter'
  , 'month'
  , 'declareName'
  , 'totalMistake'
  , 'totalDecl'
  , 'mistakePercent'
  , 'totalImp'
  , 'totalExp'
  , 'mistakeNoreason'
  , 'mistakeComp'
  , 'mistakeProxy'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          title: '年份',
          minWidth: 80,
          align: 'center',
          key: 'year'
        },
        {
          title: '季度',
          minWidth: 80,
          align: 'center',
          key: 'quarter'
        },
        {
          title: '月份',
          minWidth: 80,
          align: 'center',
          key: 'month'
        },
        {
          title: '申报单位',
          minWidth: 280,
          align: 'center',
          key: 'declareName',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            let code = params.row['declareCode']
            let name = params.row['declareName']
            return h('span', code + ' ' + name)
          }
        },
        {
          title: '差错总量',
          minWidth: 100,
          align: 'center',
          key: 'totalMistake',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报总票数',
          minWidth: 100,
          align: 'center',
          key: 'totalDecl',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '差错率(%)',
          minWidth: 100,
          align: 'center',
          key: 'mistakePercent',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '进口票数',
          minWidth: 100,
          align: 'center',
          key: 'totalImp',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '出口票数',
          minWidth: 100,
          align: 'center',
          key: 'totalExp',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '不可抗力票数',
          minWidth: 100,
          align: 'center',
          key: 'mistakeNoreason',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '企业票数',
          minWidth: 100,
          align: 'center',
          key: 'mistakeComp',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '代理票数',
          minWidth: 100,
          align: 'center',
          key: 'mistakeProxy',
          ellipsis: true,
          tooltip: true,
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
