<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="declareCode" label="申报单位">
        <xdo-select v-model="searchParam.declareCode" :options="this.cmbSource.declareData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="year" label="年份">
        <xdo-select v-model="searchParam.year" :options="this.cmbSource.yearData"></xdo-select>
      </XdoFormItem>
      <div style="display: grid; grid-template-columns: 100px auto;">
        <RadioGroup v-model="paramType" @on-change="handleParamTypeChange">
          <Radio label="M">月份</Radio>
          <Radio label="Q">季度</Radio>
        </RadioGroup>
        <div class="ivu-form-item-content">
          <xdo-select v-if="paramType==='Q'" v-model="searchParam.quarter" :options="this.mistaker.QUARTER_MAP"></xdo-select>
          <xdo-select v-if="paramType==='M'" v-model="searchParam.month" :options="this.mistaker.MONTH_MAP"></xdo-select>
        </div>
      </div>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { mistaker } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'statisticsHeadSearch',
    data() {
      return {
        paramType: 'M',
        searchParam: {
          declareCode: '',
          year: '',
          quarter: '',
          month: ''
        },
        cmbSource: {
          declareData: [],
          yearData: []
        },
        mistaker: mistaker
      }
    },
    created: function () {
      let me = this
      // 申报单位
      me.$http.post(csAPI.ieParams.CUT).then(res => {
        me.cmbSource.declareData = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.declareData = []
      })
      // 年份
      let yearRange = 10
      let thisYear = (new Date()).getFullYear()
      let years = []
      while (yearRange > -1) {
        years.push({
          value: (thisYear - yearRange).toString(),
          label: (thisYear - yearRange).toString()
        })
        yearRange--
      }
      me.$set(me.cmbSource, 'yearData', years)
    },
    methods: {
      handleParamTypeChange(selVal) {
        if (selVal === 'M') {
          this.$set(this.searchParam, 'quarter', '')
        } else {
          this.$set(this.searchParam, 'month', '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
