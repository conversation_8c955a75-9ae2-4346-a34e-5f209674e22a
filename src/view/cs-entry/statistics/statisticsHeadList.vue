<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <!--此Card为查询按钮的div 如查询 查询条件等-->
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <statisticsHeadSearch ref="headSearch"></statisticsHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoButton type="text" style="font-size: 12px;" :class="exportClass" @click="handleDownload" :key="导出">
          <XdoIcon :type="exportIcon" size="22" class="xdo-icon"/>
          导出
        </XdoButton>
        <XdoButton type="text" :disabled="isUpdate" style="font-size: 12px" :class="updateClass" @click="handleUpdate" :key="更新">
          <XdoIcon :type="updateIcon" size="22" class="xdo-icon"/>
          更新
        </XdoButton>
      </XdoCard>
      <!--点击更新的时候的提示框-->
      <XdoCard :bordered="false" v-show="isNotice">
        <p style="color: red;">{{notice}}</p>
      </XdoCard>
      <!--次card为加载的数据主体、分页等-->
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
 </section>
</template>

<script>
  import { csAPI, excelExport } from '@/api'
  import { pageParam } from '../../cs-common'
  import statisticsHeadSearch from './statisticsHeadSearch'
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './statisticsHeadListColumns'

  export default {
    name: 'statisticsHeadList',
    components: {
      statisticsHeadSearch
    },
    mixins: [dynamicHeight, columns],
    data() {
      return {
        isUpdate: false,
        isNotice: true,
        notice: '',
        showHead: true,
        showList: true,
        showSearch: false,
        showTabs: false,
        exportClass: 'xdo-btn-download',
        exportIcon: 'ios-cloud-download-outline',
        updateClass: 'xdo-btn-send',
        updateIcon: 'ios-cloud-done-outline',
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        dataList: [],
        compData: [],
        addParams: {
          headParam: {
            jobType: '02' //默认业务类型 02 删改单差错统计分析
          }
        }
      }
    },
    mounted: function () {
      this.refreshDynamicHeight(160, !this.showSearch ? ['area_search'] : null)
      this.handleSearchSubmit() //初始化数据加载js
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    },
    methods: {
      //点击页面更新按钮的方法（该方法调用后台接口往任务表中插入数据，待改造）
      handleUpdate() {
        this.$http.post(csAPI.entry.entryRecord.head.intoJob, this.addParams.headParam).then(res => {
          if (res.data.code === 200) {
            this.check = false
            this.notice = res.data.data.notice //消息提示赋值
            this.isNotice = true //把消息提醒放开显示出来
            //需要控制一下更新按钮是否置灰色
            if (res.data.data.notice === '差错统计分析运行中，不允许进行重复更新') {
              this.isUpdate = true
            }
          }
        }, () => {
        })
      },
      //点击查询条件按钮走的方法
      handleShowSearch() {
        this.showSearch = !this.showSearch
        this.refreshDynamicHeight(160, !this.showSearch ? ['area_search'] : null)
      },
      //页面一加载就走的方法走了后台查询所有的方法
      handleSearchSubmit() {
        this.pageParam.page = 1
        this.getList()
        //this.handleUpdate()
      },
      //最终请求后台查询所有数据的js方法
      getList() {
        pageParam.page = this.pageParam.page
        pageParam.limit = this.pageParam.limit
        const data = this.$refs.headSearch.searchParam
        this.$http.post(csAPI.entry.entryRecord.head.finalize, data, {params: pageParam}).then(res => {
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
        })
        this.isUpdate = false;//点击查询的时候先把更新按钮放开
      },
      //点击页面导出时候的方法
      handleDownload() {
        this.actions[0].loading = true
        const params = {
          exportColumns: Object.assign({}, this.$refs.headSearch.searchParam),
          name: '差错统计分析.xls',
          header: getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
        }
        excelExport(csAPI.entry.entryRecord.head.exportFin, params).finally(() => {
          this.actions[0].loading = false
        })
      },
      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        this.pageParam.page = page
        this.getList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        this.getList()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
