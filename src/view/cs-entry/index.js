/***
 * 关务-报关查询-路由
 */
import { namespace } from '@/project'
import statisticsHeadList from './statistics/statisticsHeadList'
import entryRecordHeadList from './entryRecord/entryRecordHeadList'
import entryDetailHeadList from './entryDetail/entryDetailHeadList'
import modifyRecordHeadList from './modifyRecord/modifyRecordHeadList'

export {
  statisticsHeadList,
  entryRecordHeadList,
  entryDetailHeadList,
  modifyRecordHeadList
}

export default  [
  {
    path: '/' + namespace + '/entry/entryRecord',
    name: 'entryRecordHeadList',
    meta: {
      title: '报关单记录'
    },
    component: entryRecordHeadList
  },
  {
    path: '/' + namespace + '/entry/modifyRecord',
    name: 'modifyRecordHeadList',
    meta: {
      title: '删改单记录'
    },
    component: modifyRecordHeadList
  },
  {
    path: '/' + namespace + '/entry/entryDetail',
    name: 'entryDetailHeadList',
    meta: {
      title: '报关单明细'
    },
    component: entryDetailHeadList
  },
  {
    path: '/' + namespace + '/entry/statistics',
    name: 'statisticsHeadList',
    meta: {
      title: '差错统计分析'
    },
    component: statisticsHeadList
  }
]
