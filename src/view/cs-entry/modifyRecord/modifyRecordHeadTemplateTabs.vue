<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <Head ref="head" :typeNoInfor="this.typeNo" :searchDataInfor="this.searchData" :textDataInfor="this.textData" :compData="compData"
              @onEditback="editback"></Head>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './modifyRecordHeadEdit'

  export default {
    name: "modifyRecordHeadTemplateTabs",
    components: {Head},
    props: {
      typeNo: {
        type: String,
        default: () => ({})
      },
      searchData: {
        type: Object,
        default: () => ({})
      },
      textData: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        tabName: 'headTab',
        searchDataInfor: {},
        textDataInfor: {},
        typeNoInfor: ''
      }
    },
    methods: {
      editback(val) {
        if (val) {
          this.$emit('oneditback', true)
        }
      }
    }
  }
</script>

<style scoped>
</style>
