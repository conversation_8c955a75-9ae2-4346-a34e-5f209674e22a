<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="删改单标志">
        <xdo-select v-model="searchParam.dmMark" :options="this.inDataSource.documnetMark" @on-change="onDmMarkChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="状态">
        <xdo-select v-model="searchParam.status" :options="this.inDataSource.documnetStatue" @on-change="onStatusChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="删改报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="iemark" label="进出口标志">
        <xdo-select v-model="searchParam.iemark" :options="this.inDataSource.I_E_MARK" @on-change="onIEMarkChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="ieport" label="进出境关别">
        <xdo-select v-model="searchParam.ieport" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="删改单日期"  @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      <XdoFormItem prop="status" label="差错属性">
        <xdo-select v-model="searchParam.errorType" :options="this.inDataSource.templateErrorType" @on-change="onErrorTypeChange"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { delList } from '../../cs-common'

  export default {
    name: 'DecEntryHeadFormalHeadSearch',
    data() {
      return {
        searchParam: {
          dmMark: '',           // 删改单标志
          status: '',           // 状态
          entryNo: '',          // 删改报关单号·
          iemark: '',           // 进出口标志
          ieport: '',           // 进出境关别
          tradeMode: '',        // 监管方式
          errorType: '',        // 差错属性
          inputDateFrom: '',    // 删改单日期开始时间
          inputDateTo: ''       // 删改单日期结束时间
        },
        inDataSource: {
          I_E_MARK: delList.I_E_MARK,
          documnetMark: delList.documnetMark,
          documnetStatue: delList.documnetStatue,
          templateErrorType: delList.templateErrorType
        }
      }
    },
    methods: {
      handleDMDateChange(e) {
        this.searchParam.inputDateFrom = e[0]  // 将删改单日期开始时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
        this.searchParam.inputDateTo = e[1]    // 删改单日期结束时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
      }
    }
  }
</script>

<style scoped>
</style>
