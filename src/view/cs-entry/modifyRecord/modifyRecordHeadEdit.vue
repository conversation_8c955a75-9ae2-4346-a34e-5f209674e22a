<template>
  <section>
    <XdoCard :bordered="false">
      <div class="xdo-enter-root" v-focus>
        <XdoForm ref="addParams.headParam" class="dc-form dc-form-3" label-position="right" :label-width="100" :model="addParams.headParam" :rules="ruleValidate" inline>
          <XdoFormItem prop="entryNo" label="删改单号">
            <XdoIInput placeholder="请输入删改单号再回车" type="text" :disabled="del" v-model="addParams.headParam.entryNo" :maxlength="18" @on-enter="selByEntryNo"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="dmMark" label="删改单标志">
            <xdo-select :options="inDataSource.documnetMark" v-model="addParams.headParam.dmMark"
                        :optionLabelRender="pcodeRender" :disabled="isText" @on-change="dynamicStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="entryNoAgain" label="重报单号">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.entryNoAgain" :maxlength="18"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="删改单日期">
            <XdoDatePicker type="date" v-model="addParams.headParam.dmDate" :disabled="isText" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </XdoFormItem>

          <XdoFormItem prop="errorType" label="差错属性">
            <xdo-select :options="inDataSource.templateErrorType" v-model="addParams.headParam.errorType"
                        :optionLabelRender="pcodeRender" :disabled="isText" @on-change="dynamicStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="status" label="状态">
            <xdo-select :options="inDataSource.documnetStatue" v-model="addParams.headParam.status"
                        :optionLabelRender="pcodeRender" :disabled="isText" @on-change="dynamicStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="进出境关别">
            <xdo-select v-model="addParams.headParam.ieport" :disabled="isText" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="申报日期">
            <XdoDatePicker type="date" v-model="addParams.headParam.ddate" :disabled="isText" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </XdoFormItem>

          <XdoFormItem label="备案号">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.emsNo" :maxlength="8"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="申报总量">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.decTotalQty" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="申报总价">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.decTotal" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="申报币制">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.curr" :maxlength="20" placeholder="多个用 / 分隔"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="运输方式">
            <xdo-select :asyncOptions="pcodeList" :meta="pcode.transf" v-model="addParams.headParam.trafMode"
                        :optionLabelRender="pcodeRender" :disabled="isText"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="提运单号">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.billNo" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="监管方式">
            <xdo-select v-model="addParams.headParam.tradeMode" :disabled="isText" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeCountry" label="启(抵)运国">
            <xdo-select v-model="addParams.headParam.tradeCountry" :disabled="isText" :asyncOptions="pcodeList" :meta="pcode.country_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="wrapType" label="包装种类">
            <xdo-select v-model="addParams.headParam.wrapType" :asyncOptions="pcodeList" :meta="pcode.wrap" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="件数">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.packNum" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="毛重">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.grossWt" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="净重">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.netWt" :maxlength="20" notConvertNumber></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="dataSource" label="数据来源">
            <xdo-select :options="inDataSource.documnetDataSource" v-model="addParams.headParam.dataSource"
                        :optionLabelRender="pcodeRender" :disabled="dataSource" @on-change="dynamicStatus"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="申报单位">
              <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.declareName" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="删改单原因">
            <XdoIInput type="text" :disabled="isText" v-model="addParams.headParam.dmReason" :maxlength="100"></XdoIInput>
          </XdoFormItem>
        </XdoForm>
        <ul>
          <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                  :show-upload-list="false" :format="['xls','xlsx']" :on-format-error="handleFormatError" :on-success="handleOnSuccess" :disabled="check">
            <strong>
              <a icon="md-cloud-upload"  long @click.prevent="upLoad()">
                <XdoIcon type="ios-loop-strong"></XdoIcon>
                [上传]
              </a>
            </strong>
          </Upload>
          <li v-for="item in fileName" :key="item.sid">
            <XdoIcon title="删除" type="md-close" @click.prevent="deleteTemplate(item.sid)" v-show="buttonShow" />
            <a @click.prevent="downloadFile(item.sid)">{{item.fileNameOrigin}}</a>
          </li>
        </ul>
      </div>
    </XdoCard>
    <br/>
    <div class="dc-container-center">
      <XdoButton class="btntool" type="warning" v-if="btnShow" @click="templateSave()">保存</XdoButton>&nbsp;
      <XdoButton class="btntool" type="warning" @click="saveClose" v-if="btnShow">保存并关闭</XdoButton>&nbsp;
      <XdoButton class="btntool"  type="primary" @click="closeAdd">关闭</XdoButton>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { delList } from '../../cs-common'
  import { isNullOrEmpty, blobSaveFile } from '@/libs/util'

  export default {
    name: 'modifyRecordHeadEdit',
    props: {
      typeNoInfor: {
        type: String,
        default: () => ({})
      },    // 传值List页面中的 typeNo
      searchDataInfor: {
        type: Object,
        default: () => ({})
      },
      textDataInfor: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        ruleValidate: {
          dmMark: [{required: true, message: '不能为空！', trigger: 'blur'}],
          status: [{required: true, message: '不能为空！', trigger: 'blur'}],
          entryNo: [{required: true, message: '不能为空！', trigger: 'blur'}],
          errorType: [{required: true, message: '不能为空！', trigger: 'blur'}],
          dataSource: [{required: true, message: '不能为空！', trigger: 'blur'}]
        },
        dataSource: false,
        del: false,
        fileName: [],
        check: false,
        buttonShow: true,
        actionType: 0,
        sid: '',
        isStatus: false,
        isText: false,//是否可编辑
        isSelect: false,
        btnShow: true,
        inDataSource: {
          //其中配置了一些下拉的选项
          documnetMark: delList.documnetMark,             // 删改单标记
          documnetStatue: delList.documnetStatue,         // 状态
          templateErrorType: delList.templateErrorType,   // 差错属性
          documnetDataSource: delList.documnetDataSource  // 数据源
        },
        addParams: {
          headParam: {
            sid: '',
            dmMark: '',//单证属性 删改单标志
            entryNo: '',//模版编号 删改单号
            entryNoAgain: '',//模版编号 重报单号
            dmDate: '',//录入时间  删改单日期
            errorType: '',//差错属性
            status: '',//状态
            ieport: '',//进出境关别
            ddate: '',//申报日期
            emsNo: '',//备案号
            decTotalQty: '',//申报总量
            decTotal: '',//申报总价
            curr: '',//申报币制
            trafMode: '', //运输方式
            billNo: '',//提运单号
            tradeMode: '',//监管方式
            tradeCountry: '',//启(抵)运国 编 码
            tradeCountryName: '',//启(抵)运国名称
            wrapType: '',//包装种类
            packNum: '',//包装件数
            grossWt: '',//毛重
            netWt: '',//净重
            dataSource: '',//数据来源
            declareName: '',//申报单位名称
            declareCode: '',//申报单位编码
            dmReason: '',//删改单原因
          },
        },
        addSuccesInfor: {},
        uploadFileConfig: {
          data: {
            headId: ''
          },
          headers: {
            Authorization: 'Bearer ' + this.$store.state.token
          },
          action: csAPI.entry.entryRecord.head.templatePus.pushUpload   // 上传的后台接口地址
        }
      }
    },
    mounted() {
      let me = this
      me.searchDataInforMethods(me.searchDataInfor)// 查看的方法，一面一加载就走吃的方法
      me.typeNoInforMethods(me.typeNoInfor) //编辑的方法，一面一加载就走吃的方法
    },
    methods: {
      // 删改单框敲击回车的时候走的js放发
      selByEntryNo() {
        let me = this
        let reentryNo = me.addParams.headParam.entryNo
        let testReg = /^[0-9]{8}[0-1][0-9]{9}$/// 判断18数字，第九位为0或者1
        let testNumLenReg = /^[0-9]{18}$/
        if (!me.isNotNull(reentryNo)) {
          // 说明页面没有输入任何值，直接敲击回车的
          me.$Message.warning('请输入删改单号再回车!')
          return false
        } else if (!testNumLenReg.test(reentryNo)) {
          me.$Message.warning('删改单号必须为18位!')
          return false
        } else {
          if (!testReg.test(reentryNo)) {
            me.$Message.warning('删改单号第九位必须是0或者1!')
            return false
          } else {
            me.findRe(reentryNo)
          }
        }
      },
      // 里面写具体调用后台的方法
      findRe(entryNo) {
        let me = this
        //把删改单号 传入后台走一下接口，获取其他变量数据
        me.$http.post(csAPI.entry.entryRecord.head.getOthersInfo + `/${entryNo}`).then(res => {
          if (res.data.code === 200) {
            me.$Message.success('删改单号回车关联成功!')
            me.addParams.headParam.declareName = res.data.data[0].declareName//申报单位名称
            me.addParams.headParam.declareCode = res.data.data[0].declareCode//申报单位编码
            me.addParams.headParam.ieport = res.data.data[0].ieport//进出境关别
            me.addParams.headParam.ddate = res.data.data[0].ddate//申报日期
            me.addParams.headParam.emsNo = res.data.data[0].emsNo//备案号
            me.addParams.headParam.trafMode = res.data.data[0].trafMode//运输方式
            me.addParams.headParam.billNo = res.data.data[0].billNo//提运单号
            me.addParams.headParam.tradeMode = res.data.data[0].tradeMode//监管方式
            me.addParams.headParam.tradeCountry = res.data.data[0].tradeCountry//启(抵)运国
            me.addParams.headParam.wrapType = res.data.data[0].wrapType//包装种类
            me.addParams.headParam.packNum = res.data.data[0].packNum//件数
            me.addParams.headParam.grossWt = res.data.data[0].grossWt//毛重
            me.addParams.headParam.netWt = res.data.data[0].netWt//净重
            me.addParams.headParam.decTotalQty = res.data.data[0].qty//申报总量
            me.addParams.headParam.decTotal = res.data.data[0].decTotal//申报总价
            me.addParams.headParam.curr = res.data.data[0].currName//申报币制
          } else {
            //说明没有查到关联的信息
            me.$Message.warning('未关联到相关信息，请手动填写!')
          }
        }).catch(() => {
        })
      },
      // 选中一条点击编辑的时候走的方法
      searchDataInforMethods(val) {
        let me = this
        me.addParams.headParam.sid = val.sid
        me.addParams.headParam.status = val.status
        me.addParams.headParam.emsNo = val.emsNo
        me.addParams.headParam.entryNo = val.entryNo
        me.addParams.headParam.dmMark = val.dmMark
        me.addParams.headParam.entryNoAgain = val.entryNoAgain
        me.addParams.headParam.dmDate = val.dmDate
        me.addParams.headParam.errorType = val.errorType
        me.addParams.headParam.ieport = val.ieport
        me.addParams.headParam.ddate = val.ddate
        me.addParams.headParam.decTotalQty = val.decTotalQty
        me.addParams.headParam.decTotal = val.decTotal
        me.addParams.headParam.curr = val.curr
        me.addParams.headParam.trafMode = val.trafMode
        me.addParams.headParam.billNo = val.billNo
        me.addParams.headParam.tradeMode = val.tradeMode
        me.addParams.headParam.tradeCountry = val.tradeCountry
        me.addParams.headParam.wrapType = val.wrapType
        me.addParams.headParam.packNum = val.packNum
        me.addParams.headParam.grossWt = val.grossWt
        me.addParams.headParam.netWt = val.netWt
        me.addParams.headParam.dataSource = val.dataSource
        me.addParams.headParam.declareName = val.declareName
        me.addParams.headParam.dmReason = val.dmReason
      },
      // 0：新增  1 编辑   2  查看
      typeNoInforMethods(newVal) {
        let me = this
        if (newVal === 0) {
          //新增
          me.addParams.headParam.dataSource = '1'
          me.actionType = 0
          me.btnShow = true
          me.isText = false
          me.isStatus = true
          me.isSelect = false
          me.check = false
          me.dataSource = true//控制数据来源的按钮不可改动
          me.getNowTime()
        } else if (newVal === 1) {
          //编辑
          me.actionType = 1
          me.btnShow = true
          me.isText = false
          me.isStatus = true
          me.isSelect = false
          me.check = false
          me.dataSource = true//控制数据来源的按钮不可改动
          me.getAllInforedit(me.addParams.headParam.sid)//走一下附件读取的方法
          me.sid = me.addParams.headParam.sid
          me.del = me.addParams.headParam.dataSource === 2
        } else if (newVal === 2) {
          //查看
          me.btnShow = false
          me.isText = true
          me.isStatus = true
          me.isSelect = true
          me.buttonShow = false
          me.check = true
          me.dataSource = true//控制数据来源的按钮不可改动
          me.del = true//控制数据来源的按钮不可改动
          me.getAllInforlook(me.addParams.headParam.sid)//走一下附件读取的方法
        }
      },
      // 删改单标记 js方法
      dynamicStatus(e) {
        let me = this
        if (e === '1') {
          me.isSelect = true
        } else if (e === '2' && me.typeNoInfor !== 2) {
          me.isSelect = false
        }
      },
      // 一些验证判断
      panduan() {
        let me = this
        let reentryNo = me.addParams.headParam.entryNo
        let reentryNoAgain = me.addParams.headParam.entryNoAgain
        let dMark = me.addParams.headParam.dmMark//标记
        let erType = me.addParams.headParam.errorType//差错属性
        let statu = me.addParams.headParam.status//状态
        let daSource = me.addParams.headParam.dataSource//数据来源
        let testReg = /^[0-9]{8}[0-1][0-9]{9}$/// 判断18数字，第九位为0或者1
        let testNumLenReg = /^[0-9]{18}$/

        if (me.isNotNull(reentryNo)) {
          if (!testNumLenReg.test(reentryNo)) {
            me.$Message.warning('删改单号必须为18位!')
            return false
          } else {
            if (!testReg.test(reentryNo)) {
              me.$Message.warning('删改单号第九位必须是0或者1!')
              return false
            }
            if (me.isNotNull(reentryNoAgain)) {
              if (!testNumLenReg.test(reentryNoAgain)) {
                me.$Message.warning('重报单号必须为18位!')
                return false
              } else {
                if (!testReg.test(reentryNoAgain)) {
                  me.$Message.warning('重报单号第九位必须是0或者1!')
                  return false
                }
                //再判断 标记 差错属性 数据来源  状态 为非空   dMark erType statu daSource
                if (!me.isNotNull(dMark)) {
                  //提示不可以为空值
                  me.$Message.warning('删改单标记不可为空!')
                  return false
                } else if (!me.isNotNull(erType)) {
                  //提示不可以为空值
                  me.$Message.warning('差错属性不可为空!')
                  return false
                } else if (!me.isNotNull(statu)) {
                  //提示不可以为空值
                  me.$Message.warning('状态不可为空!')
                  return false
                } else if (!me.isNotNull(daSource)) {
                  //提示不可以为空值
                  me.$Message.warning('数据源不可为空!')
                  return false
                } else {
                  return true
                }
              }
            } else {
              //再判断 标记 差错属性 数据来源  状态 为非空   dMark erType statu daSource
              if (!me.isNotNull(dMark)) {
                //提示不可以为空值
                me.$Message.warning('删改单标记不可为空!')
                return false
              } else if (!me.isNotNull(erType)) {
                //提示不可以为空值
                me.$Message.warning('差错属性不可为空!')
                return false
              } else if (!me.isNotNull(statu)) {
                //提示不可以为空值
                me.$Message.warning('状态不可为空!')
                return false
              } else if (!me.isNotNull(daSource)) {
                //提示不可以为空值
                me.$Message.warning('数据源不可为空!')
                return false
              } else {
                return true
              }
            }
          }
        } else {
          me.$Message.warning('删改单号不能为空!')
          return false
        }
      },
      // 如果参数为空则为false；
      isNotNull(objectNum) {
        let flag = false
        flag = !isNullOrEmpty(objectNum)
        return flag
      },
      // 保存
      templateSave() {
        let me = this
        let re = me.panduan()
        if (re) {
          if (me.actionType === 0) {
            //保存
            me.$http.post(csAPI.entry.entryRecord.head.add.intoInfo, me.addParams.headParam).then(res => {
              me.check = false
              me.$Message.success('保存成功!')
              me.sid = res.data.data.sid
              me.addSuccesInfor = res.data.data
              me.actionType = 1
            }).catch(() => {
            })
          } else if (me.actionType === 1) {
            //修改
            if (!isNullOrEmpty(me.searchDataInfor.sid)) {
              //页面跳转过
              me.addParams.headParam.sid = me.searchDataInfor.sid
              me.$http.put(csAPI.entry.entryRecord.head.updateIn.updateInfo + `/${me.searchDataInfor.sid}`, me.addParams.headParam).then(res => {
                me.$Message.success('编辑成功!')
                me.addSuccesInfor = res.data.data
              }).catch(() => {
              })
            } else {
              //本页面 修改，解决sid拿不到的问题
              me.$http.put(csAPI.entry.entryRecord.head.updateIn.updateInfo + `/${me.sid}`, me.addParams.headParam).then(res => {
                me.$Message.success('编辑成功!')
                me.addSuccesInfor = res.data.data
              }).catch(() => {
              })
            }
          }
          me.buttonShow = true//吧删除附件的放开
          return true
        }
      },
      // 保存 + 关闭
      saveClose() {
        let me = this
        let re = me.templateSave() // 调用保存方法
        if (re) {
          setTimeout(me.closeAdd, 1000)
        }
      },
      // 单纯关闭
      closeAdd() {
        let me = this
        me.isText = false
        me.isStatus = false
        me.buttonShow = true//吧删除附件的放开
        me.dataSource = false//关闭的时候把数据来源的 按钮改为可控制
        me.del = true//控制数据来源的按钮不可改动
        me.addParams.headParam.dataSource = ''//关闭的时候把数据源容器值删除
        me.$emit("onEditback", true)
      },
      getNowTime() {
        let me = this
        let date = new Date()
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let ri = date.getDate()
        let hour = date.getHours()
        let minute = date.getMinutes()
        let second = date.getSeconds()
        me.addParams.headParam.insertTime = `${year}/${month}/${ri}  ${hour}:${minute}:${second}`
      },
      // 导出方法
      saveExcelFile(res, filename) {
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'})
        blobSaveFile(blob, filename)
      },
      // 上传附件
      upLoad() {
        let me = this
        if (isNullOrEmpty(me.sid)) {
          me.check = true
          me.$Message.warning('先保存数据,再上传文件!')
        } else {
          me.uploadFileConfig.data.headId = me.sid
        }
      },
      handleFormatError() {
        let me = this
        me.$Message.warning('文件格式不对')
      },
      //附件上传成功
      handleOnSuccess(e) {
        let me = this
        if (e.success && e.code === 200) {
          me.$Message.success('上传成功!')
          me.getAllInfor()      // 调用该方法获取上传的文件的所有信息方法
        } else {
          me.$Message.warning(`${e.message}`)
        }
      },
      // 编辑的时候读取后台附件的方法
      getAllInforedit(e) {
        let me = this
        me.$http.post(csAPI.entry.entryRecord.head.getFileInfo + `/${e}`).then(res => {
          me.fileName = []
          me.addSuccesInfor = res.data.data
          res.data.data.forEach((item) => {
            me.fileName.push(item)
          })
        }).catch(() => {
        })
        me.buttonShow = true
      },
      // 点击单条查看的时候 获取模版所有信息
      getAllInforlook(e) {
        let me = this
        me.$http.post(csAPI.entry.entryRecord.head.getFileInfo + `/${e}`).then(res => {
          me.fileName = []
          me.addSuccesInfor = res.data.data
          res.data.data.forEach((item) => {
            me.fileName.push(item)
          })
        }).catch(() => {
        })
        me.buttonShow = false
      },
      // 获取模版所有信息
      getAllInfor() {
        let me = this
        me.$http.post(csAPI.entry.entryRecord.head.getFileInfo + `/${me.sid}`).then(res => {
          me.fileName = []
          me.addSuccesInfor = res.data.data
          res.data.data.forEach((item) => {
            me.fileName.push(item)
          })
        }).catch(() => {
        })
      },
      // 下载发票模版
      downloadFile(filId) {
        let me = this
        // 此处的sid，应该传当前的附件的唯一id，而不是传headId
        let excelRes = me.$http.get(csAPI.entry.entryRecord.head.getFujian + `/${filId}`, {responseType: 'blob'})
        return excelRes.then(res => {
          me.saveExcelFile(res, `模版.xls`)
        }).catch(() => {
        })
      },
      // 删除发票模版
      deleteTemplate(filId) {
        let me = this
        me.$http.delete(csAPI.entry.entryRecord.head.deleteFujian + `/${filId}`).then(() => {
          me.$Message.success('删除成功!')
          me.getAllInfor()                // 删除成功后，走一下文件读取的方法
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  ul li {
    color: #999;
    list-style: none;
    margin-left: 15px;
    display: inline-block;
  }
</style>
