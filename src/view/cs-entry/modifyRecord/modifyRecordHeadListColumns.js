const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'status'
  , 'iemark'
  , 'dmMark'
  , 'entryNo'
  , 'entryNoAgain'
  , 'dmDate'
  , 'declareName'
  , 'ieport'
  , 'ddate'
  , 'emsNo'
  , 'decTotalQty'
  , 'decTotal'
  , 'curr'
  , 'dmReason'
  , 'errorType'
  , 'file'
  , 'trafMode'
  , 'billNo'
  , 'tradeMode'
  , 'tradeCountry'
  , 'wrapType'
  , 'packNum'
  , 'grossWt'
  , 'netWt'
  , 'dataSource'
]

const excelColumnsConfig = [
  'sid'
  , 'status'
  , 'iemark'
  , 'dmMark'
  , 'entryNo'
  , 'entryNoAgain'
  , 'dmDate'
  , 'declareName'
  , 'ieport'
  , 'ddate'
  , 'emsNo'
  , 'decTotalQty'
  , 'decTotal'
  , 'curr'
  , 'dmReason'
  , 'errorType'
  , 'file'
  , 'trafMode'
  , 'billNo'
  , 'tradeMode'
  , 'tradeCountry'
  , 'wrapType'
  , 'packNum'
  , 'grossWt'
  , 'netWt'
  , 'dataSource'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.handleEditByRow(params.row)
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleViewByRow(params.row)
                  }
                }
              }, '查看')
            ]);
          },
          key: 'operation'
        },
        {
          title: '状态',
          minWidth: 100,
          align: 'center',
          key: 'status',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '进出口标志',
          minWidth: 100,
          align: 'center',
          key: 'iemark',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '删改单标志',
          minWidth: 100,
          align: 'center',
          key: 'dmMark',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '删改报关单号',
          minWidth: 140,
          align: 'center',
          key: 'entryNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '删单重报报关单号',
          minWidth: 140,
          align: 'center',
          key: 'entryNoAgain',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '删改单日期',
          minWidth: 120,
          align: 'center',
          key: 'dmDate',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报单位',
          minWidth: 150,
          align: 'center',
          key: 'declareName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '进出境关别',
          minWidth: 100,
          align: 'center',
          key: 'ieport',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报日期',
          minWidth: 120,
          align: 'center',
          key: 'ddate',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '备案号',
          minWidth: 120,
          align: 'center',
          key: 'emsNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报总量',
          minWidth: 100,
          align: 'center',
          key: 'decTotalQty',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报总价',
          minWidth: 100,
          align: 'center',
          key: 'decTotal',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '申报币制',
          minWidth: 100,
          align: 'center',
          key: 'curr',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '删改单原因',
          minWidth: 100,
          align: 'center',
          key: 'dmReason',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '差错属性',
          minWidth: 100,
          align: 'center',
          key: 'errorType',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '运输方式',
          minWidth: 100,
          align: 'center',
          key: 'trafMode',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '提运单号',
          minWidth: 140,
          align: 'center',
          key: 'billNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '监管方式',
          minWidth: 100,
          align: 'center',
          key: 'tradeMode',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '启(抵)运国',
          minWidth: 100,
          align: 'center',
          key: 'tradeCountry',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '包装种类',
          minWidth: 100,
          align: 'center',
          key: 'wrapType',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '件数',
          minWidth: 100,
          align: 'center',
          key: 'packNum',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '毛重',
          minWidth: 100,
          align: 'center',
          key: 'grossWt',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '净重',
          minWidth: 100,
          align: 'center',
          key: 'netWt',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '数据来源',
          minWidth: 100,
          align: 'center',
          key: 'dataSource',
          ellipsis: true,
          tooltip: true,
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
