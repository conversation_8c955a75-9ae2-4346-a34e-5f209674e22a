<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <modifyRecordHeadSearch ref="headSearch"></modifyRecordHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                    style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </Button>&nbsp;
          </template>
          <XdoButton type="text" :disabled="isUpdate" style="font-size: 12px;" :class="getInfoClass" @click="getInto" :key="提取">
            <XdoIcon :type="getInfoIcon" size="22" class="xdo-icon"/>
            提取
          </XdoButton>
        </div>
      </XdoCard>

      <!--页面任务表的提示栏-->
      <XdoCard :bordered="false" v-show="isNotice">
        <p style="color: red;">{{notice}}</p>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <!--编辑页面 先不开，需要研究-->
    <modifyRecordHeadTemplateTabs v-if="!showList&&showTabs" :typeNo="typeNo" :searchData="searchData" :textData="textData" :compData="compData"
                                  @oneditback="editBack"></modifyRecordHeadTemplateTabs>
  </section>
</template>

<script>
  import { csAPI, excelExport } from '@/api'
  import { editStatus, pageParam } from '../../cs-common'
  import modifyRecordHeadSearch from './modifyRecordHeadSearch'
  import modifyRecordHeadTemplateTabs from "./modifyRecordHeadTemplateTabs"
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './modifyRecordHeadListColumns'

  export default {
    name: 'modifyRecordHeadList',
    components: {
      modifyRecordHeadSearch,
      modifyRecordHeadTemplateTabs
    },
    mixins: [dynamicHeight, columns],
    data() {
      let btnComm = {
        type: 'text',
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        tableloading: false,
        isUpdate: false,
        isNotice: true,
        notice: '',

        showHead: true,
        showList: true,
        showSearch: false,
        showTabs: false,
        getInfoClass: 'xdo-btn-send',
        getInfoIcon: 'ios-cloud-done-outline',
        actions: [
          {...btnComm, label: '导出', key: 'xdo-btn-download', click: this.handleDownload, icon: 'ios-cloud-download-outline'},
          {...btnComm, label: '新增', key: 'xdo-btn-add', click: this.handleAdd, icon: 'ios-add'},
          {...btnComm, label: '编辑', key: 'xdo-btn-edit', click: this.handleEdit, icon: 'ios-create-outline'},
          {...btnComm, label: '删除', key: 'xdo-btn-delete', click: this.handleDelete, icon: 'ios-trash-outline'}
        ],
        editConfig: {
          editData: {},
          headId: '',
          editStatus: editStatus.SHOW
        },
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        dataList: [],
        compData: [],
        addJobs: {
          headJobs: {
            jobType: '01' //默认业务类型  01 删改单信息提取，
          }
        }
      }
    },
    mounted: function () {
      this.refreshDynamicHeight(120, !this.showSearch ? ['area_search'] : null)
      this.handleSearchSubmit()
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    },
    methods: {
      // 点击页面更新按钮的方法（该方法调用后台接口往任务表中插入数据，待改造）
      getInto() {
        this.$http.post(csAPI.entry.entryRecord.head.intoJobByMdify, this.addJobs.headJobs).then(res => {
          this.notice = res.data.data.notice  // 消息提示赋值
          this.isNotice = true                // 把消息提醒放开显示出来
          //需要控制一下更新按钮是否置灰色
          if (res.data.data.notice === '提取任务运行中，不允许进行重复提交') {
            this.isUpdate = true
          }
        }, () => {
        })
      },
      /**
       * 用于AgGrid单元格
       * @param options
       */
      onAgCellOperation(options) {
        if (options.methods === 'handleEditByRow') {
          this.handleEditByRow(options.params)
        } else if (options.methods === 'handleViewByRow') {
          this.handleViewByRow(options.params)
        }
      },
      // 页面点击每一行的查看的时候走的js方法查看时候走的方法，该方法在column.js中定义的方法
      handleViewByRow(val) {
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.searchData = val
        this.typeNo = 2       //查看
      },
      // 点击页面新增按钮的方法
      handleAdd() {
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.searchData = {}
        this.typeNo = 0       // 用来标记是新增还是修改，0表示新增操作
      },
      /**
       * 列表中点击数据编辑
       * @param row
       */
      handleEditByRow(row) {
        this.searchData = row
        this.showHead = !this.showHead
        this.showList = !this.showList
        this.showTabs = !this.showTabs
        this.compData = this.dataList
        this.typeNo = 1     // 用来标记是新增还是修改，1表示修改操作
        this.gridConfig.selectRows = []
      },
      customCheck(selRows, opTitle) {
        console.info('执行了自定义【' + opTitle + '】检查')
        return true
      },
      //页面编辑按钮
      handleEdit() {
        if (this.gridConfig.selectRows.length === 0) {
          this.$Message.warning('请选择您要编辑的数据!')
        } else if (this.gridConfig.selectRows.length > 1) {
          this.$Message.warning('一次仅能编辑一条数据!')
        } else {
          this.handleEditByRow(this.gridConfig.selectRows[0])
        }
      },
      handleShowSearch() {
        this.showSearch = !this.showSearch
        this.refreshDynamicHeight(120, !this.showSearch ? ['area_search'] : null)
      },
      //页面一加载就走的方法走了后台查询所有的方法
      handleSearchSubmit() {
        this.pageParam.page = 1
        this.getList() //走一下后台的初始化方法
      },
      //最终请求后台查询所有数据的js方法
      getList() {
        this.tableloading = true
        pageParam.page = this.pageParam.page
        pageParam.limit = this.pageParam.limit
        const data = this.$refs.headSearch.searchParam
        this.$http.post(csAPI.entry.entryRecord.head.select.selectDelAllPaged, data, {params: pageParam}).then(res => {
          this.tableloading = false
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
        })
      },
      //点击页面导出时候的方法
      handleDownload() {
        this.actions[0].loading = true
        const params = {
          name: '删改单记录',
          header: getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig),
          exportColumns: Object.assign({}, this.$refs.headSearch.searchParam)
        }
        excelExport(csAPI.entry.entryRecord.head.exportsgUrl, params).finally(() => {
          this.actions[0].loading = false
        })
      },
      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        this.pageParam.page = page
        this.getList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        this.getList()
      },
      editBack(val) {
        if (val) {
          this.showHead = true
          this.showList = true
          this.showTabs = false
          this.gridConfig.selectRows = []
          this.getList()
        }
      },
      //删除
      handleDelete() {
        if (this.gridConfig.selectRows.length > 0) {
          this.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              this.actions[3].loading = true
              const sids = this.gridConfig.selectRows.map(item => {
                return item.sid
              })
              this.$http.delete(csAPI.entry.entryRecord.head.delete + `/${sids}`).then(() => {
                this.$Message.success('删除成功!')
                this.gridConfig.selectRows = []
                this.getList()
              }).catch(err => {
                this.$Message.success(`${err.data.message}`)
              }).finally(() => {
                this.actions[3].loading = false
              })
            }
          })
        } else {
          this.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
