<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <entryRecordHeadSearch ref="headSearch"></entryRecordHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                    style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </Button>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <XdoModal ref="ddd" v-model="showModal" title="预览" fullscreen
              :footer-hide="true" :mask-closable="false">
      <EntryPreview v-if="showModal" :searchData="searchData"></EntryPreview>
    </XdoModal>
  </section>
</template>

<script>
  import EntryPreview from './entryPreview'
  import { csAPI, excelExport, pdfExport } from '@/api'
  import entryRecordHeadSearch from './entryRecordHeadSearch'
  import { editStatus, pageParam, entryManage } from '../../cs-common'
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './entryRecordHeadListColumns'

  export default {
    name: 'entryRecordHeadList',
    components: {
      EntryPreview,
      entryRecordHeadSearch
    },
    mixins: [dynamicHeight, columns],
    data() {
      return {
        showHead: true,
        searchData: {},
        showModal: false,
        showSearch: false,
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        gridConfig: {
          data: [],
          selectData: [],
          selectRows: [],
          gridColumns: []
        },
        entryManage: entryManage,
        editConfig: {
          headId: '',
          editData: {},
          editStatus: editStatus.SHOW
        },
        actions: [{
          type: 'text',
          needed: true,
          label: '导出',
          loading: false,
          disabled: false,
          key: 'xdo-btn-download',
          click: this.handleDownload,
          icon: 'ios-cloud-download-outline'
        }]
      }
    },
    mounted: function () {
      let me = this
      me.refreshDynamicHeight(120, !me.showSearch ? ['area_search'] : null)
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    },
    methods: {
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(120, !me.showSearch ? ['area_search'] : null)
      },
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      getList() {
        let me = this
        pageParam.page = me.pageParam.page
        pageParam.limit = me.pageParam.limit
        const data = me.$refs.headSearch.searchParam
        me.$http.post(csAPI.entry.entryRecord.head.selectAllPaged, data, {params: pageParam}).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
        })
      },
      handleDownload() {
        let me = this
        me.actions[0].loading = true
        excelExport(csAPI.entry.entryRecord.head.exportUrl, {
          name: '报关单记录',
          header: getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig),
          exportColumns: Object.assign({}, me.$refs.headSearch.searchParam)
        }).finally(() => {
          me.actions[0].loading = false
        })
      },
      handleSelectionChange(selectRows) {
        let me = this
        me.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        me.getList()
      },
      handleRowDblClick(item) {
        let me = this
        me.editConfig.editData = item
        me.editConfig.headId = item.sid
        me.editConfig.editStatus = editStatus.SHOW
        me.showHead = false
      },
      handlePrintEntry(item) {
        pdfExport(csAPI.entry.entryRecord.head.printUrl, {
          sid: item.sid,
          name: '报关单' + item.entryNo
        })
      },
      handlePreview(e) {
        let me = this
        me.showModal = true
        me.searchData = e
      },
      editBack(val) {
        if (val) {
          let me = this
          me.showHead = true
          me.gridConfig.selectRows = []
          me.getList()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
