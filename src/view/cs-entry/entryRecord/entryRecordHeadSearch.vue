<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="iemark" label="进出口标志">
        <xdo-select v-model="searchParam.iemark" :options="this.entryManage.I_E_MARK" @on-change="onIEMarkChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="ieport" label="进出境关别">
        <xdo-select v-model="searchParam.ieport" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="进出口日期" @onDateRangeChanged="handleIEDateChange" ></dc-dateRange>
      <dc-dateRange label="申报日期" @onDateRangeChanged="handleDDateChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="overseasShipper" label="境外发(收)货人">
        <xdo-select v-model="searchParam.overseasShipper" :options="this.cmbDataSource.overseasShipperList" :optionLabelRender="pcodeRender" @on-change="overseasShipperChange"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" meta="TRANSF_COMBINATION" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafName" label="运输工具名称">
        <XdoIInput type="text" v-model="searchParam.trafName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="billNo" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.billNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" meta="TRADE_COMBINATION" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="licenseNo" label="许可证号">
        <XdoIInput type="text" v-model="searchParam.licenseNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="declareCode" label="申报单位">
        <xdo-select v-model="searchParam.declareCode" :options="this.cmbDataSource.declareCodeList" :optionLabelRender="pcodeRender" @on-change="declareCodeChange"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { entryManage } from '../../cs-common'
  import { ArrayToLocaleLowerCase, isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'entryRecordHeadSearch',
    data() {
      return {
        searchParam: {
          iemark: '',
          declareCode: '',
          entryNo: '',
          ieport: '',
          iEDateFrom: '',
          iEDateTo: '',
          dDateFrom: '',
          dDateTo: '',
          emsNo: '',
          overseasShipper: '',
          trafMode: '',
          trafName: '',
          billNo: '',
          tradeMode: '',
          licenseNo: ''
        },
        entryManage: entryManage,
        cmbDataSource: {
          emsNoList: [],
          declareCodeList: [],
          overseasShipperList: []
        }
      }
    },
    created: function () {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        me.$set(me.cmbDataSource, 'emsNoList', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        }))
      }).catch(() => {
        me.$set(me.cmbDataSource, 'emsNoList', [])
      })
      // 申报单位
      me.$http.post(csAPI.ieParams.CUT).then(res => {
        me.$set(me.cmbDataSource, 'declareCodeList', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbDataSource, 'declareCodeList', [])
      })
    },
    computed: {
      ieDefaultDates() {
        let dateTo = new Date().toLocaleDateString()
        let fromDate = new Date()
        let year = fromDate.getFullYear()
        let month = fromDate.getMonth()
        let date = fromDate.getDate()
        let dateFrom = `${year}/${month}/${date}`
        return [dateFrom, dateTo]
      }
    },
    methods: {
      handleIEDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "iEDateFrom", values[0])
          this.$set(this.searchParam, "iEDateTo", values[1])
        } else {
          this.$set(this.searchParam, "iEDateFrom", '')
          this.$set(this.searchParam, "iEDateTo", '')
        }
      },
      handleDDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "dDateFrom", values[0])
          this.$set(this.searchParam, "dDateTo", values[1])
        } else {
          this.$set(this.searchParam, "dDateFrom", '')
          this.$set(this.searchParam, "dDateTo", '')
        }
      },
      onIEMarkChange(val) {
        let me = this
        me.$set(me.searchParam, 'overseasShipper', '')
        if (isNullOrEmpty(val)) {
          me.cmbDataSource.overseasShipperList = []
        } else {
          if (val === 'I') {
            me.getOverseasShipperList(csAPI.ieParams.CLI);
          }
          if (val === 'E') {
            me.getOverseasShipperList(csAPI.ieParams.PRD);
          }
        }
      },
      getOverseasShipperList(url) {
        let me = this
        me.$http.post(url).then(res => {
          me.$set(me.cmbDataSource, 'overseasShipperList', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
          me.$set(me.cmbDataSource, 'overseasShipperList', [])
        })
      },
      overseasShipperChange() {
        let me = this,
          theItems = me.cmbDataSource.overseasShipperList.filter(item => {
            return item.value === me.searchParam.overseasShipper
          })
        if (Array.isArray(theItems) && theItems.length > 0) {
          me.$set(me.searchParam, 'overseasShipper', theItems[0].value)
        } else {
          me.$set(me.searchParam, 'overseasShipper', '')
        }
      },
      declareCodeChange() {
        let me = this,
          theItems = me.cmbDataSource.declareCodeList.filter(item => {
            return item.value === me.searchParam.declareCode
          })
        if (Array.isArray(theItems) && theItems.length > 0) {
          me.$set(me.searchParam, 'declareCode', theItems[0].value)
        } else {
          me.$set(me.searchParam, 'declareCode', '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
