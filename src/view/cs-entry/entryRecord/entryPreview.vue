<template>
  <section class="head">
    <XdoCard class="leftCard" bordered>
      <span>报关单预览</span>
      <embed :src="pdfUrl" type="application/pdf" width="100%" :height="autoHeight">
    </XdoCard>
    <XdoCard class="rightCard">
      <XdoCard v-show="showPdf">
        <span>随附单预览</span>
        <embed :src="attachUrl" type="application/pdf" width="100%" height="500px">
      </XdoCard>
      <XdoCard>
        <span>随附单列表</span>
        <div v-if="showList">
          <ul>
            <li v-for="item in this.dataList" :key="item.sid">
              <a :class="{active:!item.filePath}" @click.prevent="getPreviewAttach(item)">{{item.origFileName}}</a>
            </li>
          </ul>
        </div>
        <div v-else>
          <span>该报关单当前无随附单</span>
        </div>
      </XdoCard>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'entryPreview',
    props: {
      searchData: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        pdfUrl: '',
        attachUrl: '',
        dataList: [],
        showPdf: false,
        showList: false,
        autoHeight: document.body.clientHeight - (document.body.clientHeight / 5)
      }
    },
    mounted() {
      let me = this
      me.getList()
      me.getRecordPreview()
    },
    destroyed() {
      let me = this
      window.URL.revokeObjectURL(me.pdfUrl)
      window.URL.revokeObjectURL(me.attachUrl)
    },
    methods: {
      getBlob(base64, contentType, sliceSize) {
        contentType = contentType || ''
        sliceSize = sliceSize || 512

        let byteCharacters = atob(base64)
        let byteArrays = []

        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          let slice = byteCharacters.slice(offset, offset + sliceSize)
          let byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          let byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        return new Blob(byteArrays, {type: contentType})
      },
      getRecordPreview() {
        let me = this
        if (me.searchData.sid) {
          me.$http.get(csAPI.entry.entryRecord.head.getRecordPreview + `/${me.searchData.sid}`).then(res => {
            const blob = me.getBlob(res.data, `application/pdf`)
            me.pdfUrl = window.URL.createObjectURL(blob)
          }).catch(() => {
          })
        }
      },
      getList() {
        let me = this
        if (me.searchData.sid) {
          me.$http.post(csAPI.entry.entryRecord.head.getRecord + `/${me.searchData.sid}`).then(res => {
            if (res.data.data) {
              if (res.data.data.length > 0) {
                me.showList = true
                me.dataList = res.data.data
              } else {
                me.showList = false
              }
            }
          }).catch(() => {
          })
        }
      },
      getPreviewAttach(e) {
        let me = this
        if (!isNullOrEmpty(e['filePath'])) {
          me.$http.get(csAPI.entry.entryRecord.head.getRecordPreviewAttach + `/${e.sid}`).then(res => {
            me.showPdf = true
            const blob = me.getBlob(res.data, `application/pdf`)
            me.attachUrl = window.URL.createObjectURL(blob)
          }).catch(() => {
          })
        }
      }
    }
  }
</script>

<style leng="less" scoped>
   .head {
     display: flex;
     justify-content: center;
   }

   .leftCard {
     flex: 1;
   }

   ul {
     display: flex;
     flex-wrap: wrap;
   }

   ul li {
     margin-right: 10px;
     list-style-type: none;
   }

   .rightCard {
     flex: 1;
   }

   .active {
     color: #bbbbbb;
     cursor: default;
     text-decoration: none;
   }
</style>
