import { getKeyValue } from '@/libs/util'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'iemark'
  , 'tradeCode'
  , 'tradeName'
  , 'declareCode'
  , 'declareName'
  , 'entryNo'
  , 'ieport'
  , 'iEPortName'
  , 'iedate'
  , 'ddate'
  , 'emsNo'
  , 'overseasShipper'
  , 'overseasShipperName'
  , 'trafMode'
  , 'trafModeName'
  , 'trafName'
  , 'billNo'
  , 'warehouse'
  , 'ownerCode'
  , 'ownerName'
  , 'tradeMode'
  , 'tradeModeName'
  , 'cutMode'
  , 'cutModeName'
  , 'licenseNo'
  , 'despPort'
  , 'despPortName'
  , 'contrNo'
  , 'tradeNation'
  , 'tradeNationName'
  , 'tradeCountry'
  , 'tradeCountryName'
  , 'destPort'
  , 'destPortName'
  , 'entryPort'
  , 'entryPortName'
  , 'wrapType'
  , 'wrapTypeName'
  , 'packNum'
  , 'grossWt'
  , 'netWt'
  , 'transMode'
  , 'transModeName'
  , 'feeCurr'
  , 'feeCurrName'
  , 'feeMark'
  , 'feeMarkName'
  , 'feeRate'
  , 'insurCurr'
  , 'insurCurrName'
  , 'insurMark'
  , 'insurMarkName'
  , 'insurRate'
  , 'otherCurr'
  , 'otherCurrName'
  , 'otherMark'
  , 'otherMarkName'
  , 'otherRate'
  , 'feeAll'
  , 'insurAll'
  , 'otherAll'
  , 'promiseItems'
  , 'confirmSpeical'
  , 'confirmPrice'
  , 'confirmRoyalties'
  , 'cusRemark'
  , 'certMark'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'gno'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'qty'
  , 'unitName'
  , 'decPrice'
  , 'decTotal'
  , 'currName'
  , 'originCountryName'
  , 'destinationCountryName'
  , 'districtName'
  , 'dutyModeName'
]

const excelColumnsConfig = [
  'sid'
  , 'iemark'
  , 'tradeCode'
  , 'tradeName'
  , 'declareCode'
  , 'declareName'
  , 'entryNo'
  , 'ieport'
  , 'iEPortName'
  , 'iedate'
  , 'ddate'
  , 'emsNo'
  , 'overseasShipper'
  , 'overseasShipperName'
  , 'trafMode'
  , 'trafModeName'
  , 'trafName'
  , 'billNo'
  , 'warehouse'
  , 'ownerCode'
  , 'ownerName'
  , 'tradeMode'
  , 'tradeModeName'
  , 'cutMode'
  , 'cutModeName'
  , 'licenseNo'
  , 'despPort'
  , 'despPortName'
  , 'contrNo'
  , 'tradeNation'
  , 'tradeNationName'
  , 'tradeCountry'
  , 'tradeCountryName'
  , 'destPort'
  , 'destPortName'
  , 'entryPort'
  , 'entryPortName'
  , 'wrapType'
  , 'wrapTypeName'
  , 'packNum'
  , 'grossWt'
  , 'netWt'
  , 'transMode'
  , 'transModeName'
  , 'feeCurr'
  , 'feeCurrName'
  , 'feeMark'
  , 'feeMarkName'
  , 'feeRate'
  , 'insurCurr'
  , 'insurCurrName'
  , 'insurMark'
  , 'insurMarkName'
  , 'insurRate'
  , 'otherCurr'
  , 'otherCurrName'
  , 'otherMark'
  , 'otherMarkName'
  , 'otherRate'
  , 'feeAll'
  , 'insurAll'
  , 'otherAll'
  , 'promiseItems'
  , 'confirmSpeical'
  , 'confirmPrice'
  , 'confirmRoyalties'
  , 'cusRemark'
  , 'certMark'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'gno'
  , 'codeTS'
  , 'gname'
  , 'gmodel'
  , 'qty'
  , 'unitName'
  , 'decPrice'
  , 'decTotal'
  , 'currName'
  , 'originCountryName'
  , 'destinationCountryName'
  , 'districtName'
  , 'dutyModeName'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          key: 'iemark',
          minWidth: 100,
          align: 'center',
          title: '进出口标志',
          render: (h, params) => {
            return h('span', getKeyValue(this.entryManage.I_E_MARK_MAP, params.row.iemark, false))
          },
        },
        {
          title: '申报单位',
          minWidth: 150,
          align: 'center',
          key: 'declareName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '报关单号',
          minWidth: 130,
          align: 'center',
          key: 'entryNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '进出境关别',
          minWidth: 100,
          align: 'center',
          key: 'iEPortName'
        },
        {
          key: 'iedate',
          minWidth: 100,
          align: 'center',
          title: '进出口日期'
        },
        {
          key: 'ddate',
          minWidth: 100,
          align: 'center',
          title: '申报日期'
        },
        {
          title: '备案号',
          minWidth: 120,
          align: 'center',
          key: 'emsNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '境外发(收)货人',
          minWidth: 150,
          align: 'center',
          key: 'overseasShipperName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '运输方式',
          minWidth: 100,
          align: 'center',
          key: 'trafModeName'
        },
        {
          title: '运输工具名称',
          minWidth: 120,
          align: 'center',
          key: 'trafName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '提运单号',
          minWidth: 120,
          align: 'center',
          key: 'billNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '货物存放地点',
          minWidth: 120,
          align: 'center',
          key: 'warehouse',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '消费(生产单位)',
          minWidth: 150,
          align: 'center',
          key: 'ownerName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '监管方式',
          minWidth: 100,
          align: 'center',
          key: 'tradeModeName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '征免性质',
          minWidth: 100,
          align: 'center',
          key: 'cutModeName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '许可证号',
          minWidth: 120,
          align: 'center',
          key: 'licenseNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '启运港',
          minWidth: 100,
          align: 'center',
          key: 'despPortName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '合同协议号',
          minWidth: 120,
          align: 'center',
          key: 'contrNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '贸易国',
          minWidth: 100,
          align: 'center',
          key: 'tradeNationName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '启(抵)运国',
          minWidth: 100,
          align: 'center',
          key: 'tradeCountryName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '经停(指运)港',
          minWidth: 100,
          align: 'center',
          key: 'destPortName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '入境口岸',
          minWidth: 100,
          align: 'center',
          key: 'entryPortName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '包装种类',
          minWidth: 180,
          align: 'center',
          key: 'wrapTypeName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '件数',
          minWidth: 100,
          align: 'center',
          key: 'packNum',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '毛重',
          minWidth: 100,
          align: 'center',
          key: 'grossWt',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '净重',
          minWidth: 100,
          align: 'center',
          key: 'netWt',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '成交方式',
          minWidth: 100,
          align: 'center',
          key: 'transModeName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '运费',
          minWidth: 120,
          align: 'center',
          key: 'feeAll',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '保费',
          minWidth: 120,
          align: 'center',
          key: 'insurAll',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '杂费',
          minWidth: 120,
          align: 'center',
          key: 'otherAll',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '特殊关系',
          minWidth: 80,
          align: 'center',
          key: 'confirmSpeical'
        },
        {
          title: '价格影响',
          minWidth: 80,
          align: 'center',
          key: 'confirmPrice'
        },
        {
          title: '支付特许权使用费',
          minWidth: 120,
          align: 'center',
          key: 'confirmRoyalties'
        },
        {
          title: '自报自缴',
          minWidth: 80,
          align: 'center',
          key: 'cusRemark'
        },
        {
          title: '随附单证',
          minWidth: 120,
          align: 'center',
          key: 'certMark',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '备注',
          minWidth: 150,
          align: 'center',
          key: 'note',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '备案序号',
          minWidth: 80,
          align: 'center',
          key: 'gno',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '商品编号',
          minWidth: 100,
          align: 'center',
          key: 'codeTS',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '商品名称',
          minWidth: 150,
          align: 'center',
          key: 'gname',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '规格型号',
          minWidth: 150,
          align: 'center',
          key: 'gmodel',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '数量',
          minWidth: 100,
          align: 'center',
          key: 'qty',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '单位',
          minWidth: 100,
          align: 'center',
          key: 'unitName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '单价',
          minWidth: 100,
          align: 'center',
          key: 'decPrice',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '总价',
          minWidth: 100,
          align: 'center',
          key: 'decTotal',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '币制',
          minWidth: 100,
          align: 'center',
          key: 'currName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '原产国（地区）',
          minWidth: 120,
          align: 'center',
          key: 'originCountryName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '最终目的国（地区）',
          minWidth: 120,
          align: 'center',
          key: 'destinationCountryName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '境内目的地/境外货源地',
          minWidth: 150,
          align: 'center',
          key: 'districtName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '征免',
          minWidth: 100,
          align: 'center',
          key: 'dutyModeName',
          ellipsis: true,
          tooltip: true,
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
