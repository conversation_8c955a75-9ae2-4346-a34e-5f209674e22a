import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import FileManagementSetPop from '../components/file-management-set-pop'

export const fileManagementList = {
  name: 'fileManagementList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    FileManagementSetPop
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      editData: [],
      autoCreate: false,
      overdueSetShow: false,
      warringType: 'ARCHIVES',
      toolbarEventMap: {
        'export': this.handleDownload,
        'set-notice': this.handleWarningSet,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        title: '未归档单据',
        key: 'notFiledAttach'
      }]
    },
    getFields() {
      return [{
        width: 180,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 180,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 88,
        title: '制单日期',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 380,
        tooltip: true,
        title: '未归档单据',
        key: 'notFiledAttach'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 设置预警条件
     */
    handleWarningSet() {
      let me = this,
        configs = [],
        archives = [],
        promiseArchives = me.$http.post(me.ajaxUrl.getAttachedType),
        promiseConfig = me.$http.post(me.ajaxUrl.getAll + '/' + me.warringType)
      me.setToolbarLoading('set-notice', true)
      Promise.all([promiseArchives, promiseConfig]).then(values => {
        if (Array.isArray(values) && values.length > 0) {
          archives = values[0].data.data
        }
        if (Array.isArray(values) && values.length > 1) {
          configs = values[1].data.data
        }
        if (Array.isArray(archives) && archives.length > 0) {
          let editData = [],
            filterConfigs = []
          archives.forEach(item => {
            filterConfigs = configs.filter(config => {
              return config.businessId === item.label
            })
            if (Array.isArray(filterConfigs) && filterConfigs.length > 0) {
              editData.push({
                key: item.label,
                title: item.title,
                data: {
                  businessId: item.label,
                  sid: filterConfigs[0].sid,
                  warringType: me.warringType,
                  warringMax: filterConfigs[0].warringMax,
                  warringMin: filterConfigs[0].warringMin
                }
              })
            } else {
              editData.push({
                key: item.label,
                title: item.title,
                data: {
                  sid: '',
                  warringMax: null,
                  warringMin: null,
                  businessId: item.label,
                  warringType: me.warringType
                }
              })
            }
          })
          me.$set(me, 'editData', editData)
          me.$set(me, 'overdueSetShow', true)
        } else {
          me.$Message.warning('当前未配置【提单随附单据类型】')
        }
      }).finally(() => {
        me.setToolbarLoading('set-notice', false)
      })
    },
    afterSetting() {
      let me = this
      me.$http.post(me.ajaxUrl.checkData).then(() => {
      }).catch(() => {
      }).finally(() => {
        me.handleSearchSubmit()
      })
    }
  }
}
