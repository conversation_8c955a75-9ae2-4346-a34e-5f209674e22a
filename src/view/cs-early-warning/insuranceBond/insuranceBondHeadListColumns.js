import { getKeyValue } from '@/libs/util'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'serialNo'
  , 'bailType'
  , 'documentNo'
  , 'dateStart'
  , 'dateEnd'
  , 'note'
  , 'entryNo'
  , 'customer'
  , 'payDept'
  , 'note'
  , 'chargePerson'
  , 'dataStatus'
  , 'dataStatusName'
  , 'tradeCode'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'emsNo'
  , 'totalPrice'
  , 'paymentDate'
  , 'returnDate'
]

const excelColumnsConfig = [
  'sid'
  , 'serialNo'
  , 'bailType'
  , 'documentNo'
  , 'dateStart'
  , 'dateEnd'
  , 'note'
  , 'entryNo'
  , 'customer'
  , 'payDept'
  , 'note'
  , 'chargePerson'
  , 'dataStatus'
  , 'tradeCode'
  , 'insertTime'
  , 'updateUser'
  , 'updateTime'
  , 'emsNo'
  , 'totalPrice'
  , 'paymentDate'
  , 'returnDate'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.handleEditOper()
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleRowDblClick(params.row)
                  }
                }
              }, '查看')
            ]);
          },
          key: 'operation'
        },
        {
          title: '序号',
          minWidth: 50,
          align: 'center',
          key: 'serialNo',
        },
        {
          title: '状态',
          minWidth: 80,
          align: 'center',
          key: 'dataStatusName',
          render: (h, params) => {
            if (params.row.dataStatus === '0') {// 正常
              return h('span', params.row.dataStatusName)
            } else if (params.row.dataStatus === '1') {// 预警
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF7F00'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '2') {// 超期
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF0000'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '3') {// 注销
              return h('div', [
                h('span', {
                  style: {
                    color: '#ADADAD'
                  }
                }, params.row.dataStatusName)
              ])
            } else {
              return h('span', '--')
            }
          }
        },
        {
          title: '类型',
          minWidth: 80,
          align: 'center',
          key: 'bailType',
          render: (h, params) => {
            return h('span', getKeyValue(this.earlyWarningManage.BAIL_TYPE_MAP, params.row.bailType))
          }
        },
        {
          title: '编号',
          minWidth: 100,
          align: 'center',
          key: 'documentNo',
        },
        {
          title: '金额(元/人民币)',
          minWidth: 120,
          align: 'center',
          key: 'totalPrice'
        },
        {
          title: '有效期起',
          minWidth: 120,
          align: 'center',
          key: 'dateStart',
          render: (h, params) => {
            return h('span', params.row.dateStart ? params.row.dateStart.slice(0, 10) : params.row.dateStart)
          }
        },
        {
          title: '有效期止',
          minWidth: 120,
          align: 'center',
          key: 'dateEnd',
          render: (h, params) => {
            return h('span', params.row.dateEnd ? params.row.dateEnd.slice(0, 10) : params.row.dateEnd)
          }
        },
        {
          title: '担保事项',
          minWidth: 100,
          align: 'center',
          key: 'note'
        },
        {
          title: '手册号码',
          minWidth: 100,
          align: 'center',
          key: 'emsNo'
        },
        {
          title: '报关单号码',
          minWidth: 100,
          align: 'center',
          key: 'entryNo'
        },
        {
          title: '客户',
          minWidth: 100,
          align: 'center',
          key: 'customer'
        },
        {
          title: '缴纳机关',
          minWidth: 100,
          align: 'center',
          key: 'payDept'
        },
        {
          title: '责任人',
          minWidth: 100,
          align: 'center',
          key: 'chargePerson',
        },
        {
          title: '制单日期',
          minWidth: 120,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
          }
        },
        {
          title: '缴纳日期',
          minWidth: 120,
          align: 'center',
          key: 'paymentDate',
          render: (h, params) => {
            return h('span', params.row.paymentDate ? params.row.paymentDate.slice(0, 10) : params.row.paymentDate)
          }
        },
        {
          title: '返回日期',
          minWidth: 120,
          align: 'center',
          key: 'returnDate',
          render: (h, params) => {
            return h('span', params.row.returnDate ? params.row.returnDate.slice(0, 10) : params.row.returnDate)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
