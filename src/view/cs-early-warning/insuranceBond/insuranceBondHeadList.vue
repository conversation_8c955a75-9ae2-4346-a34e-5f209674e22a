<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <insuranceBondHeadSearch ref="headSearch"></insuranceBondHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" style="display: flex; align-items: center; justify-content: space-between;" ref="area_actions">
          <xdo-toolbar :card="false" @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
          <span style="font-weight: bold;">当前预警天数: {{ warringDays }} 天</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :loading="tableloading" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                 :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <insuranceBondTabs v-if="!showHead" ref="insuranceBondTabs" @onEditBack="editBack" :editConfig="editConfig"></insuranceBondTabs>
    <warringSet v-if="warringSetShow" @onColse="warringSetClose" :warringType="this.warringType"></warringSet>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI, excelExport } from '@/api'
  import warringSet from '../base/warringSet'
  import insuranceBondTabs from './insuranceBondTabs'
  import insuranceBondHeadSearch from './insuranceBondHeadSearch'
  import { commonWarringSetMethod } from  '../base/commonWarringSetMethod'
  import { editStatus, pageParam, earlyWarningManage } from '../../cs-common'
  import { dynamicHeight, getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './insuranceBondHeadListColumns'

  export default {
    name: 'insuranceBondHeadList',
    components: {
      warringSet,
      insuranceBondTabs,
      insuranceBondHeadSearch
    },
    mixins: [dynamicHeight, columns, commonWarringSetMethod, pms],
    data() {
      return {
        actions: [],
        showHead: true,
        tableloading: false,
        warringType: 'BAIL',
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'logout': this.handleLogout,
          'delete': this.handleDelete,
          'set-notice': this.handleSetWarring
        },
        editConfig: {
          headId: '',
          editData: {},
          editStatus: editStatus.SHOW
        },
        showSearch: false,
        gridConfig: {
          data: [],
          selectData: [],
          selectRows: [],
          gridColumns: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1,
          pageSizeOpts: [10, 20, 50, 100]
        },
        earlyWarningManage: earlyWarningManage
      }
    },
    mounted: function () {
      let me = this
      me.handleSearchSubmit()
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.loadFunctions('default', 120).then()
    },
    methods: {
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(120, !me.showSearch ? ["area_search"] : null)
      },
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      getList() {
        let me = this
        me.tableloading = true
        pageParam.page = me.pageParam.page
        pageParam.limit = me.pageParam.limit
        const data = me.$refs.headSearch.searchParam
        me.$http.post(csAPI.earlyWarning.manager.bail.selectAllPaged, data, {params: pageParam}).then(res => {
          me.tableloading = false
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
        })
      },
      handleAdd() {
        let me = this
        me.editConfig.editData = {}
        me.gridConfig.selectRows = []
        me.editConfig.editStatus = editStatus.ADD
        me.showHead = false
      },
      handleEdit() {
        let me = this
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要编辑的保金保函信息!')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('一次只能编辑一条保金保函信息!')
        } else {
          me.editConfig.editStatus = editStatus.EDIT
          me.editConfig.editData = me.gridConfig.selectRows[0]
          me.editConfig.headId = me.gridConfig.selectRows[0].sid
          me.showHead = false
        }
      },
      handleEditOper() {
        let me = this
        me.editConfig.editStatus = editStatus.EDIT
        me.editConfig.editData = me.gridConfig.selectData
        me.editConfig.headId = me.gridConfig.selectData.sid
        me.showHead = false
      },
      handleLogout() {
        let me = this
        if (me.gridConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '注销',
            cancelText: '取消',
            content: '确认要注销所选项吗',
            onOk: () => {
              const data = me.gridConfig.selectRows.map(item => {
                return {
                  sid: item.sid
                }
              })
              me.$http.post(csAPI.earlyWarning.manager.bail.logout, data).then(() => {
                me.$Message.success('注销成功!')
              }).catch(() => {
              }).finally(() => {
                me.handleSearchSubmit()
              })
            }
          })
        } else {
          me.$Message.warning('请选择要注销的数据!')
        }
      },
      handleDelete() {
        let me = this
        if (me.gridConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              me.setToolbarLoading('delete', true)
              const sids = me.gridConfig.selectRows.map(item => {
                return item.sid
              })
              me.$http.delete(`${csAPI.earlyWarning.manager.bail.delete}/${sids}`).then(() => {
                me.setToolbarLoading('delete')
                me.$Message.success('删除成功!')
                me.gridConfig.selectRows = []
                me.getList()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('请选择要删除的数据!')
        }
      },
      handleDownload() {
        let me = this
        me.setToolbarLoading('export', true)
        excelExport(icsCsRebirthUri.earlyWarning.idCard.head.exportUrl, {
          name: 'idCard-表头',
          header: getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig),
          exportColumns: Object.assign({}, me.$refs.headSearch.searchParam)
        }).finally(() => {
          me.setToolbarLoading('export')
        })
      },
      handleSelectionChange(selectRows) {
        let me = this
        me.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        if (me.pageParam.page === 1) {
          me.getList()
        }
      },
      handleRowDblClick(item) {
        let me = this
        me.editConfig.editData = item
        me.editConfig.headId = item.sid
        me.editConfig.editStatus = editStatus.SHOW
        me.showHead = false
      },
      editBack(val) {
        if (val) {
          let me = this
          me.showHead = true
          me.gridConfig.selectRows = []
          me.getList()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
