<template>
  <section>
    <XdoCard :bordered="false">
      <div class="xdo-enter-root" v-focus>
        <XdoForm ref="headerEditFrom" class="dc-form dc-form-3 xdo-enter-form" :model="headerData" :rules="rulesHeader" label-position="right" :label-width="120">
          <XdoFormItem prop="documentNo" label="修理物品批次">
            <XdoIInput type="text" v-model="headerData.documentNo" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="documentName" label="修理物品名称">
            <XdoIInput type="text" v-model="headerData.documentName" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="dateEnd" label="修理物品复出/复进日期">
            <XdoDatePicker type="date" :value="headerData.dateEnd" style="width: 100%;" transfer :disabled="isLogout" @on-change="headerData.dateEnd=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="insertUser" label="制单员">
            <XdoIInput type="text" v-model="headerData.insertUser" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="insertTime" label="制单日期" class="dc-merge-2-4">
            <XdoDatePicker type="date" disabled v-model="headerData.insertTime" style="width: 100%;"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-1-4">
            <XdoIInput type="textarea" v-model="headerData.note" :autosize="{minRows: 2,maxRows: 5}" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
        </XdoForm>
      </div>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '../../cs-common'

  export default {
    name: 'repairItemsHeadEdit',
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        isLogout: false,
        editDisable: false,
        showDisable: false,
        headerData: {
          sid: '',
          serialNo: 0,
          dateEnd: '',
          documentNo: '',
          documentName: '',
          insertUser: '',
          insertTime: '',
          note: ''
        },
        rulesHeader: {
          dateEnd: [{required: true, message: '不能为空', trigger: 'blur'}],
          documentNo: [{required: true, message: '不能为空', trigger: 'blur'}],
          documentName: [{required: true, message: '不能为空', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
          {...btnComm, label: '保存继续', icon: 'dc-btn-save-1', click: this.handleSaveAndContinue},
          {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack}
        ]
      }
    },
    mounted: function () {
      let me = this
      if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
        me.resetFormData()
        me.setDefaultSerialNo()
        me.$set(me, 'isLogout', false)
        me.editDisable = false
        me.showDisable = false
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT) {
        me.headerData = {...me.editConfig.editData}
        me.$set(me, 'isLogout', false)
        me.buttons[0].needed = true
        me.buttons[1].needed = true
        me.editDisable = true
        me.showDisable = false
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.SHOW) {
        me.headerData = {...me.editConfig.editData}
        me.$set(me, 'isLogout', true)
        me.editDisable = true
        me.showDisable = true
        me.buttons[0].needed = false
        me.buttons[1].needed = false
      } else {
        console.error('缺失编辑信息!')
      }
    },
    methods: {
      /**
       * 数据保存
       * @param goon 是否继续
       */
      saveData(goon) {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.headerData)
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.$http.post(csAPI.earlyWarning.manager.repair.insert, data).then(() => {
                me.$Message.success('新增成功!')
                if (goon === true) {
                  me.resetFormData()
                  me.setDefaultSerialNo()
                  me.editConfig.editStatus = editStatus.ADD
                } else {
                  me.handleBack()
                }
              }).catch(() => {
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              const sid = me.headerData.sid
              me.$http.put(`${csAPI.earlyWarning.manager.repair.update}/${sid}`, data).then(() => {
                me.$Message.success('修改成功!')
                if (goon === true) {
                  me.resetFormData()
                  me.setDefaultSerialNo()
                  me.editConfig.editStatus = editStatus.ADD
                } else {
                  me.handleBack()
                }
              }).catch(() => {
              })
            }
          }
        })
      },
      /**
       * 保存表头数据
       */
      handleSave() {
        let me = this
        me.saveData(false)
      },
      /**
       * 保存并继续
       */
      handleSaveAndContinue() {
        let me = this
        me.saveData(true)
      },
      resetFormData() {
        let me = this
        if (me.$refs['headerEditFrom'] !== undefined) {
          me.$refs['headerEditFrom'].resetFields()
        }
        me.headerData.sid = ''
      },
      setDefaultSerialNo() {
        let me = this
        me.$http.post(csAPI.earlyWarning.manager.repair.getMaxNo).then(res => {
          me.$set(me.headerData, 'serialNo', res.data.data)
        }).catch(() => {
        })
      },
      handleBack() {
        let me = this
        me.$emit('onEditBack', true)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
