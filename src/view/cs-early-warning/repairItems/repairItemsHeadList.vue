<template>
  <section>
    <div v-show="showHead" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <repairItemsHeadSearch ref="headSearch"></repairItemsHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" style="display: flex; align-items: center; justify-content: space-between;" ref="area_actions">
          <xdo-toolbar :card="false" @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
          <span style="font-weight: bold;">当前预警天数: {{ warringDays }} 天</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :loading="tableloading" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageParam.pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <repairItemsTabs v-if="!showHead" ref="repairItemsTabs" @onEditBack="editBack" :editConfig="editConfig"></repairItemsTabs>
    <warringSet v-if="warringSetShow" @onColse="warringSetClose" :warringType="this.warringType"></warringSet>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { dynamicHeight } from '@/common'
  import { baseList } from '../js/baseList'
  import { csAPI, excelExport } from '@/api'
  import warringSet from '../base/warringSet'
  import repairItemsTabs from './repairItemsTabs'
  import { editStatus, pageParam } from '../../cs-common'
  import repairItemsHeadSearch from './repairItemsHeadSearch'
  import { columns, excelColumns } from './repairItemsHeadListColumns'
  import { commonWarringSetMethod } from  '../base/commonWarringSetMethod'

  export default {
    name: 'repairItemsHeadList',
    components: {
      warringSet,
      repairItemsTabs,
      repairItemsHeadSearch
    },
    mixins: [dynamicHeight, columns, excelColumns, commonWarringSetMethod, pms, baseList],
    data() {
      return {
        actions: [],
        showHead: true,
        tableloading: false,
        warringType: 'REPAIR',
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'export': this.handleDownload,
          'set-notice': this.handleSetWarring
        },
        editConfig: {
          headId: '',
          editData: {},
          editStatus: editStatus.SHOW
        },
        showSearch: false,
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          gridColumns: []
        },
        pageParam: {
          page: 1,
          limit: 20,
          dataTotal: -1,
          pageSizeOpts: [10, 20, 50, 100]
        },
        ajaxUrl: {
          logout: csAPI.earlyWarning.manager.repair.logout
        }
      }
    },
    mounted: function () {
      let me = this
      me.handleSearchSubmit()
      me.gridConfig.gridColumns = me.totalColumns
      me.loadFunctions('default', 120).then()
    },
    methods: {
      handleShowSearch() {
        let me = this
        me.showSearch = !me.showSearch
        me.refreshDynamicHeight(120, !me.showSearch ? ["area_search"] : null)
      },
      handleSearchSubmit() {
        let me = this
        me.pageParam.page = 1
        me.getList()
      },
      getList() {
        let me = this
        me.tableloading = true
        pageParam.page = me.pageParam.page
        pageParam.limit = me.pageParam.limit
        const data = me.$refs.headSearch.searchParam
        me.$http.post(csAPI.earlyWarning.manager.repair.selectAllPaged, data, {params: pageParam}).then(res => {
          me.tableloading = false
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
        }, () => {
        })
      },
      handleAdd() {
        let me = this
        me.gridConfig.selectRows = []
        me.editConfig.editStatus = editStatus.ADD
        me.editConfig.editData = {}
        me.showHead = false
      },
      handleEdit() {
        let me = this
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要编辑的预警信息!')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('一次只能编辑一条预警信息!')
        } else {
          me.editConfig.editStatus = editStatus.EDIT
          me.editConfig.headId = me.gridConfig.selectRows[0].sid
          me.editConfig.editData = me.gridConfig.selectRows[0]
          me.showHead = false
        }
      },
      handleEditOper() {
        let me = this
        me.editConfig.editStatus = editStatus.EDIT
        me.editConfig.headId = me.gridConfig.selectData.sid
        me.editConfig.editData = me.gridConfig.selectData
        me.showHead = false
      },
      handleDelete() {
        let me = this
        if (me.gridConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              me.setToolbarLoading('delete', true)
              const sids = me.gridConfig.selectRows.map(item => {
                return item.sid
              })
              me.$http.delete(`${csAPI.earlyWarning.manager.repair.delete}/${sids}`).then(() => {
                me.setToolbarLoading('delete')
                me.$Message.success('删除成功!')
                me.gridConfig.selectRows = []
                me.getList()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('请选择要删除的数据!')
        }
      },
      handleDownload() {
        let me = this
        me.setToolbarLoading('export', true)
        const params = {
          name: '修理物品预警',
          header: me.totalExcelColumns,
          exportColumns: Object.assign({}, me.$refs.headSearch.searchParam)
        }
        excelExport(csAPI.earlyWarning.manager.repair.export, params).finally(() => {
          me.setToolbarLoading('export')
        })
      },
      handleSelectionChange(selectRows) {
        let me = this
        me.gridConfig.selectRows = selectRows
      },
      pageChange(page) {
        let me = this
        me.pageParam.page = page
        me.getList()
      },
      pageSizeChange(pageSize) {
        let me = this
        me.pageParam.limit = pageSize
        if (me.pageParam.page === 1) {
          me.getList()
        }
      },
      handleRowDblClick(item) {
        let me = this
        me.editConfig.editStatus = editStatus.SHOW
        me.editConfig.editData = item
        me.editConfig.headId = item.sid
        me.showHead = false
      },
      editBack(val) {
        if (val) {
          let me = this
          me.showHead = true
          me.gridConfig.selectRows = []
          me.getList()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
