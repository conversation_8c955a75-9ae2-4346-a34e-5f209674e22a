<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="baseTab" label="基础配置项">
        <BusinessSettingBase></BusinessSettingBase>
      </TabPane>
      <TabPane name="InstructorTab" label="首页操作指导配置项">
        <InstructorSetting></InstructorSetting>
      </TabPane>
      <TabPane name="notifySettingsTabs" label="业务通知设置">
        <notifyItemSettingsList></notifyItemSettingsList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import InstructorSetting from './businessSetting4OM/instructor-setting'
  import BusinessSettingBase from './businessSetting4OM/businessSettingBase'
  import notifyItemSettingsList from './notifi-item-settings/notify-item-settings-list'

  export default {
    name: 'businessSetting4OM',
    components: {
      InstructorSetting,
      BusinessSettingBase,
      notifyItemSettingsList
    },
    data() {
      return {
        tabName: 'baseTab',
        tabs: {
          baseTab: true,
          InstructorTab: false,
          notifySettingsTabs: false
        }
      }
    },
    watch: {
      tabName(value) {
        let me = this
        me.tabs[value] = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
