import { editStatus } from '@/view/cs-common'
import alertNotifySettingsEdit from '../alert-notify-settings-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const alertNotifySettingsList = {
  name: 'alertNotifySettingsList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    alertNotifySettingsEdit
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      hasChildTabs: true,
      pmsLevel: 'alertNotify',
      toolbarEventMap: {
        'edit': this.handleEdit,
        'delete': this.handleDelete
      },
      cmbSource: {
        recipient: [],
        carbonCopy: [],
        noticeType: [{
          value: '0', label: '关闭'
        }, {
          value: '1', label: '实时'
        }],
        bizCode: [{
          value: 'DEC_I_SENDED', label: '进口预报单已分单'
        }, {
          value: 'DEC_I_RECEIVED', label: '进口非保税预录入单已接受委托'
        }, {
          value: 'ENTRY_I_SENDED', label: '进口报关单草单已发送'
        }, {
          value: 'ENTRY_E_SENDED', label: '出口报关单草单已发送'
        }, {
          value: 'ENTRY_I_RECEIVED', label: '接收到进口申报前草单'
        }, {
          value: 'ENTRY_E_RECEIVED', label: '接收到出口申报前草单'
        }, {
          value: 'ENTRY_I_NO_PASS', label: '进口报关单复核退回'
        }, {
          value: 'ENTRY_I_PASS', label: '进口报关单复核通过'
        }, {
          value: 'ENTRY_E_NO_PASS', label: '出口报关单复核退回'
        }, {
          value: 'ENTRY_E_PASS', label: '出口报关单复核通过'
        }, {
          value: 'DEC_I_AUDIT', label: '进口保税预录入单审核'
        }, {
          value: 'DEC_E_AUDIT', label: '出口保税预录入单审核'
        }, {
          value: 'DEC_NO_I_AUDIT', label: '进口非保税预录入单审核'
        }, {
          value: 'DEC_NO_E_AUDIT', label: '出口非保税预录入单审核'
        }, {
          value: 'MAT_I_AUDIT', label: '企业料件发送内审'
        }, {
          value: 'MAT_E_AUDIT', label: '企业成品发送内审'
        }, {
          value: 'ORG_I_AUDIT', label: '备案料件发送内审'
        }, {
          value: 'ORG_E_AUDIT', label: '备案成品发送内审'
        }, {
          value: 'MAT_NO_AUDIT', label: '非保税物料发送内审'
        }]
      }
    }
  },
  created: function () {
    let me = this
    me.cmbSourceLoad()
  },
  methods: {
    /**
     * 数据源加载
     */
    cmbSourceLoad() {
      let me = this
      me.$http.post(me.ajaxUrl.getNotifier, {}).then(res => {
        me.$set(me.cmbSource, 'recipient', res.data.data.map(item => {
          return {
            label: item['userName'],
            value: item['emailAddr']
          }
        }))
        me.$set(me.cmbSource, 'carbonCopy', res.data.data.map(item => {
          return {
            label: item['userName'],
            value: item['emailAddr']
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'recipient', [])
        me.$set(me.cmbSource, 'carbonCopy', [])
      }).finally(() => {
        me.searchFieldsReLoad('recipient')
        me.searchFieldsReLoad('carbonCopy')
      })
    },
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        type: 'select',
        key: 'bizCode',
        title: '业务名称'
      }, {
        type: 'select',
        title: '收件人',
        key: 'recipient'
      }, {
        type: 'select',
        title: '抄送人',
        key: 'carbonCopy'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 展示列字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{flex: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 288,
        key: 'bizCode',
        title: '业务名称',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bizCode)
        })
      }, {
        width: 220,
        title: '收件人',
        key: 'recipient',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.recipient)
        }, true)
      }, {
        width: 220,
        title: '抄送人',
        key: 'carbonCopy',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.carbonCopy)
        }, true)
      }, {
        flex: 1,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 点击编辑按钮执行
     */
    handleEdit() {
      let me = this
      if (me.checkRowSelected('编辑')) {
        if (me.listConfig.selectRows.length === 1) {
          me.handleEditByRow(me.listConfig.selectRows[0])
        } else {
          let selSids = me.getSelectedParams()
          me.$http.post(me.ajaxUrl.checkForMultipleEdit + '/' + selSids).then(() => {
            me.$set(me.editConfig, 'editData', {})
            me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
            me.$set(me, 'showList', false)
          }).catch(() => {
          })
        }
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
