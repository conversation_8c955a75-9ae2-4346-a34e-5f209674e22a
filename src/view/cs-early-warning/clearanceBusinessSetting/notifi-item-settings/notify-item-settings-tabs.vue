<template>
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="notifierTab" label="通知人设置">
        <notifierSettingsList></notifierSettingsList>
      </TabPane>
      <TabPane name="alertNotifyTab" label="业务通知设置">
        <alertNotifySettingsList ref="alertNotify"></alertNotifySettingsList>
      </TabPane>
    </XdoTabs>
  </section>
</template>

<script>
  import notifierSettingsList from './notifier-settings-list'
  import alertNotifySettingsList from './alert-notify-settings-list'

  export default {
    name: 'notifyItemSettingsTabs',
    components: {
      notifierSettingsList,
      alertNotifySettingsList
    },
    data() {
      return {
        tabName: 'notifierTab',
        tabs: {
          notifierTab: true,
          alertNotifyTab: false
        }
      }
    },
    watch: {
      tabName: {
        immediate: true,
        handler: function (tabName) {
          let me = this
          me.tabs[tabName] = true
          if (tabName === 'alertNotifyTab') {
            if (me.$refs['alertNotify']) {
              if (typeof me.$refs['alertNotify'].cmbSourceLoad === 'function') {
                me.$refs['alertNotify'].cmbSourceLoad()
              }
            }
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
