<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="80">
      <XdoCard :bordered="false" title="报关默认值设置" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2" style="padding-right: 12px;">
          <XdoFormItem prop="emsNo" label="备案通关默认手账册号" :label-width="150">
            <xdo-select v-model="frmData.emsNo" :options="this.cmbSource.emsNoList" :disabled="noEmit"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeMode" label="监管方式">
            <xdo-select v-model="frmData.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="countryMode" label="出口管理 表头最终目的国信息自动带到表体" :label-width="270">
            <i-switch v-model="frmData.countryMode" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="表头必填项设置" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2">
          <XdoFormItem class="dc-merge-1-3" prop="requiredField">
            <CheckboxGroup v-model="fieldList">
              <template v-for="field in fieldSource">
                <XdoCheckbox :label="field.label" :key="field.label" :disabled="noEmit">
                  <span>{{field.title}}</span>
                </XdoCheckbox>
              </template>
            </CheckboxGroup>
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="表体必填项设置" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2">
          <XdoFormItem class="dc-merge-1-3" prop="listRequiredField">
            <CheckboxGroup v-model="bodyFieldList">
              <template v-for="field in bodyFieldSource">
                <XdoCheckbox :label="field.label" :key="field.label" :disabled="noEmit">
                  <span>{{field.title}}</span>
                </XdoCheckbox>
              </template>
            </CheckboxGroup>
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="表体数据小数位数设置" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2">
          <XdoFormItem label="申报数量">
            <RadioGroup v-model="frmData.qtyDigit">
              <Radio :label="item" v-for="item in precisions" :key="item" :disabled="noEmit">{{item}}</Radio>
            </RadioGroup>
          </XdoFormItem>
          <XdoFormItem label="法一数量">
            <RadioGroup v-model="frmData.qty1Digit">
              <Radio :label="item" v-for="item in precisions" :key="item" :disabled="noEmit">{{item}}</Radio>
            </RadioGroup>
          </XdoFormItem>
          <XdoFormItem label="法二数量">
            <RadioGroup v-model="frmData.qty2Digit">
              <Radio :label="item" v-for="item in precisions" :key="item" :disabled="noEmit">{{item}}</Radio>
            </RadioGroup>
          </XdoFormItem>
          <XdoFormItem label="净重">
            <RadioGroup v-model="frmData.netWtDigit">
              <Radio :label="item" v-for="item in precisionsNetWt" :key="item" :disabled="noEmit">{{item}}</Radio>
            </RadioGroup>
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="校验项" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2" style="padding-left: 70px;">
          <XdoFormItem label="同物料保税与非保税不得同时存在" :label-width="250">
            <i-switch v-model="frmData.uniqueness" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="备案料号英文是否支持大小写" :label-width="250">
            <i-switch v-model="frmData.caseWrite" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="提单发送内审时校验随附单据是否存在" :label-width="250">
            <i-switch v-model="frmData.checkAttach" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="提单提取数据保完税标识校验" :label-width="250">
            <i-switch v-model="frmData.checkBondMark" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="提单表头总价、总量校验" :label-width="250">
            <i-switch v-model="frmData.checksum" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="提单表头净毛重自动汇总表体" :label-width="250">
            <i-switch v-model="frmData.autoNetWt" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
            <xdo-select v-model="frmData.autoNetWtDigit" :options="this.optionList"
                        :disabled="isShowAutoNetWtDigit" style="width: 95%; padding-left: 15px;"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="提单表头与表体净重是否一致性校验" :label-width="250">
            <div :class="{'showRightInput':showTotalNumber,'':!showTotalNumber}">
              <xdo-select v-model="frmData.checkNetWtTotal" :options="this.earlyWarningManage.CHECK_NET_WT"
                          :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
              <DcNumberInput v-if="showTotalNumber" v-model="frmData.netWtAllowErrVal" style="width: 80px;"
                             integerDigits="2" precision="5" :disabled="noEmit"></DcNumberInput>
            </div>
          </XdoFormItem>
          <XdoFormItem label="提单表头与表体毛重是否一致性校验" :label-width="250">
            <div :class="{'showRightInput':showGrossWtLimit,'':!showGrossWtLimit}">
              <xdo-select v-model="frmData.checkGrossWt" :options="this.earlyWarningManage.CHECK_NET_WT"
                          :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
              <DcNumberInput v-if="showGrossWtLimit" v-model="frmData.grossWtLimit" style="width: 80px;"
                             integerDigits="2" precision="5" :disabled="noEmit"></DcNumberInput>
            </div>
          </XdoFormItem>
          <XdoFormItem label="提单表头与表体监管方式不一致校验" :label-width="250">
            <xdo-select v-model="frmData.checkTradeMode" :options="this.earlyWarningManage.CHECK_TRADE_MODE"
                        :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
          </XdoFormItem>
          <XdoFormItem>
          </XdoFormItem>
          <XdoFormItem label="提单表体币制与备案币制不一致校验" :label-width="250">
            <xdo-select v-model="frmData.checkCurr" :options="this.earlyWarningManage.CHECK_CURR"
                        :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="提单表体单价*数量是否等于总价提示" :label-width="250">
            <i-switch v-model="frmData.checkTotalPrice" :true-value="checkVals.trueVal" :false-value="checkVals.falseVal" :disabled="noEmit" />
          </XdoFormItem>
          <XdoFormItem label="提单表体净重与物料净重不一致校验" :label-width="250">
            <div :class="{'showRightInput':showCheckNetWtNumber,'':!showCheckNetWtNumber}">
              <xdo-select v-model="frmData.checkNetWt" :options="this.earlyWarningManage.CHECK_NET_WT"
                          :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
              <dc-numberInput v-if="showCheckNetWtNumber" v-model="frmData.netWtLimit"
                              integerDigits="2" precision="2" append="%"
                              :disabled="noEmit" style="width: 80px;">
              </dc-numberInput>
            </div>
          </XdoFormItem>
          <XdoFormItem label="提单表体单价与物料单价不一致校验" :label-width="250">
            <div :class="{'showRightInput':showPriceLimiteNumber,'':!showPriceLimiteNumber}">
              <xdo-select v-model="frmData.checkPrice" :options="this.earlyWarningManage.CHECK_NET_WT"
                          :optionLabelRender="pcodeRender" :disabled="noEmit"></xdo-select>
              <dc-numberInput v-if="showPriceLimiteNumber" v-model="frmData.priceLimite"
                              integerDigits="2" precision="2" append="%"
                              :disabled="noEmit" style="width: 80px;">
              </dc-numberInput>
            </div>
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="提单随附单据类型设置" class="dc-merge-1-4 ieLogisticsTrackingCard">
        <div class="dc-form dc-form-2">
          <XdoFormItem class="dc-merge-1-3" prop="attachType">
            <CheckboxGroup v-model="attachTypeArr">
              <template v-for="field in attachTypeSource">
                <XdoCheckbox :label="field.label" :key="field.label" :disabled="noEmit">
                  <span>{{field.title}}</span>
                </XdoCheckbox>
              </template>
            </CheckboxGroup>
          </XdoFormItem>
        </div>
      </XdoCard>

      <XdoCard :bordered="false" title="繁转简设置" class="dc-merge-1-4 ieLogisticsTrackingCard" v-if="isShowTranslate">
        <div class="dc-form dc-form-2" >
          <XdoFormItem class="dc-merge-1-3" prop="configList">
            <CheckboxGroup v-model="translateArr">
              <template v-for="item in translateTypeSource">
                <XdoCheckbox :label="item.billType" :key="item.billType"  :disabled="noEmit">
                  <span>{{item.billName}}</span>
                </XdoCheckbox>
              </template>
            </CheckboxGroup>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { editStatus } from '@/view/cs-common'
  import { earlyWarningManage } from '@/view/cs-common'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import DcNumberInput from '@/components/dc-number-input/dc-number-input'

  let { createNamespacedHelpers } = majesty.Vuex
  let { mapMutations } = createNamespacedHelpers(namespace)

  export default {
    name: 'clearanceBusinessSettingOld',
    mixins: [commEdit],
    components: {
      DcNumberInput
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        noEmit: false,
        fieldList: [],
        fieldSource: [{
          label: 'packNum', title: '件数'
        }, {
          label: 'netWt', title: '总净重'
        }, {
          label: 'feeMark', title: '运费'
        }, {
          label: 'grossWt', title: '总毛重'
        }, {
          label: 'iEDate', title: '进口日期'
        }, {
          label: 'insurMark', title: '保费'
        }, {
          label: 'invoiceNo', title: '发票号'
        }, {
          label: 'wrapType', title: '包装种类'
        }, {
          label: 'transMode', title: '成交方式'
        }, {
          label: 'contrNo', title: '合同协议号'
        }, {
          label: 'tradeTerms', title: '贸易条款'
        }, {
          label: 'forwardCode', title: '货运代理'
        }, {
          label: 'trafName', title: '运输工具及航次'
        }, {
          label: 'tradeNation', title: '贸易国(地区)'
        }, {
          label: 'masterCustoms', title: '申报地海关(非保税申报)'
        }],
        checkVals: {
          trueVal: '1',
          falseVal: '0'
        },
        rulesHeader: {},
        cmbSource: {
          emsNoList: []
        },
        //繁转简
        translateArr: [],
        bodyFieldList: [],
        // 随附单据
        attachTypeArr: [],
        successMsg: {
          insert: '保存成功',
          update: '编辑成功'
        },
        attachTypeSource: [],
        formName: 'dataForm',
        warningStatus: false,
        isShowTranslate: false,
        translateTypeSource: [],
        isShowAutoNetWtDigit: true,
        precisions: [0, 1, 2, 3, 4, 5],
        bodyFieldSource: [{
          label: 'qty2', title: '法二数量'
        }],
        earlyWarningManage: earlyWarningManage,
        optionList: ['0', '1', '2', '3', '4', '5'],
        precisionsNetWt: [0, 1, 2, 3, 4, 5, 6, 7, 8],
        ajaxUrl: {
          insert: csAPI.earlyWarning.manager.clearanceBusiness.insert,
          update: csAPI.earlyWarning.manager.clearanceBusiness.update,
          getEmsNoSelect: csAPI.csProductClassify.bonded.getEmsNoSelect,
          loadAttachTypeUrl: csAPI.enterpriseParamsLib.comm.selectAllPaged,
          translate: csAPI.earlyWarning.manager.clearanceBusiness.translate,
          loadConfigUrl: csAPI.earlyWarning.manager.clearanceBusiness.loadConfigUrl
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '编辑', type: 'warning', command: 'edit', click: this.handleEmit}
        ]
      }
    },
    watch: {
      /**
       * 必填项(表头)
       */
      'frmData.requiredField': {
        immediate: true,
        handler: function (fields) {
          let me = this,
            fieldsList = []
          if (!isNullOrEmpty(fields)) {
            let tmpList = fields.trim().split(',')
            for (let field of tmpList) {
              if (!isNullOrEmpty(field) && fieldsList.indexOf(field.trim()) === -1) {
                fieldsList.push(field.trim())
              }
            }
          }
          me.$set(me, 'fieldList', fieldsList)
        }
      },
      /**
       * 必填项(表体)
       */
      'frmData.listRequiredField': {
        immediate: true,
        handler: function (fields) {
          let fieldsList = []
          if (!isNullOrEmpty(fields)) {
            let tmpList = fields.trim().split(',')
            for (let field of tmpList) {
              if (!isNullOrEmpty(field) && fieldsList.indexOf(field.trim()) === -1) {
                fieldsList.push(field.trim())
              }
            }
          }
          let me = this
          me.$set(me, 'bodyFieldList', fieldsList)
        }
      },
      'frmData.autoNetWtDigit': {
        immediate: true,
        handler: function (val) {
          if (val > '5') {
            let me = this
            me.$set(me.frmData, 'autoNetWtDigit', '')
          }
        }
      },
      'frmData.autoNetWt': {
        immediate: true,
        handler: function (val) {
          let me = this
          if (val === '1') {
            me.$set(me, 'isShowAutoNetWtDigit', false)
          } else {
            me.$set(me, 'isShowAutoNetWtDigit', true)
          }
        }
      },
      noEmit(val) {
        let me = this
        if (val && me.frmData.autoNetWt === '1') {
          me.isShowAutoNetWtDigit = true
        } else if (!val && me.frmData.autoNetWt === '1') {
          me.isShowAutoNetWtDigit = false
        }
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(me.ajaxUrl.getEmsNoSelect).then(res => {
        me.cmbSource.emsNoList = res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        })
      }).catch(() => {
        me.cmbSource.emsNoList = []
      })
      me.loadAttachTypes()
    },
    computed: {
      showCheckNetWtNumber() {
        return this.frmData.checkNetWt === '1' || this.frmData.checkNetWt === '2'
      },
      showPriceLimiteNumber() {
        return this.frmData.checkPrice === '1' || this.frmData.checkPrice === '2'
      },
      showTotalNumber() {
        return this.frmData.checkNetWtTotal === '1' || this.frmData.checkNetWtTotal === '2'
      },
      showGrossWtLimit() {
        return this.frmData.checkGrossWt === '1' || this.frmData.checkGrossWt === '2'
      }
    },
    methods: {
      ...mapMutations([
        'setClearanceBusinessSetting'
      ]),
      /**
       * 获取初始值
       */
      getDefaultData() {
        return {
          sid: '',
          emsNo: '',
          tradeMode: '',
          countryMode: '0',

          requiredField: '',
          listRequiredField: '',

          qtyDigit: 5,
          qty1Digit: 5,
          qty2Digit: 5,
          netWtDigit: 8,

          uniqueness: '0',
          autoMat: '0',
          checkAttach: '0',
          checksum: '0',
          caseWrite: '0',
          checkExciseTax: '0',
          checkCurr: '',
          checkTradeMode: '',

          checkNetWt: '',
          netWtLimit: '',

          checkPrice: '',
          priceLimite: '',

          checkNetWtTotal: '',
          netWtTotalLimit: '',
          netWtAllowErrVal: '0',

          checkGrossWt: '',
          grossWtLimit: '0',

          attachType: '',
          configList: '',
          checkBondMark: '0',
          autoNetWt: '0',
          checkTotalPrice: '0',
          autoNetWtDigit: '6'
        }
      },
      /**
       * 加载随附单据类型
       */
      loadAttachTypes() {
        let me = this
        //繁转简配置查询
        me.$http.post(me.ajaxUrl.translate).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.isShowTranslate = true
            me.$set(me, 'translateTypeSource', res.data.data.map(item => {
              return {
                sid: item.sid,
                billType: item.billType,
                billName: item.billName,
                isEnabled: item.isEnabled
              }
            }))
            me.$set(me, 'translateArr', res.data.data.map(item => {
              if (item.isEnabled === '1') {
                return item.billType
              }
            }))
          }
        }).catch(() => {
          me.$set(me, 'translateTypeSource', [])
        }).finally(() => {
        })

        me.$http.post(me.ajaxUrl.loadAttachTypeUrl, {
          paramsType: 'DOC_TYPE'
        }).then(res => {
          me.$set(me, 'attachTypeSource', res.data.data.map(item => {
            return {
              label: item.paramsCode,
              title: item.paramsName
            }
          }))
          me.$set(me, 'attachTypeArr', res.data.data.map(item => {
            return item.paramsCode
          }))
        }).catch(() => {
          me.$set(me, 'attachTypeSource', [])
        }).finally(() => {
          me.configLoad()
        })
      },
      /**
       * 原有配置信息加载
       */
      configLoad() {
        let me = this
        me.$http.post(me.ajaxUrl.loadConfigUrl).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.noEmit = true
            me.warningStatus = true
            me.buttons.forEach(item => {
              if (item.command === 'save') {
                item.needed = false
              }
            })
            me.frmData = res.data.data[0]
            let switchKeys = ['countryMode', 'uniqueness', 'checkAttach', 'checksum',
              'caseWrite', 'checkBondMark', 'autoNetWt', 'checkTotalPrice']
            switchKeys.forEach(key => {
              if (!['0', '1'].includes(me.frmData[key])) {
                me.frmData[key] = '0'
              }
            })
            me.setAttachTypeStr(me.frmData.attachType)
            //me.setTranslateStr(me.frmData.configList)
          } else {
            me.frmData = me.getDefaultData()
            me.noEmit = false
            me.warningStatus = false
            me.buttons.forEach(item => {
              if (item.command === 'edit') {
                item.needed = false
              }
            })
          }
        }).catch(() => {
          me.frmData = me.getDefaultData()
        })
      },
      /**
       * 解析随附单据保存内容
       */
      setAttachTypeStr(attachType) {
        if (!isNullOrEmpty(attachType)) {
          try {
            let me = this,
              attachObj = JSON.parse(attachType)
            if (attachObj.hasOwnProperty('selected') && Array.isArray(attachObj['selected'])) {
              me.$set(me, 'attachTypeArr', attachObj['selected'])
            }
          } catch (e) {
            console.info(e.message)
          }
        }
      },
      // 解析繁转简保存内容
      // setTranslateStr(translateType){
      //   if(!isNullOrEmpty(translateType)) {
      //     try{
      //       translateType.forEach(item => {
      //         if(item.isEnabled === '1'){
      //           this.translateArr.push(item.billType)
      //         }
      //       })
      //     }
      //     catch(e){
      //       console.info(e.message)
      //     }
      //   }
      // },
      /**
       * 获取随附单据保存内容
       */
      getAttachTypeStr() {
        let me = this
        return JSON.stringify({
          selected: me.attachTypeArr,
          fullTypes: me.attachTypeSource
        })
      },
      /**
       * 繁转简保存
       */
      getTranslateType() {
        let me = this
        me.translateTypeSource.forEach(item => {
          if (me.translateArr.indexOf(item.billType) > -1) {
            item.isEnabled = '1'
          } else {
            item.isEnabled = '0'
          }
        })
        return me.translateTypeSource
      },
      /**
       * 设置保存按钮加载样式
       * @param loading
       */
      setBtnSaveLoading(loading) {
        let me = this
        me.buttons[0].loading = loading
      },
      /**
       * 添加扩展参数
       */
      getExtendQueryString() {
        return '/0'
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        if (Array.isArray(me.fieldList) && me.fieldList.length > 0) {
          me.$set(me.frmData, 'requiredField', me.fieldList.join(','))
        } else {
          me.$set(me.frmData, 'requiredField', '')
        }
        if (Array.isArray(me.bodyFieldList) && me.bodyFieldList.length > 0) {
          me.$set(me.frmData, 'listRequiredField', me.bodyFieldList.join(','))
        } else {
          me.$set(me.frmData, 'listRequiredField', '')
        }
        me.$set(me.frmData, 'attachType', me.getAttachTypeStr())
        me.$set(me.frmData, 'configList', me.getTranslateType())
        if (me.warningStatus) {
          me.editConfig.editStatus = editStatus.EDIT
        } else {
          me.editConfig.editStatus = editStatus.ADD
        }
        if (!me.showCheckNetWtNumber) {
          me.$set(me.frmData, 'netWtLimit', '')
        }
        if (!me.showTotalNumber) {
          me.$set(me.frmData, 'netWtTotalLimit', '')
        }
        if (isNumber(me.frmData.netWtAllowErrVal)) {
          me.$set(me.frmData, 'netWtAllowErrVal', Number(me.frmData.netWtAllowErrVal))
        } else {
          me.$set(me.frmData, 'netWtAllowErrVal', 0)
        }
        if (isNumber(me.frmData.grossWtLimit)) {
          me.$set(me.frmData, 'grossWtLimit', Number(me.frmData.grossWtLimit))
        } else {
          me.$set(me.frmData, 'grossWtLimit', 0)
        }
        me.doSave(res => {
          let result = me.getDefaultData()
          Object.keys(result).forEach(key => {
            if (res.data.data.hasOwnProperty(key)) {
              result[key] = res.data.data[key]
            }
          })
          me.frmData = result
          me.setClearanceBusinessSetting({
            checkExciseTax: me.frmData.checkExciseTax,
            caseWrite: me.frmData.caseWrite,
            emsNo: me.frmData.emsNo,
            tradeMode: me.frmData.tradeMode,
            countryMode: me.frmData.countryMode,
            requiredField: me.frmData.requiredField,
            listRequiredField: me.frmData.listRequiredField,
            attachType: me.frmData.attachType,
            configList: me.frmData.configList,
            precisionsConfig: {
              qtyDigit: me.frmData.qtyDigit,
              qty1Digit: me.frmData.qty1Digit,
              qty2Digit: me.frmData.qty2Digit,
              netWtDigit: me.frmData.netWtDigit
            }
          })
          me.editConfig.editStatus = editStatus.EDIT
          me.noEmit = true
          me.buttons.forEach(item => {
            if (item.command === 'save') {
              item.needed = false
            } else if (item.command === 'edit') {
              item.needed = true
            }
          })
        })
      },
      /**
       * 编辑
       */
      handleEmit() {
        let me = this
        me.noEmit = false
        me.buttons.forEach(item => {
          if (item.command === 'save') {
            item.needed = true
          } else if (item.command === 'edit') {
            item.needed = false
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .ivu-form-item-content {
    padding-top: 3px;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  .showRightInput {
    display: grid;
    grid-template-columns: auto 80px;
  }
</style>
