<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="250" class="dc-form-2"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        <template v-slot:checkPrice>
          <div :class="{'showRightInput':showPriceLimiteNumber,'':!showPriceLimiteNumber}">
            <xdo-select v-model="detailConfig.model.checkPrice" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showPriceLimiteNumber" v-model="detailConfig.model.priceLimite"
                            integerDigits="2" precision="2" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>
        <template v-slot:checkNetWtTotal>
          <div :class="{'showRightInput':showTotalNumber,'':!showTotalNumber}">
            <xdo-select v-model="detailConfig.model.checkNetWtTotal" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showTotalNumber" v-model="detailConfig.model.netWtAllowErrVal" style="width: 80px;"
                            integerDigits="2" precision="5" :disabled="frmDisplay"></dc-numberInput>
          </div>
        </template>
        <template v-slot:checkGrossWt>
          <div :class="{'showRightInput':showGrossWtLimit,'':!showGrossWtLimit}">
            <xdo-select v-model="detailConfig.model.checkGrossWt" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showGrossWtLimit" v-model="detailConfig.model.grossWtLimit" style="width: 80px;"
                            integerDigits="2" precision="5" :disabled="frmDisplay"></dc-numberInput>
          </div>
        </template>
        <template v-slot:checkNetWt>
          <div :class="{'showRightInput':showCheckNetWtNumber,'':!showCheckNetWtNumber}">
            <xdo-select v-model="detailConfig.model.checkNetWt" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showCheckNetWtNumber" v-model="detailConfig.model.netWtLimit"
                            integerDigits="2" precision="2" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>

        <template v-slot:checkIDecPrice>
          <div :class="{'showRightInput':showCheckIDecPriceNumber,'':!showCheckIDecPriceNumber}">
            <xdo-select v-model="detailConfig.model.checkIDecPrice" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showCheckIDecPriceNumber" v-model="detailConfig.model.priceIPercentage"
                            integerDigits="3" precision="0" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>
        <template v-slot:checkEDecPrice>
          <div :class="{'showRightInput':showCheckEDecPriceNumber,'':!showCheckEDecPriceNumber}">
            <xdo-select v-model="detailConfig.model.checkEDecPrice" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showCheckEDecPriceNumber" v-model="detailConfig.model.priceEPercentage"
                            integerDigits="3" precision="0" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>
        <template v-slot:checkISingleWeight>
          <div :class="{'showRightInput':showCheckISingleWeightNumber,'':!showCheckISingleWeightNumber}">
            <xdo-select v-model="detailConfig.model.checkISingleWeight" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showCheckISingleWeightNumber" v-model="detailConfig.model.singleIPercentage"
                            integerDigits="3" precision="0" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>
        <template v-slot:checkESingleWeight>
          <div :class="{'showRightInput':showCheckESingleWeightNumber,'':!showCheckESingleWeightNumber}">
            <xdo-select v-model="detailConfig.model.checkESingleWeight" :options="earlyWarningManage.CHECK_NET_WT"
                        :optionLabelRender="pcodeRender" :disabled="frmDisplay"></xdo-select>
            <dc-numberInput v-if="showCheckESingleWeightNumber" v-model="detailConfig.model.singleEPercentage"
                            integerDigits="3" precision="0" append="%"
                            :disabled="frmDisplay" style="width: 80px;">
            </dc-numberInput>
          </div>
        </template>


        <template v-slot:invoiceNoType>
          <table style="width: 100%; padding: 0; margin: 0;" cellpadding="0" cellspacing="0">
            <tr>
              <td style="width: 66%;">
                <xdo-select v-model="detailConfig.model.invoiceNoType" :options="cmbSource.invoiceNoType"
                            :optionLabelRender="pcodeRender" @on-change="invoiceNoTypeChange" :disabled="frmDisplay"></xdo-select>
              </td>
              <td style="width: 34%;">
                <Input v-model="detailConfig.model.invoiceNoSplit" placeholder="请输入分隔符..." :disabled="invoiceNoSplitDisplay"></Input>
              </td>
            </tr>
          </table>
        </template>
        <template v-slot:contrNoType>
          <table style="width: 100%; padding: 0; margin: 0;" cellpadding="0" cellspacing="0">
            <tr>
              <td style="width: 66%">
                <xdo-select v-model="detailConfig.model.contrNoType" :options="cmbSource.contrNoType"
                            :optionLabelRender="pcodeRender" @on-change="contrNoTypeChange" :disabled="frmDisplay"></xdo-select>
              </td>
              <td style="width: 34%">
                <Input v-model="detailConfig.model.contrNoSplit" placeholder="请输入分隔符..." :disabled="contrNoSplitDisplay"></Input>
              </td>
            </tr>
          </table>
        </template>
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus, earlyWarningManage } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  let { createNamespacedHelpers } = majesty.Vuex
  let { mapMutations } = createNamespacedHelpers(namespace)

  export default {
    name: 'businessSettingBase',
    mixins: [baseDetailConfig],
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editStatus: editStatus.EDIT
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        sid: '',
        searching: false,
        translateArr: [],       // 繁转简
        tmpAttachType: [],
        formName: 'frmData',
        editStatus: editStatus.SHOW,
        earlyWarningManage: earlyWarningManage,
        ajaxUrl: {
          insert: csAPI.earlyWarning.manager.clearanceBusiness.insert,
          update: csAPI.earlyWarning.manager.clearanceBusiness.update,
          loadAttachTypeUrl: csAPI.enterpriseParamsLib.comm.selectAllPaged,
          translate: csAPI.earlyWarning.manager.clearanceBusiness.translate,
          loadConfigUrl: csAPI.earlyWarning.manager.clearanceBusiness.loadConfigUrl,
          getEntryChannelType: csAPI.earlyWarning.manager.clearanceBusiness.getEntryChannelType
        },
        buttons: [
          { ...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave },
          { ...btnComm, label: '编辑', type: 'warning', command: 'edit', click: this.handleEdit }
        ],
        cmbSource: {
          checkCurr: earlyWarningManage.CHECK_CURR,
          optionList: ['0', '1', '2', '3', '4', '5'],
          configListSource: [],
          precisions: [{
            label: 0, title: '0'
          }, {
            label: 1, title: '1'
          }, {
            label: 2, title: '2'
          }, {
            label: 3, title: '3'
          }, {
            label: 4, title: '4'
          }, {
            label: 5, title: '5'
          }],
          precisionsNetWt: [{
            label: 0, title: '0'
          }, {
            label: 1, title: '1'
          }, {
            label: 2, title: '2'
          }, {
            label: 3, title: '3'
          }, {
            label: 4, title: '4'
          }, {
            label: 5, title: '5'
          }, {
            label: 6, title: '6'
          }, {
            label: 7, title: '7'
          }, {
            label: 8, title: '8'
          }],
          precisionsStr: [{
            label: '0', title: '0'
          }, {
            label: '1', title: '1'
          }, {
            label: '2', title: '2'
          }, {
            label: '3', title: '3'
          }, {
            label: '4', title: '4'
          }, {
            label: '5', title: '5'
          }],
          attachTypeSource: [],
          checkExgVersionType: [{
            label: '0', title: '无'
          }, {
            label: '1', title: 'YYYYMM01'
          }, {
            label: '2', title: '按备案号默认值'
          }, {
            label: '3', title: '按单损耗最新值'
          }, {
            label: '4', title: '最新插入时间取单耗版本号'
          }],
          checkTradeMode: earlyWarningManage.CHECK_TRADE_MODE,
          entryChannelType: [{
            value: '0', label: '不开通'
          }, {
            value: '1', label: '报关小助手上载单一'
          }, {
            value: '2', label: '立交桥上载单一'
          }, {
            value: '3', label: '立交桥暂存'
          }],
          importType: [{
            value: '1', label: '光荣定制导入'
          }, {
            value: '2', label: '埃玛克定制导入'
          }, {
            value: '3', label: '日月光定制导入'
          }],
          invoiceAttach: [{
            label: '0', title: '否'
          }, {
            label: '1', title: '生成 EXCEL'
          }, {
            label: '2', title: '生成 PDF'
          }],
          // 运费生成规则
          feeType: [{
            value: '0', label: '无'
          }, {
            value: '1', label: '淳华规则'
          }, {
            value: '2', label: '生益规则'
          }, {
            value: '3', label: '欧朗规则'
          }],
          // 出口表头运抵国(地区)和表体目的国系统自动比对
          checkBodyCountryE: [{
            value: '0', label: '不校验'
          }, {
            value: '1', label: '提示'
          }, {
            value: '2', label: '限制'
          }],
          // 出口表体BOM版本设置规则提醒(日月新)
          reExgVersion: [{
            value: '0', label: '无'
          }, {
            value: '1', label: '日月新规则(M+7位阿拉伯数字)'
          }],
          contrNoType: [{
            value: '0', label: '无'
          }, {
            value: '1', label: ' 取表体第一个'
          }, {
            value: '2', label: '取表体第一个,多个时+等'
          }, {
            value: '3', label: '使用分隔符组装'
          }],
          invoiceNoType: [{
            value: '0', label: '无'
          }, {
            value: '1', label: ' 取表体第一个'
          }, {
            value: '2', label: '取表体第一个,多个时+等'
          }, {
            value: '3', label: '使用分隔符组装'
          }],
          bomCheck: [{
            //   value: '0', label: '不校验'
            // }, {
            value: '1', label: '提示'
          }, {
            value: '2', label: '限制'
          }],
          grossWtType: [{
            value: '0', label: '无'
          }, {
            value: '1', label: '表体毛重取箱单信息毛重'
          }],
          volumeType: [{
            value: '0', label: '无'
          }, {
            value: '1', label: '表体体积取箱单信息体积'
          }],
          checkEmlQty: earlyWarningManage.CHECK_CURR,
          checkCountryI: earlyWarningManage.CHECK_CURR,
          checkCountryE: earlyWarningManage.CHECK_CURR,
          involveCertCheck: earlyWarningManage.involveCertCheckStatus,
          involveCertTimesCheck: earlyWarningManage.InvolveCertTimesCheckStatus
        }
      }
    },
    watch: {
      frmDisplay: {
        immediate: true,
        handler: function() {
          this.fieldsReset()
        }
      }
    },
    created: function() {
      let me = this
      me.dataLoad()
      me.buttons[0].disabled = true
      me.loadTranslateData()
      me.loadAttachTypeSource()
    },
    computed: {
      frmDisplay() {
        return this.editStatus === editStatus.SHOW
      },
      invoiceNoSplitDisplay() {
        let me = this
        if (me.editStatus === editStatus.ADD || me.editStatus === editStatus.EDIT) {
          if (me.detailConfig.model.invoiceNoType === '3') {
            return false
          }
        }
        return true
      },
      contrNoSplitDisplay() {
        let me = this
        if (me.editStatus === editStatus.ADD || me.editStatus === editStatus.EDIT) {
          if (me.detailConfig.model.contrNoType === '3') {
            return false
          }
        }
        return true
      },
      showPriceLimiteNumber() {
        return this.detailConfig.model.checkPrice === '1' || this.detailConfig.model.checkPrice === '2'
      },
      showTotalNumber() {
        return this.detailConfig.model.checkNetWtTotal === '1' || this.detailConfig.model.checkNetWtTotal === '2'
      },
      showGrossWtLimit() {
        return this.detailConfig.model.checkGrossWt === '1' || this.detailConfig.model.checkGrossWt === '2'
      },
      showCheckNetWtNumber() {
        return this.detailConfig.model.checkNetWt === '1' || this.detailConfig.model.checkNetWt === '2'
      },

      showCheckIDecPriceNumber() {
        return this.detailConfig.model.checkIDecPrice === '1' || this.detailConfig.model.checkIDecPrice === '2'
      },
      showCheckEDecPriceNumber() {
        return this.detailConfig.model.checkEDecPrice === '1' || this.detailConfig.model.checkEDecPrice === '2'
      },
      showCheckISingleWeightNumber() {
        return this.detailConfig.model.checkISingleWeight === '1' || this.detailConfig.model.checkISingleWeight === '2'
      },
      showCheckESingleWeightNumber() {
        return this.detailConfig.model.checkESingleWeight === '1' || this.detailConfig.model.checkESingleWeight === '2'
      }

    },
    methods: {
      ...mapMutations([
        'setClearanceBusinessSetting'
      ]),
      /**
       * 设置扩展字段
       * @returns {{}}
       */
      getExtendFields() {
        return {
          // autoNetWtDigitI: '5',
          // autoNetWtDigitE: '5',
          // autoGrossWtDigitI: '5',
          // autoGrossWtDigitE: '5',
          priceLimite: '',
          netWtAllowErrVal: '0',
          grossWtLimit: '0',
          netWtLimit: '',
          priceEPercentage: '',
          priceIPercentage: '',
          singleIPercentage: '',
          singleEPercentage: ''
        }
      },
      /**
       * 加载需要执行繁转简的模块
       */
      loadTranslateData() {
        let me = this
        me.$http.post(me.ajaxUrl.translate).then(res => {
          me.$set(me.cmbSource, 'configListSource', res.data.data.map(item => {
            return {
              label: item.billType,
              title: item.billName
            }
          }))
          me.$set(me, 'translateArr', res.data.data.map(item => {
            if (item.isEnabled === '1') {
              return item.billType
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'configListSource', [])
        }).finally(() => {
          me.detailConfig.fields.filter(field => {
            return field.key === 'configList'
          }).forEach(item => {
            if (item.props) {
              item.props.options = me.cmbSource.configListSource
            } else {
              item.props = {
                options: me.cmbSource.configListSource
              }
            }
          })
          me.$nextTick(() => {
            me.$set(me.detailConfig.model, 'configList', String(me.translateArr))
          })
        })
      },
      loadAttachTypeSource() {
        let me = this
        me.$http.post(me.ajaxUrl.loadAttachTypeUrl, {
          paramsType: 'DOC_TYPE'
        }).then(res => {
          me.$set(me.cmbSource, 'attachTypeSource', res.data.data.map(item => {
            return {
              label: item.paramsCode,
              title: item.paramsName
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'attachTypeSource', [])
        }).finally(() => {
          me.detailConfig.fields.filter(field => {
            return ['attachType', 'attachCheckType'].includes(field.key)
          }).forEach(item => {
            if (item.props) {
              item.props.options = me.cmbSource.attachTypeSource
            } else {
              item.props = {
                options: me.cmbSource.attachTypeSource
              }
            }
          })
        })
      },
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        } else {
          me.$nextTick(() => {
            if (me.searching === false) {
              me.$set(me, 'searching', true)
              me.$http.post(me.ajaxUrl.getEntryChannelType).then(res => {
                if (['0', '1', '2', '3'].includes(res.data.data)) {
                  me.$set(me.detailConfig.model, 'entryChannelType', res.data.data)
                }
              }).catch(() => {
              }).finally(() => {
                me.$set(me, 'searching', false)
              })
            }
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
        me.$set(me.detailConfig, 'defaultData', fieldsObject.model)
      },
      dataLoad() {
        let me = this
        me.$http.post(me.ajaxUrl.loadConfigUrl).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$nextTick(() => {
              let theModel = me.dataCopy(res.data.data[0]),
                attachType = '',
                attachTypeObj = theModel.attachType
              try {
                let attachObj = JSON.parse(attachTypeObj)
                if (attachObj.hasOwnProperty('selected') && !isNullOrEmpty(attachObj['selected'])) {
                  attachType = attachObj['selected']
                }
              } catch (e) {
                console.info(e.message)
                attachType = ''
              }
              theModel.attachType = attachType
              me.$set(me, 'tmpAttachType', attachType.split(','))
              me.$set(me.detailConfig, 'model', theModel)
              me.$set(me, 'sid', res.data.data[0].sid)
            })
          }
        }).catch(() => {
        })
      },
      dataCopy(currModel) {
        let me = this,
          theModel = {
            sid: currModel.sid
          },
          extendFields = me.getExtendFields()
        Object.keys(me.detailConfig.model).forEach(fieldName => {
          if (currModel.hasOwnProperty(fieldName)) {
            theModel[fieldName] = currModel[fieldName]
          } else {
            theModel[fieldName] = me.detailConfig.model[fieldName]
          }
        })
        Object.keys(extendFields).forEach(fieldName => {
          if (currModel.hasOwnProperty(fieldName)) {
            theModel[fieldName] = currModel[fieldName]
          } else {
            theModel[fieldName] = me.detailConfig.model[fieldName]
          }
        })
        return theModel
      },
      getFields() {
        return [{
          isCard: true,
          title: '物料配置项',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-3'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          key: 'autoMat',
          defaultValue: '0',
          title: '是否备案物料自动提取'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'uniqueness',
          title: '同物料保税与非保税不得同时存在'
        }, {
          type: 'switch',
          key: 'caseWrite',
          defaultValue: '0',
          slot: {
            open: '可大小写',
            close: '只能大写'
          },
          title: '备案料号英文是否支持大小写'
        }, {
          type: 'switch',
          slot: {
            open: '显示',
            close: '不显示'
          },
          defaultValue: '0',
          title: '消费税率显示',
          key: 'checkExciseTax'
        }, {
          type: 'switch',
          slot: {
            open: '显示',
            close: '不显示'
          },
          defaultValue: '0',
          key: 'dutyFreeApp',
          title: '免表信息显示'
        }, {
          isCard: true,
          title: '繁转简设置',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-3'
        }, {
          title: '',
          labelWidth: 60,
          key: 'configList',
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-3',
          props: {
            options: this.dynamicSource.configListSource
          }
        }, {
          isCard: true,
          title: '通关配置项',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-3'
        }, {
          defaultValue: '0',
          type: 'radioGroup',
          key: 'checkExgVersion',
          title: '单耗版本号默认值确认',
          props: {
            options: this.dynamicSource.checkExgVersionType
          }
        }, {
          slot: {
            open: '小提单',
            close: '大提单'
          },
          type: 'switch',
          key: 'checkDec',
          defaultValue: '0',
          title: '进出口提单数据落地'
        }, {
          type: 'switch',
          key: 'billType',
          defaultValue: '0',
          title: '生成清单规则',
          slot: {
            close: '旧',
            open: '新(1000行控制)'
          }
        }, {
          slot: {
            open: '校验',
            close: '不校验'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'checkContainer',
          title: '集装箱校验(南京LG定制)'
        }, {
          slot: {
            open: '校验',
            close: '不校验'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'checkAttach',
          title: '提单发送内审时校验随附单据是否存在'
        }, {
          slot: {
            open: '校验',
            close: '不校验'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'checkBondMark',
          title: '提单提取数据保完税标识校验'
        }, {
          slot: {
            open: '校验',
            close: '不校验'
          },
          type: 'switch',
          key: 'checksum',
          defaultValue: '0',
          title: '提单表头总价、总量校验'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'checkTotalPrice',
          title: '提单表体单价*数量是否等于总价提示'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'countryMode',
          title: '出口管理 表头最终目的国信息自动带到表体'
        }, {
          slot: {
            open: '校验',
            close: '不校验'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'checkHsCode',
          title: '物料HSCODE校验'
        }, {
          slot: {
            open: '新',
            close: '旧'
          },
          type: 'switch',
          key: 'decType',
          defaultValue: '1',
          title: '新旧预录入单表头控制'
        }, {
          type: 'select',
          key: 'remarkTypeE',
          title: '出口备注自动获取栏位',
          props: {
            options: [{
              value: '0', label: '无'
            }, {
              value: '1', label: '日月新出口备注生成规则'
            }, {
              value: '2', label: '日月光出口备注生成规则'
            }]
          }
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          key: 'autoNetWtI',
          defaultValue: '0',
          title: '进口-提单表头净重自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          key: 'autoNetWtE',
          defaultValue: '0',
          title: '出口-提单表头净重自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'autoGrossWtI',
          title: '进口-提单表头毛重自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'autoGrossWtE',
          title: '出口-提单表头毛重自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          key: 'packNumI',
          defaultValue: '0',
          title: '进口-提单表头件数自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          key: 'packNumE',
          defaultValue: '0',
          title: '出口-提单表头件数自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'autoVolumeI',
          title: '进口-提单表头体积自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          key: 'autoVolumeE',
          title: '出口-提单表头体积自动汇总表体'
        }, {
          slot: {
            open: '是',
            close: '否'
          },
          type: 'switch',
          defaultValue: '0',
          title: '报关单数据落地',
          key: 'persistentEntry'
        },

          //   {
          //   slot: {
          //     open: '是',
          //     close: '否'
          //   },
          //   type: 'switch',
          //   defaultValue: '0',
          //   key: 'autoDutyform',
          //   title: '税单自动采集'
          // },
          {
            type: 'select',
            key: 'autoDutyform',
            title: '税单自动采集',
            defaultValue: '1',
            props: {
              options: [{
                value: '0', label: '否'
              }, {
                value: '1', label: '按报关单号下载'
              }, {
                value: '2', label: '按日期下载'
              }]
            }
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            key: 'matDataI',
            defaultValue: '0',
            title: '进口是否自动带入物料中心数据(单价、币制、原产国)'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            key: 'taxAttach',
            defaultValue: '0',
            title: '税单附件采集'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            key: 'matDataE',
            defaultValue: '0',
            title: '出口是否自动带入物料中心数据(单价、币制、目的国)'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'autoSumClientTotalBE',
            title: '出口保税客供金额自动汇总'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'autoSumMatTotalBE',
            title: '出口保税料费自动汇总'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'autoSumProcessTotalBE',
            title: '出口保税系统加工费自动汇总'
          }, {
            key: 'checkAppr',
            type: 'checkBoxGroup',
            title: '内审通过才允许打印草单',
            props: {
              options: [{
                label: '0', title: '清单草单'
              }, {
                label: '1', title: '企业料号清单草单'
              }, {
                label: '2', title: '报关单草单'
              }]
            }
          }, {
            type: 'select',
            key: 'taxSummary',
            title: '税金汇总配置',
            props: {
              options: [{
                value: '0', label: '不汇总'
              }, {
                value: '1', label: '按报关单税金汇总'
              }, {
                value: '2', label: '按预录入单税金汇总'
              }]
            }
          }, {
            type: 'select',
            key: 'checkTradeMode',
            title: '提单表头与表体监管方式不一致校验'
          }, {
            type: 'select',
            key: 'checkCurr',
            title: '预录入表体币制与企业料号不一致校验'
          }, {
            type: 'select',
            key: 'checkPrice',
            title: '提单表体单价与物料单价不一致校验'
          }, {
            type: 'select',
            key: 'checkNetWtTotal',
            title: '提单表头与表体净重是否一致性校验'
          }, {
            type: 'select',
            key: 'checkGrossWt',
            title: '提单表头与表体毛重是否一致性校验'
          }, {
            type: 'select',
            key: 'checkNetWt',
            title: '提单表体净重与物料净重不一致校验'
          }, {
            type: 'select',
            key: 'importType',
            title: '定制导入类型'
          }, {
            type: 'select',
            key: 'checkEmlQty',
            title: '手册余量预警'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'entCompareFlag',
            title: '订阅报关单比对配置'
          }, {
            type: 'select',
            title: '涉证超量校验',
            key: 'involveCertCheck'
          }, {
            type: 'select',
            title: '证书超次校验',
            key: 'involveCertTimesCheck'
          }, {
            labelWidth: 250,
            defaultValue: '0',
            type: 'radioGroup',
            key: 'invoiceAttach',
            title: '是否自动生成随附单据',
            props: {
              options: this.dynamicSource.invoiceAttach
            }
          }, {
            // 提单表头列数
            labelWidth: 250,
            defaultValue: '3',
            type: 'radioGroup',
            key: 'columnCount',
            props: {
              options: [{
                label: '3', title: '3'
              }, {
                label: '4', title: '4'
              }]
            },
            title: '(动态)进出口预录入单表头每行展示列数'
          }, {
            type: 'select',
            key: 'checkCountryI',
            title: '进口提单表体与物料中心产销国校验'
          }, {
            type: 'select',
            key: 'checkCountryE',
            title: '出口提单表体与物料中心产销国校验'
          }, {
            type: 'select',
            key: 'reExgVersion',
            title: '清单生成时的单耗版本号校验(限制)'
          }, {
            type: 'select',
            key: 'checkBodyCountryE',
            title: '出口表头运抵国(地区)和表体目的国比对'
          }, {
            type: 'select',
            key: 'feeType',
            title: '清单运费生成规则'
          }, {
            key: 'invoiceNoType',
            title: '预录入单表头发票号取值',
          }, {
            key: 'contrNoType',
            title: '预录入单表头合同号取值'
          }, {
            type: 'select',
            key: 'grossWtType',
            title: '预录入单表体毛重生成规则'
          }, {
            type: 'select',
            key: 'volumeType',
            title: '预录入单表体体积生成规则'
          }, {
            type: 'select',
            key: 'otherRateCheck',
            title: '进口杂费预警校验',
            props: {
              options: [{
                value: '0', label: '不校验'
              }, {
                value: '1', label: '提示'
              }]
            }
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'getManifestInfo',
            title: '是否获取舱单信息'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            key: 'checkRcep',
            defaultValue: '0',
            title: '是否校验RCEP税率'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            defaultValue: '0',
            key: 'pageConfigure',
            title: '发票模板是否分页'
          }, {
            type: 'select',
            key: 'subscriptionEntry',
            title: '订阅报关单回填',
            defaultValue: '0',
            props: {
              options: [{
                value: '0', label: '不回填'
              }, {
                value: '1', label: '按合同协议号回填'
              }]
            }
          },
          {
            type: 'select',
            key: 'checkIDecPrice',
            title: '保税进口单价与上一票校验'
          },
          {
            type: 'select',
            key: 'checkEDecPrice',
            title: '保税出口单价与上一票校验'
          },
          {
            type: 'select',
            key: 'checkISingleWeight',
            title: '保税进口单重与上一票校验'
          },
          {
            type: 'select',
            key: 'checkESingleWeight',
            title: '保税出口单重与上一票校验'
          },
          {
            type: 'select',
            key: 'checkIOriginCountry',
            title: '保税进口原产国与上一票校验',
            defaultValue: '0',
            props: {
              options: [{
                value: '0', label: '不校验'
              }, {
                value: '1', label: '提示'
              }, {
                value: '2', label: '限制'
              }]
            }
          },
          {
            type: 'select',
            key: 'invoiceSource',
            title: '发票模块预录入单来源',
            defaultValue: '0',
            props: {
              options: [{
                value: '0', label: '手动新增'
              }, {
                value: '1', label: '内审通过自动产生'
              }]
            }
          },
          {
            type: 'select',
            key: 'unitCalculate',
            title: '内销定制',
            defaultValue: '0',
            props: {
              options: [{
                value: '0', label: '否'
              }, {
                value: '1', label: '是'
              }]
            }
          },
          {
            isCard: true,
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3',
            key: 'shiFouKeXiuGai11111111',
            title: '进出口表体字段控制-是否可修改-勾选代表不可编辑'
          }, {
            title: '进口保税',
            type: 'checkBoxGroup',
            key: 'canEditBodyIBond',
            props: {
              options: [{
                label: '0', title: '申报规格型号'
              }, {
                label: '1', title: '料号申报要素'
              }]
            }
          }, {
            title: '进口非保税',
            type: 'checkBoxGroup',
            key: 'canEditBodyINobond',
            props: {
              options: [{
                label: '0', title: '申报规格型号'
              }, {
                label: '1', title: '料号申报要素'
              }]
            }
          }, {
            title: '出口保税',
            type: 'checkBoxGroup',
            key: 'canEditBodyEBond',
            props: {
              options: [{
                label: '0', title: '申报规格型号'
              }, {
                label: '1', title: '料号申报要素'
              }]
            }
          }, {
            title: '出口非保税',
            type: 'checkBoxGroup',
            key: 'canEditBodyENobond',
            props: {
              options: [{
                label: '0', title: '申报规格型号'
              }, {
                label: '1', title: '料号申报要素'
              }]
            }
          }, {
            isCard: true,
            key: '123223323423',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3',
            title: '表头数据小数位数设置'
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '进口-总净重',
            type: 'radioGroup',
            key: 'autoNetWtDigitI',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '出口-总净重',
            type: 'radioGroup',
            key: 'autoNetWtDigitE',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '进口-总毛重',
            type: 'radioGroup',
            key: 'autoGrossWtDigitI',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '出口-总毛重',
            type: 'radioGroup',
            key: 'autoGrossWtDigitE',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            title: '进口-总价',
            defaultValue: '4',
            type: 'radioGroup',
            key: 'headDecTotalDigitI',
            props: {
              options: this.dynamicSource.precisionsStr.filter(it => {
                return it.label !== '5'
              })
            }
          }, {
            labelWidth: 120,
            defaultValue: '4',
            title: '出口-总价',
            type: 'radioGroup',
            key: 'headDecTotalDigitE',
            props: {
              options: this.dynamicSource.precisionsStr.filter(it => {
                return it.label !== '5'
              })
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '进口-体积',
            type: 'radioGroup',
            key: 'headVolumeDigitI',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '出口-体积',
            type: 'radioGroup',
            key: 'headVolumeDigitE',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            isCard: true,
            key: '121212121212',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3',
            title: '表体数据小数位数设置'
          }, {
            defaultValue: 5,
            labelWidth: 120,
            key: 'qtyDigit',
            title: '申报数量',
            type: 'radioGroup',
            props: {
              options: this.dynamicSource.precisions
            }
          }, {
            defaultValue: 5,
            labelWidth: 120,
            key: 'qty1Digit',
            title: '法一数量',
            type: 'radioGroup',
            props: {
              options: this.dynamicSource.precisions
            }
          }, {
            defaultValue: 5,
            labelWidth: 120,
            key: 'qty2Digit',
            title: '法二数量',
            type: 'radioGroup',
            props: {
              options: this.dynamicSource.precisions
            }
          }, {
            title: '净重',
            defaultValue: 5,
            labelWidth: 120,
            key: 'netWtDigit',
            type: 'radioGroup',
            props: {
              options: this.dynamicSource.precisionsNetWt
            }
          }, {
            labelWidth: 120,
            defaultValue: '4',
            title: '进口-总价',
            type: 'radioGroup',
            key: 'decTotalDigitI',
            props: {
              options: this.dynamicSource.precisionsStr.filter(it => {
                return it.label !== '5'
              })
            }
          }, {
            labelWidth: 120,
            defaultValue: '4',
            title: '出口-总价',
            type: 'radioGroup',
            key: 'decTotalDigitE',
            props: {
              options: this.dynamicSource.precisionsStr.filter(it => {
                return it.label !== '5'
              })
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '进口-体积',
            type: 'radioGroup',
            key: 'volumeDigitI',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            labelWidth: 120,
            defaultValue: '5',
            title: '出口-体积',
            type: 'radioGroup',
            key: 'volumeDigitE',
            props: {
              options: this.dynamicSource.precisionsStr
            }
          }, {
            isCard: true,
            key: '121212121212',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3',
            title: '提单随附单据类型设置'
          }, {
            labelWidth: 120,
            key: 'attachType',
            title: '随附单据类型',
            type: 'checkBoxGroup',
            itemClass: 'dc-merge-1-3',
            props: {
              options: this.dynamicSource.attachTypeSource
            },
            on: {
              change: this.attachTypeChange
            }
          }, {
            labelWidth: 120,
            title: '可上传类型',
            type: 'checkBoxGroup',
            key: 'attachCheckType',
            itemClass: 'dc-merge-1-3',
            props: {
              options: this.dynamicSource.attachTypeSource
            }
          }, {
            isCard: true,
            key: '254983516486',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3',
            title: '报关单发送通道设置'
          }, {
            type: 'select',
            defaultValue: '0',
            key: 'entryChannelType',
            title: '报关单发送通道类型'
          }, {
            isCard: true,
            key: '56356356356t',
            title: '内销管理配置项',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3'
          }, {
            type: 'select',
            key: 'bomCheck',
            defaultValue: '1',
            title: 'BOM完整性不通过是否折料'
          }, {
            isCard: true,
            key: '56322356356t',
            title: '费用维护',
            type: 'empty_formItem',
            itemClass: 'dc-merge-1-3'
          }, {
            slot: {
              open: '是',
              close: '否'
            },
            type: 'switch',
            key: 'actualMainCost',
            defaultValue: '0',
            title: '费用维护是否维护实际运保费'
          }]
      },
      /**
       *
       * @param newValue
       */
      invoiceNoTypeChange(newValue) {
        let me = this
        me.setDisable('invoiceNoSplit', newValue !== '3')
        if (newValue !== '3') {
          me.$nextTick(() => {
            me.$set(me.detailConfig.model, 'invoiceNoSplit', '')
          })
        }
      },
      /**
       *
       * @param newValue
       */
      contrNoTypeChange(newValue) {
        let me = this
        me.setDisable('contrNoSplit', newValue !== '3')
        if (newValue !== '3') {
          me.$nextTick(() => {
            me.$set(me.detailConfig.model, 'contrNoSplit', '')
          })
        }
      },
      /**
       * 添加扩展参数
       */
      getExtendQueryString() {
        return '/1'
      },
      /**
       * 获取随附单据保存内容
       */
      getAttachTypeStr() {
        let me = this,
          tmpAttachType = me.detailConfig.model.attachType
        while (tmpAttachType.indexOf(',') === 0) {
          tmpAttachType = tmpAttachType.substr(1)
        }
        return JSON.stringify({
          selected: tmpAttachType,
          fullTypes: me.cmbSource.attachTypeSource
        })
      },
      /**
       * 随附单据类型只变更
       * @param newValues
       */
      attachTypeChange(newValues) {
        let me = this,
          newValue = '',
          attachCheckTypeArray = [],
          attachCheckTypeValue = [],
          attachCheckTypeValueOld = me.detailConfig.model['attachCheckType']
        if (!isNullOrEmpty(attachCheckTypeValueOld)) {
          attachCheckTypeValue = attachCheckTypeValueOld.split(',')
        }
        for (let l = 0; l < newValues.length; l++) {
          if (!me.tmpAttachType.includes(newValues[l])) {
            newValue = newValues[l]
          }
        }
        for (let i = 0; i < attachCheckTypeValue.length; i++) {
          if (!isNullOrEmpty(attachCheckTypeValue[i]) && newValues.includes(attachCheckTypeValue[i])) {
            attachCheckTypeArray.push(attachCheckTypeValue[i])
          }
        }
        if (!isNullOrEmpty(newValue) && !attachCheckTypeArray.includes(newValue)) {
          attachCheckTypeArray.push(newValue)
        }
        me.$set(me.detailConfig.model, 'attachCheckType', attachCheckTypeArray.toString())
        me.$set(me, 'tmpAttachType', newValues)
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'sid', me.sid)
        me.$set(me.detailConfig.model, 'attachType', me.getAttachTypeStr())
        if (isNullOrEmpty(me.sid)) {
          me.$set(me.editConfig, 'editStatus', editStatus.ADD)
        } else {
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        }
        console.info('attachType: ' + me.detailConfig.model.attachType)
        console.info('attachCheckType: ' + me.detailConfig.model.attachCheckType)
        me.doSave(res => {
          let currModel = deepClone(res.data.data),
            theModel = {
              sid: currModel.sid
            }
          me.$set(me, 'sid', currModel.sid)
          Object.keys(me.detailConfig.model).forEach(key => {
            if (currModel.hasOwnProperty(key)) {
              if (currModel[key] === null || typeof currModel[key] === 'undefined') {
                theModel[key] = me.detailConfig.defaultData[key]
              } else if (typeof currModel[key] === 'object' && Object.keys(currModel[key]).length === 0) {
                theModel[key] = me.detailConfig.defaultData[key]
              } else {
                theModel[key] = currModel[key]
              }
            }
          })
          let attachType = '',
            attachTypeObj = theModel.attachType
          try {
            let attachObj = JSON.parse(attachTypeObj)
            if (attachObj.hasOwnProperty('selected') && !isNullOrEmpty(attachObj['selected'])) {
              attachType = attachObj['selected']
            }
          } catch (e) {
            console.info(e.message)
            attachType = ''
          }
          theModel.attachType = attachType
          me.$set(me.detailConfig, 'model', theModel)
          me.setClearanceBusinessSetting({
            autoMat: currModel.autoMat,                              // 是否备案物料自动提取
            uniqueness: currModel.uniqueness,                        // 同物料保税与非保税不得同时存在
            caseWrite: currModel.caseWrite,                          // 备案料号英文是否支持大小写
            checkExciseTax: currModel.checkExciseTax,                // 消费税率显示
            dutyFreeApp: currModel.dutyFreeApp,                      // 免表信息显示
            configList: currModel.configList,                        // 繁转简设置
            checkExgVersion: currModel.checkExgVersion,              // 单耗版本号默认值确认
            checkDec: currModel.checkDec,                            // 进出口提单数据落地
            billType: currModel.billType,                            // 生成清单规则
            checkContainer: currModel.checkContainer,                // 集装箱校验(南京LG定制)
            checkAttach: currModel.checkAttach,                      // 提单发送内审时校验随附单据是否存在
            checkBondMark: currModel.checkBondMark,                  // 提单提取数据保完税标识校验
            checksum: currModel.checksum,                            // 提单表头总价、总量校验
            checkTotalPrice: currModel.checkTotalPrice,              // 提单表体单价*数量是否等于总价提示
            countryMode: currModel.countryMode,                      // 出口管理 表头最终目的国信息自动带到表体
            checkTradeMode: currModel.checkTradeMode,                // 提单表头与表体监管方式不一致校验
            checkCurr: currModel.checkCurr,                          // 提单表体币制与备案币制不一致校验
            checkHsCode: currModel.checkHsCode,                      // 物料HsCode校验 不校验：0 校验1
            // 提单表头净毛重自动汇总表体
            autoNetWtI: currModel.autoNetWtI,
            autoNetWtDigitI: currModel.autoNetWtDigitI,
            autoNetWtE: currModel.autoNetWtE,
            autoNetWtDigitE: currModel.autoNetWtDigitE,
            autoGrossWtI: currModel.autoGrossWtI,
            autoGrossWtDigitI: currModel.autoGrossWtDigitI,
            autoGrossWtE: currModel.autoGrossWtE,
            autoGrossWtDigitE: currModel.autoGrossWtDigitE,
            // 新旧预录入单表头控制 0 旧 1 新
            decType: currModel.decType,
            // 是否自动计算表头体积  0否 1是
            autoVolumeI: currModel.autoVolumeI,
            autoVolumeE: currModel.autoVolumeE,
            // 提单表体单价与物料单价不一致校验
            checkPrice: currModel.checkPrice,
            priceLimite: currModel.priceLimite,
            // 提单表头与表体净重是否一致性校验
            checkNetWtTotal: currModel.checkNetWtTotal,
            netWtAllowErrVal: currModel.netWtAllowErrVal,
            // 提单表头与表体毛重是否一致性校验
            checkGrossWt: currModel.checkGrossWt,
            grossWtLimit: currModel.grossWtLimit,
            // 提单表体净重与物料净重不一致校验
            checkNetWt: currModel.checkNetWt,
            netWtLimit: currModel.netWtLimit,
            // 进口是否自动带入物料中心数据
            matDataI: currModel.matDataI,
            // 出口是否自动带入物料中心数据
            matDataE: currModel.matDataE,
            // 表体数据小数位数设置
            precisionsConfig: {
              qtyDigit: currModel.qtyDigit,
              qty1Digit: currModel.qty1Digit,
              qty2Digit: currModel.qty2Digit,
              netWtDigit: currModel.netWtDigit
            },
            checkAppr: currModel.checkAppr,                          // 内审通过才允许打印草单
            attachType: currModel.attachType,                        // 提单随附单据类型设置
            attachCheckType: currModel.attachCheckType,              // 随附单据类型上传按钮json串
            entryChannelType: currModel.entryChannelType,            // 报关单发送通道类型
            importType: currModel.importType,                        // 定制导入类型 1 光荣定制导入 2 埃玛克定制导入
            columnCount: currModel.columnCount,                      // (动态)进出口预录入单表头每行展示列数
            feeType: currModel.feeType,                              // 运费生成规则
            reExgVersion: currModel.reExgVersion,                    // 出口表体BOM版本设置规则提醒(日月新)
            checkBodyCountryE: currModel.checkBodyCountryE,          // 出口表头运抵国(地区)和表体目的国系统自动比对

            invoiceNoType: currModel.invoiceNoType,                  // 预录入单表头发票号取值
            contrNoSplit: currModel.contrNoSplit,                    // 预录入单表头发票号分隔符
            contrNoType: currModel.contrNoType,                      // 预录入单表头合同号取值
            invoiceNoSplit: currModel.invoiceNoSplit,                // 预录入单表头合同号分隔符
            bomCheck: currModel.bomCheck,                            // BOM完整性不通过是否折料

            headDecTotalDigitI: currModel.headDecTotalDigitI,        // 进口-总价(表头)
            headDecTotalDigitE: currModel.headDecTotalDigitE,        // 出口-总价(表头)
            headVolumeDigitI: currModel.headVolumeDigitI,            // 进口-体积(表头)
            headVolumeDigitE: currModel.headVolumeDigitE,            // 出口-体积(表头)
            decTotalDigitI: currModel.decTotalDigitI,                // 进口-总价(表体)
            decTotalDigitE: currModel.decTotalDigitE,                // 出口-总价(表体)
            volumeDigitI: currModel.volumeDigitI,                    // 进口-体积(表体)
            volumeDigitE: currModel.volumeDigitE,                    // 出口-体积(表体)
            otherRateCheck: currModel.otherRateCheck,                // 进口杂费预警校验
            autoSumClientTotalBE: currModel.autoSumClientTotalBE,    // 出口保税客供金额自动汇总
            autoSumMatTotalBE: currModel.autoSumMatTotalBE,          // 出口保税料费自动汇总
            autoSumProcessTotalBE: currModel.autoSumProcessTotalBE,  // 出口保税系统加工费自动汇总
            getManifestInfo: currModel.getManifestInfo,              // 是否获取舱单信息
            checkRcep: currModel.checkRcep,                          // 是否校验RCEP税率
            pageConfigure: currModel.pageConfigure,                  // 发票模板是否分页

            canEditBodyIBond: currModel.canEditBodyIBond,             // 进口保税表体 可编辑
            canEditBodyINobond: currModel.canEditBodyINobond,         // 进口非保税表体 可编辑
            canEditBodyEBond: currModel.canEditBodyEBond,             // 出口保税表体 可编辑
            canEditBodyENobond: currModel.canEditBodyENobond,         // 出口非保税表体 可编辑
            actualMainCost: currModel.actualMainCost,
            taxAttach: currModel.taxAttach,                           // 税单附件下载
            subscriptionEntry: currModel.subscriptionEntry,           // 订阅报关单回填
            autoDutyform: currModel.autoDutyform,                     // 税单自动采集

            checkIDecPrice: currModel.checkIDecPrice,                 // 保税进口单价与上一票校验
            checkEDecPrice: currModel.checkEDecPrice,                 // 保税出口单价与上一票校验
            checkISingleWeight: currModel.checkISingleWeight,         // 保税进口单重与上一票校验
            checkESingleWeight: currModel.checkESingleWeight,         // 保税出口单重与上一票校验
            checkIOriginCountry: currModel.checkIOriginCountry,       // 保税进口原产国与上一票校验
            invoiceSource: currModel.invoiceSource,       // 保税进口原产国与上一票校验
            unitCalculate: currModel.unitCalculate,       // 保税进口原产国与上一票校验
          })
        }, () => {
          me.buttons[0].disabled = true
          me.buttons[1].disabled = false
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        })
        me.$set(me, 'editStatus', editStatus.SHOW)
      },
      handleEdit() {
        let me = this,
          oldModel = Object.assign({}, me.detailConfig.model),
          contrNoSplit = me.detailConfig.model['contrNoSplit'],
          invoiceNoSplit = me.detailConfig.model['invoiceNoSplit']
        me.buttons[1].disabled = true
        me.buttons[0].disabled = false
        me.$set(me, 'editStatus', editStatus.EDIT)
        setTimeout(function() {
          me.$set(me.detailConfig, 'model', oldModel)
          me.$nextTick(() => {
            me.$set(me.detailConfig.model, 'contrNoSplit', contrNoSplit)
            me.$set(me.detailConfig.model, 'invoiceNoSplit', invoiceNoSplit)
          })
        }, 600)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .ivu-form-item-content {
    white-space: normal !important;
  }

  /deep/ .ivu-switch-inner {
    left: 25px;
  }

  /deep/ .ivu-switch-checked > .ivu-switch-inner {
    left: 10px;
  }

  .ivu-switch {
    width: 113px;
  }

  .ivu-switch-checked:after {
    left: 99px;
  }

  .showRightInput {
    display: grid;
    grid-template-columns: auto 80px;
  }
</style>
