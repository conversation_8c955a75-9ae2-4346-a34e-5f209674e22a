<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="160" class="dc-form-2"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'instructorSetting',
    mixins: [baseDetailConfig],
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editStatus: editStatus.EDIT
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        sid: '',
        savedModel: {},
        formName: 'frmData',
        editStatus: editStatus.SHOW,
        ajaxUrl: {
          insert: csAPI.firstPage.instructor.insert,
          update: csAPI.firstPage.instructor.update,
          delete: csAPI.firstPage.instructor.delete,
          exportUrl: csAPI.firstPage.instructor.exportUrl,
          selectAllPaged: csAPI.firstPage.instructor.selectAllPaged
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '编辑', type: 'warning', command: 'edit', click: this.handleEdit}
        ]
      }
    },
    watch: {
      frmDisplay: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      },
      editStatus: {
        handler: function (status) {
          if (status === editStatus.EDIT) {
            let me = this
            me.afterAjaxFailure()
            me.$set(me, 'savedModel', {})
          }
        }
      }
    },
    created: function () {
      let me = this
      me.buttons[0].disabled = true
      me.dataLoad()
    },
    computed: {
      frmDisplay() {
        return this.editStatus === editStatus.SHOW
      }
    },
    methods: {
      dataLoad() {
        let me = this
        me.$http.post(me.ajaxUrl.selectAllPaged, {}).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$nextTick(() => {
              let theModel = me.dataCopy(res.data.data[0])
              me.$set(me.detailConfig, 'model', theModel)
              me.$set(me, 'sid', theModel.sid)
            })
          }
        }).catch(() => {
        })
      },
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        } else {
          me.$nextTick(() => {
            if (me.searching === false) {
              me.$set(me, 'searching', true)
              me.$http.post(me.ajaxUrl.getEntryChannelType).then(res => {
                if (['0', '1', '2', '3'].includes(res.data.data)) {
                  me.$set(me.detailConfig.model, 'entryChannelType', res.data.data)
                }
              }).catch(() => {
              }).finally(() => {
                me.$set(me, 'searching', false)
              })
            }
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
        me.$set(me.detailConfig, 'defaultData', fieldsObject.model)
      },
      dataCopy(currModel) {
        let me = this,
          theModel = {
            sid: currModel.sid
          },
          extendFields = me.getExtendFields()
        Object.keys(me.detailConfig.model).forEach(fieldName => {
          if (currModel.hasOwnProperty(fieldName)) {
            theModel[fieldName] = currModel[fieldName]
          } else {
            theModel[fieldName] = me.detailConfig.model[fieldName]
          }
        })
        Object.keys(extendFields).forEach(fieldName => {
          if (currModel.hasOwnProperty(fieldName)) {
            theModel[fieldName] = currModel[fieldName]
          } else {
            theModel[fieldName] = me.detailConfig.model[fieldName]
          }
        })
        return theModel
      },
      getFields() {
        return [{
          isCard: true,
          key: '121212121212',
          title: '操作指导配置项',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-3'
        }, {
          type: 'switch',
          key: 'guideType',
          defaultValue: '0',
          title: '是否设置操作指导'
        }, {
          title: '指导人员',
          key: 'guidePerson',
          props: {
            maxlength: 50
          }
        }, {
          key: 'guidePhone',
          title: '指导人员联系电话',
          props: {
            maxlength: 50
          }
        }, {
          itemType: 'email',
          key: 'guideEmail',
          title: '指导人员邮箱',
          props: {
            maxlength: 100
          }
        }, {
          key: 'guideWeChat',
          title: '指导人员微信',
          props: {
            maxlength: 50
          }
        }]
      },
      afterAjaxFailure() {
        let me = this
        Object.keys(me.detailConfig.model).forEach(key => {
          if (me.savedModel.hasOwnProperty(key)) {
            me.$set(me.detailConfig.model, key, me.savedModel[key])
          }
        })
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'sid', me.sid)
        if (isNullOrEmpty(me.sid)) {
          me.$set(me.editConfig, 'editStatus', editStatus.ADD)
        } else {
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        }
        me.$set(me, 'savedModel', deepClone(me.detailConfig.model))
        me.doSave(res => {
          let currModel = deepClone(res.data.data),
            theModel = {
              sid: currModel.sid
            }
          me.$set(me, 'sid', currModel.sid)
          Object.keys(me.detailConfig.model).forEach(key => {
            if (currModel.hasOwnProperty(key)) {
              if (currModel[key] === null || typeof currModel[key] === 'undefined') {
                theModel[key] = me.detailConfig.defaultData[key]
              } else if (typeof currModel[key] === 'object' && Object.keys(currModel[key]).length === 0) {
                theModel[key] = me.detailConfig.defaultData[key]
              } else {
                theModel[key] = currModel[key]
              }
            }
          })
          me.$set(me.detailConfig, 'model', theModel)
          me.buttons[0].disabled = true
          me.buttons[1].disabled = false
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
          me.$set(me, 'editStatus', editStatus.SHOW)
        })
      },
      handleEdit() {
        let me = this
        me.$set(me, 'editStatus', editStatus.EDIT)
        me.buttons[0].disabled = false
        me.buttons[1].disabled = true
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .ivu-form-item-content {
    white-space: normal !important;
  }

  /deep/ .ivu-switch-inner {
    left: 25px;
  }

  /deep/ .ivu-switch-checked > .ivu-switch-inner {
    left: 10px;
  }

  .ivu-switch {
    width: 113px;
  }

  .ivu-switch-checked:after {
    left: 99px;
  }

  .showRightInput {
    display: grid;
    grid-template-columns: auto 80px;
  }
</style>
