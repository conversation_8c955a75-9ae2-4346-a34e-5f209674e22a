<template>
  <XdoModal width="360" mask v-model="show" title="备案号--单耗版本号"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm ref="frmData" class="xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="emsNo" label="备案号">
          <xdo-select v-model="frmData.emsNo" :options="emsNoData"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="exgVersion" label="单耗版本号">
          <XdoIInput type="text" v-model="frmData.exgVersion" :maxlength="8"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'emsNoBomConfigPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      emsNoData: {
        type: Array,
        default: () => ([])
      },
      currData: {
        type: Object,
        default: () => ({
          emsNo: '',
          exgVersion: ''
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {
          emsNo: '',
          exgVersion: ''
        },
        rulesHeader: {
          emsNo: [{required: true, message: '不能为空', trigger: 'blur'}],
          exgVersion: [{required: true, message: '不能为空', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, label: '确定', icon: 'dc-btn-save', click: this.handleConfirm},
          {...btnComm, label: '取消', icon: 'dc-btn-cancel', click: this.handleClose}
        ]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            if (me.$refs['frmData']) {
              me.$refs['frmData'].resetFields()
            }
            me.$nextTick(() => {
              me.$set(me.frmData, 'emsNo', me.currData.emsNo)
              me.$set(me.frmData, 'exgVersion', me.currData.exgVersion)
            })
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$refs['frmData'].validate().then(isValid => {
          if (isValid) {
            me.$emit('doAddConfig', me.frmData)
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
