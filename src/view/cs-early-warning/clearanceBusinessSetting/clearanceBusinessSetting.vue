<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="frmDisplay" labelWidth="220"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
      <XdoCard v-if="showBomConfig" :bordered="false" title="备案号--单耗版本号对应设置" class="ieLogisticsTrackingCard">
        <BomConfigSingleList :emsNoData="dynamicSource.emsNoData" :bomConfig="bomConfig"
                             @doSyncBomConfig="doSyncBomConfig"></BomConfigSingleList>
      </XdoCard>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty } from '@/libs/util'
  import { requiredFields } from './js/requiredFields'
  import { editStatus, earlyWarningManage } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import BomConfigSingleList from './bom-config/bom-config-single-list'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  let { createNamespacedHelpers } = majesty.Vuex
  let { mapMutations } = createNamespacedHelpers(namespace)

  export default {
    name: 'clearanceBusinessSetting',
    mixins: [baseDetailConfig, requiredFields],
    components: {
      BomConfigSingleList
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editStatus: editStatus.EDIT
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      let headRequiredFields = this.getHeadRequiredFields()
      let listRequiredFields = this.getListRequiredFields()
      return {
        sid: '',
        cmbSource: {
          emsNoData: [],
          emsNoMatData: []
        },
        emsNoBomConfig: '',
        checkExgVersion: '',
        formName: 'frmData',
        bomConfigEdit: false,
        editStatus: editStatus.SHOW,
        earlyWarningManage: earlyWarningManage,
        requiredFields: {
          headIBond: headRequiredFields.headIBond,
          listIBond: listRequiredFields.listIBond,
          headINonBond: headRequiredFields.headINonBond,
          listINonBond: listRequiredFields.listINonBond,
          headEBond: headRequiredFields.headEBond,
          listEBond: listRequiredFields.listEBond,
          headENonBond: headRequiredFields.headENonBond,
          listENonBond: listRequiredFields.listENonBond
        },
        ajaxUrl: {
          insert: csAPI.earlyWarning.manager.clearanceBusiness.insert,
          update: csAPI.earlyWarning.manager.clearanceBusiness.update,
          getEmsNoSelect: csAPI.csProductClassify.bonded.getEmsNoSelect,            //  通关的
          getZtythEmsListNo: csAPI.csProductClassify.bonded.getZtythEmsListNo,      //  备案的
          loadConfigUrl: csAPI.earlyWarning.manager.clearanceBusiness.loadConfigUrl
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '编辑', type: 'warning', command: 'edit', click: this.handleEdit}
        ]
      }
    },
    watch: {
      frmDisplay: {
        immediate: true,
        handler: function () {
          this.fieldsReset()
        }
      }
    },
    created: function () {
      let me = this
      me.dataLoad()
      me.buttons[0].disabled = true
      me.loadEmsNos()
    },
    computed: {
      frmDisplay() {
        return this.editStatus === editStatus.SHOW
      },
      showBomConfig() {
        return this.checkExgVersion === '2'
      },
      bomConfig() {
        return {
          editStatus: this.editStatus,
          configStr: this.emsNoBomConfig
        }
      }
    },
    methods: {
      ...mapMutations([
        'setClearanceBusinessSetting'
      ]),
      /**
       * 加载手册备案号
       */
      loadEmsNos() {
        let me = this
        // 通关的
        me.$http.post(me.ajaxUrl.getEmsNoSelect).then(res => {
          me.$set(me.cmbSource, 'emsNoData', res.data.data.map(item => {
            return {
              label: item.VALUE,
              value: item.VALUE
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'emsNoData', [])
        }).finally(() => {
          me.detailConfig.fields.filter(field => {
            return ['emsNo'].includes(field.key)
          }).forEach(item => {
            if (item.props) {
              item.props.options = me.cmbSource.emsNoData
            } else {
              item.props = {
                options: me.cmbSource.emsNoData
              }
            }
          })
        })
        // 备案的
        me.$http.post(me.ajaxUrl.getZtythEmsListNo).then(res => {
          me.$set(me.cmbSource, 'emsNoMatData', res.data.data.map(item => {
            return {
              label: item.emsNo,
              value: item.emsNo
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'emsNoMatData', [])
        }).finally(() => {
          me.detailConfig.fields.filter(field => {
            return ['emsNoMat'].includes(field.key)
          }).forEach(item => {
            if (item.props) {
              item.props.options = me.cmbSource.emsNoMatData
            } else {
              item.props = {
                options: me.cmbSource.emsNoMatData
              }
            }
          })
        })
      },
      /**
       * 设置扩展字段
       * @returns {{}}
       */
      getExtendFields() {
        return {}
      },
      fieldsReset() {
        let me = this,
          originalData = {},
          fields = me.getFields(),
          fieldsObject = me.fieldsAnalysis(fields)
        if (!isNullOrEmpty(me.detailConfig.model.sid)) {
          originalData = deepClone(me.detailConfig.model)
          me.$nextTick(() => {
            me.$set(me.detailConfig, 'model', me.dataCopy(originalData))
          })
        }
        me.$set(me.detailConfig, 'model', fieldsObject.model)
        me.$set(me.detailConfig, 'rules', fieldsObject.rules)
        me.$set(me.detailConfig, 'fields', fieldsObject.fields)
        me.$set(me.detailConfig, 'defaultData', fieldsObject.model)
      },
      dataLoad() {
        let me = this
        me.$http.post(me.ajaxUrl.loadConfigUrl).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$nextTick(() => {
              let theModel = me.dataCopy(res.data.data[0])
              me.$set(me.detailConfig, 'model', theModel)
              me.$set(me, 'sid', res.data.data[0].sid)
              me.$set(me, 'emsNoBomConfig', res.data.data[0].emsNoBomConfig)
              me.$set(me, 'checkExgVersion', res.data.data[0].checkExgVersion)
            })
          }
        }).catch(() => {
        })
      },
      dataCopy(currModel) {
        let me = this,
          theModel = {
            sid: currModel.sid
          }
        Object.keys(me.detailConfig.model).forEach(fieldName => {
          if (currModel.hasOwnProperty(fieldName)) {
            theModel[fieldName] = currModel[fieldName]
          } else {
            theModel[fieldName] = me.detailConfig.model[fieldName]
          }
        })
        return theModel
      },
      getFields() {
        return [{
          isCard: true,
          key: '121212121212',
          title: '报关默认值设置',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          type: 'select',
          key: 'emsNoMat',
          title: '备案默认手账册号',
          props: {
            options: this.dynamicSource.emsNoMatData,
            optionLabelRender: (item) => item.value
          },
        }, {
          key: 'emsNo',
          type: 'select',
          title: '通关默认手账册号',
          props: {
            options: this.dynamicSource.emsNoData,
            optionLabelRender: (item) => item.value
          },
        }, {
          type: 'pcode',
          key: 'tradeMode',
          title: '监管方式',
          props: {
            meta: 'TRADE'
          }
        }, {
          isCard: true,
          title: '必填项设置',
          key: '121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          isCard: true,
          key: '121212121212',
          type: 'empty_formItem',
          title: '进口保税必填项设置',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '表头',
          labelWidth: 60,
          key: 'requiredField',
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.headIBond
          }
        }, {
          title: '表体',
          labelWidth: 60,
          type: 'checkBoxGroup',
          key: 'listRequiredField',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.listIBond
          }
        }, {
          isCard: true,
          key: '121212121212',
          type: 'empty_formItem',
          title: '进口非保必填项设置',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '表头',
          labelWidth: 60,
          type: 'checkBoxGroup',
          key: 'reFieldINoBond',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.headINonBond
          }
        }, {
          title: '表体',
          labelWidth: 60,
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-4',
          key: 'listReFieldINoBond',
          props: {
            options: this.requiredFields.listINonBond
          }
        }, {
          isCard: true,
          key: '121212121212',
          type: 'empty_formItem',
          title: '出口保税必填项设置',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '表头',
          labelWidth: 60,
          key: 'reFieldE',
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.headEBond
          }
        }, {
          title: '表体',
          labelWidth: 60,
          key: 'listReFieldE',
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.listEBond
          }
        }, {
          isCard: true,
          key: '121212121212',
          type: 'empty_formItem',
          title: '出口非保必填项设置',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '表头',
          labelWidth: 60,
          type: 'checkBoxGroup',
          key: 'reFieldENoBond',
          itemClass: 'dc-merge-1-4',
          props: {
            options: this.requiredFields.headENonBond
          }
        }, {
          title: '表体',
          labelWidth: 60,
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-1-4',
          key: 'listReFieldENoBond',
          props: {
            options: this.requiredFields.listENonBond
          }
        }, {
          isCard: true,
          title: '其他',
          key: '222222',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          title: '出口体积重比例',
          labelWidth: 100,
          key: 'bulkWeightScale',
          type: 'xdoInput',
          props: {
            intDigits: 13,
            precision: 5
          },
        }]
      },
      doSyncBomConfig(bomConfig) {
        let me = this
        try {
          me.$set(me, 'emsNoBomConfig', JSON.stringify(bomConfig))
        } catch (e) {
          me.$set(me, 'emsNoBomConfig', JSON.stringify([]))
        }
      },
      onBomConfigOver(isEdit) {
        let me = this
        me.$set(me, 'bomConfigEdit', isEdit)
      },
      /**
       * 添加扩展参数
       */
      getExtendQueryString() {
        return '/0'
      },
      handleSave() {
        let me = this
        if (me.bomConfigEdit) {
          me.$Message.warning('请先完成对备案号--单耗版本号对应设置后再保存!')
          return
        }
        me.$set(me.detailConfig.model, 'sid', me.sid)
        me.$set(me.detailConfig.model, 'emsNoBomConfig', me.emsNoBomConfig)
        if (isNullOrEmpty(me.sid)) {
          me.$set(me.editConfig, 'editStatus', editStatus.ADD)
        } else {
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        }
        me.doSave(res => {
          let currModel = deepClone(res.data.data),
            theModel = {
              sid: currModel.sid
            }
          me.$set(me, 'sid', currModel.sid)
          Object.keys(me.detailConfig.model).forEach(key => {
            if (currModel.hasOwnProperty(key)) {
              if (currModel[key] === null || typeof currModel[key] === 'undefined') {
                theModel[key] = me.detailConfig.defaultData[key]
              } else if (typeof currModel[key] === 'object' && Object.keys(currModel[key]).length === 0) {
                theModel[key] = me.detailConfig.defaultData[key]
              } else {
                theModel[key] = currModel[key]
              }
            }
          })
          me.$set(me.detailConfig, 'model', theModel)
          me.setClearanceBusinessSetting({
            emsNoMat: currModel.emsNoMat,                            // 备案默认手账册号
            emsNo: currModel.emsNo,                                  // 通关默认手账册号
            tradeMode: currModel.tradeMode,                          // 监管方式
            // 表头(进口保税)
            reFieldI: currModel.requiredField,
            requiredField: currModel.requiredField,
            // 表体(进口保税)
            listReFieldI: currModel.listRequiredField,
            listRequiredField: currModel.listRequiredField,
            reFieldINoBond: currModel.reFieldINoBond,                // 表头(进口非保税)
            listReFieldINoBond: currModel.listReFieldINoBond,        // 表体(进口非保税)
            reFieldE: currModel.reFieldE,                            // 表头(出口保税)
            listReFieldE: currModel.listReFieldE,                    // 表体(出口保税)
            reFieldENoBond: currModel.reFieldENoBond,                // 表头(出口非保税)
            listReFieldENoBond: currModel.listReFieldENoBond         // 表体(出口非保税)
          })
        }, () => {
          me.buttons[0].disabled = true
          me.buttons[1].disabled = false
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
        })
        me.$set(me, 'editStatus', editStatus.SHOW)
      },
      handleEdit() {
        let me = this,
        oldModel = Object.assign({}, me.detailConfig.model),
        reFieldINoBond = me.detailConfig.model['reFieldINoBond'],
        reFieldENoBond = me.detailConfig.model['reFieldENoBond']
        me.$set(me, 'editStatus', editStatus.EDIT)
        me.buttons[0].disabled = false
        me.buttons[1].disabled = true
        setTimeout(function () {
          me.$set(me.detailConfig, 'model', oldModel)
          me.$nextTick(() => {
            me.$set(me.detailConfig.model, 'reFieldINoBond', reFieldINoBond)
            me.$set(me.detailConfig.model, 'reFieldENoBond', reFieldENoBond)
          })
        }, 300)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  /deep/ .ivu-form-item-content {
    white-space: normal !important;
  }

  /deep/ .ivu-switch-inner {
    left: 25px;
  }

  /deep/ .ivu-switch-checked > .ivu-switch-inner {
    left: 10px;
  }

  .ivu-switch {
    width: 113px;
  }

  .ivu-switch-checked:after {
    left: 99px;
  }

  .showRightInput {
    display: grid;
    grid-template-columns: auto 80px;
  }

  /deep/ .ieLogisticsTrackingCard .ivu-card-head {
    border-top: 1px solid #e8eaec;
  }

  /deep/ .ieLogisticsTrackingCard .ivu-card-body {
    padding: 0 !important;
  }
</style>
