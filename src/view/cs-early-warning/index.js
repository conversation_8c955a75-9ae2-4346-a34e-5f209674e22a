/***
 * 关务-预警管理-路由
 */
import { namespace } from '@/project'
import priceHeadList  from './price/priceHeadList'
import idCardHeadList  from './idCard/idCardHeadList'
import DecOverdueList from './dec-overdue/dec-overdue-list'
import netWeightHeadList  from './netWeight/netWeightHeadList'
import derateEquipHeadList  from './derateEquip/derateEquipHeadList'
import repairItemsHeadList  from './repairItems/repairItemsHeadList'
import importExportHeadList  from './importExport/importExportHeadList'
import manualMarginHeadList  from './manualMargin/manualMarginHeadList'
import produceSpillHeadList  from './produceSpill/produceSpillHeadList'
import FileManagementList from './file-management/file-management-list'
import insuranceBondHeadList  from './insuranceBond/insuranceBondHeadList'
import processExpireHeadList  from './processExpire/processExpireHeadList'
import PartitionConfigList from './partition-config/partition-config-list'
import BusinessSetting4OM from './clearanceBusinessSetting/businessSetting4OM'
import ImportsNoArrivedList from './imports-no-arrived/imports-no-arrived-list'
import ProxyUserSettingsList from './proxy-user-settings/proxy-user-settings-list'
import repairItemsIList from './repair-item-warning/repair-items/repair-items-i-list'
import repairItemsEList from './repair-item-warning/repair-items/repair-items-e-list'
import ImportBillsOverdueList from './import-bills-overdue/import-bills-overdue-list'
import WarringEmailConfigHeadList from './warringEmailConfig/WarringEmailConfigHeadList'
import ClearanceBusinessSetting from './clearanceBusinessSetting/clearanceBusinessSetting'
import tempEntryExitIList from './repair-item-warning/temp-entry-exit/temp-entry-exit-i-list'
import tempEntryExitEList from './repair-item-warning/temp-entry-exit/temp-entry-exit-e-list'
import notifyItemSettingsTabs from './clearanceBusinessSetting/notifi-item-settings/notify-item-settings-tabs'

export default [
  {
    path: '/' + namespace + '/earlyWarning/idCard',
    name: 'idCardHeadList',
    meta: {
      icon: 'ios-document',
      title: '证件卡类预警管理'
    },
    component: idCardHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/insuranceBond',
    name: 'insuranceBondHeadList',
    meta: {
      icon: 'ios-document',
      title: '保金保函预警管理'
    },
    component: insuranceBondHeadList
  },
  {
    path:'/' + namespace + '/earlyWarning/produceSpill',
    name:'produceSpillHeadList',
    meta: {
      icon: 'ios-document',
      title: '生产能力证明超金额预警'
    },
    component: produceSpillHeadList
  },
  {
    path:'/' + namespace + '/earlyWarning/processExpire',
    name:'processExpireHeadList',
    meta: {
      icon: 'ios-document',
      title: '外发加工到期预警'
    },
    component: processExpireHeadList
  },
  {
    path:'/' + namespace + '/earlyWarning/derateEquip',
    name:'derateEquipHeadList',
    meta: {
      icon: 'ios-document',
      title: '减免税设备解除监管预警'
    },
    component: derateEquipHeadList
  },
  {
    path:'/' + namespace + '/earlyWarning/importExport',
    name:'importExportHeadList',
    meta: {
      icon: 'ios-document',
      title: '暂时进出口预警'
    },
    component: importExportHeadList
  },
  {
    path:'/' + namespace + '/earlyWarning/repairItems',
    name:'repairItemsHeadList',
    meta: {
      icon: 'ios-document',
      title: '修理物品预警'
    },
    component: repairItemsHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/price',
    name: 'priceHeadList',
    meta: {
      icon: 'ios-document',
      title: '价格预警管理'
    },
    component: priceHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/netWeight',
    name: 'netWeightHeadList',
    meta: {
      icon: 'ios-document',
      title: '净重预警管理'
    },
    component: netWeightHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/manualMargin',
    name: 'manualMarginHeadList',
    meta: {
      icon: 'ios-document',
      title: '手册余量预警'
    },
    component: manualMarginHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/warringEmailConfig',
    name: 'warringEmailConfig',
    meta: {
      icon: 'ios-document',
      title: '预警通知人设置'
    },
    component: WarringEmailConfigHeadList
  },
  {
    path: '/' + namespace + '/earlyWarning/ClearanceBusinessSetting',
    name: 'clearanceBusinessSetting',
    meta: {
      icon: 'ios-document',
      title: '通关业务设置'
    },
    component: ClearanceBusinessSetting
  },
  {
    path: '/' + namespace + '/earlyWarning/businessSetting4OM',
    name: 'businessSetting4OM',
    meta: {
      icon: 'ios-document',
      title: '通关业务设置(运维)'
    },
    component: BusinessSetting4OM
  },
  {
    path: '/' + namespace + '/earlyWarning/importBillsOverdueList',
    name: 'importBillsOverdueList',
    meta: {
      icon: 'ios-document',
      title: '进口制单超期预警'
    },
    component: ImportBillsOverdueList
  },
  {
    path: '/' + namespace + '/earlyWarning/decOverdueList',
    name: 'decOverdueList',
    meta: {
      icon: 'ios-document',
      title: '报关超期预警'
    },
    component: DecOverdueList
  },
  {
    path: '/' + namespace + '/earlyWarning/fileManagementList',
    name: 'fileManagementList',
    meta: {
      icon: 'ios-document',
      title: '档案管理预警'
    },
    component: FileManagementList
  },
  {
    path: '/' + namespace + '/earlyWarning/importsNoArrivedList',
    name: 'importsNoArrivedList',
    meta: {
      icon: 'ios-document',
      title: '进口未到货预警'
    },
    component: ImportsNoArrivedList
  },
  {
    path: '/' + namespace + '/earlyWarning/proxyUserSettingsList',
    name: 'proxyUserSettingsList',
    meta: {
      icon: 'ios-document',
      title: '代理用户设置'
    },
    component: ProxyUserSettingsList
  },
  {
    path: '/' + namespace + '/earlyWarning/partitionConfigList',
    name: 'partitionConfigList',
    meta: {
      icon: 'ios-document',
      title: '关务分区配置'
    },
    component: PartitionConfigList
  },
  {
    path: '/' + namespace + '/earlyWarning/repairItemsIList',
    name: 'repairItemsIList',
    meta: {
      icon: 'ios-document',
      title: '修理物品进境预警'
    },
    component: repairItemsIList
  },
  {
    path: '/' + namespace + '/earlyWarning/repairItemsEList',
    name: 'repairItemsEList',
    meta: {
      icon: 'ios-document',
      title: '修理物品出境预警'
    },
    component: repairItemsEList
  },
  {
    path: '/' + namespace + '/earlyWarning/tempEntryExitIList',
    name: 'tempEntryExitIList',
    meta: {
      icon: 'ios-document',
      title: '暂时进境预警'
    },
    component: tempEntryExitIList
  },
  {
    path: '/' + namespace + '/earlyWarning/tempEntryExitEList',
    name: 'tempEntryExitEList',
    meta: {
      icon: 'ios-document',
      title: '暂时出境预警'
    },
    component: tempEntryExitEList
  },
  {
    path: '/' + namespace + '/earlyWarning/notifyItemSettingsTabs',
    name: 'notifyItemSettingsTabs',
    meta: {
      icon: 'ios-document',
      title: '预警通知设置'
    },
    component: notifyItemSettingsTabs
  }
]
