<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="copGNo" label="料号">
        <XdoIInput type="text" v-model="searchParam.copGNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="decPrice" label="基础单价">
        <xdo-input v-model="searchParam.decPrice" decimal int-length="11" precision="5" ></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="upperLimit" label="上限比例(%)">
        <xdo-input v-model="searchParam.upperLimit" number int-length="11" ></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="lowerLimit" label="下限比例(%)">
        <xdo-input v-model="searchParam.lowerLimit" number int-length="11" ></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="curr" label="币制">
        <xdo-select v-model="searchParam.curr" :asyncOptions="pcodeList" :meta="pcode.curr_outdated"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="insertUser" label="制单员">
        <XdoIInput type="text" v-model="searchParam.insertUser" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="setInsertDate"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { earlyWarningManage } from '../../cs-common'

  export default {
    name: 'priceHeadSearch',
    data() {
      return {
        searchParam: {
          copGNo: '',
          decPrice: 0,
          upperLimit: 0,
          lowerLimit: 0,
          curr: '',
          insertUser: '',
          insertTimeFrom: '',
          insertTimeTo: '',
        },
        earlyWarningManage: earlyWarningManage
      }
    },
    methods: {
      setInsertDate(values) {
        if (values instanceof Array && values.length === 2) {
          this.searchParam.insertTimeFrom = values[0]
          this.searchParam.insertTimeTo = values[1]
        } else {
          this.searchParam.insertTimeFrom = ''
          this.searchParam.insertTimeTo = ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
