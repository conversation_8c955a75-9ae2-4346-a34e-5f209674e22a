<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <Head ref="head" @onEditBack="editBack" :edit-config="editConfig" v-if="true"></Head>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" v-else>
      <TabPane name="headTab" label="表头">
        <Head ref="head" @onEditBack="editBack" :edit-config="editConfig"></Head>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" title="返回列表页面" @click="editBack" icon="ios-undo" size="small"></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Head from './priceHeadEdit'
  import { editStatus } from '../../cs-common'

  export default {
    name: 'priceTabs',
    components: {
      Head
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        headId: '',
        showBody: false,
        tabName: 'headTab',
        tabs: {
          headTab: true
        }
      }
    },
    mounted: function () {
      let me = this
      if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
        me.headId = ''
        me.showBody = false
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT) {
        me.headId = me.editConfig.headId
        me.showBody = true
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.SHOW) {
        me.headId = me.editConfig.headId
        me.showBody = true
      }
    },
    methods: {
      editBack(val) {
        if (val) {
          let me = this
          me.$emit('onEditBack', true)
        }
      }
    },
    watch: {
      tabName(value) {
        this.tabs[value] = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
