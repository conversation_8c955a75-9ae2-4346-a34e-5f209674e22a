import { getKeyValue } from '@/libs/util'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'serialNo'
  , 'copGNo'
  , 'dataStatus'
  , 'dataStatusName'
  , 'decPrice'
  , 'upperLimit'
  , 'lowerLimit'
  , 'note'
  , 'tradeCode'
  , 'insertTime'
  , 'userName'
  , 'updateUser'
  , 'updateTime'
]

const excelColumnsConfig = [
  'sid'
  , 'serialNo'
  , 'copGNo'
  , 'dataStatus'
  , 'dataStatusName'
  , 'decPrice'
  , 'upperLimit'
  , 'lowerLimit'
  , 'note'
  , 'tradeCode'
  , 'insertTime'
  , 'userName'
  , 'updateUser'
  , 'updateTime'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          width: 120,
          title: '操作',
          align: 'center',
          key: 'operation',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.handleEditOper()
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleRowDblClick(params.row)
                  }
                }
              }, '查看')
            ]);
          }
        },
        {
          title: '序号',
          minWidth: 50,
          align: 'center',
          key: 'serialNo'
        },
        {
          title: '料号',
          key: 'copGNo',
          minWidth: 100,
          tooltip: true,
          ellipsis: true,
          align: 'center'
        },
        {
          title: '基础单价',
          minWidth: 100,
          align: 'center',
          key: 'decPrice',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '币制',
          minWidth: 100,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
          },
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '上限比例(%)',
          minWidth: 100,
          align: 'center',
          key: 'upperLimit'
        },
        {
          title: '下限比例(%)',
          minWidth: 100,
          align: 'center',
          key: 'lowerLimit'
        },
        {
          title: '制单员',
          minWidth: 100,
          align: 'center',
          key: 'userName'
        },
        {
          title: '制单日期',
          minWidth: 120,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
          }
        },
        {
          title: '备注',
          minWidth: 150,
          align: 'center',
          key: 'note'
        }
      ]
    }
  }
}

const excelColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          key: 'serialNo',
          value: '序号'
        },
        {
          key: 'copGNo',
          value: '料号'
        },
        {
          key: 'decPrice',
          value: '基础单价'
        },
        {
          key: 'curr',
          value: '币制'
        },
        {
          key: 'upperLimit',
          value: '上限比例(%)'
        },
        {
          key: 'lowerLimit',
          value: '下限比例(%)'
        },
        {
          key: 'userName',
          value: '制单员'
        },
        {
          key: 'insertTime',
          value: '制单日期'
        },
        {
          key: 'note',
          value: '备注'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
