import { getKeyValue } from '@/libs/util'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'status'
  , 'statusName'
  , 'gmark'
  , 'gMarkName'
  , 'emsNo'
  , 'serialNo'
  , 'copGNo'
  , 'gname'
  , 'codeTS'
  , 'gmodel'
  , 'decPrice'
  , 'curr'
  , 'qty'
  , 'usedQty'
  , 'marginQty'
  , 'marginQtyRate'
]

const excelColumnsConfig = [
  'sid'
  , 'status'
  , 'statusName'
  , 'gmark'
  , 'gMarkName'
  , 'emsNo'
  , 'serialNo'
  , 'copGNo'
  , 'gname'
  , 'codeTS'
  , 'gmodel'
  , 'decPrice'
  , 'curr'
  , 'qty'
  , 'usedQty'
  , 'marginQty'
  , 'marginQtyRate'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '序号',
          minWidth: 80,
          align: 'center',
          type: 'index',
        },
        {
          title: '状态',
          minWidth: 80,
          align: 'center',
          key: 'status',
          render: (h, params) => {
            if (params.row.status === '0') {// 正常
              return h('span', params.row.statusName)
            } else if (params.row.status === '1') {// 预警
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF7F00'
                  }
                }, params.row.statusName)
              ])
            } else if (params.row.status === '2') {// 超期
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF0000'
                  }
                }, params.row.statusName)
              ])
            } else if (params.row.status === '3') {// 取消预警
              return h('div', [
                h('span', {
                  style: {
                    color: '#666'
                  }
                }, params.row.statusName)
              ])
            } else {
              return h('span', '--')
            }
          }
        },
        {
          width: 80,
          title: '物料属性',
          align: 'center',
          key: 'gMarkName',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '手册号',
          align: 'center',
          key: 'emsNo',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '备案序号',
          align: 'center',
          key: 'serialNo',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '备案料号',
          align: 'center',
          key: 'copGNo',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '中文品名',
          align: 'center',
          key: 'gname',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '商品编码',
          align: 'center',
          key: 'codeTS',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '申报要素',
          align: 'center',
          key: 'gmodel',
          tooltip: true,
          ellipsis: true
        },
        {
          width: 120,
          title: '备案单价',
          align: 'center',
          key: 'decPrice',
          tooltip: true,
          ellipsis: true
        },
        {
          title: '币制',
          minWidth: 100,
          align: 'center',
          key: 'curr',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.curr_outdated, params.row.curr), params.row.curr))
          },
          ellipsis: true,
          tooltip: true
        },
        {
          width: 120,
          title: '备案数量',
          align: 'center',
          key: 'qty',
          tooltip: true,
          ellipsis: true
        }, {
          width: 120,
          title: '已用数量',
          align: 'center',
          key: 'usedQty',
          tooltip: true,
          ellipsis: true
        }, {
          width: 120,
          title: '剩余数量',
          align: 'center',
          key: 'marginQty',
          tooltip: true,
          ellipsis: true
        }, {
          width: 120,
          title: '剩余数量比例',
          align: 'center',
          key: 'marginQtyRate',
          tooltip: true,
          ellipsis: true
        }
      ]
    }
  }
}

const excelColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          key: 'statusName',
          value: '状态'
        },
        {
          key: 'gMarkName',
          value: '物料属性'
        },
        {
          key: 'emsNo',
          value: '手册号'
        },
        {
          key: 'serialNo',
          value: '备案序号'
        },
        {
          key: 'copGNo',
          value: '备案料号'
        },
        {
          key: 'gname',
          value: '中文品名'
        },
        {
          key: 'codeTS',
          value: '商品编码'
        },
        {
          key: 'gmodel',
          value: '申报要素'
        },
        {
          key: 'decPrice',
          value: '备案单价'
        },
        {
          key: 'curr',
          value: '币制'
        },
        {
          key: 'qty',
          value: '备案数量'
        },
        {
          key: 'usedQty',
          value: '已用数量'
        },
        {
          key: 'marginQty',
          value: '剩余数量'
        },
        {
          key: 'marginQtyRate',
          value: '剩余数量比例'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
