<template>
  <XdoModal v-model="show" mask width="560" title="进口制单超期预警设置"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div style='color: red'>说明：维护到港日期，且到港日期在以下范围中，预录入单还未内审通过的单据，进行邮件预警</div>
    <div class="header" style="margin: 5px;">
      <span class="firstZhi">至</span><span class="secondZhi">至</span>
      <DynamicForm ref="frmData" labelWidth="60"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>

    <XdoCard>
      <h5 slot="title">预警通知地址(E-mail)</h5>
      <div>
        <XdoTable class="dc-table" ref="table" :columns="warringEmailsGridConfig.gridColumns" max-height="200"
                  no-data-text="请至“预警设置”菜单选择预警通知人"
                  :data="warringEmailsGridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      </div>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: right; margin: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNumber, isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { getWarringSetInfo } from '../../base/commonWarringSetMethod'

  export default {
    name: 'importBillsOverdueSetPop',
    mixins: [baseDetailConfig, getWarringSetInfo],
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        warringType: 'OVERDUE',
        ajaxUrl: {
          insert: csAPI.earlyWarning.manager.warringSet.insert,
          update: csAPI.earlyWarning.manager.warringSet.update
        },
        buttons: [
          {...btnComm, label: '关闭', type: 'default', icon: 'dc-btn-cancel', click: this.handleClose},
          {...btnComm, label: '设置', type: 'primary', icon: 'dc-btn-save', click: this.handleSave}
        ],
        validDatas: {
          sid0: '',
          warringMin0: null,
          warringMax0: null,

          sid1: '',
          warringMin1: null,
          warringMax1: null
        },
        warringEmailsGridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [{
            width: 140,
            title: '通知人名称',
            align: 'center',
            key: 'userName',
            tooltip: true,
            ellipsis: true
          }, {
            title: '邮箱地址',
            align: 'center',
            key: 'userEmail',
            tooltip: true,
            ellipsis: true
          }]
        }
      }
    },
    watch: {
      show: {
        handler: function (show) {
          let me = this
          if (show) {
            me.dataLoad()
          } else {
            if (me.$refs['frmData']) {
              me.$refs['frmData'].resetFields()
            }
          }
        }
      }
    },
    created: function () {
      this.initialData()
    },
    methods: {
      initialData() {
        let me = this
        //获取预警设置数据
        me.getWarringSetInfo(me.warringType, (data) => {
          Object.assign(me.warringSet, data)
        })
        //获取邮箱设置数据
        me.$http.post(`${csAPI.earlyWarning.manager.warringEmails.head.selectByWarringType}/${me.warringType}`, '').then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.warringEmailsGridConfig, 'data', res.data.data)
          }
        }).catch(() => {
        })
      },
      dataClear() {
        let me = this
        // 清空
        me.$set(me.validDatas, 'sid0', '')
        me.$set(me.validDatas, 'warringMin0', null)
        me.$set(me.validDatas, 'warringMax0', null)
        me.$set(me.validDatas, 'sid1', '')
        me.$set(me.validDatas, 'warringMin1', null)
        me.$set(me.validDatas, 'warringMax1', null)
      },
      dataLoad() {
        let me = this,
          data0 = me.editConfig.editData['0'] || {},
          data1 = me.editConfig.editData['1'] || {},
          sid0 = data0['sid'],
          sid1 = data1['sid']
        // 清空
        me.dataClear()

        // 普货
        if (!isNullOrEmpty(sid0)) {
          me.$set(me.validDatas, 'sid0', sid0)
        }
        if (isNumber(data0['warringMin'])) {
          me.$set(me.validDatas, 'warringMin0', data0['warringMin'])
        }
        if (isNumber(data0['warringMax'])) {
          me.$set(me.validDatas, 'warringMax0', data0['warringMax'])
        }
        // 危化品
        if (!isNullOrEmpty(sid1)) {
          me.$set(me.validDatas, 'sid1', sid1)
        }
        if (isNumber(data1['warringMin'])) {
          me.$set(me.validDatas, 'warringMin1', data1['warringMin'])
        }
        if (isNumber(data1['warringMax'])) {
          me.$set(me.validDatas, 'warringMax1', data1['warringMax'])
        }
      },
      afterModelLoaded(isAdd) {
        let me = this
        if (isAdd) {
          me.dataClear()
          // me.$nextTick(() => {
          //   if (me.$refs['fi_warringMin0']) {
          //     me.$refs['fi_warringMin0'].$set(me.$refs['fi_warringMin0'], 'validateState', '')
          //     me.$refs['fi_warringMin0'].$set(me.$refs['fi_warringMin0'], 'validateMessage', '')
          //   }
          //   if (me.$refs['warringMax0']) {
          //     me.$refs['warringMax0'].$set(me.$refs['warringMax0'], 'validateState', '')
          //     me.$refs['warringMax0'].$set(me.$refs['warringMax0'], 'validateMessage', '')
          //   }
          //   if (me.$refs['fi_warringMin1']) {
          //     me.$refs['fi_warringMin1'].$set(me.$refs['fi_warringMin1'], 'validateState', '')
          //     me.$refs['fi_warringMin1'].$set(me.$refs['fi_warringMin1'], 'validateMessage', '')
          //   }
          //   if (me.$refs['fi_warringMax1']) {
          //     me.$refs['fi_warringMax1'].$set(me.$refs['fi_warringMax1'], 'validateState', '')
          //     me.$refs['fi_warringMax1'].$set(me.$refs['fi_warringMax1'], 'validateMessage', '')
          //   }
          // })
        } else {
          me.$set(me.detailConfig.model, 'billType', '1')
          me.$set(me.detailConfig.model, 'warringMin0', me.validDatas.warringMin0)
          me.$set(me.detailConfig.model, 'warringMax0', me.validDatas.warringMax0)
          me.$set(me.detailConfig.model, 'warringMin1', me.validDatas.warringMin1)
          me.$set(me.detailConfig.model, 'warringMax1', me.validDatas.warringMax1)
        }
      },
      getFields() {
        return [{
          title: '普货',
          columnGap: 22,
          key: 'warring0',
          type: 'group_form_line',
          class: 'dc-merge-1-4',
          fields: [{
            title: '',
            labelWidth: 0,
            // required: true,
            type: 'xdoInput',
            key: 'warringMin0',
            itemClass: 'slotLeft',
            props: {
              intDigits: 3,
              precision: 0
            },
            slot: {
              prepend: '到港前',
              append: '天'
            }
          }, {
            title: '',
            labelWidth: 0,
            // required: true,
            type: 'xdoInput',
            key: 'warringMax0',
            itemClass: 'slotRight',
            props: {
              intDigits: 3,
              precision: 0
            },
            slot: {
              prepend: '到港后',
              append: '天 '
            }
          }]
        }, {
          columnGap: 22,
          title: '危化品',
          key: 'warring1',
          class: 'dc-merge-1-4',
          type: 'group_form_line',
          fields: [{
            title: '',
            labelWidth: 0,
            // required: true,
            type: 'xdoInput',
            key: 'warringMin1',
            itemClass: 'slotLeft',
            props: {
              intDigits: 3,
              precision: 0
            },
            slot: {
              prepend: '到港前',
              append: '天'
            }
          }, {
            title: '',
            labelWidth: 0,
            // required: true,
            type: 'xdoInput',
            key: 'warringMax1',
            itemClass: 'slotRight',
            props: {
              intDigits: 3,
              precision: 0
            },
            slot: {
              prepend: '到港后',
              append: '天'
            }
          }]
        }]
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      /**
       * 根据需提交的数据及回调函数执行保存操作
       * @param formData
       * @param callback
       * @param funFinally
       */
      doSaveByFormData(formData, callback) {
        let me = this
        if (typeof formData === "object" && Object.keys(formData).length > 0) {
          if (me.editConfig.editStatus === editStatus.ADD) {
            me.setBtnSaveLoading('save', true)
            let insertPromise0 = me.$http.post(me.ajaxUrl.insert, {
              businessId: '0',
              warringType: me.warringType,
              warringMin: formData.warringMin0,
              warringMax: formData.warringMax0
            })
            let insertPromise1 = me.$http.post(me.ajaxUrl.insert, {
              businessId: '1',
              warringType: me.warringType,
              warringMin: formData.warringMin1,
              warringMax: formData.warringMax1
            })
            Promise.all([insertPromise0, insertPromise1]).then(values => {
              if (typeof callback === 'function') {
                callback.call(me, values)
              }
              me.$Message.success(me.successMsg.insert + '!')
            }).finally(() => {
              me.setBtnSaveLoading('save', false)
            })
          } else if (me.editConfig.editStatus === editStatus.EDIT) {
            let updatePromise0 = me.$http.put(me.ajaxUrl.update + '/' + me.validDatas.sid0, {
              businessId: '0',
              warringType: me.warringType,
              warringMin: formData.warringMin0,
              warringMax: formData.warringMax0
            })
            let updatePromise1 = me.$http.put(me.ajaxUrl.update + '/' + me.validDatas.sid1, {
              businessId: '1',
              warringType: me.warringType,
              warringMin: formData.warringMin1,
              warringMax: formData.warringMax1
            })
            Promise.all([updatePromise0, updatePromise1]).then(values => {
              if (typeof callback === 'function') {
                callback.call(me, values)
              }
              me.$Message.success(me.successMsg.update + '!')
            }).finally(() => {
              me.setBtnSaveLoading('save', false)
            })
          }
        } else {
          console.error('未设置有效的form数据: formData')
        }
      },
      handleSave() {
        let me = this
        me.doSave(() => {
          me.$emit('onReload')
          me.handleClose()
        })
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
  }

  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  /deep/ .dc-form {
    grid-column-gap: 0;
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  .firstZhi {
    top: 60px;
    left: 303px;
    position: absolute;
  }

  .secondZhi {
    top: 88px;
    left: 303px;
    position: absolute;
  }
</style>
