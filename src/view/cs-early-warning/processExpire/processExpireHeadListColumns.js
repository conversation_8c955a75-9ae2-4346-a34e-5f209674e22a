const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'serialNo'
  , 'dataStatus'
  , 'dataStatusName'
  , 'documentNo'
  , 'documentName'
  , 'dateStart'
  , 'dateEnd'
  , 'note'
  , 'tradeCode'
  , 'insertTime'
  , 'userName'
  , 'updateUser'
  , 'updateTime'
]

const excelColumnsConfig = [
  'sid'
  , 'serialNo'
  , 'dataStatus'
  , 'dataStatusName'
  , 'documentNo'
  , 'documentName'
  , 'dateStart'
  , 'dateEnd'
  , 'note'
  , 'tradeCode'
  , 'insertTime'
  , 'userName'
  , 'updateUser'
  , 'updateTime'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.handleEditOper()
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleRowDblClick(params.row)
                  }
                }
              }, '查看')
            ]);
          },
          key: 'operation'
        },
        {
          title: '序号',
          minWidth: 50,
          align: 'center',
          key: 'serialNo',
        },
        {
          title: '状态',
          minWidth: 80,
          align: 'center',
          key: 'dataStatusName',
          render: (h, params) => {
            if (params.row.dataStatus === '0') {// 正常
              return h('span', params.row.dataStatusName)
            } else if (params.row.dataStatus === '1') {// 预警
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF7F00'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '2') {// 超期
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF0000'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '3') {// 注销
              return h('div', [
                h('span', {
                  style: {
                    color: '#ADADAD'
                  }
                }, params.row.dataStatusName)
              ])
            } else {
              return h('span', '--')
            }
          }
        },
        {
          title: '外发加工批次号',
          width: 130,
          key: 'documentNo',
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '加工单位',
          width: 160,
          key: 'documentName',
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '外发加工',
          align: 'center',
          children: [
            {
              title: '开始日期',
              minWidth: 120,
              align: 'center',
              key: 'dateStart',
              render: (h, params) => {
                return h('span', params.row.dateStart ? params.row.dateStart.slice(0, 10) : params.row.dateStart)
              }
            },
            {
              title: '结束日期',
              minWidth: 120,
              align: 'center',
              key: 'dateEnd',
              render: (h, params) => {
                return h('span', params.row.dateEnd ? params.row.dateEnd.slice(0, 10) : params.row.dateEnd)
              }
            }
          ]
        },
        {
          title: '制单员',
          minWidth: 100,
          align: 'center',
          key: 'userName'
        },
        {
          title: '制单日期',
          minWidth: 120,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
          }
        },
        {
          title: '备注',
          minWidth: 150,
          align: 'center',
          key: 'note'
        }
      ]
    }
  }
}

const excelColumns = {
  data() {
    return {
      totalExcelColumns: [
        {
          key: 'serialNo',
          value: '序号'
        },
        {
          key: 'dataStatusName',
          value: '状态'
        },
        {
          key: 'documentNo',
          value: '外发加工批次号'
        },
        {
          key: 'documentName',
          value: '加工单位'
        },
        {
          key: 'dateStart',
          value: '开始日期'
        },
        {
          key: 'dateEnd',
          value: '结束日期'
        },
        {
          key: 'userName',
          value: '制单员'
        },
        {
          key: 'insertTime',
          value: '制单日期'
        },
        {
          key: 'note',
          value: '备注'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns,
  excelColumns
}
