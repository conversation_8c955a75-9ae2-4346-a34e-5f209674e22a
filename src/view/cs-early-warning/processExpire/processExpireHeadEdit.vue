<template>
  <section>
    <XdoCard :bordered="false">
      <div class="xdo-enter-root" v-focus>
        <XdoForm ref="headerEditFrom" class="dc-form dc-form-3 xdo-enter-form" :model="headerData" :rules="rulesHeader" label-position="right" :label-width="120">
          <XdoFormItem prop="documentNo" label="外发加工批次号">
            <XdoIInput type="text" v-model="headerData.documentNo" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="documentName" label="加工单位">
            <XdoIInput type="text" v-model="headerData.documentName" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="insertUser" label="制单员">
            <XdoIInput type="text" v-model="headerData.insertUser" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="dateStart" label="外发加工开始日期">
            <XdoDatePicker type="date" :value="headerData.dateStart" style="width: 100%;" transfer :disabled="isLogout" @on-change="headerData.dateStart=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="dateEnd" label="外发加工结束日期">
            <XdoDatePicker type="date" :value="headerData.dateEnd" style="width: 100%;" transfer :disabled="isLogout" @on-change="headerData.dateEnd=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="insertTime" label="制单日期">
            <XdoDatePicker type="date" disabled v-model="headerData.insertTime" style="width: 100%;"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-1-4">
            <XdoIInput type="textarea" v-model="headerData.note" :autosize="{minRows: 2,maxRows: 5}" :disabled="isLogout"></XdoIInput>
          </XdoFormItem>
        </XdoForm>
      </div>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'

  export default {
    name: 'processExpireHeadEdit',
    props: {
      editConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        isLogout: false,
        editDisable: false,
        showDisable: false,
        headerData: {
          sid: '',
          serialNo: 0,
          dateStart: '',
          dateEnd: '',
          documentNo: '',
          documentName: '',
          insertUser: '',
          insertTime: '',
          note: ''
        },
        rulesHeader: {
          documentNo: [{required: true, message: '不能为空', trigger: 'blur'}],
          documentName: [{required: true, message: '不能为空', trigger: 'blur'}],
          dateStart: [{
            required: true, message: '不能为空', trigger: 'blur'
          }, {
            message: ' ', trigger: 'change', validator: this.dateRangeValid
          }],
          dateEnd: [{
            required: true, message: '不能为空', trigger: 'blur'
          }, {
            message: ' ', trigger: 'change', validator: this.dateRangeValid
          }]
        },
        buttons: [
          {...btnComm, label: '保存', click: this.handleSave},
          {...btnComm, label: '保存继续', click: this.handleSaveAndContinue},
          {...btnComm, label: '返回', click: this.handleBack}
        ]
      }
    },
    mounted: function () {
      let me = this
      if (me.editConfig && me.editConfig.editStatus === editStatus.ADD) {
        me.resetFormData()
        me.setDefaultSerialNo()
        me.$set(me, 'isLogout', false)
        me.editDisable = false
        me.showDisable = false
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.EDIT) {
        me.headerData = {...me.editConfig.editData}
        me.$set(me, 'isLogout', false)
        me.buttons[0].needed = true
        me.buttons[1].needed = true
        me.editDisable = true
        me.showDisable = false
      } else if (me.editConfig && me.editConfig.editStatus === editStatus.SHOW) {
        me.headerData = {...me.editConfig.editData}
        me.$set(me, 'isLogout', true)
        me.editDisable = true
        me.showDisable = true
        me.buttons[0].needed = false
        me.buttons[1].needed = false
      } else {
        console.error('缺失编辑信息!')
      }
    },
    methods: {
      dateRangeValid(rule, value, callback) {
        let me = this
        if (isNullOrEmpty(me.headerData.dateStart) === true || isNullOrEmpty(me.headerData.dateEnd) === true) {
          callback()
        } else {
          let startDate = new Date(me.headerData.dateStart)
          let entDate = new Date(me.headerData.dateEnd)
          if (startDate > entDate) {
            callback(new Error(' '));
          } else {
            callback()
          }
        }
      },
      /**
       * 数据保存
       * @param goon 是否继续
       */
      saveData(goon) {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.headerData)
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.$http.post(csAPI.earlyWarning.manager.expire.insert, data).then(() => {
                me.$Message.success('新增成功!')
                if (goon === true) {
                  me.resetFormData()
                  me.setDefaultSerialNo()
                  me.editConfig.editStatus = editStatus.ADD
                } else {
                  me.handleBack()
                }
              }).catch(() => {
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              const sid = me.headerData.sid
              me.$http.put(`${csAPI.earlyWarning.manager.expire.update}/${sid}`, data).then(() => {
                me.$Message.success('修改成功!')
                if (goon === true) {
                  me.resetFormData()
                  me.setDefaultSerialNo()
                  me.editConfig.editStatus = editStatus.ADD
                } else {
                  me.handleBack()
                }
              }).catch(() => {
              })
            }
          }
        })
      },
      /**
       * 保存表头数据
       */
      handleSave() {
        let me = this
        me.saveData(false)
      },
      /**
       * 保存并继续
       */
      handleSaveAndContinue() {
        let me = this
        me.saveData(true)
      },
      resetFormData() {
        let me = this
        if (me.$refs['headerEditFrom'] !== undefined) {
          me.$refs['headerEditFrom'].resetFields()
        }
        me.headerData.sid = ''
      },
      setDefaultSerialNo() {
        let me = this
        me.$http.post(csAPI.earlyWarning.manager.expire.getMaxNo).then((res) => {
          me.$set(me.headerData, 'serialNo', res.data.data)
        })
      },
      handleBack() {
        let me = this
        me.$emit('onEditBack', true)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
