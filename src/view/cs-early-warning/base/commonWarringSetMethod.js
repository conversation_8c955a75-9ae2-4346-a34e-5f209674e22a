import { csAPI } from '@/api'

export const commonWarringSetMethod = {
  data() {
    return {
      warringDays: 0,
      warringPercent: 0,
      warringSetShow: false,
      warringCloseShow: false
    }
  },
  created: function () {
    this.loadWarringData()
  },
  methods: {
    /**
     * 获取预警设置数据
     */
    loadWarringData() {
      let me = this
      me.$http.post(`${csAPI.earlyWarning.manager.warringSet.singleSelect}/${me.warringType}`, '').then(res => {
        me.warringPercent = res.data.data.warringPercent
        me.warringDays = res.data.data.warringDays
      }).catch(() => {
      })
    },
    handleSetWarring() {
      this.warringSetShow = true
    },
    warringSetClose() {
      this.warringSetShow = false
      this.loadWarringData()
      this.getList()
    },
    handleCloseWarring() {
      //  解除预警
      this.warringCloseShow = true
    },
    warringCloseModel() {
      this.warringCloseShow = false
      this.getList()
    }
  }
}

export const getWarringSetInfo = {
  methods: {
    //获取预警设置数据
    getWarringSetInfo(warringType, callback) {
      this.$http.post(`${csAPI.earlyWarning.manager.warringSet.singleSelect}/${warringType}`, '').then(res => {
        callback(res.data.data)
      }).catch(() => {
      })
    }
  }
}
