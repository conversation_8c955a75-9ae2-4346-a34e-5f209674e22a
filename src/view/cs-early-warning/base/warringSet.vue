<template>
  <section>
    <XdoModal value="true" title="预警设置" width="60%"
              :closable="false" :mask-closable="false" :footer-hide="true">
      <XdoCard :bordered="false">
        <div class="xdo-enter-root" v-focus>
          <XdoForm ref="headerEditFrom" :model="warringSet" :rules="rulesWarringSet" label-position="right">
            <XdoFormItem prop="warringPercent" label="预警条件" v-if="pageContent.pageType === pageType.Percent">
              <xdo-input v-model="warringSet.warringPercent" number int-length="4" precision="2" style="width: 200px;">
                <span slot="prepend">{{this.pageContent.prependText}}</span>
                <span slot="append">{{this.pageContent.appendText}}</span>
              </xdo-input>
            </XdoFormItem>
            <XdoFormItem prop="warringDays" label="预警条件" v-if="pageContent.pageType === pageType.Date">
              <xdo-input v-model="warringSet.warringDays" number int-length="6" style="width: 200px;">
                <span slot="prepend">{{this.pageContent.prependText}}</span>
                <span slot="append">{{this.pageContent.appendText}}</span>
              </xdo-input>
            </XdoFormItem>
            <XdoCard>
              <h5 slot="title">预警通知地址(E-mail)</h5>
              <div>
                <XdoTable class="dc-table" ref="table" max-height="200" no-data-text="请至“预警设置”菜单选择预警通知人"
                          :columns="warringEmailsGridConfig.gridColumns" :data="warringEmailsGridConfig.data" stripe border
                          @on-selection-change="handleSelectionChange"></XdoTable>
              </div>
            </XdoCard>
          </XdoForm>
        </div>
      </XdoCard>
      <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
        <template v-for="item in buttons">
          <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
        </template>
      </div>
    </XdoModal>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { dynamicHeight } from '@/common'
  import { isNullOrEmpty } from '@/libs/util'
  import { getWarringSetInfo } from  './commonWarringSetMethod'

  export default {
    name: 'warringSet',
    mixins: [getWarringSetInfo, dynamicHeight],
    props: {
      warringType: {
        type: String,
        default: () => ('')
      },
      selSids: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        isEdit: true,
        pageType: {
          Date: 1,            // 日期
          Percent: 2          // 百分比
        },
        searchData: {},
        // 页面内容
        pageContent: {
          pageType: '',       // DATE 日期 PERCENT 百分比
          appendText: '',     // 控件后内容
          prependText: ''     // 控件前内容
        },
        warringSet: {
          sid: '',
          warringDays: null,
          warringPercent: null
        },
        warringEmailsGridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [{
            width: 140,
            tooltip: true,
            ellipsis: true,
            align: 'center',
            key: 'userName',
            title: '通知人名称'
          }, {
            tooltip: true,
            ellipsis: true,
            align: 'center',
            key: 'userEmail',
            title: '邮箱地址'
          }]
        },
        rulesWarringSet: {
          warringDays: [{required: true, type: 'number', message: '不能为空', trigger: 'blur'}],
          warringPercent: [{required: true, type: 'number', message: '不能为空', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, type: 'primary', label: '保存', icon: 'dc-btn-save', click: this.handleSave},
          {...btnComm, type: 'primary', label: '关闭', icon: 'dc-btn-cancel', click: this.handleClose},
          {...btnComm, type: 'primary', label: '保存关闭', icon: 'dc-btn-save-1', click: this.handleSaveAndClose}
        ],
        actions: [
          {...btnComm, type: 'text', label: '新增', key: 'xdo-btn-add', click: this.handleAdd, icon: 'ios-add'},
          {...btnComm, type: 'text', label: '编辑', key: 'xdo-btn-edit', click: this.editInfor, icon: 'ios-create-outline'},
          {...btnComm, type: 'text', label: '删除', key: 'xdo-btn-delete', click: this.handleDelete, icon: 'ios-trash-outline'}
        ]
      }
    },
    created: function () {
      let me = this
      me.initialPageType()
      //初始化数据
      me.initialData()
    },
    methods: {
      initialPageType() {
        let me = this
        //初始化页面类型
        switch (me.warringType) {
          case 'WT'://净重预警管理
            break
          case 'PRICE'://价格预警管理
            break
          case 'SPILL'://生产能力证明超金预警
            Object.assign(me.pageContent, {pageType: me.pageType.Date, prependText: '有效期少于', appendText: '天'})
            break
          case 'REPAIR'://修理物品预警
          case 'EXPORT'://暂时进出口预警
          case 'EXPIRE'://外发加工到期预警
          case 'EQUIP'://减免税设备解除监管预警
          case 'CARD'://证件卡类预警管理
          case 'BAIL'://保金保函预警管理
            Object.assign(me.pageContent, {pageType: me.pageType.Date, prependText: '到期前', appendText: '天'})
            break
          case 'MARGIN'://手册余量
            Object.assign(me.pageContent, {pageType: me.pageType.Percent, prependText: '剩余数量比例低于', appendText: '%'})
            break
          case 'REPAIR_I':    // 修理物品进境预警
          case 'REPAIR_E':    // 修理物品出境预警
          case 'TEMPORARY_I': // 暂时进境预警
          case 'TEMPORARY_E': // 暂时出境预警
            Object.assign(me.pageContent, {pageType: me.pageType.Date, prependText: '到期前', appendText: '天'})
            break
        }
      },
      initialData() {
        let me = this
        //获取预警设置数据
        me.getWarringSetInfo(me.warringType, (data) => {
          Object.assign(me.warringSet, data)
        })
        //获取邮箱设置数据
        me.$http.post(`${csAPI.earlyWarning.manager.warringEmails.head.selectByWarringType}/${me.warringType}`, '').then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.warringEmailsGridConfig, "data", res.data.data)
          }
        }).catch(() => {
        })
      },
      /**
       * 数据保存
       */
      saveData(isClose) {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.warringSet)
            data.warringType = me.warringType
            let sid = me.warringSet.sid
            //修改
            if (!isNullOrEmpty(sid)) {
              if (me.warringType === 'CARD' && Array.isArray(me.selSids) && me.selSids.length > 0) {
                me.$http.post(`${csAPI.earlyWarning.manager.warringSet.update4Card}/${me.selSids}`, data).then(() => {
                  me.$Message.success('设置成功!')
                  if (isClose) {
                    me.handleClose()
                  }
                }).catch(() => {
                })
              } else {
                me.$http.put(`${csAPI.earlyWarning.manager.warringSet.update}/${sid}`, data).then(() => {
                  me.$Message.success('设置成功!')
                  if (isClose) {
                    me.handleClose()
                  }
                }).catch(() => {
                })
              }
            } else {
              me.$http.post(csAPI.earlyWarning.manager.warringSet.insert, data).then(() => {
                me.$Message.success('设置成功!')
                if (isClose) {
                  me.handleClose()
                }
              }).catch(() => {
              })
            }
          }
        })
      },
      /**
       * 选择赋值
       */
      handleSelectionChange(selectRows) {
        let me = this
        me.warringEmailsGridConfig.selectRows = selectRows
      },
      /**
       * 保存表头数据
       */
      handleSave() {
        let me = this
        me.saveData(false)
      },
      /**
       * 保存并关闭
       */
      handleSaveAndClose() {
        let me = this
        me.saveData(true)
      },
      handleClose() {
        let me = this
        me.$emit('onColse')
      },
      handleAdd() {
        let me = this
        me.isEdit = false
      },
      /**
       * 预警地址变更
       */
      editInfor() {
        let me = this
        if (me.warringEmailsGridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要编辑的数据!')
        } else if (me.warringEmailsGridConfig.selectRows.length > 1) {
          me.$Message.warning('一次仅能编辑一条数据!')
        } else {
          me.searchData = me.warringEmailsGridConfig.selectRows[0]
          me.isEdit = false
          me.warringEmailsGridConfig.selectRows = []
        }
      },
      /**
       * 删除
       */
      handleDelete() {
        let me = this
        if (me.warringEmailsGridConfig.selectRows.length > 0) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '删除',
            cancelText: '取消',
            content: '确认删除所选项吗',
            onOk: () => {
              const sids = me.warringEmailsGridConfig.selectRows.map(item => {
                return item['bodyId']
              })
              me.$http.delete(`${csAPI.earlyWarning.manager.warringEmails.body.delete}/${sids}`).then(() => {
                me.$Message.success('删除成功!')
                me.warringEmailsGridConfig.selectRows = []
                me.initialData()
              }).catch(() => {
              })
            }
          })
        } else {
          me.$Message.warning('未选择数据，请选择对应的数据进行操作!')
        }
      },
      /**
       * 新增关闭
       * @param e
       */
      addClose(e) {
        if (e) {
          let me = this
          me.isEdit = true
          me.searchData = {}
          me.initialData()
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .warringEmailList {
    overflow-y: auto;
    max-height: 160px;
  }

  .ivu-form-item {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
