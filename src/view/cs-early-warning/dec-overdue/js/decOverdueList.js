import { isNullOrEmpty } from '@/libs/util'
import { editStatus, entryManage } from '@/view/cs-common'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import DecOverdueSetPop from '../components/dec-overdue-set-pop'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

export const decOverdueList = {
  name: 'decOverdueList',
  mixins: [baseSearchConfig, baseListConfig],
  components: {
    DecOverdueSetPop
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      cmbSource: {
        iemark: entryManage.I_E_MARK
      },
      overdueSetShow: false,
      warringType: 'DECLARE_CUSTOMS',
      toolbarEventMap: {
        'export': this.handleDownload,
        'set-notice': this.handleWarningSet,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }, {
        key: 'iemark',
        type: 'select',
        title: '进出口标识'
      }]
    },
    getFields() {
      return [{
        width: 180,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        key: 'listNo',
        title: '清单编号'
      }, {
        width: 88,
        title: '制单日期',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }, {
        width: 180,
        tooltip: true,
        title: '制单员',
        key: 'insertUser'
      }, {
        width: 110,
        key: 'trafMode',
        title: '运输方式',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      }, {
        width: 250,
        tooltip: true,
        title: '境外收发货人',
        key: 'overseasShipperName'
      }, {
        width: 110,
        key: 'iemark',
        title: '进出口标识',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.dynamicSource.iemark)
        }
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 设置预警条件
     */
    handleWarningSet() {
      let me = this
      me.overdueSetShow = true
      me.$http.post(me.ajaxUrl.singleSelect + '/' + me.warringType).then(res => {
        if (res.data.data && !isNullOrEmpty(res.data.data.sid)) {
          me.$set(me.editConfig, 'editStatus', editStatus.EDIT)
          me.$set(me.editConfig, 'editData', JSON.parse(JSON.stringify(res.data.data)))
        } else {
          me.$set(me.editConfig, 'editStatus', editStatus.ADD)
          me.$set(me.editConfig, 'editData', {})
        }
      }).catch(() => {
      })
    },
    afterSetting() {
      let me = this
      me.$http.post(me.ajaxUrl.checkData).then(() => {
      }).catch(() => {
      }).finally(() => {
        me.handleSearchSubmit()
      })
    }
  }
}
