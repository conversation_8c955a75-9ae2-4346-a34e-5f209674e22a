<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          </xdo-toolbar>
          <span style="padding: 0; font-weight: bold; position: absolute; top: 4px; right: 20px;">当前默认预警天数: {{ warringDays }} 天</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <warringSet v-if="warringSetShow" @onColse="warringSetClose" :warringType="this.warringType" :sel-sids="selSids"></warringSet>
    <WarringConfigPop :show.sync="warringInfo.show" :old-data="warringInfo.selData" @doConfigSet="doConfigSet"></WarringConfigPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { repairItemsIList } from './js/repairItemsIList'

  export default {
    name: 'repairItemsIList',
    mixins: [repairItemsIList],
    data() {
      return {
        warringType: 'REPAIR_I',
        listConfig: {
          operationColumnShow: false,
          exportTitle: '修理物品进境预警'
        },
        ajaxUrl: {
          logout: csAPI.earlyWarning.repairItemI.logout,
          restore: csAPI.earlyWarning.repairItemI.restore,
          setting: csAPI.earlyWarning.repairItemI.setting,
          exportUrl: csAPI.earlyWarning.repairItemI.exportUrl,
          selectAllPaged: csAPI.earlyWarning.repairItemI.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
