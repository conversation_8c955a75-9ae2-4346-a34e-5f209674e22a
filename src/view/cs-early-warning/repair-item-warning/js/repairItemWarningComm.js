import WarringConfigPop from '../components/warring-config-pop'
import warringSet from '@/view/cs-early-warning/base/warringSet'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { commonWarringSetMethod } from '@/view/cs-early-warning/base/commonWarringSetMethod'

export const repairItemWarningComm = {
  name: 'repairItemWarningComm',
  mixins: [columnRender, baseSearchConfig, listDataProcessing, commonWarringSetMethod],
  components: {
    warringSet,
    WarringConfigPop
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        warningStatus: [{
          value: '0', label: '正常'
        }, {
          value: '1', label: '预警'
        }, {
          value: '2', label: '超期'
        }, {
          value: '3', label: '注销'
        }]
      },
      warringInfo: {
        show: false,
        idList: [],
        selData: {
          warningNote: '',
          warningDay: null
        }
      },
      toolbarEventMap: {
        'logout': this.handleLogout,
        'restore': this.handleRestore,
        'export': this.handleDownload,
        'set-notice': this.handleSetWarringSelected,
        'set-default-notice': this.handleSetWarring
      }
    }
  },
  computed: {
    selSids() {
      return this.getSelectedParams()
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, type: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        title: '状态',
        type: 'select',
        key: 'warningStatus'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'gname',
        title: '商品名称'
      }, {
        range: true,
        key: 'endDate',
        title: '要求复运日期'
      }, {
        range: true,
        key: 'completeDate',
        title: '实际复运日期'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 显示列表
     * @returns {{}[]}
     */
    getFields() {
      let me = this
      return [{
        width: 100,
        title: '状态',
        key: 'warningStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.warningStatus)
        })
      }, {
        width: 150,
        key: 'warningDay',
        title: '预警提前天数'
      }, {
        width: 160,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 150,
        key: 'gname',
        tooltip: true,
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'qty',
        title: '原数量'
      }, {
        width: 120,
        key: 'useQty',
        title: '已复运数量'
      }, {
        width: 120,
        key: 'remainQty',
        title: '剩余数量'
      }, {
        width: 110,
        key: 'endDate',
        title: '要求复运日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 110,
        key: 'completeDate',
        title: '实际复运日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '备注',
        key: 'warningNote'
      }, {
        width: 120,
        title: '制单员',
        key: 'userName'
      }, {
        width: 120,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 注销
     */
    handleLogout() {
      let me = this
      if (me.checkRowSelected('注销')) {
        me.$Modal.confirm({
          title: '提醒',
          loading: true,
          okText: '确认',
          cancelText: '取消',
          content: '确认要注销所选项吗',
          onOk: () => {
            let selSids = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.logout, selSids).then(() => {
              me.$Message.success('注销成功!')
            }).catch(() => {
            }).finally(() => {
              me.getList()
            })
            setTimeout(() => {
              me.$Modal.remove()
            }, 150)
          }
        })
      }
    },
    /**
     * 恢复
     */
    handleRestore() {
      let me = this
      if (me.checkRowSelected('恢复')) {
        let logoutRows = me.listConfig.selectRows.filter(item => {
          return item.warningStatus !== '3'
        })
        if (Array.isArray(logoutRows) && logoutRows.length > 0) {
          me.$Message.success('只有状态为注销的数据可执行恢复操作!')
        } else {
          let selSids = me.getSelectedParams()
          me.$http.post(me.ajaxUrl.restore, selSids).then(() => {
            me.$Message.success('恢复成功!')
          }).catch(() => {
          }).finally(() => {
            me.getList()
          })
        }
      }
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 下载
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 设置预警
     */
    handleSetWarringSelected() {
      let me = this
      if (me.checkRowSelected('设置预警')) {
        me.$set(me.warringInfo, 'idList', me.getSelectedParams())
        me.$set(me.warringInfo.selData, 'warningNote', '')
        me.$set(me.warringInfo.selData, 'warningDay', null)
        if (me.listConfig.selectRows.length === 1) {
          me.$set(me.warringInfo.selData, 'warningNote', me.listConfig.selectRows[0]['warningNote'])
          me.$set(me.warringInfo.selData, 'warningDay', me.listConfig.selectRows[0]['warningDay'])
        } else {
          let sameRows = me.listConfig.selectRows.filter(item => {
            return item.sid === me.listConfig.selectRows[0].sid
          })
          if (sameRows.length === me.listConfig.selectRows.length) {
            me.$set(me.warringInfo.selData, 'warningNote', me.listConfig.selectRows[0]['warningNote'])
            me.$set(me.warringInfo.selData, 'warningDay', me.listConfig.selectRows[0]['warningDay'])
          }
        }
        me.$set(me.warringInfo, 'show', true)
      }
    },
    /**
     * 设置预警
     * @param configData
     */
    doConfigSet(configData) {
      let me = this
      me.$http.post(me.ajaxUrl.setting, {
        ...configData,
        idList: me.warringInfo.idList
      }).then(() => {
        me.$Message.success('设置预警成功!')
        me.$set(me.warringInfo, 'idList', [])
        me.$set(me.warringInfo, 'show', false)
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    }
  }
}
