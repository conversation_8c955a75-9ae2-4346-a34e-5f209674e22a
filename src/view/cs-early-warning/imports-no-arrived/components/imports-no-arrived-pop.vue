<template>
  <XdoModal v-model="show" mask width="560" title="进口未到货预警设置"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div style='color: red'>说明：维护到港日期后，在以下日期范围内，到厂日期还未维护的单据，进行邮件预警</div>
    <div class="header" style="margin: 5px;">
      <DynamicForm ref="frmData" labelWidth="120"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <XdoCard>
      <h5 slot="title">预警通知地址(E-mail)</h5>
      <div>
        <XdoTable class="dc-table" ref="table" :columns="warringEmailsGridConfig.gridColumns"  max-height="200"
                  no-data-text="请至“预警设置”菜单选择预警通知人"
                  :data="warringEmailsGridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      </div>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: right; margin: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { getWarringSetInfo } from '../../base/commonWarringSetMethod'

  export default {
    name: 'importsNoArrivedPop',
    mixins: [baseDetailConfig, getWarringSetInfo],
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        warringType: 'ARRIVAL_OVERDUE',
        ajaxUrl: {
          insert: csAPI.earlyWarning.manager.warringSet.insert,
          update: csAPI.earlyWarning.manager.warringSet.update
        },
        buttons: [
          {...btnComm, label: '关闭', type: 'default', icon: 'dc-btn-cancel', click: this.handleClose},
          {...btnComm, label: '设置', type: 'primary', icon: 'dc-btn-save', click: this.handleSave}
        ],
        warringEmailsGridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [{
            width: 140,
            tooltip: true,
            ellipsis: true,
            align: 'center',
            key: 'userName',
            title: '通知人名称'
          }, {
            tooltip: true,
            ellipsis: true,
            align: 'center',
            title: '邮箱地址',
            key: 'userEmail'
          }]
        }
      }
    },
    created: function () {
      this.initialData()
    },
    watch: {
      show: {
        handler: function (show) {
          let me = this
          if (!show) {
            if (me.$refs['frmData']) {
              me.$refs['frmData'].resetFields()
            }
          }
        }
      }
    },
    methods: {
      initialData() {
        let me = this
        //获取预警设置数据
        me.getWarringSetInfo(me.warringType, (data) => {
          Object.assign(me.warringSet, data)
        })
        //获取邮箱设置数据
        me.$http.post(`${csAPI.earlyWarning.manager.warringEmails.head.selectByWarringType}/ARRIVAL`, '').then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.warringEmailsGridConfig, 'data', res.data.data)
          }
        }).catch(() => {
        })
      },
      getFields() {
        return [{
          required: true,
          title: '预警天数',
          type: 'xdoInput',
          key: 'warringDays',
          props: {
            intDigits: 3,
            precision: 0
          },
          slot: {
            append: '天'
          },
          itemClass: 'dc-merge-1-4'
        }]
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'warringType', me.warringType)
        me.doSave(() => {
          me.$emit('onReload')
          me.handleClose()
        })
      }
    }
  }
</script>

<style scoped>
  /deep/ .ivu-modal-body {
    padding: 1px !important;
  }

  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  /deep/ .dc-form {
    grid-column-gap: 0;
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }

  .firstZhi {
    top: 40px;
    left: 303px;
    position: absolute;
  }
</style>
