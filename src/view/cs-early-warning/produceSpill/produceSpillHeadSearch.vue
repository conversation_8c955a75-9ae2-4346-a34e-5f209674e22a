<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <dc-dateRange label="生产能力证明开始日期" @onDateRangeChanged="setProduceProveBeginDate"></dc-dateRange>
      <dc-dateRange label="生产能力证明结束日期" @onDateRangeChanged="setProduceProveEndDate"></dc-dateRange>
      <XdoFormItem prop="insertUser" label="制单员">
        <XdoIInput type="text" v-model="searchParam.insertUser" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="setInsertDate"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { earlyWarningManage } from '../../cs-common'

  export default {
    name: 'produceSpillHeadSearch',
    data() {
      return {
        searchParam: {
          dateStartFrom: '',    // 有效期
          dateStartTo: '',
          dateEndFrom: '',
          dateEndTo: '',
          //totalPrice: 0,
          insertUser: '',
          insertTimeFrom: '',
          insertTimeTo: ''
        },
        earlyWarningManage: earlyWarningManage
      }
    },
    methods: {
      setProduceProveBeginDate(values) {
        if (values instanceof Array && values.length === 2) {
          this.searchParam.dateStartFrom = values[0]
          this.searchParam.dateStartTo = values[1]
        } else {
          this.searchParam.dateStartFrom = ''
          this.searchParam.dateStartTo = ''
        }
      },
      setProduceProveEndDate(values) {
        if (values instanceof Array && values.length === 2) {
          this.searchParam.dateEndFrom = values[0]
          this.searchParam.dateEndTo = values[1]
        } else {
          this.searchParam.dateEndFrom = ''
          this.searchParam.dateEndTo = ''
        }
      },
      setInsertDate(values) {
        if (values instanceof Array && values.length === 2) {
          this.searchParam.insertTimeFrom = values[0]
          this.searchParam.insertTimeTo = values[1]
        } else {
          this.searchParam.insertTimeFrom = ''
          this.searchParam.insertTimeTo = ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
