const columnsConfig = [
  'selection'
  , 'operation'
  , 'sid'
  , 'serialNo'
  , 'dataStatus'
  , 'dataStatusName'
  , 'documentName'
  , 'documentNo'
  , 'dateStart'
  , 'dateEnd'
  , 'authority'
  , 'cardHolder'
  , 'chargePerson'
  , 'insertTime'
  , 'note'
  , 'tradeCode'
  , 'updateUser'
  , 'updateTime'
  , 'warringDays'
]

const excelColumnsConfig = [
  'sid'
  , 'serialNo'
  , 'dataStatus'
  , 'documentName'
  , 'documentNo'
  , 'dateStart'
  , 'dateEnd'
  , 'authority'
  , 'cardHolder'
  , 'chargePerson'
  , 'insertTime'
  , 'note'
  , 'tradeCode'
  , 'updateUser'
  , 'updateTime'
  , 'warringDays'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          key: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.gridConfig.selectData = params.row
                    this.handleEditOper()
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleRowDblClick(params.row)
                  },
                },
              }, '查看')
            ]);
          },
          key: 'operation'
        },
        {
          title: '序号',
          width: 50,
          align: 'center',
          key: 'serialNo',
        },
        {
          title: '状态',
          width: 80,
          align: 'center',
          key: 'dataStatusName',
          render: (h, params) => {
            if (params.row.dataStatus === '0') {// 正常
              return h('span', params.row.dataStatusName)
            } else if (params.row.dataStatus === '1') {// 预警
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF7F00'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '2') {// 超期
              return h('div', [
                h('span', {
                  style: {
                    color: '#FF0000'
                  }
                }, params.row.dataStatusName)
              ])
            } else if (params.row.dataStatus === '3') {// 注销
              return h('div', [
                h('span', {
                  style: {
                    color: '#ADADAD'
                  }
                }, params.row.dataStatusName)
              ])
            } else {
              return h('span', '--')
            }
          }
        },
        {
          title: '证件卡类名称',
          width: 150,
          align: 'left',
          key: 'documentName',
        },
        {
          title: '证件编号',
          width: 150,
          align: 'left',
          key: 'documentNo',
        },
        {
          title: '有效期起',
          width: 120,
          align: 'center',
          key: 'dateStart',
          render: (h, params) => {
            return h('span', params.row.dateStart ? params.row.dateStart.slice(0, 10) : params.row.dateStart)
          }
        },
        {
          title: '有效期止',
          width: 120,
          align: 'center',
          key: 'dateEnd',
          render: (h, params) => {
            return h('span', params.row.dateEnd ? params.row.dateEnd.slice(0, 10) : params.row.dateEnd)
          }
        },
        {
          title: '预警天数',
          width: 100,
          align: 'left',
          key: 'warringDays',
        },
        {
          title: '发证机关',
          width: 100,
          align: 'left',
          key: 'authority',
        },
        {
          title: '持卡人',
          width: 100,
          align: 'left',
          key: 'cardHolder',
        },
        {
          title: '负责人',
          width: 100,
          align: 'left',
          key: 'chargePerson',
        },
        {
          title: '制单日期',
          width: 120,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return h('span', params.row.insertTime ? params.row.insertTime.slice(0, 10) : params.row.insertTime)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
