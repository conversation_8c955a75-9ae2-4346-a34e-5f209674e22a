<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <idCardHeadSearch ref="headSearch"></idCardHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
          <span style="padding: 0; font-weight: bold; position: absolute; top: 4px; right: 20px;">当前预警天数: {{ warringDays }} 天</span>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <idCardTabs v-if="!showList" ref="idCardTabs" @onEditBack="editBack" :editConfig="editConfig"></idCardTabs>
    <warringSet v-if="warringSetShow" @onColse="warringSetClose" :warringType="this.warringType" :sel-sids="selSids"></warringSet>
    <ImportPage :importShow.sync="modelImportShow" :importKey="importConfig.importKey" :importConfig="importConfig.Config"
                @onImportSuccess="afterImport"></ImportPage>
    <DialogExtract :show.sync='isShowExtract' :head-id='editConfig.editData.sid' :edit-config='editConfig'  @onConfirm='handleDialogExtractConfirm'></DialogExtract>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import ImportPage from 'xdo-import'
  import idCardTabs from './idCardTabs'
  import warringSet from '../base/warringSet'
  import idCardHeadSearch from './idCardHeadSearch'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { commonWarringSetMethod } from  '../base/commonWarringSetMethod'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { columnsConfig, excelColumnsConfig, columns } from './idCardHeadListColumns'
  import DialogExtract from '@/view/cs-early-warning/idCard/DialogExtract'

  export default {
    name: 'idCardHeadList',
    components: {
      DialogExtract,
      ImportPage,
      idCardTabs,
      warringSet,
      idCardHeadSearch
    },
    mixins: [pms, commList, columns, commonWarringSetMethod, dynamicImport],
    data() {
      let commImportConfig = this.getCommImportConfig('WARRING_CARD', {
        tradeName: ''
      })
      return {
        isShowExtract:false,
        actions: [],
        warringType: 'CARD',
        modelImportShow: false,
        importConfig: {
          importKey: 'warringCard',
          Config: commImportConfig
        },
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'import': this.handleImport,
          'logout': this.handleLogout,
          'delete': this.handleDelete,
          'set-notice': this.handleSetWarring,
          'extract': this.handleExtract,//数据提取
        },
        gridConfig: {
          exportTitle: '证件卡类预警-表头'
        },
        ajaxUrl: {
          delete: csAPI.earlyWarning.manager.card.delete,
          exportUrl: csAPI.earlyWarning.manager.card.export,
          selectAllPaged: csAPI.earlyWarning.manager.card.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.gridConfig.exportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
      me.loadFunctions().then()
    },
    computed: {
      selSids() {
        return this.getSelectedParams()
      }
    },
    methods: {
      handleExtract(){
        this.isShowExtract = true
      },
      handleDialogExtractConfirm() {
        this.handleSearchSubmit()
      },
      /**
       * 导入
       */
      handleImport() {
        this.modelImportShow = true
      },
      afterImport() {
        this.modelImportShow = false
        this.getList()
      },
      handleLogout() {
        let me = this
        if (me.checkRowSelected('注销', true)) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '注销',
            cancelText: '取消',
            content: '确认要注销所选项吗',
            onOk: () => {
              let data = me.gridConfig.selectRows.map(item => {
                return {
                  sid: item.sid
                }
              })
              me.$http.post(csAPI.earlyWarning.manager.card.logout, data).then(() => {
                me.$Message.success('注销成功!')
                me.handleSearchSubmit()
              }).catch(() => {
                me.$Message.success('注销失败!')
              })
            }
          })
        }
      },
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      handleDownload() {
        let me = this
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
