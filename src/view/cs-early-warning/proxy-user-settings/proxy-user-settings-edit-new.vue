<template>
  <section>
    <XdoCard :bordered="false" title="代理用户设置" class="xdo-enter-root" v-focus>
      <XdoForm ref="dataFrom" class="dc-form-4 xdo-enter-form" :model="frmData" :rules="rules" label-position="right" :label-width="120">
        <DcFormItem prop="userNo" label="用户登录名" class="dc-merge-1-3">
          <XdoIInput type="text" v-model="frmData.userNo" :disabled="showDisable" :maxlength="50"></XdoIInput>
        </DcFormItem>
        <DcFormItem class="dc-merge-3-5">
        </DcFormItem>
        <DcFormItem label="报关行" class="dc-merge-1-5">
          <Row>
            <Col span="2" style="text-align: center;">
              <Checkbox v-model="frmData.declareIsEnable" :true-value="cbxValue.trueValue" :false-value="cbxValue.falseValue" :disabled="showDisable"></Checkbox>
            </Col>
            <Col span="9">
              <xdo-select v-model="frmData.declareAlias" :disabled="declareDisable"
                          :options="cmbSource.declareAlias" :optionLabelRender="pcodeRender"></xdo-select>
            </Col>
            <Col span="6"><XdoIInput type="text" v-model="frmData.declareCode" disabled></XdoIInput></Col>
          </Row>
          <Row>
            <Col span="24" style="text-align: left; padding: 8px 0 12px 0;">
              <CheckboxGroup v-model="declareAuthArray">
                <Checkbox label="1" :disabled="declareDisable"><span>提单</span></Checkbox>
                <Checkbox label="2" :disabled="declareDisable"><span>报关追踪</span></Checkbox>
                <Checkbox label="3" :disabled="declareDisable"><span>报关单</span></Checkbox>
                <Checkbox label="4" :disabled="declareDisable"><span>费用维护</span></Checkbox>
                <Checkbox label="5" :disabled="declareDisable"><span>发票箱单</span></Checkbox>
                <Checkbox label="6" :disabled="declareDisable"><span>物流追踪</span></Checkbox>
                <Checkbox label="7" :disabled="declareDisable"><span>免税申请表</span></Checkbox>
                <Checkbox label="9" :disabled="declareDisable"><span>订单批次</span></Checkbox>
              </CheckboxGroup>
            </Col>
          </Row>
        </DcFormItem>
        <DcFormItem label="货代代理" class="dc-merge-1-5">
          <Row>
            <Col span="2" style="text-align: center;">
              <Checkbox v-model="frmData.forwardIsEnable" :true-value="cbxValue.trueValue" :false-value="cbxValue.falseValue" :disabled="showDisable"></Checkbox>
            </Col>
            <Col span="9">
              <xdo-select v-model="frmData.forwardAlias" :disabled="forwardDisable"
                          :options="cmbSource.forwardAlias" :optionLabelRender="pcodeRender"></xdo-select>
            </Col>
            <Col span="6"><XdoIInput type="text" v-model="frmData.forwardCode" disabled></XdoIInput></Col>
          </Row>
          <Row>
            <Col span="24" style="text-align: left; padding: 8px 0 12px 0;">
              <CheckboxGroup v-model="forwardAliasArray">
                <Checkbox label="1" :disabled="forwardDisable"><span>提单</span></Checkbox>
                <Checkbox label="2" :disabled="forwardDisable"><span>报关追踪</span></Checkbox>
                <Checkbox label="3" :disabled="forwardDisable"><span>报关单</span></Checkbox>
                <Checkbox label="4" :disabled="forwardDisable"><span>费用维护</span></Checkbox>
                <Checkbox label="5" :disabled="forwardDisable"><span>发票箱单</span></Checkbox>
                <Checkbox label="6" :disabled="forwardDisable"><span>物流追踪</span></Checkbox>
                <Checkbox label="8" :disabled="forwardDisable"><span>采购订单</span></Checkbox>
                <Checkbox label="9" :disabled="forwardDisable"><span>订单批次</span></Checkbox>
              </CheckboxGroup>
            </Col>
          </Row>
        </DcFormItem>


        <DcFormItem label="供应商" class="dc-merge-1-5">
          <Row>
            <Col span="2" style="text-align: center;">
              <Checkbox v-model="frmData.prdIsEnable" :true-value="cbxValue.trueValue" :false-value="cbxValue.falseValue" :disabled="showDisable"></Checkbox>
            </Col>
            <Col span="9">
              <xdo-select v-model="frmData.prdAlias" :disabled="prdDisable"
                          :options="cmbSource.prdAlias" :optionLabelRender="pcodeRender"></xdo-select>
            </Col>
            <Col span="6"><XdoIInput type="text" v-model="frmData.prdCode" disabled></XdoIInput></Col>
          </Row>
          <Row>
            <Col span="24" style="text-align: left; padding: 8px 0 12px 0;">
              <CheckboxGroup v-model="prdAliasArray">

                <Checkbox label="4" :disabled="prdDisable"><span>费用维护</span></Checkbox>
                <Checkbox label="8" :disabled="prdDisable"><span>采购订单</span></Checkbox>
                <Checkbox label="9" :disabled="prdDisable"><span>订单批次</span></Checkbox>

              </CheckboxGroup>
            </Col>
          </Row>
        </DcFormItem>




        <DcFormItem label="预报单录入" class="dc-merge-1-5">
          <Row>
            <Col span="2" style="text-align: center;">
              <Checkbox v-model="frmData.preDeclareIsEnable" :true-value="cbxValue.trueValue" :false-value="cbxValue.falseValue" :disabled="showDisable"></Checkbox>
            </Col>
            <Col span="9">
              <xdo-select v-model="frmData.preDeclareAlias" :disabled="preDeclareDisable"
                          :options="cmbSource.forwardAlias" :optionLabelRender="pcodeRender"></xdo-select>
            </Col>
            <Col span="6"><XdoIInput type="text" v-model="frmData.preDeclareCode" disabled></XdoIInput></Col>
          </Row>

        </DcFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'proxyUserSettingsEditNew',
    props: {
      /**
       * 传入的编辑信息
       */
      editConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      let btnComm = {
          needed: true,
          loading: false,
          disabled: false
        },
        defaultData = this.getDefaultData()
      return {
        frmData: defaultData,
        cmbSource: {
          declareAlias: [],
          forwardAlias: [],
          prdAlias: []
        },
        declareAuthArray: [],
        forwardAliasArray: [],
        prdAliasArray: [],
        cbxValue: {
          trueValue: '1',
          falseValue: '0'
        },
        rules: {
          userNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        ajaxUrl: {
          insert: csAPI.customsClearanceRiskSetting.proxyUserSettings.insert,
          update: csAPI.customsClearanceRiskSetting.proxyUserSettings.update,
          getProxy: csAPI.customsClearanceRiskSetting.proxyUserSettings.getProxy
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      },
      editConfig: {
        deep: true,
        immediate: true,
        handler: function (config) {
          let me = this
          me.$nextTick(() => {
            me.$set(me, 'declareAuthArray', [])
            me.$set(me, 'forwardAliasArray', [])
            me.$set(me, 'prdAliasArray', [])
            if (config.editStatus === editStatus.ADD) {
              me.$nextTick(() => {
                me.$set(me, 'frmData', me.getDefaultData())
              })
            } else {
              if (config.editData !== null && typeof config.editData === 'object') {
                let copyData = deepClone(config.editData)
                me.$nextTick(() => {
                  Object.keys(copyData).forEach(key => {
                    me.$set(me.frmData, key, copyData[key])
                  })
                  if (!isNullOrEmpty(copyData['declareAuth'])) {
                    me.$set(me, 'declareAuthArray', copyData['declareAuth'].split(','))
                  }
                  if (!isNullOrEmpty(copyData['forwardAuth'])) {
                    me.$set(me, 'forwardAliasArray', copyData['forwardAuth'].split(','))
                  }
                  if (!isNullOrEmpty(copyData['prdAuth'])) {
                    me.$set(me, 'prdAliasArray', copyData['prdAuth'].split(','))
                  }
                  me.$nextTick(() => {
                    me.$set(me.frmData, 'declareCode', copyData['declareCode'])
                    me.$set(me.frmData, 'forwardCode', copyData['forwardCode'])
                    me.$set(me.frmData, 'prdCode', copyData['prdCode'])
                    me.$set(me.frmData, 'preDeclareCode', copyData['preDeclareCode'])
                  })
                })
              }
            }
          })
        }
      },
      declareAuthArray: {
        handler: function (declareAuth) {
          let me = this,
            result = ''
          if (declareAuth.includes('1')) {
            result = '1,'
          }
          if (declareAuth.includes('2')) {
            result += '2,'
          }
          if (declareAuth.includes('3')) {
            result += '3,'
          }
          if (declareAuth.includes('4')) {
            result += '4,'
          }
          if (declareAuth.includes('5')) {
            result += '5,'
          }
          if (declareAuth.includes('6')) {
            result += '6,'
          }
          if (declareAuth.includes('7')) {
            result += '7,'
          }
          if (declareAuth.includes('9')) {
            result += '9,'
          }
          if (result.indexOf(',') > -1) {
            result = result.substring(0, result.length - 1)
          }
          me.$set(me.frmData, 'declareAuth', result)
        }
      },
      forwardAliasArray: {
        handler: function (forwardAlias) {
          let me = this,
            result = ''
          if (forwardAlias.includes('1')) {
            result = '1,'
          }
          if (forwardAlias.includes('2')) {
            result += '2,'
          }
          if (forwardAlias.includes('3')) {
            result += '3,'
          }
          if (forwardAlias.includes('4')) {
            result += '4,'
          }
          if (forwardAlias.includes('5')) {
            result += '5,'
          }
          if (forwardAlias.includes('6')) {
            result += '6,'
          }
          if (forwardAlias.includes('8')) {
            result += '8,'
          }
          if (forwardAlias.includes('9')) {
            result += '9,'
          }
          if (result.indexOf(',') > -1) {
            result = result.substring(0, result.length - 1)
          }
          me.$set(me.frmData, 'forwardAuth', result)
        }
      },

      prdAliasArray: {
        handler: function (prdAlias) {
          let me = this,
            result = ''
          if (prdAlias.includes('4')) {
            result = '4,'
          }
          if (prdAlias.includes('8')) {
            result += '8,'
          }
          if (prdAlias.includes('9')) {
            result += '9,'
          }

          if (result.indexOf(',') > -1) {
            result = result.substring(0, result.length - 1)
          }
          me.$set(me.frmData, 'prdAuth', result)
        }
      },

      'frmData.declareIsEnable': {
        handler: function (declareEnable) {
          let me = this
          if (declareEnable !== '1') {
            me.$set(me.frmData, 'declareAlias', '')
            me.$set(me.frmData, 'declareCode', '')
            me.$set(me.frmData, 'declareName', '')
            me.$set(me, 'declareAuthArray', [])
          }
        }
      },
      'frmData.forwardIsEnable': {
        handler: function (forwardEnable) {
          let me = this
          if (forwardEnable !== '1') {
            me.$set(me.frmData, 'forwardAlias', '')
            me.$set(me.frmData, 'forwardCode', '')
            me.$set(me.frmData, 'forwardName', '')
            me.$set(me, 'forwardAliasArray', [])
          }
        }
      },

      'frmData.prdIsEnable': {
        handler: function (forwardEnable) {
          let me = this
          if (forwardEnable !== '1') {
            me.$set(me.frmData, 'prdAlias', '')
            me.$set(me.frmData, 'prdCode', '')
            me.$set(me.frmData, 'prdName', '')
            me.$set(me, 'prdAliasArray', [])
          }
        }
      },


      'frmData.preDeclareIsEnable': {
        handler: function (preDeclareEnable) {
          let me = this
          if (preDeclareEnable !== '1') {
            me.$set(me.frmData, 'preDeclareAlias', '')
            me.$set(me.frmData, 'preDeclareCode', '')
            me.$set(me.frmData, 'preDeclareName', '')
          }
        }
      },
      'frmData.declareAlias': {
        handler: function (declare) {
          let me = this,
            currItem = me.cmbSource.declareAlias.find(item => item.value === declare)
          if (currItem) {
            me.$set(me.frmData, 'declareName', currItem['label'])
            me.$set(me.frmData, 'declareCode', currItem['agentTradeCode'])
          } else {
            me.$set(me.frmData, 'declareCode', '')
            me.$set(me.frmData, 'declareName', '')
          }
        }
      },
      'frmData.forwardAlias': {
        handler: function (forward) {
          let me = this,
            currItem = me.cmbSource.forwardAlias.find(item => item.value === forward)
          if (currItem) {
            me.$set(me.frmData, 'forwardName', currItem['label'])
            me.$set(me.frmData, 'forwardCode', currItem['agentTradeCode'])
          } else {
            me.$set(me.frmData, 'forwardCode', '')
            me.$set(me.frmData, 'forwardName', '')
          }
        }
      },

      'frmData.prdAlias': {
        handler: function (prd) {
          let me = this,
            currItem = me.cmbSource.prdAlias.find(item => item.value === prd)
          if (currItem) {
            me.$set(me.frmData, 'prdName', currItem['label'])
            me.$set(me.frmData, 'prdCode', currItem['agentTradeCode'])
          } else {
            me.$set(me.frmData, 'prdCode', '')
            me.$set(me.frmData, 'prdName', '')
          }
        }
      },


      'frmData.preDeclareAlias': {
        handler: function (declare) {
          let me = this,
            currItem = me.cmbSource.forwardAlias.find(item => item.value === declare)
          if (currItem) {
            me.$set(me.frmData, 'preDeclareName', currItem['label'])
            me.$set(me.frmData, 'preDeclareCode', currItem['agentTradeCode'])
          } else {
            me.$set(me.frmData, 'preDeclareCode', '')
            me.$set(me.frmData, 'preDeclareName', '')
          }
        }
      },
    },
    computed: {
      /**
       * 输入组件是否可输
       * @returns {boolean}
       */
      showDisable() {
        let me = this
        return !(me.editConfig.editStatus === editStatus.ADD || me.editConfig.editStatus === editStatus.EDIT)
      },
      declareDisable() {
        let me = this
        if (me.showDisable) {
          return true
        }
        return me.frmData.declareIsEnable !== '1'
      },
      forwardDisable() {
        let me = this
        if (me.showDisable) {
          return true
        }
        return me.frmData.forwardIsEnable !== '1'
      },

      prdDisable() {
        let me = this
        if (me.showDisable) {
          return true
        }
        return me.frmData.prdIsEnable !== '1'
      },
      preDeclareDisable() {
        let me = this
        if (me.showDisable) {
          return true
        }
        return me.frmData.preDeclareIsEnable !== '1'
      },
    },
    created: function () {
      let me = this
      me.$http.get(me.ajaxUrl.getProxy).then(res => {
        me.$set(me.cmbSource, 'declareAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'CUT'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))
        me.$set(me.cmbSource, 'forwardAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'FOD'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))

        me.$set(me.cmbSource, 'prdAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'PRD'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))
      }).catch(() => {
        me.$set(me.cmbSource, 'declareAlias', [])
        me.$set(me.cmbSource, 'forwardAlias', [])
        me.$set(me.cmbSource, 'prdAlias', [])
        me.$set(me.cmbSource, 'preDeclareAlias', [])
      })
    },
    methods: {
      /**
       * 获取默认值
       * @returns {{forwardCode: string, forwardAlias: string, forwardIsEnable: string, forwardName: string, declareAuth: string, declareAlias: string, declareName: string, declareIsEnable: string, forwardAuth: string, declareCode: string, sid: string}}
       */
      getDefaultData() {
        return {
          sid: '',
          userNo: '',
          declareIsEnable: '',  // 报关行生效标志
          declareAlias: '',     // 报关行简码
          declareCode: '',      // 报关行企业十位代码
          declareName: '',      // 报关行企业名称
          declareAuth: '',      // 报关行权限,包含 预录入单制单,报关追踪维护。格式：0,1(未授权/授权)
          forwardIsEnable: '',  // 货代生效标志
          forwardAlias: '',     // 货代简码
          forwardCode: '',      // 货代企业十位代码
          forwardName: '',      // 货代企业名称
          forwardAuth: '',       // 货代权限,包含 预录入单制单,物流追踪维护。格式：0,1…(未授权/授权)
          preDeclareIsEnable:'', // 预报单权限,格式：0,1(未授权/授权)
          preDeclareCode: '',      // 预报单企业十位代码
          preDeclareName: '',      // 预报单企业名称
          prdIsEnable: '',  // 供应商生效标志
          prdAlias: '',     // 供应商简码
          prdCode: '',      // 供应商企业十位代码
          prdName: '',      // 供应商企业名称
          prdAuth: '',      // 供应商权限,包含 预录入单制单,报关追踪维护。格式：0,1(未授权/授权)
        }
      },
      setToolbarLoading(command, val = false) {
        let me = this,
          commandAction = me.buttons.find(it => it.command === command)
        if (commandAction) {
          commandAction.loading = val
        }
      },
      /**
       * 调用列表界面方法并将当前编辑界面信息传给列表界面
       * @param showList
       * @param editStatus
       * @param data
       */
      refreshIncomingData(showList, editStatus, data) {
        let me = this
        me.$emit('onEditBack', {
          editData: data,
          showList: showList,
          editStatus: editStatus
        })
      },
      /**
       * 保存
       */
      handleSave() {
        let me = this
        if (!isNullOrEmpty(me.frmData.declareAlias)) {
          let currItem = me.cmbSource.declareAlias.find(item => item.value === me.frmData.declareAlias)
          if (currItem) {
            me.$set(me.frmData, 'declareName', currItem['label'])
          }
        }
        if (!isNullOrEmpty(me.frmData.forwardAlias)) {
          let currItem = me.cmbSource.forwardAlias.find(item => item.value === me.frmData.forwardAlias)
          if (currItem) {
            me.$set(me.frmData, 'forwardName', currItem['label'])
          }
        }


        if (!isNullOrEmpty(me.frmData.prdAlias)) {
          let currItem = me.cmbSource.prdAlias.find(item => item.value === me.frmData.prdAlias)
          if (currItem) {
            me.$set(me.frmData, 'prdName', currItem['label'])
          }
        }

        if (!isNullOrEmpty(me.frmData.preDeclareAlias)) {
          let currItem = me.cmbSource.forwardAlias.find(item => item.value === me.frmData.preDeclareAlias)
          if (currItem) {
            me.$set(me.frmData, 'preDeclareName', currItem['label'])
          }
        }
        me.$refs['dataFrom'].validate().then(isValid => {
          if (isValid) {
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.setToolbarLoading('save', true)
              me.$http.post(me.ajaxUrl.insert, me.frmData).then(res => {
                me.refreshIncomingData(true, editStatus.EDIT, res.data.data)
                me.$Message.success('新增成功!')
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('save')
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              me.setToolbarLoading('save', true)
              me.$http.put(me.ajaxUrl.update + '/' + me.frmData.sid, me.frmData).then(res => {
                me.refreshIncomingData(true, editStatus.EDIT, res.data.data)
                me.$Message.success('修改成功!')
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('save')
              })
            }
          }
        })
      },
      /**
       * 返回
       */
      handleBack() {
        let me = this
        me.refreshIncomingData(true, editStatus.SHOW, me.getDefaultData())
      }
    }
  }
</script>
