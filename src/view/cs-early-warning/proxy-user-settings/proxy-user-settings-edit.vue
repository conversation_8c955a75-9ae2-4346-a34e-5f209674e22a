<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { agentAuthConfig } from '@/view/cs-early-warning/proxy-user-settings/js/agentAuthConfig'

  export default {
    name: 'proxyUserSettingsEdit',
    mixins: [baseDetailConfig, agentAuthConfig],
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        cmbSource: {},
        firstTime: true,
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.customsClearanceRiskSetting.proxyUserSettings.insert,
          update: csAPI.customsClearanceRiskSetting.proxyUserSettings.update
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      },
      'detailConfig.model.agentCode': {
        handler: function () {
          let me = this
          if (!me.firstTime) {
            me.agentCodeChange()
          }
        }
      }
    },
    methods: {
      getFields() {
        let me = this,
          agentAuthData = me.getAgentAuth()
        return [{
          required: true,
          props: {
            maxlength: 50
          },
          key: 'userNo',
          title: '用户登录名'
        }, {
          required: true,
          title: '代理权限',
          key: 'agentAuth',
          type: 'checkBoxGroup',
          itemClass: 'dc-merge-2-4',
          props: {
            options: agentAuthData.map(item => {
              return {
                label: item.value,
                title: item.label
              }
            })
          },
          on: {
            change: me.agentAuthChange
          }
        }, {
          type: 'select',
          required: true,
          key: 'agentCode',
          title: '(代理)所属公司'//,
          // on: {
          //   change: me.agentCodeChange
          // }
        }, {
          props: {
            disabled: true
          },
          key: 'agentTradeCode',
          title: '(代理)企业海关编码'
        }, {
          props: {
            disabled: true
          },
          key: 'agentName',
          title: '(代理)企业名称'
        }, {
          props: {
            disabled: true
          },
          type: 'select',
          key: 'agentType',
          title: '(代理)企业类型'
        }]
      },
      afterModelLoaded(flag) {
        let me = this,
          theKey = 'agentAuth'
        if (!flag) {
          me.$nextTick(() => {
            me.setRealData(me.detailConfig.model[theKey])
            me.afterValidFailure()
          })
        }
        me.$nextTick(() => {
          me.$set(me, 'firstTime', false)
        })
      },
      agentCodeChange() {
        let me = this,
          selItem = me.dynamicSource.agentCode.find(item => {
            return item.value === me.detailConfig.model['agentCode']
          })
        console.info('agentCodeChange')
        if (selItem) {
          me.$set(me.detailConfig.model, 'agentTradeCode', selItem['agentTradeCode'])
          me.$set(me.detailConfig.model, 'agentName', selItem['actualCompanyName'])
          if (selItem['value'].indexOf('CUT') > -1) {
            me.$set(me.detailConfig.model, 'agentType', 'CUT')
          } else if (selItem['value'].indexOf('FOD') > -1) {
            me.$set(me.detailConfig.model, 'agentType', 'FOD')
          } else {
            me.$set(me.detailConfig.model, 'agentType', '')
          }
        } else {
          me.$set(me.detailConfig.model, 'agentTradeCode', '')
          me.$set(me.detailConfig.model, 'agentName', '')
          me.$set(me.detailConfig.model, 'agentType', '')
        }
      },
      agentAuthChange() {
        let me = this,
          theKey = 'agentAuth'
        me.agentAuthDataChange(me.detailConfig.model[theKey])
      },
      handleSave() {
        let me = this,
          theKey = 'agentAuth'
        if (me.detailConfig.model.hasOwnProperty(theKey)) {
          me.$set(me.detailConfig.model, theKey, me.getSaveData())
        }
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
