import { isNullOrEmpty } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import proxyUserSettingsEditNew from '../proxy-user-settings-edit-new'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const proxyUserSettingsListNew = {
  name: 'proxyUserSettingsListNew',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    proxyUserSettingsEditNew
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        disable: true
      },
      cmbSource: {
        declareAlias: [],
        forwardAlias: [],
        prdAlias: [],
      },
      importShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete
      }
    }
  },
  created: function () {
    let me = this
    me.loadProxy()
  },
  methods: {
    actionLoaded() {
      let me = this,
        theEditAction = me.actions.find(it => it.command === 'edit')
      if (theEditAction) {
        me.$set(me.listConfig, 'disable', false)
      } else {
        me.$set(me.listConfig, 'disable', true)
      }
    },
    /**
     * 获取【代理报关行/货代】
     */
    loadProxy() {
      let me = this
      me.$http.get(me.ajaxUrl.getProxy).then(res => {
        me.$set(me.cmbSource, 'declareAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'CUT'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))
        me.$set(me.cmbSource, 'forwardAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'FOD'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))

        me.$set(me.cmbSource, 'prdAlias', res.data.data
          .filter(it => {
            return it['customerType'] === 'PRD'
          })
          .map(item => {
            return {
              label: item.companyName,
              value: item['actualCustomerCode'],
              agentTradeCode: item['customerTradeCode']
            }
          }))
      }).catch(() => {
        me.$set(me.cmbSource, 'declareAlias', [])
        me.$set(me.cmbSource, 'forwardAlias', [])
        me.$set(me.cmbSource, 'prdAlias', [])
      }).finally(() => {
        let fieldDeclare = me.searchConfig.fields.find(item => {
          return item.key === 'declareAlias'
        })
        me.fieldOptimization(fieldDeclare)
        let fieldForward = me.searchConfig.fields.find(item => {
          return item.key === 'forwardAlias'
        })
        me.fieldOptimization(fieldForward)

        let fieldPrd = me.searchConfig.fields.find(item => {
          return item.key === 'prdAlias'
        })
        me.fieldOptimization(fieldPrd)
      })
    },
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        type: 'select',
        title: '报关行',
        key: 'declareAlias'
      }, {
        type: 'select',
        title: '货代代理',
        key: 'forwardAlias'
      }, {
        type: 'select',
        title: '供应商',
        key: 'prdAlias'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 显示列表
     * @returns {({width: number, title: string, key: string}|{children: [{cellRendererFramework: *, width: number, title: string, key: string}, {width: number, title: string, key: string}, {cellRendererFramework: *, width: number, title: string, key: string}], title: string}|{children: [{cellRendererFramework: *, width: number, title: string, key: string}, {width: number, title: string, key: string}, {cellRendererFramework: *, width: number, title: string, key: string}], title: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 160,
        key: 'userNo',
        title: '用户登录名'
      }, {
        title: '报关行',
        children: [{
          width: 316,
          title: '报关行企业',
          key: 'declareAlias',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.keyValueRender(h, params, 'declareAlias', 'declareName')
          }, true)
        }, {
          width: 120,
          key: 'declareCode',
          title: '报关行十位代码'
        }, {
          width: 156,
          key: 'declareAuth',
          title: '报关行权限',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            let currValue = params.row['declareAuth'],
              resultTitle = ''
            if (currValue.indexOf('1') > -1) {
              resultTitle = '提单'
            }
            if (currValue.indexOf('2') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '报关追踪'
              } else {
                resultTitle += ', 报关追踪'
              }
            }
            if (currValue.indexOf('3') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '报关单'
              } else {
                resultTitle += ', 报关单'
              }
            }
            if (currValue.indexOf('4') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '费用维护'
              } else {
                resultTitle += ', 费用维护'
              }
            }
            if (currValue.indexOf('5') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '发票箱单'
              } else {
                resultTitle += ', 发票箱单'
              }
            }
            if (currValue.indexOf('6') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '物流追踪'
              } else {
                resultTitle += ', 物流追踪'
              }
            }
            return me.toolTipRender(h, resultTitle)
          })
        }]
      }, {
        title: '货代代理',
        children: [{
          width: 316,
          title: '货代企业名称',
          key: 'forwardAlias',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            return me.keyValueRender(h, params, 'forwardAlias', 'forwardName')
          }, true)
        }, {
          width: 110,
          key: 'forwardCode',
          title: '货代十位代码'
        }, {
          width: 156,
          title: '货代权限',
          key: 'forwardAuth',
          cellRendererFramework: me.baseCellRenderer(function (h, params) {
            let currValue = params.row['forwardAuth'],
              resultTitle = ''
            if (currValue.indexOf('1') > -1) {
              resultTitle = '提单'
            }
            if (currValue.indexOf('2') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '报关追踪'
              } else {
                resultTitle += ', 报关追踪'
              }
            }
            if (currValue.indexOf('3') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '报关单'
              } else {
                resultTitle += ', 报关单'
              }
            }
            if (currValue.indexOf('4') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '费用维护'
              } else {
                resultTitle += ', 费用维护'
              }
            }
            if (currValue.indexOf('5') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '发票箱单'
              } else {
                resultTitle += ', 发票箱单'
              }
            }
            if (currValue.indexOf('6') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '物流追踪'
              } else {
                resultTitle += ', 物流追踪'
              }
            }
            if (currValue.indexOf('8') > -1) {
              if (isNullOrEmpty(resultTitle)) {
                resultTitle = '采购订单'
              } else {
                resultTitle += ', 采购订单'
              }
            }
            return me.toolTipRender(h, resultTitle)
          })
        }]
      },
        {
          title: '供应商',
          children: [{
            width: 316,
            title: '供应商企业名称',
            key: 'prdAlias',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              return me.keyValueRender(h, params, 'prdAlias', 'prdName')
            }, true)
          }, {
            width: 110,
            key: 'prdCode',
            title: '供应商十位代码'
          }, {
            width: 156,
            title: '供应商权限',
            key: 'prdAuth',
            cellRendererFramework: me.baseCellRenderer(function (h, params) {
              let currValue = params.row['prdAuth'],
                resultTitle = ''
              if (currValue.indexOf('8') > -1) {
                resultTitle = '采购订单'
              }
              return me.toolTipRender(h, resultTitle)
            })
          }]
        }
        ,
        {
        width: 88,
        title: '创建日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
