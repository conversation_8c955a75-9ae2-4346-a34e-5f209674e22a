import { isNullOrEmpty } from '@/libs/util'

export const agentAuthConfig = {
  name: 'agentAuthConfig',
  data() {
    return {
      agentAuthData: {
        one: 0,
        two: 0,
        three: 0,
        four: 0
      }
    }
  },
  methods: {
    /**
     * 获取数据源
     * @returns {*[]}
     */
    getAgentAuth() {
      return [{
        value: '1', label: '预录入单制单'
      }, {
        value: '2', label: '物流追踪维护'
      }, {
        value: '3', label: '报关追踪维护'
      }, {
        value: '4', label: '提单货运代理'
      }]
    },
    /**
     * 根据key值组获取中文值
     * @param keys
     */
    getNamesFormKeys(keys) {
      let me = this,
        resultArray = [],
        originalArray = [],
        agentAuthData = me.getAgentAuth()
      if (!isNullOrEmpty(keys)) {
        originalArray = keys.split(',')
        originalArray.forEach((value, index) => {
          let item = undefined
          if (index === 0 && value === '1') {
            item = agentAuthData.find(cell => {
              return cell.value === '1'
            })
          } else if (index === 1 && value === '1') {
            item = agentAuthData.find(cell => {
              return cell.value === '2'
            })
          } else if (index === 2 && value === '1') {
            item = agentAuthData.find(cell => {
              return cell.value === '3'
            })
          } else if (index === 3 && value === '1') {
            item = agentAuthData.find(cell => {
              return cell.value === '4'
            })
          }
          if (item && !isNullOrEmpty(item.label)) {
            resultArray.push(item.label)
          }
        })
        return resultArray.toString()
      }
      return ''
    },
    /**
     * 将组件至转化为配置值
     * @param originalVal
     */
    agentAuthDataChange(originalVal) {
      let me = this
      me.$set(me.agentAuthData, 'one', 0)
      me.$set(me.agentAuthData, 'two', 0)
      me.$set(me.agentAuthData, 'three', 0)
      me.$set(me.agentAuthData, 'four', 0)
      if (!isNullOrEmpty(originalVal)) {
        if (originalVal.indexOf('1') > -1) {
          me.$set(me.agentAuthData, 'one', 1)
        }
        if (originalVal.indexOf('2') > -1) {
          me.$set(me.agentAuthData, 'two', 1)
        }
        if (originalVal.indexOf('3') > -1) {
          me.$set(me.agentAuthData, 'three', 1)
        }
        if (originalVal.indexOf('4') > -1) {
          me.$set(me.agentAuthData, 'four', 1)
        }
      }
    },
    /**
     * 根据组件值设置参数值
     * @param originalVal
     */
    setRealData(originalVal) {
      let me = this,
        originalArray = []
      me.$set(me.agentAuthData, 'one', 0)
      me.$set(me.agentAuthData, 'two', 0)
      me.$set(me.agentAuthData, 'three', 0)
      me.$set(me.agentAuthData, 'four', 0)
      if (!isNullOrEmpty(originalVal)) {
        originalArray = originalVal.split(',')
        if (originalArray.length > 0 && originalArray[0] === '1') {
          me.$set(me.agentAuthData, 'one', 1)
        }
        if (originalArray.length > 1 && originalArray[1] === '1') {
          me.$set(me.agentAuthData, 'two', 1)
        }
        if (originalArray.length > 2 && originalArray[2] === '1') {
          me.$set(me.agentAuthData, 'three', 1)
        }
        if (originalArray.length > 3 && originalArray[3] === '1') {
          me.$set(me.agentAuthData, 'four', 1)
        }
      }
    },
    /**
     * 获取用于保存的值
     * @returns {string}
     */
    getSaveData() {
      let me = this,
        result = []
      if (me.agentAuthData.one === 1) {
        result.push('1')
      } else {
        result.push('0')
      }
      if (me.agentAuthData.two === 1) {
        result.push('1')
      } else {
        result.push('0')
      }
      if (me.agentAuthData.three === 1) {
        result.push('1')
      } else {
        result.push('0')
      }
      if (me.agentAuthData.four === 1) {
        result.push('1')
      } else {
        result.push('0')
      }
      return result.toString()
    },
    /**
     * 保存错误后执行的操作
     */
    afterValidFailure() {
      let me = this,
        theKey = 'agentAuth',
        agentAuthValue = ','
      if (me.agentAuthData.one === 1) {
        agentAuthValue += '1,'
      }
      if (me.agentAuthData.two === 1) {
        agentAuthValue += '2,'
      }
      if (me.agentAuthData.three === 1) {
        agentAuthValue += '3,'
      }
      if (me.agentAuthData.four === 1) {
        agentAuthValue += '4'
      }
      me.$nextTick(() => {
        me.$set(me.detailConfig.model, theKey, agentAuthValue)
      })
    }
  }
}
