// import ProxyUserSettingsEdit from '../proxy-user-settings-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import proxyUserSettingsEditNew from '../proxy-user-settings-edit-new'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { agentAuthConfig } from '@/view/cs-early-warning/proxy-user-settings/js/agentAuthConfig'

export const proxyUserSettingsList = {
  name: 'proxyUserSettingsList',
  mixins: [columnRender, agentAuthConfig, baseSearchConfig, listDataProcessing],
  components: {
    // ProxyUserSettingsEdit,
    proxyUserSettingsEditNew
  },
  data() {
    let agentAuthData = this.getAgentAuth(),
      params = this.getParams(agentAuthData)
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        disable: true
      },
      cmbSource: {
        agentCode: [],
        agentType: [{
          value: 'CUT',
          label: '报关行'
        }, {
          value: 'FOD',
          label: '货代'
        }],
        agentAuth: agentAuthData
      },
      importShow: false,
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete
      }
    }
  },
  created: function () {
    this.loadProxy()
  },
  methods: {
    actionLoaded() {
      let me = this,
        theEditAction = me.actions.find(it => it.command === 'edit')
      if (theEditAction) {
        me.$set(me.listConfig, 'disable', false)
      } else {
        me.$set(me.listConfig, 'disable', true)
      }
    },
    /**
     * 获取【代理报关行/货代】
     */
    loadProxy() {
      let me = this
      me.$http.get(me.ajaxUrl.getProxy).then(res => {
        me.$set(me.cmbSource, 'agentCode', res.data.data.map(item => {
          return {
            label: item.companyName,
            value: item.customerCode,
            agentTradeCode: item['customerTradeCode'],
            actualCompanyName: item['actualCompanyName']
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'agentCode', [])
      }).finally(() => {
        let field = me.searchConfig.fields.find(item => {
          return item.key === 'agentCode'
        })
        me.fieldOptimization(field)
      })
    },
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        type: 'select',
        key: 'agentCode',
        title: '(代理)所属公司',
        itemClass: 'dc-merge-1-3'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        theKey = 'agentAuth',
        paramObj = deepClone(me.searchConfig.model)
      if (paramObj.hasOwnProperty(theKey)) {
        me.agentAuthDataChange(paramObj[theKey])
        paramObj[theKey] = me.getSaveData()
      }
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 160,
        key: 'userNo',
        title: '用户登录名'
      }, {
        width: 320,
        key: 'agentAuth',
        title: '代理权限',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return h('span', me.getNamesFormKeys(params.row[params.column.key]))
        }, true)
      }, {
        width: 280,
        key: 'agentCode',
        title: '(代理)所属公司',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'actualAgentCode', 'agentName')
        }, true)
      }, {
        width: 130,
        key: 'agentTradeCode',
        title: '(代理)企业海关编码'
      }, {
        width: 110,
        key: 'agentType',
        title: '(代理)企业类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.agentType)
        }, true)
      }, {
        width: 88,
        title: '创建日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
