<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoCard :bordered="false" title="基础信息" class="ieLogisticsTrackingCard">
      <div class="xdo-enter-root" v-focus>
        <FormBuilder ref="baseForm" :schema="schema" :items="baseFields" :rules="rulesHeader" :model="frmData" :disabled="showDisable">
          <template v-slot:districtCode>
            <AreaPostCascader :options="areaOptions" @onAreaDataChanged="onAreaDataChanged" :disabled="showDisable"></AreaPostCascader>
          </template>
          <template v-slot:wrapType2>
            <Input v-model="wrapType2View" placeholder="请选择包装种类..." disabled>
              <XdoButton slot="append" type="primary" :disabled="showDisable" @click="onWrapType2Search" style="width: 80px;">请选择</XdoButton>
            </Input>
          </template>
        </FormBuilder>
      </div>
    </XdoCard>
    <XdoCard :bordered="false" title="申报信息" class="ieLogisticsTrackingCard">
      <DynamicForm ref="appForm" :disabled="showDisable" labelWidth="136" :class="schema.class"
                   :model="frmData" :rules="rulesHeader" :fields="appFields">
      </DynamicForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-bottom: 6px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
    <WrapTypeSelectPop :show.sync="wrapType2PopShow" :wrap-type="frmData.wrapType2"
                       @doWrapTypeFill="doChangeWrapType2"></WrapTypeSelectPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
  import { getTradeModeByEmsNo } from '@/view/cs-ie-manage/js/comm/regulatoryRules'
  import AreaPostCascader from '@/view/cs-ie-manage-mixins/components/area-post-cascader'
  import WrapTypeSelectPop from '@/view/cs-ie-manage/components/wrap-type-select/wrap-type-select-pop'

  export default {
    name: 'billOfLadingTemplateEdit',
    mixins: [commEdit],
    components: {
      AreaPostCascader,
      WrapTypeSelectPop
    },
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      detailConfig: {
        type: Object,
        default: () => ({
          frmData: {},
          baseFields: [],
          appFields: []
        })
      },
      objSource: {
        type: Object,
        default: () => ({
          cutDataValueCode: {}
        })
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false,
        type: 'primary'
      }
      return {
        dynamicSource: {
          shipFromToData: []
        },
        formName: 'baseForm',
        fullTradeModeList: [],
        wrapType2PopShow: false,
        schema: {
          titleWidth: 110,
          class: 'dc-form dc-form-4'
        },
        ajaxUrl: {
          insert: csAPI.csImportExport.template.insert,
          update: csAPI.csImportExport.template.insert
        },
        rulesHeader: {
          tempName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          bondMark: [{required: false, message: '不能为空!', trigger: 'blur'}]
        },
        buttons: [{
          ...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'
        }, {
          ...btnComm, click: this.handleSaveContinue, icon: 'dc-btn-save', label: '保存继续'
        }, {
          ...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'
        }],
        disabledFieldsComm:['declareName', 'declareCreditCode', 'agentCreditCode', 'agentName'],
        disabledFields:[]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function(status) {
          this.buttons[0].needed = status !== editStatus.SHOW
          this.disabledFields = status === editStatus.ADD ? this.disabledFieldsComm : [...this.disabledFieldsComm, 'decType']
          if (this.frmData.decType === '0') {
            this.disabledFields = [...this.disabledFields, 'bondMark']
          } else {
            this.disabledFields = this.disabledFields
          }
          this.focusReset()
        }
      },
      'frmData.overseasShipper': {
        immediate: true,
        handler: function(overseasShipper) {
          this.overseasShipperChange(overseasShipper)
        }
      },
      'frmData.declareCodeCustoms': {
        handler: function(declareCodeCustoms) {
          //this.$set(this.frmData, 'declareCode', '')
          if (!isNullOrEmpty(declareCodeCustoms)) {
            let declareCode = this.objSource.cutDataValueCode[declareCodeCustoms]
            if (!isNullOrEmpty(declareCode)) {
              this.$set(this.frmData, 'declareCode', declareCode)
            }
          }
          this.declareCodeEnter()
        }
      },
      'frmData.declareCode': {
        handler: function() {
          this.declareCodeEnter()
        }
      },
      iEMark: {
        immediate: true,
        handler: function() {
          //console.info(this.getTradeModeByEmsNo)
        }
      },
      'frmData.emsNo': {
        immediate: true,
        handler: function() {
          //console.info(this.getTradeModeByEmsNo)
        }
      },
      'frmData.decType': {
        handler: function(decType) {
          if (decType === '0') {
            this.$set(this.frmData, 'bondMark', null)
            this.rulesHeader.bondMark[0].required = false
            this.disabledFields = [...this.disabledFieldsComm, 'bondMark']
          } else {
            this.rulesHeader.bondMark[0].required = true
            this.disabledFields = this.disabledFieldsComm
          }
          this.disabledFields = this.editConfig.editStatus === 'add' ? this.disabledFields : [...this.disabledFields, 'decType']
        }
      }
    },
    created: function () {
      let me = this
      me.pcodeList(me.pcode.trade).then(res => {
        me.$set(me, 'fullTradeModeList', res)
      }).catch(() => {
        me.$set(me, 'fullTradeModeList', [])
      })
    },
    computed: {
      baseFields() {
        let me = this
        return me.detailConfig.baseFields.map(field => {
          if (!me.disabledFields.includes(field.key)) {
            if (field.props) {
              field.props.disabled = me.showDisable
            } else {
              field.props = {
                disabled: me.showDisable
              }
            }
          } else {
            if (field.props) {
              field.props.disabled = true
            } else {
              field.props = {
                disabled: true
              }
            }
          }
          return field
        })
      },
      appFields() {
        let me = this,
          fields = []
        me.detailConfig.appFields.forEach(field => {
          let theField = deepClone(field)
          if (theField.key === 'tradeMode') {
            theField.type = 'select'
            theField.props = {
              options: me.filterTradeMode
            }
          }
          if (!me.disabledFields.includes(theField.key)) {
            if (theField.props) {
              theField.props.disabled = me.showDisable
            } else {
              theField.props = {
                disabled: me.showDisable
              }
            }
          } else {
            if (theField.props) {
              theField.props.disabled = true
            } else {
              theField.props = {
                disabled: true
              }
            }
          }

          if (['agentCode'].includes(theField.key)) {
            theField.on = {
              enter: me.agentCodeEnter
            }
          }
          if (['agentCreditCode'].includes(theField.key)) {
            theField.title = ''
            theField.labelWidth = 0
          }
          fields.push(theField)
        })
        return fields
      },
      areaOptions() {
        return {
          area: {
            field: 'districtCode',
            value: this.frmData.districtCode
          },
          post: {
            field: 'districtPostCode',
            value: this.frmData.districtPostCode
          }
        }
      },
      /**
       * 用于展示其他包装种类
       * @returns {string}
       */
      wrapType2View() {
        let me = this,
          result = []
        if (!isNullOrEmpty(me.frmData.wrapType2)) {
          let wrapTypes = me.frmData.wrapType2.split(',')
          if (Array.isArray(wrapTypes) && wrapTypes.length > 0) {
            wrapTypes.forEach(wrap => {
              if (!isNullOrEmpty(wrap)) {
                result.push(wrap + ' ' + me.pcodeGet('WRAP', wrap))
              }
            })
          }
        }
        return result.toString()
      },
      /**
       * 根据EmsNo及iEMark过滤后的监管方式
       * @returns {*}
       */
      filterTradeMode() {
        let me = this
        return getTradeModeByEmsNo(me.frmData.emsNo, me.iEMark, me.fullTradeModeList)
      }
    },
    methods: {
      focusReset() {
        let me = this, theEl,
          baseForm, baseInputs = [],
          appForm, appInputs = []
        me.$nextTick(() => {
          // baseElements
          if (me.$refs['baseForm']) {
            baseForm = me.$refs['baseForm']
            if (baseForm && baseForm.$el && baseForm.$el['elements']) {
              for (let i = 0; i < baseForm.$el['elements'].length; i++) {
                theEl = baseForm.$el['elements'][i]
                if (theEl.disabled !== true) {
                  baseInputs.push(theEl)
                }
              }
            }
          }
          // appElements
          if (me.$refs['appForm']) {
            appForm = me.$refs['appForm']
            if (appForm && appForm.$el && appForm.$el['elements']) {
              for (let j = 0; j < appForm.$el['elements'].length; j++) {
                theEl = appForm.$el['elements'][j]
                if (theEl.disabled !== true) {
                  appInputs.push(theEl)
                }
              }
            }
          }
          for (let b = 0; b < baseInputs.length; b++) {
            if (baseInputs[b].tagName === 'INPUT') {
              me.setElementEnter(baseInputs[b], function (e) {
                if (e.keyCode === 13) {
                  if (baseInputs[b + 1]) {
                    if (baseInputs[b + 1].tagName === 'INPUT') {
                      baseInputs[b + 1].focus()
                    } else if (baseInputs[b + 1].tagName === 'BUTTON') {
                      baseInputs[b + 1].click()
                    }
                  } else if (appInputs[0]) {
                    if (appInputs[0].tagName === 'INPUT') {
                      appInputs[0].focus()
                    } else if (appInputs[0].tagName === 'BUTTON') {
                      appInputs[0].click()
                    }
                  }
                }
              })
            }
          }
          // eslint-disable-next-line for-direction
          for (let a = 0; a < appInputs.length; a++) {
            if (appInputs[a].tagName === 'INPUT') {
              me.setElementEnter(appInputs[a], function (e) {
                if (e.keyCode === 13) {
                  if (appInputs[a + 1]) {
                    if (appInputs[a + 1].tagName === 'INPUT') {
                      appInputs[a + 1].focus()
                    } else if (appInputs[a + 1].tagName === 'BUTTON') {
                      appInputs[a + 1].click()
                    }
                  }
                }
              })
            }
          }
          if (baseInputs[0] && baseInputs[0].tagName === 'INPUT') {
            baseInputs[0].focus()
          }
        })
      },
      setElementEnter(element, handleEnter) {
        if (element.addEventListener) {
          element.addEventListener('keydown', handleEnter, false)
        } else if (element.attachEvent) {
          element.attachEvent('onkeydown', handleEnter)
        } else {
          element['onkeydown'] = handleEnter
        }
      },
      getDefaultData() {
        return JSON.parse(JSON.stringify(this.detailConfig.frmData))
      },
      sourceReload(field, source) {
        let me = this
        me.detailConfig.baseFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        me.detailConfig.appFields.forEach(item => {
          if (item.key === field) {
            if (item.props) {
              item.props.options = source
            } else {
              item.props = {
                options: source
              }
            }
          }
        })
        let fieldVal2 = me.frmData[field]
        me.$set(me.frmData, field, ' ')
        me.$set(me.frmData, field, fieldVal2)
      },
      /**
       * 根据境外收发货人变化设置Ship From/To的数据源
       * @param overseasShipper
       */
      overseasShipperChange(overseasShipper) {
        let me = this
        if (isNullOrEmpty(overseasShipper)) {
          me.$set(me.dynamicSource, 'shipFromToData', [])
          if (me.iEMark === 'I') {
            me.sourceReload('shipFrom', [])
          } else if (me.iEMark === 'E') {
            me.sourceReload('shipTo', [])
          }
        } else {
          if (me.iEMark === 'I') {
            me.$http.post(csAPI.csBaseInfo.supplierInfo.shipFrom.getShipFromCodeName + '/' + overseasShipper).then(res => {
              me.$set(me.dynamicSource, 'shipFromToData', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.dynamicSource, 'shipFromToData', [])
            }).finally(() => {
              me.sourceReload('shipFrom', me.dynamicSource.shipFromToData)
            })
          } else if (me.iEMark === 'E') {
            me.$http.post(csAPI.csBaseInfo.clientInfo.shipTo.getShipToCodeName + '/' + overseasShipper).then(res => {
              me.$set(me.dynamicSource, 'shipFromToData', ArrayToLocaleLowerCase(res.data.data))
            }).catch(() => {
              me.$set(me.dynamicSource, 'shipFromToData', [])
            }).finally(() => {
              me.sourceReload('shipTo', me.dynamicSource.shipFromToData)
            })
          }
        }
      },
      /**
       * 境内货源地/境内目的地
       * @param areaObj
       */
      onAreaDataChanged(areaObj) {
        let me = this
        me.$set(me.frmData, 'districtCode', areaObj['districtCode'])
        me.$set(me.frmData, 'districtPostCode', areaObj['districtPostCode'])
      },
      /**
       * 显示其他包装种类选择弹出框
       */
      onWrapType2Search() {
        let me = this
        me.$set(me, 'wrapType2PopShow', true)
      },
      /**
       * 修改其他包装种类
       * @param wrapType
       */
      doChangeWrapType2(wrapType) {
        let me = this
        me.$set(me.frmData, 'wrapType2', wrapType)
        me.$set(me, 'wrapType2PopShow', false)
      },
      /**
       * 申报单位代码获取名称
       */
      declareCodeEnter() {
        let me = this,
          queryCode = me.frmData.declareCode
        if (!isNullOrEmpty(queryCode) && queryCode.trim().length === 10) {
          me.pcodeRemote(me.pcode.company, queryCode.trim()).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              let re_name = res[0].NAME
              me.$set(me.frmData, 'declareName', re_name)
              me.$set(me.frmData, 'declareCreditCode', res[0].CREDIT_CODE)
            } else {
              me.$set(me.frmData, 'declareName', '')
              me.$set(me.frmData, 'declareCreditCode', '')
            }
          })
        } else {
          me.$set(me.frmData, 'declareName', '')
          me.$set(me.frmData, 'declareCreditCode', '')
        }
      },
      /**
       * 清单申报单位海关十位编码回车事件
       */
      agentCodeEnter() {
        let me = this,
          queryCode = me.frmData.agentCode
        if (!isNullOrEmpty(queryCode) && queryCode.trim().length === 10) {
          me.pcodeRemote(me.pcode.company, queryCode.trim()).then(res => {
            if (Array.isArray(res) && res.length > 0) {
              me.$set(me.frmData, 'agentName', res[0].NAME)
              me.$set(me.frmData, 'agentCreditCode', res[0].CREDIT_CODE)
            } else {
              me.$set(me.frmData, 'agentName', '')
              me.$set(me.frmData, 'agentCreditCode', '')
            }
          }).catch(() => {
            me.$set(me.frmData, 'agentName', '')
            me.$set(me.frmData, 'agentCreditCode', '')
          })
        } else {
          me.$set(me.frmData, 'agentName', '')
          me.$set(me.frmData, 'agentCreditCode', '')
        }
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
        })
      },
      /**
       * 保存继续
       */
      handleSaveContinue() {
        let me = this
        if (typeof me.beforeSave === 'function') {
          me.beforeSave.call(me)
        }
        me.doSave(() => {
          me.refreshIncomingData(false, editStatus.ADD, {})
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  /deep/ .ieLogisticsTrackingCard .ivu-card-body {
    padding: 0 8px 2px 8px;
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }
</style>
