<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch" class="xdo-enter-root" v-focus>
            <div class="separateLine"></div>
            <FormBuilder ref="headSearch" :schema="schema" :items="searchFields" :model="searchParam">
            </FormBuilder>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <BillOfLadingTemplateEdit v-show="!showList" @onEditBack="editBack" :editConfig="editConfig"
                              :i-e-mark="iEMark" :obj-source="objSource" :detailConfig="myDetailConfig"></BillOfLadingTemplateEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseListFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { billOfLadingTemplateList } from '../js/bill-of-lading-template/billOfLadingTemplateList'

  export default {
    name: 'billOfLadingITemplateList',
    mixins: [billOfLadingTemplateList],
    data() {
      return {
        iEMark: 'I',
        gridConfig: {
          exportTitle: '进口提单模板'
        },
        ajaxUrl: {
          deleteUrl: csAPI.csImportExport.template.delete,
          getLister: csAPI.csImportExport.template.selectInsertUser,
          selectAllPaged: csAPI.csImportExport.template.selectAllPaged,
          export: csAPI.csImportExport.template.export
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
