<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
            <XdoButton type="text" @click="handleBack">
              <XdoIcon type="ios-undo" size="22" style="color: green;" />
            </XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { dynamicReport } from '../js/dynamicReport'
  import { dynamicExport } from '@/view/cs-common/dynamic-export/dynamicExport'

  export default {
    name: 'dynamicReport',
    mixins: [dynamicReport, dynamicExport],
    data() {
      return {
        listConfig: {
          exportTitle: ''
        },
        taskInfo: {
          taskCode: 'DYNAMIC_REPORT'     // 添加任务使用的taskCode
        },
        ajaxUrl: {
          exportUrl: csAPI.dynamicReports.details.exportUrl,
          getParams: csAPI.dynamicReports.details.getParams,
          getColumns: csAPI.dynamicReports.details.getColumns,
          selectAllPaged: csAPI.dynamicReports.details.selectAllPaged
        }
      }
    },
    watch: {
      reportName: {
        immediate: true,
        handler: function (reportName) {
          let me = this
          me.$set(me.listConfig, 'exportTitle', reportName)
        }
      }
    },
    computed: {
      extendParams() {
        return {
          reportId: this.reportId
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
