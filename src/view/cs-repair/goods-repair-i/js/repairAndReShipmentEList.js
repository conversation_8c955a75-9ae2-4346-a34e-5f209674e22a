import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import TemplateSelectPop from '../../components/template-select-pop'
import RepairAndReShipmentETabs from '../repair-and-re-shipment-e-tabs'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const repairAndReShipmentEList = {
  name: 'repairAndReShipmentEList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    TemplateSelectPop,
    RepairAndReShipmentETabs
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      templatePop: {
        show: false
      },
      cmbSource: {
        status: [{
          value: '0', label: '暂存'
        }, {
          value: '1', label: '报关生成'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'dec-generation': this.handleDecGeneration
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({range: boolean, title: string, key: string}|{range: boolean, title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, type: string, key: string})[]}
     */
    getParams() {
      return [{
        range: true,
        key: 'completeDate',
        title: '复运完成日期'
      }, {
        range: true,
        key: 'declareDate',
        title: '报关单申报日期'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        title: '状态',
        key: 'status',
        type: 'select'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 展示列
     * @returns {{}[]}
     */
    getFields() {
      let me = this
      return [{
        width: 160,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 110,
        key: 'declareDate',
        title: '报关单申报日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 110,
        key: 'completeDate',
        title: '复运完成日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '制单员',
        key: 'userName'
      }, {
        width: 120,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 报关生成
     */
    handleDecGeneration() {
      let me = this
      if (me.checkRowSelected('报关生成', true)) {
        me.$set(me.templatePop, 'show', true)
      }
    },
    /**
     * 执行报关生成
     * @param templateId
     */
    doDecGeneration(templateId) {
      let me = this
      me.$http.post(me.ajaxUrl.dec, {
        templateId: templateId,
        sid: me.listConfig.selectRows[0].sid
      }).then(() => {
        me.$Message.success('报关生成成功!')
        me.handleSearchSubmit()
        me.$set(me.templatePop, 'show', false)
      }).catch(() => {
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
