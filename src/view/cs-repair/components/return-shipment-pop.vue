<template>
  <XdoModal width="600" mask v-model="show" title="复运"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm ref="dataForm" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="emsListNo" label="单据内部编号">
          <XdoIInput type="text" v-model="frmData.emsListNo" clearable :maxlength="32"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    name: 'returnShipmentPop',
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {
          emsListNo: ''
        },
        rulesHeader: {
          emsListNo: [{required: true, message: '不能为空!', trigger: 'blur'}]
        },
        buttons: [
          {...btnComm, click: this.handleConfirm, icon: 'dc-btn-save', label: '保存'},
          {...btnComm, click: this.handleClose, icon: 'dc-btn-cancel', label: '关闭'}
        ]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (!show) {
            if (me.$refs['dataForm']) {
              me.$refs['dataForm'].resetFields()
            }
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$refs['dataForm'].validate().then(isValid => {
          if (isValid) {
            me.$emit('doReturnShipment', me.frmData)
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
