<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <RepairAndReShipmentEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></RepairAndReShipmentEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <RepairAndReShipmentBodyList :head-id="currConfig.editData.sid"></RepairAndReShipmentBodyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { goodsRepairTabs } from '../comm/goodsRepairTabs'
  import RepairAndReShipmentEdit from './repair-and-re-shipment-edit'
  import RepairAndReShipmentBodyList from './repair-and-re-shipment-body-list'

  export default {
    name: 'repairAndReShipmentTabs',
    components: {
      RepairAndReShipmentEdit,
      RepairAndReShipmentBodyList
    },
    mixins: [goodsRepairTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
