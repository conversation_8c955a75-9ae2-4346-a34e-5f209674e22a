<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <OutboundRepairEdit :edit-config="currConfig" @onHeadSaved="onHeadSaved" @backToList="backToList"></OutboundRepairEdit>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <OutboundRepairBodyList :head-id="currConfig.editData.sid"></OutboundRepairBodyList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import OutboundRepairEdit from './outbound-repair-edit'
  import { goodsRepairTabs } from '../comm/goodsRepairTabs'
  import OutboundRepairBodyList from './outbound-repair-body-list'

  export default {
    name: 'outboundRepairTabs',
    components: {
      OutboundRepairEdit,
      OutboundRepairBodyList
    },
    mixins: [goodsRepairTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
