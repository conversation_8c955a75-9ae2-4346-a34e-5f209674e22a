import { isNullOrEmpty, isNumber } from '@/libs/util'
import { editStatus, productClassify } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import RepairAndReShipmentBodyExtractPop from '../../components/repair-and-re-shipment-body-extract-pop'

export const repairAndReShipmentBodyList = {
  name: 'repairAndReShipmentBodyList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    RepairAndReShipmentBodyExtractPop
  },
  props: {
    headId: {
      type: String,
      default: () => ('')
    },
    canEdit: {
      type: String,
      default: () => (editStatus.EDIT)
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      pmsLevel: 'body',
      extractConfig: {
        show: false
      },
      hasChildTabs: true,
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'extract': this.handleExtract
      },
      cmbSource: {
        gmark: productClassify.GMARK_SELECT
      }
    }
  },
  watch: {
    headId: {
      immediate: true,
      handler: function () {
        this.handleSearchSubmit()
      }
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions = me.actions.filter(item => item.command !== 'edit')
    },
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.headId
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 120,
        key: 'gmark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmark)
        })
      }, {
        width: 150,
        key: 'entryNo',
        title: '原报关单号'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 180,
        key: 'gname',
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 180,
        key: 'gmodel',
        title: '规格型号',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'unit',
        title: '计量单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.unit)
        })
      }, {
        width: 150,
        key: 'qty',
        title: '数量'
      }, {
        width: 136,
        title: '原单价',
        key: 'decPrice'
      }, {
        width: 120,
        key: 'repairPrice',
        title: '修理费用单价',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          let cmbVal = 0,
            tmpVal = params.row[params.column.key]
          if (isNumber(tmpVal)) {
            cmbVal = Number(tmpVal)
          }
          return h('dc-numberInput', {
            props: {
              precision: 5,
              value: cmbVal,
              integerDigits: 10
            },
            on: {
              'on-change': function (newValue) {
                if (isNumber(newValue)) {
                  params.row[params.column.key] = Number(newValue)
                }
              },
              'on-blur': function () {
                me.rowUpdate(params.row)
              },
              'on-enter': function () {
                me.rowUpdate(params.row)
              }
            }
          }, cmbVal)
        })
      }, {
        width: 136,
        title: '新总价',
        key: 'decTotal'
      }, {
        width: 136,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 220,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          let cmbVal = '',
            tmpVal = params.row[params.column.key]
          if (!isNullOrEmpty(tmpVal)) {
            cmbVal = String(tmpVal).trim()
          }
          return h('Input', {
            props: {
              type: 'text',
              value: cmbVal,
              clearable: true
            },
            on: {
              'on-change': function (e) {
                let changedValue = ''
                if (!isNullOrEmpty(e.target.value)) {
                  changedValue = String(e.target.value).trim()
                }
                params.row[params.column.key] = changedValue
              },
              'on-blur': function () {
                me.rowUpdate(params.row)
              },
              'on-enter': function () {
                me.rowUpdate(params.row)
              }
            }
          }, cmbVal)
        })
      }]
    },
    /**
     * 提取
     */
    handleExtract() {
      let me = this
      me.$set(me.extractConfig, 'show', true)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 数据行更新
     * @param row
     */
    rowUpdate(row) {
      let me = this
      if (!isNullOrEmpty(row.sid)) {
        me.$http.put(me.ajaxUrl.update + '/' + row.sid, row).then(() => {
          me.getList()
        }).catch(() => {
        })
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
