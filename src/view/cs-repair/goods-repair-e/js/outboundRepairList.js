import OutboundRepairTabs from '../outbound-repair-tabs'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import ReturnShipmentPop from '../../components/return-shipment-pop'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const outboundRepairList = {
  name: 'outboundRepairList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    ReturnShipmentPop,
    OutboundRepairTabs
  },
  data() {
    let params = this.getParams()
    return {
      reTrans: {
        sid: '',
        show: false
      },
      baseParams: [
        ...params
      ],
      cmbSource: {
        status: [{
          value: '0', label: '待复运'
        }, {
          value: '1', label: '复运中'
        }, {
          value: '2', label: '已复运'
        }],
        assureType: [{
          value: '0', label: '保金'
        }, {
          value: '1', label: '保函'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'extract': this.handleExtract,
        're-trans': this.handleReTrans
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({type: string, title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{range: boolean, title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'status',
        type: 'select',
        title: '复运状态'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        range: true,
        key: 'declareDate',
        title: '报关单申报日期'
      }, {
        type: 'select',
        title: '担保方式',
        key: 'assureType'
      }, {
        range: true,
        key: 'endDate',
        title: '最迟复运日期'
      }, {
        range: true,
        key: 'completeDate',
        title: '复运完成日期'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 显示列表
     * @returns {{}[]}
     */
    getFields() {
      let me = this
      return [{
        width: 160,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        key: 'status',
        title: '复运状态',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 200,
        key: 'declareCode',
        title: '报关单申报单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'declareCode', 'declareName')
        })
      }, {
        width: 110,
        key: 'declareDate',
        title: '报关单申报日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 150,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 150,
        title: '担保方式',
        key: 'assureType',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.assureType)
        })
      }, {
        width: 150,
        key: 'assurePrice',
        title: '担保金额(RMB)'
      }, {
        width: 110,
        key: 'endDate',
        title: '最迟复运日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 110,
        key: 'completeDate',
        title: '复运完成日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '制单员',
        key: 'userName'
      }, {
        width: 120,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 提取
     */
    handleExtract() {
      let me = this
      me.$http.post(me.ajaxUrl.fetch).then(() => {
        me.$Message.success('数据提取成功!')
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    },
    /**
     * 复运
     */
    handleReTrans() {
      let me = this
      if (me.checkRowSelected('复运', true)) {
        me.$set(me.reTrans, 'show', true)
        me.$set(me.reTrans, 'sid', me.listConfig.selectRows[0].sid)
      }
    },
    /**
     * 执行复运操作
     * @param data
     */
    doReturnShipment(data) {
      let me = this
      me.$http.post(me.ajaxUrl.reTrans, {
        sid: me.reTrans.sid,
        emsListNo: data['emsListNo']
      }).then(() => {
        me.$Message.success('复运设置成功!')
      }).catch(() => {
      }).finally(() => {
        me.$set(me.reTrans, 'sid', '')
        me.$set(me.reTrans, 'show', false)
        me.getList()
      })
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
