import { productClassify } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const outboundRepairBodyList = {
  name: 'outboundRepairBodyList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    headId: {
      type: String,
      default: () => ('')
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      pmsLevel: 'body',
      hasActions: false,
      hasChildTabs: true,
      toolbarEventMap: {},
      listConfig: {
        checkColumnShow: false,
        operationColumnShow: false
      },
      cmbSource: {
        gmark: productClassify.GMARK_SELECT
      }
    }
  },
  watch: {
    headId: {
      immediate: true,
      handler: function () {
        this.handleSearchSubmit()
      }
    }
  },
  methods: {
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.headId
      return paramObj
    },
    getFields() {
      let me = this
      return [{
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 180,
        key: 'gname',
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 150,
        key: 'qty',
        title: '申报数量'
      }, {
        width: 136,
        key: 'decPrice',
        title: '申报单价'
      }, {
        width: 136,
        key: 'decTotal',
        title: '申报总价'
      }, {
        width: 180,
        key: 'gmodel',
        title: '规格型号',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'unit',
        title: '计量单位',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.unit)
        })
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        key: 'gmark',
        title: '物料类型',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.gmark)
        })
      }, {
        width: 136,
        title: '剩余数量',
        key: 'remainQty'
      }, {
        width: 136,
        key: 'useQty',
        title: '已复运数量'
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    }
  }
}
