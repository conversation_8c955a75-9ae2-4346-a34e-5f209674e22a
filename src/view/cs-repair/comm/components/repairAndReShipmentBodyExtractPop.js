import { isNumber } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const repairAndReShipmentBodyExtractPop = {
  name: 'repairAndReShipmentBodyExtractPop',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  props: {
    show: {
      type: Boolean,
      require: true
    },
    headId: {
      type: String,
      default: () => ('')
    },
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {},
      pmsLevel: 'extract',
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'extract-all': this.handleExtractAll,
        'extract-choose': this.handleExtractChoose
      }
    }
  },
  created: function () {
    let me = this
    me.urlSet()
    me.$set(me.listConfig, 'settingColumns', me.getFields())
    me.loadListConfig()
  },
  methods: {
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'gname',
        title: '商品名称'
      }, {
        key: 'codeTS',
        title: '商品编码'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 136,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 136,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 110,
        key: 'declareDate',
        title: '报关单申报日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 180,
        key: 'gname',
        title: '商品名称',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 136,
        title: '总价',
        key: 'decTotal'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        key: 'qty',
        title: '原数量'
      }, {
        width: 120,
        key: 'remainQty',
        title: '剩余数量'
      }, {
        width: 120,
        key: 'extractQty',
        title: '本次复运数',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          let cmbVal = 0,
            tmpVal = params.row[params.column.key]
          if (isNumber(tmpVal)) {
            cmbVal = Number(tmpVal)
          }
          return h('dc-numberInput', {
            props: {
              precision: 5,
              value: cmbVal,
              integerDigits: 10
            },
            on: {
              'on-change': function (newValue) {
                if (isNumber(newValue)) {
                  params.row[params.column.key] = Number(newValue)
                }
              }
            }
          }, cmbVal)
        })
      }, {
        width: 120,
        key: 'repairPrice',
        title: '修理费用单价',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          let cmbVal = 0,
            tmpVal = params.row[params.column.key]
          if (isNumber(tmpVal)) {
            cmbVal = Number(tmpVal)
          }
          return h('dc-numberInput', {
            props: {
              precision: 5,
              value: cmbVal,
              integerDigits: 10
            },
            on: {
              'on-change': function (newValue) {
                if (isNumber(newValue)) {
                  params.row[params.column.key] = Number(newValue)
                }
              }
            }
          }, cmbVal)
        })
      }]
    },
    /**
     * 提取勾选
     */
    handleExtractChoose() {
      let me = this
      if (me.checkRowSelected('提取勾选')) {
        me.setToolbarLoading('extract-choose', true)
        let selected = me.listConfig.selectRows.map(item => {
          return {
            sid: item.sid,
            extractQty: item.extractQty,
            repairPrice: item.repairPrice
          }
        })
        me.$http.post(me.ajaxUrl.extraction, {
          data: selected,
          headId: me.headId
        }).then(() => {
          me.$Message.success('提取成功!')
          me.$emit('doResearch')
          me.$emit('update:show', false)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('extract-choose')
        })
      }
    },
    /**
     * 提取全部
     */
    handleExtractAll() {
      let me = this,
        searchParam = me.getSearchParams()
      me.setToolbarLoading('extract-all', true)
      me.$http.post(me.ajaxUrl.extraction, {
        data: [],
        ...searchParam,
        headId: me.headId
      }).then(() => {
        me.$Message.success('提取成功!')
        me.$emit('doResearch')
      }).catch(() => {
      }).finally(() => {
        me.$emit('update:show', false)
        me.setToolbarLoading('extract-all')
      })
    }
  }
}
