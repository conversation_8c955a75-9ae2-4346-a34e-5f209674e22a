
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'

export const goodsRepairTabs = {
  mixins: [],
  props: {
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false
      },
      currConfig: {
        editData: {},
        editStatus: editStatus.SHOW
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (tabName) {
        let me = this
        me.tabs[tabName] = true
      }
    },
    editConfig: {
      deep: true,
      immediate: true,
      handler: function (config) {
        let me = this
        me.$set(me.currConfig, 'editData', config.editData)
        me.$set(me.currConfig, 'editStatus', config.editStatus)
      }
    }
  },
  methods: {
    /**
     * 表头保存完成
     * @param newConfig
     */
    onHeadSaved(newConfig) {
      let me = this
      me.$set(me.currConfig, 'editData', newConfig.editData)
      me.$set(me.currConfig, 'editStatus', newConfig.editStatus)
    },
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.$emit('onEditBack', {
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    }
  },
  computed: {
    showBody() {
      let me = this
      return me.currConfig.editData && !isNullOrEmpty(me.currConfig.editData.sid)
    }
  }
}
