<template>
  <section class="xdo-enter-root">
    <XdoCard :bordered="false" :title="attachTitle" class="ieLogisticsTrackingCard">
      <div v-show="canModify" style="padding: 0; margin: 0;">
        <XdoForm ref="attachForm" :show-message="false" :model="attachForm">
          <table class="frmTable">
            <tr>
              <td>
                <XdoIInput type="text" disabled :value="fileName" style="width: 100%;"></XdoIInput>
                <input ref="file" v-if="showFileInput" @change="fileChanged" type="file" style="display: none;" />
              </td>
              <td style="width: 40px;"><XdoButton @click="handleSelectFile">浏览</XdoButton></td>
              <td style="width: 40px;"><XdoButton type="warning" @click="handleSaveAttach">上传</XdoButton></td>
            </tr>
          </table>
        </XdoForm>
      </div>
      <XdoTable :height="tableHeight" size="small" stripe :columns="grdColumns" :data="dataAcmpSource" border style="margin-top: 2px;"></XdoTable>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import acmpColumns from './attachmentUploadingColumns'
  import { getHttpHeaderFileName, blobSaveFile } from '@/libs/util'

  export default {
    name: 'attachmentUploading',
    mixins: [acmpColumns],
    props: {
      sid: {
        type: String,
        require: true
      },
      isEdit: {
        type: Boolean,
        require: true
      },
      attachTitle: {
        type: String,
        default: '附件上传'
      },
      height: {
        type: [Number, String],
        default: '-'
      }
    },
    data() {
      return {
        fileName: '',
        attachForm: {},
        selectedFile: null,
        editForm: {
          businessType: 'DEV',  // 单证类型：必填，字符型，长度1位（2-手册、3-报核、4－清单、5－质疑
          acmpNo: '',
          acmpFormat: '',
          acmpType: '',
          billSerialNo: '',
          businessSid: ''
        },
        dataAcmpSource: [],
        grdColumns: [],
        showFileInput: true
      }
    },
    watch: {
      sid: function (val) {
        this.editForm.businessSid = val
        this.loadData()
      },
      canModify: {
        immediate: true,
        handler: function (canModify) {
          let me = this
          if (canModify) {
            me.$set(me, 'grdColumns', me.acmpColumns.editColumns)
          } else {
            me.$set(me, 'grdColumns', me.acmpColumns.viewColumns)
          }
        }
      }
    },
    mounted() {
      let me = this
      if (!isNullOrEmpty(me.sid)) {
        me.editForm.businessSid = me.sid
        me.loadData()
      }
    },
    methods: {
      loadData() {
        let me = this
        me.$set(me, 'showFileInput', false)
        me.$http.post(csAPI.attachedInfo.list, {
          businessSid: me.sid
        }).then(res => {
          me.dataAcmpSource = res.data.data
        }).catch(() => {
        }).finally(() => {
          me.$set(me, 'showFileInput', true)
        })
      },
      fileChanged(e) {
        let me = this
        if (e.target.files[0] === undefined)
          return
        me.selectedFile = e.target.files[0]
        if (me.selectedFile) {
          me.fileName = me.selectedFile.name
        } else {
          me.fileName = ''
        }
      },
      handleSelectFile() {
        let me = this
        if (me.$refs['file']) {
          const event = new MouseEvent('click')
          me.$refs['file'].dispatchEvent(event)
        }
      },
      handleSaveAttach() {
        let me = this
        if (!me.selectedFile) {
          me.$Message.error('请选择上传的附件')
          return
        }
        const fd = new FormData();
        fd.append('file', me.selectedFile)
        for (let val in me.editForm) {
          if (me.editForm.hasOwnProperty(val)) {
            fd.append(val, me.editForm[val])
          }
        }
        me.$http.post(csAPI.attachedInfo.insert, fd, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then(() => {
          me.loadData()
          me.$Message.success('上传成功')
          me.$refs['attachForm'].resetFields()
          me.fileName = ''
          me.selectedFile = null
        }).catch(() => {
        })
      },
      removeAttach(params) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            const sids = params.row.sid
            me.$http.delete(`${csAPI.attachedInfo.delete}/${sids}`).then(() => {
              me.$Message.success('删除成功!')
              me.loadData()
              me.$refs['attachForm'].resetFields()
              me.fileName = ''
              me.selectedFile = null
            }).catch(() => {
            })
          }
        })
      },
      downloadFile(sysId) {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.get}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          const name = getHttpHeaderFileName(res.headers)
          const blob = new Blob([res.data], {type: 'application/octet-stream'})
          blobSaveFile(blob, name)
        })
      }
    },
    computed: {
      canModify() {
        return !isNullOrEmpty(this.sid) && this.isEdit
      },
      tableHeight() {
        if (typeof this.height === 'number') {
          if (this.canModify) {
            return this.height - 28
          } else {
            return this.height
          }
        }
        return '-'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-tip {
    min-height: 100px;
  }

  .frmTable {
    margin: 0;
    width: 100%;
    padding: 1px 0;
    display: table;
    border-color: grey;
    border-spacing: 1px;
    border-collapse: separate;
  }

  .frmTable tr, .frmTable td {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  /deep/ .ieLogisticsTrackingCard .ivu-card-body{
    padding: 8px;
  }
</style>
