<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="capitalNo" label="资产编号">
        <XdoIInput type="text" v-model="searchParam.capitalNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="useAddress" label="使用地点">
        <XdoIInput type="text" v-model="searchParam.useAddress"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="使用日期" @onDateRangeChanged="handleValidUseDateChange"></dc-dateRange>
      <XdoFormItem prop="useStatus" label="使用状态">
        <xdo-select v-model="searchParam.useStatus" :options="this.taxExemptionEquipment.USE_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeStatus" label="监管状态">
        <xdo-select v-model="searchParam.tradeStatus" :options="this.taxExemptionEquipment.REGULATORY_STATUS_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="depositaryOfficer" label="保管负责人">
        <XdoIInput type="text" v-model="searchParam.depositaryOfficer"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="申报日期" @onDateRangeChanged="handleValidDeclareDateChange"></dc-dateRange>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :meta="pcode.trade"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gmodel" label="规格型号">
        <XdoIInput type="text" v-model="searchParam.gmodel"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="codeTS" label="商品编码">
        <XdoIInput type="text" v-model="searchParam.codeTS"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="放行日期" @onDateRangeChanged="handleValidPassDateChange"></dc-dateRange>
      <XdoFormItem prop="gmark" label="物料类型">
        <xdo-select v-model="searchParam.gmark" :options="this.erpInterfaceData.MAT_FLAG_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <XdoIInput type="text" v-model="searchParam.emsNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="itemNo" label="项目确认书编号">
        <XdoIInput type="text" v-model="searchParam.itemNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="监管有效期" @onDateRangeChanged="handleValidTradeValidDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { erpInterfaceData, taxExemptionEquipment } from '@/view/cs-common'

  export default {
    name: 'usageRecordSearch',
    data() {
      return {
        cmbSource: {},
        searchParam: {
          capitalNo: '',
          useAddress: '',
          useDateFrom: '',
          useDateTo: '',
          useStatus: '',
          tradeStatus: '',
          depositaryOfficer: '',
          emsListNo: '',
          entryNo: '',
          declareDateFrom: '',
          declareDateTo: '',
          tradeMode: '',
          gname: '',
          gmodel: '',
          codeTS: '',
          passDateFrom: '',
          passDateTo: '',
          gmark: '',
          emsNo: '',
          itemNo: '',
          tradeValidDateFrom: '',
          tradeValidDateTo: ''
        },
        erpInterfaceData: erpInterfaceData,
        taxExemptionEquipment: taxExemptionEquipment
      }
    },
    methods: {
      /**
       * 使用日期
       */
      handleValidUseDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "useDateFrom", values[0])
          this.$set(this.searchParam, "useDateTo", values[1])
        } else {
          this.$set(this.searchParam, "useDateFrom", '')
          this.$set(this.searchParam, "useDateTo", '')
        }
      },
      /**
       * 申报日期
       */
      handleValidDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      },
      /**
       * 放行日期
       * @param values
       */
      handleValidPassDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "passDateFrom", values[0])
          this.$set(this.searchParam, "passDateTo", values[1])
        } else {
          this.$set(this.searchParam, "passDateFrom", '')
          this.$set(this.searchParam, "passDateTo", '')
        }
      },
      /**
       * 监管有效期
       * @param values
       */
      handleValidTradeValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "tradeValidDateFrom", values[0])
          this.$set(this.searchParam, "tradeValidDateTo", values[1])
        } else {
          this.$set(this.searchParam, "tradeValidDateFrom", '')
          this.$set(this.searchParam, "tradeValidDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
