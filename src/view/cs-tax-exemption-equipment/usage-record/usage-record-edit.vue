<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-5 ieLogisticsTrackingCard" title="报关记录">
        <div class="dc-form dc-form-4" style="padding-right: 10px;">
          <XdoFormItem prop="emsListNo" label="单据内部编号">
            <XdoIInput type="text" v-model="frmData.emsListNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="entryNo" label="报关单号">
            <XdoIInput type="text" v-model="frmData.entryNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="declareDate" label="申报日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.declareDate" disabled></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="tradeMode" label="监管方式">
            <xdo-select v-model="frmData.tradeMode" :meta="pcode.tradeMode" disabled
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="gname" label="商品名称">
            <XdoIInput type="text" v-model="frmData.gname" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="codeTS" label="商品编码">
            <XdoIInput type="text" v-model="frmData.codeTS" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gmodel" label="申报规格型号" class="dc-merge-3-5">
            <XdoIInput type="text" v-model="frmData.gmodel" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="qty" label="数量">
            <xdo-input v-model="frmData.qty" decimal int-length="11" precision="5" disabled></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decTotal" label="金额">
            <xdo-input v-model="frmData.decTotal" decimal int-length="11" precision="5" disabled></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select v-model="frmData.curr" :meta="pcode.curr_outdated" disabled
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="unit" label="单位">
            <xdo-select v-model="frmData.unit" :meta="pcode.unit" disabled
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="gmark" label="物料类型">
            <xdo-select v-model="frmData.gmark" :options="this.erpInterfaceData.MAT_FLAG_MAP" disabled
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="passDate" label="放行日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.passDate" disabled></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="emsNo" label="备案号">
            <XdoIInput type="text" v-model="frmData.emsNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="itemNo" label="项目确认书编号">
            <XdoIInput type="text" v-model="frmData.itemNo" disabled></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <XdoCard :bordered="false" class="dc-merge-1-5 ieLogisticsTrackingCard" style="margin-top: 0;" title="使用记录">
        <div class="dc-form dc-form-4" style="padding-right: 10px;">
          <XdoFormItem prop="capitalNo" label="资产编号">
            <XdoIInput type="text" v-model="frmData.capitalNo" :maxlength="30" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="useAddress" label="使用地点">
            <XdoIInput type="text" v-model="frmData.useAddress" :maxlength="30" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="tradeStatus" label="监管状态">
            <xdo-select v-model="frmData.tradeStatus" :options="this.taxExemptionEquipment.REGULATORY_STATUS_MAP"
                        :optionLabelRender="pcodeRender" disabled></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeValidDate" label="监管有效期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.tradeValidDate" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="useDate" label="使用日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.useDate" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="useStatus" label="使用状态">
            <xdo-select v-model="frmData.useStatus" :options="this.taxExemptionEquipment.USE_STATUS_MAP"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="depositaryOfficer" label="保管负责人">
            <XdoIInput type="text" v-model="frmData.depositaryOfficer" :maxlength="10" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { convertToDate } from '@/libs/datetime'
  import { equipmentEdit } from '../js/comm/equipmentEdit'
  import { erpInterfaceData, taxExemptionEquipment } from '@/view/cs-common'

  export default {
    name: 'usageRecordEdit',
    mixins: [equipmentEdit],
    data() {
      return {
        rulesHeader: {},
        formName: 'dataForm',
        erpInterfaceData: erpInterfaceData,
        taxExemptionEquipment: taxExemptionEquipment,
        ajaxUrl: {
          insert: csAPI.taxExemptionEquipment.usageRecord.insert,
          update: csAPI.taxExemptionEquipment.usageRecord.update
        }
      }
    },
    watch: {
      'frmData.tradeValidDate': {
        immediate: true,
        handler: function (validDate) {
          let me = this
          me.$set(me.frmData, 'tradeStatus', '1')
          if (isNullOrEmpty(validDate)) {
            me.$set(me.frmData, 'tradeStatus', '0')
          } else {
            try {
              let theTime = convertToDate(validDate),
                theTimeInt = theTime.getTime(),
                nowInt = (new Date()).getTime()
              if (theTimeInt >= nowInt) {
                me.$set(me.frmData, 'tradeStatus', '0')
              }
            } catch (e) {
              console.error(e.message)
            }
          }
        }
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          // 不可编辑
          emsListNo: '',
          entryNo: '',
          declareDate: '',
          tradeMode: '',
          gname: '',
          codeTS: '',
          gmodel: '',
          qty: '',
          decTotal: '',
          curr: '',
          unit: '',
          gmark: '',
          passDate: '',
          emsNo: '',
          itemNo: '',
          // 可编辑
          capitalNo: '',
          useAddress: '',
          tradeStatus: '',
          tradeValidDate: '',
          useDate: '',
          useStatus: '1',
          depositaryOfficer: ''
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }
</style>
