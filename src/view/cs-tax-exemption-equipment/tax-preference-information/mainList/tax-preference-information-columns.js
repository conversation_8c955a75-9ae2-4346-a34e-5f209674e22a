import { getKeyValue } from '@/libs/util'
import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'exemptsNo'
  ,'tmpNo'
  ,'seqNo'
  ,'projectId'
  ,'status'
  , 'emsListNo'
  , 'billTypeName'
  , 'apprFromName'
  , 'iemark'
  , 'cutMode'
  , 'apprItemName'
  , 'apprDeptName'
  , 'licenseNo'
  , 'transMode'
  , 'itemMode'
  , 'isDeclare'
  , 'validDate'
  , 'masterCustoms'
  , 'declareCustoms'
  , 'linkMan'
  , 'linkManTel'
  ,'contactCellphone'
  , 'taxAssureReason'
  , 'supplierCode'
  , 'isEnabled'
  , 'entryNo'
  , 'insertTime'
  , 'insertUser'
  , 'note'
  , 'itemNo'
  , 'entryPort'
  , 'declareCode'
  , 'tradeCode'
  , 'tradeName'
  , 'assureNo'
  , 'contrNo'
  , 'erpEmsListNo'
  ,'receiptStatus'
  ,'projectFund'
  ,'entrustedCoScc'
  ,'applType'
  ,'businessType'
  ,'hasSpecFile'
  ,'comCoScc'
  ,'applyCoScc'
  ,'mtype'
  ,'apprStatus'
  ,'apprStatusName'
  ,'applyAddress'
  ,'enterpriseType'
  ,'useAddress'
  ,'entryStatus'
  ,'entrySeqNo'
  ,'entryDeclareDate'
  ,'hwab'
  ,'batchNo'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '内审状态',
          key: 'apprStatus',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.statusList, params.row.apprStatus))
          }
        },
        {
          title: '发送状态',
          key: 'status',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.sendstatusList, params.row.status))
          }
        },
        {
          title: '免表海关编号',
          key: 'exemptsNo',
          align: 'center',
          minWidth: 120,
          cellClassRules: {
            'demo-table-error-row': (params) => { return params.data.exemptsNo !== null }
          }
        },
        {
          title: '暂存单编号',
          key: 'tmpNo',
          align: 'center',
          minWidth: 120,
        },
        {
          title: '中心统一编号',
          key: 'seqNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '项目信息编号',
          key: 'projectId',
          align: 'center',
          minWidth: 120,
        },
        {
          title: '企业内部编号',
          key: 'emsListNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '业务种类',
          key: 'billTypeName',
          align: 'center',
          minWidth: 120,
        },
        {
          minWidth: 120,
          align: 'center',
          title: '政策依据',
          key: 'apprFromName'
        },
        {
          minWidth: 120,
          key: 'iemark',
          align: 'center',
          title: '进(出)口标志',
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.iemark, params.row.iemark))
          }
        },
        {
          title: '征免性质',
          key: 'cutMode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.levytype, params.row.cutMode), params.row.cutMode))
          }
        },
        {
          title: '项目确认书编号',
          key: 'itemNo',
          align: 'center',
          minWidth: 120,
        },
        {
          title: '产业政策审批条目',
          key: 'apprItemName',
          align: 'center',
          minWidth: 120,
        },
        {
          title: '审批部门',
          key: 'apprDeptName',
          align: 'center',
          minWidth: 120,
        },
        {
          title: '许可证号',
          key: 'licenseNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '企业代码',
          key: 'tradeCode',
          align: 'center',
          minWidth: 120
        },
        {
          title: '企业名称',
          key: 'tradeName',
          align: 'center',
          minWidth: 120
        },
        {
          title: '成交方式',
          key: 'transMode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transac, params.row.transMode), params.row.transMode))
          }
        },
        {
          title: '项目性质',
          key: 'itemMode',
          align: 'center',
          minWidth: 120
        },
        {
          title: '进（出）口岸',
          key: 'entryPort',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.entryPort), params.row.entryPort))
          }
        },
        {
          title: '是否已申报进口',
          key: 'isDeclare',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.isDeclare, params.row.isDeclare))
          }
        },
        {
          title: '担保编号',
          key: 'assureNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '有效日期',
          key: 'validDate',
          align: 'center',
          minWidth: 120
        },
        {
          title: '主管海关',
          key: 'masterCustoms',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.masterCustoms), params.row.masterCustoms))
          }
        },
        {
          title: '报关单申报地海关',
          key: 'declareCustoms',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.declareCustoms), params.row.declareCustoms))
          }
        },
        {
          title: '联系人',
          key: 'linkMan',
          align: 'center',
          minWidth: 120
        },
        {
          title: '电话',
          key: 'linkManTel',
          align: 'center',
          minWidth: 120
        },
        {
          title: '手机',
          key: 'contactCellphone',
          align: 'center',
          minWidth: 120
        },
        {
          title: '税款担保原因',
          key: 'taxAssureReason',
          align: 'center',
          minWidth: 120
        },
        {
          title: '供应商',
          key: 'supplierCode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.tradeCodeData, params.row.supplierCode))
          }
        },
        {
          title: '受托单位',
          key: 'declareCode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.declareData, params.row.declareCode))
          }
        },
        {
          title: '是否有效',
          key: 'isEnabled',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.isEnabled, params.row.isEnabled))
          }
        },
        {
          title: '录入人',
          key: 'insertUser',
          align: 'center',
          minWidth: 120
        },
        {
          title: '录入日期',
          key: 'insertTime',
          align: 'center',
          minWidth: 120
        },
        {
          title: '报关单号',
          key: 'entryNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '表头备注',
          key: 'note',
          align: 'center',
          minWidth: 120
        },
        {
          title: '合同号',
          key: 'contrNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '单据内部编号',
          key: 'erpEmsListNo',
          align: 'center',
          minWidth: 120
        },
        {
          title: '免表类型',
          key: 'mtype',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.mtype, params.row.mtype))
          }
        },
        {
          title: '申请人统一社会信用代码',
          key: 'applyCoScc',
          align: 'center',
          minWidth: 250
        },
        {
          title: '收发货人统一社会信用代码',
          key: 'comCoScc',
          align: 'center',
          minWidth: 250
        },
        {
          title: '状况报告书递交标志',
          key: 'hasSpecFile',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.hasSpecFile, params.row.hasSpecFile))
          }
        },
        {
          title: '申请人市场主体类型/代码',
          key: 'businessType',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.businessType, params.row.businessType,false))
          }
        },
        {
          title: '申请形式',
          key: 'applType',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.applType, params.row.applType))
          }
        },
        {
          title: '受托单位统一社会信用代码',
          key: 'entrustedCoScc',
          align: 'center',
          minWidth: 250
        },
        {
          title: '项目资金性质',
          key: 'projectFund',
          align: 'center',
          minWidth: 150
        },
        {
          title: '回执状态',
          key: 'receiptStatus',
          align: 'center',
          minWidth: 150,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.receiptStatus, params.row.receiptStatus))
          }
        },
        {
          title: '申请人所在地',
          key: 'applyAddress',
          align: 'center',
          minWidth: 150
        },
        {
          title: '申请人种类/代码',
          key: 'enterpriseType',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.enterpriseType, params.row.enterpriseType,false))
          }
        },
        {
          title: '使用地点',
          key: 'useAddress',
          align: 'center',
          minWidth: 150
        },
        {
          title: '报关状态',
          key: 'entryStatus',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.cmbSource.entryStatus, params.row.entryStatus))
          }
        },
        {
          title: '报关单统一编号',
          key: 'entrySeqNo',
          align: 'center',
          minWidth: 150
        },
        {
          title: '概要申报日期',
          key: 'entryDeclareDate',
          align: 'center',
          minWidth: 150
        },
        {
          title: '提运单号',
          key: 'hwab',
          align: 'center',
          minWidth: 150
        },
        {
          title: '批次号',
          key: 'batchNo',
          align: 'center',
          minWidth: 150
        }/*,
        {
          title: '申报地海关',
          key: 'entryCustoms',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.customs_rel, params.row.entryCustoms), params.row.entryCustoms))
          }
        },
        {
          title: '运输方式',
          key: 'trafMode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.transf, params.row.trafMode), params.row.trafMode))
          }
        },
        {
          title: '运输工具名称',
          key: 'trafName',
          align: 'center',
          minWidth: 150
        },
        {
          title: '航次号',
          key: 'voyageNo',
          align: 'center',
          minWidth: 150
        },
        {
          title: '提运单号',
          key: 'hwab',
          align: 'center',
          minWidth: 150
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.trade, params.row.tradeMode), params.row.tradeMode))
          }
        },
        {
          title: '毛重(KG)',
          key: 'grossWt',
          align: 'center',
          minWidth: 150
        }*/
      ]
    }
  }
}

export {
  columns,
  columnsConfig,
  excelColumnsConfig
}
