<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="企业内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="exemptsNo" label="免表编号">
        <XdoIInput type="text" v-model="searchParam.exemptsNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="isEnabled" label="是否有效">
        <xdo-select v-model="searchParam.isEnabled" :options="cmbSource.isEnabled" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="发送状态">
        <xdo-select v-model="searchParam.status" :options="cmbSource.sendstatusList" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareCode" label="受托单位">
        <xdo-select v-model="searchParam.declareCode" :options="cmbSource.declareData" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="isDeclare" label="是否已申报">
        <xdo-select v-model="searchParam.isDeclare" :options="cmbSource.isDeclare" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryStatus" label="报关状态">
        <xdo-select v-model="searchParam.entryStatus" :options="cmbSource.entryStatus" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="validDateFrom" label="有效日期">
        <Row>
          <Col span="11">
            <XdoDatePicker type="date" v-model="searchParam.validDateFrom" placeholder="有效日期--开始"
                           @on-change="searchParam.validDateFrom=$event"></XdoDatePicker>
          </Col>
          <Col span="2" style="text-align: center;">-</Col>
          <Col span="11">
            <XdoDatePicker type="date" v-model="searchParam.validDateTo" placeholder="有效日期--结束"
                           @on-change="searchParam.validDateTo=$event"></XdoDatePicker>
          </Col>
        </Row>
      </XdoFormItem>
      <XdoFormItem prop="status" label="回执状态">
        <xdo-select v-model="searchParam.receiptStatus" :options="cmbSource.RECEIPT_STATUS" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="录入日期" @onDateRangeChanged="handleApprDateChange"></dc-dateRange>
      <XdoFormItem prop="linkMan" label="联系人">
        <XdoIInput type="text" v-model="searchParam.linkMan"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>

  import { taxPreference,importExportManage } from '@/view/cs-common'
  import { csAPI } from '@/api'

  export default {
    name: 'taxPreferenceInformationSearch',
    data() {
      return {
        searchParam: {
          isDeclare: '',
          isEnabled: '',
          exemptsNo: '',
          contrNo: '',
          emsListNo: '',
          validDateFrom: '',
          validDateTo: '',
          entryNo: '',
          declareCode: '',
          status: '',
          receiptStatus: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          linkMan: '',
          batchNo: '',
        },
        cmbSource: {
          isDeclare: taxPreference.IS_DECLARE_MAP,
          isEnabled: taxPreference.IS_ENABLED_MAP,
          sendstatusList: taxPreference.SEND_STATUS_MAP,
          RECEIPT_STATUS: taxPreference.RECEIPT_STATUS,
          declareData: [],
          entryStatus:[...importExportManage.twoStepsSendStatus,...importExportManage.entryStatus]
        }
      }
    },
    created() {
      let me = this
      // 获取报关行
      me.$http.post(csAPI.ieParams.CUT).then(res => {
        me.cmbSource.declareData = res.data.data.map(item => {
          return {
            label: item['LABEL'],
            value: item['CODE']
          }
        })
      }).catch(() => {
        me.cmbSource.declareData = []
      })
      //me.getFreeParams()
      //me.getItemNo()
    },
    methods: {
      /**
       * 制单日期
       * @param values
       */
      handleApprDateChange(values){
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }

    },
  }
</script>
