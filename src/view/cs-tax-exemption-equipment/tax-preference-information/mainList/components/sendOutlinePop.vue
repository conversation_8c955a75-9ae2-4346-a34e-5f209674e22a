<template>
  <Modal v-model="show" width="820" class-name="vertical-center-modal" title="发送概要申报" :closable="false"
         :mask-closable="false">
    <XdoForm ref="editForm" :model="formModel" :label-width="130" :rules="formRules" class="dc-form dc-form-2">
      <XdoFormItem prop="entryCustoms" label="申报地海关">
        <xdo-select v-model="formModel.entryCustoms" :asyncOptions="pcodeList" transfer
                    :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="formModel.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafName" label="运输工具名称">
        <XdoIInput type="text" v-model="formModel.trafName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="voyageNo" label="航次号">
        <XdoIInput type="text" v-model="formModel.voyageNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="hwab" label="提运单号">
        <XdoIInput type="text" v-model="formModel.hwab"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="formModel.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="grossWt" label="毛重">
        <xdo-input v-model="formModel.grossWt" int-length="13" precision="5" decimal></xdo-input>
      </XdoFormItem>
      <XdoFormItem prop="cardMark" label="是否涉证">
        <xdo-select v-model="formModel.cardMark"
                    :options="this.productClassify.CREDENTIAL_FLAG" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="checkMark" label="是否涉检">
        <xdo-select v-model="formModel.checkMark"
                    :options="this.productClassify.CREDENTIAL_FLAG" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="taxMark" label="是否涉税">
        <xdo-select v-model="formModel.taxMark"
                    :options="this.productClassify.CREDENTIAL_FLAG" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryDeclareCode" label="报关单申报单位">
        <xdo-select v-model="formModel.entryDeclareCode" :options="cmbSource.declareData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryDeclareName" label="报关单申报单位名称">
        <XdoIInput type="text" v-model="formModel.entryDeclareName" disabled="true"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryDeclareScc" label="报关单申报单位信用代码">
        <XdoIInput type="text" v-model="formModel.entryDeclareScc" disabled="true"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
    <div slot="footer">
      <Button type="text" @click="handleClose">取消申报</Button>
      <Button type="primary" @click="handleSend" :loading="loading">确认申报</Button>
    </div>
  </Modal>
</template>
<script>

  import { csAPI } from '@/api'
  import { productClassify } from '@/view/cs-common'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'sendOutlinePop',
    props: {
      headId: {
        type: String
      },
      show: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        formModel: {
          entryCustoms: '',
          trafMode: '',
          trafName: '',
          voyageNo: '',
          hwab: '',
          grossWt: null,
          cardMark: '',
          checkMark: '',
          taxMark: '',
          tradeMode: '',
          entryDeclareCode: '',
          entryDeclareName: '',
          entryDeclareScc: ''
        },
        cmbSource: {
          declareData: []
        },
        productClassify: productClassify,
        formRules: {
          entryCustoms: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          trafMode: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          tradeMode: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          grossWt: [{ type: 'number', required: true, message: '不能为空！', trigger: 'blur' }],
          cardMark: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          checkMark: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          taxMark: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          entryDeclareCode: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          entryDeclareName: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          entryDeclareScc: [{ required: true, message: '不能为空！', trigger: 'blur' }]
        }
      }
    },
    methods: {
      handleClose() {
        this.$emit('onClose', false)
      },
      handleSend() {
        this.$refs['editForm'].validate().then(isValid => {
          if (isValid) {
            this.sendOutline()
          }
        })
      },
      sendOutline() {
        this.loading = true
        const param = Object.assign({}, this.formModel)
        this.$http.post(csAPI.tax.list.sendOutline + '/' + this.headId, param)
          .then((res) => {
            if (res.data.success) {
              this.$Message.info('发送成功！')
              this.$emit('onClose', false)
            } else {
              this.$Message.error(res.message)
            }
          }).finally(() => {
          this.loading = false
        })
      }
    },
    mounted() {
      this.$http.post(csAPI.ieParams.CUT).then(res => {
        this.cmbSource.declareData = res.data.data.map(item => {
          return {
            label: item['LABEL'],
            value: item['CODE']
          }
        })
      }).catch(() => {
        me.cmbSource.declareData = []
      })
    },
    watch: {
      'formModel.entryDeclareCode': {
        immediate: true,
        handler: function(val) {
          if (!isNullOrEmpty(val)) {
            this.formModel.entryDeclareName = this.cmbSource.declareData.filter(x => {
              return x.value === val
            })[0].label
            this.$http.post(csAPI.ieParams.getCreditCode + `/${val}`).then(res => {
              this.formModel.entryDeclareScc = res.data.data
            }).catch(() => {
            })
          }
        }
      }
    }
  }
</script>
<style scoped>
</style>

