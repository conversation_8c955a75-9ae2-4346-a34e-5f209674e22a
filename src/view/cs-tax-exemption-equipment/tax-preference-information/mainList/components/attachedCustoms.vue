<template>
  <section>
    <div v-if="showToolbar" class="action" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      <div style="white-space: nowrap; display: inline-flex; font-weight: bold; width: 280px;">
        当前状态: {{attachStatus}}
      </div>
    </div>
    <XdoCard :bordered="false">
      <div class="form-title-wrapper">
        <p><span>单据内部编号</span></p>
      </div>
      <div class="form-title-body-wrapper">
        <RadioGroup type="button" v-model="emsListNo">
          <Radio :label="edit.editData.emsListNo"></Radio>
        </RadioGroup>
      </div>
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" stripe border>
        <template slot-scope="{ row }" slot="attachNoSlot">
          <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
            {{ attachNoListItem.attachNo }}
          </div>
          <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
            {{ attachNoListItem.orderNo }}
          </div>
          <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index" style="margin: 0; padding: 0;">
            <ul>
              <li v-for="item of attachNoListItem.origin" :key="item" style="margin: 0; width: 100%; color: black;">
                {{ item }}
              </li>
            </ul>
          </div>
        </template>
        <template slot-scope="{ row }" slot="attachDataSlot">
          <!--标准格式-->
          <template>
            <div class="filesList">
              <span>附件</span>
              <Upload v-show="buttonIsShow && row.canUpload" :show-upload-list="false" multiple
                      :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                      :on-format-error="handleFormatError" :on-exceeded-size="handleExceededSize"
                      :before-upload="handleBeforeUpload" :on-error="handleOnError" :on-success="handleOnSuccess">
                <strong>
                  <a icon="md-cloud-upload" long @click.prevent="onUpAttach(row.attachType, '')">
                    <XdoIcon type="ios-loop-strong"></XdoIcon>
                    [上传]
                  </a>
                </strong>
              </Upload>
              <ul>
                <li v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
                  <div v-for="(attachFileItem, index) of attachNoListItem.attachFiles" :key="attachFileItem.fileSid">
                    <span :title="attachFileItem.fileName">
                      <XdoIcon title="删除" type="md-close" v-show="buttonIsShow" style="cursor: pointer;"
                               @click="onDeleteFile(attachFileItem.fileSid, index, row.attachType, attachNoListItem.attachNo)" />
                      <a @click.prevent="downloadFile(attachFileItem.fileSid)">{{attachFileItem.fileName}}</a>
                    </span>
                  </div>
                </li>
              </ul>
            </div>
          </template>
        </template>
      </XdoTable>
    </XdoCard>
    <UniversalProgress :show.sync="uploadProgressShow" :completed="uploadCompleted" :failure="uploadFailure"></UniversalProgress>
    <dc-file-preview-pop :show.sync="filePreview.show" :file-data="filePreview.fileData"></dc-file-preview-pop>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, getHttpHeaderFileName } from '@/libs/util'
  import UniversalProgress from '@/components/universal-progress/universal-progress'

  export default {
    name: 'AttachCustoms',
    mixins: [pms],
    props: {
      edit: {
        type: Object,
        default: () => ({})
      },
      aeoShow: {
        type: Boolean,
        default: false
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      loaded: {
        type: Boolean,
        default: false
      }
    },
    components: {
      UniversalProgress
    },
    data() {
      return {
        timer: null,
        actions: [],
        emsListNo: this.edit.editData.emsListNo,
        attachTypeEnum: {
          invoiceNo_1: 'invoiceNo_1',
          pactCode_4: 'pactCode_4',
          other_5: 'other_5',
          other_6: 'other_6',
          other_7: 'other_7',
          apply_9: 'apply_9',
          attorney_10: 'attorney_10',
          situation_11: 'situation_11',
          prove_12: 'prove_12'
        },
        gridConfig: {
          gridColumns: [{
            title: '单证类型',
            width: 120,
            align: 'center',
            key: 'attachTypeName'
          },{
            title: '文件',
            align: 'left',
            slot: 'attachDataSlot',
            renderHeader: (h)  => {
              return h('div',[
                h('span', {
                  style: {color: '#ff0000'}
                }, '文件(只可上传后缀名为pdf的文件，单个文件大小不能超过3.5M,且每页不超过400K)'),
              ])
            },
          }],
          data: []
        },
        ajaxUrl: {
          lockDocAccompanying: '',
          unlockDocAccompanying: '',
          download: csAPI.attachedInfo.get,
          upload: csAPI.attachedInfo.insert3M,
          delete: csAPI.attachedInfo.delete,
          getAttachList: csAPI.attachedInfo.list,
          loadAttachTypeUrl: csAPI.enterpriseParamsLib.comm.selectAllPaged
        },
        uploadInfo: {
          acmpNo: '',
          acmpType: ''
        },
        tmpAttachData: [],
        uploadFailure: false,
        uploadCompleted: false,
        uploadProgressShow: false,
        filePreview: {
          show: false,
          fileData: []
        },
        toolbarEventMap: {
          'lock': this.handleLock,
          'unlock': this.handleUnLock
        },
        isLocked: false
      }
    },
    created() {
      let me = this
      me.loadAttachTypeConfig()
      me.loadFunctions('attach')

    },
    mounted() {
      let me = this
      me.$nextTick(() => {
        me.loadAttach()
      })
    },
    watch: {
      loaded: {
        immediate: true,
        handler: function (loaded) {
          let me = this
          me.$set(me, 'isLocked', loaded)
        }
      }
    },
    methods: {
      /**
       * 根据选中值及所有值配置table内容
       * @param selected
       * @param fullTypes
       */
      setTableData(selected, fullTypes) {
        let me = this,
          columns = []
        if (Array.isArray(selected) && selected.length > 0 && Array.isArray(fullTypes) && fullTypes.length > 0) {
          let attachItems = [],
            attachCheckType = []
            attachCheckType = 'invoiceNo_1,pactCode_4,other_5,other_6,other_7,apply_9,attorney_10,situation_11,prove_12'.split(',')
          selected.forEach(attachType => {
            attachItems = fullTypes.filter(attachItem => {
              return attachItem.label === attachType
            })
            if (attachItems.length > 0) {
              columns.push({
                attachNoList: [],
                attachType: attachItems[0].label,
                attachTypeName: attachItems[0].title,
                canUpload: attachCheckType.includes(attachItems[0].label)
              })
            }
          })
        }
        me.$set(me.gridConfig, 'data', columns)
      },
      /**
       * 加载随附单据类型的配置信息
       */
      loadAttachTypeConfig() {
        let me = this,
          selected = [],
          fullTypes = [],
          configExists = true,
          config = '{ "selected" : "invoiceNo_1,pactCode_4,other_5,other_6,other_7,apply_9,attorney_10,situation_11,prove_12","fullTypes" :[{ "label" : "invoiceNo_1","title" : "发票" },{ "label" : "pactCode_4","title" : "合同" },{ "label" : "other_5","title" : "其他" },{ "label" : "other_6","title" : "其他" },{ "label" : "other_7","title" : "其他" },{ "label" : "apply_9","title" : "申请表" },{ "label" : "attorney_10","title" : "委托书及代理协议" },{ "label" : "situation_11","title" : "情况说明" },{ "label" : "prove_12","title" : "相关证明文件" }]}'
        if (!isNullOrEmpty(config)) {
          try {
            let configObj = JSON.parse(config)
            if (configObj.hasOwnProperty('selected') && !isNullOrEmpty(configObj['selected'])) {
              selected = configObj['selected'].split(',')
            } else {
              configExists = false
            }
            if (configObj.hasOwnProperty('fullTypes') && Array.isArray(configObj['fullTypes'])) {
              fullTypes = configObj['fullTypes']
            } else {
              configExists = false
            }
          } catch (e) {
            console.info(e.message)
            selected = []
            fullTypes = []
            configExists = false
          }
        } else {
          configExists = false
        }
        if (configExists === false) {
          me.$http.post(me.ajaxUrl.loadAttachTypeUrl, {
            paramsType: 'DOC_TYPE'
          }).then(res => {
            fullTypes = res.data.data.map(item => {
              return {
                label: item.paramsCode,
                title: item.paramsName
              }
            })
            selected = res.data.data.map(item => {
              return item.paramsCode
            })
          }).catch(() => {
            selected = []
            fullTypes = []
          }).finally(() => {
            me.setTableData(selected, fullTypes)
            me.fillEmptyAttachData()
          })
        } else {
          me.setTableData(selected, fullTypes)
        }
      },
      fillEmptyAttachData() {
        let me = this
        if (me.gridConfig.data.length > 0) {
          for (let row of me.gridConfig.data) {
            if (!me.tmpAttachData.hasOwnProperty(row.attachType)) {
              me.tmpAttachData[row.attachType] = ['']
            } else if (Array.isArray(me.tmpAttachData[row.attachType]) && me.tmpAttachData[row.attachType].length === 0) {
              me.tmpAttachData[row.attachType] = ['']
            }
          }
          // 初始化 单证信息
          me.gridConfig.data.map(item => {
            me.tmpAttachData[item.attachType].forEach(value => {
              item.attachNoList.length = 0
              item.attachNoList.push({
                orderNo: '',
                attachNo: value,
                attachFiles: []
              })
            })
          })
        }
      },
      /**
       * 获取随附单证数据（不含附件信息）
       */
      loadAttach() {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.getPreAcmpInfo}/${me.edit.editData.sid}/DEV_CUS`).then(res => {
          me.$set(me, 'tmpAttachData', res.data.data)
          me.fillEmptyAttachData()
          // 加载文件
          me.loadAttachFiles()
        }).catch(() => {
        })
      },
      /**
       * 加载文件
       */
      loadAttachFiles() {
        let me = this
        // 加载对应的文件信息
        let attachType = Object.values(me.attachTypeEnum).join(',')
        me.$http.post(me.ajaxUrl.getAttachList, {
          acmpType: attachType,
          businessSid: me.edit.editData.sid,
          businessType: ('DEV_CUS')
        }).then(res => {
          let attachFiles = res.data.data
          // 初始化 单证对应的文件信息
          me.gridConfig.data.forEach(grdData => {
            if (Array.isArray(grdData.attachNoList) && grdData.attachNoList.length > 0) {
              // 其他
              let files = attachFiles.filter(item => item.acmpType === grdData.attachType).map(item => {
                return {fileSid: item.sid, fileName: item.originFileName}
              })
              me.$set(grdData.attachNoList[0], 'attachFiles', files)
            }
          })
        }).catch(() => {
        })
      },
      /**
       * 文件删除
       * @param fileSid
       * @param index
       * @param attachType
       * @param attachNo
       */
      onDeleteFile(fileSid, index, attachType, attachNo) {
        let me = this
        if (isNullOrEmpty(attachNo)) {
          attachNo = ''
        }
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            me.$http.delete(`${me.ajaxUrl.delete}/${fileSid}`).then(() => {
              me.$Message.success('删除成功!')
              me.gridConfig.data.filter(item => item.attachType === attachType).map(item => {
                item.attachNoList.filter(attachNoItem => attachNoItem.attachNo === attachNo).map(attachNoItem => {
                  attachNoItem.attachFiles.splice(index, 1)
                })
              })
            }).catch(() => {
            })
          }
        })
      },
      /**
       * 文件上传
       * @param attachType
       * @param attachNo
       */
      onUpAttach(attachType, attachNo) {
        let me = this
        me.$set(me.uploadInfo, 'acmpNo', attachNo)
        me.$set(me.uploadInfo, 'acmpType', attachType)
      },
      /**
       * 上传之前回调方法
       * @param file
       */
      handleBeforeUpload(file) {
        let me = this,
          fileName = file.name
        if (fileName.indexOf('.') > -1) {
          let suffix = fileName.substring(fileName.lastIndexOf('.') + 1)
          if (['exe', 'com', 'pif', 'bat', 'scr'].includes(suffix)) {
            return false
          }
        }
        me.$set(me, 'uploadFailure', false)
        me.$set(me, 'uploadCompleted', false)
        me.$set(me, 'uploadProgressShow', true)
      },
      /**
       * 上传错误回调方法
       */
      handleOnError() {
        let me = this
        me.$nextTick(() => {
          me.$set(me, 'uploadFailure', true)
        })
      },
      /**
       * 上传附件成功后回调
       * @param response
       * @param file
       */
      handleOnSuccess(response, file) {
        let me = this
        if (response.success) {
          let attachDataList = {
            fileName: file.name,
            fileSid: response.data.sid
          }
          me.gridConfig.data.filter(item => item.attachType === response.data.acmpType).map(item => {
            item.attachNoList[0].attachFiles.push(attachDataList)
          })
          me.$set(me, 'uploadCompleted', true)
        } else {
          me.$nextTick(() => {
            me.$set(me, 'uploadFailure', true)
          })
          me.$Message.error(response.message)
        }
      },
      /**
       * 文件格式验证失败时的钩子
       */
      handleFormatError() {
        // let me = this
        // me.$nextTick(() => {
        //   me.$set(me, 'uploadFailure', true)
        // })
      },
      /**
       * 文件超出指定大小限制时的钩子
       */
      handleExceededSize() {
        // let me = this
        // me.$nextTick(() => {
        //   me.$set(me, 'uploadFailure', true)
        // })
      },
      /**
       * 附件下载
       * @param fn
       * @param delay
       * @param sysId
       */
      debounce(fn, delay, sysId) {
        let me = this
        return function () {
          clearTimeout(me.timer)
          let call = !me.timer
          call && fn.call(this, sysId)
          me.timer = setTimeout(function () {
            me.timer = false
          }, delay)
        }
      },
      downloadFile(sysId) {
        let me = this
        me.debounce(me.downFun, 2000, sysId)()
      },
      downFun(sysId) {
        let me = this
        me.$http.get(`${me.ajaxUrl.download}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          // 下载
          const name = getHttpHeaderFileName(res.headers)
          // const blob = new Blob([res.data], {type: 'application/octet-stream'})
          // blobSaveFile(blob, name)

          // 预览
          me.$set(me.filePreview, 'show', true)
          me.$set(me.filePreview, 'fileData', [{
            sid: sysId,
            originFileName: name
          }])
        }).catch(() => {
        })
      },
    },
    computed: {
      /**
       * 上传(删除)按钮是否显示
       * @returns {boolean}
       */
      buttonIsShow() {
        let me = this
        if (me.isLocked) {
          return false
        }
        return !me.aeoShow
      },
      uploadFileConfig() {
        let me = this
        return {
          action: me.ajaxUrl.upload,
          headers: {
            Authorization: 'Bearer ' + me.$store.state.token
          },
          data: {
            acmpFormat: '',
            billSerialNo: '',
            businessSid: me.edit.editData.sid,
            acmpNo: me.uploadInfo.acmpNo,
            businessType: ('DEV_CUS'),
            acmpType: me.uploadInfo.acmpType
          }
        }
      },
      showToolbar() {
        let me = this
        if (!isNullOrEmpty(me.edit.editData.sid)) {
          if (Array.isArray(me.actions) && me.actions.length > 0) {
            return true
          }
        }
        return false
      },
      attachStatus() {
        let me = this
        if (me.isLocked) {
          return '已锁定'
        }
        return '未锁定'
      },
      /**
       * 通关业务设置
       */
      configData() {
        return this.$store.state[`${namespace}`].clearanceBusinessSetting
      }
    }
  }
</script>
<style scoped>
  tr.ivu-table-row-hover td {
    background-color: snow;
  }

  .ivu-icon {
    cursor: pointer;
  }

  .ivu-upload {
    width: 80px;
    display: inline-block;
  }

  .filesList {
    margin-top: 2px;
    text-align: left;
    margin-bottom: 2px;
  }

  .filesList strong a {
    font-size: 14px;
  }

  ul li {
    color: #999;
    list-style: none;
    margin-left: 15px;
    display: inline-block;
  }

  .form-title-wrapper {
    line-height: 1px;
    padding: 5px 10px !important;
    background-color: #dcdee2 !important;
  }

  .form-title-wrapper p {
    width: 100%;
    height: 20px;
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
  }

  .form-title-wrapper p span {
    vertical-align: middle;
  }

  .form-title-body-wrapper {
    padding: 8px 8px 2px 8px;
  }

  .form-title-body-wrapper .ivu-form-item {
    margin-bottom: 3px;
  }
</style>
