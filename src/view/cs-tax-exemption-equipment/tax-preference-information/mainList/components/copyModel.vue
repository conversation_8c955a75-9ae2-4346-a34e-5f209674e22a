<template>
  <XdoModal width="600" mask v-model="show" title="提取信息"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm ref="emsListNoFrom" class="xdo-enter-form" :model="modleData" :rules="rulesHeader" label-position="right" :label-width="120">
      <XdoFormItem prop="erpEmsListNo" label="企业内部编号">
        <XdoIInput type="text" v-model="modleData.erpEmsListNo" :maxlength="32" :disabled="showDisable"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
    <div class="xdo-enter-action" style="text-align: center; margin-top: 10px;">
      <XdoButton type="primary" @click="handleProduce">生成</XdoButton>&nbsp;
      <XdoButton type="primary" @click="handleClose">取消</XdoButton>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'

  export default {
    name: 'copyModel',
    props: {
      show: {
        type: Boolean,
        required: true,
        default: false
      },
      selectRows: {
        type: Object,
        required: true,
        default: () => ({})
      }
    },
    data() {
      return {
        showDisable: false,
        modleData: {
          erpEmsListNo: ''
        },
        ajaxUrl: {
          copy: csAPI.tax.list.copy,
        },
        rulesHeader: {
          erpEmsListNo: [{ required: true, message: '不能为空!', trigger: 'blur' },
            {max: 32, message: '长度不能超过32位字节(汉字占2位)！', trigger: 'blur'}],
        },
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleProduce() {
        let me = this
        me.$refs['emsListNoFrom'].validate().then(isValid => {
          if (isValid) {
            me.$http.post(me.ajaxUrl.copy, {
              sid: me.selectRows.sid,
              erpEmsListNo: me.modleData.erpEmsListNo,
            }).then(res => {
              if (res.data.success) {
                me.$Message.success(res.data.message)
              } else {
                me.$Message.error(res.data.message)
              }
              me.handleClose()
            }).catch(() => {
            })
          }
        })
      },
    }
  }
</script>
