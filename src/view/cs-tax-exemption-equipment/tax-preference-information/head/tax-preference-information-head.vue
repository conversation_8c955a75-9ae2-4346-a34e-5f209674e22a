<template>
  <section v-focus>
    <XdoForm ref="headerEditFrom" :model="frmData" :rules="rulesHeader"
             label-position="right" :label-width="150" style="padding: 0; grid-column-gap: 0;">
      <XdoCard class="dc-card">
        <div class="dc-form dc-form-4 xdo-enter-form" style="padding-right: 3px; padding-top: 2px;">
          <XdoFormItem prop="emsListNo" label="企业内部编号">
            <XdoIInput type="text" v-model="frmData.emsListNo" :disabled="isEmsListNoAuto" :maxlength="32"></XdoIInput>
          </XdoFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="masterCustoms" label="主管海关">
            <xdo-select v-model="frmData.masterCustoms" :asyncOptions="pcodeList" :meta="pcode.customs_rel"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="receiptStatus" label="回执状态">
            <xdo-select v-model="frmData.receiptStatus" :options="cmbSource.receiptStatus"
                        :optionLabelRender="pcodeRender" disabled></xdo-select>
          </DcFormItem>

          <XdoFormItem prop="batchNo" label="批次号">
            <XdoIInput type="text" v-model="frmData.batchNo" :disabled="true" ></XdoIInput>
          </XdoFormItem>
        </div>
        <p style="font-weight: bold; padding: 3px 22px; border-bottom: #dcdee2 solid 1px;">
          征免税信息
        </p>
        <div class="dc-form dc-form-4 xdo-enter-form" style="padding-right: 3px; padding-top: 2px;">
          <XdoFormItem prop="seqNo" label="中心统一编号" class="dc-merge-1-5">
            <XdoIInput type="text" v-model="frmData.seqNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="exemptsNo" label="免表海关编号" class="dc-merge-1-3">
            <XdoIInput type="text" v-model="frmData.exemptsNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="projectId" label="项目信息编号" class="dc-merge-3-5">
            <XdoIInput type="text" v-model="frmData.projectId" :maxlength="18" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="mtype"
                      label="免表类型">
            <xdo-select v-model="frmData.mtype" :options="cmbSource.mtype" :optionLabelRender="pcodeRender"
                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="cutMode"
                      label="征免性质">
            <xdo-select v-model="frmData.cutMode" :asyncOptions="pcodeList" :meta="pcode.levytype"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </DcFormItem>
          <XdoFormItem prop="validDate" label="有效日期">
            <XdoDatePicker type="date" v-model="frmData.validDate" @on-change="frmData.validDate=$event"
                           placeholder="有效日期" :disabled="isShow"></XdoDatePicker>
          </XdoFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="applyCode" label="减免税申请人" class="dc-merge-1-5">
            <div class="dc-form">
              <XdoIInput type="text" v-model="frmData.applyCode" disabled></XdoIInput>
              <XdoIInput type="text" v-model="frmData.applyName" disabled></XdoIInput>
              <XdoIInput type="text" v-model="frmData.applyCoScc" disabled></XdoIInput>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="comCode"
                      label="收发货人" class="dc-merge-1-5">
            <div class="dc-form">
              <XdoIInput type="text" v-model="frmData.comCode" disabled></XdoIInput>
              <XdoIInput type="text" v-model="frmData.comName" disabled></XdoIInput>
              <XdoIInput type="text" v-model="frmData.comCoScc" disabled></XdoIInput>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="declareCode" label="受托单位" class="dc-merge-1-5">
            <div class="dc-form">
              <xdo-select v-model="frmData.declareCode" :options="cmbSource.declareData"
                          :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
              <XdoIInput type="text" v-model="frmData.declareName" disabled></XdoIInput>
              <XdoIInput type="text" v-model="frmData.entrustedCoScc" disabled></XdoIInput>
            </div>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="isDeclare" label="是否已申报进口">
            <xdo-select v-model="frmData.isDeclare" :options="cmbSource.isDeclare" :disabled="isShow"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </DcFormItem>
          <XdoFormItem prop="entryNo" label="报关单号">
            <XdoIInput type="text" v-model="frmData.entryNo" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="apprFromName" label="政策依据">
            <xdo-select v-model="frmData.apprFromName" :options="cmbSource.apprFrom" :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="contrNo"
                      label="合同协议号">
            <XdoIInput type="text" v-model="frmData.contrNo" :disabled="isShow" :maxlength="35"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="enterpriseType" label="申请人种类/代码">
            <xdo-select v-model="frmData.enterpriseType" :options="enterpriseType"

                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="transMode" label="成交方式">
            <xdo-select v-model="frmData.transMode" :asyncOptions="pcodeList" :meta="pcode.transac"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="entryPort" label="进(出)口岸">
            <xdo-select v-model="frmData.entryPort" :asyncOptions="pcodeList" :meta="pcode.customs_rel"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="iemark"
                      label="进(出)口标志">
            <xdo-select v-model="frmData.iemark" :options="cmbSource.iemark" :optionLabelRender="pcodeRender"
                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="linkMan"
                      label="联系人">
            <XdoIInput type="text" v-model="frmData.linkMan" :disabled="isShow"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="linkManTel" label="电话">
            <XdoIInput type="text" v-model="frmData.linkManTel" :disabled="isShow"></XdoIInput>
          </DcFormItem>
          <DcFormItem prop="note_1" label="申请随附单证纸本资料自行前往主管海关递交" label-width="286" style="padding-left:14px" class="dc-merge-3-5">
            <!--            <p style="margin-top:4px">{{isExistFile}}</p>-->
            <xdo-select v-model="frmData.isExistFile" :options="isExistOptions" :optionLabelRender="pcodeRender"
                        disabled="true"></xdo-select>
          </DcFormItem>
          <DcFormItem></DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" class="dc-merge-1-3"
                      prop="businessType" label="申请人市场主体类型/代码">
            <xdo-select v-model="frmData.businessType" :options="businessType"

                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="applType"
                      label="申请形式">
            <xdo-select v-model="frmData.applType" :options="cmbSource.applType" :optionLabelRender="pcodeRender"
                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="declareCustoms" label="报关单申报地海关">
            <xdo-select v-model="frmData.declareCustoms" :asyncOptions="pcodeList" :meta="pcode.customs_rel"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </DcFormItem>
<!--          <DcFormItem></DcFormItem>-->
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="applyAddress" label="申请人所在地" class="dc-merge-1-3">
            <XdoIInput type="text" v-model="frmData.applyAddress" :disabled="isShow"></XdoIInput>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="hasSpecFile" label="状况报告书递交标志">
            <xdo-select v-model="frmData.hasSpecFile" :options="cmbSource.hasSpecFile" :optionLabelRender="pcodeRender"
                        :disabled="isShow"></xdo-select>
          </DcFormItem>
          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"
                      prop="useAddress" label="使用地点" class="dc-merge-1-5">
            <XdoIInput type="text" v-model="frmData.useAddress" :disabled="isShow"></XdoIInput>
          </DcFormItem>

          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange" prop="note"
                      label="备注" class="dc-merge-1-5">
            <XdoIInput type="text" v-model="frmData.note" :disabled="isShow"></XdoIInput>
          </DcFormItem>
        </div>
        <p style="font-weight: bold; padding: 3px 22px; border-bottom: #dcdee2 solid 1px;">
          其他信息
          <!--
                    <i ref="arrawHidden" class="ivu-icon ivu-icon-ios-arrow-forward arrowHidden" @click="onHandlerHidden"></i>
          -->
        </p>
        <div class="dc-form dc-form-4 xdo-enter-form" style="padding-right: 3px; padding-top: 2px;">
          <XdoFormItem prop="tmpNo" label="暂存单编号">
            <XdoIInput type="text" v-model="frmData.tmpNo" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="billTypeName" label="业务种类">
            <xdo-select v-model="frmData.billTypeName" :options="cmbSource.billType" :disabled="isShow"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="apprItemName" label="产业政策审批条目">
            <xdo-select v-model="frmData.apprItemName" :options="cmbSource.apprItem" :disabled="isShow"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="apprDeptName" label="审批部门">
            <xdo-select v-model="frmData.apprDeptName" :options="cmbSource.apprDept" :disabled="isShow"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="itemMode" label="项目性质">
            <XdoIInput type="text" v-model="frmData.itemMode" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="projectFund" label="项目资金性质">
            <XdoIInput type="text" v-model="frmData.projectFund" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="supplierCode" label="供应商">
            <xdo-select v-model="frmData.supplierCode" :options="cmbSource.tradeCodeData"
                        :optionLabelRender="pcodeRender" :disabled="isShow"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="licenseNo" label="许可证号">
            <XdoIInput type="text" v-model="frmData.licenseNo" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="taxAssureReason" label="担保原因">
            <XdoIInput type="text" v-model="frmData.taxAssureReason" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="assureNo" label="担保编号">
            <XdoIInput type="text" v-model="frmData.assureNo" :disabled="isShow"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="itemNo" label="项目确认书编号">
            <xdo-select v-model="frmData.itemNo" :options="cmbSource.itemNoData" :disabled="isShow"></xdo-select>
          </XdoFormItem>
<!--          <DcFormItem :auditMark="canAeoAudit" :auditData="auditDataShow" @onAuditChange="onAuditChange"-->
<!--                      prop="contactCellphone" label="手机">-->
<!--            <XdoIInput type="text" v-model="frmData.contactCellphone" :disabled="isShow"></XdoIInput>-->
<!--          </DcFormItem>-->
          <XdoFormItem prop="isEnabled" label="是否有效">
            <xdo-select v-model="frmData.isEnabled" :options="cmbSource.isEnabled" :optionLabelRender="pcodeRender"
                        :disabled="isEditEnabled"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeTerms" label="贸易条款">
            <xdo-select v-model="frmData.tradeTerms" :options="cmbSource.tradeTermList" :optionLabelRender="pcodeRender"
                        :disabled="isShow"></xdo-select>
          </XdoFormItem>
        </div>
        <p style="font-weight: bold; padding: 3px 22px; border-bottom: #dcdee2 solid 1px;">
          概要申报信息
        </p>
        <div class="dc-form dc-form-4 xdo-enter-form" style="padding-right: 3px; padding-top: 2px;">
          <XdoFormItem prop="entryCustoms" label="申报地海关">
            <xdo-select v-model="frmData.entryCustoms" :asyncOptions="pcodeList" :meta="pcode.customs_rel"
                        :optionLabelRender="pcodeRender" disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="trafMode" label="运输方式">
            <xdo-select v-model="frmData.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                        :optionLabelRender="pcodeRender" disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="trafName" label="运输工具名称">
            <XdoIInput type="text" v-model="frmData.trafName" disabled="true"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="voyageNo" label="航次号">
            <XdoIInput type="text" v-model="frmData.voyageNo" disabled="true"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="hwab" label="提运单号">
            <XdoIInput type="text" v-model="frmData.hwab" disabled="true"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="tradeMode" label="监管方式">
            <xdo-select v-model="frmData.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                        :optionLabelRender="pcodeRender" disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="grossWt" label="毛重(KG)">
            <XdoIInput type="text" v-model="frmData.grossWt" disabled="true"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="cardMark" label="是否涉证">
            <xdo-select v-model="frmData.cardMark" :options="this.productClassify.CREDENTIAL_FLAG"
                        disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="checkMark" label="是否涉检">
            <xdo-select v-model="frmData.checkMark" :options="this.productClassify.CREDENTIAL_FLAG"
                        disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="taxMark" label="设否涉税">
            <xdo-select v-model="frmData.taxMark" :options="this.productClassify.CREDENTIAL_FLAG"
                        disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="entryDeclareCode" label="报关单申报单位">
            <xdo-select v-model="frmData.entryDeclareCode" :options="cmbSource.declareData"
                        :optionLabelRender="pcodeRender" disabled="true"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="entryDeclareName" label="报关单申报单位名称">
            <XdoIInput type="text" v-model="frmData.entryDeclareName" disabled="true"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="entryDeclareScc" label="报关单申报单位信用代码">
            <XdoIInput type="text" v-model="frmData.entryDeclareScc" disabled="true"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </div>
    <TemplateSelectionList ref="selTemplate" :show.sync="tmpSelListShow" iemark="I" bond-mark="0"
                           @selectTemplate:success="selectTemplateSuccess"></TemplateSelectionList>
    <TemplateConfirm :show.sync="tempConfirmShow"
                     @confirm:success="afterConfirm"></TemplateConfirm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus, TemplateConfirm } from '@/view/cs-common'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import { auditTrailDetail } from '@/view/cs-aeoManage/base/auditTrailDetail'
  import TemplateSelectionList from '../components/template-selection/template-selection-list'
  import { productClassify } from '@/view/cs-common'

  export default {
    name: 'taxPreferenceInformationHead',
    mixins: [commEdit, auditTrailDetail],
    props: {
      aeoShow: {
        type: Boolean,
        default: false
      },
      parentConfig: {
        type: Object,
        default: () => ({})
      },
      cmbSource: {
        type: Object,
        default: () => ({})
      }
    },
    components: {
      TemplateConfirm,
      TemplateSelectionList
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        frmData: {},
        isShow: false,
        isEmsListNoAuto: false,
        moduleName: 'LZ',
        bondedShow: false,
        isEditDeclare: false,
        isEditEnabled: false,
        tmpSelListShow: false,
        tempConfirmShow: false,
        isLoadFromTemplate: false,
        productClassify: productClassify,
        businessType: [],
        enterpriseType: [],
        isExistOptions: [
          {value: '0', label: '否'},
          {value: '1', label: '是'}
        ],
        ajaxUrl: {
          selectAllPaged: csAPI.tax.list.selectAllPaged,
          devFreeApplyHead: csAPI.tax.list.devFreeApplyHead,
          categoryParameter: csAPI.aeoManage.aeoReview.actions.categoryParameter,
          bodyTypeParameters: csAPI.aeoManage.aeoReview.actions.bodyTypeParameters
        },
        rulesHeader: {
          iemark: [{required: true, message: '不能为空!', trigger: 'blur'}],
          cutMode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          isEnabled: [{required: true, message: '不能为空!', trigger: 'blur'}],
          isDeclare: [{required: true, message: '不能为空!', trigger: 'blur'}],
          apprFromName: [{required: true, message: '不能为空!', trigger: 'blur'}],
          contrNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          transMode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          entryPort: [{required: true, message: '不能为空!', trigger: 'blur'}],
          declareCustoms: [{required: true, message: '不能为空!', trigger: 'blur'}],
          linkMan: [{required: true, message: '不能为空!', trigger: 'blur'}],
         linkManTel: [{required: true, message: '不能为空!', trigger: 'blur'}],
          note: [{required: true, message: '不能为空!', trigger: 'blur'}],
          masterCustoms: [{required: true, message: '不能为空!', trigger: 'blur'}],
          hasSpecFile:[{required: true, message: '不能为空!', trigger: 'blur'}],
          // contactCellphone:[{required: true, message: '不能为空!', trigger: 'blur'}],
        },
        buttons: [
          {...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave},
          {...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack},
          {...btnComm, label: '选择模板', icon: 'dc-btn-saveTemplate', click: this.handleChooseTemplate},
          {...btnComm, label: '保存为模板', icon: 'dc-btn-saveTemplate', click: this.handleSaveTemplate}
        ]
      }
    },
    mounted() {
      let me = this
      if (me.parentConfig.editStatus === editStatus.ADD) {
        me.frmData = me.getDefaultData()
      } else if (me.parentConfig.editStatus === editStatus.EDIT) {
        me.frmData = me.parentConfig.editData
      } else if (me.parentConfig.editStatus === editStatus.SHOW) {
        me.frmData = me.parentConfig.editData
        me.isShow = true
        me.isEditDeclare = true
        me.isEditEnabled = true
        me.buttons[0].disabled = true
        me.buttons[2].disabled = true
        me.buttons[3].disabled = true
      }
      if (this.frmData.applType != '') {
        if (this.frmData.applType === '0') {
          this.frmData.isExistFile = '0'
        } else {
          this.frmData.isExistFile = '1'
        }
      }
      me.getIsEditEmsListNo()
      me.categoryParameter()
      me.bodyTypeParameters()
    },
    watch: {
      'parentConfig': {
        immediate: true,
        handler: function(val) {
          if (val.editStatus === editStatus.EDIT) {
            this.isEditEnabled = val.editData.status !== '0'
          }
        }
      },
      'frmData.declareCode': {
        immediate: true,
        handler: function(val) {
          if (!isNullOrEmpty(val)) {
            this.frmData.declareName = this.cmbSource.declareData.filter(x => {
              return x.value === val
            })[0].label
            this.$http.post(csAPI.ieParams.getCreditCode + `/${val}`).then(res => {
              this.frmData.entrustedCoScc = res.data.data
            }).catch(() => {
            })
            this.$http.post(csAPI.ieParams.getCompanyName + `/${val}`).then(res => {
              this.frmData.declareName = res.data.data
            }).catch(() => {
            })
          }
        }
      },
      'frmData.applType': {
        immediate: true,
        handler: function(val) {
          if (!isNullOrEmpty(val)) {
            if (val === '0') {
              this.frmData.isExistFile = '0'
            } else {
              this.frmData.isExistFile = '1'
            }
          }
        }
      }
    },
    methods: {
      getIsEditEmsListNo() {
        let me = this
        me.$http.get(csAPI.enterpriseParamsLib.customDocNo.judgeIsExistsRule + '/' + me.moduleName).then(res => {
          if (this.parentConfig.editStatus !== editStatus.EDIT) {
            me.$set(me, 'isEmsListNoAuto', res.data.data)
          } else {
            me.$set(me, 'isEmsListNoAuto', true)
          }
        }).catch(() => {
          me.$set(me, 'isEmsListNoAuto', false)
        }).finally(() => {
        })
      },
      categoryParameter() {
        let me = this
        me.$http.post(csAPI.aeoManage.aeoReview.actions.categoryParameter).then(res => {
          me.$set(me, "enterpriseType", res.data.data.filter(item => {
            return item.customParamCode === '4'
          }).map(it => {
            return {
              value: it.paramsCode,
              label: it.note
            }
          }))
        }).catch(() => {
        }).finally(()=>{
            me.$nextTick(() => {
              if (isNullOrEmpty(me.frmData.businessType) && this.parentConfig.editStatus === editStatus.ADD) {
                me.$set(me.frmData, 'enterpriseType', me.enterpriseType[0].value)
              }
            })
        })
      },
      bodyTypeParameters() {
        let me = this
        me.$http.post(csAPI.aeoManage.aeoReview.actions.bodyTypeParameters).then(res => {
          me.$set(me, "businessType", res.data.data.filter(item => {
            return item.customParamCode === '5'
          }).map(it => {
            return {
              value: it.paramsCode,
              label: it.note
            }
          }))
        }).catch(() => {
        }).finally(()=>{
          me.$nextTick(() => {
            if ( isNullOrEmpty(me.frmData.businessType) && this.parentConfig.editStatus === editStatus.ADD) {
              me.$set(me.frmData, 'businessType', me.businessType[0].value)
            }
          })
        })
      },
      getDefaultData() {
        return {
          emsListNo: '',
          billTypeName: '',
          apprFromName: '财关税[2021]4号',
          iemark: 'I',
          cutMode: '428',
          itemNo: '',
          apprItemName: '',
          apprDeptName: '',
          licenseNo: '',
          contrNo: '',
          transMode: '',
          itemMode: '',
          entryPort: '',
          isDeclare: '0',
          assureNo: '',
          validDate: '',
          // masterCustoms: this.cmbSource.masterCustoms,
          masterCustoms: '0117',
          declareCustoms: '',
          linkMan: this.cmbSource.linkmanName,
          linkManTel: this.cmbSource.telephoneNo,
          taxAssureReason: '',
          supplierCode: '',
          declareCode: '**********',
          declareName: '',
          isEnabled: '1',
          tradeTerms: '',
          entryNo: '',
          note: '',
          tmpNo: '',
          seqNo: '',
          projectId: '',
          applyCoScc: this.$store.state.user.socialCreditCode,
          applyCode: this.$store.state.user.company,
          applyName: this.$store.state.user.companyName,
          comCoScc: this.$store.state.user.socialCreditCode,
          comCode: this.$store.state.user.company,
          comName: this.$store.state.user.companyName,
          hasSpecFile: '',
          contactCellphone: this.cmbSource.mobilePhone,
          businessType: '',
          applType: '0',
          entrustedCoScc: '',
          projectFund: '',
          receiptStatus: '',
          exemptsNo: '',
          mtype: '1',
          // applyAddress: this.cmbSource.address,
          applyAddress: '北京市北京经济技术开发区核心区34M3、42M2、32M4地块',
          enterpriseType: this.$store.state.user.company.substring(5, 6),
          // useAddress: this.cmbSource.freeAddress,
          useAddress: '北京市北京经济技术开发区核心区34M3、42M2、32M4地块',
          isExistFile: '0',
          entryCustoms: '',
          trafMode: '',
          trafName: '',
          voyageNo: '',
          hwab: '',
          tradeMode: '',
          grossWt: null,
          cardMark: '',
          checkMark: '',
          taxMark: '',
          batchNo:''
        }
      },
      handleChooseTemplate() {
        this.tmpSelListShow = true
      },
      /**
       * 选择模板后操作
       * @param templateEntity
       */
      selectTemplateSuccess(templateEntity) {
        let me = this
        me.isLoadFromTemplate = true
        let theData = me.getDefaultData()

        Object.keys(templateEntity).forEach(property => {
          if (templateEntity.hasOwnProperty(property) && property !== 'sid'
            && theData.hasOwnProperty(property)) {
            me.$set(theData, property, templateEntity[property])
          }
        })

        theData.batchNo = me.frmData.batchNo
        me.frmData = theData

        setTimeout(() => {
          me.isLoadFromTemplate = false
        }, 500)
      },
      handleSaveTemplate() {
        this.tempConfirmShow = true
      },
      /**
       * 保存确认
       * @param templateName
       */
      afterConfirm(templateName) {
        let me = this
        me.tempConfirmShow = false
        if (templateName && templateName.trim().length > 0) {
          let entity = JSON.parse(JSON.stringify(me.frmData))
          me.$refs.selTemplate.saveTemplate(entity, templateName, () => {
            me.$Message.success('模板保存成功')
          })
        }
      },
      handleSave() {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            let http = null
            const param = Object.assign({}, me.frmData)
            if (me.parentConfig.editStatus === editStatus.ADD) {
              http = me.$http.post(me.ajaxUrl.devFreeApplyHead, param)
            } else {
              let sid = me.parentConfig.editData.sid
              http = me.$http.put(`${me.ajaxUrl.devFreeApplyHead}/${sid}`, param)
            }
            http.then(res => {
              if (res.data.success) {
                this.isEmsListNoAuto = true
              }
              me.$Message.success(res.data.message)
              me.parentConfig.editStatus = editStatus.EDIT
              me.parentConfig.editData = res.data.data
              me.frmData = res.data.data
              me.$emit('isShowBody', true)
              me.$emit('onAfterHeadSaved')
            }).catch(() => {
            })
          }
        })
      },
      /**
       * 隐藏显示备案信息
       */
      onHandlerHidden() {
        let me = this
        if (me.bondedShow) {
          me.$refs.arrawHidden.className = 'ivu-icon ivu-icon-ios-arrow-forward arrowHidden'
        } else {
          me.$refs.arrawHidden.className = 'ivu-icon ivu-icon-ios-arrow-forward arrowHidden showBonded'
        }
        me.$set(me, 'bondedShow', !me.bondedShow)
      },
      /**
       * 数据重新加载
       */
      dataReload() {
        let me = this
        if (me.parentConfig.editStatus === editStatus.EDIT) {
          me.$http.post(me.ajaxUrl.selectAllPaged, {
            sid: me.parentConfig.editData.sid
          }, {
            params: {
              pageParam: {
                page: 1,
                limit: 20,
                dataTotal: -1
              }
            }
          }).then(res => {
            if (Array.isArray(res.data.data) && res.data.data.length > 0) {
              me.$set(me, 'frmData', res.data.data[0])
            }
          }).catch(() => {
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .arrowHidden {
    top: 304px;
    left: -66px;
    z-index: 1000;
    cursor: pointer;
    position: relative;
    transition: all 0.5s;
  }

  .showBonded {
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
  }

  .dc-card > .ivu-card-head {
    padding: 5px 10px !important;
  }

  /deep/ .audit-view label.ivu-form-item-label {
    background-color: greenyellow;
  }

  /deep/ .audit-error label.ivu-form-item-label {
    color: white;
    background-color: red;
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }
</style>
