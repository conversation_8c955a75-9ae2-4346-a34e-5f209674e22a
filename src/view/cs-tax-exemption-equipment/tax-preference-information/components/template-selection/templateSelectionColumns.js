import { baseColumnsMultiSelection, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

// 通用列
const showColumns = [
  ...baseColumnsMultiSelection
  , 'tempName'
  , 'userName'
  , 'insertTime'
  , 'declareCustoms'
  , 'hasSpecFile'
  , 'contactCellphone'
]

const columns = {
  mixins: [baseColumns],
  data() {
    let me = this
    let baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 682,
          align: 'center',
          title: '模板名称',
          key: 'tempName'
        },
        {
          title: '制单员',
          align: 'center',
          width: 205,
          key: 'userName'
        },
        {
          title: '制单日期',
          key: 'insertTime',
          align: 'center',
          width: 88,
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  showColumns,
  columns
}
