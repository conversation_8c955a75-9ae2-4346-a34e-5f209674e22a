<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="tempName" label="模板名称">
        <XdoIInput type="text" v-model="searchParam.tempName"></XdoIInput>
    </XdoFormItem>
    <XdoFormItem prop="lister" label="制单员">
      <xdo-select v-model="searchParam.lister" :options="this.cmbSource.listerData"></xdo-select>
    </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'

  export default {
    name: 'templateSelectionSearch',
    data() {
      return {
        searchParam: {
          tempName: '',
          lister: '',
        },
        cmbSource: {
          listerData: []
        }
      }
    },
    created() {
      let me = this
      me.$http.post(csAPI.tax.head.selectInsertUser).then(res => {
        if (res.data.data.length > 0) {
          me.$set(me.cmbSource, 'listerData', res.data.data.map(item => {
            return {
              value: item.insertUser,
              label: item.insertUser
            }
          }))
        } else {
          me.searchParam.lister = ''
        }
      }).catch(() => {
        me.$set(me.cmbSource, 'listerData', [])
        me.searchParam.lister = ''
      })
    }
  }
</script>

<style scoped>
</style>
