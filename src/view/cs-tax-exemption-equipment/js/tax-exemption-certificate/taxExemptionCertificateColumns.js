import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  , 'certNo'
  , 'gname'
  , 'amout'
  , 'curr'
  , 'itemNo'
  , 'attached'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '征免税证明编号',
          key: 'certNo',
          width: 200,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '商品名称',
          key: 'gname',
          minWidth: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '金额',
          key: 'amout',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '项目确认书编号',
          key: 'itemNo',
          width: 200,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          width: 80,
          title: '附件',
          align: 'center',
          key: 'attached',
          render: (h, params) => {
            let attached = params.row[params.column.key]
            if (attached === 'Y') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  color: '',
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.handleView(params.row.sid)
                  }
                }
              }, attached)
            } else {
              return h('span', '')
            }
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
