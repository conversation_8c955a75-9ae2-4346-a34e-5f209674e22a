import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  , 'insertTime'
  , 'itemNo'
  , 'applyDate'
  , 'applyAmout'
  , 'curr'
  , 'usedAmout'
  , 'remainAmout'
  , 'attached'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '录入日期',
          width: 100,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '确认书编号',
          key: 'itemNo',
          minWidth: 200,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '申请日期',
          width: 100,
          align: 'center',
          key: 'applyDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '申请额度',
          key: 'applyAmout',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          key: 'curr',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '已使用额度',
          key: 'usedAmout',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '剩余额度',
          key: 'remainAmout',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          width: 80,
          title: '附件',
          align: 'center',
          key: 'attached',
          render: (h, params) => {
            let attached = params.row[params.column.key]
            if (attached === 'Y') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  color: '',
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.handleView(params.row.sid)
                  }
                }
              }, attached)
            } else {
              return h('span', '')
            }
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
