import pms from '@/libs/pms'
import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const equipmentList = {
  mixins: [pms, commList],
  data() {
    return {
      tableId: '',
      tableShow: true,
      configColumns: [],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      hideOptions: false,
      orginShowColumns: [],
      showtableColumnSetup: false
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
  },
  methods: {
    /**
     * 设置列表显示字段
     * @param totalColumns
     * @param columnsConfig
     * @param hideOptions
     */
    setShowFields(totalColumns, columnsConfig, hideOptions = false) {
      let me = this
      me.orginShowColumns = JSON.parse(JSON.stringify(columnsConfig))
      me.configColumns = getColumnsByConfig(totalColumns, columnsConfig)
      me.tableId = me.$route.path
      let columns = me.$bom3.showTableColumns(me.tableId, me.configColumns)
      me.hideOptions = hideOptions
      me.handleUpdateColumn(columns)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showtableColumnSetup = true
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      if (me.hideOptions) {
        me.gridConfig.gridColumns = columns
      } else {
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
      }
      // 解决iView table 的问题
      me.tableShow = false
      me.$nextTick(() => {
        me.tableShow = true
      })
      me.gridConfig.exportColumns = getExcelColumnsByConfig(columns, me.orginShowColumns)
    }
  }
}
