import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  , 'capitalNo'
  , 'useAddress'
  , 'useDate'
  , 'useStatus'
  , 'tradeStatus'
  , 'depositaryOfficer'
  , 'emsListNo'
  , 'entryNo'
  , 'declareDate'
  , 'tradeMode'
  , 'gname'
  , 'gmodel'
  , 'codeTS'
  , 'unit'
  , 'qty'
  , 'decTotal'
  , 'curr'
  , 'passDate'
  , 'gmark'
  , 'emsNo'
  , 'itemNo'
  , 'tradeValidDate'
]

const columns = {
  mixins: [baseColumns],
  data () {
    return {
      totalColumns: [
        {
          title: '资产编号',
          key: 'capitalNo',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '使用地点',
          key: 'useAddress',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '使用日期',
          key: 'useDate',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '使用状态',
          key: 'useStatus',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.taxExemptionEquipment.USE_STATUS_MAP)
          }
        },
        {
          title: '监管状态',
          key: 'tradeStatus',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.taxExemptionEquipment.REGULATORY_STATUS_MAP)
          }
        },
        {
          title: '保管负责人',
          key: 'depositaryOfficer',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '单据内部编号',
          key: 'emsListNo',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单号',
          key: 'entryNo',
          width: 130,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '申报日期',
          key: 'declareDate',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 130,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '商品名称',
          key: 'gname',
          width: 160,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '规格型号',
          key: 'gmodel',
          width: 200,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '商品编码',
          key: 'codeTS',
          width: 100,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '单位',
          key: 'unit',
          width: 90,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          title: '数量',
          key: 'qty',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '金额',
          key: 'decTotal',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '币制',
          key: 'curr',
          width: 130,
          align: 'center',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          title: '放行日期',
          key: 'passDate',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '物料类型',
          key: 'gmark',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
          }
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '项目确认书编号',
          key: 'itemNo',
          width: 200,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '监管有效期',
          key: 'tradeValidDate',
          width: 90,
          align: 'center',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
