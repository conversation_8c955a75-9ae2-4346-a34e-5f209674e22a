import { baseColumnsShow, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  ...baseColumnsShow
  , 'declareYear'
  , 'insertTime'
  , 'attached'
]

const columns = {
  mixins: [ baseColumns ],
  data () {
    return {
      totalColumns: [
        {
          title: '申报年份',
          key: 'declareYear',
          minWidth: 150,
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '录入日期',
          width: 120,
          align: 'center',
          key: 'insertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          title: '附件',
          width: 80,
          align: 'center',
          key: 'attached',
          render: (h, params) => {
            let attached = params.row[params.column.key]
            if (attached === 'Y') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  color: '',
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.handleView(params.row.sid)
                  }
                }
              }, attached)
            } else {
              return h('span', '')
            }
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  columns
}
