<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CustomsAnnualReportSearch ref="headSearch"></CustomsAnnualReportSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange" key="headTable"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CustomsAnnualReportEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></CustomsAnnualReportEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="configColumns" class="height:500px"></TableColumnSetup>
    <XdoModal v-model="showModal" title="附件浏览" fullscreen
              :footer-hide="true" :mask-closable="false">
      <AttachmentUploading :sid="viewSid" :is-edit="false" :attach-title="attachTitle"></AttachmentUploading>
    </XdoModal>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { equipmentList } from '../js/comm/equipmentList'
  import CustomsAnnualReportEdit from './customs-annual-report-edit'
  import CustomsAnnualReportSearch from './customs-annual-report-search'
  import { columnsConfig, columns } from '../js/customs-annual-report/customsAnnualReportColumns'
  import AttachmentUploading from '@/view/cs-tax-exemption-equipment/project-confirmation/components/attachment-uploading'

  export default {
    name: 'customsAnnualReportList',
    components: {
      AttachmentUploading,
      CustomsAnnualReportEdit,
      CustomsAnnualReportSearch
    },
    mixins: [equipmentList, columns],
    data() {
      return {
        viewSid: '',
        // 查询条件行数
        searchLines: 1,
        attachTitle: '',
        showModal: false,
        gridConfig: {
          exportTitle: '海关年报'
        },
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'export': this.handleDownload,
          'setting': this.handleTableColumnSetup
        },
        ajaxUrl: {
          deleteUrl: csAPI.taxExemptionEquipment.customsAnnualReport.delete,
          exportUrl: csAPI.taxExemptionEquipment.customsAnnualReport.exportUrl,
          selectAllPaged: csAPI.taxExemptionEquipment.customsAnnualReport.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.setShowFields(me.totalColumns, columnsConfig)
    },
    methods: {
      /**
       * 预览
       * @param sid
       */
      handleView(sid) {
        let me = this
        me.showModal = true
        me.viewSid = sid
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
