<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="declareYear" label="申报年份">
        <xdo-input v-model="searchParam.declareYear" number int-length="4"></xdo-input>
      </XdoFormItem>
      <dc-dateRange label="录入日期" @onDateRangeChanged="handleValidInsertTimeChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'customsAnnualReportSearch',
    data () {
      return {
        searchParam: {
          declareYear: null,
          insertTimeFrom: '',
          insertTimeTo: ''
        }
      }
    },
    methods: {
      /**
       * 申请日期
       * @param values
       */
      handleValidInsertTimeChange (values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
