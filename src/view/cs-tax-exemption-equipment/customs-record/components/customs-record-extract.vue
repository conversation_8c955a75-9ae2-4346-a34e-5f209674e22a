<template>
  <XdoModal width="1024" mask v-model="show" title="提取"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header">
      <XdoForm :label-width="72" class="dc-form dc-form-3" inline style="padding-top: 0;">
        <XdoFormItem prop="entryNo" label="报关单号">
          <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
        </XdoFormItem>
        <dc-dateRange label="申报日期" @onDateRangeChanged="handleValidDeclareDateChange"></dc-dateRange>
        <XdoFormItem prop="tradeMode" label="监管方式">
          <xdo-select v-model="searchParam.tradeMode" :meta="pcode.trade"
                      :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="gmark" label="物料类型">
          <xdo-select v-model="searchParam.gmark" :options="this.erpInterfaceData.MAT_FLAG_MAP"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="emsNo" label="备案号">
          <XdoIInput type="text" v-model="searchParam.emsNo"></XdoIInput>
        </XdoFormItem>
        <div class="xdo-enter-action" style="text-align: right;">
          <template v-for="item in buttons">
            <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                    @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
          </template>
        </div>
      </XdoForm>
    </div>
    <div class="content">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" height="355"
                :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { erpInterfaceData } from '@/view/cs-common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

  export default {
    name: 'customsRecordExtract',
    mixins: [commList, baseColumns],
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false,
        type: 'primary'
      }
      return {
        searchParam: {
          entryNo: '',
          declareDateFrom: '',
          declareDateTo: '',
          tradeMode: '',
          gmark: '',
          emsNo: ''
        },
        erpInterfaceData: erpInterfaceData,
        buttons: [{
          ...btnComm, label: '查询', key: 'xdo-btn-download', icon: 'ios-search-outline', click: this.handleSearchSubmit
        }, {
          ...btnComm, label: '确定', key: 'xdo-btn-download', icon: 'ios-checkmark-circle-outline', click: this.handleConfirm
        }],
        ajaxUrl: {
          // 提取报关记录数据
          saveRecordData: csAPI.taxExemptionEquipment.customsRecord.saveRecordData,
          // 查询需要提取的数据
          selectAllPaged: csAPI.taxExemptionEquipment.customsRecord.getEntryRecordData
        },
        gridConfig: {
          gridColumns: [
            {
              title: '单据内部编号',
              key: 'emsListNo',
              width: 180,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              title: '报关单号',
              key: 'entryNo',
              width: 130,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              width: 90,
              align: 'center',
              title: '申报日期',
              key: 'declareDate',
              render: (h, params) => {
                return this.dateTimeShowRender(h, params)
              }
            },
            {
              title: '备案号',
              key: 'emsNo',
              width: 150,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              title: '商品编码',
              key: 'codeTS',
              width: 100,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              title: '商品名称',
              key: 'gname',
              width: 160,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              title: '规格型号',
              key: 'gmodel',
              width: 200,
              align: 'center',
              ellipsis: true,
              tooltip: true
            },
            {
              width: 130,
              tooltip: true,
              ellipsis: true,
              align: 'center',
              title: '监管方式',
              key: 'tradeMode',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.trade)
              }
            },
            {
              key: 'qty',
              width: 150,
              title: '数量',
              tooltip: true,
              ellipsis: true,
              align: 'center'
            },
            {
              width: 150,
              title: '金额',
              tooltip: true,
              ellipsis: true,
              key: 'decTotal',
              align: 'center'
            },
            {
              width: 130,
              key: 'curr',
              title: '币制',
              tooltip: true,
              ellipsis: true,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
              }
            },
            {
              width: 90,
              key: 'unit',
              title: '单位',
              tooltip: true,
              ellipsis: true,
              align: 'center',
              render: (h, params) => {
                return this.cmbShowRender(h, params, [], this.pcode.unit)
              }
            },
            {
              width: 90,
              key: 'passDate',
              align: 'center',
              title: '放行日期',
              render: (h, params) => {
                return this.dateTimeShowRender(h, params)
              }
            },
            {
              width: 90,
              key: 'gmark',
              align: 'center',
              title: '物料类型',
              render: (h, params) => {
                return this.cmbShowRender(h, params, this.erpInterfaceData.MAT_FLAG_MAP)
              }
            },
            {
              width: 90,
              align: 'center',
              title: '监管有效期',
              key: 'tradeValidDate',
              render: (h, params) => {
                return this.dateTimeShowRender(h, params)
              }
            },
            {
              width: 200,
              key: 'itemNo',
              tooltip: true,
              ellipsis: true,
              align: 'center',
              title: '项目确认书编号'
            }
          ]
        }
      }
    },
    methods: {
      /**
       * 申报日期
       */
      handleValidDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      },
      getSearchParams() {
        return this.searchParam
      },
      /**
       * 确定
       */
      handleConfirm() {
        let me = this
        me.buttons[1].loading = true
        me.$http.post(me.ajaxUrl.saveRecordData, me.searchParam).then(() => {
          me.$Message.success('提取完成!')
          me.handleClose()
        }).catch(() => {
        }).finally(() => {
          me.buttons[1].loading = false
        })
      },
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
        me.$emit('import:success', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }

  .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    border-color: #57a3f3;
    background-color: #57a3f3;
  }
</style>
