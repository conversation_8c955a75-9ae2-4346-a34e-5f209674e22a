<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CustomsRecordSearch ref="headSearch"></CustomsRecordSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" :disable="gridDisable"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CustomsRecordEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></CustomsRecordEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="configColumns" class="height:500px"></TableColumnSetup>
    <CustomsRecordExtract :show.sync="modelExtractShow" @import:success="handleSearchSubmit"></CustomsRecordExtract>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { erpInterfaceData } from '@/view/cs-common'
  import CustomsRecordEdit from './customs-record-edit'
  import { equipmentList } from '../js/comm/equipmentList'
  import CustomsRecordSearch from './customs-record-search'
  import CustomsRecordExtract from './components/customs-record-extract'
  import { columnsConfig, columns } from '../js/customs-record/customsRecordColumns'

  export default {
    name: 'customsRecordList',
    components: {
      CustomsRecordEdit,
      CustomsRecordSearch,
      CustomsRecordExtract
    },
    mixins: [equipmentList, columns],
    data() {
      return {
        // 查询条件行数
        searchLines: 4,
        gridConfig: {
          exportTitle: '报关记录'
        },
        modelExtractShow: false,
        erpInterfaceData: erpInterfaceData,
        toolbarEventMap: {
          'edit': this.handleEdit,
          'extract': this.handleExtract,
          'export': this.handleDownload,
          'setting': this.handleTableColumnSetup
        },
        ajaxUrl: {
          deleteUrl: csAPI.taxExemptionEquipment.customsRecord.delete,
          exportUrl: csAPI.taxExemptionEquipment.customsRecord.exportUrl,
          selectAllPaged: csAPI.taxExemptionEquipment.customsRecord.selectAllPaged
        }
      }
    },
    mounted: function () {
      let me = this
      me.setShowFields(me.totalColumns, columnsConfig)
    },
    methods: {
      /**
       * 提取
       */
      handleExtract() {
        this.modelExtractShow = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
