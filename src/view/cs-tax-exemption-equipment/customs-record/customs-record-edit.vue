<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false" title="报关记录" class="ieLogisticsTrackingCard">
      <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
        <XdoFormItem prop="emsListNo" label="单据内部编号">
          <XdoIInput type="text" v-model="frmData.emsListNo" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="declareDate" label="申报日期">
          <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.declareDate" disabled></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="passDate" label="放行日期">
          <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.passDate" disabled></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="entryNo" label="报关单号">
          <XdoIInput type="text" v-model="frmData.entryNo" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="tradeMode" label="监管方式">
          <xdo-select v-model="frmData.tradeMode" :meta="pcode.trade" disabled
                      :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="gmark" label="物料类型">
          <xdo-select v-model="frmData.gmark" :options="this.erpInterfaceData.MAT_FLAG_MAP" disabled
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="emsNo" label="备案号">
          <XdoIInput type="text" v-model="frmData.emsNo" disabled></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="itemNo" label="项目确认书编号">
          <xdo-select v-model="frmData.itemNo" :options="this.cmbSource.itemNoData" :disabled="showDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="tradeValidDate" label="监管有效期">
          <XdoDatePicker type="datetime" format="yyyy-MM-dd" v-model="frmData.tradeValidDate" :disabled="showDisable"></XdoDatePicker>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { formatDate } from '@/libs/datetime'
  import { erpInterfaceData } from '@/view/cs-common'
  import { isNullOrEmpty, isNumber } from '@/libs/util'
  import { equipmentEdit } from '../js/comm/equipmentEdit'

  export default {
    name: 'customsRecordEdit',
    mixins: [equipmentEdit],
    data() {
      return {
        rulesHeader: {},
        cmbSource: {
          itemNoData: []
        },
        formName: 'dataForm',
        erpInterfaceData: erpInterfaceData,
        ajaxUrl: {
          insert: csAPI.taxExemptionEquipment.customsRecord.insert,
          update: csAPI.taxExemptionEquipment.customsRecord.update,
          getItemNo: csAPI.taxExemptionEquipment.projectConfirmation.getItemNoList
        }
      }
    },
    watch: {
      'frmData.passDate': {
        immediate: true,
        handler: function (passDate) {
          if (!isNullOrEmpty(passDate) && isNullOrEmpty(this.frmData.tradeValidDate)) {
            let passDateStr = formatDate(passDate)
            if (!isNullOrEmpty(passDateStr)) {
              let year = passDateStr.substring(0, 4)
              if (isNumber(year)) {
                let validDate = (parseInt(year) + 3) + passDateStr.substring(4)
                this.$set(this.frmData, 'tradeValidDate', validDate)
              }
            }
          }
        }
      }
    },
    created() {
      this.getItemNo()
    },
    methods: {
      getItemNo() {
        let me = this
        me.$http.post(me.ajaxUrl.getItemNo).then(res => {
          me.$set(me.cmbSource, 'itemNoData', res.data.data.map(item => {
            return {
              label: item,
              value: item
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'itemNoData', [])
        })
      },
      getDefaultData() {
        return {
          sid: '',
          emsListNo: '',
          declareDate: '',
          passDate: '',
          entryNo: '',
          tradeMode: '',
          gmark: '',
          emsNo: '',
          itemNo: '',
          tradeValidDate: ''
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body{
    padding: 8px 8px 2px 8px;
  }
</style>
