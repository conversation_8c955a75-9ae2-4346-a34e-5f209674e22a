<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <EnterpriseDataSearch ref="headSearch" iemark="E"></EnterpriseDataSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <EnterpriseDataEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig" iemark="E"></EnterpriseDataEdit>
    <ImportPage :importKey="importConfig.importKey" :importConfig="importConfig.Config"
                :importShow.sync="importShow" @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { enterpriseDataList } from '../js/enterpriseDataList'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './enterpriseEDataListColumns'

  export default {
    name: 'EnterpriseEDataList',
    mixins: [enterpriseDataList, columns],
    data() {
      return {
        iEMark: 'E',
        gridConfig: {
          exportTitle: '企业出口数据'
        },
        ajaxUrl: {
          delete: csAPI.interimVerification.enterpriseData.exports.delete,
          exportUrl: csAPI.interimVerification.enterpriseData.exports.exportUrl,
          selectAllPaged: csAPI.interimVerification.enterpriseData.exports.selectAllPaged
        }
      }
    },
    mounted: function () {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
