<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="状态">
        <xdo-select v-model="searchParam.status" :options="this.interimVerification.CHECK_STATUS_SOURCE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="bondMark" label="保完税标志">
        <xdo-select v-model="searchParam.bondMark" :options="this.interimVerification.BONDED_FLAG_SOURCE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="linkedNo" label="关联单号">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="warehouseNo" :label="labelName.warehouseNo">
        <XdoIInput type="text" v-model="searchParam.warehouseNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="warehouseGNo" :label="labelName.warehouseGNo">
        <XdoIInput type="text" v-model="searchParam.warehouseGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange :label="labelName.warehouseDate" @onDateRangeChanged="handleWarehouseDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { interimVerification } from '@/view/cs-common'

  export default {
    name: 'enterpriseDataSearch',
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        searchParam: {
          status: '',
          bondMark: '',
          linkedNo: '',
          warehouseNo: '',
          warehouseGNo: '',
          facGNo: '',
          warehouseDateFrom: '',
          warehouseDateTo: ''
        },
        labelName: {
          warehouseNo: '',
          warehouseGNo: '',
          warehouseDate: ''
        },
        interimVerification: interimVerification
      }
    },
    watch: {
      iemark: {
        immediate: true,
        handler: function (val) {
          if (val === 'I') {
            this.$set(this.labelName, 'warehouseNo', '入库单号')
            this.$set(this.labelName, 'warehouseGNo', '入库料号')
            this.$set(this.labelName, 'warehouseDate', '入库日期')
          } else if (val === 'E') {
            this.$set(this.labelName, 'warehouseNo', '出库单号')
            this.$set(this.labelName, 'warehouseGNo', '出库料号')
            this.$set(this.labelName, 'warehouseDate', '出库日期')
          }
        }
      }
    },
    methods: {
      /**
       * 出、出库日期修改
       */
      handleWarehouseDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "warehouseDateFrom", values[0])
          this.$set(this.searchParam, "warehouseDateTo", values[1])
        } else {
          this.$set(this.searchParam, "warehouseDateFrom", '')
          this.$set(this.searchParam, "warehouseDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
