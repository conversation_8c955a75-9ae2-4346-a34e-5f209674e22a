<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false" title="基础信息">
      <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="120">
        <XdoFormItem prop="bondMark" label="保完税标志">
          <xdo-select v-model="frmData.bondMark" :options="this.interimVerification.BONDED_FLAG_SOURCE" :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
        </XdoFormItem>
        <XdoFormItem prop="linkedNo" label="关联单号">
          <XdoIInput type="text" v-model="frmData.linkedNo" :maxlength="15" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="warehouseNo" :label="labelName.warehouseNo">
          <XdoIInput type="text" v-model="frmData.warehouseNo" :maxlength="15" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="warehouseLineNo" :label="labelName.warehouseLineNo">
          <XdoIInput type="text" v-model="frmData.warehouseLineNo" :maxlength="4" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="warehouseGNo" :label="labelName.warehouseGNo">
          <XdoIInput type="text" v-model="frmData.warehouseGNo" :maxlength="100" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="warehouseQty" :label="labelName.warehouseQty">
          <xdo-input v-model="frmData.warehouseQty" decimal int-length="13" precision="5" :disabled="showDisable"></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="warehouseUnit" :label="labelName.warehouseUnit">
          <XdoIInput type="text" v-model="frmData.warehouseUnit" :maxlength="6" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="warehouseDate" :label="labelName.warehouseDate">
          <XdoDatePicker type="date" v-model="frmData.warehouseDate" style="width: 100%;" transfer :disabled="showDisable"></XdoDatePicker>
        </XdoFormItem>
        <XdoFormItem prop="warehouse" :label="labelName.warehouse">
          <XdoIInput type="text" v-model="frmData.warehouse" :maxlength="6" :disabled="showDisable"></XdoIInput>
        </XdoFormItem>
        <XdoFormItem prop="dataSource" label="数据来源">
          <xdo-select v-model="frmData.dataSource" :options="this.interimVerification.DATA_SOURCE_SOURCE" :optionLabelRender="pcodeRender" disabled></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { interimVerification } from '@/view/cs-common'
  import { enterpriseDataEdit } from '../js/enterpriseDataEdit'

  export default {
    name: 'CertificateTypeHeadEdit',
    mixins: [ enterpriseDataEdit ],
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I','E'].includes(value)
        }
      }
    },
    data () {
      return {
        rulesHeader: {
          bondMark: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          linkedNo: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          warehouseNo: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          warehouseGNo: [{ required: true, message: '不能为空!', trigger: 'blur' }],
          warehouseQty: [{ type: 'number', required: true, message: '不能为空!', trigger: 'blur' }],
          warehouseUnit: [{ required: true, message: '不能为空!', trigger: 'blur' }]
        },
        interimVerification: interimVerification,
        labelName: {
          warehouseNo: '',
          warehouseLineNo: '',
          warehouseGNo: '',
          warehouseQty: '',
          warehouseUnit: '',
          warehouseDate: '',
          warehouse: ''
        },
        formName: 'dataForm'
      }
    },
    watch: {
      iemark: {
        immediate: true,
        handler: function(val) {
          if (val === 'I') {
            // 标签
            this.$set(this.labelName, 'warehouseNo', '入库单号')
            this.$set(this.labelName, 'warehouseLineNo', '入库单行号')
            this.$set(this.labelName, 'warehouseGNo', '入库料号')
            this.$set(this.labelName, 'warehouseQty', '入库数量')
            this.$set(this.labelName, 'warehouseUnit', '入库单位')
            this.$set(this.labelName, 'warehouseDate', '入库日期')
            this.$set(this.labelName, 'warehouse', '入库仓别')
            // 地址
            this.$set(this.ajaxUrl, 'insert', csAPI.interimVerification.enterpriseData.imports.insert)
            this.$set(this.ajaxUrl, 'update', csAPI.interimVerification.enterpriseData.imports.update)
          } else if (val === 'E') {
            // 标签
            this.$set(this.labelName, 'warehouseNo', '出库单号')
            this.$set(this.labelName, 'warehouseLineNo', '出库单行号')
            this.$set(this.labelName, 'warehouseGNo', '出库料号')
            this.$set(this.labelName, 'warehouseQty', '出库数量')
            this.$set(this.labelName, 'warehouseUnit', '出库单位')
            this.$set(this.labelName, 'warehouseDate', '出库日期')
            this.$set(this.labelName, 'warehouse', '出库仓别')
            // 地址
            this.$set(this.ajaxUrl, 'insert', csAPI.interimVerification.enterpriseData.exports.insert)
            this.$set(this.ajaxUrl, 'update', csAPI.interimVerification.enterpriseData.exports.update)
          }
        }
      }
    },
    methods: {
      getDefaultData () {
        return {
          sid: '',
          bondMark: '',
          linkedNo: '',
          warehouseNo: '',
          warehouseLineNo: '',
          warehouseGNo: '',
          warehouseQty: null,
          warehouseUnit: '',
          warehouseDate: '',
          warehouse: '',
          dataSource: ''
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }
</style>
