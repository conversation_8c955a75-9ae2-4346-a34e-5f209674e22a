import { editStatus } from '@/view/cs-common'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const enterpriseDataEdit = {
  mixins: [ commEdit ],
  data () {
    return {
      buttons: [
        { type: 'primary', disabled: false, loading: false, needed: true, click: this.handleSave, icon: 'dc-btn-save', label: '保存' },
        { type: 'warning', disabled: false, loading: false, needed: true, click: this.handleSaveContinue, icon: 'dc-btn-save-1', label: '保存继续' },
        { type: 'primary', disabled: false, loading: false, needed: true, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回' }
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function(val) {
        if (val === editStatus.SHOW) {
          this.buttons[0].needed = false
          this.buttons[1].needed = false
        } else {
          this.buttons[0].needed = true
          this.buttons[1].needed = true
        }
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * @param loading
     */
    setBtnSaveLoading (loading){
      this.buttons[0].loading = loading
      this.buttons[1].loading = loading
    },
    /**
     * 保存
     */
    handleSave () {
      let me = this
      me.doSave((res) => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存继续
     */
    handleSaveContinue () {
      let me = this
      me.doSave(() => {
        me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
      })
    }
  }
}
