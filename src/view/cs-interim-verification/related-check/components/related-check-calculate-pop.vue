<template>
  <XdoModal width="760" mask v-model="show" title="计算"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <span style="font-weight: bold; color: red; padding-right: 30px;">提示：点击计算后数据会被清空，请等待核算成功后，再做数据核对</span>
          <XdoButton type="warning" class="dc-margin-right" @click="handleCalculate">计算</XdoButton>
          <XdoButton type="primary" class="dc-margin-right" @click="handleClose">取消</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <DynamicForm ref="search" class="dc-form-2" :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
        </DynamicForm>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
  import { interimVerification, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'relatedCheckCalculatePop',
    mixins: [baseSearchConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      inSource: {
        type: Object,
        default: () => ({
          emsNo: []
        })
      }
    },
    data() {
      return {
        cmbSource: {
          gmark: erpInterfaceData.MAT_FLAG_MAP,
          bondMark: interimVerification.BONDED_FLAG_SOURCE
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              me.$set(me, 'baseParams', me.getParams())
            })
          }
        }
      }
    },
    computed: {
      whDateLabel() {
        if (this.iEMark === 'I') {
          return '入库日期'
        }
        return '出库日期'
      },
      whQtyTypeLabel() {
        if (this.iEMark === 'I') {
          return '入库数量'
        }
        return '出库数量'
      },
      /**
       * 动态标签
       */
      dynamicLabel() {
        return {
          whDate: this.whDateLabel,
          whQtyType: this.whQtyTypeLabel
        }
      },
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {...this.inSource, ...this.cmbSource}
      }
    },
    methods: {
      getParams() {
        return [{
          range: true,
          title: '申报日期',
          key: 'declareDate'
        }, {
          range: true,
          key: 'whDate',
          title: '入库日期'
        // }, {
        //   key: 'facGNo',
        //   title: '企业料号'
        // }, {
        //   key: 'copGNo',
        //   title: '备案料号'
        }, {
          key: 'emsNo',
          title: '备案号',
          type: 'select',
          props: {
            optionLabelRender: item => item.label
          }
        // }, {
        //   key: 'linkedNo',
        //   title: '关联单号'
        // }, {
        //   title: '申报数量',
        //   key: 'qtyType',
        //   type: 'radioGroup',
        //   props: {
        //     options: [{
        //       label: '', title: '全部'
        //     }, {
        //       label: '0', title: '等于0'
        //     }, {
        //       label: '1', title: '不等于0'
        //     }]
        //   },
        //   defaultValue: ''
        // }, {
        //   title: '入库数量',
        //   key: 'whQtyType',
        //   type: 'radioGroup',
        //   props: {
        //     options: [{
        //       label: '', title: '全部'
        //     }, {
        //       label: '0', title: '等于0'
        //     }, {
        //       label: '1', title: '不等于0'
        //     }]
        //   },
        //   defaultValue: ''
        }, {
          type: 'select',
          key: 'bondMark',
          title: '保完税标识'
        // }, {
        //   key: 'gmark',
        //   type: 'select',
        //   title: '物料标识'
        }]
      },
      handleCalculate() {
        let me = this
        me.$emit('doCalculation', me.searchConfig.model)
      },
      handleClose() {
        this.$emit('update:show', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }

  /deep/ .ivu-switch-inner {
    left: 25px;
  }

  /deep/ .ivu-switch-checked > .ivu-switch-inner {
    left: 10px;
  }

  .ivu-switch {
    width: 69px;
  }

  .ivu-switch-checked:after {
    left: 55px;
  }
</style>
