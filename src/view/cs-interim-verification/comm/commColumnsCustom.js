import pms from '@/libs/pms'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { operationRenderer } from '../../cs-common/agGridJs/operation_renderer'

// 自定义列导出功能通用JS
export const commColumnsCustom = {
  mixins: [commList, pms],
  data() {
    return {
      tableId: '',
      grdShow: true,
      isShowOp: true,  //在没有编辑权限的时候  是否显示查看按钮 =操作列
      tableShow: true,
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      p_group: 'default',
      isShowSetting: false, //是否显示自定义配置列 按钮
      checkboxSelection: true,
      isAutoSizeAllColumns: false,//查询完成后事件 根据列的内容 调整列宽 必须设置 ref 值  ref_agGrid
      showtableColumnSetup: false,
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup
      }
    }
  },
  mounted: function () {
    let me = this
    me.loadFunctions(me.p_group).then(() => {
      if (typeof me.loadFunctionsAfterOne === 'function') {
        me.loadFunctionsAfterOne()
      }
      me.setShowFields(me.totalColumns)
      me.actionLoad()
    })
  },
  methods: {
    actionLoad() {
      let me = this
      if (me.isShowSetting) {
        me.actions.push({
          ...me.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      }
      if (typeof me.loadOtherActions === 'function') {
        me.loadOtherActions()
      }
    },
    /**
     * 设置列表显示字段
     * @param totalColumns
     * @param defaultColumns
     */
    setShowFields(totalColumns, defaultColumns) {
      let me = this,
        columns = []
      me.tableId = me.$route.path + '/' + me.$options.name
      if (Array.isArray(defaultColumns) && defaultColumns.length > 0) {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns, defaultColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns)
      }
      me.handleUpdateColumn(columns)
    },
    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showtableColumnSetup = true
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'grdShow', false)
      let edit = me.actions.filter(item => {
        return item.command === 'edit'
      })
      //判断是否存在修改按钮  若存在 就增加 查看修改列
      let bascol = []
      if (Array.isArray(edit) && edit.length > 0) {
        bascol = me.getDefaultColumnsC()
      } else {
        bascol = me.isShowOp ? [{
          title: '操作',
          fixed: 'left',
          width: 90,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(me, [{title: '查看', handle: 'handleViewByRow', marginRight: '0'}])
        }] : []
      }
      me.gridConfig.gridColumns = [...bascol, ...columns]
      me.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
      me.$nextTick(() => {
        me.$set(me, 'grdShow', true)
      })
    },
    /**
     * 获取操作列
     * @returns {{cellRendererFramework: *, width: number, fixed: string, title: string, align: string, key: string}[]}
     */
    getDefaultColumnsC() {
      return [{
        width: 120,
        title: '操作',
        fixed: 'left',
        align: 'center',
        key: 'operation',
        cellRendererFramework: operationRenderer(this)
      }]
    },
    /**
     * AG 选中行变化事件
     * @param params
     */
    handleSelectionChanged(params) {
      this.handleSelectionChange(params.api.getSelectedRows())
    },
    /**
     * 查询完成后事件 根据列的内容 调整列宽
     */
    afterSearch() {
    },
    /**
     *查询 enter 键 出发查询事件
     * @param e
     */
    handleSearchSubmitForm(e) {
      if (e.key === 'Enter') {
        this.handleSearchSubmit()
      }
    }
  }
}
