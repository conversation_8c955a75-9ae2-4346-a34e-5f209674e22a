
import { formatDate } from '@/libs/datetime'
import { isNumber, getKeyValue, isNullOrEmpty } from "@/libs/util"

/**
 * table列自定义方法
 * @type {{methods: {}}}
 */
export const columnRender = {
  methods: {
    /**
     * 自定义渲染带提示框功能
     * @param h
     * @param value
     * @param toolTip
     * @returns {*}
     */
    toolTipRender(h, value, toolTip) {
      if (isNullOrEmpty(value) && isNullOrEmpty(toolTip)) {
        return h('span', '')
      } else {
        if (isNullOrEmpty(toolTip)) {
          toolTip = value
        }
        let returnToolTip = function (options) {
          let contentStyle = {
            whiteSpace: 'normal',
            wordBreak: 'break-all'
          }
          if (isNumber(options.contentHeight)) {
            contentStyle.height = options.contentHeight + 'px'
          }
          return h('Tooltip', {
            class: 'ivu-table-cell-tooltip',
            props: {
              transfer: true,
              theme: options.theme,
              maxWidth: options.maxWidth,
              placement: options.placement
            }
          }, [
            h('span', {
              class: 'ivu-table-cell-tooltip-content'
            }, options.value),
            h('div', {
              slot: 'content',
              style: contentStyle
            }, options.toolTip)
          ])
        }
        if (typeof toolTip === "object") {
          if (typeof this.getCustomRender === 'function') {
            let maxWidth = 300
            if (isNumber(toolTip.maxWidth)) {
              maxWidth = toolTip.maxWidth
            }
            let theme = toolTip.theme || 'dark'
            let contentHeight = toolTip.contentHeight
            let placement = toolTip.placement || 'bottom'
            toolTip = this.getCustomRender(h, toolTip)
            return returnToolTip({
              value: value,
              theme: theme,
              toolTip: toolTip,
              maxWidth: maxWidth,
              placement: placement,
              contentHeight: contentHeight
            })
          } else {
            return returnToolTip({
              value: value,
              toolTip: '请设置自定义渲染方法: getCustomRender(h, toolTip) !'
            })
          }
        } else {
          return returnToolTip({
            value: value,
            toolTip: toolTip
          })
        }
      }
    },
    /**
     * 自定义单元格日期渲染方法
     * @param h
     * @param params
     * @param format
     * @returns {*}
     */
    dateTimeShowRender(h, params, format) {
      if (isNullOrEmpty(format)) {
        format = 'yyyy-MM-dd'
      }
      let dateTimeVal = params.row[params.column.key]
      if (isNullOrEmpty(dateTimeVal)) {
        dateTimeVal = ''
      } else {
        dateTimeVal = formatDate(dateTimeVal, format)
      }
      return h('span', dateTimeVal)
    },
    /**
     * 自定义单元格下拉框渲染方法
     * @param h
     * @param params
     * @param cmbSource
     * @param pCodeKey
     * @param toolTip
     * @param toUpperCase
     * @returns {*}
     */
    cmbShowRender(h, params, cmbSource, pCodeKey, toolTip, toUpperCase) {
      let cmbVal = params.row[params.column.key]
      if (isNullOrEmpty(cmbVal)) {
        cmbVal = ''
      } else if (!isNullOrEmpty(pCodeKey)) {
        if (toUpperCase === true) {
          cmbVal = getKeyValue(this.pcodeGet(pCodeKey, cmbVal.toUpperCase()), cmbVal.toUpperCase())
        } else {
          cmbVal = getKeyValue(this.pcodeGet(pCodeKey, cmbVal), cmbVal)
        }
      } else {
        cmbVal = getKeyValue(cmbSource, cmbVal)
      }
      if (params.column.tooltip === true) {
        return this.toolTipRender(h, cmbVal, toolTip)
      } else {
        return h('span', cmbVal)
      }
    },
    /**
     * 自定义key-value列展示规则
     * @param h
     * @param params
     * @param key
     * @param value
     * @returns {*}
     */
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      if (params.column.tooltip === true) {
        return this.toolTipRender(h, showVal.trim())
      } else {
        return h('span', showVal.trim())
      }
    }
  }
}
