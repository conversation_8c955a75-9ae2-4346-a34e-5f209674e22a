import { namespace } from '@/project'
import DeliveryList from './erp-details/delivery/delivery-list'
import CustomsCheckIList from './customs-check/customs-check-i-list'
import CustomsCheckEList from './customs-check/customs-check-e-list'
import RelatedCheckIList from './related-check/related-check-i-list'
import RelatedCheckEList from './related-check/related-check-e-list'
import WarehousingList from './erp-details/warehousing/warehousing-list'

export default [
  {
    path: '/' + namespace + '/interimVerification/EnterpriseDataImport',
    name: 'warehousingList',
    meta: {
      icon: 'ios-document',
      title: 'ERP入库明细'
    },
    component: WarehousingList
  },
  {
    path: '/' + namespace + '/interimVerification/EnterpriseDataExport',
    name: 'deliveryList',
    meta: {
      icon: 'ios-document',
      title: 'ERP出库明细'
    },
    component: DeliveryList
  },
  {
    path: '/' + namespace + '/interimVerification/CustomsCheckImport',
    name: 'customsCheckIList',
    meta: {
      icon: 'ios-document',
      title: '进口物料核对'
    },
    component: CustomsCheckIList
  },
  {
    path: '/' + namespace + '/interimVerification/CustomsCheckExport',
    name: 'customsCheckEList',
    meta: {
      icon: 'ios-document',
      title: '出口物料核对'
    },
    component: CustomsCheckEList
  },
  {
    path: '/' + namespace + '/interimVerification/RelatedCheckIList',
    name: 'relatedCheckIList',
    meta: {
      icon: 'ios-document',
      title: '进口关联核对'
    },
    component: RelatedCheckIList
  },
  {
    path: '/' + namespace + '/interimVerification/RelatedCheckEList',
    name: 'relatedCheckEList',
    meta: {
      icon: 'ios-document',
      title: '出口关联核对'
    },
    component: RelatedCheckEList
  }
]
