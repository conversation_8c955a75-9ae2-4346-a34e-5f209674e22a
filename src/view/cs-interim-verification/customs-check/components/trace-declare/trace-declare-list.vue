<template>
  <section>
    <XdoCard :bordered="false">
      <div class="action" ref="area_actions">
        <template v-for="item in actions">
          <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                     @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</XdoButton>&nbsp;
        </template>
        <XdoButton type="text" @click="backToList" style="float: right;"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </div>
    </XdoCard>
    <XdoCard :bordered="false">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { traceBackList } from '../../js/traceBackList'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './traceDeclareListColumns'

  export default {
    name: 'traceDeclareList',
    mixins: [traceBackList, columns],
    watch: {
      iemark: {
        immediate: true,
        handler: function (val) {
          if (val === 'I') {
            // 导出文件名称
            this.$set(this.gridConfig, 'exportTitle', '进口申报数量追溯')
            // 地址
            this.$set(this.ajaxUrl, 'exportUrl', csAPI.interimVerification.customsCheck.imports.traceDeclare.exportUrl)
            this.$set(this.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.customsCheck.imports.traceDeclare.selectAllPaged)
          } else if (val === 'E') {
            // 导出文件名称
            this.$set(this.gridConfig, 'exportTitle', '出口申报数量追溯')
            // 地址
            this.$set(this.ajaxUrl, 'exportUrl', csAPI.interimVerification.customsCheck.exports.traceDeclare.exportUrl)
            this.$set(this.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.customsCheck.exports.traceDeclare.selectAllPaged)
          }
        }
      }
    },
    mounted: function () {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
    },
    methods: {
      /**
       * 获取查询条件
       */
      getSearchParams() {
        return {
          facGNo: this.traceData.facGNo,
          linkedNo: this.traceData.linkedNo,
          tradeCode: this.traceData.tradeCode
        }
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
