<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="linkedNo" label="关联单号">
        <XdoIInput type="text" v-model="searchParam.linkedNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="bondMark" label="保完税标志">
        <xdo-select v-model="searchParam.bondMark" :options="this.interimVerification.BONDED_FLAG_SOURCE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="facGNo" label="企业料号">
        <XdoIInput type="text" v-model="searchParam.facGNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="differnce" label="是否差异">
        <xdo-select v-model="searchParam.differnce" :options="this.interimVerification.DIFFERENCE_SOURCE" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="申报日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { interimVerification } from '@/view/cs-common'

  export default {
    name: 'customsCheckSearch',
    data() {
      return {
        searchParam: {
          linkedNo: '',
          bondMark: '',
          facGNo: '',
          differnce: '',
          declareDateFrom: '',
          declareDateTo: ''
        },
        interimVerification: interimVerification
      }
    },
    methods: {
      /**
       * 申报日期范围
       * @param values
       */
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
