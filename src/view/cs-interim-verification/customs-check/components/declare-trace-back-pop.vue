<template>
  <XdoModal width="960" mask v-model="show" title="申报数量追溯"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <DynamicForm :label-width="90" :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
        </DynamicForm>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <XdoCard :bordered="false">
      <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" height="400"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseListConfig } from '@/mixin/generic/baseListConfig'
  import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'

  export default {
    name: 'declareTraceBackPop',
    mixins: [baseSearchConfig, baseListConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      row: {
        type: String,
        default: () => ({})
      },
      popType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['customs', 'related'].includes(value)
        }
      }
    },
    data() {
      let params = this.getParams()
      let fields = this.getFields()
      return {
        autoCreate: false,
        baseParams: [
          ...params
        ],
        baseFields: [
          ...fields
        ],
        cmbSource: {},
        ajaxUrl: {
          exportUrl: '',
          selectAllPaged: ''
        },
        toolbarEventMap: {
          'export': this.handleDownload
        },
        initSearch: false
      }
    },
    created: function () {
      let me = this
      if (me.iEMark === 'I') {
        if (me.popType === 'customs') {
          me.$set(me.ajaxUrl, 'exportUrl', csAPI.interimVerification.customsCheck.imports.traceDeclare.exportUrl)
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.customsCheck.imports.traceDeclare.selectAllPaged)
        } else {
          me.$set(me.ajaxUrl, 'exportUrl', csAPI.interimVerification.relatedCheck.traceBackImport.exportUrl)
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.relatedCheck.traceBackImport.selectAllPaged)
        }
      } else {
        if (me.popType === 'customs') {
          me.$set(me.ajaxUrl, 'exportUrl', csAPI.interimVerification.customsCheck.exports.traceDeclare.exportUrl)
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.customsCheck.exports.traceDeclare.selectAllPaged)
        } else {
          me.$set(me.ajaxUrl, 'exportUrl', csAPI.interimVerification.relatedCheck.traceBackExport.exportUrl)
          me.$set(me.ajaxUrl, 'selectAllPaged', csAPI.interimVerification.relatedCheck.traceBackExport.selectAllPaged)
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            let me = this
            me.$nextTick(() => {
              me.handleSearchSubmit()
            })
          }
        }
      }
    },
    computed: {
      extendParams() {
        if (isNullOrEmpty(this.row.linkedNo)) {
          return {
            queryType: '1',
            facGNo: this.row.facGNo
          }
        } else {
          return {
            queryType: '1',
            facGNo: this.row.facGNo,
            linkedNo: this.row.linkedNo
          }
        }
      },
      linkedNoLabel() {
        if (this.iEMark === 'I') {
          return '入库关联号'
        }
        return '出库关联号'
      },
      /**
       * 动态标签
       */
      dynamicLabel() {
        return {
          linkedNo: this.linkedNoLabel
        }
      }
    },
    methods: {
      actionLoaded() {
        let me = this
        me.actions = [{
          ...me.actionsComm,
          label: '导出',
          command: 'export',
          key: 'xdo-btn-download',
          icon: 'ios-cloud-download-outline'
        }]
      },
      getParams() {
        return [{
          range: true,
          title: '申报日期',
          key: 'declareDate'
        }, {
          key: 'entryNo',
          title: '报关单号'
          // }, {
          //   key: 'linkedNo',
          //   title: '出入库关联号'
        }]
      },
      getFields() {
        return [{
          width: 100,
          key: 'declareDate',
          title: '申报日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        }, {
          width: 180,
          key: 'entryNo',
          title: '报关单号'
        }, {
          width: 160,
          key: 'listNo',
          title: '核注清单编号'
        }, {
          width: 180,
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          width: 160,
          key: 'facGNo',
          title: '企业料号'
        }, {
          width: 160,
          key: 'linkedNo',
          title: '关联单号'
        }, {
          width: 160,
          key: 'qty',
          title: '申报数量'
        }, {
          width: 120,
          key: 'emsNo',
          title: '备案号'
        }, {
          width: 88,
          key: 'serialNo',
          title: '备案序号'
        }, {
          width: 220,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        }]
      },
      handleClose() {
        this.$emit('update:show', false)
      },
      handleTableColumnSetup() {
        this.listSetupShow = true
      },
      /**
       * 导出
       */
      handleDownload() {
        this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
