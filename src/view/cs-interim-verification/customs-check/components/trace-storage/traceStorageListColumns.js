import { getKeyValue } from '@/libs/util'
import { formatDate } from '@/libs/datetime'

const columnsConfig = [
  'sid'
  , 'status'
  , 'bondMark'
  , 'linkedNo'
  , 'warehouseNo'
  , 'warehouseLineNo'
  , 'warehouseGNo'
  , 'warehouseQty'
  , 'warehouseUnit'
  , 'warehouseDate'
  , 'warehouse'
  , 'unit'
  , 'qtyChange'
  , 'copGNo'
  , 'facGNo'
  , 'dataSource'
]

const excelColumnsConfig = [
  'sid'
  , 'statusName'
  , 'bondMark'
  , 'linkedNo'
  , 'warehouseNo'
  , 'warehouseLineNo'
  , 'warehouseGNo'
  , 'warehouseQty'
  , 'warehouseUnit'
  , 'warehouseDate'
  , 'warehouse'
  , 'unit'
  , 'qtyChange'
  , 'copGNo'
  , 'facGNo'
  , 'dataSourceName'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          title: '状态',
          width: 120,
          key: 'status',
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.interimVerification.CHECK_STATUS_MAP, params.row.status))
          }
        },
        {
          title: '状态',
          width: 120,
          key: 'statusName',
          align: 'center'
        },
        {
          title: '保完税标志',
          width: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return h('span', getKeyValue(this.interimVerification.BONDED_FLAG_MAP, params.row.bondMark))
          }
        },
        {
          title: '关联单号',
          width: 120,
          tooltip: true,
          align: 'center',
          key: 'linkedNo'
        },
        {
          title: '出库单号',
          width: 120,
          align: 'center',
          key: 'warehouseNo'
        },
        {
          title: '出库单行号',
          width: 120,
          align: 'center',
          key: 'warehouseLineNo'
        },
        {
          title: '出库料号',
          width: 120,
          align: 'center',
          key: 'warehouseGNo'
        },
        {
          title: '出库数量',
          width: 120,
          align: 'right',
          key: 'warehouseQty'
        },
        {
          title: '出库单位',
          width: 120,
          align: 'center',
          key: 'warehouseUnit'//,
          // render: (h, params) => {
          //   return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.warehouseUnit), params.row.warehouseUnit))
          // }
        },
        {
          title: '出库日期',
          width: 120,
          align: 'center',
          key: 'warehouseDate',
          render: (h, params) => {
            return h('span', formatDate(params.row.warehouseDate, 'yyyy-MM-dd'))
          }
        },
        {
          title: '出库仓别',
          width: 120,
          align: 'center',
          key: 'warehouse'
        },
        {
          title: '申报单位',
          width: 120,
          align: 'center',
          key: 'unit',
          render: (h, params) => {
            return h('span', getKeyValue(this.pcodeGet(this.pcode.unit, params.row.unit), params.row.unit))
          }
        },
        {
          title: '转换申报单位数量',
          width: 120,
          align: 'right',
          key: 'qtyChange'
        },
        {
          title: '备案料号',
          width: 120,
          align: 'center',
          key: 'copGNo'
        },
        {
          title: '企业料号',
          width: 120,
          align: 'center',
          key: 'facGNo'
        },
        {
          title: '数据来源',
          width: 120,
          align: 'center',
          key: 'dataSource',
          render: (h, params) => {
            return h('span', getKeyValue(this.interimVerification.DATA_SOURCE_MAP, params.row.dataSource))
          }
        },
        {
          title: '数据来源',
          width: 120,
          align: 'center',
          key: 'dataSourceName'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
