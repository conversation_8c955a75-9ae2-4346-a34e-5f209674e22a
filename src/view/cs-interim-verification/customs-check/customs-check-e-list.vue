<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        <div style="white-space: nowrap; display: inline-flex;">
          <div style="white-space: nowrap; font-weight: bold; text-align: right; padding-right: 10px;">{{jobInfo.paramsStr}}</div>
          <div style="white-space: nowrap; font-weight: bold; width: 345px;">
            状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后核算时间: {{jobInfo.lastTime}}
          </div>
        </div>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CustomsCheckCalculatePop :show.sync="showCalculate" :in-source="cmbSource" :i-e-mark="iEMark" @doCalculation="doCalculation"></CustomsCheckCalculatePop>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ErpDetailsListPop :show.sync="erpDetailsListPopInfo.show" :i-e-mark="iEMark" mid-type="MID_E_CHECK"
                       :row="erpDetailsListPopInfo.row"></ErpDetailsListPop>
    <DeclareTraceBackPop :show.sync="declareTraceBackPopInfo.show" popType="customs" :i-e-mark="iEMark" :row="declareTraceBackPopInfo.row"></DeclareTraceBackPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { customsCheckListNew } from './js/customsCheckListNew'

  export default {
    name: 'customsCheckEList',
    mixins: [customsCheckListNew],
    data() {
      return {
        listConfig: {
          exportTitle: '出口物料核对'
        },
        ajaxUrl: {
          insertJob: csAPI.interimVerification.jobTask.insertJob,
          getLastJob: csAPI.interimVerification.jobTask.getLastJob,
          exportUrl: csAPI.interimVerification.customsCheck.exports.exportUrl,
          selectAllPaged: csAPI.interimVerification.customsCheck.exports.selectAllPaged
        },
        iEMark: 'E'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .ivu-switch-inner {
    left: 25px;
  }

  /deep/ .ivu-switch-checked > .ivu-switch-inner {
    left: 10px;
  }

  .ivu-switch {
    width: 69px;
  }

  .ivu-switch-checked:after {
    left: 55px;
  }
</style>
