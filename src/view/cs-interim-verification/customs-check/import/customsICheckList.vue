<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CustomsCheckSearch ref="headSearch" iemark="I"></CustomsCheckSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading" style="font-size: 12px;" :class="item.key"
                       @click="item.click" :key="item.label"><XdoIcon :type="item.icon" size="22" class="xdo-icon"/>{{ item.label }}</XdoButton>&nbsp;
          </template>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <CustomsCheckEdit v-if="editShow" @onEditBack="editBack" :editConfig="editConfig" iemark="I"></CustomsCheckEdit>
    <TraceDeclareList v-if="declareTraceShow" @onEditBack="hideTraceBack" :trace-data="traceData" iemark="I"></TraceDeclareList>
    <TraceStorageList v-if="storageTraceShow" @onEditBack="hideTraceBack" :trace-data="traceData" iemark="I"></TraceStorageList>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { customsCheckList } from '../js/customsCheckList'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './customsICheckListColumns'

  export default {
    name: 'CustomsICheckList',
    mixins: [ customsCheckList, columns ],
    data () {
      return {
        ajaxUrl: {
          checkUrl: csAPI.interimVerification.customsCheck.imports.update,
          exportUrl: csAPI.interimVerification.customsCheck.imports.exportUrl,
          selectAllPaged: csAPI.interimVerification.customsCheck.imports.selectAllPaged
        },
        gridConfig: {
          exportTitle: '进口报关核对'
        }
      }
    },
    mounted: function() {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .xdo-btn-check i {
    color: green;
  }

  .xdo-btn-uncheck i {
    color: red;
  }
</style>
