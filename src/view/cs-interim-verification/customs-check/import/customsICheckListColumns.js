import { getKeyValue } from '@/libs/util'
import { formatDate } from '@/libs/datetime'

const columnsConfig = ['selection'
  , 'operation'
  , 'sid'
  , 'status'
  , 'declareDate'
  , 'linkedNo'
  , 'bondMark'
  , 'facGNo'
  , 'qty'
  , 'qtyChange'
  , 'warehouseQty'
  , 'differnce'
  , 'note'
]

const excelColumnsConfig = [
  'sid'
  , 'statusName'
  , 'declareDate'
  , 'linkedNo'
  , 'bondMark'
  , 'facGNo'
  , 'qty'
  , 'qtyChange'
  , 'warehouseQty'
  , 'differnce'
  , 'note'
]

const columns = {
  data() {
    return {
      totalColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          key: 'selection'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: ''
                },
                on: {
                  click: () => {
                    this.handleEditByRow(params.row)
                  }
                }
              }, '编辑'),
              //添加查看按钮
              h('a', {
                props: {
                  type: 'primary',
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    this.handleViewByRow(params.row)
                  },
                },
              }, '查看')
            ]);
          },
          key: 'operation'
        },
        {
          title: '状态',
          minWidth: 120,
          key: 'status',
          align: 'center',
          render: (h, params) => {
            return h('span', getKeyValue(this.interimVerification.CHECK_STATUS_MAP, params.row.status))
          }
        },
        {
          title: '状态',
          minWidth: 120,
          key: 'statusName',
          align: 'center'
        },
        {
          title: '申报日期',
          minWidth: 120,
          align: 'center',
          key: 'declareDate',
          render: (h, params) => {
            return h('span', formatDate(params.row.declareDate, 'yyyy-MM-dd'))
          }
        },
        {
          title: '提取单号',
          width: 120,
          align: 'center',
          tooltip: true,
          key: 'linkedNo'
        },
        {
          title: '保完税标志',
          minWidth: 120,
          align: 'center',
          key: 'bondMark',
          render: (h, params) => {
            return h('span', getKeyValue(this.interimVerification.BONDED_FLAG_MAP, params.row.bondMark))
          }
        },
        {
          title: '企业料号',
          minWidth: 120,
          align: 'center',
          key: 'facGNo'
        },
        {
          title: '申报数量',
          minWidth: 120,
          align: 'right',
          key: 'qty',
          render: (h, params) => {
            return h('a', {
              props: {
                type: 'primary',
                size: 'small'
              },
              style: {
                color: 'blue'
              },
              on: {
                click: () => {
                  this.doTraceBack('qty', params.row)
                }
              }
            }, params.row.qty)
          }
        },
        {
          title: '入库数量',
          minWidth: 120,
          align: 'right',
          key: 'warehouseQty',
          render: (h, params) => {
            return h('a', {
              props: {
                type: 'primary',
                size: 'small'
              },
              style: {
                color: 'blue'
              },
              on: {
                click: () => {
                  this.doTraceBack('wh', params.row)
                }
              }
            }, params.row.warehouseQty)
          }
        },
        {
          title: '入库数量(转申报单位)',
          minWidth: 120,
          align: 'right',
          key: 'qtyChange'
        },
        {
          title: '差异',
          minWidth: 120,
          align: 'right',
          key: 'differnce'
        },
        {
          title: '备注',
          minWidth: 250,
          align: 'left',
          key: 'note'
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
