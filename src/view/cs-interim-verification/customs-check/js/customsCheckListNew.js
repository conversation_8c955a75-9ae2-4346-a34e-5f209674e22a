import { formatDate, isDate } from '@/libs/datetime'
import { isNumber, isNullOrEmpty } from '@/libs/util'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import DeclareTraceBackPop from '../components/declare-trace-back-pop'
import { interimVerification, erpInterfaceData } from '@/view/cs-common'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import CustomsCheckCalculatePop from '../components/customs-check-calculate-pop'
import ErpDetailsListPop from '../../erp-details/components/erp-details-list-pop'
import { detailGeneralMethod } from '@/view/cs-productClassify/base/detailGeneralMethod'

export const customsCheckListNew = {
  name: 'customsCheckListNew',
  mixins: [baseSearchConfig, baseListConfig, detailGeneralMethod],
  components: {
    ErpDetailsListPop,
    DeclareTraceBackPop,
    CustomsCheckCalculatePop
  },
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      cmbSource: {
        emsNo: [],
        gmark: erpInterfaceData.MAT_FLAG_MAP,
        bondMark: interimVerification.BONDED_FLAG_SOURCE,
        updateStatus: interimVerification.CALCULATE_STATUS_MAP
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'calculation': this.handleCalculation
      },
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      showCalculate: false,
      erpDetailsListPopInfo: {
        show: false,
        row: {}
      },
      declareTraceBackPopInfo: {
        show: false,
        row: {}
      }
    }
  },
  created: function () {
    let me = this
    me.getLastJobInfo()
    me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
    // 备案号
    me.getEmsNoList((req) => {
      if (!req.data) {
        return []
      }
      let emsNoDataArr = []
      for (let item of req.data) {
        if (!isNullOrEmpty(item.emsNo)) {
          emsNoDataArr.push({
            value: item.emsNo,
            label: item.emsNo
          })
        }
      }
      me.$set(me.cmbSource, 'emsNo', emsNoDataArr)
    })
  },
  computed: {
    whQtyLabel() {
      if (this.iEMark === 'I') {
        return '入库数量'
      }
      return '出库数量'
    },
    whDateLabel() {
      if (this.iEMark === 'I') {
        return '入库日期'
      }
      return '出库日期'
    },
    linkedNoLabel() {
      if (this.iEMark === 'I') {
        return '入库关联号'
      }
      return '出库关联号'
    },
    qtyConvertLabel() {
      if (this.iEMark === 'I') {
        return '入库数量(转申报单位)'
      }
      return '出库数量(转申报单位)'
    },
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {
        whQty: this.whQtyLabel,
        whDate: this.whDateLabel,
        linkedNo: this.linkedNoLabel,
        qtyConvert: this.qtyConvertLabel
      }
    },
    jobType() {
      return this.iEMark === 'I' ? 'MID_I_CHECK' : 'MID_E_CHECK'
    },
    jobStatusName() {
      let me = this,
        theStatus = me.cmbSource.updateStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }
  },
  methods: {
    getParams() {
      return [{
        range: true,
        title: '申报日期',
        key: 'declareDate2'
      }, {
        range: true,
        key: 'whDate2',
        title: '入库日期'
      }, {
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGNo',
        title: '备案料号'
      }, {
        title: '申报数量',
        key: 'qtyType',
        type: 'radioGroup',
        props: {
          options: [{
            label: '', title: '全部'
          }, {
            label: '0', title: '等于0'
          }, {
            label: '1', title: '不等于0'
          }]
        },
        defaultValue: ''
      }, {
        type: 'select',
        key: 'bondMark',
        title: '保完税标识'
      }, {
        key: 'gmark',
        type: 'select',
        title: '物料标识'
      }, {
        title: '差异',
        key: 'diffType',
        type: 'radioGroup',
        props: {
          options: [{
            label: '', title: '全部'
          }, {
            label: '0', title: '等于0'
          }, {
            label: '1', title: '> 0'
          }, {
            label: '2', title: '< 0'
          }]
        },
        defaultValue: ''
      }]
    },
    getFields() {
      return [{
        width: 100,
        key: 'gmark',
        title: '物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.gmark)
        }
      }, {
        width: 100,
        key: 'bondMark',
        title: '保完税标识',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.bondMark)
        }
      }, {
        width: 160,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 160,
        key: 'copGNo',
        title: '备案料号'
      }, {
        width: 130,
        tooltip: true,
        key: 'linkedNo',
        title: '入库关联号'
      }, {
        key: 'qty',
        width: 150,
        tooltip: true,
        title: '申报数量',
        render: (h, params) => {
          let qty = params.row['qty']
          if (isNumber(qty) && Number(qty) > 0) {
            return h('a', {
              props: {
                type: 'primary',
                size: 'small'
              },
              style: {
                color: 'blue'
              },
              on: {
                click: () => {
                  this.doShowQty(params.row)
                }
              }
            }, qty)
          }
          return h('span', qty)
        }
      }, {
        width: 150,
        key: 'whQty',
        tooltip: true,
        title: '入库数量',
        render: (h, params) => {
          let whQty = params.row['whQty']
          if (isNumber(whQty) && Number(whQty) > 0) {
            return h('a', {
              props: {
                type: 'primary',
                size: 'small'
              },
              style: {
                color: 'blue'
              },
              on: {
                click: () => {
                  this.doShowWhQty(params.row)
                }
              }
            }, whQty)
          }
          return h('span', whQty)
        }
      }, {
        width: 150,
        tooltip: true,
        key: 'qtyConvert',
        title: '入库数量(转申报单位)'
      }, {
        width: 150,
        title: '差异',
        tooltip: true,
        key: 'diff',
        render: (h, params) => {
          let diff = params.row['diff']
          if (isNumber(diff) && Number(diff) < 0) {
            // 此处待修改
            return h('span', {
              style: {
                color: 'red'
              },
            }, String(diff))
          } else {
            if (!isNumber(diff)) {
              diff = ''
            }
            return h('span', String(diff))
          }
        }
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        me.$set(me.jobInfo, 'status', res.data.data['status'])

        let lastParams = res.data.data['extendFiled5'],
          lastParamsJson = {}
        if (!isNullOrEmpty(lastParams)) {
          try {
            lastParamsJson = JSON.parse(lastParams)
          } catch (e) {
            lastParamsJson = {}
          }
        }
        me.fillCalculateParams(lastParamsJson)

        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    },
    /**
     * 展示申报数量
     * @param row
     */
    doShowQty(row) {
      let me = this,
        myRow = deepClone(row)
      me.$set(me.declareTraceBackPopInfo, 'show', true)
      delete myRow.linkedNo
      me.$set(me.declareTraceBackPopInfo, 'row', myRow)
    },
    /**
     * 展示入库数量
     * @param row
     */
    doShowWhQty(row) {
      let me = this,
        myRow = deepClone(row)
      me.$set(me.erpDetailsListPopInfo, 'show', true)
      delete myRow.linkedNo
      me.$set(me.erpDetailsListPopInfo, 'row', myRow)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 计算
     */
    handleCalculation() {
      this.$set(this, 'showCalculate', true)
    },
    validDate(date) {
      if (date instanceof Date) {
        return true
      }
      if (isDate(date)) {
        return true
      }
      return false
    },
    /**
     * 分析并设置计算条件
     * @param params
     */
    fillCalculateParams(params) {
      let me = this,
        paramsStr = ''
      if (me.validDate(params.declareDateFrom) || me.validDate(params.declareDateTo)) {
        paramsStr = '申报: '
        if (me.validDate(params.declareDateFrom)) {
          paramsStr += formatDate(params.declareDateFrom, 'yyyy-MM-dd')
        }
        paramsStr += ' ~ '
        if (me.validDate(params.declareDateTo)) {
          paramsStr += formatDate(params.declareDateTo, 'yyyy-MM-dd')
        }
        paramsStr += ', '
      }
      if (me.validDate(params.whDateFrom) || me.validDate(params.whDateTo)) {
        if (me.iEMark === 'I') {
          paramsStr += '入库: '
        } else {
          paramsStr += '出库: '
        }
        if (me.validDate(params.whDateFrom)) {
          paramsStr += formatDate(params.whDateFrom, 'yyyy-MM-dd')
        }
        paramsStr += ' ~ '
        if (me.validDate(params.whDateTo)) {
          paramsStr += formatDate(params.whDateTo, 'yyyy-MM-dd')
        }
        paramsStr += ', '
      }
      if (!isNullOrEmpty(params.emsNo)) {
        paramsStr += params.emsNo + ', '
      }
      if (params.bondMark === '0') {
        paramsStr += '保税'
      } else if (params.bondMark === '1') {
        paramsStr += '非保税'
      }
      me.$set(me.jobInfo, 'paramsStr', paramsStr)
    },
    /**
     * 执行计算
     */
    doCalculation(params) {
      let me = this
      me.$set(me, 'showCalculate', false)
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType,
        checkParam: params
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
      me.fillCalculateParams(params)
    }
  },
  beforeDestroy: function () {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
