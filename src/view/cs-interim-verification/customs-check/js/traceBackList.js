import { commList } from '@/view/cs-interim-verification/comm/commList'

export const traceBackList = {
  mixins: [ commList ],
  props: {
    iemark: {
      type: String,
      required: true,
      validate: function(value) {
        return ['I', 'E'].includes(value)
      }
    },
    traceData: {
      type: Object,
      default: () => ({
        facGNo: '',
        linkedNo: '',
        tradeCode: ''
      })
    }
  },
  data () {
    return {
      myDynamicHeight: 108,
      actions: [{
        type: 'text',
        needed: true,
        loading: false,
        disabled: false,
        label: '导出',
        key: 'xdo-btn-download',
        click: this.handleDownload,
        icon: 'ios-cloud-download-outline'
      }],
      ajaxUrl: {
        exportUrl: '',
        selectAllPaged: ''
      },
      gridConfig: {
        exportTitle: ''
      }
    }
  },
  methods: {
    /**
     * 下载
     */
    handleDownload () {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 3)
    },
    /**
     * 返回列表界面
     */
    backToList () {
      this.$emit('onEditBack')
    }
  }
}
