<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { interimVerification, erpInterfaceData } from '@/view/cs-common'

  export default {
    name: 'erpDetailsEdit',
    mixins: [baseDetailConfig],
    props: {
      iEMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        cmbSource: {
          gmark: erpInterfaceData.MAT_FLAG_MAP,
          bondMark: interimVerification.BONDED_FLAG_SOURCE,
          dataSource: interimVerification.DATA_SOURCE_SOURCE
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.fieldsReset()
          me.buttons[me.buttons.findIndex(btn => btn.command === 'save')].needed = !me.showDisable
        }
      }
    },
    created: function () {
      let me = this
      if (me.iEMark === 'I') {
        me.$set(me.ajaxUrl, 'insert', csAPI.interimVerification.erpDetails.warehousing.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.interimVerification.erpDetails.warehousing.update)
      } else {
        me.$set(me.ajaxUrl, 'insert', csAPI.interimVerification.erpDetails.delivery.insert)
        me.$set(me.ajaxUrl, 'update', csAPI.interimVerification.erpDetails.delivery.update)
      }
    },
    computed: {
      qtyLabel() {
        if (this.iEMark === 'I') {
          return '入库数量(转申报单位)'
        }
        return '出库数量(转申报单位)'
      },
      whNoLabel() {
        if (this.iEMark === 'I') {
          return '入库单号'
        }
        return '出库单号'
      },
      whGNoLabel() {
        if (this.iEMark === 'I') {
          return '入库料号'
        }
        return '出库料号'
      },
      whDateLabel() {
        if (this.iEMark === 'I') {
          return '入库日期'
        }
        return '出库日期'
      },
      whStoreLabel() {
        if (this.iEMark === 'I') {
          return '入库仓别'
        }
        return '出库仓别'
      },
      whLineNoLabel() {
        if (this.iEMark === 'I') {
          return '入库单行号'
        }
        return '出库单行号'
      },
      whQtyLabel() {
        if (this.iEMark === 'I') {
          return '入库数量'
        }
        return '出库数量'
      },
      whUnitLabel() {
        if (this.iEMark === 'I') {
          return '入库单位'
        }
        return '出库单位'
      },
      /**
       * 动态标签
       */
      dynamicLabel() {
        return {
          qty: this.qtyLabel,
          whNo: this.whNoLabel,
          whQty: this.whQtyLabel,
          whGNo: this.whGNoLabel,
          whUnit: this.whUnitLabel,
          whDate: this.whDateLabel,
          whStore: this.whStoreLabel,
          whLineNo: this.whLineNoLabel
        }
      }
    },
    methods: {
      getFields() {
        return [{
          type: 'select',
          key: 'bondMark',
          title: '保完税标志'
        }, {
          key: 'gmark',
          type: 'select',
          title: '物料类型'
        }, {
          key: 'linkedNo',
          title: '关联单号'
        }, {
          key: 'whGNo',
          title: '出/入库料号'
        }, {
          key: 'whNo',
          title: '出/入库单号'
        }, {
          key: 'whLineNo',
          title: '出/入库单行号'
        }, {
          key: 'whQty',
          type: 'xdoInput',
          title: '出/入库数量',
          props: {
            intDigits: 8,
            precision: 3
          }
        }, {
          key: 'whUnit',
          title: '出/入库单位'
        }, {
          key: 'whDate',
          title: '出/入库日期'
        }, {
          key: 'whStore',
          title: '出/入库仓别'
        }, {
          type: 'pcode',
          key: 'unit',
          title: '申报单位',
          props: {
            meta: 'UNIT'
          }
        }, {
          key: 'qty',
          type: 'xdoInput',
          title: '出/入库数量(转申报单位)',
          props: {
            intDigits: 8,
            precision: 3
          }
        }, {
          key: 'copGNo',
          title: '备案料号'
        }, {
          type: 'select',
          title: '数据来源',
          key: 'dataSource'
        }]
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
