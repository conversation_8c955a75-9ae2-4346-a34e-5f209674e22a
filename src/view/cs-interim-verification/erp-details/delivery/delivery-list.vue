<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions" style="display: flex; align-items: center; justify-content: space-between; background-color: white;">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:export>
            <ExportAsync :param="taskInfo" :click="onExportClick" :columns="exportHeader" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
        <div style="font-weight: bold; width: 345px; white-space: nowrap;">状态: <span :style="jobStatusStyle">{{jobStatusName}}</span>   最后同步时间: {{jobInfo.lastTime}}</div>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <ErpDetailsEdit v-if="!showList" :edit-config="editConfig" @onEditBack="editBack" :i-e-mark="iEMark"></ErpDetailsEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { erpDetailsList } from '../js/erpDetailsList'

  export default {
    name: 'deliveryList',
    mixins: [erpDetailsList],
    data() {
      return {
        listConfig: {
          exportTitle: '出库明细信息'
        },
        ajaxUrl: {
          insertJob: csAPI.interimVerification.jobTask.insertJob,
          getLastJob: csAPI.interimVerification.jobTask.getLastJob,
          deleteUrl: csAPI.interimVerification.erpDetails.delivery.delete,
          exportUrl: csAPI.interimVerification.erpDetails.delivery.exportUrl,
          selectAllPaged: csAPI.interimVerification.erpDetails.delivery.selectAllPaged
        },
        iEMark: 'E',
        taskInfo: {
          taskCode: 'MID_EXG_E'     // 添加任务使用的taskCode
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
