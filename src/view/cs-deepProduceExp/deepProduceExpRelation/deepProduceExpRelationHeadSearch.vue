<template>
  <section v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="zcfhgswdm" label="转入海关十位代码">
        <xdo-select  v-model="searchParam.tradeCodeIn" :options="this.listImp"
                     :optionLabelRender="pcodeRender" @on-change="change" ></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="zcfszch" label="转入手/账册号">
        <XdoIInput type="text" v-model="searchParam.emsNoIn"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem  label="转入手/账册有效期">
        <XdoDatePicker type="date" v-model="searchParam.validDateIn" placeholder="请选择有效期" style="width: 100%" transfer></XdoDatePicker>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { delList } from '../../cs-common'

  export default {
    name: 'DecEntryHeadFormalHeadSearch',
    components: {

    },
    data() {
      return {
        searchParam: {
          tradeCodeIn:'',//转入方海关十位代码
          emsNoIn:'', //转入方手账册号
          validDateIn:'' //转入方有效日期
        },
        inDataSource:{
          documnetMark: delList.documnetMark,
          documnetStatue: delList.documnetStatue,
          I_E_MARK: delList.I_E_MARK,
          templateErrorType: delList.templateErrorType,
        },
        listImp:[],//改数组用来存放转出方名称及代码
      }
    },
    created: function () {
      this.getHgswm();
    },
    methods: {
      getHgswm(){
        console.log('走后台获取海关')
        this.$http.post(csAPI.ieParams.GETOUTHGSWDM).then(res => {
          if (res.status === 200) {
            if (res.data.success === true) {
              this.listImp = ArrayToLocaleLowerCase(res.data.data)
            } else {
              this.listImp = []
            }
          }
        }).catch(() => {
          this.$Message.warning('该客户还未维护海关代码!')
          this.listImp=[]
        })
      },

      change(){
        console.log('获取下拉值')
        console.log(this.searchParam.tradeCodeIn)
      }

    }
  }
</script>
<style scoped>
</style>
