/***
 * 关务-深加工出口-路由
 */
import { namespace } from '@/project'
import deepProduceExpDetailHeadList from './deepProduceExpDetail/deepProduceExpDetailHeadList'
import deepProduceExpFromToHeadList from './deepProduceExpFromTo/deepProduceExpFromToHeadList'
import deepProduceExppickUpHeadList from './deepProduceExppickUp/deepProduceExppickUpHeadList'
import deepProduceExpRelationHeadList from './deepProduceExpRelation/deepProduceExpRelationHeadList'

export {
  deepProduceExpDetailHeadList,   //备案关系明细
  deepProduceExpFromToHeadList,   // 深加工转入数据管理
  deepProduceExppickUpHeadList,   // 深加工转入信息提取
  deepProduceExpRelationHeadList  //备案关系维护
}

export default  [
  {
    path: '/' + namespace + '/deepProduceExp/relation',
    name: 'deepProduceExpRelationHeadList',
    meta: {
      title: '备案关系维护'
    },
    component: deepProduceExpRelationHeadList
  },
  {
    path: '/' + namespace + '/deepProduceExp/relationDetail',
    name: 'deepProduceExpDetailHeadList',
    meta: {
      title: '备案关系明细'
    },
    component: deepProduceExpDetailHeadList
  },
  {
    path: '/' + namespace + '/deepProduceExp/managedata',
    name: 'deepProduceExpFromToHeadList',
    meta: {
      title: '转入数据管理'
    },
    component: deepProduceExpFromToHeadList
  },
  {
    path: '/' + namespace + '/deepProduceExp/pickUp',
    name: 'deepProduceExppickUpHeadList',
    meta: {
      title: '转入信息提取'
    },
    component: deepProduceExppickUpHeadList
  }
]
