<template>
  <section v-focus>
    <XdoForm style="margin-top: 80px;margin-top: 80px"  ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :rules="ruleValidate" :label-width="130" inline>
      <XdoFormItem prop="emsCopNoOut" label="转出内部编号">
<!--        <Input type="text" style="width: 150px" v-model="searchParam.emsCopNoIn"></Input>-->
        <xdo-select style="width: 160px" v-model="searchParam.emsCopNoOut" :options="this.listZcfszch"></xdo-select>

      </XdoFormItem>
      <div style="width: 400px">
        <dc-dateRange  label="结转周期"  @onDateRangeChanged="handleDMDateChange"></dc-dateRange>
      </div>
    </XdoForm>
    <div style="margin-left: 280px;margin-top: 80px " >
      <XdoButton style="width: 100px" type="primary"  class="dc-margin-right" @click="handleSearchSubmit">确定</XdoButton>
      <XdoButton style="width: 100px" type="warning" class="dc-margin-right" @click="close">取消</XdoButton>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'

  export default {
    name: 'DecEntryHeadFormalHeadSearch',
    props: {
      typeNoInfor: {type: Array, default: () => ({})},//传值List页面中的 typeNo
      flag: {type: String, default: () => ({})},//传值List页面中的 typeNo
      searchCon: {type: Object, default: () => ({})},//传值List页面中的 typeNo
    },
    data() {
      return {
        listZcfszch: [],//转入内部编号
        searchParam: {
          emsCopNoOut: '',//转出内部编号
          beginDate: '',//日期开始
          endDate: '',//日期结束
        },
        ruleValidate: {
          emsCopNoOut: [{required: true, message: '不能为空！', trigger: 'blur'}],
          beginDate: [{required: true, message: '不能为空！', trigger: 'blur'}],
          endDate: [{required: true, message: '不能为空！', trigger: 'blur'}]
        },
        sids: []
      }
    },
    mounted: function () {
      this.getZrnbbh() //加载获取转入方手账册号
      //接受父页面传过来的参数
      this.typeNoInforMethods(this.typeNoInfor) //接受父页面传过来的当前所勾选的数据的sid值
    },
    methods: {
      //获取转入内部编号
      getZrnbbh() {
        this.$http.post(csAPI.deep.deepImpRecord.head.getOutNbbh.getOutNbbh).then(res => {
          if (res.data.success === true) {
            this.listZcfszch = res.data.data
          }
        }).catch(() => {
          this.listZcfszch = ''
        })
      },
      typeNoInforMethods(info) {
        //遍历e数组，获取其中的sid
        info.forEach(item => {
          this.sids.push(item.sid)
        })
      },
      handleDMDateChange(e) {
        this.searchParam.beginDate = e[0];//转出方手账册有效期开始时间赋值（e默认传数组，第一个元素为开始时间，第一个为开始时间）
        this.searchParam.endDate = e[1];//转出方手账册有效期结束时间赋值（e默认传数组，第一个元素为开始时间，第二个为结束时间）
      },
      //页面点击确定的时候走的方法，1：拿当前的转入内部编号去表头中找到当前的headId,并且把当前的  结转周期更新为最新的周期，
      handleSearchSubmit() {
        console.log(this.flag)
        console.log(this.searchCon)
        if (this.searchParam.emsCopNoOut === '' || this.searchParam.emsCopNoOut === null) {
          this.$Message.warning('请输入转出内部编号!')
          return
        }
        // else if(this.searchParam.beginDate===''||this.searchParam.endDate===''||this.searchParam.beginDate===null||this.searchParam.beginDate===null){
        //   this.$Message.warning('请输入结转周期时间!')
        //   return
        // }
        let fromDate = 1
        let toDate = 1
        if (this.flag === 0) {
          if (this.sids === '' || this.sids === null) {
            this.$Message.warning('请勾选数据!')
            return;
          } else {
            //确认勾选 传值 sids到后台
            // this.typeNoInfor  //sid[]

            this.$http.post(csAPI.deep.deepImpRecord.head.confirmOutSomeBodyList.outConfirmSome + `/${this.searchParam.emsCopNoOut}` + `/${fromDate}` + `/${toDate}`, this.sids).then(res => {
              // this.$http.post(csAPI.deep.deepImpRecord.head.confirmSomeBodyList.confirmSome+`/${this.searchParam.emsCopNoIn}`+`/${this.searchParam.beginDate}`+`/${this.searchParam.endDate}`,this.sids).then(res => {
              if (res.data.success === true) {
                this.$Message.success(res.data.data);
                this.close();
              } else {
                this.$Message.warning(res.data.data);
                this.close();
              }
            }, () => {
            })
          }
        } else if (this.flag === 1) {
          //确认全部 传值查询条件，以对象的形式查询
          // this.searchCon  // {}
          this.$http.post(csAPI.deep.deepImpRecord.head.confirmOutAllBodyList.outConfirmAll + `/${this.searchParam.emsCopNoOut}` + `/${fromDate}` + `/${toDate}`, this.searchCon).then(res => {
            if (res.data.success === true) {
              this.$Message.success(res.data.data);
              this.$emit('result', true) //页面自动关闭
              this.close();
            } else {
              this.$Message.warning(res.data.data);
              this.$emit('result', true) //页面自动关闭
              this.close();
            }
          }, () => {
          })
        }
      },
      //点击关闭的时候走的js方法
      close() {
        this.$emit('close', false)
      }
    }
  }
</script>

<style scoped>
</style>
