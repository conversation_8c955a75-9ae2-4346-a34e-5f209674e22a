<template>
  <section v-focus>
    <XdoCard :bordered="false">
      <div class="action" ref="area_actions" style="margin-bottom: 5px">
        <template v-for="item in actions">
          <XdoButton v-if="item.needed === true"  :type="item.type" :disabled="item.disabled" :loading="item.loading"
                  style="font-size: 12px" :class="item.key"
                  @click="item.click" :key="item.label">
            <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
            {{ item.label }}
          </XdoButton>&nbsp;
        </template>
      </div>

      <div v-show="isxy">
        <XdoForm  ref="addParams.headParam" class="dc-form dc-form-4"  label-position="right" :label-width="100" :model="addParams.headParam" :rules="ruleValidate" inline>
          <XdoFormItem prop="facGNoIn" label="转出企业料号">
            <XdoIInput type="text" v-model="bodyMidSearchParam.facGNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNoIn" label="转出备案料号">
            <XdoIInput type="text"  v-model="bodyMidSearchParam.copGNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNoIn" label="转出方备案序号">
            <XdoIInput type="text"  v-model="bodyMidSearchParam.gNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gNoOut" label="转入方备案序号">
            <XdoIInput type="text"  v-model="bodyMidSearchParam.gNoIn"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="entryGNo" label="报关序号">
            <XdoIInput type="text"  v-model="bodyMidSearchParam.entryGNo"></XdoIInput>
          </XdoFormItem>
        </XdoForm>
      </div>
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
             :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
              show-total show-sizer
              @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>

    </XdoCard>
  </section>
</template>

<script>
  import { csAPI, excelExport } from '@/api'
  //  import {ArrayToLocaleLowerCase} from '@/libs/util'
  import { dynamicHeight, getColumnsByConfig,getExcelColumnsByConfig} from '@/common'
  import { columnsConfig, columns,excelColumnsConfig } from './deepProduceExpFromToDetaiListColumns'

  export default {
    name: "deepProduceExpFromToDetailList",
    mixins: [dynamicHeight, columns],
    props:{
      typeNoInfor:{type: String, default: () => ({})},//传值List页面中的 typeNo
      searchDataInfor: {type: Object, default: () => ({})},
      textDataInfor:{type: Object, default: () => ({})},
//      compData:{type: Array, default: () => ({})},//list页面传过来的数据
    },
    data(){
      return{
        actions: [
          { type: 'text', disabled: false, click: this.exportBody, icon: 'ios-cloud-download-outline', label: '导出', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.getListByHeadId, icon: 'ios-paper-plane-outline', label: '查询', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.cx, icon: 'ios-arrow-dropdown', label: '查询条件', key:'xdo-btn-download', loading: false, needed: true }
        ],
        addParams:{

          headParam:{
            tradeMode:'',//监管方式
            emsCopNoIn:'',//转入内部编号
            beginDate:'',// 结转日期开始
            endDate:'',//结转日期结束
            emsNoOut:'',//转出方备案号
            tradeCodeOutName:'',//转出方名称
            tradeNameOut:'',//转出方名称
            customerCodeOut:'',//转出方代码
            tradeCodeOut:'',//转出方海关十位码
            emsNoIn:'',//转入方备案号
            applyNo:'',//申请表编号
            entryNoIn:'',//转入报关单号
            entryNoOut:'',//转出报关单号
            dataSource:'',//数据来源
            tradeCodeIn:'',//转入方海关十位代码
            status:'0',//状态
            remark:''//备注
          },
        },

        bodyMidSearchParam:{
          facGNoOut:'',//备案序号(转入)
          copGNoOut:'',//备案料号(转出)
          gNoOut:'',//备案料号
          gNoIn:'',//转出备案序号
          entryGNo:'',//报关序号



          headId:'' //表头id
        },

        headID:'',//表头的sid
        pageParam: {   //分页获取表体数据
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        ruleValidate:{
          customerCodeOut: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          tradeCodeOut: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          emsNoOut: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          emsNoIn: [{ required: true, message: '不能为空！', trigger: 'blur' }]
        },
        bodySearchParam:{  //标题查询条件
          copGNoOut:'', //转出备案料号
          gNoOut:'', //转出备案序号
          copGNoIn:'', //转入备案料号
          gNoIn:'', //转入备案序号
          facGNoIn:'', //转出备案料号
          headId:''//表头id
        },
        isxy:false,
        sid:'',
      }
    },
    created() {

    },
    mounted(){
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.searchDataInforMethods(this.searchDataInfor)// 查看的方法，一面一加载就走js方法

    },
    watch:{
    },
    methods:{
      //把list页面传过来的值接受并且赋值给表头各个框
      searchDataInforMethods(val){
        this.bodyMidSearchParam.headId = val.sid;
      },
      //根据当前表头id、查询参数 获取下面所有的列表结果值
      getListByHeadId(){
        let pageParam = {
          page:this.pageParam.page,
          limit:this.pageParam.limit
        }
        console.log(this.bodyMidSearchParam)
        const data = this.bodyMidSearchParam  //此为查询框里面的查询条件值
        // console.log(data)
        this.$http.post(csAPI.deep.deepImpRecord.head.geteXgFromToBillList.selectExpBillDelAllPaged, data, { params: pageParam }).then(res => {
          console.log(res.data.data)
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
          this.bodyMidSearchParam.gNoIn=''
          this.bodyMidSearchParam.facGNoIn=''
          this.bodyMidSearchParam.copGNoIn=''
          this.bodyMidSearchParam.gNoOut=''
          this.bodyMidSearchParam.entryGNo=''

        })
      },

      //点击列表页面编辑的时候获取当前表头id下面的表体所有数据
      getBodyList(){
        //获取表体数据  getBodyList
        let pageParam = {
          page:this.pageParam.page,
          limit:this.pageParam.limit
        }
        console.log(this.bodyMidSearchParam)
        const data = this.bodyMidSearchParam  //此为查询框里面的查询条件值
        this.$http.post(csAPI.deep.deepImpRecord.head.geteXgFromToBillList.selectExpBillDelAllPaged, data, { params: pageParam }).then(res => {
          console.log('wh')
          console.log(res.data.data)

          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total

        })

      },

      cx(){
        this.isxy=!this.isxy
      },



      //点击表体数据导出
      exportBody(){
        let bodList={
          matchResult:'',//匹配结果
          facGNoIn:'',//企业料号
          copGNoIn:'',//备案料号
          unit:'',//申报计量单位
          decQty:'',//数量
          decPrice:'',//单价
          decTotal:'',//总价
          curr:'',//币制
          gNoOut:'',//转出方备案序号
          gNoIn:'',//转入方备案序号
          entryGNo:'',//报关序号
          headId:this.searchDataInfor.sid //把表头sid赋值给表体headId
        }
        const params = {
          exportColumns: Object.assign({}, bodList),
          name: '备案成品转出清单数据.xls',
          header: getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
        }
        excelExport(csAPI.deep.deepImpRecord.head.exportExgFromToBillList, params).finally(() => {
        })
      },





      forId(index){
        let res = "forid_" +index
        return res

      },




      pageChange(page) {
        this.pageParam.page = page
        this.getBodyList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        if(this.pageParam.page === 1){
          this.getBodyList()
        }
      },

      //点击表体查询的时候走的方法
      handleSearchSubmit() {
        this.pageParam.page = 1
        this.getBodyList() //走一下后台的初始化方法
      },

      //选中赋值
      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      }



    },


  }
</script>

<style lang="less" scoped>
  section{
    display: flex;
  }
  section div{
    flex: 1;
  }
  ul li{
    display: inline-block;
    list-style: none;
    margin-left: 15px;
    color: #999;
  }

</style>
