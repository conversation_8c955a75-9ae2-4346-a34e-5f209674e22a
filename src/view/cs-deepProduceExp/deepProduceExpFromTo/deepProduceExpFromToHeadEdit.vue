<template>
  <section v-focus>
    <!--左边部分-->
    <XdoCard  class="dataManageleft">
      <XdoCard :bordered="false">
        <div ref="area_head">
            <XdoButton type="primary" class="dc-margin-right" :disabled="bomm1" @click="saveHeader">保存</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="closeAdd">关闭</XdoButton>

        </div>
        <div>
          <XdoForm ref="addParams.headParam" class="dc-form dc-form-4"  label-position="right" :label-width="110" :model="addParams.headParam" :rules="ruleValidate" inline>
            <XdoFormItem prop="emsNo" label="手帐册号">
              <xdo-select  v-model="addParams.headParam.emsNo" :disabled="chaxunEdit" :options="this.listSzch" @on-change="getThreeParamsChange"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="tradeCodeInName" label="转入方名称及代码">
              <XdoIInput type="text" :disabled="true" v-model="addParams.headParam.tradeCodeInName"></XdoIInput>
            </XdoFormItem>



            <XdoFormItem prop="emsCopNoOut" label="转出内部编号">
              <XdoIInput type="text" :maxlength="20" :disabled="chaxunEdit"  v-model="addParams.headParam.emsCopNoOut"></XdoIInput>
            </XdoFormItem>


            <XdoFormItem  label="结转周期">
              <XdoDatePicker type="date" :disabled="chaxun"  v-model="addParams.headParam.beginDate" placeholder="开始时间" style="width: 105px" transfer></XdoDatePicker>
              <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
              <XdoDatePicker type="date" :disabled="chaxun" v-model="addParams.headParam.endDate" placeholder="结束时间" style="width: 105px" transfer></XdoDatePicker>
            </XdoFormItem>
<!--            <FormItem prop="emsNoIn" label="转入方备案号">-->
<!--              <xdo-select  v-model="addParams.headParam.emsNoIn" :disabled="chaxun" :options="this.listZcfszch" @on-change="getZcfMjd"></xdo-select>-->
<!--            </FormItem>-->

<!--            <FormItem prop="emsNoOut" label="转出方备案号">-->
<!--              <xdo-select  v-model="addParams.headParam.emsNoOut" :disabled="chaxun" :options="this.listZrfszch"></xdo-select>-->
<!--            </FormItem>-->
            <XdoFormItem prop="applyNo" label="申请表编号">
              <XdoIInput type="text" :maxlength="18"  :disabled="chaxun" v-model="addParams.headParam.applyNo"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="entryNoIn" label="转入报关单号">
              <XdoIInput type="text" :maxlength="18"  :disabled="chaxun" v-model="addParams.headParam.entryNoIn"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="entryNoOut" label="转出报关单号">
              <XdoIInput type="text" :maxlength="18"  :disabled="chaxun" v-model="addParams.headParam.entryNoOut"></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="tradeMode" label="监管方式">
              <xdo-select  v-model="addParams.headParam.tradeMode" :disabled="chaxun" :options="this.inDataSource.jgfs"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="remark" label="备注" class="dc-merge-1-5">
              <XdoIInput type="text" :maxlength="50"  :disabled="chaxun" v-model="addParams.headParam.remark"></XdoIInput>
            </XdoFormItem>
          </XdoForm>
        </div>

      </XdoCard>
      <XdoCard :bordered="false">
        <div :hidden="bomm" class="action" ref="area_actions" style="margin-bottom: 5px">
          <template v-for="item in actions">
            <XdoButton v-if="item.needed === true"  :type="item.type" :disabled="item.disabled" :loading="item.loading"
                    style="font-size: 12px" :class="item.key"
                    @click="item.click" :key="item.label">
              <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
              {{ item.label }}
            </XdoButton>&nbsp;
          </template>
        </div>

        <XdoForm id="isYc" ref="addParams.headParam" class="dc-form dc-form-4"  label-position="right" :label-width="100" :model="addParams.headParam" :rules="ruleValidate" inline>
          <XdoFormItem prop="facGNoOut" label="企业料号">
            <XdoIInput type="text" :disabled="bomm" v-model="bodyMidSearchParam.facGNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNoOut" label="备案料号">
            <XdoIInput type="text" :disabled="bomm" v-model="bodyMidSearchParam.copGNoOut"></XdoIInput>
          </XdoFormItem>

          <XdoFormItem prop="copGNoIn" label="备案料号(转入)">
            <XdoIInput type="text"  :disabled="bomm" v-model="bodyMidSearchParam.copGNoIn"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gNoIn" label="备案序号(转入)">
            <XdoIInput type="text" :disabled="bomm" v-model="bodyMidSearchParam.gNoIn"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="entryGNo" label="报关序号">
            <XdoIInput type="text" :disabled="bomm" v-model="bodyMidSearchParam.entryGNo"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="matchResult" label="匹配结果">
            <xdo-select v-model="bodyMidSearchParam.matchResult" :disabled="bomm" :options="this.inDataSource.status" ></xdo-select>
          </XdoFormItem>

        </XdoForm>
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
               :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                show-total show-sizer
                @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>

      </XdoCard>

      <XdoCard :bordered="false">
        <XdoForm ref="bodyParam" class="dc-form dc-form-4"  label-position="right" :label-width="100" :model="addParams.headParam" :rules="ruleValidate" inline>
          <XdoFormItem prop="facGNoOut" label="转出企业料号">
            <xdo-select :maxlength="20" v-model="bodyParam.facGNoOut" :disabled="bomm" :options="this.listZrqtlh"   @on-change="inToOther"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="copGNoOut" label="转出备案料号">
            <XdoIInput type="text" :maxlength="32"  :disabled="true" v-model="bodyParam.copGNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gNoOut" label="转出备案序号">
            <XdoIInput type="text"  :disabled="true"  v-model="bodyParam.gNoOut"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="unit" label="申报计量单位">
            <XdoIInput type="text"  :disabled="true"  v-model="bodyParam.unit"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="curr" label="币制">
            <xdo-select :maxlength="3" v-model="bodyParam.curr" :disabled="bomm" :asyncOptions="pcodeList"
                        :meta="pcode.curr_outdated" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="copGNoIn" label="转入方备案料号">
            <!--<Input type="text" :disabled="chaxun"  v-model="bodyParam.copGNoOut"></Input>-->
            <xdo-select :maxlength="20" mixer v-model="bodyParam.copGNoIn" :disabled="bomm" :options="this.zcList"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="gNoIn" label="转入方备案序号">
            <xdo-select :maxlength="20"  mixer v-model="bodyParam.gNoIn" :disabled="bomm" :options="this.zcListxh"></xdo-select>
          </XdoFormItem>

          <!--          <FormItem prop="grossWt" label="毛重">-->
          <!--            <xdo-input v-model="frmData.grossWt" decimal int-length="10" precision="5" :disabled="showDisable"></xdo-input>-->
          <!--          </FormItem>-->

          <XdoFormItem prop="decPrice" label="单价">
<!--            <Input type="text" :disabled="bomm" :maxlength="2"  v-model="bodyParam.decPrice"></Input>-->
            <xdo-input v-model="bodyParam.decPrice" notConvertNumber decimal int-length="11" precision="5" :disabled="bomm"></xdo-input>
          </XdoFormItem>


          <XdoFormItem prop="decQty" label="数量">
<!--            <Input type="text" :disabled="bomm" number int-length="19" v-model="bodyParam.decQty"></Input>-->
            <xdo-input v-model="bodyParam.decQty" notConvertNumber decimal int-length="11" precision="5" :disabled="bomm"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="decTotal" label="总价">
<!--            <Input type="text" :disabled="bomm" :maxlength="2" v-model="bodyParam.decTotal"></Input>-->
            <xdo-input v-model="bodyParam.decTotal" notConvertNumber decimal int-length="11" precision="5" :disabled="bomm"></xdo-input>
          </XdoFormItem>


          <XdoFormItem prop="exgVersion" label="单耗版本号">
            <xdo-input v-model="bodyParam.exgVersion" :maxlength="8"  :disabled="bomm"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="entryGNo" label="清单序号">
            <XdoIInput type="text" :maxlength="2" :disabled="bomm"  v-model="bodyParam.entryGNo" placeholder="敲击回车自动保存列表" @on-enter="intoBodyList" ></XdoIInput>
          </XdoFormItem>
        </XdoForm>
      </XdoCard>
    </XdoCard>
    <!--右边部分-->
    <XdoCard class="dataManageright" v-show="isShow">
      <XdoCard :bordered="false">
        <p>出货单编号:</p>
        <XdoIInput v-for='(item,index) in sear' :id="forId(index)" v-model="item.VALUE" :key="index" :disabled="bomm" number :maxlength="18" placeholder="敲击回车自动保存列表" @keyup.enter="intoBillList(index)"></XdoIInput>
<!--        <xdo-input v-model="searchParam.serialNo" number int-length="11"  ></xdo-input>-->
      </XdoCard>
    </XdoCard>
  </section>
</template>
<script>
  import { csAPI, excelExport } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  //  import {ArrayToLocaleLowerCase} from '@/libs/util'
  import { dynamicHeight, getColumnsByConfig,getExcelColumnsByConfig} from '@/common'
  import { columnsConfig, columns,excelColumnsConfig } from './deepProduceExpFromToAddListColumns'
  import { delList } from '../../cs-common'
  import { blobSaveFile } from '@/libs/util'

  export default {
    name: "deepProduceExpFromToHeadEdit",
    mixins: [dynamicHeight, columns],
    props:{
      typeNoInfor:{type: String, default: () => ({})},//传值List页面中的 typeNo
      searchDataInfor: {type: Object, default: () => ({})},
      textDataInfor:{type: Object, default: () => ({})},
//      compData:{type: Array, default: () => ({})},//list页面传过来的数据
    },
    data(){
      return{
        actions: [
          { type: 'text', disabled: false, click: this.delInto, icon: 'ios-add', label: '新增', key:'xdo-btn-add', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.delBySid, icon: 'ios-trash-outline', label: '删除', key:'xdo-btn-delete', loading: false, needed: true },
          // { type: 'text', disabled: false, click: this.closeAdd, icon: 'ios-cloud-upload-outline', label: '导入', key:'xdo-btn-upload', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.exportBody, icon: 'ios-cloud-download-outline', label: '导出', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.match, icon: 'ios-paper-plane-outline', label: '匹配备案序号', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.getBgxh, icon: 'ios-log-out', label: '生成清单序号', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.exportReceipt, icon: 'ios-albums', label: '导出发票清单', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.birthBaoGuan, icon: 'ios-basket-outline', label: '生成报关信息', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.getListByHeadId, icon: 'ios-paper-plane-outline', label: '查询', key:'xdo-btn-download', loading: false, needed: true },
          { type: 'text', disabled: false, click: this.cx, icon: 'ios-arrow-dropdown', label: '查询条件', key:'xdo-btn-download', loading: false, needed: true }
        ],
        isShow: false,
        isHeadBj:'',//用来表示表体编辑和修改 1：修改 2：新增
        isBj:'',//标记是否是编辑 1：标题编辑
//        this.addParams.headParam
        addBillParams:{
          receiveBillNo:'',//收货编号
          headId:'' //表头id
        },
        zcList:[],//转出备案料号
        zcListxh:[],//转出备案序号
        sear:[{id:'',value:''}],
        addParams:{
          headParam:{
            emsNo:'',//转入转出手帐册号
            tradeMode:'',//监管方式
            emsCopNoOut:'',//转出内部编号
            beginDate:'',// 结转日期开始
            endDate:'',//结转日期结束
            emsNoIn:'',//转入方备案号
            tradeCodeInName:'',//转入方名称
            tradeNameIn:'',//转入方名称
            customerCodeIn:'',//转入方代码
            tradeCodeIn:'',//转入方海关十位码
            emsNoOut:'',//转出方备案号
            applyNo:'',//申请表编号
            entryNoIn:'',//转入报关单号
            entryNoOut:'',//转出报关单号
            dataSource:'',//数据来源
            tradeCodeOut:'',//转出方海关十位代码
            status:'0',//状态
            remark:''//备注
          },
        },
        flag0:'',
        flag1:'',
        flag2:'',
        flag3:'',
        inDataSource:{
          status: delList.matchResult,
          jgfs: delList.jgfs
        },
        bodyMidSearchParam:{
          copGNoOut:'',//备案料号(转出)
          gNoIn:'',//备案序号(转入)
          facGNoOut:'',//企业料号
          copGNoIn:'',//备案料号
          gNoOut:'',//转出备案序号
          // gNoIn:'',//转入备案序号
          entryGNo:'',//报关序号
          matchResult:'0',//匹配结果
          headId:'' //表头id
        },
        listZrqtlh:[],//改数组用来存放转入企业料号
        listZcfbaxh:[],//改数组用来存转出备案序号
        bodyParam:{
          sid:'',//id
          exgVersion:'',//单耗版本号
          facGNoOut:'',//转入企业料号
          copGNoIn:'',//转入备案料号
          gNoIn:'',//转入备案序号
          unit:'',//申报计量单位
          curr:'',//币制
          gNoOut:'',//转出备案序号
          decPrice:'',//单价
          decQty:'',//数量
          decTotal:'',//总价
          entryGNo:'',//报关序号
          matchResult:'0',//匹配结果
          serialNo:'',//录入顺序或导入顺序
          copGNoOut:'',//转出备案料号
          headId:'' //表头id
        },

        headID:'',//表头的sid
        pageParam: {   //分页获取表体数据
          page: 1,
          limit: 20,
          dataTotal: -1
        },
        gridConfig: {
          data: [],
          selectRows: [],
          gridColumns: [],
          selectData: []
        },
        ruleValidate:{
          emsNo: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          entryNoIn: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          tradeMode: [{ required: true, message: '不能为空！', trigger: 'blur' }],
          emsCopNoOut: [{ required: true, message: '不能为空！', trigger: 'blur' }]
        },
        chaxun:true,
        chaxunEdit:true,
        listImp:[],//改数组用来存放转出方名称及代码
        listSzch:[],//用来存放转入转出手帐册号

        listZcfszch:[],//转出方手账册号
        listZrfszch:[],//转入方手账册号

        bodySearchParam:{  //标题查询条件
          copGNoOut:'', //转出备案料号
          gNoOut:'', //转出备案序号
          copGNoIn:'', //转入备案料号
          gNoIn:'', //转入备案序号
          facGNoOut:'', //转出备案料号
          headId:''//表头id
        },
        searchParam:{
          bondedFlag:'0',
          emsNo:'',
          gMark:'I'
        },
        searchTwo:{
          bondedFlag: "0",
          emsNo: "",
          facGNo: "",
          gMark: "I"
        },
        listSid:'',//表体sid
        bomm:true,
        bomm1:true,
        isxy:false,
        dataSource:false,
        del:false,
        fileName:[ ],
        check:false,
        buttonShow:true,
        actionType:0,
        sid:'',
        isStatus:false,
        isText:false,//是否可编辑
        isSelect:false,
        btnShow:true,
        addSuccesInfor:{},
      }
    },
    created() {
//      this.inToProp()  //加载转出方名称及代码
    },
    mounted(){
      this.isYingC() //默认隐藏
      this.getThreeParams()//获取转入转出手帐册号



      // this.getZcfSzchFromTo() //加载获取转入方手账册号
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.searchDataInforMethods(this.searchDataInfor)// 查看的方法，一面一加载就走js方法
      this.typeNoInforMethods(this.typeNoInfor) //编辑的方法，一面一加载就走js方法
    },
    watch:{
    },
    methods:{
      cx(){
        // console.log('查询条件')
        // console.log(this.isxy)
        this.isxy=!this.isxy;
        // console.log('查询条件')
        // console.log(this.isxy)
        if(this.isxy===true){
          this.isYing();
        }else{
          this.isYingC();
        }

      },
      isYingC(){
        document.getElementById('isYc').style.display = "none";
      },
      isYing(){
        document.getElementById('isYc').style.display = "";
      },
      //生成报关序号
      getBgxh(){
        let headId=this.headID //表头id
        this.$http.post(csAPI.deep.deepExgRecord.head.getBgxuh.bg+`/${headId}`).then( res => {
          if(res.data.code === 200){
            this.$Message.success('生成清单序号成功！')

          }
        },()=>{})
        this.getBodyList();//掉一下标题初始化方法
      },

      //匹配备案序号
      match(){
        let headId=this.headID //表头id
        this.$http.post(csAPI.deep.deepExgRecord.head.pipeiBaxh.pi+`/${headId}`).then( res => {
          console.log(res.data)
          if(res.data.data === false){
            this.$Message.success('匹配完成，部分数据未匹配成功！')

          }else if(res.data.success === true){
            this.$Message.success('匹配成功！')
          }
          // if(res.data.code === 200){
          //   this.$Message.success('匹配备案序号成功！')
          //   this.getBodyList();//掉一下标题初始化的方法
          // }
        },()=>{})
        this.getBodyList()  //调一下获取表体列表的方法
      },
      //导出发票清单
      exportReceipt(){
        let headId=this.headID //表头id
        let excelRes =  this.$http.get(csAPI.deep.deepExgRecord.head.exportReceipt.noReceipt+`/${headId}`,{responseType: 'blob'})
        return excelRes.then(res => {
          this.saveExcelFile(res, `深加工转出发票清单.xls`)
        },()=>{})
      },

      saveExcelFile(res, filename){
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'})
        blobSaveFile(blob, filename)
      },







      //生成报关信息
      birthBaoGuan(){
        let headId=this.headID //表头id
        this.$http.post(csAPI.deep.deepExgRecord.head.birthBaoGuan.birthBG+`/${headId}`).then( res => {
          // this.$http.post('http://**************:9999/gwstd/api/v1/recordHeadFromTo/generateDecErpI'+`/${headId}`).then( res => {
          if(res.data.code === 200){
            this.$Message.success('生成报关信息成功!')
            this.getBodyList();//调一下标题初始化方法
          }
        },()=>{})

      },
      //单纯关闭
      closeAdd(){
        this.chaxun = false
        this.bomm = true
        this.chaxunEdit = true
//        this.bomm=false
//        this.chaxun = true
        this.isText = false
        this.isStatus = false
        this.buttonShow=true;//吧删除附件的放开
        this.dataSource=false;//关闭的时候把数据来源的 按钮改为可控制
        this.del=true//控制数据来源的按钮不可改动
        this.searchData = ''//关闭的时候把数据源容器值删除
        this.headID=''
        this.isHeadBj='';
        this.$emit("onEditback",true)
//        this.getList();//获取表头加载所有的
      },
      //获取转出方手账册号
//       getZcfSzchFromTo(){
//         let type='0';
//         let tradeCode='aa';
//         this.$http.post(csAPI.deep.deepExgRecord.head.GETMD+`/${type}`+`/${tradeCode}`).then(res => {
//           let tmpArr = []
// //          console.log('0909')
// //          console.log(res.data.data)
//           for (let item of res.data.data) {
//             tmpArr.push({
//               value: item.VALUE,
//               label: item.VALUE
//             })
//           }
// //            console.log(tmpArr)
//           this.listZcfszch = tmpArr
//         }).catch(() => {
//           this.listZcfszch=''
//         })
//       },

      //手帐册号改变事件js
      getThreeParams(){
        // console.log(3333)
        //调用后台接口获取其他的参数并且赋值
        let type='8';
        let tradeCode='aa';//形式参数（可有可无）
        this.$http.post(csAPI.deep.deepExgRecord.head.GETMD+`/${type}`+`/${tradeCode}`).then(res => {
          let tmpArr = []
         // console.log('0909')
         // console.log(res.data.data)
          for (let item of res.data.data) {
            tmpArr.push({
              value: item.EMS_NO_OUT+'-----'+item.EMS_NO_IN,
              label: item.EMS_NO_OUT+'-----'+item.EMS_NO_IN
            })
          }
          this.listSzch = tmpArr
        }).catch(() => {
          this.listSzch=''
        })

      },
      //转入转出手帐册号change事件
      getThreeParamsChange(e){
        if(e!==null&&e!==''&&e!==undefined){
          // console.log('22222')
          // console.log(this.addParams.headParam.emsNo)
          let type='1';
          let tradeCode=this.addParams.headParam.emsNo;//手账册号
          this.$http.post(csAPI.deep.deepExgRecord.head.GETMD+`/${type}`+`/${tradeCode}`).then(res => {
            this.addParams.headParam.tradeCodeInName=res.data.data[0].TRADECODEOUT
            // this.getZrfSzch();//调一下获取转入方手账册号
          }).catch(() => {
            this.addParams.headParam.tradeCodeInName=''
          })
        }else{
          this.addParams.headParam.tradeCodeInName=''
        }
      },



      //获取转入方名称及代码
      getZcfMjd(){
        if(isNullOrEmpty(this.addParams.headParam.emsNoIn)){
          return;
        }else{
          let type='1';
          let tradeCode=this.addParams.headParam.emsNoIn;//转出方手账册号
          this.$http.post(csAPI.deep.deepExgRecord.head.GETMD+`/${type}`+`/${tradeCode}`).then(res => {
            this.addParams.headParam.tradeCodeInName=res.data.data[0].TRADECODEOUT
            // this.getZrfSzch();//调一下获取转入方手账册号
          }).catch(() => {
            this.addParams.headParam.tradeCodeInName=''
          })
        }
      },
      //获取转入方手账册号
      getZrfSzch(){
        let type='2';
        let tradeCode=this.addParams.headParam.emsNoOut;//转出方手账册号
        this.$http.post(csAPI.deep.deepExgRecord.head.GETMD+`/${type}`+`/${tradeCode}`).then(res => {
          let tmpArr = []
          for (let item of res.data.data) {
            tmpArr.push({
              value: item.EMS_NO_OUT,
              label: item.EMS_NO_OUT
            })
          }
          this.listZrfszch = tmpArr
        }).catch(() => {
          this.listZrfszch=''
        })
      },
      //表头保存
      saveHeader(){
        //先判断转出方、转入方手账册号是否为空（必填）
        if(isNullOrEmpty(this.addParams.headParam.emsCopNoOut)){
          this.$Message.warning('转出内部编号必填！');
        }else if(isNullOrEmpty(this.addParams.headParam.entryNoIn)){
          this.$Message.warning('转入报关单号必填！');
        }else if(isNullOrEmpty(this.addParams.headParam.tradeMode)){
          this.$Message.warning('监管方式必填！');
        }else{
          // console.log(this.isHeadBj)
          this.addParams.headParam.customerCodeIn=this.addParams.headParam.tradeCodeInName.substring(0,6)
          this.addParams.headParam.tradeNameIn=this.addParams.headParam.tradeCodeInName.substring(16,25)
          this.addParams.headParam.dataSource=1
          this.addParams.headParam.tradeCodeOut=this.$store.state.user.company
          this.addParams.headParam.tradeCodeIn=this.searchDataInfor.tradeCodeIn
          this.addParams.headParam.emsNoOut=this.addParams.headParam.emsNo.substring(0,12)
          this.addParams.headParam.emsNoIn=this.addParams.headParam.emsNo.substring(17,29)
          if(this.isHeadBj===1){
            //表头修改 this.isHeadBj='';
            console.log('走表头编辑')
            // console.log(this.addParams.headParam)
            this.$http.put(csAPI.deep.deepExgRecord.head.updateHead+`/${this.headID}`,this.addParams.headParam).then( res => {
              console.log(res.data.success)
              if(res.data.success === true){
//            console.log(res.data.data)
                this.$Message.success('编辑成功!')
                this.isHeadBj='';
              }
            },()=>{}).catch(() => {
              this.$Message.success('当前表头有标题数据无法修改！')
            })

          }else{

            //新增表头
            let tradeCodeOut=this.addParams.headParam.emsCopNoOut; //转出内部编号
            let entryNoIn=this.addParams.headParam.entryNoIn;//转入报关单号
            let entryNoOut=this.addParams.headParam.entryNoOut;//转出报关单号
            // this.$http.post(csAPI.deep.deepExgRecord.head.getReByNblj.oneInfo+`/${tradeCodeOut}`+`/${entryNoIn}`+`/${entryNoOut}`).then( res => {
            this.$http.post(csAPI.deep.deepExgRecord.head.getReByNblj.oneInfo
              + '?tradeCodeOut=' + `${tradeCodeOut}`
              + '&entryNoIn=' + `${entryNoIn}`
              + '&entryNoOut' + `${entryNoOut}`, {
                // tradeCodeOut: this.addParams.headParam.emsCopNoIn,     // 转入内部编号
                // entryNoIn: this.addParams.headParam.entryNoIn,              // 转入报关单号
                // entryNoOut: this.addParams.headParam.entryNoOut             // 转出报关单号
            }).then( res => {
              if(res.data.code === 200){
                //校验通过
                this.getaZcfhgswm();
              }else{
                //说明校验未通过
                this.$Message.warning(res.data.data[0]);
              }
            },()=>{})
          }

        }
      },
      //获取转入方海关十位码
      getaZcfhgswm(){
//        console.log('转出方海关')
        let emsNoIn=this.addParams.headParam.emsNoIn  //转入方手（账）册号
        let emsNoOut=this.addParams.headParam.emsNoOut  //转出方手（账）册号
        console.log('6969')
        console.log(this.addParams.headParam)
        console.log(emsNoIn)
        console.log(emsNoOut)

        this.$http.post(csAPI.deep.deepExgRecord.head.getZcfhgswm.getzchg+ '?emsNoIn=' + `${emsNoIn}`
          + '&emsNoOut=' + `${emsNoOut}`, {
          // tradeCodeOut: this.addParams.headParam.emsCopNoIn,     // 转入内部编号
          // entryNoIn: this.addParams.headParam.entryNoIn,              // 转入报关单号
          // entryNoOut: this.addParams.headParam.entryNoOut             // 转出报关单号
        }).then( res => {
         // console.log(res.data.code)
          if(res.data.code === 200){
//            console.log('得到转出方海关十位吗')
//            console.log(res.data.data)
            this.addParams.headParam.tradeCodeIn=res.data.data //给转出方海关十位码赋值
//            console.log(this.addParams.headParam.tradeCodeOut)
            this.realInto();//走真正的插入方法
          }
        },()=>{})


      },
      //走真正的插入方法
      realInto(){
        // console.log('中国')
        this.addParams.headParam.customerCodeIn=this.addParams.headParam.tradeCodeInName.substring(1,4)
        this.addParams.headParam.tradeNameIn=this.addParams.headParam.tradeCodeInName.substring(13,25)
        this.addParams.headParam.dataSource=1
        this.addParams.headParam.tradeCodeIn=this.$store.state.user.company
        // this.addParams.headParam.emsNoIn=this.addParams.headParam.emsNo.substring(0,12)
        // this.addParams.headParam.emsNoOut=this.addParams.headParam.emsNo.substring(17,29)
        console.log('11')
        console.log(this.addParams.headParam)
        console.log('11')

        this.$http.post(csAPI.deep.deepExgRecord.head.fromToAdd.intoInfo,this.addParams.headParam).then( res => {
          if(res.data.code === 200){
            this.$Message.success('保存成功！')
//            console.log(res.data.data)
            this.isShow = true
            document.getElementsByClassName('dataManageleft')[0].style.width = '85%'
            document.getElementsByClassName('dataManageright')[0].style.width = '15%'
            this.headID=res.data.data.sid  //把表头的SID赋值给表体的headId
            //表头保存成功后把表退下面所有的置灰按钮全部放开
            this.chaxun=false;
            this.bomm=false;
            this.flag0=''
            this.flag1=''
            this.flag2=''
            this.flag3=''
            this.getZrqylh();//调一下获取转出企业料号的数据（给下拉赋值）
//            this.getBillList();//调一下获取收货单号在当前表头下面的所有的值

          }
        },()=>{})
      },
      //调用转出企业料号的下拉框接口
      getZrqylh(){
         console.log('走了获取转出起业料号')
        // let emsNoIn=this.addParams.headParam.emsNoIn  //转入方海关十位代码
        let emsNoOut=this.addParams.headParam.emsNoOut  //转入方海关十位代码
//        console.log(emsNoIn)
        this.$http.post(csAPI.deep.deepExgRecord.head.selzr.getZrqyl+`/${emsNoOut}`).then(res => {
           // console.log(res.data.data)
          res.data.data.forEach(item =>{
            this.listZrqtlh.push(item.FAC_G_NO_OUT)
          })

        })

      },
      //转入企业料号下拉改变事件  this.bodyParam.facGNoIn===''
      inToOther(){
        // console.log('其他副职')
        if(!isNullOrEmpty(this.bodyParam.facGNoOut)){
//            console.log('走了其他三个赋值')

          let qylh= this.bodyParam.facGNoOut  //获取到下拉框来面的企业料号
          let zrfbah= this.addParams.headParam.emsNoOut  //获取到转出方备案号
          this.$http.post(csAPI.deep.deepExgRecord.head.getOtherFromTo.getRe+`/${qylh}`+`/${zrfbah}`).then(res => {
            if(res.data.code === 200){
              this.bodyParam.gNoOut=res.data.data.G_NO_OUT  //转入备案料号
              this.bodyParam.copGNoOut=res.data.data.COP_G_NO_OUT   //转入备案序号
              this.zcList=res.data.data.ZCBALH    //转出备案料号
              this.zcListxh=res.data.data.ZCBAXH  //转出备案序号
              this.bodyParam.unit=res.data.data.UNIT    //申报计量单位
            }
          })
        }else{
          this.bodyParam.gNoOut=''  //转入备案料号
          this.bodyParam.copGNoOut=''   //转入备案序号
          this.bodyParam.unit=''    //申报计量单位
        }
      },
      //根据当前表头id、查询参数 获取下面所有的列表结果值
      getListByHeadId(){
        let pageParam = {
          page:this.pageParam.page,
          limit:this.pageParam.limit
        }
        this.bodyMidSearchParam.headId=this.headID //把表头sid赋值给表体headId

        const data = this.bodyMidSearchParam  //此为查询框里面的查询条件值
        this.$http.post(csAPI.deep.deepExgRecord.head.getFromToBodyList.selectDelAllPaged, data, { params: pageParam }).then(res => {
          // console.log(res.data.data)
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
        })
      },
      //点击删除的时候走的方法
      delBySid(){
//          console.log('走了删除的aa')
        if (this.gridConfig.selectRows.length > 0) {
          this.$Modal.confirm({
            title: '提醒',
            content: '确认删除所选项吗',
            okText: '删除',
            cancelText: '取消',
            onOk: () => {
              const sids = this.gridConfig.selectRows.map(item => {
                return item.sid
              })
              this.$http.delete(csAPI.deep.deepExgRecord.head.deleteFromToList + `/${sids}`).then(() => {
                this.$Message.success('删除成功！')
                this.gridConfig.selectRows = []
                this.delInto()
                this.getBodyList()  //调一下获取表体列表的方法
              }).catch(() => {
              }).finally(() => {
              })
            },
            onCancel: () => {
            }
          })
        } else {
          this.$Message.warning('未选择数据, 请选择对应的数据进行操作!')
        }

      },
      //点击表体列表中的编辑按钮走的js方法
      handleRowDblClick(val){
        console.log(val)
        let me = this
        me.listZrqtlh=[]
        this.getZrqylh();//调一下获取表体的转入企业料号的Js方法
        this.getBillList();//调一下获取收货单号在当前表头下面的所有的值
        this.isBj=1;//表示编辑
        if(this.typeNoInfor===2){
          this.$Message.warning('查看不可编辑表体数据');
        }else{
          this.bodyParam.sid=val.sid
          this.bodyParam.matchResult=val.matchResult
          this.bodyParam.facGNoOut=val.facGNoOut
          this.bodyParam.copGNoIn=val.copGNoIn
          me.inToOther()
          this.$nextTick(function(){
              me.bodyParam.copGNoOut=val.copGNoOut
              me.bodyParam.gNoOut=val.gNoOut
          })
          this.bodyParam.unit=val.unit
          this.bodyParam.decQty=val.decQty
          this.bodyParam.decPrice=val.decPrice
          this.bodyParam.decTotal=val.decTotal
          this.bodyParam.curr=val.curr
          this.bodyParam.gNoIn=val.gNoIn
          this.bodyParam.entryGNo=val.entryGNo
          this.bodyParam.exgVersion=val.exgVersion
        }
      },
      //点击表体数据导出
      exportBody(){
        let bodList={
          matchResult:'',//匹配结果
          facGNoOut:'',//企业料号
          copGNoIn:'',//备案料号
          unit:'',//申报计量单位
          decQty:'',//数量
          decPrice:'',//单价
          decTotal:'',//总价
          curr:'',//币制
          gNoOut:'',//转出方备案序号
          gNoIn:'',//转入方备案序号
          entryGNo:'',//报关序号
          headId:this.headID //把表头sid赋值给表体headId
        }

        const params = {
          exportColumns: Object.assign({}, bodList),
          name: '深加工转出表体数据',
          header: getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
        }
        excelExport(csAPI.deep.deepExgRecord.head.exportFromToBodyList, params).finally(() => {
        })



      },
      //敲击回车的时候走的保存方法
      intoBodyList(){

         // console.log(this.isBj)
//          console.log(this.headID)
        this.bodyParam.headId=this.headID //把表头id赋值 进去入参
//        console.log(this.bodyParam.headId)
        //先判断转出方、转入方手账册号是否为空（必填）
        if(this.bodyParam.curr===''){
          this.$Message.warning('币制必填！');
        }else if(this.bodyParam.decPrice===''){
          this.$Message.warning('单价必填！');
        }else if(this.bodyParam.decQty===''){
          this.$Message.warning('数量必填！');
        }else if(this.bodyParam.decTotal===''){
          this.$Message.warning('总价必填！');
        }else if(this.bodyParam.entryGNo>51){
          this.$Message.warning('报关序号最大值不能超过50！');
        }else{
          //验证参数
          let headId=this.headID; //表头的sid
          let gNoIn=this.bodyParam.gNoIn;//转入备案序号
          let gNoOut=this.bodyParam.gNoOut;//转出备案序号
          let entryGNo=this.bodyParam.entryGNo;//报关序号
          this.$http.post(csAPI.deep.deepExgRecord.head.yanzFromToList.yanz+`/${headId}`+`/${gNoIn}`+`/${gNoOut}`+`/${entryGNo}`).then( res => {
//            console.log(res.data.data)
            if(res.data.data === 1){
              this.$Message.warning('填写的报关序号不符合归并原则！');
            }else{
              this.realIntoList();//调一下查询数据库中最大的序号

            }
          },()=>{})
        }
      },
      //表体编辑
      updateList(){
       // console.log('走表体编辑')

        if(this.bodyParam.matchResult==='未匹配'){
          this.bodyParam.matchResult=0
        }else{
          this.bodyParam.matchResult=1
        }

        console.log(this.bodyParam)
        this.$http.put(csAPI.deep.deepExgRecord.head.updateList+`/${this.bodyParam.sid}`,this.bodyParam).then( res => {
          if(res.data.code === 200){
//            console.log(res.data.data)
           this.$Message.success('编辑成功!')
            this.delInto()
            this.isBj='';
            this.getBodyList();
          }
        },()=>{}).catch(() => {
          // this.$Message.success('')
        })
      },

      //查询数据库中最大的序号
      realIntoList(){
        // console.log(this.isBj)
//        console.log('查询最大的序号')
        this.$http.post(csAPI.deep.deepExgRecord.head.selMaxFromTo+`/${this.headID}`).then( res => {
          if(res.data.code === 200){
            this.bodyParam.serialNo=res.data.data
            if(this.isBj===1){
              //表体编辑
              this.updateList();//调一下表体编辑的接口
            }else{
              //表体保存
              this.realBiaoti();//最后真的表体保存
            }
          }
        },()=>{}).catch(() => {
          // this.$Message.success('')
        })
      },
      //表体保存aa
      realBiaoti(){
        console.log(this.bodyParam)
        this.bodyParam.matchResult=0
        this.$http.post(csAPI.deep.deepExgRecord.head.intoFromToBodyList,this.bodyParam).then( res => {
          if(res.data.code === 200){
//            console.log(res.data.data)
            this.$Message.success('保存成功！')
            this.listSid=res.data.data.sid
            this.getBodyList();//调一下根据当前表头id获取下面所有的列表结果值
            this.delInto();//调一下新增清除所有的框的值
          }
        },()=>{}).catch(() => {
          // this.$Message.success('')
        })

      },
      //点击新增的时候把最下面的输入框里的值清空
      delInto(){
        this.isBj=''
        this.bodyParam.sid=''
        this.bodyParam.facGNoOut=''
        this.bodyParam.copGNoIn=''
        this.bodyParam.copGNoOut=''
        this.bodyParam.gNoIn=''
        this.bodyParam.unit=''
        this.bodyParam.curr=''
        this.bodyParam.gNoOut=''
        this.bodyParam.decPrice=''
        this.bodyParam.decQty=''
        this.bodyParam.decTotal=''
        this.bodyParam.entryGNo=''
        this.bodyParam.matchResult=''
        this.bodyParam.exgVersion=''
      },
      //点击列表页面编辑的时候获取当前表头id下面的表体所有数据
      getBodyList(){
        //获取表体数据  getBodyList
        let pageParam = {
          page:this.pageParam.page,
          limit:this.pageParam.limit
        }
        this.bodySearchParam.headId=this.headID //把表头sid赋值给表体headId
        const data = this.bodySearchParam  //此为查询框里面的查询条件值

        console.log('cccc')
        console.log(this.bodySearchParam)


        this.$http.post(csAPI.deep.deepExgRecord.head.getFromTBodyList.selectFromToBodyList, data, { params: pageParam }).then(res => {
          console.log('wh')
          console.log(res.data.data)

          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total

        })

      },
      //0：新增  1 编辑   2  查看
      typeNoInforMethods(newval){
        if(newval === 0){
          //新增
          document.getElementsByClassName('dataManageleft')[0].style.width = '100%'
          this.isHeadBj = 2
          this.chaxun = false
          this.bomm = true
          this.bomm1 = false
          this.chaxunEdit = false
        }else if(newval === 1){
          //编辑
//              console.log('编辑')
//              console.log(this.bomm)
          this.isShow = true
          document.getElementsByClassName('dataManageleft')[0].style.width = '85%'
          document.getElementsByClassName('dataManageright')[0].style.width = '15%'
          this.isHeadBj = 1
          this.chaxun = false
          this.bomm = false
          this.bomm1 = false
          this.getBodyList() //调一下获取当前headid下面所有的表体列表
          this.getBillList() //调一下获取当前headid下面所有的收货单号列表
          this.getZrqylh() //调一下获取转入企业料号
        }else if(newval === 2){
//          console.log(this.bomm)
//            console.log(2222)
          //查看
          this.isShow = true
          document.getElementsByClassName('dataManageleft')[0].style.width = '85%'
          document.getElementsByClassName('dataManageright')[0].style.width = '15%'
          this.chaxun = true
          this.bomm = true
          this.bomm1 = true
          this.getBodyList() //调一下获取当前headid下面所有的表体列表
          this.getBillList() //调一下获取当前headid下面所有的收货单号列表
        }
      },

      //敲击收货号回车走的方法
      intoBillList(e){

//        console.log('收货单')
//        console.log(this.sear[e])
        if(!isNullOrEmpty(this.sear[e].ID)) {
          if(!isNullOrEmpty(this.sear[e].VALUE)){
//            console.log('走了修改的方法')
            //修改收货单号方法
            let sid = this.sear[e].ID;//收货单编号
            let receiveBillNo = this.sear[e].VALUE;//收货单编号
            let headId = this.headID;//收货单编号
//            console.log(sid)
//            console.log(receiveBillNo)
            this.$http.post(csAPI.deep.deepExgRecord.head.updBill.updBi + `/${sid}`+ `/${receiveBillNo}`+ `/${headId}`).then(res => {
//              console.log(res.data.data)
              if (res.data.code === 200) {
                this.$Message.success('操作成功');
                this.getBillList();
              } else {
                this.$Message.warning(res.data.message);
              }
            }, () => {
            })
          }else{
//            console.log('走了删除的方法')
            //在判断value为空值，说明是删除收货单号方法
            let sid = this.sear[e].ID;//收货单编号
//            console.log(sid)
            this.$http.post(csAPI.deep.deepExgRecord.head.delBill.delBi + `/${sid}`).then(res => {
//              console.log(res.data.data)
              if (res.data.code === 200) {
                this.$Message.success('操作成功');
                this.getBillList();
              } else {
                this.$Message.warning(res.data.message);
              }
            }, () => {
            })


          }
        }else{
//          console.log('走了新增的方法')
//          console.log(e)
          //sid 为空说明是新增的   新增收货单号
          let headId = this.headID; //表头的sid
//          console.log(this.sear[e].value)
//          console.log('走了新增的方法')

          let receiveBillNo = this.sear[e].VALUE;//收货单编号
          this.$http.post(csAPI.deep.deepExgRecord.head.inToBill.Bill + `/${headId}` + `/${receiveBillNo}`).then(res => {
//            console.log(res.data.data)
            if (res.data.code === 200) {
              this.sear = res.data.data;
//              console.log('结果')
//              console.log(this.sear)
//              console.log('结果')
              this.getBillList();
            } else {
              this.$Message.warning(res.data.message);
            }
          }, () => {
          })
        }
      },
      //加载收货单号所有的数据的方法
      getBillList(){
        let me = this
//        console.log('走了加载收货单号的方法')
        //在判断value为空值，说明是删除收货单号方法
        let headId = this.headID; //表头的sid
        this.$http.post(csAPI.deep.deepExgRecord.head.getBillList.getBill + `/${headId}`).then(res => {
         console.log(res.data.data)
         console.log(res.data.data.length)
          if (res.data.code === 200) {
//            this.$Message.successMsg('操作成功');
            this.sear=res.data.data
            this.sear.push({
              label:'',
              value:''
            })
            //this.sear[this.sear.length-1].focus()
            //console.log(this.sear[this.sear.length-1])
            //console.log(this.sear.length-1)
            this.$nextTick(() => {
              // console.log(document.getElementById('forid_5'))
              console.log(document.getElementById('forid_5'))
              document.getElementById(`forid_${me.sear.length-1}`).focus();

            })

            //document.getElementById('ceshi').focus();
//            console.log('打印sear')
//            console.log(this.sear)
//            this.$Message.success('新增成功！')
          } else {
            this.$Message.warning(res.data.message);
          }
        }, () => {
        })
      },

      forId(index){
        let res = "forid_" +index
        return res

      },


      //把list页面传过来的值接受并且赋值给表头各个框
      searchDataInforMethods(val){
        console.log(val)
        this.addParams.headParam.emsNo=val.emsNoOut+'-----'+val.emsNoIn;//手帐册号
        this.headID=val.sid;
        this.addParams.headParam.emsCopNoOut=val.emsCopNoOut;//转入内部编号
        this.addParams.headParam.beginDate=val.beginDate;//开始时间
        this.addParams.headParam.endDate=val.endDate;//结束时间
        this.addParams.headParam.emsNoOut=val.emsNoOut;//转出方备案号
        this.addParams.headParam.tradeCodeInName=val.tradeCodeInName;//转出方名称及代码
        this.addParams.headParam.emsNoIn=val.emsNoIn;//转入方备案号
        this.addParams.headParam.applyNo=val.applyNo;//申请表编号
        this.addParams.headParam.entryNoIn=val.entryNoIn;//转入报关单号
        this.addParams.headParam.entryNoOut=val.entryNoOut;//转出报关单号
        this.addParams.headParam.remark=val.remark;//备注
        this.addParams.headParam.tradeMode=val.tradeMode;//监管方式
        console.log(this.addParams.headParam)
      },

      //获取表头加载所有的
      getList() {
        let pageParam={
          page:this.pageParam.page,
          limit:this.pageParam.limit
        }
        let par={
          emsCopNoOut, //转入内部编号
          beginDate,//开始时间
          endDate,//结束时间
          emsNoOut,//转出方备案号
          tradeCodeInName,//转出方名称及代码
          emsNoIn,//转入方备案号
          applyNo,//申请表编号
          entryNoIn,//转入报关单号
          entryNoOut,//转出报关单号
          remark  //备注
        }
        const data = par
//          console.log(data)
        this.$http.post(csAPI.deep.deepExgRecord.head.getHeadList.selectHeadList, data, { params: pageParam }).then(res => {
          this.gridConfig.data = res.data.data
          this.pageParam.page = res.data.pageIndex
          this.pageParam.dataTotal = res.data.total
        })
      },

      pageChange(page) {
        this.pageParam.page = page
        this.getBodyList()
      },
      pageSizeChange(pageSize) {
        this.pageParam.limit = pageSize
        this.getBodyList()
      },
      //点击表体查询的时候走的方法
      handleSearchSubmit() {
//        console.log('走了请求后台的方法了')
        this.pageParam.page = 1
        this.getBodyList() //走一下后台的初始化方法
      },
      //选中赋值
      handleSelectionChange(selectRows) {
        this.gridConfig.selectRows = selectRows
      }
    },
  }
</script>

<style lang="less" scoped>
  section{
    display: flex;
  }
  section div{
    //flex: 1;
  }
  ul li{
    display: inline-block;
    list-style: none;
    margin-left: 15px;
    color: #999;
  }

</style>
