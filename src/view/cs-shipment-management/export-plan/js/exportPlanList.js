import exportPlanTabs from '../export-plan-tabs'
import { blobSaveFile, getHttpHeaderFileName } from '@/libs/util'
import extractPop from '../../components/export-plan/extract-pop'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const exportPlanList = {
  name: 'exportPlanList',
  components: {
    extractPop,
    exportPlanTabs
  },
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      extractPop: {
        show: false
      },
      downloading: false,
      cmbSource: {
        status: [{
          value: '1', label: '暂存'
        }, {
          value: '2', label: '提单已匹配'
        }, {
          value: '3', label: '已比对'
        }, {
          value: '4', label: '邮件已发送'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'extract': this.handleExtract,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup,
        'batch-extract': this.handleBatchExtract,
        'generate-customs-info': this.handleGenerateCustomsInfo
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        title: '出货ID',
        key: 'linkedNo'
      }, {
        key: 'invoiceNo',
        title: '发票号码'
      }, {
        title: '客户代码',
        key: 'clientCode'
      }, {
        title: '客户名称',
        key: 'clientName'
      }, {
        title: '制单员',
        key: 'userName'
      }, {
        range: true,
        title: 'ERP创建时间',
        key: 'lastUpdateTime'
      }, {
        range: true,
        title: '制单日期',
        key: 'insertTime'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '出货ID',
        key: 'linkedNo'
      }, {
        width: 120,
        key: 'status',
        title: '状态',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        key: 'invoiceNo',
        title: '发票号码'
      }, {
        width: 120,
        title: '出货编号',
        key: 'shipmentNo'
      }, {
        width: 120,
        title: '客户代码',
        key: 'clientCode'
      }, {
        width: 120,
        title: '客户名称',
        key: 'clientName'
      }, {
        width: 120,
        title: '出货日期',
        key: 'shipmentDate',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '收货地址',
        key: 'shippingAddress'
      }, {
        width: 120,
        key: 'marks',
        title: '标记唛头'
      }, {
        width: 120,
        title: '交货单号',
        key: 'deliveryOrderNo'
      }, {
        width: 120,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 120,
        title: '付款方式',
        key: 'paymentMode'
      }, {
        width: 120,
        key: 'shipTo',
        title: 'SHIP TO'
      }, {
        width: 120,
        title: '收货人',
        key: 'receivingParty'
      }, {
        width: 120,
        title: '通知人',
        key: 'notifyParty'
      }, {
        width: 120,
        title: '启运港',
        key: 'despPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.port_lin)
        })
      }, {
        width: 120,
        title: '目的港',
        key: 'destPort',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.port_lin)
        })
      }, {
        width: 120,
        key: 'packingQty',
        title: '外包装数量'
      }, {
        width: 120,
        key: 'copGName',
        title: '主要品名'
      }, {
        width: 136,
        title: '总毛重',
        key: 'grossWtTotal'
      }, {
        width: 136,
        title: '总体积',
        key: 'volumeTotal'
      }, {
        width: 136,
        key: 'remark1',
        title: '备注1',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 136,
        key: 'remark2',
        title: '备注2',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '制单员',
        key: 'userName'
      }, {
        width: 120,
        title: 'ERP创建时间',
        key: 'lastUpdateTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 提取
     */
    handleExtract() {
      let me = this
      me.$set(me.extractPop, 'show', true)
    },
    /**
     * 执行提取
     * @param linkedNo
     */
    doExtract(linkedNo) {
      let me = this
      me.$http.post(me.ajaxUrl.extractUrl + '/' + linkedNo).then(res => {
        me.$Message.success(res.data.message)
        me.$set(me.extractPop, 'show', false)
        me.handleSearchSubmit()
      }).catch(() => {
      })
    },
    /**
     * 批量提取
     */
    handleBatchExtract() {
      let me = this
      me.$http.post(me.ajaxUrl.batchExtractUrl).then(res => {
        me.$Message.success(res.data.message)
        me.handleSearchSubmit()
      }).catch(() => {
      })
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 生成报关资料
     * @param type
     */
    handleGenerateCustomsInfo(type) {
      let me = this,
        contentType = ''
      if (me.checkRowSelected('生成报关资料', true)) {
        if (type === 'pdf') {
          contentType = 'application/pdf'
        } else if (type === 'excel') {
          contentType = 'application/vnd.ms-excel'
        } else {
          return
        }
        let sid = me.listConfig.selectRows[0].sid
        me.$http.post(me.ajaxUrl.checkData + '/' + sid).then(() => {
          me.$set(me, 'downloading', true)
          me.$http.post(me.ajaxUrl.generateCustomsInfoUrl + '/' + sid + '/' + type, {}, {
            responseType: 'blob'
          }).then(res => {
            let headName = getHttpHeaderFileName(res.headers),
              blob = new Blob([res.data], {type: contentType})
            blobSaveFile(blob, headName ? headName : '生成报关资料')
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'downloading', false)
          })
        }).catch(() => {
        })
      }
    }
  }
}
