<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:generate-customs-info>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 120px;" :loading="downloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>生成报关资料 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="handleGenerateCustomsInfo('pdf')">
                    生成PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="handleGenerateCustomsInfo('excel')">
                    生成Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <exportPlanTabs v-if="!showList" :edit-config="editConfig" @onEditBack="editBack"></exportPlanTabs>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <extractPop :show.sync="extractPop.show" @doExtract="doExtract"></extractPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { exportPlanList } from './js/exportPlanList'

  export default {
    name: 'exportPlanList',
    mixins: [exportPlanList],
    data() {
      return {
        listConfig: {
          exportTitle: '出口计划表头'
        },
        ajaxUrl: {
          deleteUrl: csAPI.shipmentManagement.exportPlan.head.delete,
          extractUrl: csAPI.shipmentManagement.exportPlan.head.extract,
          exportUrl: csAPI.shipmentManagement.exportPlan.head.exportUrl,
          checkData: csAPI.shipmentManagement.exportPlan.head.checkData,
          batchExtractUrl: csAPI.shipmentManagement.exportPlan.head.batchExtract,
          selectAllPaged: csAPI.shipmentManagement.exportPlan.head.selectAllPaged,
          generateCustomsInfoUrl: csAPI.shipmentManagement.exportPlan.head.generateCustomsInfo      // {sid}/{type}
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
