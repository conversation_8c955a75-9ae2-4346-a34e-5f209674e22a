<template>
  <XdoModal width="600" mask v-model="show" title="提取"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <DynamicForm ref="frmData" labelWidth="100" class="dc-form-1"
                 :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
    </DynamicForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'extractPop',
    mixins: [baseDetailConfig],
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        buttons: [
          {...btnComm, label: '提取', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '取消', type: 'warning', command: 'cancel', click: this.handleClose}
        ]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.$set(me.detailConfig.model, 'linkedNo', '')
          } else {
            if (me.$refs['frmData']) {
              if (typeof me.$refs['frmData']['resetFields'] === 'function') {
                me.$refs['frmData']['resetFields'].call()
              }
            }
          }
        }
      }
    },
    methods: {
      getFields() {
        return [{
          required: true,
          title: '出货ID',
          key: 'linkedNo',
          props: {
            maxLength: '50'
          }
        }]
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleSave() {
        let me = this
        me.$refs[me.formName].validate().then(isValid => {
          if (isValid) {
            me.$emit('doExtract', me.detailConfig.model['linkedNo'])
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-form-1 {
    width: 100%;
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(1, 1fr);
  }

  .dc-form-1 > div {
    grid-column: 1;
  }
</style>
