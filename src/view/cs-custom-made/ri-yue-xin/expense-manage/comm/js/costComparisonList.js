import { csAPI } from '@/api'
import { expenseManage } from '@/view/cs-common'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const costComparisonList = {
  name: 'costComparisonList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'delete': this.handleDelete,
        'compare': this.handleCompare,
        'exam-send': this.handleExamSend,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        remitteeCode: [],
        differenceCompare: [{
          value: '0', label: '等于0'
        }, {
          value: 'N', label: '不等于0'
        }],
        // 审核状态
        auditStatus: expenseManage.AUDIT_STATUS_MAP,
        // 收款单位类型
        remitteeType: expenseManage.REMITTEE_TYPE_MAP,
        // 比对状态
        compareStatus: expenseManage.COMPARE_STATUS_MAP
      }
    }
  },
  /**
   * 创建
   */
  created: function () {
    let me = this
    // 收款单位
    me.$http.post(csAPI.ieParams.selectComboxByCode + '/PRD,FOD,CUT').then(res => {
      me.$set(me.cmbSource, 'remitteeCode', ArrayToLocaleLowerCase(res.data.data))
    }).catch(() => {
      me.$set(me.cmbSource, 'remitteeCode', [])
    }).finally(() => {
      me.searchFieldsReLoad('remitteeCode')
    })
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      // let me = this
      // me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 比对
     */
    handleCompare() {
      let me = this
      if (me.checkRowSelected('比对')) {
        me.setToolbarLoading('compare', true)
        me.$http.post(me.ajaxUrl.compareUrl, me.listConfig.selectRows).then(res => {
          me.handleSearchSubmit()
          me.$Message.success(res.data.message)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('compare')
        })
      }
    },
    /**
     * 发送内审
     */
    handleExamSend() {
      let me = this
      if (me.checkRowSelected('发送内审')) {
        me.setToolbarLoading('exam-send', true)
        me.$http.post(me.ajaxUrl.sendExamUrl + '/' + me.getSelectedParams()).then(() => {
          me.handleSearchSubmit()
          me.$Message.success('发送内审成功!')
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('exam-send')
        })
      }
    }
  }
}
