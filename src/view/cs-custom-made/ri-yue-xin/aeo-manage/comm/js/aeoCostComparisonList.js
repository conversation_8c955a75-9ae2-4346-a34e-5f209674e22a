import { csAPI } from '@/api'
import { addEvent } from '@/libs/util'
import { expenseManage } from '@/view/cs-common'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

function OpCellRenderer() {
}

OpCellRenderer.prototype.init = function(params) {
  let vm = undefined,
    editDisplay = '',
    vmParent = undefined,
    divContainer = document.createElement('div'),
    fwComWrapper = params['frameworkComponentWrapper']
  if (fwComWrapper) {
    vmParent = fwComWrapper.parent
  }
  while (vmParent.$parent && typeof vmParent.$parent['cellEditStyle'] !== 'function') {
    vmParent = vmParent.$parent
  }
  if (vmParent.$parent) {
    vm = vmParent.$parent
  }
  if (vm && typeof vm.cellEditStyle === 'function') {
    editDisplay = vm.cellEditStyle()
  }

  let editA = document.createElement('a')
  editA.innerHTML = '进入审核'
  editA.setAttribute('type', 'primary')
  editA.style.marginLeft = '6px'
  editA.style.display = editDisplay
  addEvent(editA, 'click', function () {
    if (vm && typeof vm.showEditByRow === 'function') {
      vm.showEditByRow(params.data)
    }
  })
  divContainer.appendChild(editA)

  this.eGui = divContainer
}

OpCellRenderer.prototype.getGui = function() {
  return this.eGui
}

export const aeoCostComparisonList = {
  name: 'aeoCostComparisonList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      toolbarEventMap: {
        'pass': this.handlePass,
        'return': this.handleReturn
      },
      components: {
        operateCellRenderer: OpCellRenderer
      },
      cmbSource: {
        remitteeCode: [],
        differenceCompare: [{
          value: '0', label: '等于0'
        }, {
          value: 'N', label: '不等于0'
        }],
        // 审核状态
        auditStatus: expenseManage.AUDIT_STATUS_MAP,
        // 收款单位类型
        remitteeType: expenseManage.REMITTEE_TYPE_MAP,
        // 比对状态
        compareStatus: expenseManage.COMPARE_STATUS_MAP
      }
    }
  },
  /**
   * 创建
   */
  created: function () {
    let me = this
    // 收款单位
    me.$http.post(csAPI.ieParams.selectComboxByCode + '/PRD,FOD,CUT').then(res => {
      me.$set(me.cmbSource, 'remitteeCode', ArrayToLocaleLowerCase(res.data.data))
    }).catch(() => {
      me.$set(me.cmbSource, 'remitteeCode', [])
    }).finally(() => {
      me.searchFieldsReLoad('remitteeCode')
    })
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 审核通过
     */
    handlePass() {
      let me = this
      if (me.checkRowSelected('审核通过')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '审核通过',
          cancelText: '取消',
          content: '确认所选项审核通过吗?',
          onOk: () => {
            me.setToolbarLoading('pass', true)
            me.$http.post(me.ajaxUrl.auditBatch + '/2', me.listConfig.selectRows.map(item => {
              return {
                sid: item.sid
              }
            })).then(() => {
              me.handleSearchSubmit()
              me.$Message.success('审核通过成功!')
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('pass')
            })
          }
        })
      }
    },
    /**
     * 审核驳回
     */
    handleReturn() {
      let me = this
      if (me.checkRowSelected('审核驳回')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '审核驳回',
          cancelText: '取消',
          content: '确认所选项审核驳回吗?',
          onOk: () => {
            me.setToolbarLoading('return', true)
            me.$http.post(me.ajaxUrl.auditBatch + '/3', me.listConfig.selectRows.map(item => {
              return {
                sid: item.sid
              }
            })).then(() => {
              me.handleSearchSubmit()
              me.$Message.success('审核驳回成功!')
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('return')
            })
          }
        })
      }
    }
  }
}
