import { csAPI } from '@/api'
import { editStatus, AeoInfoList } from '@/view/cs-common'
import costComparisonDetails from '@/view/cs-custom-made/ri-yue-xin/expense-manage/comm/cost-comparison/cost-comparison-details'

export const aeoCostComparisonTabs = {
  name: 'aeoCostComparisonTabs',
  components: {
    AeoInfoList,
    costComparisonDetails
  },
  props: {
    inSource: {
      type: Object,
      default: () => ({})
    },
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        aeoTab: false
      },
      ajaxUrl: {
        getCostAuditList: csAPI.expenseManage.riYueXin.costAeo.costAudit.getCostAuditList
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (tabName) {
        let me = this
        me.tabs[tabName] = true
      }
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  }
}
