<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <costComparisonEEdit ref="head" :edit-config="editConfig" :in-source="inSource" is-exam
                             @onEditBack="editBack"></costComparisonEEdit>
      </TabPane>
      <TabPane name="bodyTab" label="比对明细">
        <costComparisonDetails ref="body" :head-id="editConfig.editData.sid" i-e-mark="E"></costComparisonDetails>
      </TabPane>
      <TabPane name="aeoTab" label="内审情况">
        <AeoInfoList :sid="editConfig.editData.sid" :special-url="ajaxUrl.getCostAuditList" show-title="false"></AeoInfoList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { aeoCostComparisonTabs } from '../comm/js/aeoCostComparisonTabs'
  import costComparisonEEdit from '@/view/cs-custom-made/ri-yue-xin/expense-manage/cost-comparison-e/cost-comparison-e-edit'

  export default {
    name: 'aeoCostComparisonETabs',
    mixins: [aeoCostComparisonTabs],
    components: {
      costComparisonEEdit
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
