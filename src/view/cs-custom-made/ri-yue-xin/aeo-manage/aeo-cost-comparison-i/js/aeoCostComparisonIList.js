import aeoCostComparisonITabs from '../aeo-cost-comparison-i-tabs'
import { aeoCostComparisonList } from '../../comm/js/aeoCostComparisonList'

export const aeoCostComparisonIList = {
  name: 'aeoCostComparisonIList',
  mixins: [aeoCostComparisonList],
  components: {
    aeoCostComparisonITabs
  },
  /**
   * 方法
   */
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'payNo',
        title: '账单编号'
      }, {
        // type:'select',
        title: '收款单位',
        key: 'remitteeCode'
      }, {
        type: 'select',
        title: '差异结果',
        key: 'differenceCompare'
      }, {
        type: 'select',
        title: '审核状态',
        defaultValue: '1',
        key: 'auditStatus'
      }]
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '比对状态',
        key: 'compareStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.compareStatus)
        })
      }, {
        width: 120,
        key: 'payNo',
        title: '账单编号'
      }, {
        width: 150,
        title: '收款单位',
        key: 'remitteeCode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'remitteeCode', 'remitteeName')
        }, true)
      }, {
        width: 136,
        title: '预估总费用',
        key: 'estimateCostRmbTotal'
      }, {
        width: 136,
        title: '实际总费用',
        key: 'actualCostRmbTotal'
      }, {
        width: 136,
        title: '总差异',
        key: 'differenceTotal'
      }, {
        width: 88,
        title: '操作时间',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '操作员',
        key: 'userName'
      }, {
        width: 120,
        key: 'note',
        title: '备注说明',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'auditor',
        title: '审核人'
      }, {
        width: 120,
        title: '审核状态',
        key: 'auditStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.auditStatus)
        })
      }, {
        width: 200,
        title: '审核意见',
        key: 'auditOpinion',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    }
  }
}
