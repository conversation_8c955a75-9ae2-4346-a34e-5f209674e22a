import filingNoticeEdit from '../filing-notice-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const filingNoticeList = {
  name: 'filingNoticeList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    filingNoticeEdit
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        status: [{
          value: '0', label: '暂存'
        }, {
          value: '1', label: '已提交'
        }, {
          value: '2', label: '已处理'
        }],
        modifyMark: [{
          value: '1', label: '修改'
        }, {
          value: '3', label: '新增'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'submit': this.handleSubmit,
        'process': this.handleProcess,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, type: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        title: '状态',
        key: 'status',
        type: 'select'
      }, {
        key: 'copGNo',
        title: '备案料号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        width: 120,
        title: '备案标识',
        key: 'modifyMark',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.modifyMark)
        })
      }, {
        width: 120,
        key: 'copGNo',
        title: '备案料号'
      }, {
        width: 88,
        key: 'sendDate',
        title: '提交日期',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 88,
        key: 'apprDate',
        title: '处理日期',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 150,
        tooltip: true,
        title: '提交人',
        key: 'sendUserName',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 150,
        tooltip: true,
        title: '处理人',
        key: 'apprUserName',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 扩展的自定义编辑校验(可外部覆盖)
     * @param row
     * @param opTitle
     */
    // eslint-disable-next-line no-unused-vars
    extendEditCheck(row, opTitle) {
      let me = this
      if (['1', '2'].includes(row.status)) {
        me.$Message.success('已处理数据，不允许修改!')
        return false
      }
      return true
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 提交
     */
    handleSubmit() {
      let me = this
      if (me.checkRowSelected('提交')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '提交',
          cancelText: '取消',
          content: '确认提交所选项吗?',
          onOk: () => {
            me.setToolbarLoading('submit', true)
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.statusUpdate + '/' + params + '/1').then(() => {
              me.$Message.success('提交成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('submit')
            })
          }
        })
      }
    },
    /**
     * 处理
     */
    handleProcess() {
      let me = this
      if (me.checkRowSelected('处理')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '处理',
          cancelText: '取消',
          content: '确认处理所选项吗?',
          onOk: () => {
            me.setToolbarLoading('process', true)
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.statusUpdate + '/' + params + '/2').then(() => {
              me.$Message.success('处理成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('process')
            })
          }
        })
      }
    }
  }
}
