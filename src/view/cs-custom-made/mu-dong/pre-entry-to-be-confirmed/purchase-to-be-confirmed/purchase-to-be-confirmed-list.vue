<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <reasonForReturnPop :show.sync="reasonForReturn.show" @doReturn="doReturn"></reasonForReturnPop>
    <DialogAttached :show.sync="isShowDialogAttached" :showRowData="rowDataDialogAttached"></DialogAttached>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { formatDate } from '@/libs/datetime'
  import { purchaseToBeConfirmedList } from './js/purchaseToBeConfirmedList'
  import  DialogAttached  from  '../finance-to-be-confirmed/components/DialogAttached'

  export default {
    name: 'purchaseToBeConfirmedList',
    mixins: [purchaseToBeConfirmedList],
    components: {
      DialogAttached
    },
    data() {
      return {
        listConfig: {
          exportTitle: '采购待确认'
        },
        rowDataDialogAttached: {},
        isShowDialogAttached: false,
        ajaxUrl: {
          exportUrl: csAPI.customMade.muDong.preEntryToBeConfirmed.purchaseToBeConfirmed.exportUrl,
          cancelUrl: csAPI.customMade.muDong.preEntryToBeConfirmed.purchaseToBeConfirmed.cancelUrl,
          returnUrl: csAPI.customMade.muDong.preEntryToBeConfirmed.purchaseToBeConfirmed.returnUrl,
          confirmUrl: csAPI.customMade.muDong.preEntryToBeConfirmed.purchaseToBeConfirmed.confirmUrl,
          selectAllPaged: csAPI.customMade.muDong.preEntryToBeConfirmed.purchaseToBeConfirmed.selectAllPaged
        }
      }
    },
    methods: {
      /**
       * 文件 下载 行内
       */
      handleShowFile(row) {
        this.rowDataDialogAttached = row
        this.isShowDialogAttached = true
      }
    },
    mounted() {
      let me = this
      me.$nextTick(() => {
        let nowTime = new Date(),
          today = formatDate(nowTime, 'yyyy-MM-dd'),
          fromDay = today.substring(0, 8) + '01'
        me.$set(me.searchConfig.model, 'erpInsertTimeFrom', fromDay)
        me.$set(me.searchConfig.model, 'erpInsertTimeTo', today)
        me.handleSearchSubmit()
      })
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
