import { namespace } from '@/project'
import toBeConfirmedList from './to-be-confirmed-list/to-be-confirmed-list'
import financeToBeConfirmedList from './finance-to-be-confirmed/finance-to-be-confirmed-list'
import purchaseToBeConfirmedList from './purchase-to-be-confirmed/purchase-to-be-confirmed-list'

/**
 * 预录入待确认
 */
export default [
  {
    path: '/' + namespace + '/preEntryToBeConfirmed/toBeConfirmedList',
    name: 'toBeConfirmedList',
    meta: {
      title: '待确认列表',
      icon: 'ios-document'
    },
    component: toBeConfirmedList
  },
  {
    path: '/' + namespace + '/preEntryToBeConfirmed/purchaseToBeConfirmedList',
    name: 'purchaseToBeConfirmedList',
    meta: {
      title: '采购待确认',
      icon: 'ios-document'
    },
    component: purchaseToBeConfirmedList
  },
  {
    path: '/' + namespace + '/preEntryToBeConfirmed/financeToBeConfirmedList',
    name: 'financeToBeConfirmedList',
    meta: {
      title: '财务待确认',
      icon: 'ios-document'
    },
    component: financeToBeConfirmedList
  }
]
