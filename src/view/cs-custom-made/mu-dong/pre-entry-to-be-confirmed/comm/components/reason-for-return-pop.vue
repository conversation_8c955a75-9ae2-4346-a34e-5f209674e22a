<template>
  <XdoModal width="600" mask v-model="show" title="退回"
            :closable="false" footer-hide :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <XdoForm class="xdo-enter-form" :model="frmData" :rules="rules" label-position="right" :label-width="120">
        <XdoFormItem prop="reason" label="退回原因">
          <XdoIInput type="text" v-model="frmData.reason" :maxlength="256"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary',
        disabled: false
      }
      return {
        rules: {},
        frmData: {
          reason: ''
        },
        buttons: [
          {...btnComm, click: this.handleConfirm, icon: 'dc-btn-save', label: '确认'},
          {...btnComm, click: this.handleClose, icon: 'dc-btn-cancel', label: '关闭'}
        ]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.$set(me.frmData, 'reason', '')
          }
        }
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      },
      handleConfirm() {
        let me = this
        me.$emit('doReturn', me.frmData.reason)
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
