import { csAPI } from '@/api'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import { productClassify, importExportManage, preEntryToBeConfirmed } from '@/view/cs-common'

export const baseToBeConfirmedList = {
  name: 'baseToBeConfirmedList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        emsNo: [],
        agentCode: [],
        overseasShipper: [],
        iEMark: importExportManage.I_E_MARK_MAP,
        bondMark: productClassify.BONDED_FLAG_SELECT,
        pageMark: preEntryToBeConfirmed.PAGE_MARK_MAP,
        apprStatus: importExportManage.auditStatusMap,
        confirmStatus: preEntryToBeConfirmed.CONFIRM_STATUS_MAP,
        confirmStatusDesc: preEntryToBeConfirmed.CONFIRM_STATUS_DESC_MAP,
        financeConfirmStatus: preEntryToBeConfirmed.FINANCE_CONFIRM_STATUS_MAP,
        purchaseConfirmStatus: preEntryToBeConfirmed.PURCHASE_CONFIRM_STATUS_MAP
      }
    }
  },
  created: function () {
    let me = this
    // 备案号
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.$set(me.cmbSource, 'emsNo', res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE
        }
      }))
    }).catch(() => {
      me.$set(me.cmbSource, 'emsNo', [])
    }).finally(() => {
      me.searchFieldsReLoad('emsNo')
    })
    // 报关单申报单位
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.$set(me.cmbSource, 'agentCode', ArrayToLocaleLowerCase(res.data.data))
    }).catch(() => {
      me.$set(me.cmbSource, 'agentCode', [])
    }).finally(() => {
      me.searchFieldsReLoad('agentCode')
    })
    // 境外收(发)货人
    me.$http.post(csAPI.ieParams.selectComboxByCode + '/PRD, CLI').then(res => {
      me.$set(me.cmbSource, 'overseasShipper', [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)])
    }).catch(() => {
      me.$set(me.cmbSource, 'overseasShipper', [])
    }).finally(() => {
      me.searchFieldsReLoad('overseasShipper')
    })
  },
  methods: {
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
