<template>
  <XdoModal v-model="show" mask width="900" title="财务确认"
            :mask-closable="false" footer-hide :closable="false">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered='false'>
      <!-- gird -->
      <xdo-ag-grid ref='ref_agGrid' rowSelection='multiple' :checkboxSelection='checkboxSelection'
                   :columns='gridConfig.gridColumns' :data='gridConfig.data' :height='dynamicHeight-100'
                   :GridReady='handleGridReady' @selectionChanged='handleSelectionChanged'>
      </xdo-ag-grid>
    </XdoCard>
    <div class="xdo-enter-action dc-edit-actions">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </div>
    <DailogDetail :showEdit.sync="isShowDailogDetail" :pSelectOneRow="selectOneRow"
                  @onSaveOk="handleSaveOk"></DailogDetail>
  </XdoModal>
</template>

<script>
  import { csOcrApi } from '../../../api'
  import DailogDetail from'./DailogDetail'
  import { commonMain } from '@/view/cs-acustomization/common/comm/commonMain'
  import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'

  export default {
    name: 'DailogMain',       // 财务确认 弹出框 第一层
    mixins: [commonMain],
    components: {
      DailogDetail
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      /**
       * 页面选中的 数据行
       */
      pSelectRows: {
        type: Array
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        type: 'primary'
      }
      return {
        selectOneRow: {},
        initSearch: false,              // 初始化不查询
        isShowOpView: false,            // 在没有编辑权限的时候 是否显示查看按钮 = 操作列
        p_group: 'meiYouAnNiu',         // 按钮的 组名 跟PMS有关
        checkboxSelection: false,
        isShowDailogDetail: false,
        ajaxUrl: {
          list: csOcrApi.financecConfirm.getConfirmList
        },
        totalColumns: [
          { width: 136, title: '单据编号', key: 'emsListNo' },
          { width: 136, title: '报关单号', key: 'entryNo' },
          { width: 200, title: '财务增值税发票号', key: 'vatInvoiceNo' },
          { width: 136, title: '报关单金额', key: 'sumDecTotal' },
          { width: 136, title: '发票总金额', key: 'totalInvoiceAmount' },
          {
            width: 120,
            title: '操作',
            cellRendererFramework: operationRenderer(this, [{
              title: '维护',
              marginRight: '0',
              handle: 'handleWeiHu'
            }])
          }
        ],
        buttons: [
          { ...btnComm, code: 'true', label: '确认', disabled: false, click: this.handleConfirm },
          { ...btnComm, label: '关闭', code: 'Cancel', disabled: false, click: this.handleClose }
        ]
      }
    },
    watch: {
      show(val) {
        if (val) {
          this.getList()
        }
      }
    },
    methods: {
      /**
       * 子页面保存成功 后的 事件
       */
      handleSaveOk() {
        this.getList()
      },
      /**
       * 维护按钮 事件
       */
      handleWeiHu(param) {
        this.selectOneRow = param
        this.isShowDailogDetail = true
      },
      /**
       * 重写 查询
       * @param searchUrl
       */
      doSearch(searchUrl) {
        let me = this
        me.$nextTick(() => {
          let params = me.pSelectRows.map(i => {
            return i.sid
          })
          me.$http.post(searchUrl, params).then(res => {
            me.gridConfig.data = res.data
            me.handleCommonAfterSearchSuccess()
          }).catch(() => {
            me.handleCommonAfterSearchCatch()
          }).finally(() => {
            me.gridConfig.selectRows = []
            me.handleCommonAfterSearchFinally()
          })
        })
      },
      /**
       * 确定
       */
      handleConfirm() {
        let me = this
        me.$emit('doConfirm', this.pSelectRows)
      },
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>
