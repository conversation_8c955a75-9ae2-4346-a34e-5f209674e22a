import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'
import MyRowEditInputNumber from '../../../../../cs-ocr/gwOcrInvoiceI/components/MyRowEditInputNumber'

export const columns = {
  mixins: [columnRender],
  components: {
    MyRowEditInputNumber
  },
  data() {
    let totalColumnsBase = [
      { width: 200, title: '发票号', key: 'invoiceNo', editable: true },
      { width: 200, title: '发票金额', key: 'invoiceAmount', editable: true }
    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ]
    }
  }
}
