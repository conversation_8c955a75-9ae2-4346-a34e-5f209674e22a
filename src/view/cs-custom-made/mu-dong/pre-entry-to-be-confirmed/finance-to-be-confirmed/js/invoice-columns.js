import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { baseColumnsShow, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'invoiceNo'
  , 'invoiceAmount'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const columns = {
  mixins: [baseEditColumns],
  data() {
    let me = this
    let baseFields = me.getInvoiceColumns()
    return {
      totalColumns: [...baseFields, {
        width: 150,
        title: '发票号',
        align: 'center',
        key: 'invoiceNo',
        render: (h, params) => {
          return me.inputRender(h, params)
        }
      }, {
        width: 200,
        tooltip: true,
        ellipsis: true,
        align: 'center',
        title: '发票金额',
        key: 'invoiceAmount',
        render: (h, params) => {
          return me.inputRender(h, params)
        }
      }]
    }
  },
  methods: {
    getInvoiceColumns() {
      let me = this
      return [{
        width: 36,
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, {
        width: 100,
        title: '操作',
        align: 'center',
        key: 'operation',
        render: (h, { row, index }) => {
          if (row.$rowStatus === editStatus.ADD || row.$rowStatus === editStatus.EDIT) {
            return [h('Button', {
              props: {
                size: 'small',
                type: 'error'
              },
              style: {
                marginLeft: '6px'
              },
              on: {
                click: () => {
                  me.abandonRowEdit(row, index)
                }
              }
            }, '取消')]
          } else {
            return h('Button', {
              props: {
                size: 'small'
              },
              style: {
                display: this.operationEditShow()
              },
              on: {
                click: () => {
                  me.setRowEdit(index)
                }
              }
            }, '编辑')
          }
        }
      }]
    },
    /**
     * 单元格值变更事件
     * @param e
     */
    onCellValueChanged(e) {
      let me = this,
        data = e.data,
        field = e.column['colId'],
        vatInvoiceNo = data.vatInvoiceNo
      if (!isNullOrEmpty(vatInvoiceNo)) {
        vatInvoiceNo = vatInvoiceNo.trim()
        if (vatInvoiceNo.length > 50) {
          me.$Message.warning('财务增值税发票号不能超过50位字节长度(一个汉字2位字节长度)!')
          if ('vatInvoiceNo' === field) {
            e.data['vatInvoiceNo'] = e.oldValue
          }
        }
      }
    },
    handleClose() {
      let me = this
      me.$emit('update:show', false)
    },
    handleConfirm() {
      let me = this
      me.addEmptyRow({
        moduleName: '',
        tradeMode: '',
        fixedPrefix: ''
      })
    }
  }
}

export {
  columns,
  columnsConfig,
  baseColumnsShow
}
