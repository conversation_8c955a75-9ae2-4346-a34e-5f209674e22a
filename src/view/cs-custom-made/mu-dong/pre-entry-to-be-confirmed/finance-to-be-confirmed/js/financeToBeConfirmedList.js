import { importExportManage } from '@/view/cs-common'
import invoiceNoPop from '../components/invoice-no-pop'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import reasonForReturnPop from '../../comm/components/reason-for-return-pop'
import { operationRenderer } from '@/view/cs-acustomization/common/agGridJs/operation_renderer'
import { baseToBeConfirmedList } from '@/view/cs-custom-made/mu-dong/pre-entry-to-be-confirmed/comm/js/baseToBeConfirmedList'

export const financeToBeConfirmedList = {
  name: 'financeToBeConfirmedList',
  mixins: [baseToBeConfirmedList],
  components: {
    invoiceNoPop,
    reasonForReturnPop
  },
  data() {
    return {
      invoiceNoPop: {
        show: false,
        selectRows: []
      },
      reasonForReturn: {
        show: false
      },
      cmbSource: {
        purchaseConfirm: [{
          value: '0', label: '空'
        }, {
          value: '1', label: '非空'
        }]
      },
      toolbarEventMap: {
        'cancel': this.handleCancel,
        'return': this.handleReturn,
        'confirm': this.handleConfirm
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{title: string, key: string}|{range: boolean, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'iEMark',
        type: 'select',
        title: '进出口标志'
      }, {
        type: 'select',
        key: 'bondMark',
        title: '保完税标识'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        range: true,
        title: '制单日期',
        key: 'erpInsertTime'
      }, {
        type: 'pcode',
        props: {
          meta: 'TRADE'
        },
        title: '监管方式',
        key: 'tradeMode'
      }, {
        key: 'emsNo',
        title: '备案号',
        type: 'select',
        props: {
          optionLabelRender: (opt) => opt.label
        }
      }, {
        title: '制单员',
        key: 'erpInsertUser'
      }, {
        title: '发票号',
        key: 'invoiceNo'
      }, {
        type: 'select',
        title: '境外收(发)货人',
        key: 'overseasShipper'
      }, {
        type: 'pcode',
        props: {
          meta: 'TRANSF'
        },
        key: 'trafMode',
        title: '运输方式'
      }, {
        key: 'trafName',
        title: '运输工具名称'
      }, {
        key: 'contrNo',
        title: '合同协议号'
      }, {
        key: 'entryNo',
        title: '报关单号'
      }, {
        title: '内审员',
        key: 'internalAuditUser'
      }, {
        key: 'remark',
        title: '内部备注'
      }, {
        type: 'pcode',
        key: 'entryPort',
        title: '入/离境口岸',
        props: {
          meta: 'CIQ_ENTY_PORT'
        }
      }, {
        key: 'hawb',
        title: '提运单号'
      }, {
        type: 'select',
        defaultValue: '0',
        title: '财务确认状态',
        key: 'financeConfirmStatus'
      }, {
        key: 'vatInvoiceNo',
        title: '财务增值税发票号'
      }, {
        type: 'select',
        title: '采购确认用户',
        key: 'purchaseConfirm'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['pageMark'] = '3'
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 100,
        align: 'center',
        title: '随附单据',
        cellRendererFramework: operationRenderer(this, [{ title: '下载', handle: 'handleShowFile', marginRight: '0' }])
      }, {
        width: 150,
        title: '退回原因',
        key: 'refuseReason',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '内审状态',
        key: 'apprStatus',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.apprStatus)
        })
      }, {
        width: 120,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        key: 'bondMark',
        title: '保完税标识',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.bondMark)
        })
      }, {
        width: 120,
        key: 'iEMark',
        title: '进出口标志',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.iEMark)
        })
      }, {
        width: 120,
        key: 'tradeMode',
        title: '监管方式',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        })
      }, {
        width: 160,
        title: '境外收(发)货人',
        key: 'overseasShipper',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.keyValueRender(h, params, 'overseasShipper', 'overseasShipperName')
        }, true)
      }, {
        width: 150,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 150,
        key: 'contrNo',
        title: '合同协议号'
      }, {
        width: 136,
        key: 'netWt',
        title: '总净重'
      }, {
        width: 136,
        key: 'grossWt',
        title: '总毛重'
      }, {
        width: 136,
        key: 'volume',
        title: '总体积'
      }, {
        width: 136,
        title: '件数',
        key: 'packNum'
      }, {
        width: 80,
        key: 'feeMark',
        title: '运费-类型',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, importExportManage.feeTypeMap)
        })
      }, {
        width: 80,
        key: 'feeRate',
        title: '运费-费率'
      }, {
        width: 150,
        key: 'feeCurr',
        title: '运费-币制',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 80,
        key: 'insurMark',
        title: '保费-类型',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, importExportManage.feeTypeMap)
        })
      }, {
        width: 80,
        key: 'insurRate',
        title: '保费-费率'
      }, {
        width: 150,
        key: 'insurCurr',
        title: '保费-币制',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 80,
        title: '杂费-类型',
        key: 'otherMark',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, importExportManage.feeTypeMap)
        })
      }, {
        width: 80,
        key: 'otherRate',
        title: '杂费-费率'
      }, {
        width: 150,
        title: '杂费-币制',
        key: 'otherCurr',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 136,
        title: '总金额',
        key: 'sumDecTotal'
      }, {
        width: 88,
        title: '制单日期',
        key: 'erpInsertTime',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '制单员',
        key: 'erpInsertUserName'
      }, {
        width: 120,
        key: 'entryNo',
        title: '报关单号'
      }, {
        width: 120,
        key: 'hawb',
        title: '提运单号'
      }, {
        width: 120,
        key: 'trafName',
        title: '运输工具名称'
      }, {
        width: 120,
        title: '航次号',
        key: 'voyageNo'
      }, {
        width: 120,
        key: 'trafMode',
        title: '运输方式',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.transf)
        })
      }, {
        width: 120,
        title: '内审员',
        key: 'internalAuditUserName'
      }, {
        width: 150,
        key: 'remark',
        title: '内部备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'entryPort',
        title: '入/离境口岸',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
        })
      }, {
        width: 120,
        key: 'inviteDate',
        title: '申请进/出口日期',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        key: 'iedate',
        title: '进/出口日期',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        key: 'emsNo',
        title: '备案号'
      }, {
        width: 136,
        key: 'cWeight',
        title: '计费重量'
      }, {
        width: 120,
        title: '采购确认用户',
        key: 'purchaseComfirmerName'
      }, {
        width: 120,
        title: '财务确认用户',
        key: 'financeComfirmerName'
      }, {
        width: 136,
        title: '财务确认状态',
        key: 'financeConfirmStatus',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.financeConfirmStatus)
        })
      }, {
        width: 136,
        key: 'vatInvoiceNo',
        title: '财务增值税发票号'
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }]
    },
    /**
     * 访问确认后端方法
     * @param confirmRows
     */
    realDoConfirm(confirmRows) {
      let me = this
      me.setToolbarLoading('confirm', true)
      me.$http.post(me.ajaxUrl.confirmUrl, {
        params: confirmRows
      }).then(() => {
        me.handleSearchSubmit()
        this.isShowFinancecConfirm = false
        me.$Message.success('确认成功!')
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('confirm')
        me.$set(me.invoiceNoPop, 'show', false)
      })
    },
    /**
     * 执行确认
     * @param confirmRows
     */
    doConfirm(confirmRows) {
      let me = this
      me.$http.post(me.ajaxUrl.checkForFinanceConfirm, {
        params: confirmRows
      }).then(res => {
        if (res.data.data === '1') {
          me.$Modal.confirm({
            title: '提醒',
            okText: '继续',
            loading: true,
            cancelText: '取消',
            content: res.data.message,
            onOk: () => {
              me.realDoConfirm(confirmRows)
              setTimeout(() => {
                me.$Modal.remove()
              }, 150)
            }
          })
        } else {
          me.realDoConfirm(confirmRows)
        }
      }).catch(() => {
      })
    },
    /**
     * 执行取消操作
     */
    doCancel() {
      let me = this
      me.setToolbarLoading('cancel', true)
      me.$Modal.confirm({
        title: '提醒',
        okText: '是',
        loading: true,
        cancelText: '否',
        content: '是否清除已确认的“发票号”!',
        onOk: () => {
          me.$http.post(`${me.ajaxUrl.cancelUrl}/1`, me.getSelectedParams()).then(() => {
            me.handleSearchSubmit()
            me.$Message.success('取消成功!')
          }).catch(() => {
          }).finally(() => {
            me.setToolbarLoading('cancel')
          })
          setTimeout(() => {
            me.$Modal.remove()
          }, 150)
        },
        onCancel: () => {
          me.$http.post(`${me.ajaxUrl.cancelUrl}/0`, me.getSelectedParams()).then(() => {
            this.isShowFinancecConfirm = true
            me.$Message.success('取消成功!')
          }).catch(() => {
          }).finally(() => {
            me.setToolbarLoading('cancel')
          })
          setTimeout(() => {
            me.$Modal.remove()
          }, 150)
        }
      })
    },
    /**
     * 取消
     */
    handleCancel() {
      let me = this
      if (me.checkRowSelected('取消')) {
        me.setToolbarLoading('cancel', true)
        me.$http.post(me.ajaxUrl.CancelCheckUrl, me.getSelectedParams()).then(() => {
          me.doCancel()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('cancel')
        })
      }
    },
    /**
     * 退回
     */
    handleReturn() {
      let me = this
      if (me.checkRowSelected('退回')) {
        let confirmStatus = me.listConfig.selectRows.filter(row => {
          return row.confirmStatus === '3'
        })
        if (Array.isArray(confirmStatus) && confirmStatus.length > 0) {
          me.$Message.warning('存在财务不可退回的单据!')
        } else {
          me.$set(me.reasonForReturn, 'show', true)
        }
      }
    },
    /**
     * 执行退回
     * @param reason
     */
    doReturn(reason) {
      let me = this
      me.setToolbarLoading('return', true)
      me.$http.post(me.ajaxUrl.returnUrl, {
        refuseReason: reason,
        sids: me.getSelectedParams()
      }).then(() => {
        me.handleSearchSubmit()
        me.$Message.success('退回成功!')
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('return')
        me.$set(me.reasonForReturn, 'show', false)
      })
    }
  }
}
