import { commList } from '@/view/cs-interim-verification/comm/commList'

export const baseMethods = {
  mixins: [commList],
  data() {
    let btnComm = {
      type: 'text',
      needed: true,
      loading: false,
      disabled: false
    }
    return {
      searchLines: 0,
      OffsetHeight: -28,
      hasChildTabs: true,
      ajaxUrl: {
        // insert: csAPI.csBaseInfo.productionUnit.insert,
        // update: csAPI.csBaseInfo.productionUnit.update,
        // delete: csAPI.csBaseInfo.productionUnit.delete,
        // selectAllPaged: csAPI.csBaseInfo.productionUnit.selectAllPaged
      },
      actions: [
        { ...btnComm, label: '新增', command: 'add', icon: 'ios-add', key: 'xdo-btn-add', click: this.handleAdd }
        // {...btnComm, label: '删除', command: 'delete', icon: 'ios-trash-outline', key: 'xdo-btn-delete', click: this.handleDelete}
      ]
    }
  },
  methods: {
    handleAdd() {
      let me = this
      me.addEmptyRow({
        moduleName: '',
        tradeMode: '',
        fixedPrefix: ''
      })
    },
    rowEdit() {
      let me = this
      if (me.checkRowSelected('编辑', true)) {
        let index = -1,
          theInd = -1,
          theRow = me.gridConfig.selectRows[0]
        for (let row of me.gridConfig.data) {
          theInd++
          if (row.sid === theRow.sid) {
            index = theInd
          }
        }
        if (index > -1) {
          me.setRowEdit(index)
        } else {
          console.error('当前选中的数据存在问题!')
        }
      }
    },
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    getSearchParams() {
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : { customerType: this.customerType }))
    }
  }
}
