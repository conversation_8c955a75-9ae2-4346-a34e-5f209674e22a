<template>
  <section>
    <XdoCard :bordered="false">
      <template v-for="item in actions">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   style="font-size: 12px;" :class="item.key" @click="item.click" :key="item.label">
          <XdoIcon :type="item.icon" size="22" class="xdo-icon"/>
          {{ item.label }}
        </XdoButton>&nbsp;
      </template>
    </XdoCard>
    <XdoCard :bordered="false">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
      <div class="dc-container-center">
        <XdoButton class="btntool" type="warning" @click="templateSave">保存</XdoButton>&nbsp;
        <XdoButton class="btntool" type="primary" @click="closeAdd">返回</XdoButton>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { getColumnsByConfig } from '@/common'
  import { baseMethods } from '../js/invoiceMethods'
  import { columnsConfig, columns } from '../js/invoice-columns'

  export default {
    name: 'invoiceNoOperate',
    mixins: [columns, baseMethods],
    props: {
      customerType: {
        type: Object,
        default: () => ''
      }
    },
    mounted: function() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    },
    closeAdd() {
      let me = this
      Object.keys(me.addParams).forEach(item => {
        me.addParams[item] = ''
      })
      me.$emit('modalClose', true)
    },
    templateSave() {
      let me = this
      me.$refs['table'].getAgGrdApi().stopEditing()
      me.$emit('doConfirm', me.listConfig.data)
    }
  }
</script>
