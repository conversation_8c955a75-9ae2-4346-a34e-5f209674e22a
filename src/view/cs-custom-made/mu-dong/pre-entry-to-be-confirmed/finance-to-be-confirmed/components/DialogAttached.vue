<template>
  <XdoModal v-model="show" mask width="500" title="随附单据信息"
            :mask-closable="false" footer-hide :closable="false">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px;color: #389DE9;line-height: 1;"></XdoIcon>
    </a>
    <AttachedDocument v-if="show" ref="attachInfo"  :head-id="showRowData.decErpId" :edit="editData" :iemark="showRowData.iemark" :loaded="1"></AttachedDocument>
    <XdoForm class="dc-form dc-form-2 xdo-enter-form" ref="form" :show-message="false"  :label-width="50">
      <XdoFormItem class="dc-merge-1-3" style="text-align: right;">
        <XdoButton type="error" icon="ios-close" style="margin-left: 5px;" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>

<script>
  import  AttachedDocument from '@/view/cs-ie-manage/attached-document/attached-document'

  export default {
    name: 'DialogAttached',   // 行内 附件查看 弹出框
    components: {
      AttachedDocument
    },
    props: {
      show: {
        type: Boolean,
        required: true
      },
      /**
       * 当前行的数据
       */
      showRowData: {
        type: Object,
        required: true
      }
    },
    data() {
      return {}
    },
    computed: {
      editData() {
        return {
          editData: this.showRowData
        }
      }
    },
    watch: {
      show() {
        console.log(this.showRowData)
      }
    },
    methods: {
      /**
       * 关闭
       */
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

