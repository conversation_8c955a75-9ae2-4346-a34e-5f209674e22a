import { namespace } from '@/project'
import arrivalList from './arrival-list/arrival-list'
import logisticsQueryList from './logistics-query/logistics-query-list'
import containerQueryList from './container-query/container-query-list.vue'
import customsClearanceTrackingList from './customs-clearance-tracking/customs-clearance-tracking-list'

export default [
  {
    path: '/' + namespace + '/reports/arrivalList',
    name: 'arrivalList',
    meta: {
      title: '到货清单',
      icon: 'ios-document'
    },
    component: arrivalList
  },
  {
    path: '/' + namespace + '/reports/logisticsQueryList',
    name: 'logisticsQueryList',
    meta: {
      title: '物流查询',
      icon: 'ios-document'
    },
    component: logisticsQueryList
  },
  {
    path: '/' + namespace + '/reports/customsClearanceTrackingList',
    name: 'customsClearanceTrackingList',
    meta: {
      title: '报关时效追踪',
      icon: 'ios-document'
    },
    component: customsClearanceTrackingList
  },
  {
    path: '/' + namespace + '/reports/containerQueryList',
    name: 'containerQueryList',
    meta: {
      title: '集装箱信息',
      icon: 'ios-document'
    },
    component: containerQueryList
  }
]
