import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const logisticsQueryList = {
  name: 'logisticsQueryList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {},
      listConfig: {
        checkColumnShow: false,           // 显示选择列
        operationColumnShow: false        // 显示操作列
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  computed: {
    defaultDates() {
      let today = new Date(),
        dateTo = today.toLocaleDateString(),
        dateFrom = new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString()
      return [dateFrom, dateTo]
    }
  },
  methods: {
    /**
     * 首次查询前赋值
     */
    beforeFirstSearch() {
      let me = this
      me.$set(me.searchConfig.model, 'planArrivalDateFrom', me.defaultDates[0])
      me.$set(me.searchConfig.model, 'planArrivalDateTo', me.defaultDates[1])
    },
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'sa',
        title: 'SA'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        title: '清单内部编号',
        key: 'billEmsListNo'
      }, {
        key: 'hawb',
        title: '提运单号'
      }, {
        title: '境外发货人名称',
        key: 'overseasShipperName'
      }, {
        key: 'note1',
        title: 'DN号'
      }, {
        key: 'orderNo',
        title: '订单号'
      }, {
        range: true,
        title: '预计到厂日期',
        key: 'planArrivalDate'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string}|{width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        key: 'sa',
        width: 120,
        title: 'SA'
      }, {
        width: 120,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        title: '清单内部编号',
        key: 'billEmsListNo'
      }, {
        width: 120,
        key: 'hawb',
        title: '提运单号'
      }, {
        width: 120,
        title: '境外发货人名称',
        key: 'overseasShipperName'
      }, {
        width: 120,
        key: 'note1',
        title: 'DN号'
      }, {
        width: 120,
        key: 'orderNo',
        title: '订单号'
      }, {
        width: 120,
        title: '报关状态',
        key: 'entryStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.keyValueRender(h, params, 'entryStatus', 'entryStatusName')
        })
      }, {
        width: 120,
        title: '预计到厂日期',
        key: 'planArrivalDate',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        key: 'deliveryDate',
        title: '实际到厂日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '关务人员',
        key: 'insertUserName'
      }, {
        width: 120,
        title: 'ETA日期',
        key: 'arrivalPortDate',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
