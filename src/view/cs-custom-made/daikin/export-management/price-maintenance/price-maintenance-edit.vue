<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <XdoCard :bordered="false" class="ieLogisticsTrackingCard" title="详细信息">
        <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                     :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        </DynamicForm>
      </XdoCard>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'priceMaintenanceEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        cmbSource: {
          customerCode: []
        },
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.daiKinOrderManagement.priceMaintenance.insert,
          update: csAPI.daiKinOrderManagement.priceMaintenance.update
        }
      }
    },
    created: function() {
      let me = this
      // 客户
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.$set(me.cmbSource, 'customerCode', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'customerCode', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'customerCode')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function() {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      onCustomerCodeChange() {
        let me = this,
          customerCode = me.detailConfig.model['customerCode'],
          item = me.cmbSource.customerCode.find(x => x.value === customerCode)
        if (item) {
          me.$set(me.detailConfig.model, 'customerName', item.label)
        } else {
          me.$set(me.detailConfig.model, 'customerName', '')
        }
      },
      getFields() {
        let me = this
        return [{
          type: 'select',
          required: true,
          title: '客户代码',
          key: 'customerCode',
          on: {
            change: me.onCustomerCodeChange
          }
        }, {
          props: {
            disabled: true
          },
          title: '客户名称',
          key: 'customerName'
        }, {
          key: 'facGNo',
          required: true,
          props: {
            maxLength: 50
          },
          title: '企业料号'
        }, {
          title: '单价',
          required: true,
          props: {
            intDigits: 8,
            precision: 5
          },
          key: 'decPrice',
          type: 'xdoInput'
        }, {
          key: 'curr',
          title: '币制',
          type: 'pcode',
          required: true,
          props: {
            meta: 'CURR_OUTDATED'
          }
        }, {
          required: true,
          key: 'beginDate',
          type: 'datePicker',
          title: '有效期起始日'
        }, {
          required: true,
          key: 'endDate',
          type: 'datePicker',
          title: '有效期截至日'
        }, {
          props: {
            maxLength: 50
          },
          title: '报价单号',
          key: 'quotationNo'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxLength: 255
          }
        }]
      },
      handleSave() {
        let me = this
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
