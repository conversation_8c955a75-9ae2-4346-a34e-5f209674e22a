import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const orderWithdrawalPop = {
  name: 'orderWithdrawalPop',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        type:[]
      },
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      pmsLevel: 'extract',
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'extract-all': this.handleExtractAll,
        'extract-selected': this.handleExtractChoose
      }
    }
  },
  computed: {
    /**
     * 动态数据源
     * @returns {*}
     */
    dynamicSource() {
      let me = this
      return {
        ...me.cmbSource
      }
    }
  },
  mounted: function() {
    let me = this
    me.$http.post(me.ajaxUrl.getAllTypes).then(res => {
      if(!res.data){
        return []
      }
      console.log("类别数据源",res.data.data)
      me.cmbSource.type = res.data.data.map((item) => {
        return {
          value: item,
          label: ''
        }
      })
    }).catch(() => {
      me.cmbSource.type = []
    }).finally( () => {
      me.searchFieldsReLoad('type')
    })

  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions = [{
        ...me.actionsComm,
        label: '勾选提取',
        key: 'xdo-btn-edit',
        icon: 'ios-checkmark',
        command: 'extract-selected'
      }, {
        ...me.actionsComm,
        label: '全部提取',
        icon: 'md-done-all',
        key: 'xdo-btn-upload',
        command: 'extract-all'
      }]
    },
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'type',
        type: 'select',
        title: '类别'
      }, {
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        key: 'customerFacGNo',
        title: 'Parts No/客户料号'
      }, {
        title: '申报图号',
        key: 'facGNo'
      }, {
        key: 'note',
        title: '备注'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列信息重置
     * @param showErrMsg
     */
    fieldReLoad(showErrMsg) {
      let me = this,
        fields = me.getFields()
      if (showErrMsg) {
        me.$set(me.listConfig, 'settingColumns', fields)
      } else {
        me.$set(me.listConfig, 'settingColumns', fields.filter(field => field.key !== 'tempRemark'))
      }
      me.loadListConfig()
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 268,
        title: '错误信息',
        key: 'tempRemark',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        key: 'type',
        title: '类别'
      }, {
        width: 120,
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        width: 156,
        key: 'customerFacGNo',
        title: 'Parts No/客户料号'
      }, {
        width: 120,
        title: '申报图号',
        key: 'facGNo'
      }, {
        width: 120,
        title: '申报数量',
        key: 'orderQty'
      }, {
        width: 120,
        key: 'exportQty',
        title: '已出口数'
      }, {
        width: 120,
        key: 'surplusQty',
        title: '申报剩余数'
      }, {
        width: 120,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        }, true)
      }, {
        width: 268,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: 'Internal Order No.',
        key: 'internalOrderNo'
      }, {
        width: 120,
        title: '英文品名',
        key: 'copGNameEn'
      }]
    },
    /**
     * 点击查询按钮
     */
    handleSearchSubmit() {
      let me = this
      me.$set(me.pageParam, 'page', 1)
      me.fieldReLoad(false)
      me.getList()
    },
    /**
     * 显示信息
     * @param res
     */
    errMsgShow(res) {
      let me = this
      me.$Message.success(res.data.message)
      me.$emit('doExtracted')
      if (Array.isArray(res.data.data) && res.data.data.length > 0) {
        me.fieldReLoad(true)
        me.$set(me.listConfig, 'data', res.data.data)
        me.$set(me.pageParam, 'page', res.data.pageIndex)
        me.$set(me.pageParam, 'dataTotal', res.data.total)
      } else {
        me.handleClose()
      }
    },
    /**
     * 提取全部
     */
    handleExtractAll() {
      let me = this,
        searchParam = me.getSearchParams()
      me.setToolbarLoading('extract-all', true)
      me.$http.post(me.ajaxUrl.extractByParams, {
        ...searchParam,
        headId: me.headId
      }).then(res => {
        me.errMsgShow(res)
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading('extract-all')
      })
    },
    /**
     * 提取勾选
     */
    handleExtractChoose() {
      let me = this
      if (me.checkRowSelected('提取勾选')) {
        me.setToolbarLoading('extract-selected', true)
        let params = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.extractByChoose + '/' + me.headId + '/' + params).then(res => {
          me.errMsgShow(res)
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('extract-selected')
        })
      }
    }
  }
}
