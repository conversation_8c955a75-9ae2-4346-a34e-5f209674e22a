import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import orderManagementEdit from '../order-management-edit'
import orderManagementBodyList from '../order-management-body-list'

export const orderManagementTabs = {
  name: 'orderManagementTabs',
  components: {
    orderManagementEdit,
    orderManagementBodyList
  },
  props: {
    inSource: {
      type: Object,
      default: () => ({})
    },
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false
      }
    }
  },
  computed: {
    showBody() {
      let me = this
      return !isNullOrEmpty(me.editConfig.editData.sid)
    },
    parentConfig() {
      let me = this
      return {
        headId: me.editConfig.editData.sid,
        editStatus: me.editConfig.editStatus
      }
    }
  },
  watch: {
    tabName: {
      immediate: true,
      handler: function (tabName) {
        let me = this
        me.tabs[tabName] = true
      }
    }
  },
  methods: {
    /**
     * 返回列表界面
     */
    backToList() {
      let me = this
      me.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      let me = this
      me.$emit('onEditBack', backObj)
    }
  }
}
