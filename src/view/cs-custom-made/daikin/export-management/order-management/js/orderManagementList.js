import ImportPage from 'xdo-import'
import { isNullOrEmpty } from '@/libs/util'
import { interimVerification } from '@/view/cs-common'
import orderManagementTabs from '../order-management-tabs'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const orderManagementList = {
  name: 'orderManagementList',
  components: {
    ImportPage,
    orderManagementTabs
  },
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      jobInfo: {
        status: '',
        lastTime: '',
        interval: '',
        paramsStr: ''
      },
      jobType: 'CAL_ORDER_EXPORT_QTY',
      toolbarEventMap: {
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'import': this.handleImport,
        'export': this.handleDownload,
        'calculate': this.handleCalculate,
        'setting': this.handleTableColumnSetup
      },
      cmbSource: {
        customerCode: [],
        updateStatus: interimVerification.CALCULATE_STATUS_MAP
      }
    }
  },
  /**
   * 计算属性
   */
  computed: {
    jobStatusName() {
      let me = this,
        theStatus = me.cmbSource.updateStatus.filter(item => {
          return item.value === me.jobInfo.status
        })
      if (Array.isArray(theStatus) && theStatus.length > 0) {
        return theStatus[0].label
      }
      return ''
    },
    jobStatusStyle() {
      let me = this
      if (['0', '1'].includes(me.jobInfo.status)) {
        return 'color: blue'
      } else if (me.jobInfo.status === '2') {
        return 'color: green'
      } else if (me.jobInfo.status === '3') {
        return 'color: red'
      }
    }
  },
  methods: {
    /**
     * 获取最终Job执行状态
     */
    getLastJobInfo() {
      let me = this
      me.$http.post(me.ajaxUrl.getLastJob, {
        jobType: me.jobType
      }).then(res => {
        me.$set(me.jobInfo, 'status', res.data.data['status'])
        me.$set(me.jobInfo, 'lastTime', res.data.data['endTime'])
        if (['2', '3'].includes(me.jobInfo.status)) {
          clearInterval(me.jobInfo.interval)
          me.$set(me.jobInfo, 'interval', '')
        }
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    },
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        title: '订单管理号',
        key: 'manageOrderNo'
      }, {
        title: '客户',
        type: 'select',
        key: 'customerCode'
      }, {
        range: true,
        title: '接单日',
        key: 'receiveDate'
      }, {
        key: 'type',
        title: '类别'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '订单管理号',
        key: 'manageOrderNo'
      }, {
        width: 236,
        title: '客户',
        key: 'customerCode',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.customerCode)
        }, true)
      }, {
        width: 120,
        title: '接单日',
        key: 'receiveDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        key: 'type',
        title: '类别'
      }, {
        width: 120,
        title: '目的地',
        key: 'destName',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 120,
        title: '运输方式',
        key: 'trafModeName'
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 弹出导入窗体
     */
    handleImport() {
      let me = this
      me.$set(me.importConfig, 'show', true)
    },
    /**
     * 完成导入
     */
    onAfterImport() {
      let me = this
      me.$set(me.importConfig, 'show', false)
      me.getList()
    },
    /**
     * 计算
     */
    handleCalculate() {
      let me = this
      me.$http.post(me.ajaxUrl.insertJob, {
        jobType: me.jobType
      }).then(() => {
        if (isNullOrEmpty(me.jobInfo.interval)) {
          me.$set(me.jobInfo, 'interval', setInterval(me.getLastJobInfo, 10000))
        }
        me.$Message.success('操作成功!')
      }).catch(() => {
        clearInterval(me.jobInfo.interval)
        me.$set(me.jobInfo, 'interval', '')
      })
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  },
  beforeDestroy: function() {
    let me = this
    clearInterval(me.jobInfo.interval)
    me.$set(me.jobInfo, 'interval', '')
  }
}
