import { editStatus } from '@/view/cs-common'
import orderManagementBodyEdit from '../order-management-body-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const orderManagementBodyList = {
  name: 'orderManagementBodyList',
  components: {
    orderManagementBodyEdit
  },
  mixins: [baseSearchConfig, columnRender, listDataProcessing],
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        headId: '',
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {},
      pmsLevel: 'body',
      hasChildTabs: true,
      toolbarEventMap: {
        'edit': this.handleEdit,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      if (me.parentConfig.editStatus === editStatus.EDIT) {
        me.$set(me.listConfig, 'disable', false)
      } else {
        me.actions = []
        me.$set(me.listConfig, 'disable', true)
      }
    },
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        key: 'facGNo',
        title: '申报图号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      paramObj['headId'] = me.parentConfig.headId
      return paramObj
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '订单管理号',
        key: 'manageOrderNo'
      }, {
        width: 120,
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        width: 156,
        key: 'customerFacGNo',
        title: 'Parts No/客户料号'
      }, {
        width: 120,
        key: 'facGNo',
        title: '申报图号'
      }, {
        width: 136,
        key: 'orderQty',
        title: '申报数量'
      }, {
        width: 120,
        title: '订单图号',
        key: 'orderFacGNo'
      }, {
        width: 120,
        key: 'qty',
        title: '订单数量'
      }, {
        width: 138,
        title: '英文品名',
        key: 'copGNameEn',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 156,
        key: 'internalOrderNo',
        title: 'Internal Order No.'
      }, {
        width: 136,
        key: 'etd',
        title: '注文要求納期(ETD)',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 88,
        key: 'changeEtd',
        title: '調整納期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 88,
        key: 'lastEtd',
        title: '最終納期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 136,
        title: '已出口数',
        key: 'exportQty'
      }, {
        width: 136,
        key: 'surplusQty',
        title: '申报剩余数'
      }, {
        width: 136,
        title: '单价',
        key: 'decPrice'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 136,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
