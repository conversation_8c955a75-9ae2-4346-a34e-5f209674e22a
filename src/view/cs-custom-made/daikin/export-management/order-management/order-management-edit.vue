<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <XdoCard :bordered="false" class="ieLogisticsTrackingCard" title="详细信息">
        <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                     :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        </DynamicForm>
      </XdoCard>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'orderManagementEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        cmbSource: {},
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.daiKinOrderManagement.orderManagement.head.insert,
          update: csAPI.daiKinOrderManagement.orderManagement.head.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded() {
        let me = this
        me.$set(me.detailConfig, 'model', me.editConfig.editData)
      },
      getFields() {
        return [{
          required: true,
          props: {
            maxLength: 50,
            disabled: true
          },
          title: '订单管理号',
          key: 'manageOrderNo'
        }, {
          title: '客户',
          type: 'select',
          required: true,
          props: {
            disabled: true
          },
          key: 'customerCode'
        }, {
          title: '接单日',
          props: {
            disabled: true
          },
          key: 'receiveDate',
          type: 'datePicker'
        }, {
          key: 'type',
          title: '类别',
          required: true,
          props: {
            disabled: true
          },
        }, {
          title: '目的地',
          props: {
            maxLength: 50
          },
          key: 'destName'
        }, {
          props: {
            maxLength: 50
          },
          title: '运输方式',
          key: 'trafModeName'
        }]
      },
      handleSave() {
        let me = this,
          formData = deepClone(me.detailConfig.model)
        me.$refs[me.formName].validate().then(isValid => {
          if (isValid) {
            me.setBtnSaveLoading('save', true)
            me.$http.put(me.ajaxUrl.update + '/' + formData.sid, formData).then(res => {
              me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
              me.$Message.success('保存成功!')
            }).catch(() => {
            }).finally(() => {
              me.setBtnSaveLoading('save', false)
            })
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
