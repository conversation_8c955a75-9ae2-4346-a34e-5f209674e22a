<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="90"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'paymentConditionEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        cmbSource: {
          customerCode: [],
          settlementYear: [{
            value: '1', label: '月结30天'
          }, {
            value: '2', label: '月结60天'
          }, {
            value: '3', label: '月结90天'
          }, {
            value: '4', label: '月结120天'
          }, {
            value: '6', label: '月结180天'
          }],
          settlementDate: [{
            value: '1', label: '1'
          }, {
            value: '2', label: '2'
          }, {
            value: '3', label: '3'
          }, {
            value: '4', label: '4'
          }, {
            value: '5', label: '5'
          }, {
            value: '6', label: '6'
          }, {
            value: '7', label: '7'
          }, {
            value: '8', label: '8'
          }, {
            value: '9', label: '9'
          }, {
            value: '10', label: '10'
          }, {
            value: '11', label: '11'
          }, {
            value: '12', label: '12'
          }, {
            value: '13', label: '13'
          }, {
            value: '14', label: '14'
          }, {
            value: '15', label: '15'
          }, {
            value: '16', label: '16'
          }, {
            value: '17', label: '17'
          }, {
            value: '18', label: '18'
          }, {
            value: '19', label: '19'
          }, {
            value: '20', label: '20'
          }, {
            value: '21', label: '21'
          }, {
            value: '22', label: '22'
          }, {
            value: '23', label: '23'
          }, {
            value: '24', label: '24'
          }, {
            value: '25', label: '25'
          }, {
            value: '26', label: '26'
          }, {
            value: '27', label: '27'
          }, {
            value: '28', label: '28'
          }, {
            value: '29', label: '29'
          }, {
            value: '30', label: '30'
          }, {
            value: '31', label: '31'
          }]
        },
        ajaxUrl: {
          insert: csAPI.receiptPaymentManagement.paymentCondition.insert,
          update: csAPI.receiptPaymentManagement.paymentCondition.update
        }
      }
    },
    created: function () {
      let me = this
      // 客户
      me.$http.post(csAPI.ieParams.CLI).then(res => {
        me.$set(me.cmbSource, 'customerCode', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'customerCode', [])
      }).finally(() => {
        let field = me.detailConfig.fields.find(p => p.key === 'customerCode')
        if (field) {
          me.fieldOptimization(field)
        }
      })
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      onCustomerCodeChange() {
        let me = this,
          customerCode = me.detailConfig.model['customerCode'],
          item = me.cmbSource.customerCode.find(x => x.value === customerCode)
        if (item) {
          me.$set(me.detailConfig.model, 'companyName', item.label)
        } else {
          me.$set(me.detailConfig.model, 'companyName', '')
        }
      },
      getFields() {
        let me = this
        return [{
          required: true,
          type: 'select',
          title: '客户代码',
          key: 'customerCode',
          on: {
            change: me.onCustomerCodeChange
          }
        }, {
          props: {
            disabled: true
          },
          title: '客户名称',
          key: 'companyName'
        }, {
          type: 'select',
          title: '结算月份',
          key: 'settlementYear',
          props: {
            optionLabelRender: (opt) => opt.label
          }
        }, {
          type: 'select',
          title: '结算日期',
          key: 'settlementDate',
          props: {
            optionLabelRender: (opt) => opt.label
          }
          // }, {
          //   type: 'select',
          //   title: '境外收货人',
          //   key: 'overseasShipper'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxLength: 255
          },
          itemClass: 'dc-merge-2-4'
        }]
      },
      handleSave() {
        let me = this,
          overseasShipper = me.detailConfig.model['overseasShipper']
        if (isNullOrEmpty(overseasShipper)) {
          me.$set(me.detailConfig.model, 'overseasShipperName', '')
        } else {
          let item = me.dynamicSource.overseasShipper.find(x => x.value === overseasShipper)
          if (item) {
            me.$set(me.detailConfig.model, 'overseasShipperName', item.label)
          } else {
            me.$set(me.detailConfig.model, 'overseasShipperName', '')
          }
        }
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
