import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const collectionReportList = {
  name: 'collectionReportList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        overseasShipper: []
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        title: '发票号',
        key: 'invoiceNo'
      }, {
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        range: true,
        title: 'ATD/客户验收日',
        key: 'atdAcceptanceDate'
      }, {
        type: 'select',
        title: '境外收货人',
        key: 'overseasShipper'
      }, {
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        key: 'facExgNo',
        title: '成品料号'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 120,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 120,
        title: '制单日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 156,
        title: '境外收货人',
        key: 'overseasShipper',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.overseasShipper)
        }, true)
      }, {
        width: 120,
        title: 'ATD/客户验收日',
        key: 'atdAcceptanceDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '入金截止日',
        key: 'depositDeadlineDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '开票申请日',
        key: 'lnvApplyDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '实际入金日',
        key: 'actualDepositDate',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 120,
        title: '客户订单号',
        key: 'customerOrderNo'
      }, {
        width: 120,
        key: 'facExgNo',
        title: '成品料号'
      }, {
        width: 136,
        key: 'qty',
        title: '数量'
      }, {
        width: 136,
        key: 'price',
        title: '单价'
      }, {
        width: 136,
        title: '总价',
        key: 'totalPrice'
      }, {
        width: 150,
        key: 'curr',
        title: '币制',
        cellRendererFramework: me.baseCellRenderer(function(h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
