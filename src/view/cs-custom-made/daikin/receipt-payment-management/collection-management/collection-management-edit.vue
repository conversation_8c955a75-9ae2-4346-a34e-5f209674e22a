<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'collectionManagementEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        cmbSource: {},
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.receiptPaymentManagement.collectionManagement.head.insert,
          update: csAPI.receiptPaymentManagement.collectionManagement.head.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded() {
        let me = this
        me.$set(me.detailConfig, 'model', me.editConfig.editData)
      },
      getFields() {
        return [{
          title: '发票号',
          props: {
            disabled: true
          },
          key: 'invoiceNo'
        }, {
          props: {
            disabled: true
          },
          key: 'emsListNo',
          title: '单据内部编号'
        }, {
          props: {
            disabled: true
          },
          title: '制单日期',
          key: 'insertTime',
          type: 'datePicker'
        }, {
          type: 'select',
          props: {
            disabled: true
          },
          title: '境外收货人',
          key: 'overseasShipper'
        }, {
          props: {
            disabled: true
          },
          type: 'datePicker',
          title: 'ATD/客户验收日',
          key: 'atdAcceptanceDate'
        }, {
          props: {
            disabled: true
          },
          title: '入金截止日',
          type: 'datePicker',
          key: 'depositDeadlineDate'
        }, {
          title: '开票申请日',
          type: 'datePicker',
          key: 'lnvApplyDate'
        }, {
          title: '实际入金日',
          type: 'datePicker',
          key: 'actualDepositDate'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxLength: 255
          }
        }]
      },
      handleSave() {
        let me = this,
          formData = deepClone(me.detailConfig.model)
        me.setBtnSaveLoading('save', true)
        me.$http.post(me.ajaxUrl.insert, formData).then(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          me.$Message.success('保存成功!')
        }).catch(() => {
        }).finally(() => {
          me.setBtnSaveLoading('save', false)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
