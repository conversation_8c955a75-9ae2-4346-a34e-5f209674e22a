<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <collectionManagementEdit ref="head" :edit-config="editConfig" :in-source="inSource"
                                  @onEditBack="editBack"></collectionManagementEdit>
      </TabPane>
      <TabPane name="bodyTab" label="表体" v-if="showBody">
        <collectionManagementList :parent-config="parentConfig"></collectionManagementList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import { collectionManagementTabs } from './js/collectionManagementTabs'

  export default {
    name: 'collectionManagementTabs',
    mixins: [collectionManagementTabs]
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
