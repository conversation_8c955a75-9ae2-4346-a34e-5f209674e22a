import orderLevelEdit from '../order-level-edit'
import { commitmentManagementList } from '../../comm/js/commitmentManagementList'

export const orderLevelList = {
  name: 'orderLevelList',
  mixins: [commitmentManagementList],
  components: {
    orderLevelEdit
  },
  data() {
    return {
      commitmentsLevel: '2',
      pmsLevel: 'orderLevel',
      toolbarEventMap: {
        'add': this.handleAdd
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'orderNo',
        title: '订单号'
      }, {
        key: 'supplierCode',
        title: '供应商代码及名称'
      }, {
        type: 'select',
        title: '特许权使用费确认',
        key: 'confirmRoyalties'
      }, {
        type: 'select',
        title: '特殊关系确认',
        key: 'confirmSpecial'
      }, {
        type: 'select',
        title: '价格影响确认',
        key: 'confirmPrice'
      }, {
        type: 'select',
        title: '公式定价确认',
        key: 'confirmFormulaPrice'
      }, {
        type: 'select',
        title: '暂定价格确认',
        key: 'confirmTempPrice'
      }]
    },
    /**
     * 列表字段
     * @returns {({cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        key: 'orderNo',
        title: '订单号'
      }, {
        width: 120,
        title: '供应商代码',
        key: 'supplierCode'
      }, {
        width: 168,
        title: '供应商名称',
        key: 'supplierName'
      }, {
        width: 156,
        title: '特许权使用费确认',
        key: 'confirmRoyalties',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmRoyalties)
        })
      }, {
        width: 136,
        title: '特殊关系确认',
        key: 'confirmSpecial',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmSpecial)
        })
      }, {
        width: 136,
        key: 'confirmPrice',
        title: '价格影响确认',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmPrice)
        })
      }, {
        width: 136,
        title: '公式定价确认',
        key: 'confirmFormulaPrice',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmFormulaPrice)
        })
      }, {
        width: 136,
        title: '暂定价格确认',
        key: 'confirmTempPrice',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmTempPrice)
        })
      }, {
        width: 120,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    }
  }
}
