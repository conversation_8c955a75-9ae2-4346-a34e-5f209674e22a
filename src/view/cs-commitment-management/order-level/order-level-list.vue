<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" labelWidth="136">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <orderLevelEdit v-if="!showList" :edit-config="editConfig" :in-source="cmbSource"
                    @onEditBack="editBack"></orderLevelEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <ImportPage :importKey="importKey" :importShow.sync="importShow" :importConfig="importConfig"
                @onImportSuccess="onAfterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { orderLevelList } from './js/orderLevelList'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'

  export default {
    name: 'orderLevelList',
    mixins: [orderLevelList, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('ORDER_LEVEL')
      return {
        listConfig: {
          exportTitle: '订单级别'
        },
        // 导入
        importKey: 'orderLevel',
        importConfig: importConfig,
        ajaxUrl: {
          deleteUrl: csAPI.commitmentManagement.orderLevel.delete,
          selectAllPaged: csAPI.commitmentManagement.orderLevel.selectAllPaged
        }
      }
    },
    created: function () {
      let me = this
      me.$http.post(csAPI.ieParams.PRD).then(res => {
        me.$set(me.cmbSource, 'supplierCode', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'supplierCode', [])
      })
    },
    methods: {
      actionLoaded() {
        let me = this
        me.actions.push({
          ...me.actionsComm,
          label: '新增',
          command: 'add',
          icon: 'ios-add',
          key: 'xdo-btn-add'
        }, {
          ...me.actionsComm,
          label: '编辑',
          command: 'edit',
          key: 'xdo-btn-edit',
          icon: 'ios-create-outline'
        }, {
          ...me.actionsComm,
          label: '删除',
          command: 'delete',
          key: 'xdo-btn-delete',
          icon: 'ios-trash-outline'
        }, {
          ...me.actionsComm,
          label: '导入',
          command: 'import',
          key: 'xdo-btn-upload',
          icon: 'ios-cloud-upload-outline'
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
