import itemLevelEdit from '../item-level-edit'
import { commitmentManagementList } from '../../comm/js/commitmentManagementList'

export const itemLevelList = {
  name: 'itemLevelList',
  mixins: [commitmentManagementList],
  components: {
    itemLevelEdit
  },
  data() {
    return {
      pmsLevel: 'itemLevel',
      commitmentsLevel: '3'
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {({title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string}|{type: string, title: string, key: string})[]}
     */
    getParams() {
      return [{
        key: 'facGNo',
        title: '企业料件料号'
      }, {
        type: 'select',
        title: '特许权使用费确认',
        key: 'confirmRoyalties'
      }, {
        type: 'select',
        title: '特殊关系确认',
        key: 'confirmSpecial'
      }, {
        type: 'select',
        title: '价格影响确认',
        key: 'confirmPrice'
      }, {
        type: 'select',
        title: '公式定价确认',
        key: 'confirmFormulaPrice'
      }, {
        type: 'select',
        title: '暂定价格确认',
        key: 'confirmTempPrice'
      }]
    },
    /**
     * 列表字段
     * @returns {({width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 120,
        key: 'facGNo',
        title: '企业料件料号'
      }, {
        width: 156,
        title: '特许权使用费确认',
        key: 'confirmRoyalties',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmRoyalties)
        })
      }, {
        width: 136,
        title: '特殊关系确认',
        key: 'confirmSpecial',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmSpecial)
        })
      }, {
        width: 136,
        key: 'confirmPrice',
        title: '价格影响确认',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmPrice)
        })
      }, {
        width: 136,
        title: '公式定价确认',
        key: 'confirmFormulaPrice',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmFormulaPrice)
        })
      }, {
        width: 136,
        title: '暂定价格确认',
        key: 'confirmTempPrice',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.confirmTempPrice)
        })
      }, {
        width: 120,
        key: 'note',
        title: '备注',
        cellRendererFramework: me.baseCellRenderer(null, true)
      }]
    }
  }
}
