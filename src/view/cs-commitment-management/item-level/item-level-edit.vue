<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="136"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'itemLevelEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.commitmentManagement.itemLevel.insert,
          update: csAPI.commitmentManagement.itemLevel.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      getFields() {
        return [{
          isCard: true,
          title: '详细信息',
          key: '12121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'facGNo',
          props: {
            maxlength: 50
          },
          title: '企业料件料号'
        }, {
          type: 'select',
          title: '特许权使用费确认',
          key: 'confirmRoyalties'
        }, {
          type: 'select',
          title: '特殊关系确认',
          key: 'confirmSpecial'
        }, {
          type: 'select',
          key: 'confirmPrice',
          title: '价格影响确认'
        }, {
          type: 'select',
          title: '公式定价确认',
          key: 'confirmFormulaPrice'
        }, {
          type: 'select',
          title: '暂定价格确认',
          key: 'confirmTempPrice'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 255
          },
          itemClass: 'dc-merge-1-4'
        }]
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'commitmentsLevel', '3')
        me.doSave()
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
