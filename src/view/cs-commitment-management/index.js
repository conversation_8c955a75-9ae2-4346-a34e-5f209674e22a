/***
 * 关务-价格说明管理-路由
 */
import { namespace } from '@/project'
import itemLevelList from './item-level/item-level-list'
import orderLevelList from './order-level/order-level-list'
import supplierLevelList from './supplier-level/supplier-level-list'

export default [
  {
    path: '/' + namespace + '/commitmentManagement/supplierLevelList',
    name: 'supplierLevelList',
    meta: {
      title: '供应商级别'
    },
    component: supplierLevelList
  },
  {
    path: '/' + namespace + '/commitmentManagement/orderLevelList',
    name: 'orderLevelList',
    meta: {
      title: '订单级别'
    },
    component: orderLevelList
  },
  {
    path: '/' + namespace + '/commitmentManagement/itemLevelList',
    name: 'itemLevelList',
    meta: {
      title: '料号级别'
    },
    component: itemLevelList
  }
]
