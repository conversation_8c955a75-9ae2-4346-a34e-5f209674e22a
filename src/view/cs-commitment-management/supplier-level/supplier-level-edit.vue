<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="136"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'

  export default {
    name: 'supplierLevelEdit',
    mixins: [baseDetailConfig],
    data() {
      return {
        formName: 'frmData',
        ajaxUrl: {
          insert: csAPI.commitmentManagement.supplierLevel.insert,
          update: csAPI.commitmentManagement.supplierLevel.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          me.buttons[me.buttons.findIndex(btn => btn.key === 'save')].needed = !me.showDisable
        }
      }
    },
    methods: {
      /**
       * 供应商值变更事件
       */
      handleSupplierCodeChanged() {
        let me = this,
          supplierCode = me.detailConfig.model['supplierCode'],
          item = me.inSource.supplierCode.find(x => x.value === supplierCode)
        if (item) {
          me.$set(me.detailConfig.model, 'supplierName', item.label)
        } else {
          me.$set(me.detailConfig.model, 'supplierName', '')
        }
      },
      getFields() {
        let me = this
        return [{
          isCard: true,
          title: '详细信息',
          key: '12121212121212',
          type: 'empty_formItem',
          itemClass: 'dc-merge-1-4'
        }, {
          key: 'facGNo',
          props: {
            maxlength: 50
          },
          title: '企业料件料号'
        }, {
          type: 'select',
          title: '供应商代码',
          key: 'supplierCode',
          on: {
            change: me.handleSupplierCodeChanged
          }
        }, {
          props: {
            disabled: true
          },
          title: '供应商名称',
          key: 'supplierName'
        }, {
          type: 'select',
          title: '特许权使用费确认',
          key: 'confirmRoyalties'
        }, {
          type: 'select',
          title: '特殊关系确认',
          key: 'confirmSpecial'
        }, {
          type: 'select',
          key: 'confirmPrice',
          title: '价格影响确认'
        }, {
          type: 'select',
          title: '公式定价确认',
          key: 'confirmFormulaPrice'
        }, {
          type: 'select',
          title: '暂定价格确认',
          key: 'confirmTempPrice'
        }, {
          key: 'note',
          title: '备注',
          props: {
            maxlength: 50
          }
        }]
      },
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'commitmentsLevel', '1')
        if (!isNullOrEmpty(me.detailConfig.model.supplierCode)) {
          let currItem = me.inSource.supplierCode.find(x => x.value === me.detailConfig.model.supplierCode)
          if (currItem) {
            me.$set(me.detailConfig.model, 'supplierName', currItem.label)
          } else {
            me.$set(me.detailConfig.model, 'supplierName', '')
          }
        } else {
          me.$set(me.detailConfig.model, 'supplierName', '')
        }
        me.doSave()
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
