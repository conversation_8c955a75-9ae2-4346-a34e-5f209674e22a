<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="status" label="物流状态">
        <xdo-select v-model="searchParam.goodsStatus" :options="this.cmbDataSource.statusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNoTo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNoTo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="wrapType" label="包装种类">
        <xdo-select v-model="searchParam.wrapType" :asyncOptions="pcodeList" :meta="pcode.wrap" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="到货日" @onDateRangeChanged="handleArrivalDateChange"></dc-dateRange>
      <XdoFormItem prop="carrier" label="承运人">
        <XdoIInput type="text" v-model="searchParam.carrier"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="transPort" label="转入口岸">
        <xdo-select v-model="searchParam.transPort" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="transNo" label="转关单号">
        <XdoIInput type="text" v-model="searchParam.transNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="到港日" @onDateRangeChanged="handleArrivalPortDateChange"></dc-dateRange>
      <XdoFormItem prop="despPort" label="启运港">
        <xdo-select v-model="searchParam.despPort" :asyncOptions="pcodeList" :meta="pcode.port_lin" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="damageMark" label="有无破损">
        <xdo-select v-model="searchParam.damageMark" :options="this.cmbDataSource.damageMarkList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="dispatch" label="是否急件">
        <xdo-select v-model="searchParam.dispatch" :options="cmbDataSource.dispatch" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tailoredTaxi" label="是否专车">
        <xdo-select v-model="searchParam.tailoredTaxi" :options="cmbDataSource.tailoredTaxi" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handHeadInsertTimeChange"></dc-dateRange>
      <XdoFormItem prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { importExportManage } from '../../cs-common'

  export default {
    name: 'DecILogisticsTrackHeadSearch',
    data() {
      return {
        searchParam: {
          goodsStatus: '',
          emsListNoTo: '',
          invoiceNo: '',
          wrapType: '',
          arrivalDateFrom: '',
          arrivalDateTo: '',
          carrier: '',
          transPort: '',
          transNo: '',
          arrivalPortDateFrom: '',
          arrivalPortDateTo: '',
          despPort: '',
          damageMark: '',
          hawb: '',
          contrNo: '',
          dispatch: '',
          tailoredTaxi: '',
          batchNo: ''
        },
        cmbDataSource: {
          dispatch: [{
            value: '0', label: '否'
          }, {
            value: '1', label: '是'
          }],
          tailoredTaxi: [{
            value: '0', label: '否'
          }, {
            value: '1', label: '是'
          }],
          statusList: importExportManage.statusMap,
          damageMarkList: importExportManage.damageMarkMap
        }
      }
    },
    methods: {
      handleArrivalDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "arrivalDateFrom", values[0])
          this.$set(this.searchParam, "arrivalDateTo", values[1])
        } else {
          this.$set(this.searchParam, "arrivalDateFrom", '')
          this.$set(this.searchParam, "arrivalDateTo", '')
        }
      },
      handleArrivalPortDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "arrivalPortDateFrom", values[0])
          this.$set(this.searchParam, "arrivalPortDateTo", values[1])
        } else {
          this.$set(this.searchParam, "arrivalPortDateFrom", '')
          this.$set(this.searchParam, "arrivalPortDateTo", '')
        }
      },
      handHeadInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "headInsertTimeFrom", values[0])
          this.$set(this.searchParam, "headInsertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "headInsertTimeFrom", '')
          this.$set(this.searchParam, "headInsertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
