import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const columnsConfig = [
  'selection'
  , 'operation'
  , 'goodsStatus'
  , 'insertTime'
  , 'batchNo'
  , 'emsListNo'
  , 'lockStatus'
  , 'sid'
  , 'shipDate'
  , 'despPort'
  , 'carrier'
  , 'headId'
  , 'arrivalPortDate'
  , 'entryPort'
  , 'portContacts'
  , 'transPort'
  , 'reachDate'
  , 'transContacts'
  , 'transNo'
  , 'arrivalDate'
  , 'damageMark'
  , 'damageRemark'
  , 'plateNum'
  , 'deliveryPerson'
  , 'linkManTel'
  , 'trafCost'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'transDate'
  , 'inOutNo'
  , 'invoiceNo'
  , 'trafMode'
  , 'packNum'
  , 'wrapType'
  , 'grossWt'
  , 'hawb'
  , 'contrNo'
  , 'dispatch'
  , 'tailoredTaxi'
  , 'portNucleicAcid'
  , 'portElimination'
  , 'samplingTest'
]

const excelColumnsConfig = [
  'goodsStatusName'
  , 'insertTime'
  , 'batchNo'
  , 'emsListNo'
  , 'lockStatus'
  , 'sid'
  , 'shipDate'
  , 'despPort'
  , 'carrier'
  , 'headId'
  , 'arrivalPortDate'
  , 'entryPort'
  , 'portContacts'
  , 'transPort'
  , 'reachDate'
  , 'transContacts'
  , 'transNo'
  , 'arrivalDate'
  , 'damageMarkName'
  , 'damageRemark'
  , 'plateNum'
  , 'deliveryPerson'
  , 'linkManTel'
  , 'trafCost'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'transDate'
  , 'inOutNo'
  , 'invoiceNo'
  , 'trafMode'
  , 'packNum'
  , 'wrapType'
  , 'grossWt'
  , 'headInsertTime'
  , 'hawb'
  , 'contrNo'
  , 'dispatch'
  , 'tailoredTaxi'
  , 'portNucleicAcid'
  , 'portElimination'
  , 'samplingTest'
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          width: 120,
          title: '物流状态',
          key: 'goodsStatusName'
        },
        {
          width: 138,
          title: '制单日期',
          key: 'headInsertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '批次号',
          key: 'batchNo',
        },
        {
          width: 120,
          tooltip: true,
          title: '状态',
          key: 'lockStatus',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.lockMap)
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'carrier',
          title: '承运人'
        },
        {
          width: 120,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 145,
          key: 'trafMode',
          title: '运输方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          width: 120,
          title: '件数',
          key: 'packNum'
        },
        {
          width: 120,
          tooltip: true,
          key: 'wrapType',
          title: '包装种类',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.wrap)
          }
        },
        {
          width: 120,
          title: '毛重',
          key: 'grossWt'
        },
        {
          width: 130,
          title: '到厂日期',
          key: 'arrivalDate'
        },
        {
          width: 120,
          tooltip: true,
          title: '送货人',
          key: 'deliveryPerson'
        },
        {
          width: 120,
          tooltip: true,
          title: '联系电话',
          key: 'linkManTel'
        },
        {
          width: 120,
          title: '车牌号',
          key: 'plateNum'
        },
        {
          width: 130,
          title: '到达日期',
          key: 'reachDate'
        },
        {
          width: 150,
          title: '转入口岸',
          key: 'transPort',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'transNo',
          title: '转关单号'
        },
        {
          width: 130,
          title: '到港日期',
          key: 'arrivalPortDate'
        },
        {
          width: 130,
          key: 'shipDate',
          title: '发货日期'
        },
        {
          width: 150,
          key: 'hawb',
          title: '提运单号'
        },
        {
          width: 168,
          tooltip: true,
          title: '启运港',
          key: 'despPort',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.port_lin)
          }
        },
        {
          width: 120,
          title: '有无破损',
          key: 'damageMarkName'
        },
        {
          width: 120,
          tooltip: true,
          title: '破损说明',
          key: 'damageRemark'
        },
        {
          width: 120,
          tooltip: true,
          key: 'contrNo',
          title: '合同协议号'
        },
        {
          width: 88,
          tooltip: true,
          key: 'dispatch',
          title: '是否急件',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.dispatch)
          }
        },
        {
          width: 98,
          tooltip: true,
          title: '是否专车',
          key: 'tailoredTaxi',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.tailoredTaxi)
          }
        },
/*        {
          width: 98,
          tooltip: true,
          title: '口岸核酸',
          key: 'portNucleicAcid',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.tailoredTaxi)
          }
        },
        {
          width: 98,
          tooltip: true,
          title: '口岸消杀',
          key: 'portElimination',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.tailoredTaxi)
          }
        },*/
        {
          width: 98,
          tooltip: true,
          title: '海关查验',
          key: 'samplingTest',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.tailoredTaxi)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
