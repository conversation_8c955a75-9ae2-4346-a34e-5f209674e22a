import { getKeyValue, isNullOrEmpty } from '@/libs/util'
import {  baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'
import {baseColumnsExport} from "@/view/cs-knorr/common/comm/baseColumns";

// 通用列
const commColumns = [
  'insertTime'
  , 'complete'
  , 'emsListNo'
  , 'entrustDate'
  , 'entrustPerson'
  , 'beEntrustPerson'
  , 'declareDate'
  , 'declarePort'
  , 'emsNo'
  , 'entryNo'
  , 'checkDate'
  , 'checkReason'
  , 'completeCheckDate'
  , 'checkResult'
  , 'entourage'
  , 'customsCheckDate'
  , 'customsCheckReason'
  , 'customsCompleteCheckDate'
  , 'customsCheckResult'
  , 'customsEntourage'
  , 'dutyDate'
  , 'paymentDate'
  , 'noticeDate'
  , 'dutyType'
  , 'dutyTotal'
  , 'dutyPrice'
  , 'dutyInterest'
  , 'taxPrice'
  , 'taxInterest'
  , 'passDate'
  , 'deliveryDate'
  , 'declarePerson'
  , 'checkFile'
  , 'otherPrice'
  , 'updateUser'
  , 'updateTime'
  , 'tradeCode'
  , 'headId'
  , 'headInsertTime'
  , 'customsCheckFile'
  , 'checkPerson'
  , 'customsCheckPerson'
  , 'erpHeadId'
  , 'note1'
  , 'note2'
  , 'note4'
  , 'tradeMode'
  , 'trafMode'
  , 'emsNo'
  , 'hawb'
  , 'declareCode'
  , 'declareCodeCustoms'
  , 'contrNo'
  , 'entryDeclareDate'
  , 'taxTotal'
  , 'exciseInterest'
  , 'purchaseConfirm'
  , 'financeConfirm'
  , 'financeNo'
  , 'invoiceNo'
  , 'inspectionNo'
]

const columnsConfig = [
  // ...baseColumnsShow
   ...commColumns
  , 'goodsStatus'
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
  , 'goodsStatusName'
]

const columns = {
  mixins: [baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          title: '状态',
          width: 80,
          align: 'center',
          key: 'goodsStatus',
          render: (h, params) => {
            let toolTip = this.getCustomTrackingTimeLine(params.row)
            return this.cmbShowRender(h, params, this.cmbDataSource.statusList, '', toolTip)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '状态',
          key: 'goodsStatusName'
        },
        {
          width: 80,
          key: 'complete',
          title: '报关达成',
          render: (h, params) => {
            let showColor = '',
              cmbVal = params.row[params.column.key]
            if (cmbVal === '2') {
              showColor = 'red'
            } else if (cmbVal === '3') {
              showColor = 'green'
            }
            if (isNullOrEmpty(cmbVal)) {
              cmbVal = ''
            } else {
              cmbVal = getKeyValue(this.cmbDataSource.complete, cmbVal)
            }
            return h('span', {
              style: {
                color: showColor
              }
            }, cmbVal)
          }
        },
        {
          title: '制单日期',
          width: 130,
          align: 'center',
          key: 'headInsertTime'
        },
        {
          title: '备案号',
          width: 120,
          align: 'center',
          key: 'emsNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '提运单号',
          key: 'hawb',
          width: 150,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '报关单号',
          width: 200,
          align: 'center',
          key: 'entryNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '清单内部编号',
          width: 180,
          align: 'center',
          key: 'emsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '监管方式',
          width: 120,
          align: 'center',
          key: 'tradeMode',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '委托日期',
          width: 120,
          align: 'center',
          key: 'entrustDate'
        },
        {
          title: '受托人',
          width: 120,
          align: 'center',
          key: 'beEntrustPerson',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单申报日期',
          width: 120,
          align: 'center',
          key: 'entryDeclareDate'
        },
        {
          title: '申报口岸',
          width: 120,
          align: 'center',
          key: 'declarePort',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
          }
        },
        {
          title: '运输方式',
          width: 120,
          align: 'center',
          key: 'trafMode',
          ellipsis: true,
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          title: '放行日期',
          width: 120,
          align: 'center',
          key: 'passDate'
        },
        {
          title: '到厂日期',
          width: 120,
          align: 'center',
          key: 'deliveryDate'
        },
        {
          title: '商检查验日期',
          width: 120,
          align: 'center',
          key: 'checkDate'
        },
        {
          title: '海关查验日期',
          width: 120,
          align: 'center',
          key: 'customsCheckDate'
        },
        {
          title: '完税日期',
          width: 120,
          align: 'center',
          key: 'dutyDate'
        },
        {
          title: '税金请款日期',
          width: 120,
          align: 'center',
          key: 'paymentDate'
        },
        {
          title: '到货通知日期',
          width: 120,
          align: 'center',
          key: 'noticeDate'
        },
        {
          title: '缴税方式',
          width: 120,
          align: 'center',
          key: 'dutyType',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.cmbDataSource.dutyTypeList)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '完税总价',
          width: 120,
          align: 'center',
          key: 'dutyTotal',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '关税',
          width: 120,
          align: 'center',
          key: 'dutyPrice',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '关税缓税利息',
          width: 120,
          align: 'center',
          key: 'dutyInterest',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '增值税',
          width: 120,
          align: 'center',
          key: 'taxPrice',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '增值税缓税利息',
          width: 120,
          align: 'center',
          key: 'taxInterest',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '其他',
          width: 120,
          align: 'center',
          key: 'otherPrice',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '总税款',
          width: 120,
          align: 'center',
          key: 'taxTotal',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '委托人',
          width: 120,
          align: 'center',
          key: 'entrustPerson',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关行十位编号',
          width: 120,
          align: 'center',
          key: 'declareCode',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关行',
          width: 160,
          align: 'center',
          key: 'declareCodeCustoms',
          // render: (h, params) => {
          //   return this.cmbShowRender(h, params, this.cmbDataSource.cutData)
          // },
          ellipsis: true,
          tooltip: true
        },
        {
          title: '合同协议号',
          width: 120,
          align: 'center',
          key: 'contrNo',
          ellipsis: true,
          tooltip: true,
        },
        {
          width: 136,
          align: 'center',
          title: '消费税缓息',
          key: 'exciseInterest'
        },
        {
          width: 88,
          title: '采购确认',
          key: 'purchaseConfirm',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [{value: '0', label: '否'}, {value: '1', label: '是'}])
          }
        },
        {
          width: 88,
          title: '财务确认',
          key: 'financeConfirm',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [{value: '0', label: '否'}, {value: '1', label: '是'}])
          }
        },
        {
          width: 88,
          key: 'financeNo',
          title: '财务单据号'
        }, {
          width: 88,
          title: '发票号',
          key: 'invoiceNo'
        }, {
          width: 88,
          title: '报检单号',
          key: 'inspectionNo'
        }
      ],
      modifyExportColumns: [{
        key: 'emsListNo',
        value: '清单内部编号'
      }, {
        value: '到港日期',
        key: 'arrivalPortDate'
      }, {
        value: '报关单申报日期',
        key: 'entryDeclareDate'
      }, {
        key: 'entryNo',
        value: '报关单号'
      }, {
        value: '报检单号',
        key: 'inspectionNo'
      }, {
        key: 'checkDate',
        value: '商检查验日期'
      }, {
        key: 'checkReason',
        value: '商检查验原因'
      }, {
        value: '商检查验完成日期',
        key: 'completeCheckDate'
      }, {
        key: 'checkResult',
        value: '商检查验结果'
      }, {
        value: '海关查验日期',
        key: 'customsCheckDate'
      }, {
        value: '海关查验原因',
        key: 'customsCheckReason'
      }, {
        value: '海关查验完成日期',
        key: 'customsCompleteCheckDate'
      }, {
        value: '海关查验结果',
        key: 'customsCheckResult'
      }, {
        key: 'dutyType',
        value: '缴税方式'
      }, {
        value: '关税',
        key: 'dutyPrice'
      }, {
        value: '增值税',
        key: 'taxPrice'
      }, {
        value: '附加税',
        key: 'addTaxPrice'
      }, {
        value: '总税款',
        key: 'taxTotal'
      }, {
        key: 'passDate',
        value: '放行日期'
      }, {
        key: 'notePass',
        value: '放行备注'
      }, {
        key: 'noticeDate',
        value: '到货通知日期'
      }, {
        value: '预计到货日期',
        key: 'planArrivalDate'
      }, {
        value: '车牌号',
        key: 'plateNum'
      }, {
        value: '送货人',
        key: 'deliveryPerson'
      }, {
        value: '联系电话',
        key: 'linkManTel'
      }, {
        value: '送货单号',
        key: 'deliveryNo'
      }, {
        value: '到厂日期',
        key: 'deliveryDate'
      }, {
        key: 'inOutNo',
        value: '入库单据号'
      }, {
        value: '破损标记',
        key: 'damageMark'
      }, {
        value: '物流费用',
        key: 'customsFee'
      }, {
        value: '报关费用',
        key: 'logisticsFee'
      }]
    }
  }
}
export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
