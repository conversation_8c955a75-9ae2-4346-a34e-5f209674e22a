<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false">
      <div ref="area_form-title">
        <div class="form-title-wrapper">
          <p><span>清单编号</span></p>
        </div>
        <div class="form-title-body-wrapper">
          <RadioGroup type="button" @on-change="handleChangeBill" v-model="currentBillNo">
            <Radio :label="item.emsListNo" v-for="item in customsTrackList" :key="item.sid">{{item.emsListNo}}</Radio>
          </RadioGroup>
        </div>
      </div>
    </XdoCard>
    <XdoForm ref="headerEditFrom" :model="headerData" :rules="rulesHeader" label-position="right" :label-width="120">
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>委托</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="entrustDate" label="委托日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.entrustDate" :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="entrustPerson" label="委托人">
            <XdoIInput type="text" v-model="headerData.entrustPerson" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="beEntrustPerson" label="受托人">
            <XdoIInput type="text" v-model="headerData.beEntrustPerson" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>申报</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="declareDate" label="申报日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.declareDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="declarePort" label="申报口岸">
            <xdo-select v-model="headerData.declarePort" :disabled="showDisable" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="entryNo" label="报关单号">
            <XdoIInput type="text" v-model="headerData.entryNo" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>商检查验</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="checkDate" label="查验日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.checkDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="checkReason" label="查验原因">
            <XdoIInput type="text" v-model="headerData.checkReason" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="completeCheckDate" label="查验完成日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.completeCheckDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="checkResult" label="查验结果">
            <XdoIInput type="text" v-model="headerData.checkResult" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="entourage" label="陪同人员">
            <XdoIInput type="text" v-model="headerData.entourage" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <SimpleAttachment label="查验单据" v-show="showUpload" @uploadStatus="uploadStatus"
                            :business-type="attachTypeSJCY" :business-sid="headerData.sid"
                            :aeo-show="aeoShow"></SimpleAttachment>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>海关查验</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="customsCheckDate" label="查验日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.customsCheckDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="customsCheckReason" label="查验原因">
            <XdoIInput type="text" v-model="headerData.customsCheckReason" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="customsCompleteCheckDate" label="查验完成日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.customsCompleteCheckDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="customsCheckResult" label="查验结果">
            <XdoIInput type="text" v-model="headerData.customsCheckResult" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="customsEntourage" label="陪同人员">
            <XdoIInput type="text" v-model="headerData.customsEntourage" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <SimpleAttachment label="查验单据" v-show="showUpload" @uploadStatus="uploadStatus"
                            :business-type="attachTypeHGCY" :business-sid="headerData.sid"
                            :aeo-show="aeoShow"></SimpleAttachment>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>完税</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="dutyDate" label="完税日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.dutyDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="dutyType" label="缴税方式">
            <xdo-select v-model="headerData.dutyType" :options="this.cmbDataSource.dutyTypeList"
                        :optionLabelRender="pcodeRender" :disabled="showDisable"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="dutyTotal" label="完税总价">
            <xdo-input v-model="headerData.dutyTotal" decimal int-length="11" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="dutyPrice" label="关税">
            <xdo-input v-model="headerData.dutyPrice" decimal int-length="11" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="taxPrice" label="增值税">
            <xdo-input v-model="headerData.taxPrice" decimal int-length="11" precision="5" :disabled="showDisable"></xdo-input>
          </XdoFormItem>
          <SimpleAttachment label="税单" v-show="showUpload" @uploadStatus="uploadStatus"
                            :business-type="attachTypeWSSD" :business-sid="headerData.sid"
                            :aeo-show="aeoShow"></SimpleAttachment>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="form-title-wrapper">
          <p><span>放行</span></p>
        </div>
        <div class="form-title-body-wrapper dc-form dc-form3">
          <XdoFormItem prop="passDate" label="放行日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.passDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="deliveryDate" label="到厂日期">
            <XdoDatePicker type="datetime" format="yyyy-MM-dd HH" placeholder="请选择日期" v-model="headerData.deliveryDate" transfer :disabled="showDisable"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="note1" label="报关费用">
            <XdoIInput type="text" v-model="headerData.note1" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="linkedNo" label="入库关联编号">
            <XdoIInput type="text" v-model="headerData.linkedNo" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="financeNo" label="财务单据号">
            <XdoIInput type="text" v-model.trim="headerData.financeNo" placeholder="" :disabled="showDisable" :maxlength="30"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
    <UniversalProgress :show.sync="progress.uploadProgressShow"
                       :completed="progress.uploadCompleted" :failure="progress.uploadFailure"></UniversalProgress>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import UniversalProgress from '@/components/universal-progress/universal-progress'
  import { customsTrackEdit } from '@/view/cs-ie-manage-mixins/entry/customsTrackEdit'

  export default {
    name: 'CustomsTrack',
    mixins: [customsTrackEdit],
    components: {
      UniversalProgress
    },
    data() {
      return {
        progress: {
          uploadFailure: false,
          uploadCompleted: false,
          uploadProgressShow: false
        }
      }
    },
    methods: {
      getDefaultData() {
        return {
          sid: '',
          entrustDate: '',
          entrustPerson: '',
          beEntrustPerson: '',
          declareDate: '',
          declarePort: '',
          entryNo: '',
          checkDate: '',
          checkReason: '',
          completeCheckDate: '',
          checkResult: '',
          entourage: '',
          customsCheckDate: '',
          customsCheckReason: '',
          customsCompleteCheckDate: '',
          customsCheckResult: '',
          customsEntourage: '',
          dutyDate: '',
          dutyType: '',
          dutyTotal: '',
          dutyPrice: '',
          taxPrice: '',
          passDate: '',
          deliveryDate: '',
          note1: '',
          linkedNo: '',
          note4: ''
        }
      },
      loadCustomsTrackListData() {
        let me = this
        me.$http.post(`${csAPI.csImportExport.iBill.selectListByHeadId}/${me.headId}`).then(res => {
          me.customsTrackList = res.data.data
          if (res.data.data.length > 0) {
            me.currentBillNo = res.data.data[0].emsListNo
            me.loadCustomsTrackData(res.data.data[0].sid)
          } else {
            // me.buttons[0].disabled = true
          }
        }).catch(() => {
        })
      },
      loadCustomsTrackData(sid) {
        let me = this
        me.$http.post(`${csAPI.csImportExport.iCustomsTracking.selectBySId}/${sid}`).then(res => {
          me.headerData = res.data.data
        }).catch(() => {
        })
      },
      handleSave() {
        let me = this
        me.$refs['headerEditFrom'].validate().then(isValid => {
          if (isValid) {
            const data = Object.assign({}, me.headerData)
            if (me.initData) {
              const sid = me.headerData.sid
              me.buttons[0].loading = true
              // 保存一条物流追踪单
              me.$http.put(`${csAPI.csImportExport.iCustomsTracking.update}/${sid}`, data).then(() => {
                me.$Message.success('修改成功!')
              }).catch(() => {
              }).finally(() => {
                me.buttons[0].loading = false
              })
            } else {
              // 保存多条物流追踪单
              me.buttons[0].loading = true
              me.$http.post(`${csAPI.csImportExport.iCustomsTracking.updateMult}`, {
                sidList: me.customsTrackList.map(item => {
                  return item.sid
                }),
                paramData: data
              }).then(() => {
                me.$Message.success('修改成功!')
              }).catch(() => {
              }).finally(() => {
                me.buttons[0].loading = false
              })
            }
          }
        })
      },
      /**
       * 文件上传状态变更
       * @param statusObj
       */
      uploadStatus(statusObj) {
        let me = this
        me.$set(me.progress, 'uploadFailure', statusObj.uploadFailure)
        me.$set(me.progress, 'uploadCompleted', statusObj.uploadCompleted)
        me.$set(me.progress, 'uploadProgressShow', statusObj.uploadProgressShow)
      }
    }
  }
</script>

<style scoped>
  .form-title-wrapper {
    line-height: 1px;
    padding: 5px 10px !important;
    background-color: #dcdee2 !important;
  }

  .form-title-wrapper p {
    width: 100%;
    height: 20px;
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
  }

  .form-title-wrapper p span {
    vertical-align: middle;
  }

  .form-title-body-wrapper {
    padding: 8px 8px 2px 8px;
  }

  .form-title-body-wrapper .ivu-form-item {
    margin-bottom: 3px;
  }
</style>
