<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <CustomsTrackSearch ref="headSearch" :formResId="formResId" iemark="I" :showSearch="showSearch"></CustomsTrackSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:batchUpdate>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                    <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:batchSchedule>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>报关达成<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="doSetSchedule('1')">
                    进行中
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="doSetSchedule('2')">
                    延迟
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="doSetSchedule('3')">
                    完成
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" @onAgCellOperation="onAgCellOperation"
                  :height="dynamicHeight" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <customsTrackITabs v-if="!showList" :edit-config="editConfig"
                       @onEditBack="editBack"></customsTrackITabs>
    <SetArrivalNotice :show.sync="popArrivalNoticeShow" @onConfirm="doGenerateArrivalNotification"></SetArrivalNotice>
    <DcImport v-model="batchUpdateShow"
              :startRow="updateConfig.startRow" :config="updateConfig.config" :bizParam="updateConfig.params"
              @importSuccess="afterImport"></DcImport>
    <financeSetPop :show.sync="financeConfig.show" :show-finance-no="showFinanceNo" ie-mark="I" :source-data="financeConfig.source"
                   @doFinance="goToFinance"></financeSetPop>
    <customsSubmitVerifyPop :show.sync="submitVerifyConfig.show" :err-data="submitVerifyConfig.data"
                            @onContinue="onSubmitContinue"></customsSubmitVerifyPop>
    <arrivalNoticeSendPop :show.sync="arrivalNoticeSendShow"
                          @doNoticeDownload="printArrivalNotification" @doMailSend="doMailSend"></arrivalNoticeSendPop>
    <TableColumnSetup v-model="trackESetupShow" :resId="trackId" :columns="trackHeadListColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { importExportManage } from '@/view/cs-common'
  import customsTrackITabs from './customs-track-i-tabs'
  import { formatDate, convertToDate } from '@/libs/datetime'
  import arrivalNoticeSendPop from './arrival-notice-send-pop'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import SetArrivalNotice from '@/view/cs-ie-manage-mixins/entry/set-arrival-notice'
  import { customsTrackList } from '@/view/cs-ie-manage-mixins/entry/customsTrackList'
  import { columnsConfig,excelColumnsConfig, columns } from './decICustomsTrackHeadListColumns'

  export default {
    name: 'DecICustomsTrackHeadList',
    components: {
      SetArrivalNotice,
      customsTrackITabs,
      arrivalNoticeSendPop
    },
    mixins: [customsTrackList, columns],
    data() {
      return {
        iEMark: 'I',
        trackId: '',
        searchLines: 9,
        gridConfig: {
          exportTitle: '进口报关追踪'
        },
        popArrivalNoticeShow: false,
        arrivalNoticeSendShow: false,
        trackESetupShow: false,
        trackHeadListColumns:[],
        cmbDataSource: {
          statusList: importExportManage.entryTrackStateMapI
        },
        toolbarEventMap: {
          'generate-notice': this.generateArrivalNotification,
          'settings': this.handleTableColumnSetup
        },
        ajaxUrl: {
          // 财务确认
          finance: csAPI.csImportExport.iCustomsTracking.finance,
          // 采购确认/取消
          purchase: csAPI.csImportExport.iCustomsTracking.purchase,
          exportUrl: csAPI.csImportExport.iCustomsTracking.exportUrl,
          // 发送邮件
          sendEmail: csAPI.csImportExport.iCustomsTracking.sendEmail,
          batchUpdate: csAPI.csImportExport.iCustomsTracking.batchUpdate,
          // 采购确认/取消校验
          confirmCheck: csAPI.csImportExport.iCustomsTracking.purchaseCheck,
          printArrivalNotice: csAPI.csImportExport.iEntry.printArrivalNotice,
          selectAllPaged: csAPI.csImportExport.iCustomsTracking.selectAllPaged,
          generateNotification: csAPI.csImportExport.iEntry.generateArrivalNotification,
          setCompleteSchedule: csAPI.csImportExport.iCustomsTracking.setCompleteSchedule
        }
      }
    },
    // mounted: function() {
    //   let me = this
    //   me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    // },
    created:function (){
      let me = this
      me.trackId = me.$route.path+'/'+me.$options.name
      me.$set(me,'trackHeadListColumns',getColumnsByConfig(me.totalColumns,columnsConfig))
      let columns = me.$bom3.showTableColumns(me.trackId,me.trackHeadListColumns)
      me.handleUpdateColumn(columns)
    },

    methods: {
      handleTableColumnSetup() {
        this.trackESetupShow = true
      },
      /**
       * 设置列
       * @param columns
       */
      handleUpdateColumn(columns) {
        let me = this
        me.gridConfig.gridColumns = [...me.getDefaultColumns(), ...columns]
        me.gridConfig.exportColumns = columns.map(columns => {
          return {
            key: columns.key,
            value: columns.title
          }
        })
      },
      /**
       * ToolTip自定义渲染
       * @param h
       * @param toolTip
       * @returns {Array}
       */
      getCustomRender(h, toolTip) {
        let spans = []
        for (let row of toolTip.rows) {
          for (let cell of row.cells) {
            spans.push(h('div', {
              style: {
                float: 'left',
                width: cell.width + 'px'
              }
            }, isNullOrEmpty(cell.value) ? '--' : cell.value))
          }
        }
        return spans
      },
      /**
       * 日期时间转换
       * @param row
       * @param datetime
       * @returns {*}
       */
      showDateTime(row, datetime) {
        let theDateTime = ''
        if (typeof datetime === "string") {
          theDateTime = row[datetime]
        } else if (Array.isArray(datetime) && datetime.length === 2) {
          let firstDate = row[datetime[0]]
          let secondDate = row[datetime[1]]
          if (!isNullOrEmpty(firstDate)) {
            if (!isNullOrEmpty(secondDate)) {
              if (typeof firstDate === "string" && firstDate.length === 13) {
                firstDate = firstDate + ':00:00'
              }
              if (typeof secondDate === "string" && secondDate.length === 13) {
                secondDate = secondDate + ':00:00'
              }
              firstDate = convertToDate(firstDate)
              secondDate = convertToDate(secondDate)
              if (firstDate instanceof Date) {
                if (secondDate instanceof Date) {
                  if (firstDate > secondDate) {
                    theDateTime = secondDate
                  } else {
                    theDateTime = firstDate
                  }
                } else {
                  theDateTime = firstDate
                }
              } else if (secondDate instanceof Date) {
                theDateTime = secondDate
              }
            } else {
              theDateTime = firstDate
            }
          } else {
            if (!isNullOrEmpty(secondDate)) {
              theDateTime = secondDate
            }
          }
        }
        if (isNullOrEmpty(theDateTime)) {
          return ''
        }
        if (theDateTime instanceof Date) {
          return formatDate(theDateTime, 'yyyy-MM-dd hh')
        } else {
          let tmpDateTime = convertToDate(theDateTime)
          if (tmpDateTime.toString() === 'Invalid Date') {
            return theDateTime
          } else {
            return formatDate(theDateTime, 'yyyy-MM-dd hh')
          }
        }
      },
      /**
       * 获取自定义ToolTip显示内容
       * @param row
       * @returns {{maxWidth: number, contentHeight: number, rows: *[]}}
       */
      getCustomTrackingTimeLine(row) {
        return {
          maxWidth: 184,
          theme: 'light',
          contentHeight: 159,
          placement: 'right',
          rows: [{
            cells: [{
              value: '到港',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'arrivalPortDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '清单申报',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'billDeclareDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '报关单申报',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'entryDeclareDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '查验',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, ['checkDate', 'customsCheckDate']),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '缴税',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'dutyDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '放行',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'passDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '到货通知',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'noticeDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '实际到货',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'deliveryDate'),
              float: 'right',
              width: 80
            }]
          }, {
            cells: [{
              value: '财务结算',
              float: 'left',
              width: 80
            }, {
              value: this.showDateTime(row, 'payDate'),
              float: 'right',
              width: 80
            }]
          }]
        }
      },
      // /**
      //  * 加载自定义按钮(临时添加)
      //  */
      // loadExtendActions() {
      //   let me = this,
      //     gActions = me.actions.filter(action => {
      //       return action.command === 'generate-notice'
      //     })
      //   if (gActions.length === 0) {
      //     me.actions.push({
      //       needed: true,
      //       loading: false,
      //       disabled: false,
      //       label: '生成到货通知',
      //       key: 'generateNotice',
      //       command: 'generate-notice',
      //       icon: 'ios-cloud-upload-outline'
      //     })
      //   }
      // },
      /**
       * 弹出预警通知人内容
       */
      generateArrivalNotification() {
        let me = this
        if (me.checkRowSelected('生成到货通知')) {
          // me.printArrivalNotification()
          me.$set(me, 'arrivalNoticeSendShow', true)
        }
      },
      /**
       * 生成到货通知
       * @param userNames
       */
      doGenerateArrivalNotification(userNames) {
        let me = this
        me.popArrivalNoticeShow = false
        let sids = me.getSelectedParams()
        me.setToolbarProperty('generate-notice', 'loading', true)
        me.$http.post(me.ajaxUrl.generateNotification + '/' + sids + '/' + userNames).then(() => {
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarProperty('generate-notice', 'loading', false)
        })
      },
      /**
       * 打印到货通知
       */
      printArrivalNotification() {
        let me = this,
          sids = me.getSelectedParams()
        me.$set(me.gridConfig, 'exportTitle', '进口报关追踪--到货通知')
        me.doExport(me.ajaxUrl.printArrivalNotice + '/' + sids, me.actions.findIndex(it => it.command === 'generate-notice'), function() {
          me.$set(me.gridConfig, 'exportTitle', '进口报关追踪')
        })
      },
      /**
       * 下载前操作
       * @param type
       */
      beforeDownload(type) {
        let me = this
        if (type === '0') {
          me.$set(me.gridConfig, 'exportColumns', getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig))
        } else {
          me.$set(me.gridConfig, 'exportColumns', me.modifyExportColumns)
        }
      },
      /**
       * 发送邮件
       */
      doMailSend() {
        let me = this,
          sids = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.sendEmail + '/' + sids).then(() => {
          me.$Message.success('邮件发送成功!')
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .dc-select-selection {
    height: 24px;
    overflow: hidden;
  }

  /deep/ .dc-select-multiple .dc-select-input {
    height: 22px;
  }
</style>
