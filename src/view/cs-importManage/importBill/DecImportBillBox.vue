<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecImportBillBoxSearch ref="headSearch" :showSearch="showSearch"></DecImportBillBoxSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:print-invoice>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="downloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载发票 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBill(0)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBill(1)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:print-box>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="boxDownloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载箱单 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBox(0)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printBox(1)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template v-slot:print-contract>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;" :loading="contractDownloading">
                <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载合同 <XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printContract(0)">
                    下载PDF
                  </XdoButton>&nbsp;
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="printContract(1)">
                    下载Excel
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard>
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <DecImportTabs v-if="!showList" :edit-config="editConfig" :dataList="dataList" :compData="compData"
                   :templatecodelist="templateCodeList" @oneditback="backToList"></DecImportTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI, zipExport } from '@/api'
  import DecImportTabs from './DecImportTabs'
  import { editStatus } from '@/view/cs-common'
  import { getColumnsByConfig } from '@/common'
  import DecImportBillBoxSearch from './DecImportBillBoxSearch'
  import { columnsConfig, columns } from './DecImportBillBoxColumns'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { isNullOrEmpty, ArrayToLocaleLowerCase, getHttpHeaderFileName, blobSaveFile } from '@/libs/util'

  export default {
    name: 'DecImportBillBox',
    components: {
      DecImportTabs,
      DecImportBillBoxSearch
    },
    mixins: [commList, columns, pms],
    data() {
      return {
        dataList: [],
        compData: [],
        downloading: false,
        templateCodeList: [],
        boxDownloading: false,
        contractDownloading: false,
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'lock': this.handleLock,
          'print-box': this.printBox,
          'unlock': this.handleUnLock,
          'delete': this.handleDelete,
          'print-invoice': this.printBill,
          'print-contract': this.printContract
        },
        ajaxUrl: {
          lock: csAPI.importBill.clockbill.clockBill,
          delete: csAPI.importBill.deletebill.deleteBill,
          selectAllPaged: csAPI.importBill.search.searchInfor,
          getTemplateList: csAPI.importBill.templatelist.templateList
        }
      }
    },
    created: function() {
      this.getClientForwarder()
    },
    mounted: function() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.loadFunctions().then()
    },
    methods: {
      /**
       * 获取客户信息 + 货代
       */
      getClientForwarder() {
        let me = this
        me.$http.post(csAPI.importBill.custumer.custumerInfor + `/PRD`).then(res => {
          me.$set(me, 'dataList', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
        me.$http.post(csAPI.importBill.custumer.custumerInfor + `/FOD`).then(res => {
          me.$set(me, 'compData', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
      },
      afterSearch() {
        let me = this
        me.$set(me, 'dataList', [])
        me.$set(me, 'compData', [])
        me.$set(me, 'templateCodeList', [])
      },
      /**
       * 点击编辑按钮执行
       */
      handleEdit() {
        let me = this
        if (me.checkRowSelected('编辑', true)) {
          let selData = me.gridConfig.selectRows[0]
          if (selData.status === '1') {
            me.$Message.warning('锁定数据不可编辑!')
          } else {
            me.$http.post(me.ajaxUrl.getTemplateList, null, {
              params: {
                iemark: 'I',
                customerCode: selData.supplierCode
              }
            }).then(res => {
              me.$set(me, 'templateCodeList', res.data.data)
              me.handleEditByRow(selData)
            }).catch(() => {
            })
          }
        }
      },
      /**
       * 列表中点击数据展示
       * @param row
       */
      handleViewByRow(row) {
        let me = this
        me.$http.post(me.ajaxUrl.getTemplateList, null, {
          params: {
            iemark: 'I',
            customerCode: row.supplierCode
          }
        }).then(res => {
          me.$set(me, 'templateCodeList', res.data.data)
          me.$set(me.editConfig, 'editData', row)
          me.$set(me.editConfig, 'editStatus', editStatus.SHOW)
          me.$set(me, 'showList', false)
        }).catch(() => {
        })
      },
      /**
       * 数据删除
       */
      handleDelete() {
        this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
      },
      /**
       * 锁定
       */
      handleLock() {
        let me = this
        if (me.checkRowSelected('锁定')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认锁定所选项吗',
            onOk: () => {
              me.setToolbarLoading('lock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lock}/1/${params}`).then(() => {
                me.$Message.success('锁定成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('lock', false)
              })
            }
          })
        }
      },
      /**
       * 解锁
       */
      handleUnLock() {
        let me = this
        if (me.checkRowSelected('解锁')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确认',
            cancelText: '取消',
            content: '确认解锁所选项吗',
            onOk: () => {
              me.setToolbarLoading('unlock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lock}/0/${params}`).then(() => {
                me.$Message.success('锁定成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('unlock', false)
              })
            }
          })
        }
      },
      downloadStreamFile(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], { type: `application/pdf` })
        blobSaveFile(blob, filename)
      },
      /**
       * 获取Blob
       * @param base64 base64字符串
       * @param contentType 导出格式 MIME 类型
       * @param sliceSize 分割大小
       * @returns {Blob}
       */
      getBlob(base64, contentType, sliceSize) {
        contentType = contentType || ''
        sliceSize = sliceSize || 512

        let byteCharacters = atob(base64)
        let byteArrays = []

        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          let slice = byteCharacters.slice(offset, offset + sliceSize)

          let byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          let byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        return new Blob(byteArrays, { type: contentType })
      },
      /**
       * 打印发票
       * @param e
       */
      printBill(e) {
        let me = this
        if (e === 0) {
          me.isPDF = true
        } else if (e === 1) {
          me.isPDF = false
        }
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要打印的数据')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('只能选择一条数据打印')
        } else {
          me.downloading = true
          me.$http.post(csAPI.importBill.printbill.printBill, '', {
            params: {
              billType: 1,
              isPdf: me.isPDF,
              headId: me.gridConfig.selectRows[0].sid
            }
          }).then(res => {
            me.$Message.success('打印成功!')
            if (e === 0) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/pdf`)
                blobSaveFile(blob, item.value)
              })
            } else if (e === 1) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/vnd.ms-excel`)
                blobSaveFile(blob, item.value)
              })
            }
          }).catch(() => {
          }).finally(() => {
            me.downloading = false
          })
        }
      },
      /**
       * 打印箱单
       */
      printBox(e) {
        let me = this
        if (e === 0) {
          me.isPDF = true
        } else if (e === 1) {
          me.isPDF = false
        }
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要打印的数据')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('只能选择一条数据打印!')
        } else {
          me.boxDownloading = true
          me.$http.post(csAPI.importBill.printbill.printBill, '', {
            params: {
              billType: 2,
              isPdf: me.isPDF,
              headId: me.gridConfig.selectRows[0].sid
            }
          }).then(res => {
            me.$Message.success('打印成功!')
            if (e === 0) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/pdf`)
                blobSaveFile(blob, item.value)
              })
            } else if (e === 1) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/vnd.ms-excel`)
                blobSaveFile(blob, item.value)
              })
            }
          }).catch(() => {
          }).finally(() => {
            me.boxDownloading = false
          })
        }
      },
      /**
       * 打印合同
       */
      printContract(e) {
        let me = this
        if (e === 0) {
          me.isPDF = true
        } else if (e === 1) {
          me.isPDF = false
        }
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.warning('请选择您要打印的数据')
        } else if (me.gridConfig.selectRows.length > 1) {
          me.$Message.warning('只能选择一条数据打印!')
        } else {
          me.contractDownloading = true
          me.$http.post(csAPI.importBill.printbill.printBill, '', {
            params: {
              billType: 3,
              isPdf: me.isPDF,
              headId: me.gridConfig.selectRows[0].sid
            }
          }).then(res => {
            me.$Message.success('打印成功!')
            if (e === 0) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/pdf`)
                blobSaveFile(blob, item.value)
              })
            } else if (e === 1) {
              res.data.data.forEach(item => {
                const blob = me.getBlob(item.key, `application/vnd.ms-excel`)
                blobSaveFile(blob, item.value)
              })
            }
          }).catch(() => {
          }).finally(() => {
            me.contractDownloading = false
          })
        }
      },
      statusName(val) {
        if (val === '0') {
          return '未锁定'
        } else if (val === '1') {
          return '锁定'
        }
      },
      codeConvertName(val) {
        if (val === null) {
          return val
        } else {
          let item = this.compData.find(p => p.key === val)
          if (!item) {
            return val
          }
          return item.value
        }
      },
      /**
       * 如果选择使用电子签章
       * @param row
       */
      handleDownloadPdf(row) {
        if (!isNullOrEmpty(row.sid)) {
          zipExport(csAPI.importBill.downloadPdf.downloadPdf, {
            ieMark: 'I',
            headId: row.sid,
            emsListNo:row.emsListNo,
            invoiceNo:row.invoiceNo
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
