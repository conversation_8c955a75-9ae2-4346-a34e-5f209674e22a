<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form xdo-enter-form"
             :rules="rulesHeader" :model="addParams" label-position="right" :label-width="110" inline>
      <XdoCard :border="false" title="预录入单信息" class="dc-merge-1-4">
        <div class="dc-form-4">
          <XdoFormItem prop="emsListNo" label="单据内部编号">
            <XdoIInput v-model="addParams.emsListNo" :disabled="isText" :maxlength="32"
                       @on-enter="searchInfor"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="hawb" label="提运单号">
            <XdoIInput v-model="addParams.hawb" :disabled="isText" :maxlength="30"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="voyageDate" label="航班日期">
            <XdoDatePicker type="date" placeholder="航班日期" v-model="addParams.voyageDate" :disabled="isChange"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="templateHeadId" label="模版">
            <xdo-select v-model="addParams.templateHeadId" clearable :options="templatecode" dataValue="key" dataLabel="value"
                        :optionLabelRender="item => item.value" :disabled="isChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="invoiceNo" label="发票号">
            <XdoIInput v-model="addParams.invoiceNo" :disabled="isChange" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="contrNo" label="合同协议号">
            <XdoIInput v-model="addParams.contrNo" :disabled="isChange" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="wrapType" label="包装种类">
            <xdo-select v-model="addParams.wrapType" :disabled="isChange" :meta="pcode.wrap"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="件数">
            <XdoIInput v-model="addParams.packNum" :disabled="isChange" :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="总净重">
            <XdoIInput v-model="addParams.netWt" :disabled="isChange" notConvertNumber :maxlength="10"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="总毛重">
            <XdoIInput v-model="addParams.grossWt" :disabled="isChange" :maxlength="10"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem label="运输方式">
            <xdo-select :asyncOptions="pcodeList" :meta="pcode.transf" v-model="addParams.trafMode"
                        :optionLabelRender="pcodeRender" :disabled="isChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="tradeCountry" label="启运国(地区)">
            <xdo-select v-model="addParams.tradeCountry" :disabled="isChange" :meta="pcode.country_outdated"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="despPort" label="启运港">
            <xdo-select v-model="addParams.despPort" :disabled="isChange" :meta="pcode.port_lin"
                        :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem label="备注" class="dc-merge-2-5">
            <XdoIInput v-model="addParams.note" :disabled="isChange" :maxlength="500"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <Card :border="false" class="dc-merge-1-4">
        <div slot="title">
          <p style="vertical-align: middle; width: inherit; padding-right: 20px;">
            发票信息
          </p>
          <XdoButton type="primary" icon="ios-eye-outline" @click="openSelectTemplate" v-if="btnShow">选择发票信息模板</XdoButton>
        </div>
        <div class="dc-form-4">
          <XdoFormItem prop="consignorCode" label="境外发货人" class="dc-merge-1-3">
            <xdo-select v-model="addParams.consignorCode" clearable :options="selectSource.consigneeSource" dataValue="customerCode" dataLabel="companyName"
                        :optionLabelRender="item => item.customerCode + ' ' + (item.companyName?item.companyName:'')" :disabled="isChange"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="consignorAddr" label="发货人地址" class="dc-merge-3-5">
            <XdoIInput type="text" v-model="addParams.consignorAddr" :disabled="isChange" :maxlength="500"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="consignorTel" label="发货人电话">
            <XdoIInput type="text" v-model="addParams.consignorTel" :disabled="isChange" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="invoiceDate" label="发票日期">
            <XdoDatePicker type="date" v-model="addParams.invoiceDate" :disabled="isChange" placeholder="请选择日期" style="width: 100%;" transfer></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="shipFromName" label="SHIP_FROM名称">
            <xdo-select v-if="isShipFromSelect" v-model="addParams.shipFromName" clearable dataValue="label" :maxlength="100"
                        :disabled="isChange||isshipFrom" :options="selectSource.shipFromSource"></xdo-select>
            <XdoIInput v-else type="text" v-model="addParams.shipFromName" rows="2" :disabled="isChange" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="shipFrom" label="SHIP_FROM地址及电话" class="dc-merge-2-4 dc-merge-row-shipTo">
            <XdoIInput type="textarea" v-model="addParams.shipFrom" rows="2" :disabled="isChange" :maxlength="512"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="despPortEn" label="启运港">
            <XdoIInput type="text" v-model="addParams.despPortEn" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="destPort" label="目的港">
            <XdoIInput type="text" v-model="addParams.destPort" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="forwarder" label="货运代理">
            <XdoIInput type="text" v-model="addParams.forwarder" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="trafModeName" label="运输方式">
            <XdoIInput type="text" v-model="addParams.trafModeName" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="transModeName" label="成交方式">
            <XdoIInput type="text" v-model="addParams.transModeName" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="paymentMode" label="付款方式">
            <XdoIInput type="text" v-model="addParams.paymentMode" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="marks" label="包装尺寸信息" class="dc-merge-2-5">
            <XdoIInput type="text" v-model="addParams.marks" :maxlength="255" :disabled="isChange"></XdoIInput>
          </XdoFormItem>
        </div>
      </Card>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <XdoButton type="warning" v-if="btnShow" @click="templateSave">保存</XdoButton>&nbsp;
      <XdoButton type="primary" v-if="btnShow" @click="handleSaveTemplate">保存为模板</XdoButton>&nbsp;
      <XdoButton type="primary" @click="closeAdd">返回</XdoButton>&nbsp;
    </div>
    <XdoCard :border="false">
      <div class="xdo-enter-action action" style="text-align: left; margin: 2px;">
        <XdoButton type="warning" @click="productBill" :loading="productBillLoading" :disabled="isButton">生成单据</XdoButton>&nbsp;
        <XdoButton type="warning" @click="handlerBatchDownload" :disabled="batchDownloadDisabled">批量下载</XdoButton>&nbsp;
      </div>
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" stripe border>
        <template slot-scope="{ row }" slot="action">
          <Dropdown trigger="click" transfer>
            <XdoButton type="text" style="font-size: 12px; width: 95px;">
              <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>下载 <XdoIcon type="ios-arrow-down"></XdoIcon>
            </XdoButton>
            <DropdownMenu slot="list">
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="downloadInfor(row,0)">
                  预览PDF
                </XdoButton>&nbsp;
              </DropdownItem>
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="downloadInfor(row,2)">
                  下载PDF
                </XdoButton>&nbsp;
              </DropdownItem>
              <DropdownItem style="padding: 0; margin: 0;">
                <XdoButton type="text" style="font-size: 12px;" class="xdo-btn-download" @click="downloadInfor(row,1)">
                  下载Excel
                </XdoButton>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </template>
      </XdoTable>
    </XdoCard>
    <XdoModal v-model="showSelectTemplate" title="选择模板" footer-hide :mask-closable="false" :width="900">
      <InvoiceSelectTemplate ref="selTemplate" @selectTemplate:success="selectTemplateSuccess" @selectTemplate:close="closeSelectTemplateModel"></InvoiceSelectTemplate>
    </XdoModal>
    <TemplateConfirm :show.sync="tempConfirmShow" @confirm:success="afterConfirm"></TemplateConfirm>
    <eSealModal :show.sync="sealShow" :declList="declList" @onConfirm="onConfirmMethod"></eSealModal>
    <dc-file-preview-pop :show.sync="filePreview.show" :file-data="filePreview.fileData" :customize-url="filePreview.pdfUrl"></dc-file-preview-pop>
  </section>
</template>

<script>
  import { csAPI, zipExport } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import eSealModal from '../e-seal/eSealModal'
  import { TemplateConfirm }  from '@/view/cs-common'
  import { columnsConfig, columns } from './importBillColumns'
  import { getHttpHeaderFileName, blobSaveFile, isNullOrEmpty } from '@/libs/util'
  import InvoiceSelectTemplate from '../../decInvoiceBoxTemplateI/InvoiceSelectTemplate'

  export default {
    name: 'importBillEdit',
    components: {
      eSealModal,
      TemplateConfirm,
      InvoiceSelectTemplate
    },
    mixins: [columns],
    props: {
      typeNoInfor: {
        type: Number,
        default: () => (2)
      },
      searchDataInfor: {
        type: Object,
        default: () => ({})
      },
      textData: {
        type: Object,
        default: () => ({})
      },
      dataList: {
        type: Array,
        default: () => ([])
      },
      compData: {
        type: Array,
        default: () => ([])
      },
      templatecodelist: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      return {
        sid: '',
        isPdf: true,
        btnShow: true,
        isText: false,
        actionType: '',
        isChange: false,
        isButton: false,
        isshipFrom: false,
        isShipFromSelect: false,
        templatecode: this.templatecodelist,
        addParams: {
          sid: '',
          erpHeadId: '',        // 提单表体id
          emsListNo: '',        // 关联编号
          hawb: '',             // 提运单号
          invoiceNo: '',        // 发票号
          contrNo: '',          // 合同号
          supplierCode: '',
          supplierName: '',
          trafMode: '',         // 运输方式
          forwardName: '',      // 货代名称
          grossWt: null,        // 总毛重
          netWt: null,          // 总净重
          packNum: null,        // 件数
          wrapType: '',         // 包装种类
          voyageDate: null,     // 航班日期
          tradeCountry: '',
          note: '',             // 备注
          templateHeadId: '',   // 模版id
          templateCode: '',     // 模版编号
          status: '',
          consignorCode: '',
          consignorName: '',
          consignorAddr: '',
          consignorTel: '',
          despPortEn: '',
          despPort: '',
          destPort: '',
          shipFromName: '',
          shipFrom: '',
          invoiceDate: new Date().toLocaleDateString(),
          curr: '',
          forwarder: '',
          trafModeName: '',
          paymentMode: '',
          transModeName: '',
          bankInfo: '',
          marks: ''
        },
        gridConfig: {
          data: [],
          selectRows: [],
          selectData: [],
          gridColumns: []
        },
        rulesHeader: {
          emsListNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          invoiceNo: [{required: true, message: '不能为空!', trigger: 'blur'}],
          customerCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
          templateHeadId: [{required: true, message: '不能为空!', trigger: 'blur'}],
          consignorCode: [{required: true, message: '不能为空!', trigger: 'change'}],
          consignorAddr: [{max: 250, message: '长度不能超过250位字节(汉字占2位)!', trigger: 'blur'}],
          consignorTel: [{max: 100, message: '长度不能超过100位字节(汉字占2位)!', trigger: 'blur'}],
          despPort: [{max: 20, message: '长度不能超过20位字节(汉字占2位)!', trigger: 'blur'}],
          destPort: [{max: 20, message: '长度不能超过20位字节(汉字占2位)!', trigger: 'blur'}],
          shipFrom: [{max: 512, message: '长度不能超过512位字节(汉字占2位)!', trigger: 'blur'}],
          curr: [{max: 20, message: '长度不能超过20位字节(汉字占2位)!', trigger: 'blur'}],
          forwarder: [{max: 50, message: '长度不能超过50位字节(汉字占2位)!', trigger: 'blur'}],
          trafModeName: [{max: 20, message: '长度不能超过20位字节(汉字占2位)!', trigger: 'blur'}],
          paymentMode: [{max: 50, message: '长度不能超过50位字节(汉字占2位)!', trigger: 'blur'}],
          transModeName: [{max: 100, message: '长度不能超过100位字节(汉字占2位)!', trigger: 'blur'}],
          bankInfo: [{max: 500, message: '长度不能超过500位字节(汉字占2位)!', trigger: 'blur'}]
        },
        selectSource: {
          shipFromSource: [],
          consigneeSource: []
        },
        declList: [],
        sealShow: false,
        tempConfirmShow: false,
        showSelectTemplate: false,
        productBillLoading: false,
        consigneeIsFromTemplate: false, //收货人是否来源于模板
        filePreview: {
          show: false,
          fileData: [],
          pdfUrl: csAPI.importBill.download.DdownloadPdf
        }
      }
    },
    created() {
      this.initialData()
    },
    mounted() {
      let me = this
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
      me.searchDataInforMethods(me.searchDataInfor)
      me.typeNoInforMethods(me.typeNoInfor)
    },
    watch: {
      handler(newVal) {
        let me = this
        if (me.actionType === 0) {
          me.$http.post(csAPI.importBill.templatelist.templateList, null, {
            params: {
              iemark: 'I',
              customerCode: newVal
            }
          }).then(res => {
            me.templatecode = res.data.data
          }).catch(() => {
          })
        }
      },
      'templatecode': {
        immediate: true,
        handler: function (newVal) {
          if (newVal && Array.isArray(newVal) && newVal.length === 1) {
            this.addParams.templateHeadId = newVal[0].key
          }
        }
      },
      'addParams.consignorCode': {
        handler: function (newVal, oldVal) {
          if (newVal !== oldVal) {
            if (this.typeNoInfor === 0) {
              this.consigneeCodeChange(newVal)
              if (!isNullOrEmpty(oldVal)) {
                this.shipFromInfo(newVal)
              }
            } else if (this.typeNoInfor === 1 && !isNullOrEmpty(oldVal)) {
              this.consigneeCodeChange(newVal)
              this.shipFromInfo(newVal)
            }
          }
        }
      }
    },
    computed: {
      handler() {
        return this.addParams.consignorCode
      },
      batchDownloadDisabled() {
        let me = this
        return me.gridConfig.data.length <= 0
      }
    },
    methods: {
      typeNoInforMethods(newVal) {
        if (newVal === 0) {
          this.actionType = 0
        } else if (newVal === 1) {
          this.actionType = 1
          this.isText = true
          this.isShipFromSelect = false
          this.searchBodyBill()
        } else if (newVal === 2) {
          this.actionType = 1
          this.isText = true
          this.isShipFromSelect = false
          this.isChange = true
          this.btnShow = false
          this.isButton = true
          this.searchBodyBill()
        }
      },
      searchDataInforMethods(newVal) {
        let me = this
        me.sid = newVal.sid
        Object.assign(me.addParams, newVal)
      },
      // 关联编号回车查询
      searchInfor() {
        let me = this
        me.templatecode = ''
        me.$http.post(csAPI.importBill.searchno.searchNo, null, {
          params: {
            emsListNo: me.addParams.emsListNo
          }
        }).then(res => {
          Object.assign(me.addParams, res.data.data)
          me.addParams.erpHeadId = res.data.data.sid
          me.addParams.supplierCode = res.data.data.overseasShipper
          me.addParams.consignorCode = res.data.data.overseasShipper
          if (me.actionType === 0) {
            me.$http.post(csAPI.importBill.templatelist.templateList, null, {
              params: {
                iemark: 'I',
                customerCode: me.addParams.consignorCode
              }
            }).then(res => {
              me.templatecode = res.data.data
            }).catch(() => {
            })
          }
          if (res.data.data.shipFromName) {
            me.isshipFrom = true
            me.isShipFromSelect = false
            me.addParams.shipFromName = res.data.data.shipFromName
          } else {
            if (res.data.data.overseasShipper) {
              me.isshipFrom = false
              me.isShipFromSelect = true
              me.$http.post(csAPI.csBaseInfo.supplierInfo.shipFrom.allshipFrom, {
                supplierCode: res.data.data.overseasShipper
              }).then(val => {
                if (val.data.data) {
                  if (val.data.data.length > 0) {
                    val.data.data.forEach(item => {
                      me.selectSource.shipFromSource.push({
                        value: item.shipFromCode,
                        label: item.shipFromName
                      })
                    })
                  } else {
                    me.isshipFrom = true
                    me.isShipFromSelect = false
                  }
                }
              }).catch(() => {
              })
            }
          }
          if (res.data.data.shipFromAddress) {
            me.addParams.shipFrom = res.data.data.shipFromAddress
          }
        }).catch(() => {
        })
      },
      // 境外发货人变更
      shipFromInfo(val) {
        let me = this
        me.selectSource.shipFromSource = []
        me.$http.post(csAPI.csBaseInfo.supplierInfo.shipFrom.allshipFrom, {
          supplierCode: val
        }).then(res => {
          if (res.data.data) {
            if (res.data.data.length > 0) {
              me.isshipFrom = false
              me.isShipFromSelect = true
              res.data.data.forEach(item => {
                me.selectSource.shipFromSource.push({
                  value: item.shipFromCode,
                  label: item.shipFromName
                })
              })
            } else {
              me.isshipFrom = true
              me.isShipFromSelect = false
            }
          }
        }).catch(() => {
        })
      },
      // 模版
      choiceTemplate(e) {
        let me = this
        if (e !== '') {
          me.addParams.templateCode = ''
          me.$http.post(csAPI.importBill.templatelist.templateList, null, {
            params: {
              iemark: 'I',
              customerCode: e
            }
          }).then(res => {
            me.templatecode = res.data.data
          }).catch(() => {
          })
        }
      },
      // 新增
      templateSave() {
        let me = this
        me.$refs['formInline'].validate().then(valid => {
          if (valid) {
            me.dataList.forEach(item => {
              if (item.key === me.addParams.supplierCode) {
                me.addParams.supplierName = item.value
              }
            })
            me.templatecode.forEach(item => {
              if (item.key === me.addParams.templateHeadId) {
                me.addParams.templateCode = item.value
              }
            })
            me.selectSource.consigneeSource.forEach(item => {
              if (item.customerCode === me.addParams.consignorCode) {
                me.addParams.consignorName = (item.companyName ? item.companyName : '')
              }
            })
            if (me.actionType === 0) {
              if (me.sid === '' || me.sid === undefined) {
                me.$http.post(csAPI.importBill.addbill.addBill, me.addParams).then(res => {
                  me.sid = res.data.data.sid
                  me.addParams.status = res.data.data.status
                  me.$Message.success('新增成功!')
                  me.searchBodyBill()
                  me.isText = true
                }).catch(() => {
                })
              } else {
                me.$http.put(csAPI.importBill.updatebill.updateBill + `/${me.sid}`, me.addParams).then(() => {
                  me.$Message.success('修改成功!')
                  me.searchBodyBill()
                }).catch(() => {
                })
              }
            } else if (me.actionType === 1) {
              me.$http.put(csAPI.importBill.updatebill.updateBill + `/${me.sid}`, me.addParams).then(() => {
                me.$Message.success('修改成功!')
                me.searchBodyBill()
              }).catch(() => {
              })
            }
          }
        })
      },
      // 生成单据
      productBill() {
        let me = this
        if (me.sid === undefined) {
          me.$Message.error('请先保存数据')
        } else {
          // 判断是否上传了电子签章
          me.$http.post(csAPI.csBaseInfo.electronicSeal.selectAllPaged).then(res => {
            if (res.data.data && res.data.data.length > 0) {
              me.sealShow = true
              me.declList = res.data.data
            } else {
              me.continueProductBill('0', {headId: me.sid, isExistSeal: false})
            }
          })
        }
      },
      /**
       * 批量下载
       */
      handlerBatchDownload() {
        let me = this
        zipExport(csAPI.importBill.downloadPdf.downloadPdf, {
          headId: me.sid,
          ieMark: 'I',
          emsListNo:me.addParams.emsListNo,
          invoiceNo:me.addParams.invoiceNo
        })
      },
      // 如果选择使用电子签章
      onConfirmMethod(obj) {
        this.continueProductBill('1', obj)
      },
      // 继续生成单据
      continueProductBill(type, params) {
        let me = this
        if (me.sid !== undefined) {
          me.productBillLoading = true
          let data = {}
          if (type === '0') {
            data = Object.assign({}, params)
          } else if (type === '1') {
            data = Object.assign({}, params, {headId: me.sid, ieMark: 'I'})
          }
          me.$http.post(csAPI.importBill.productbill.productBill, data).then(() => {
            me.searchBodyBill()
            me.$Message.success('单据生成成功')
          }).catch(() => {
          }).finally(() => {
            me.productBillLoading = false
          })
        }
      },
      // 查询表体数据
      searchBodyBill() {
        let me = this
        me.$http.post(csAPI.importBill.billlist.billList + `/${me.sid}`).then(val => {
          me.gridConfig.data = val.data.data
        }).catch(() => {
        })
      },
      // 返回
      closeAdd() {
        let me = this
        me.$emit('onEditback', true)
      },
      // 关闭返回
      closeBack() {
        let me = this
        me.$refs['formInline'].validate().then(valid => {
          if (valid) {
            me.templateSave()
            setTimeout(me.closeAdd, 1000)
          }
        })
      },
      // 下载PDF
      pdfDownload(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: `application/pdf`})
        blobSaveFile(blob, filename)
      },
      // 下载excel
      excelDownload(stream, headers) {
        const filename = getHttpHeaderFileName(headers)
        const blob = new Blob([stream], {type: `application/vnd.ms-excel`})
        blobSaveFile(blob, filename)
      },
      downloadInfor(e, num) {
        let me = this
        if (num === 0 || num === 2) {
          me.isPdf = true
        } else if (num === 1) {
          me.isPdf = false
        }

        let fastDfsId = ''
        if (me.isPdf && !isNullOrEmpty(e.fdfsPdfId)) {
          fastDfsId = e.fdfsPdfId
          me.isPdf = false
        } else {
          fastDfsId = e.fdfsId
        }

        const param = {
          isPdf: me.isPdf,
          fastDfsId: fastDfsId,
          fileName: e['buildFileName'],//e.billType === '1' ? me.addParams.invoiceNo : `${e.buildFileName}`
          headId:e.headId,
          billType:e.billType
        }

        me.$http.post(csAPI.importBill.download.Ddownload, null, {
          params: param,
          responseType: 'blob'
        }).then(res => {
          if (num === 0) {
            const filename = getHttpHeaderFileName(res.headers)
            // 预览
            me.$set(me.filePreview, 'show', true)
            me.$set(me.filePreview, 'fileData', [{
              sid: e.sid,
              originFileName: filename
            }])
          } else if (num === 1) {
            me.$Message.success('下载成功!')
            me.excelDownload(res.data, res.headers)
          } else if (num === 2) {
            me.$Message.success('下载成功!')
            me.pdfDownload(res.data, res.headers)
          }
        }, () => {
        })
      },
      covername(e) {
        if (e === '1') {
          return '发票'
        } else if (e === '2') {
          return '箱单'
        } else if (e === '3') {
          return '合同'
        }
      },
      /**
       * 打开选择模板
       */
      openSelectTemplate() {
        let me = this
        me.showSelectTemplate = true
        me.$refs.selTemplate.onSearch()
      },
      /**
       * 选择模板后操作
       * @param templateEntity
       */
      selectTemplateSuccess(templateEntity) {
        let me = this,
          entity = me.addParams
        Object.keys(entity).forEach(property => {
          if (templateEntity.hasOwnProperty(property) && property !== 'sid' && property !== 'supplierCode') {
            me.$set(entity, property, templateEntity[property])
          }
        })
        me.addParams = entity
        me.consigneeIsFromTemplate = true //标记数据来源于模板
      },
      /**
       * 选择模板关闭后操作
       */
      closeSelectTemplateModel() {
        this.showSelectTemplate = false
      },
      /**
       * 保存为模板
       */
      handleSaveTemplate() {
        let me = this
        me.$refs['formInline'].validateField("consignorCode", (errMsg) => {
          if (!errMsg) {
            me.tempConfirmShow = true
          }
        })
      },
      /**
       * 保存确认
       * @param templateName
       */
      afterConfirm(templateName) {
        let me = this
        me.tempConfirmShow = false
        if (templateName && templateName.trim().length > 0) {
          let entity = JSON.parse(JSON.stringify(me.addParams))
          me.$refs.selTemplate.saveTemplate(entity, templateName, () => {
            me.$Message.success('模板保存成功')
          })
        }
      },
      /**
       * 收货人变更
       * @param selectedVal
       */
      consigneeCodeChange(selectedVal) {
        let me = this
        me.$set(me.addParams, 'consignorAddr', '')
        // me.$set(me.addParams, 'consignorTel', '')
        if (selectedVal && !me.consigneeIsFromTemplate) {
          me.selectSource.consigneeSource.forEach((item) => {
            if (item.customerCode === selectedVal) {
              me.$set(me.addParams, 'consignorAddr', item.addressEn)
              me.$set(me.addParams, 'consignorTel', item.telephoneNo)
              me.$set(me.addParams, 'consignorName', item.companyName)
            }
          })
          me.consigneeIsFromTemplate = false //还原数据来源
        }
      },
      /**
       * 初始化收货人数据
       */
      initialData() {
        let me = this
        me.$http.post(csAPI.ieParams.getListByCustomerType + `/PRD`).then(res => {
          me.$set(me.selectSource, "consigneeSource", res.data.data)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .dc-merge-row(@start,@end) {
    grid-row-start: @start;
    grid-row-end: @end;
  }

  .dc-merge-row-shipTo {
    .dc-merge-row(2, 4);
  }

  .dc-merge-row-bank {
    .dc-merge-row(4, 6);
  }

  .dc-merge-row-marks {
    .dc-merge-row(6, 8);
  }

  .dc-form-4 {
    grid-template-columns: repeat(4, minmax(100px, 1fr));
  }
</style>
