<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem label="内部编号">
        <XdoIInput v-model="searchParam.emsListNo" :maxlength="30"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="发票号">
        <XdoIInput v-model="searchParam.invoiceNo" :maxlength="30"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="合同协议号">
        <XdoIInput v-model="searchParam.contrNo" :maxlength="30"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="境外发货人">
        <XdoIInput v-model="searchParam.consignorName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem label="SHIP FROM">
        <XdoIInput v-model="searchParam.shipFrom" :maxlength="100"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="运输方式">
        <xdo-select :asyncOptions="pcodeList" :meta="pcode.transf" v-model="searchParam.trafMode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="status" label="货运代理">
        <xdo-select v-model="searchParam.forwardCode" clearable :options="inDataSource.forwardCode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'DecImportBillBoxSearch',
    props: {
      showSearch: {
        type: Boolean,
        default: () => ({})
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',    // 关联编号
          invoiceNo: '',    // 发票号
          contrNo: '',      // 合同号
          trafMode: '',     // 运输方式
          forwardCode: '',  // 货运代理
          shipFrom: ''
        },
        inDataSource: {}
      }
    },
    watch: {
      showSearch: {
        immediate: true,
        handler: function (val) {
          if (val) {
            this.custumerInfor()
            this.forWardInfor()
          }
        }
      }
    },
    methods: {
      // 获取客户信息+货代
      custumerInfor() {
        let me = this
        me.$http.post(csAPI.importBill.custumer.custumerInfor + `/PRD`).then(res => {
          me.$set(me.inDataSource, 'custumerType', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
      },
      forWardInfor() {
        let me = this
        me.$http.post(csAPI.importBill.custumer.custumerInfor + `/FOD`).then(res => {
          me.$set(me.inDataSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
        }).catch(() => {
        })
      }
    }
  }
</script>

<style scoped>
</style>
