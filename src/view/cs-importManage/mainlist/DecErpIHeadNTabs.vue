<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头" index="'1'">
        <Layout>
          <Content>
            <DynamicHead v-if="dynamicHeadTabShow" ref="DynamicHead" ie-mark="I" bond-mark="" :edit-config="editConfig"
                         :saved-audit-data="headAuditData"
                         @onEditBack="editBack" @onAfterHeadSaved="afterHeadSaved"></DynamicHead>
            <Head v-else ref="head" :edit-config="editConfig" :saved-audit-data="headAuditData"
                  @onEditBack="editBack" @onAfterHeadSaved="afterHeadSaved"></Head>
          </Content>
          <Sider hide-trigger collapsible width="380" collapsed-width="36" v-model="collapsed"
                 style="overflow: hidden; background: transparent; height: 732px;">
            <div class="right-sider">
              <XdoIcon type="md-menu" @click.native="handleRightSliderClick"/>
            </div>
            <XdoCard>
              <p ref="attachTitle" style="font-weight: bold; padding: 3px 10px; border-bottom: #dcdee2 solid 1px;">
                随附单证
              </p>
              <DecAttachedDocumentsList :parent-config="editConfig" ie-mark="I" :aeo-show="!showBody"></DecAttachedDocumentsList>
            </XdoCard>
          </Sider>
        </Layout>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="表体" index="'2'">
        <Body ref="body" v-if="tabs.bodyTab" :head-id="headData.sid" :operation-status="editConfig.editStatus" :head-data="headData" :body-audits="bodyAuditData"
              @refreshBill:success="refreshBill"></Body>
      </TabPane>
      <TabPane name="certTab" v-if="certTabShow" label="涉证管理" index="'3'">
        <Cert ref="cert" v-if="tabs.certTab" :parent-config="parentConfig" :iemark="ieMark" @onEditBack="editBack"></Cert>
      </TabPane>
      <TabPane name="billTab" v-if="showBody" label="草单" index="'4'">
        <Bill ref="bill" v-if="tabs.billTab" :head-id="headData.sid" iemark="I" :head-data="headData" :operation-status="editConfig.editStatus" @onEditBack="editBack"></Bill>
      </TabPane>
      <TabPane name="logisticsTab" v-if="logisticsTabShow" label="物流追踪" index="'5'">
        <Logistics ref="logistics" v-if="tabs.logisticsTab" :head-id="headData.sid" @onEditBack="editBack"></Logistics>
      </TabPane>
      <TabPane name="entryTab" v-if="entryTabShow" label="报关追踪" index="'6'">
        <CustomsTrackingI ref="entryTrack" v-if="tabs.entryTab" :head-id="headData.sid" @onEditBack="editBack"></CustomsTrackingI>
      </TabPane>
      <TabPane name="attachTab" v-if="showBody" label="随附单据" index="'7'">
        <Attach ref="attachInfo" v-if="tabs.attachTab" :head-id="headData.sid" :edit="editConfig" iemark="I" :loaded="attachLock"></Attach>
      </TabPane>
      <TabPane name="aeoTab" v-if="showBody" label="内审情况" index="'8'">
        <AeoInfoList ref="aeoInfo" v-if="tabs.aeoTab" :sid="headData.sid" :show-title="aeoShowTitle" ></AeoInfoList>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import Logistics from '../logistics/Logistics'
  import Head from '../head/DecErpIHeadNHeadEdit'
  import Body from '../body/DecErpIListNBodyList'
  import CustomsTrackingI from '../entry/customs-tracking-i'
  import { decErpHeadTabs } from '@/view/cs-ie-manage-mixins/mainList/decErpHeadTabs'

  export default {
    name: 'DecErpIHeadNTabs',
    components: {
      Head,
      Body,
      Logistics,
      CustomsTrackingI
    },
    mixins: [decErpHeadTabs],
    methods: {
      doExtract(rowData) {
        let me = this
        if (me.$refs.head) {
          if (typeof me.$refs.head.selectTemplateSuccess === 'function') {
            me.$refs.head.selectTemplateSuccess(rowData)
          }
        } else if (me.$refs.DynamicHead) {
          if (typeof me.$refs.DynamicHead.selectTemplateSuccess === 'function') {
            me.$refs.DynamicHead.selectTemplateSuccess(rowData, false)
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }

  .right-sider {
    height: 38px;
    padding: 7px 0 0 6px;
    background: rgb(245, 247, 247);
    border-bottom: 1px solid rgb(214, 219, 222);

    .ivu-layout-sider-children {
      overflow-y: hidden;
      margin-right: -18px;
    }
  }

  .right-sider i {
    color: #389de9;
    cursor: pointer;
    font-size: 26px;
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    transition: transform .2s linear;
    -webkit-transition: -webkit-transform .2s linear;
  }
</style>
