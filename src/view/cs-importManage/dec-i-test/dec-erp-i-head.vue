<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
        <template v-slot:feeMark>
          <FeeCascader ref="fiFeeMark" :options="feeOptions" @onFeeDataChanged="onFeeDataChanged"
                       :disabled="feeMarkDisable"></FeeCascader>
        </template>
        <template v-slot:insurMark>
          <FeeCascader ref="fiInsurMark" :options="insurOptions" @onFeeDataChanged="onInsurDataChanged"
                       :disabled="insurMarkDisable"></FeeCascader>
        </template>
        <template v-slot:otherMark>
          <FeeCascader ref="fiOtherMark" :options="otherOptions" @onFeeDataChanged="onOtherDataChanged"
                       :disabled="showDisable"></FeeCascader>
        </template>
        <template v-slot:receiveCode>
          <XdoIInput type="text" v-model="receiveCodeName" disabled></XdoIInput>
        </template>
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 2px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
import {csAPI} from '@/api'
import {importExportManage} from '@/view/cs-common'
import {baseForm} from '@/mixin/generic/form/baseForm'
import {isNullOrEmpty, ArrayToLocaleLowerCase} from '@/libs/util'
import FeeCascader from '@/view/cs-ie-manage-mixins/components/fee-cascader'
import {checkBoxIndeterminate} from '@/view/cs-ie-manage/dec-erp-head/dynamic/js/checkBoxIndeterminate'

export default {
  name: 'decErpIHead',
  components: {
    FeeCascader
  },
  mixins: [baseForm, checkBoxIndeterminate],
  data() {
    let fields = this.getFields()
    return {
      selfFields: fields,
      isEmsListNoAuto: false,
      cmbSource: {
        shipFrom: [],
        entryType: importExportManage.entryType,
        billType: importExportManage.billTypeMap,
        dclcusMark: importExportManage.dclcusMark,
        dclcusType: importExportManage.dclcusType,
        tradeTerms: importExportManage.tradeTermList,
        billListType: importExportManage.BILL_TYPE_MAP,
        mergeType: importExportManage.mergeTypeMapFull,
        containerType: importExportManage.containerType
      },
      ajaxUrl: {
        insert: csAPI.csImportExport.decErpIHeadN.insert,
        update: csAPI.csImportExport.decErpIHeadN.update
      }
    }
  },
  mounted: function () {
    let me = this

    // 是否【单据内部编号】自动生成
    me.$http.get(csAPI.enterpriseParamsLib.customDocNo.judgeIsExistsRule + '/LI').then(res => {
      me.$set(me, 'isEmsListNoAuto', res.data.data)
    }).catch(() => {
      me.$set(me, 'isEmsListNoAuto', false)
    }).finally(() => {
      me.setDisable('emsListNo', me.isEmsListNoAuto || me.newDisable)
    })

    me.fieldsReorganization()
  },
  computed: {
    receiveCodeName() {
      let me = this,
        result = '',
        code = me.detailConfig.model.receiveCode,
        name = me.detailConfig.model.receiveName
      if (!isNullOrEmpty(code)) {
        result = code.trim()
      }
      if (!isNullOrEmpty(name)) {
        result += ' ' + name.trim()
      }
      return result.trim()
    },
    feeOptions() {
      let me = this
      return {
        mark: {
          field: 'feeMark',
          value: me.detailConfig.model.feeMark
        },
        rate: {
          field: 'feeRate',
          value: me.detailConfig.model.feeRate
        },
        curr: {
          field: 'feeCurr',
          value: me.detailConfig.model.feeCurr
        }
      }
    },
    insurOptions() {
      let me = this
      return {
        mark: {
          field: 'insurMark',
          value: me.detailConfig.model.insurMark
        },
        rate: {
          field: 'insurRate',
          value: me.detailConfig.model.insurRate
        },
        curr: {
          field: 'insurCurr',
          value: me.detailConfig.model.insurCurr
        }
      }
    },
    otherOptions() {
      let me = this
      return {
        mark: {
          field: 'otherMark',
          value: me.detailConfig.model.otherMark
        },
        rate: {
          field: 'otherRate',
          value: me.detailConfig.model.otherRate
        },
        curr: {
          field: 'otherCurr',
          value: me.detailConfig.model.otherCurr
        }
      }
    }
  },
  watch: {
    'detailConfig.model.overseasShipper': {
      immediate: true,
      handler: function (val) {
        let me = this
        if (isNullOrEmpty(val)) {
          me.$set(me.cmbSource, 'shipFrom', [])
          me.sourceReLoad('shipFrom')
        } else {
          me.$http.post(csAPI.csBaseInfo.supplierInfo.shipFrom.getShipFromCodeName + '/' + val).then(res => {
            me.$set(me.cmbSource, 'shipFrom', ArrayToLocaleLowerCase(res.data.data))
          }).catch(() => {
            me.$set(me.cmbSource, 'shipFrom', [])
          }).finally(() => {
            me.sourceReLoad('shipFrom')
          })
        }
      }
    },
    'detailConfig.model.agentCreditCode': {
      handler: function (creditCode) {
        let me = this
        me.$nextTick(() => {
          let agentCodeField = me.detailConfig.fields.find(f => f.key === 'agentCode')
          if (agentCodeField) {
            agentCodeField.slot.append = creditCode
          }
        })
      }
    }
  },
  methods: {
    /**
     * 获取默认值(调用时可覆盖)
     * @returns {{}}
     */
    getDefaultData() {
      let me = this
      return {
        agentCode: me.$store.state.user.company,
        receiveCode: me.$store.state.user.company,
        receiveName: me.$store.state.user.companyName
      }
    },
    /**
     * 赋值后操作
     * @param isNew
     */
    afterModelAssignment(isNew) {
      let me = this
      if (isNew) {
        me.agentCodeEnter()
      }
      me.afterModelLoadedExtend()
    },
    /**
     * 清单申报单位海关十位编码回车事件
     */
    agentCodeEnter() {
      let me = this,
        queryCode = me.detailConfig.model.agentCode.trim()
      if (!isNullOrEmpty(queryCode) && queryCode.length === 10) {
        me.pcodeRemote(me.pcode.company, queryCode).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            me.$set(me.detailConfig.model, 'agentName', res[0]['NAME'])
            me.$set(me.detailConfig.model, 'agentCreditCode', res[0]['CREDIT_CODE'])
          } else {
            me.$set(me.detailConfig.model, 'agentName', '')
            me.$set(me.detailConfig.model, 'agentCreditCode', '')
          }
        }).catch(() => {
          me.$set(me.detailConfig.model, 'agentName', '')
          me.$set(me.detailConfig.model, 'agentCreditCode', '')
        })
      } else {
        me.$set(me.detailConfig.model, 'agentName', '')
        me.$set(me.detailConfig.model, 'agentCreditCode', '')
      }
    },
    handleShowTemplate() {
      console.info('选择模板')
    },
    onWrapType2Search() {
      console.info('选择其他包装种类')
    },
    declareCodeEnter() {
      console.info('海关十位代码回车')
    },
    declareCreditCodeEnter() {
      console.info('社会信用代码')
    },
    /**
     * 运费变更方法
     * @param feeObj
     */
    onFeeDataChanged(feeObj) {
      let me = this
      me.$set(me.detailConfig.model, 'feeMark', feeObj['feeMark'])
      me.$set(me.detailConfig.model, 'feeRate', feeObj['feeRate'])
      me.$set(me.detailConfig.model, 'feeCurr', feeObj['feeCurr'])
    },
    /**
     * 保费变更方法
     * @param insurObj
     */
    onInsurDataChanged(insurObj) {
      let me = this
      me.$set(me.detailConfig.model, 'insurMark', insurObj['insurMark'])
      me.$set(me.detailConfig.model, 'insurRate', insurObj['insurRate'])
      me.$set(me.detailConfig.model, 'insurCurr', insurObj['insurCurr'])
    },
    /**
     * 杂费变更方法
     * @param otherObj
     */
    onOtherDataChanged(otherObj) {
      let me = this
      me.$set(me.detailConfig.model, 'otherMark', otherObj['otherMark'])
      me.$set(me.detailConfig.model, 'otherRate', otherObj['otherRate'])
      me.$set(me.detailConfig.model, 'otherCurr', otherObj['otherCurr'])
    },
    getFields() {
      let me = this
      return [{
        isCard: true,
        key: '1241324',
        title: '基础信息',
        type: 'empty_formItem',
        itemClass: 'dc-merge-1-4'
      }, {
        props: {
          maxlength: 32
        },
        key: 'emsListNo',
        title: '单据内部编号',
        slot: {
          append: {
            type: 'Button',
            label: '选择模板',
            on: {
              click: me.handleShowTemplate
            }
          }
        }
      }, {
        title: '航班日期',
        key: 'voyageDate',
        type: 'datePicker',
        props: {
          placement: '请选择日期'
        }
      }, {
        type: 'select',
        title: '货运代理',
        key: 'forwardCode'
      }, {
        type: 'pcode',
        key: 'trafMode',
        title: '运输方式',
        props: {
          meta: 'TRANSF'
        }
      }, {
        key: 'hawb',
        title: '提运单号'
      }, {
        props: {
          maxlength: 50
        },
        key: 'trafName',
        title: '运输工具及航次'
      }, {
        type: 'select',
        title: '境外发货人',
        key: 'overseasShipper'
      }, {
        type: 'select',
        key: 'shipFrom',
        title: 'Ship From'
      }, {
        props: {
          maxlength: 100
        },
        title: '发票号',
        key: 'invoiceNo'
      }, {
        key: 'contrNo',
        title: '合同协议号'
      }, {
        type: 'pcode',
        key: 'wrapType',
        title: '包装种类',
        props: {
          meta: 'WRAP'
        }
      }, {
        key: 'wrapType2',
        title: '其他包装',
        itemClass: 'dc-merge-1-3',
        props: {
          placement: '请选择包装种类...'
        },
        slot: {
          append: {
            type: 'Button',
            label: '请选择',
            on: {
              click: me.onWrapType2Search
            }
          }
        }
      }, {
        props: {
          intDigits: 9
        },
        title: '件数',
        key: 'packNum',
        type: 'xdoInput'
      }, {
        props: {
          intDigits: 13,
          precision: 5
        },
        slot: {
          append: 'KG'
        },
        key: 'netWt',
        title: '总净重',
        type: 'xdoInput'
      }, {
        props: {
          intDigits: 13,
          precision: 5
        },
        slot: {
          append: 'KG'
        },
        key: 'grossWt',
        title: '总毛重',
        type: 'xdoInput'
      }, {
        slot: {
          append: 'm³'
        },
        props: {
          intDigits: 11,
          precision: 5
        },
        title: '体积',
        key: 'volume',
        type: 'xdoInput'
      }, {
        type: 'pcode',
        key: 'tradeNation',
        title: '贸易国(地区)',
        props: {
          meta: 'COUNTRY_OUTDATED'
        }
      }, {
        type: 'pcode',
        key: 'tradeCountry',
        title: '启运国(地区)',
        props: {
          meta: 'COUNTRY_OUTDATED'
        }
      }, {
        type: 'pcode',
        title: '启运港',
        key: 'despPort',
        props: {
          meta: 'PORT_LIN'
        }
      }, {
        type: 'pcode',
        title: '经停港',
        key: 'destPort',
        props: {
          meta: 'PORT_LIN'
        }
      }, {
        key: 'remark',
        title: '内部备注'
      }, {
        key: 'inviteDate',
        type: 'datePicker',
        title: '申请进口日期',
        props: {
          placement: '请选择日期'
        }
      }, {
        props: {
          intDigits: 5,
          precision: 2
        },
        key: 'cweight',
        title: '计费重量',
        type: 'xdoInput'
      }, {
        type: 'select',
        key: 'ownerCreditCode',
        title: '消费使用单位代码'
      }, {
        props: {
          disabled: true
        },
        key: 'ownerName',
        title: '消费使用单位名称'
      }, {
        isCard: true,
        key: '3245114',
        title: '申报信息',
        type: 'empty_formItem',
        itemClass: 'dc-merge-1-4'
      }, {
        type: 'select',
        title: '报关行简码及名称',
        key: 'declareCodeCustoms'
      }, {
        key: 'declareCode',
        title: '海关十位代码',
        on: {
          enter: me.declareCodeEnter
        }
      }, {
        title: '社会信用代码',
        key: 'declareCreditCode',
        on: {
          enter: me.declareCreditCodeEnter
        }
      }, {
        key: 'emsNo',
        type: 'select',
        title: '备案号',
        props: {
          optionLabelRender: (opt) => opt.label
        }
      }, {
        props: {
          meta: 'TRADE'
        },
        type: 'pcode',
        title: '监管方式',
        key: 'tradeMode'
      }, {
        type: 'pcode',
        key: 'cutMode',
        title: '征免性质',
        props: {
          meta: 'LEVYTYPE'
        }
      }, {
        type: 'pcode',
        key: 'ieport',
        title: '进境关别',
        props: {
          meta: 'CUSTOMS_REL'
        }
      }, {
        type: 'pcode',
        key: 'entryPort',
        title: '入境口岸',
        props: {
          meta: 'CIQ_ENTY_PORT'
        }
      }, {
        key: 'licenseNo',
        title: '许可证号'
      }, {
        type: 'select',
        key: 'billType',
        title: '清单归并类型'
      }, {
        type: 'select',
        key: 'mergeType',
        title: '报关单归并类型'
      }, {
        type: 'pcode',
        title: '申报地海关',
        key: 'masterCustoms',
        props: {
          meta: 'CUSTOMS_REL'
        }
      }, {
        key: 'iedate',
        title: '进口日期',
        type: 'datePicker',
        props: {
          placement: '请选择日期'
        }
      }, {
        type: 'pcode',
        title: '成交方式',
        key: 'transMode',
        props: {
          meta: 'TRANSAC'
        }
      }, {
        key: 'warehouse',
        title: '货物存放地点'
      }, {
        title: '运费',
        key: 'feeMark'
      }, {
        title: '保费',
        key: 'insurMark'
      }, {
        title: '杂费',
        key: 'otherMark'
      }, {
        key: 'district',
        title: '境内目的地',
        type: 'group_form_line',
        fields: [{
          type: 'pcode',
          labelWidth: 0,
          props: {
            meta: 'AREA'
          },
          key: 'districtCode'
        }, {
          type: 'pcode',
          labelWidth: 0,
          props: {
            meta: 'POST_AREA'
          },
          key: 'districtPostCode'
        }]
      }, {
        key: 'receiveCode',
        title: '境内收货人名称'
      }, {
        type: 'select',
        title: '报关标志',
        key: 'dclcusMark'
      }, {
        type: 'select',
        title: '报关类型',
        key: 'dclcusType'
      }, {
        type: 'select',
        key: 'entryType',
        title: '报关单类型'
      }, {
        type: 'select',
        title: '清单类型',
        key: 'billListType'
      }, {
        props: {
          maxlength: 12
        },
        key: 'relEmsNo',
        title: '关联备案号'
      }, {
        key: 'relListNo',
        title: '关联清单编号'
      }, {
        props: {
          indeterminate: true,
          options: [{
            label: '1',
            labelIM: '9',
            title: '特殊关系确认'
          }, {
            label: '2',
            labelIM: '8',
            title: '价格影响确认'
          }, {
            label: '3',
            labelIM: '7',
            title: '支付特许权使用费确认'
          }, {
            label: '4',
            labelIM: '6',
            title: '自报自缴'
          }]
        },
        slot: {
          append: {
            type: 'Input',
            style: {
              float: 'right',
              width: '116px'
            },
            props: {
              disabled: true,
              value: "注: '✔'为是; '━'为否"
            }
          }
        },
        on: {
          change: function (values) {
            let value1 = me.getLastStatus(values, '1', '9', 'promiseItems'),
              value2 = me.getLastStatus(values, '2', '8', 'promiseItems'),
              value3 = me.getLastStatus(values, '3', '7', 'promiseItems'),
              value4 = me.getLastStatus(values, '4', '6', 'promiseItems'),
              newValueArray = []
            if (!isNullOrEmpty(value1)) {
              newValueArray.push(value1)
            }
            if (!isNullOrEmpty(value2)) {
              newValueArray.push(value2)
            }
            if (!isNullOrEmpty(value3)) {
              newValueArray.push(value3)
            }
            if (!isNullOrEmpty(value4)) {
              newValueArray.push(value4)
            }
            me.$set(me, 'oldPromiseItems', newValueArray)
            me.$set(me.detailConfig.model, 'promiseItems', newValueArray.toString())
          }
        },
        title: '价格说明',
        key: 'promiseItems',
        type: 'checkBoxGroup',
        itemClass: 'dc-merge-1-4 eye-catching'
      }, {
        props: {
          disabled: true
        },
        key: 'declareName',
        title: '申报单位名称'
      }, {
        slot: {
          append: ''
        },
        props: {
          intDigits: 10,
          precision: 5
        },
        key: 'sumQty',
        title: '总数量',
        type: 'xdoInput'
      }, {
        slot: {
          append: ''
        },
        props: {
          intDigits: 13,
          precision: 5
        },
        title: '总金额',
        type: 'xdoInput',
        key: 'sumDecTotal'
      }, {
        type: 'pcode',
        key: 'curr',
        title: '币制',
        props: {
          disabled: true,
          meta: 'CURR_OUTDATED'
        }
      }, {
        type: 'select',
        title: '贸易条款',
        key: 'tradeTerms',
        props: {
          optionLabelRender: (opt) => opt.label
        }
      }, {
        type: 'select',
        title: '集装箱类型',
        key: 'containerType'
      }, {
        props: {
          intDigits: 5
        },
        type: 'xdoInput',
        title: '集装箱数量',
        key: 'containerNum'
      }, {
        props: {
          maxlength: 50
        },
        key: 'applyNo',
        title: '申报表编号'
      }, {
        slot: {
          append: ''
        },
        key: 'agentCode',
        title: '清单申报单位',
        on: {
          enter: me.agentCodeEnter
        }
      }, {
        props: {
          disabled: true
        },
        key: 'agentName',
        title: '清单申报单位名称'
      }, {
        key: 'note',
        title: '备注',
        props: {
          maxlength: 256
        },
        itemClass: 'dc-merge-1-4'
      }]
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ivu-card-head {
  padding: 5px 10px !important;
}

.dc-form {
  grid-template-columns: repeat(3, minmax(100px, 1fr));
}

/deep/ .eye-catching label {
  font-weight: bold;
  color: orangered !important;
}

/deep/ .eye-catching .ivu-form-item-content span {
  color: orangered !important;
}
</style>
