<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </XdoCard>
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="table" class="dc-table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <DecErpIHead v-if="!showList" :edit-config="editConfig" :in-source="cmbSource" @onEditBack="editBack"></DecErpIHead>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { decITestList } from './js/decITestList'
  import { ArrayToLocaleLowerCase } from '@/libs/util'

  export default {
    name: 'decITestList',
    mixins: [decITestList],
    data() {
      return {
        cmbSource: {
          emsNo: [],
          forwardCode: [],
          ownerCreditCode: [],
          overseasShipper: [],
          cutDataValueCode: {},
          declareCodeCustoms: []
        },
        listConfig: {
          exportTitle: '进口提单表头--测试'
        },
        ajaxUrl: {
          deleteUrl: csAPI.csImportExport.decErpIHeadN.delete,
          exportUrl: csAPI.csImportExport.decErpIHeadN.exportUrl,
          getEmsNoSelect: csAPI.csProductClassify.bonded.getEmsNoSelect,
          selectAllPaged: csAPI.csImportExport.decErpIHeadN.selectAllPaged,
          selectComBoxByCode: csAPI.ieParams.selectComboxByCode + '/PRD, CLI'
        }
      }
    },
    created: function () {
      // 备案号
      let me = this
      me.$http.post(me.ajaxUrl.getEmsNoSelect).then(res => {
        me.$set(me.cmbSource, 'emsNo', res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'emsNo', [])
      }).finally(() => {
        me.searchFieldsReLoad('emsNo')
      })
      // 境外收/发货人
      me.$http.post(me.ajaxUrl.selectComBoxByCode).then(res => {
        me.$set(me.cmbSource, 'overseasShipper', [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)])
      }).catch(() => {
        me.$set(me.cmbSource, 'overseasShipper', [])
      }).finally(() => {
        me.searchFieldsReLoad('overseasShipper')
      })
      // 货代
      me.$http.post(csAPI.ieParams.FOD).then(res => {
        me.$set(me.cmbSource, 'forwardCode', ArrayToLocaleLowerCase(res.data.data))
      }).catch(() => {
        me.$set(me.cmbSource, 'forwardCode', [])
      })
      // 报关行
      me.$http.post(csAPI.ieParams.CUT).then(res => {
        me.$set(me.cmbSource, 'declareCodeCustoms', ArrayToLocaleLowerCase(res.data.data))
        let valueCodeObj = {}
        for (let item of res.data.data) {
          valueCodeObj[item.VALUE] = item['CODE']
        }
        me.$set(me.cmbSource, 'cutDataValueCode', valueCodeObj)
      }).catch(() => {
        me.$set(me.cmbSource, 'cutDataValueCode', {})
        me.$set(me.cmbSource, 'declareCodeCustoms', [])
      })
      // 消费使用/生产销售单位代码
      me.$http.post(csAPI.csBaseInfo.productionUnit.selectList).then(res => {
        me.$set(me.cmbSource, 'ownerCreditCode', res.data.data.filter(item => {
          return item.customerType === 'I'
        }).map(it => {
          return {
            code: it.ownerCode,
            label: it.ownerName,
            value: it.ownerCreditCode
          }
        }))
      }).catch(() => {
        me.$set(me.cmbSource, 'ownerCreditCode', [])
      })

      me.$nextTick(() => {
        let field = me.searchConfig.fields.find(p => p.key === 'insertTime')
        if (field) {
          field.props.values = me.ieDefaultDates
        }
      })
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
