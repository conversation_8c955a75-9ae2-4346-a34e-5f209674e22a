import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      allColumns: [
        {
          width: 120,
          title: '税号',
          key: 'codeTS',
          tooltip: true
        },
        {
          width: 120,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 90,
          key: 'isRecord',
          title: '是否备案',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.taxRateManage.IS_RECORD_MAP)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '进口最惠国税率',
          key: 'impDiscountRate'
        },
        {
          width: 120,
          tooltip: true,
          title: '进口普通税率',
          key: 'impOrdinaryRate'
        },
        {
          width: 120,
          tooltip: true,
          key: 'impTempRate',
          title: '进口暂定税率'
        },
        {
          width: 90,
          key: 'accordType',
          title: '进口协定税率',
          render: (h, params) => {
            let accordType = params.row[params.column.key]
            if (accordType === '1') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.showAccordType(params.row.codeTS)
                  }
                }
              }, 'Y')
            } else {
              return h('span', '')
            }
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'addTaxRate',
          title: '进口增值税税率'
        },
        {
          width: 120,
          tooltip: true,
          key: 'impConsumeRate',
          title: '进口消费税税率'
        },
        {
          width: 150,
          tooltip: true,
          key: 'impFundRate',
          title: '进口废弃电器电子基金'
        },
        {
          width: 120,
          tooltip: true,
          title: '保障措施关税税率',
          key: 'impSafeguardRate'
        },
        {
          width: 150,
          key: 'antidumpType',
          title: '进口反倾销税税率',
          render: (h, params) => {
            let antidumpType = params.row[params.column.key]
            if (antidumpType === '1') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.showAntidumpType(params.row.codeTS)
                  }
                }
              }, 'Y')
            } else {
              return h('span', '')
            }
          }
        },
        {
          width: 150,
          key: 'ervailType',
          title: '进口反补贴税税率',
          render: (h, params) => {
            let ervailType = params.row[params.column.key]
            if (ervailType === '1') {
              return h('a', {
                props: {
                  size: 'small',
                  type: 'primary'
                },
                style: {
                  cursor: 'hand'
                },
                on: {
                  click: () => {
                    this.showErvailType(params.row.codeTS)
                  }
                }
              }, 'Y')
            } else {
              return h('span', '')
            }
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'expTaxRate',
          title: '出口关税税率'
        },
        {
          width: 120,
          tooltip: true,
          key: 'expTempRate',
          title: '出口暂定税率'
        },
        {
          width: 120,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          title: '监管条件',
          key: 'controlMa'
        }
      ],
      defaultColumns: [{key: 'codeTS'}, {key: 'gname'}, {key: 'isRecord'}, {key: 'impDiscountRate'}, {key: 'impOrdinaryRate'},
        {key: 'impTempRate'}, {key: 'accordType'}, {key: 'addTaxRate'}, {key: 'impConsumeRate'}, {key: 'impFundRate'}, {key: 'impSafeguardRate'},
        {key: 'antidumpType'}, {key: 'ervailType'}, {key: 'expTaxRate'}, {key: 'expTempRate'}, {key: 'unit1'}, {key: 'unit2'}, {key: 'controlMa'}]
    }
  }
}
