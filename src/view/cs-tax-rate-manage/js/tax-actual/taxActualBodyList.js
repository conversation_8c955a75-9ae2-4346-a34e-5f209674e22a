import { editStatus } from '@/view/cs-common'
import { columns } from './taxActualBodyColumns'
import { commTaxList } from '../comm/commTaxList'

export const taxActualBodyList = {
  mixins: [commTaxList, columns],
  mounted: function () {
    let me = this
    me.$set(me, 'tableId', me.$route.path + '/body')
    me.$set(me, 'realTotalColumns', me.allColumns)
    me.$set(me, 'defaultColumns', me.defaultColumns)
    me.setShowFields(me.realTotalColumns, true)
    me.loadFunctions('body').then()
  },
  methods: {
    operationEditShow() {
      return this.editDisplay
    }
  },
  computed: {
    editDisplay() {
      if (this.parentConfig.editStatus === editStatus.EDIT) {
        return ''
      }
      return 'none'
    }
  }
}
