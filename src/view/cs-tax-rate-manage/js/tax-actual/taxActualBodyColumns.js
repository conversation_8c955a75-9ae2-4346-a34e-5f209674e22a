import { extendColumns } from '../comm/commColumns'

export const columns = {
  mixins: [extendColumns],
  data() {
    return {
      allColumns: [
        {
          width: 80,
          key: 'gno',
          tooltip: true,
          title: '商品序号'
        },
        {
          width: 100,
          key: 'codeTs',
          tooltip: true,
          title: '商品编码'
        },
        {
          width: 100,
          tooltip: true,
          key: 'dutyMode',
          title: '征免规定',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.levymode)
          }
        },
        {
          width: 180,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 180,
          key: 'gqty',
          title: '数量',
          tooltip: true
        },
        {
          width: 120,
          key: 'gunit',
          title: '单位',
          tooltip: true,
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 120,
          title: '币制',
          tooltip: true,
          key: 'tradeCurrency',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '外汇折算率',
          key: 'exchangeRate'
        },
        {
          width: 180,
          tooltip: true,
          title: '完税价格',
          key: 'dutyValue'
        },
        {
          width: 120,
          tooltip: true,
          key: 'rateType',
          title: '税率计征标准'
        },
        {
          width: 120,
          tooltip: true,
          title: '从价税率',
          key: 'valoremRate'
        },
        {
          width: 120,
          tooltip: true,
          key: 'qtyRate',
          title: '从量税率'
        },
        {
          width: 180,
          key: 'tax',
          title: '税额',
          tooltip: true
        }
      ],
      defaultColumns: []
    }
  }
}
