import { isNullOrEmpty } from '@/libs/util'
import { extendColumns } from '../comm/commColumns'

export const columns = {
  mixins: [extendColumns],
  data() {
    let me = this
    return {
      /**
       * 重写  操作列渲染    重点在于  customize: true,
       * @returns {[{type: string, key: string}, {width: number, title: string, customize: boolean, render: (function(*, *): VNode), key: string}]}
       */
      getDefaultColumns() {
        return [{
          width: 60,
          align: 'center',
          key: 'selection',
          type: 'selection'
        }, {
          width: 116,
          title: '操作',
          customize: true,
          key: 'operation',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                },
                on: {
                  click: () => {
                    me.handleDownloadDutyForm(params.row)
                  }
                }
              }, '下载'),
              h('a', {
                props: {
                  type: 'primary'
                },
                style: {
                  marginLeft: '15px'
                },
                on: {
                  click: () => {
                    me.handleViewByRow(params.row)
                  }
                }
              }, '查看')
            ])
          }
        }]
      },
      allColumns: [
        {
          width: 100,
          title: '状态',
          key: 'downloadStatus'
        },
        {
          width: 180,
          tooltip: true,
          key: 'batchNo',
          title: '批次号'
        },
        {
          width: 180,
          tooltip: true,
          key: 'entryId',
          title: '报关单号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 150,
          tooltip: true,
          title: '监管方式',
          key: 'tradeMode',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.trade)
          }
        },
        {
          width: 130,
          key: 'ddate',
          title: '报关单申报日期',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        },
        {
          width: 180,
          tooltip: true,
          key: 'taxVouNo',
          title: '税费单号'
        },
        {
          width: 120,
          key: 'taxId',
          tooltip: true,
          title: '税费单序号'
        },
        {
          width: 120,
          key: 'emsNo',
          tooltip: true,
          title: '备案号'
        },
        {
          width: 120,
          tooltip: true,
          title: '发票号',
          key: 'invoiceNo'
        },
        {
          width: 150,
          key: 'billNo',
          tooltip: true,
          title: '提运单号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'contrNo',
          title: '合同号'
        },
        {
          width: 130,
          key: 'gname',
          tooltip: true,
          title: '主要商品名称'
        },
        {
          width: 120,
          title: '申报关区',
          key: 'masterCustoms',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
          }
        },
        {
          width: 120,
          key: 'taxType',
          title: '税费种类',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.taxRateManage.TAX_TYPE_MAP)
          }
        },
        {
          width: 150,
          title: '税单生成时间',
          key: 'generationTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 160,
          tooltip: true,
          key: 'bankName',
          title: '支付银行'
        },
        {
          width: 160,
          tooltip: true,
          key: 'payAcct',
          title: '支付银行账户'
        },
        {
          width: 130,
          tooltip: true,
          key: 'payType',
          title: '支付方式',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.taxRateManage.PAY_TYPE_MAP)
          }
        },
        {
          width: 150,
          key: 'taxDate',
          title: '银行扣款时间',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 180,
          tooltip: true,
          title: '支付金额',
          key: 'paymentAmount'
        },
        {
          width: 180,
          tooltip: true,
          title: '完税金额',
          key: 'dutyValue'
        },
        {
          width: 120,
          key: 'sumTaxMark',
          tooltip: true,
          title: '汇总征税标志',
          render: (h, params) => {
            let value = params.row[params.column.key] === 'COLLPAY' ? '是' : params.row[params.column.key]
            return h('span', value)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'operator',
          title: '操作员账号'
        },
        {
          width: 120,
          tooltip: true,
          title: '是否支付',
          key: 'transStatus',
          render: (h, params) => {
            return me.cmbShowRender(h, params, [{
              value: 'N', label: '未支付'
            }, {
              value: 'S', label: '已支付'
            }, {
              value: 'P', label: '支付中'
            }, {
              value: 'A', label: '支付中'
            }])
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'payBkCode',
          title: '支付银行编码'
        },
        {
          width: 120,
          tooltip: true,
          key: 'payOpBkName',
          title: '付款开户行名称'
        },
        {
          width: 120,
          tooltip: true,
          key: 'payOpBkCode',
          title: '付款开户行行号'
        },
        {
          width: 120,
          tooltip: true,
          key: 'delayMark',
          title: '延期标志'
        },
        {
          width: 120,
          tooltip: true,
          key: 'payeeName',
          title: '收款人名称'
        },
        {
          width: 120,
          tooltip: true,
          key: 'incomeName',
          title: '收款银行名称'
        },
        {
          width: 120,
          tooltip: true,
          key: 'incomeSys',
          title: '收入系统'
        },
        {
          width: 120,
          tooltip: true,
          key: 'subjectCode',
          title: '税费种类编码'
        },
        {
          width: 120,
          tooltip: true,
          key: 'subjectName',
          title: '税费种类名称'
        },
        {
          width: 120,
          tooltip: true,
          title: '财政部名称',
          key: 'exchequerName'
        },
        {
          width: 150,
          title: '最后下载时间',
          key: 'lastDownloadTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
          }
        },
        {
          width: 68,
          title: '核对单',
          key: 'checkFinalPath',
          render: (h, params) => {
            let value = params.row[params.column.key]
            if (isNullOrEmpty(value)) {
              return h('span', '')
            } else {
              return h('div', [
                h('a', {
                  props: {
                    type: 'primary'
                  },
                  style: {
                    marginLeft: '15px'
                  },
                  on: {
                    click: () => {
                      if (me.taxAttach) {
                        me.fileDownload(params.row, '0')
                      } else {
                        me.pdfPreview(params.row, '0')
                      }
                    }
                  }
                }, 'Y')
              ])
            }
          }
        },
        {
          width: 68,
          title: '缴款书',
          key: 'payFinalPath',
          render: (h, params) => {
            let value = params.row[params.column.key]
            if (isNullOrEmpty(value)) {
              return h('span', '')
            } else {
              return h('div', [
                h('a', {
                  props: {
                    type: 'primary'
                  },
                  style: {
                    marginLeft: '15px'
                  },
                  on: {
                    click: () => {
                      if (me.taxAttach) {
                        me.fileDownload(params.row, '1')
                      } else {
                        me.pdfPreview(params.row, '1')
                      }
                    }
                  }
                }, 'Y')
              ])
            }
          }
        },{
          width: 120,
          tooltip: true,
          key: 'reimbursementStatus',
          title: '报销状态',
          render: (h, params) => {
            return me.cmbShowRender(h, params, me.taxRateManage.REIMBURSEMENT_STATUS_MAP)
          }
        }
      ],
      defaultColumns: []
    }
  }
}
