import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      allColumns: [
        {
          width: 120,
          title: '税号',
          key: 'codeTS',
          tooltip: true
        },
        {
          width: 120,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 120,
          tooltip: true,
          title: '协定名称',
          key: 'agreementName'
        },
        {
          width: 180,
          tooltip: true,
          key: 'countryName',
          title: '原产国(地区)',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'countryCode', 'countryName')
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '协定税率',
          key: 'impAgreementRate'
        },
        {
          width: 120,
          tooltip: true,
          title: '进口最惠国税率',
          key: 'impDiscountRate'
        },
        {
          width: 120,
          tooltip: true,
          title: '进口普通税率',
          key: 'impOrdinaryRate'
        },
        {
          width: 120,
          tooltip: true,
          key: 'impTempRate',
          title: '进口暂定税率'
        },
        {
          width: 120,
          tooltip: true,
          key: 'impConsumeRate',
          title: '进口消费税税率'
        },
        {
          width: 120,
          tooltip: true,
          key: 'addTaxRate',
          title: '增值税税率'
        },
        {
          width: 150,
          tooltip: true,
          key: 'impFundRate',
          title: '进口废弃电器电子基金'
        },
        {
          width: 120,
          tooltip: true,
          title: '保障措施关税税率',
          key: 'impSafeguardRate'
        },
        {
          width: 120,
          tooltip: true,
          key: 'expTaxRate',
          title: '出口关税税率'
        },
        {
          width: 120,
          tooltip: true,
          key: 'expTempRate',
          title: '出口暂定税率'
        }
      ],
      defaultColumns: [{key: 'codeTS'}, {key: 'gname'}, {key: 'agreementName'}, {key: 'countryName'}, {key: 'impAgreementRate'},
        {key: 'impDiscountRate'}, {key: 'impOrdinaryRate'}, {key: 'impTempRate'}, {key: 'impConsumeRate'}, {key: 'addTaxRate'},
        {key: 'impFundRate'}, {key: 'impSafeguardRate'}, {key: 'expTaxRate'}, {key: 'expTempRate'}]
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
