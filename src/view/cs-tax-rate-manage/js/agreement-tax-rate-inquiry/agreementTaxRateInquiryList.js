import pms from '@/libs/pms'
import { commList } from '@/view/cs-interim-verification/comm/commList'

export const agreementTaxRateInquiryList = {
  mixins: [pms, commList],
  data() {
    return {
      tableId: '',
      tableShow: true,
      showtableColumnSetup: false,
      actions: [{
        needed: true,
        loading: false,
        disabled: false,
        icon: 'ios-cog',
        label: '自定义配置',
        command: 'setting'
      }],
      realTotalColumns: [],
      defaultColumns: [],
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 设置列表显示字段
     * @param totalColumns
     */
    setShowFields(totalColumns) {
      let me = this
      me.tableId = me.$route.path
      if (me.defaultColumns.length > 0) {
        me.gridConfig.gridColumns = me.$bom3.showTableColumns(me.tableId, totalColumns, me.defaultColumns)
      } else {
        me.gridConfig.gridColumns = me.$bom3.showTableColumns(me.tableId, totalColumns)
      }
    },
    getGrdColumns(columns) {
      return columns
    },
    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showtableColumnSetup = true
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.gridConfig.gridColumns = me.getGrdColumns(columns)
      me.gridConfig.exportColumns = []
      // 解决iview table 的问题
      me.tableShow = false
      me.$nextTick(() => {
        me.tableShow = true
      })
    }
  }
}
