import { extendColumns } from '../comm/commColumns'

export const columns = {
  mixins: [extendColumns],
  data() {
    return {
      allColumns: [
        {
          width: 120,
          tooltip: true,
          key: 'emsListNo',
          title: '单据内部编号'
        },
        {
          width: 120,
          key: 'listNo',
          tooltip: true,
          title: '核注清单编号'
        },
        {
          width: 120,
          title: '制单日期',
          key: 'decInsertTime',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 120,
          tooltip: true,
          key: 'entryNo',
          title: '报关单号'
        },
        {
          width: 120,
          title: '申报日期',
          key: 'declareDate',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 130,
          tooltip: true,
          key: 'tradeMode',
          title: '监管方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          width: 180,
          tooltip: true,
          key: 'taxTotalEstimate',
          title: '预估税金合计(RMB)'
        },
        {
          width: 180,
          tooltip: true,
          title: '预估关税(RMB)',
          key: 'dutyValueEstimate'
        },
        {
          width: 180,
          tooltip: true,
          title: '预估增值税(RMB)',
          key: 'taxValueEstimate'
        },
        {
          width: 180,
          tooltip: true,
          title: '预估消费税(RMB)',
          key: 'exciseTaxEstimate'
        },
        {
          width: 180,
          tooltip: true,
          key: 'taxTotal',
          title: '实际税金合计(RMB)'
        },
        {
          width: 180,
          tooltip: true,
          key: 'dutyValue',
          title: '实际关税(RMB)'
        },
        {
          width: 180,
          tooltip: true,
          key: 'taxValue',
          title: '实际增值税(RMB)'
        },
        {
          width: 180,
          tooltip: true,
          key: 'exciseTax',
          title: '实际消费税(RMB)'
        },
        {
          width: 160,
          tooltip: true,
          title: '申报单位',
          key: 'declareName'
        },
        {
          width: 130,
          tooltip: true,
          key: 'dutyType',
          title: '付税方式',
          render: (h, params) => {
            return this.cmbShowRender(h, params, this.taxRateManage.TAX_PAYMENT_MAP)
          }
        },
        {
          width: 120,
          key: 'payDate',
          title: '支付日期',
          render: (h, params) => {
            return this.dateTimeShowRender(h, params)
          }
        },
        {
          width: 150,
          tooltip: true,
          title: '制单员',
          key: 'updateUserName'
        },
        {
          width: 200,
          key: 'note',
          title: '备注',
          tooltip: true
        }
      ],
      defaultColumns: []
    }
  }
}
