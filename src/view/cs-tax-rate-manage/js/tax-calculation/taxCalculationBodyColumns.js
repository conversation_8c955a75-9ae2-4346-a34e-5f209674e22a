import { extendColumns } from '../comm/commColumns'

export const columns = {
  mixins: [extendColumns],
  data() {
    return {
      allColumns: [
        {
          width: 100,
          tooltip: true,
          key: 'serialNo',
          title: '报关序号'
        },
        {
          width: 70,
          key: 'gno',
          tooltip: true,
          title: '备案序号'
        },
        {
          width: 130,
          key: 'gname',
          tooltip: true,
          title: '商品名称'
        },
        {
          width: 100,
          key: 'codeTS',
          tooltip: true,
          title: '商品编码'
        },
        {
          title: '预估税金(RMB)',
          children: [
            {
              width: 120,
              tooltip: true,
              title: '完税价格',
              key: 'taxTotalEstimate'
            },
            {
              width: 120,
              tooltip: true,
              title: '关税率',
              key: 'tariffRatEstimate'
            },
            {
              width: 90,
              title: '关税',
              tooltip: true,
              key: 'dutyValueEstimate'
            },
            {
              width: 90,
              tooltip: true,
              title: '增值税',
              key: 'taxValueEstimate'
            },
            {
              width: 90,
              tooltip: true,
              title: '消费税',
              key: 'exciseTaxEstimate'
            },
            {
              width: 90,
              title: '汇率',
              tooltip: true,
              key: 'rateEstimate'
            },
            {
              width: 90,
              title: '关税类型',
              tooltip: true,
              key: 'taxType'
            }
          ]
        },
        {
          title: '实际税金(RMB)',
          children: [
            {
              width: 120,
              tooltip: true,
              key: 'taxTotal',
              title: '完税价格',
            },
            {
              width: 120,
              tooltip: true,
              title: '关税率',
              key: 'tariffRat'
            },
            {
              width: 90,
              title: '关税',
              tooltip: true,
              key: 'dutyValue'
            },
            {
              width: 90,
              tooltip: true,
              title: '增值税',
              key: 'taxValue'
            },
            {
              width: 90,
              tooltip: true,
              title: '消费税',
              key: 'exciseTax'
            },
            {
              width: 90,
              key: 'rate',
              title: '汇率',
              tooltip: true
            }
          ]
        },
        {
          width: 80,
          key: 'qty',
          tooltip: true,
          title: '申报数量'
        },
        {
          width: 120,
          key: 'unit',
          tooltip: true,
          title: '单报单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 90,
          tooltip: true,
          key: 'decPrice',
          title: '申报单价'
        },
        {
          width: 90,
          tooltip: true,
          key: 'decTotal',
          title: '申报总价'
        },
        {
          width: 120,
          key: 'curr',
          title: '币制',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
          }
        },
        {
          width: 80,
          key: 'qty1',
          tooltip: true,
          title: '法一数量'
        },
        {
          width: 120,
          key: 'unit1',
          tooltip: true,
          title: '法一单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 80,
          key: 'qty2',
          tooltip: true,
          title: '法二数量'
        },
        {
          width: 120,
          key: 'unit2',
          tooltip: true,
          title: '法二单位',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.unit)
          }
        },
        {
          width: 150,
          tooltip: true,
          title: '原产国',
          key: 'originCountry',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.country_outdated)
          }
        }
      ],
      defaultColumns: []
    }
  }
}
