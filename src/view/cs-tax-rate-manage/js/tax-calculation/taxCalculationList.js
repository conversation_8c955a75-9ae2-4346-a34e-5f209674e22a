import ImportPage from 'xdo-import'
import { isNullOrEmpty } from '@/libs/util'
import { taxRateManage } from '@/view/cs-common'
import { columns } from './taxCalculationColumns'
import { commTaxList } from '../comm/commTaxList'
import DefendPayment from '../../components/tax-calculation/defend-payment'

export const taxCalculationList = {
  mixins: [columns, commTaxList],
  components: {
    ImportPage,
    DefendPayment
  },
  data() {
    let headImportConfig = this.getCommImportConfig('TAX-HEAD-I-UPDATE'),
      listImportConfig = this.getCommImportConfig('TAX-LIST-I-UPDATE')
    return {
      // 查询条件行数
      searchLines: 3,
      gridConfig: {
        exportTitle: '税金计算'
      },
      toolbarEventMap: {
        'edit': this.handleEdit,                                   // 编辑
        'defend-payment': this.handleDefendPayment,                // 维护支付方式
        'export': this.handleDownload,                             // 导出
        'estimated-tax': this.handleEstimated,                     // 预估税金
        'generate-actual-taxes': this.handleGenerateActualTaxes,   // 自动生成实际税金
        'clear-actual-taxes': this.handleClearActualTaxes          // 清空实际税金
      },
      importHeadShow: false,
      importHeadConfig: {
        importKey: '',
        Config: headImportConfig
      },
      importListShow: false,
      importListConfig: {
        importKey: '',          // 任务编码
        Config: listImportConfig
      },
      defendPaymentShow: false,
      taxRateManage: taxRateManage
    }
  },
  mounted: function () {
    let me = this
    me.$set(me, 'tableId', me.$route.path)
    me.$set(me, 'realTotalColumns', me.allColumns)
    me.$set(me, 'defaultColumns', me.defaultColumns)
    me.setShowFields(me.realTotalColumns)
    me.loadFunctions().then()
  },
  methods: {
    /**
     * 导入汇总
     */
    uploadSum() {
      this.importHeadShow = true
    },
    /**
     * 导入表体
     */
    uploadBody() {
      this.importListShow = true
    },
    afterImport() {
      this.importHeadShow = false
      this.importListShow = false
      this.getList()
    },
    /**
     * 维护支付方式
     */
    handleDefendPayment() {
      let me = this
      if (me.checkRowSelected('维护支付方式')) {
        let defendedRows = me.gridConfig.selectRows.filter(row => {
          return !isNullOrEmpty(row.dutyType) || !isNullOrEmpty(row.payDate)
        })
        if (defendedRows.length > 0) {
          me.$Modal.confirm({
            okText: '是',
            title: '提醒',
            cancelText: '否',
            content: '已存在维护的信息，是否重新维护',
            onOk: () => {
              me.defendPaymentShow = true
            }
          })
        } else {
          me.defendPaymentShow = true
        }
      }
    },
    /**
     * 维护支付方式
     * @param payInfo
     */
    onDefendPayment(payInfo) {
      let me = this
      me.$http.post(me.ajaxUrl.defendPayment, payInfo).then(() => {
        me.$Message.success('维护支付方式成功!')
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    },
    /**
     * 下载
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 预估税金
     */
    handleEstimated() {
      let me = this
      if (me.checkRowSelected('预估税金')) {
        me.setToolbarLoading('estimated-tax', true)
        let params = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.taxEstimated, {
          sids: params
        }).then(() => {
          me.$Message.success({
            duration: 5,
            content: '预估税金成功!'
          })
          me.getList()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('estimated-tax')
        })
      }
    },
    /**
     * 自动生成实际税金
     */
    handleGenerateActualTaxes() {
      let me = this
      if (me.checkRowSelected('自动生成实际税金')) {
        me.setToolbarLoading('generate-actual-taxes', true)
        let params = me.getSelectedParams()
        me.$http.post(me.ajaxUrl.generateActualTaxes, {
          sids: params
        }).then(() => {
          me.$Message.success('自动生成实际税金成功!')
        }).catch(() => {
        }).finally(() => {
          me.getList()
          me.setToolbarLoading('generate-actual-taxes')
        })
      }
    },
    /**
     * 清空实际税金
     */
    handleClearActualTaxes() {
      let me = this
      if (me.checkRowSelected('清空实际税金')) {
        me.$Modal.confirm({
          okText: '是',
          title: '提醒',
          loading: true,
          cancelText: '否',
          content: '确定清空实际税金信息?',
          onOk: () => {
            me.setToolbarLoading('clear-actual-taxes', true)
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.clearActualTaxes, {
              sids: params
            }).then(() => {
              me.$Message.success('清空实际税金成功!')
            }).catch(() => {
            }).finally(() => {
              me.getList()
              setTimeout(() => {
                me.$Modal.remove()
                me.setToolbarLoading('clear-actual-taxes')
              }, 150)
            })
          }
        })
      }
    }
  }
}
