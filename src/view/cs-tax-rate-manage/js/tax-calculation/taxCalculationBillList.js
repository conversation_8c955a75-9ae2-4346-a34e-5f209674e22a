import ImportPage from 'xdo-import'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from '@/view/cs-common'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import taxCalculationBillEdit from '../../tax-calculation/tax-calculation-bill-edit'

export const taxCalculationBillList = {
  mixins: [baseSearchConfig, baseListConfig],
  props: {
    parentConfig: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    ImportPage,
    taxCalculationBillEdit
  },
  data() {
    let params = this.getCommParams()
    let fields = this.getCommFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      cmbSource: {
        gmark: [{
          value: 'I',
          label: '料件'
        }, {
          value: 'E',
          label: '成品'
        }]
      },
      hasChildTabs: true,
      toolbarEventMap: {
        'edit': this.handleEdit
      }
    }
  },
  created: function () {
    let me = this,
      showColumns = [],
      rootId = me.$route.path + '/' + me.$options.name
    me.$set(me, 'listId', rootId + '/listId')
    if (Array.isArray(me.defaultFields) && me.defaultFields.length > 0) {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields, me.defaultFields)
    } else {
      showColumns = me.$bom3.showTableColumns(me.listId, me.baseFields)
    }
    me.handleUpdateColumn(showColumns)
  },
  mounted() {
    let me = this
    me.loadFunctions('bill').then(() => {
      if (me.editDisable) {
        me.setToolbarProperty('edit', 'disabled', true)
      }
    })
  },
  computed: {
    /**
     * 动态标签
     */
    dynamicLabel() {
      return {}
    },
    editDisable() {
      return this.parentConfig.editStatus !== editStatus.EDIT
    },
    exportHeader() {
      let columns = this.listConfig.columns.filter(column => {
        return ['serialNo', 'facGNo', 'gname', 'taxPrice', 'dutyValue', 'taxValue', 'exciseTax', 'dutyValueInterest',
          'taxValueInterest', 'otherTax', 'inputDate'].includes(column.key)
      })
      columns.splice(2, 0, {
        width: 120,
        key: 'qty',
        title: '申报数量'
      })
      return columns.map(theCol => {
        return {
          key: theCol.key,
          value: theCol.title
        }
      })
    }
  },
  methods: {
    getCommParams() {
      return [{
        key: 'facGNo',
        title: '企业料号'
      }, {
        key: 'copGNo',
        title: '备案料号'
      }, {
        key: 'gmark',
        type: 'select',
        title: '物料类型',
        props: {
          optionLabelRender: (opt) => opt.label
        },
      }, {
        key: 'codeTS',
        title: '商品编码'
      }, {
        type: 'pcode',
        title: '原产国',
        key: 'originCountry',
        props: {
          meta: 'COUNTRY_OUTDATED'
        }
      }]
    },
    getCommFields() {
      return [{
        width: 120,
        title: '顺序号',
        key: 'serialNo'
      }, {
        width: 120,
        key: 'facGNo',
        title: '企业料号'
      }, {
        width: 120,
        key: 'copGNo',
        title: '备案料号'
      }, {
        width: 90,
        key: 'gmark',
        title: '物料类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.cmbSource.gmark)
        }
      }, {
        width: 120,
        key: 'gname',
        title: '商品名称'
      }, {
        width: 100,
        key: 'codeTS',
        title: '商品编码'
      }, {
        width: 160,
        key: 'taxPrice',
        title: '完税价格'
      }, {
        width: 120,
        title: '关税',
        key: 'dutyValue'
      }, {
        width: 160,
        title: '增值税',
        key: 'taxValue'
      }, {
        width: 200,
        title: '消费税',
        key: 'exciseTax'
      }, {
        width: 120,
        title: '关税缓税利息',
        key: 'dutyValueInterest'
      }, {
        width: 120,
        title: '增值税缓税利息',
        key: 'taxValueInterest'
      }, {
        width: 120,
        key: 'otherTax',
        title: '其他进口环节税'
      }, {
        width: 120,
        key: 'taxTotal',
        title: '税金汇总'
      }, {
        width: 120,
        key: 'qty',
        title: '申报数量'
      }, {
        width: 120,
        key: 'unit',
        title: '申报单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 120,
        key: 'decPrice',
        title: '申报单价'
      }, {
        width: 120,
        key: 'decTotal',
        title: '申报总价'
      }, {
        width: 120,
        key: 'curr',
        title: '币制',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.curr_outdated)
        }
      }, {
        width: 160,
        key: 'qty1',
        title: '法一数量'
      }, {
        width: 120,
        key: 'unit1',
        title: '法一单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 160,
        key: 'qty2',
        tooltip: true,
        title: '法二数量'
      }, {
        width: 120,
        key: 'unit2',
        title: '法二单位',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.unit)
        }
      }, {
        width: 120,
        title: '原产国',
        key: 'originCountry',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }
      }, {
        width: 120,
        key: 'inputDate',
        title: '税金维护日期'
      }]
    },
    handleTableColumnSetup() {
      this.listSetupShow = true
    },
    /**
     * 自定义列变更
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.colOptions = true
      if (me.listConfig.colOptions) {
        me.$set(me.listConfig, 'columns', [...me.getDefaultColumns(), ...columns])
      } else {
        me.$set(me.listConfig, 'columns', columns)
      }
      me.listSetupShow = false
    },
    afterImport() {
      let me = this
      me.importShow = false
      me.handleSearchSubmit()
    },
    downLoadModify() {
      //  修改导出
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    uploadModify() {
      //  修改导入
      let me = this
      me.importShow = true
    },
    getSearchParams() {
      // 获取查询条件
      let me = this,
        paramObj = Object.assign({}, deepClone(me.searchConfig.model), {
          headId: me.parentConfig.editData.sid
        })
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    getTotalData() {
      let me = this
      me.$http.post(me.ajaxUrl.totalUrl, {
        headId: me.parentConfig.editData.sid
      }).then(res => {
        me.totalContent = res.data.data
      }).catch(() => {
      })
    },
    afterSearchSuccess() {
      this.getTotalData()
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      let me = this
      if (isNullOrEmpty(row.sid)) {
        me.handleAdd()
        me.editConfig.editData = row
      } else {
        if (me.customCheck([row], '编辑')) {
          me.showList = false
          me.editConfig.editData = row
          me.editConfig.editStatus = editStatus.EDIT
        }
      }
    }
  }
}
