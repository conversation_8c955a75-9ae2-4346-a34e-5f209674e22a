import ImportPage from 'xdo-import'
import { editStatus } from '@/view/cs-common'
import { commTaxList } from '../comm/commTaxList'
import { columns } from './taxCalculationBodyColumns'

export const taxCalculationBodyList = {
  mixins: [commTaxList, columns],
  components: {
    ImportPage
  },
  props: {
    parentConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data() {
    let importConfig = this.getCommImportConfig('')
    return {
      // 有子tab组件
      hasChildTabs: true,
      gridConfig: {
        exportTitle: ''
      },
      importShow: false,
      importConfig: importConfig,
      toolbarEventMap: {
        'import': this.handleImport,        // 导入
        'export': this.handleDownload       // 导出
      }
    }
  },
  mounted: function () {
    let me = this
    me.$set(me, 'tableId', me.$route.path + '/body')
    me.$set(me, 'realTotalColumns', me.allColumns)
    me.$set(me, 'defaultColumns', me.defaultColumns)
    me.setShowFields(me.realTotalColumns, true)
    me.loadFunctions('body').then(() => {
      let setIndex = me.actions.findIndex(it => it.command === 'setting')
      if (setIndex > -1) {
        me.actions.splice(setIndex, 1)
      }
      let importIndex = me.actions.findIndex(it => it.command === 'import')
      if (importIndex > -1) {
        me.actions.splice(importIndex, 1)
      }
    })
  },
  computed: {
    editDisplay() {
      if (this.parentConfig.editStatus === editStatus.EDIT) {
        return ''
      }
      return 'none'
    }
  },
  watch: {
    parentConfig: {
      deep: true,
      immediate: true,
      handler: function (config) {
        this.$set(this.editConfig, 'headData', config.editData)
        this.$set(this.editConfig, 'headId', config.editData.sid)
        this.$set(this.editConfig, 'editStatus', config.editData.editStatus)
      }
    }
  },
  methods: {
    /**
     * 获取查询条件
     */
    getSearchParams() {
      return Object.assign({
        headId: this.editConfig.headId
      }, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
    },
    /**
     * 导入
     */
    handleImport() {
      this.importShow = true
    },
    afterImport() {
      this.importShow = false
      this.getList()
    },
    /**
     * 下载
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    operationEditShow() {
      return this.editDisplay
    }
  }
}
