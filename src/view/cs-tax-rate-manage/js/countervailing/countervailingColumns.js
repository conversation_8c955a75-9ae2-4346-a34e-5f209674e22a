import { isNullOrEmpty } from '@/libs/util'
import { baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

export const columns = {
  mixins: [baseColumns],
  data() {
    return {
      allColumns: [
        {
          width: 180,
          tooltip: true,
          key: 'countryName',
          title: '原产国(地区)',
          render: (h, params) => {
            return this.keyValueRender(h, params, 'countryCode', 'countryName')
          }
        },
        {
          width: 120,
          tooltip: true,
          title: '原厂商(中文)',
          key: 'companyNameCn'
        },
        {
          width: 120,
          tooltip: true,
          title: '原厂商(英文)',
          key: 'companyNameEn'
        },
        {
          width: 120,
          tooltip: true,
          key: 'impErvailRate',
          title: '进口反补贴税税率'
        }
      ],
      gridConfig: {
        data: [],
        selectData: [],
        selectRows: [],
        gridColumns: []
      },
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1,
        pageSizeOpts: [10, 20, 50, 100]
      }
    }
  },
  methods: {
    keyValueRender(h, params, key, value) {
      let keyVal = params.row[key]
      let valueVal = params.row[value]
      let showVal = ''
      if (!isNullOrEmpty(keyVal)) {
        showVal = keyVal
      }
      if (!isNullOrEmpty(valueVal)) {
        showVal += ' ' + valueVal
      }
      return this.toolTipRender(h, showVal.trim())
    }
  }
}
