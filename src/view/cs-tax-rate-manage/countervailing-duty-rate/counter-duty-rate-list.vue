<template>
  <section>
    <XdoCard :bordered="false">
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="realGrdHeight"
                :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
      <div ref="area_page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { columns } from './../js/countervailing/countervailingColumns'

  export default {
    name: 'counter-duty-rate-list',
    mixins: [columns],
    props: {
      defaultParams: {
        type: Object,
        required: true,
        default: () => ({})
      },
      grdHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        totalColumns: [],
        columnsConfig: []
      }
    },
    mounted() {
      let me = this
      me.totalColumns = [...me.allColumns]
      me.totalColumns.forEach(item => {
        me.columnsConfig.push(item.key)
      })
      me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, me.columnsConfig)
    },
    watch: {
      defaultParams: {
        deep: true,
        immediate: true,
        handler: function (params) {
          if (params && params.hasOwnProperty('codeTS')) {
            let me = this
            me.gridConfig.data = []
            me.$nextTick(() => {
              me.handleSubmit()
            })
          }
        }
      }
    },
    computed: {
      realGrdHeight() {
        if (this.grdHeight > 0) {
          return this.grdHeight
        }
        return this.dynamicHeight
      }
    },
    methods: {
      handleSubmit() {
        let me = this
        me.$http.post(csAPI.taxRateManage.TariffErvail.selectAllPaged, {
          codeTS: me.defaultParams.codeTS
        }, {
          params: {
            page: me.pageParam.page,
            limit: me.pageParam.limit
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.dataTotal = res.data.total
        })
      },
      // 分页触发
      pageChange(val) {
        let me = this
        me.pageParam.page = val
        me.handleSubmit()
      },
      pageSizeChange(val) {
        let me = this
        me.pageParam.limit = val
        if (me.pageParam.page === 1) {
          me.handleSubmit()
        }
      }
    }
  }
</script>

<style scoped>
</style>
