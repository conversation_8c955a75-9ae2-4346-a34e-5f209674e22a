<template>
  <XdoModal v-model="show" mask width="570" title="进口反补贴税税率"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <CounterDutyRateList :defaultParams="defaultParams" :grdHeight="400"></CounterDutyRateList>
  </XdoModal>
</template>

<script>
  import CounterDutyRateList from './counter-duty-rate-list'

  export default {
    name: 'counter-duty-rate-pop',
    components: {
      CounterDutyRateList
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      defaultParams: {
        type: Object,
        required: true,
        default: () => ({})
      }
    },
    methods: {
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style scoped>
</style>
