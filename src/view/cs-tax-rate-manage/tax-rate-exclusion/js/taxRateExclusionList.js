import taxRateExclusionEdit from '../tax-rate-exclusion-edit'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const taxRateExclusionList = {
  name: 'taxRateExclusionList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  components: {
    taxRateExclusionEdit
  },
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      cmbSource: {
        status: [{
          value: '0', label: '无效'
        }, {
          value: '1', label: '有效'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'export': this.handleDownload,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  methods: {
    /**
     * 查询条件
     * @returns {{title: string, key: string}[]}
     */
    getParams() {
      return [{
        key: 'codeTS',
        title: 'HS 编码'
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 列表字段
     * @returns {({width: number, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string}|{cellRendererFramework, width: number, tooltip: boolean, title: string, key: string}|{cellRendererFramework, width: number, tooltip: boolean, title: string, key: string}|{cellRendererFramework, width: number, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        width: 92,
        key: 'codeTS',
        title: 'HS 编码'
      }, {
        width: 68,
        title: '状态',
        key: 'status',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.status)
        })
      }, {
        flex: 1,
        width: 150,
        key: 'note',
        title: '备注',
        tooltip: true,
        cellRendererFramework: me.baseCellRenderer(null, true)
      }, {
        width: 260,
        tooltip: true,
        title: '录入人',
        key: 'userName',
        // key: 'insertUserName',
        // cellRendererFramework: me.baseCellRenderer(function (h, params) {
        //   return me.keyValueRender(h, params, 'insertUser', 'insertUserName')
        // }, true)
      }, {
        width: 88,
        title: '录入日期',
        key: 'insertTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
