<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab">
      <TabPane name="headTab" label="表头">
        <Head ref="head" @onEditBack="editBack" :edit-config="editConfig"></Head>
      </TabPane>
      <TabPane name="bodyTab" v-if="showBody" label="报关单税金">
        <Body ref="body" v-if="tabs.bodyTab" :parent-config="parentConfig"></Body>
      </TabPane>
      <TabPane name="preEnteredTab" v-if="preEnteredTabShow" label="预录入单税金">
        <Bill ref="bill" v-if="tabs.preEnteredTab" :parent-config="parentConfig"></Bill>
      </TabPane>
      <template v-slot:extra>
        <XdoButton type="text" @click="backToList"><XdoIcon type="ios-undo" size="22" style="color: green;" /></XdoButton>
      </template>
    </XdoTabs>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import Head from './tax-calculation-edit'
  import { isNullOrEmpty } from '@/libs/util'
  import { editStatus } from '@/view/cs-common'
  import Body from './tax-calculation-body-list'
  import Bill from './tax-calculation-bill-list'

  export default {
    name: 'bondedEHeadTabs',
    components: {
      Head,
      Body,
      Bill
    },
    mixins: [pms],
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {
            sid: ''
          },
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        actions: [],
        tabName: 'headTab',
        tabs: {
          headTab: true,
          bodyTab: false,
          preEnteredTab: false
        },
        ajaxUrl: {
          getRecordBySid: csAPI.taxRateManage.taxCalculation.imports.head.selectAllPaged
        }
      }
    },
    created: function () {
      let me = this
      me.loadFunctions('tabs').then()
    },
    watch: {
      tabName(value) {
        let me = this
        me.tabs[value] = true
        if (value === 'headTab') {
          if (!isNullOrEmpty(me.editConfig.editData.sid)) {
            me.$http.post(me.ajaxUrl.getRecordBySid, {
              sid: me.editConfig.editData.sid
            }).then(res => {
              if (Array.isArray(res.data.data) && res.data.data.length === 1) {
                me.editConfig.editData = res.data.data[0]
              }
            }).catch(() => {
            })
          }
        }
      }
    },
    computed: {
      parentConfig() {
        let me = this
        return {
          editData: me.editConfig.editData,
          editStatus: me.editConfig.editStatus
        }
      },
      showBody() {
        let me = this
        return me.editConfig.editStatus === editStatus.EDIT || me.editConfig.editStatus === editStatus.SHOW
      },
      preEnteredTabShow() {
        let me = this,
          preEnteredTabs = me.actions.filter(action => {
            return action.command === 'preEnteredTab'
          })
        if (Array.isArray(preEnteredTabs) && preEnteredTabs.length > 0) {
          return me.showBody
        }
        return false
      }
    },
    methods: {
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      }
    }
  }
</script>

<style lang="less" scoped>
  .spin-content {
    width: 100%;
    background: #fff;
    min-height: 300px;
    position: relative;
  }
</style>
