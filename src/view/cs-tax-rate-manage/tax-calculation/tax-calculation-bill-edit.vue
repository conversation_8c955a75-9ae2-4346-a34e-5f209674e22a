<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="110"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <XdoButton v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                   @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</XdoButton>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { editStatus } from '@/view/cs-acustomization/shengYi/api/constant'

  export default {
    name: 'taxCalculationBillEdit',
    mixins: [baseDetailConfig],
    props: {
      inSource: {
        type: Object,
        default: () => ({})
      },
      parentConfig: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      let btnComm = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        formName: 'frmData',
        ajaxUrl: {
          update: csAPI.taxRateManage.taxCalculation.imports.bill.update,
        },
        buttons: [
          {...btnComm, label: '保存', type: 'primary', command: 'save', click: this.handleSave},
          {...btnComm, label: '返回', type: 'warning', command: 'cancel', click: this.handleBack}
        ]
      }
    },
    computed: {
      /**
       * 动态数据源
       * @returns {*}
       */
      dynamicSource() {
        return {
          ...this.inSource
        }
      },
      inputDate() {
        let date = new Date()
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let day = date.getDate()
        return `${year}-${month}-${day}`
      }
    },
    mounted() {
      this.buttons[0].disabled = this.editConfig.editStatus === editStatus.SHOW
    },
    methods: {
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded(flag) {
        let me = this
        if (flag) {
          let fields = me.getFields().map(field => {
            return field.key
          })
          fields.forEach(key => {
            if ('inputDate' !== key && me.editConfig.editData.hasOwnProperty(key)) {
              me.$set(me.detailConfig.model, key, me.editConfig.editData[key])
            }
          })
          if (isNullOrEmpty(me.detailConfig.model.inputDate)) {
            me.$set(me.detailConfig.model, 'inputDate', me.inputDate)
          }
        } else {
          if (isNullOrEmpty(me.editConfig.editData.sid)) {
            let fields = me.getFields().map(field => {
              return field.key
            })
            fields.forEach(key => {
              if ('inputDate' !== key && me.editConfig.editData.hasOwnProperty(key)) {
                me.$set(me.detailConfig.model, key, me.editConfig.editData[key])
              }
            })
          }
        }
      },
      getFields() {
        return [
          {
            title: '顺序号',
            key: 'serialNo',
            props: {
              disabled: true
            }
          },
          {
            title: '企业料号',
            key: 'facGNo',
            props: {
              disabled: true
            }
          }, {
            title: '商品名称',
            key: 'gname',
            props: {
              disabled: true
            }
          }, {
            title: '申报数量',
            key: 'qty',
            props: {
              disabled: true
            }
          }, {
            title: '完税价格',
            key: 'taxPrice',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '关税',
            key: 'dutyValue',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '增值税',
            key: 'taxValue',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '消费税',
            key: 'exciseTax',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '关税缓税利息',
            key: 'dutyValueInterest',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '增值税缓税利息',
            key: 'taxValueInterest',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            title: '其他进口环节税',
            key: 'otherTax',
            type: 'xdoInput',
            props: {
              intDigits: 9,
              precision: 2,
            }
          }, {
            key: 'inputDate',
            type: 'datePicker',
            title: '税金维护日期'
          }]
      },
      handleSave() {
        let me = this
        me.$refs[me.formName].validate().then(isValid => {
          if (isValid) {
            let data = Object.assign({}, me.detailConfig.model, {
              sid: me.editConfig.editData.sid,
              headId: me.parentConfig.editData.sid,
              erpListId: me.editConfig.editData.erpListId
            })
            me.$http.post(me.ajaxUrl.update, data).then(res => {
              me.$Message.success(res.data.message)
              me.detailConfig.model = res.data.data
              me.handleBack()
            }).catch(() => {
            })
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }
</style>
