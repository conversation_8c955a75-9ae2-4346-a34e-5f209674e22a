<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="申报日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareName" label="申报单位">
        <XdoIInput type="text" v-model="searchParam.declareName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="dutyType" label="付税方式">
        <xdo-select v-model="searchParam.dutyType" :options="this.taxRateManage.TAX_PAYMENT_MAP" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="支付日期" @onDateRangeChanged="handlePayDateChange"></dc-dateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { taxRateManage } from '@/view/cs-common'

  export default {
    name: 'taxCalculationSearch',
    data() {
      return {
        searchParam: {
          emsListNo: '',
          entryNo: '',
          declareDateFrom: '',
          declareDateTo: '',
          tradeMode: '',
          declareName: '',
          dutyType: '',
          payDateFrom: '',
          payDateTo: ''
        },
        taxRateManage: taxRateManage
      }
    },
    methods: {
      /**
       * 申报日期范围
       * @param values
       */
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'declareDateFrom', values[0])
          this.$set(this.searchParam, 'declareDateTo', values[1])
        } else {
          this.$set(this.searchParam, 'declareDateFrom', '')
          this.$set(this.searchParam, 'declareDateTo', '')
        }
      },
      /**
       * 支付日期范围
       * @param values
       */
      handlePayDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'payDateFrom', values[0])
          this.$set(this.searchParam, 'payDateTo', values[1])
        } else {
          this.$set(this.searchParam, 'payDateFrom', '')
          this.$set(this.searchParam, 'payDateTo', '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
