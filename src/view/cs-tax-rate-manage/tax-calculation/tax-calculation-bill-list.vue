<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" label-width="110">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:batchUpdate>
            <Dropdown trigger="click">
              <XdoButton type="text" style="font-size: 12px; width: 95px;">
                <XdoIcon type="ios-build-outline" size="22" class="xdo-icon"/>批量修改<XdoIcon type="ios-arrow-down"></XdoIcon>
              </XdoButton>
              <DropdownMenu slot="list">
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-download" @click="downLoadModify">
                    <XdoIcon type="ios-cloud-download-outline" size="22" class="xdo-icon"/>  修改导出
                  </XdoButton>
                </DropdownItem>
                <DropdownItem style="padding: 0; margin: 0;">
                  <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadModify">
                    <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>  修改导入
                  </XdoButton>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight" :disable="editDisable"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page" style="height: 26px; overflow: hidden;">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
          <span style="position: relative; top: -25px; float: right; margin-right: 80px; font-weight: bold;">{{totalContent}}</span>
        </div>
      </XdoCard>
    </div>
    <taxCalculationBillEdit v-if="!showList" :editConfig="editConfig" :inSource="cmbSource" :parent-config="parentConfig"
                            @onEditBack="editBack"></taxCalculationBillEdit>
    <ImportPage :importKey="importKey" :importShow.sync="importShow" :importConfig="importConfig"
                @onImportSuccess="afterImport"></ImportPage>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { dynamicImport } from '@/view/cs-common/dynamic-import/dynamicImport'
  import { taxCalculationBillList } from '../js/tax-calculation/taxCalculationBillList'

  export default {
    name: 'taxCalculationBillList',
    mixins: [taxCalculationBillList, dynamicImport],
    data() {
      let importConfig = this.getCommImportConfig('TAX-DEC-LIST-I-UPDATE', {
        headId: this.parentConfig.editData.sid
      })
      return {
        cmbSource: {},
        searchLines: 2,
        totalContent: '',
        defaultFields: [],
        importShow: false,
        listConfig: {
          exportTitle: '税金预录入单'
        },
        importConfig: importConfig,
        importKey: 'TAX-DEC-LIST-I-UPDATE',
        ajaxUrl: {
          batchUpdate: csAPI.csImportExport.decErpIListN.batchUpdate,
          totalUrl: csAPI.taxRateManage.taxCalculation.imports.bill.total,
          exportUrl: csAPI.taxRateManage.taxCalculation.imports.bill.exportUrl,
          selectAllPaged: csAPI.taxRateManage.taxCalculation.imports.bill.selectAllPaged
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
