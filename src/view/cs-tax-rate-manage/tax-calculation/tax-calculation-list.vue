<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <TaxCalculationSearch ref="headSearch"></TaxCalculationSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
            <template v-slot:import>
              <Dropdown trigger="click">
                <XdoButton type="text" style="font-size: 12px; width: 95px;">
                  <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>导入<XdoIcon type="ios-arrow-down"></XdoIcon>
                </XdoButton>
                <DropdownMenu slot="list">
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadSum">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>汇总税金
                    </XdoButton>&nbsp;
                  </DropdownItem>
                  <DropdownItem style="padding: 0; margin: 0;">
                    <XdoButton type="text" style="font-size: 12px; width: 100%;" class="xdo-btn-upload" @click="uploadBody">
                      <XdoIcon type="ios-cloud-upload-outline" size="22" class="xdo-icon"/>报关单税金
                    </XdoButton>
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
          </xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TaxCalculationTabs v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></TaxCalculationTabs>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="realTotalColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
    <ImportPage :importShow.sync="importHeadShow" :importKey="importHeadConfig.importKey" :importConfig="importHeadConfig.Config"
                @onImportSuccess="afterImport"></ImportPage>
    <ImportPage :importShow.sync="importListShow" :importKey="importListConfig.importKey" :importConfig="importListConfig.Config"
                @onImportSuccess="afterImport"></ImportPage>
    <DefendPayment :show.sync="defendPaymentShow" :selected="selected"
                   @onConfirm="onDefendPayment"></DefendPayment>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import TaxCalculationTabs from './tax-calculation-tabs'
  import TaxCalculationSearch from './tax-calculation-search'
  import { taxCalculationList } from '../js/tax-calculation/taxCalculationList'

  export default {
    name: 'taxCalculationList',
    components: {
      TaxCalculationTabs,
      TaxCalculationSearch
    },
    mixins: [taxCalculationList],
    data() {
      return {
        ajaxUrl: {
          exportUrl: csAPI.taxRateManage.taxCalculation.imports.head.exportUrl,
          taxEstimated: csAPI.taxRateManage.taxCalculation.imports.head.taxEstimated,
          defendPayment: csAPI.taxRateManage.taxCalculation.imports.head.defendPayment,
          selectAllPaged: csAPI.taxRateManage.taxCalculation.imports.head.selectAllPaged,
          clearActualTaxes: csAPI.taxRateManage.taxCalculation.imports.head.clearActualTaxes,
          generateActualTaxes: csAPI.taxRateManage.taxCalculation.imports.head.generateActualTaxes
        }
      }
    },
    computed: {
      selected() {
        return this.getSelectedParams()
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
