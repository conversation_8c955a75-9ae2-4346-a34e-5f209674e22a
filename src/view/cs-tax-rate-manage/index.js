import { namespace } from '@/project'
// 实际税金
import TaxActualList from './tax-actual/tax-actual-list'
// 税金计算
import TaxCalculationList from './tax-calculation/tax-calculation-list'
// 税率查询
import TaxRateInquiryList from './tax-rate-inquiry/tax-rate-inquiry-list'
// 实际税金明细
import ActualTaxDetailsList from './actual-tax-details/actual-tax-details-list'
// 税率排除目录
import taxRateExclusionList from './tax-rate-exclusion/tax-rate-exclusion-list'
// 协定税率查询
import AgreementTaxRateInquiryList from './agreement-tax-rate-inquiry/agreement-tax-rate-inquiry-list'

export default [
  {
    path: '/' + namespace + '/taxRateManage/taxRateInquiry',
    name: 'taxRateInquiryList',
    meta: {
      icon: 'ios-document',
      title: '税率查询'
    },
    component: TaxRateInquiryList
  },
  {
    path: '/' + namespace + '/taxRateManage/agreementTaxRateInquiry',
    name: 'agreementTaxRateInquiryList',
    meta: {
      icon: 'ios-document',
      title: '协定税率查询'
    },
    component: AgreementTaxRateInquiryList
  },
  {
    path: '/' + namespace + '/taxRateManage/taxCalculation',
    name: 'taxCalculationList',
    meta: {
      icon: 'ios-document',
      title: '税金计算'
    },
    component: TaxCalculationList
  },
  {
    path: '/' + namespace + '/taxRateManage/taxActual',
    name: 'taxActualList',
    meta: {
      icon: 'ios-document',
      title: '实际税金'
    },
    component: TaxActualList
  },
  {
    path: '/' + namespace + '/taxRateManage/actualTaxDetailsList',
    name: 'actualTaxDetailsList',
    meta: {
      icon: 'ios-document',
      title: '实际税金明细'
    },
    component: ActualTaxDetailsList
  },
  {
    path: '/' + namespace + '/taxRateManage/taxRateExclusionList',
    name: 'taxRateExclusionList',
    meta: {
      icon: 'ios-document',
      title: '税率排除目录'
    },
    component: taxRateExclusionList
  }
]
