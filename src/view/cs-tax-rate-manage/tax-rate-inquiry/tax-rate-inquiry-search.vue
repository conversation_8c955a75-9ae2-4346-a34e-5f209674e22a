<template>
  <section>
    <XdoForm class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="codeTS" label="税号">
        <XdoIInput type="text" v-model="searchParam.codeTS" clearable :maxlength="12"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="isRecord" label="是否备案">
        <xdo-select v-model="searchParam.isRecord" :options="this.taxRateManage.IS_RECORD_MAP"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="gname" label="商品名称">
        <XdoIInput type="text" v-model="searchParam.gname" clearable :maxlength="50"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { taxRateManage } from '@/view/cs-common'

  export default {
    name: 'taxRateInquirySearch',
    data () {
      return {
        searchParam: {
          codeTS: '',
          isRecord: '',
          gname: ''
        },
        taxRateManage: taxRateManage
      }
    }
  }
</script>

<style scoped>
</style>
