import { taxRateManage } from '@/view/cs-common'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const actualTaxDetailsList = {
  name: 'actualTaxDetailsList',
  mixins: [columnRender, baseSearchConfig, listDataProcessing],
  data() {
    let params = this.getParams()
    return {
      baseParams: [
        ...params
      ],
      importShow: false,
      listConfig: {
        operationColumnShow: false
      },
      toolbarEventMap: {
        'export': this.handleDownload,
        'setting': this.handleColumnSetup
      },
      cmbSource: {
        transStatus: [{
          value: 'N',
          label: '未支付'
        }, {
          value: 'S',
          label: '已支付'
        }, {
          value: 'P',
          label: '支付中'
        }, {
          value: 'A', label: '支付中'
        }],
        taxType: taxRateManage.TAX_TYPE_MAP,
        downloadStatus: taxRateManage.DOWNLOAD_STATUS_MAP
      }
    }
  },
  methods: {
    /**
     * 获取查询条件
     * @returns {*[]}
     */
    getParams() {
      return [{
        key: 'entryId',
        title: '报关单号'
      }, {
        key: 'taxVouNo',
        title: '税费单号'
      }, {
        type: 'select',
        key: 'taxType',
        title: '税费种类'
      }, {
        range: true,
        key: 'ddate',
        title: '报关单申报日期'
      }, {
        range: true,
        title: '税单生成时间',
        key: 'generationTime'
      // }, {
      //   type: 'select',
      //   title: '下载状态',
      //   key: 'downloadStatus'
      }, {
        type: 'select',
        title: '是否支付',
        key: 'transStatus'
      }, {
        key: 'codeTs',
        title: '商品编码'
      }, {
        title: '币制',
        type: 'pcode',
        key: 'tradeCurrency',
        props: {
          meta: 'CURR_OUTDATED'
        }
      }]
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this,
        paramObj = deepClone(me.searchConfig.model)
      Object.keys(me.extendParams).forEach(key => {
        paramObj[key] = me.extendParams[key]
      })
      return paramObj
    },
    /**
     * 获取列字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 72,
        title: '状态',
        key: 'downloadStatus'
      }, {
        width: 142,
        key: 'batchNo',
        title: '批次号'
      },{
        width: 142,
        key: 'entryId',
        title: '报关单号'
      }, {
        width: 168,
        tooltip: true,
        key: 'emsListNo',
        title: '单据内部编号'
      }, {
        width: 150,
        title: '监管方式',
        key: 'tradeMode',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.trade)
        }, true)
      }, {
        width: 110,
        key: 'ddate',
        title: '报关单申报日期',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params)
        })
      }, {
        width: 180,
        tooltip: true,
        key: 'taxVouNo',
        title: '税费单号'
      }, {
        width: 120,
        key: 'taxId',
        tooltip: true,
        title: '税费单序号'
      }, {
        width: 120,
        key: 'emsNo',
        tooltip: true,
        title: '备案号'
      }, {
        width: 120,
        tooltip: true,
        title: '发票号',
        key: 'invoiceNo'
      }, {
        width: 150,
        key: 'billNo',
        tooltip: true,
        title: '提运单号'
      }, {
        width: 120,
        tooltip: true,
        key: 'contrNo',
        title: '合同号'
      }, {
        width: 130,
        key: 'gname',
        tooltip: true,
        title: '主要商品名称'
      }, {
        width: 120,
        title: '申报关区',
        key: 'masterCustoms',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.customs_rel)
        }, true)
      }, {
        width: 120,
        key: 'taxType',
        title: '税费种类',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.taxType)
        })
      }, {
        width: 150,
        title: '税单生成时间',
        key: 'generationTime',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 160,
        tooltip: true,
        key: 'bankName',
        title: '支付银行'
      }, {
        width: 130,
        tooltip: true,
        key: 'payType',
        title: '支付方式',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, taxRateManage.PAY_TYPE_MAP)
        })
      }, {
        width: 150,
        key: 'taxDate',
        title: '银行扣款时间',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.dateTimeShowRender(h, params, 'yyyy-MM-dd hh:mm:ss')
        })
      }, {
        width: 180,
        tooltip: true,
        title: '支付金额',
        key: 'paymentAmount'
      }, {
        width: 180,
        tooltip: true,
        title: '完税金额',
        key: 'dutyValue'
      }, {
        width: 120,
        key: 'sumTaxMark',
        tooltip: true,
        title: '汇总征税标志'
      }, {
        width: 120,
        tooltip: true,
        key: 'operator',
        title: '操作员账号',
      }, {
        width: 120,
        tooltip: true,
        title: '是否支付',
        key: 'transStatus',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.dynamicSource.transStatus)
        })
      }, {
        width: 80,
        key: 'gno',
        tooltip: true,
        title: '商品序号'
      }, {
        width: 100,
        key: 'codeTs',
        tooltip: true,
        title: '商品编码'
      }, {
        width: 100,
        tooltip: true,
        key: 'dutyMode',
        title: '征免规定',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.levymode)
        })
      }, {
        width: 180,
        key: 'gnameList',
        tooltip: true,
        title: '商品名称'
      }, {
        width: 180,
        key: 'gqty',
        title: '数量',
        tooltip: true
      }, {
        width: 120,
        key: 'gunit',
        title: '单位',
        tooltip: true,
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.unit)
        })
      }, {
        width: 120,
        title: '币制',
        tooltip: true,
        key: 'tradeCurrency',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, [], me.pcode.curr_outdated)
        })
      }, {
        width: 120,
        tooltip: true,
        title: '外汇折算率',
        key: 'exchangeRate'
      }, {
        width: 180,
        tooltip: true,
        title: '完税价格',
        key: 'dutyValueList'
      }, {
        width: 120,
        tooltip: true,
        key: 'rateType',
        title: '税率计征标准'
      }, {
        width: 120,
        tooltip: true,
        title: '从价税率',
        key: 'valoremRate'
      }, {
        width: 120,
        tooltip: true,
        key: 'qtyRate',
        title: '从量税率'
      }, {
        width: 180,
        key: 'tax',
        title: '税额',
        tooltip: true
      }]
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, 'export')
    }
  }
}
