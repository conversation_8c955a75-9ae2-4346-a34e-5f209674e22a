<template>
  <section>
    <Collapse simple v-model="collapseName">
      <Panel name="taxActual">
        税单表头信息
        <div slot="content" style="border-top: 1px solid #e8eaec; padding: 6px 0 0 0;">
          <XdoForm ref="headerEditFrom" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader"
                   label-position="right" :label-width="120" style="padding: 0; grid-column-gap: 0;">
            <XdoFormItem prop="entryId" label="报关单号">
              <XdoIInput type="text" v-model="frmData.entryId" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="taxVouNo" label="税费单号">
              <XdoIInput type="text" v-model="frmData.taxVouNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="taxId" label="税费单序号">
              <XdoIInput type="text" v-model="frmData.taxId" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="taxType" label="税费种类">
              <xdo-select v-model="frmData.taxType" :options="this.taxRateManage.TAX_TYPE_MAP" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="emsListNo" label="单据内部编号">
              <XdoIInput type="text" v-model="frmData.emsListNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="ddate" label="报关单申报日期">
              <XdoIInput type="text" v-model="frmData.ddate" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="emsNo" label="备案号">
              <XdoIInput type="text" v-model="frmData.emsNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="declPort" label="申报口岸">
              <xdo-select v-model="frmData.declPort" :meta="pcode.customs_rel" disabled
                          :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="iePort" label="进出口岸">
              <xdo-select v-model="frmData.ieport" disabled :asyncOptions="pcodeList"
                          :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="tradeCodeName" label="收发货单位">
              <XdoIInput type="text" v-model="tradeCodeName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="ownerCodeName" label="消费使用单位">
              <XdoIInput type="text" v-model="ownerCodeName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="agentCodeName" label="申报单位">
              <XdoIInput type="text" v-model="agentCodeName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="tradeMode" label="监管方式">
              <xdo-select v-model="frmData.tradeMode" :asyncOptions="pcodeList"
                          :meta="pcode.trade" :optionLabelRender="pcodeRender" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="cutMode" label="征免性质">
              <xdo-select v-model="frmData.cutMode" disabled :asyncOptions="pcodeList"
                          :meta="pcode.levytype" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="invoiceNo" label="发票号">
              <XdoIInput type="text" v-model="frmData.invoiceNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="billNo" label="提运单号">
              <XdoIInput type="text" v-model="frmData.billNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="contrNo" label="合同号">
              <XdoIInput type="text" v-model="frmData.contrNo" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="gname" label="主要商品名称">
              <XdoIInput type="text" v-model="frmData.gname" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="masterCustoms" label="申报关区">
              <xdo-select v-model="frmData.masterCustoms" disabled :asyncOptions="pcodeList"
                          :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
            </XdoFormItem>
            <FormItem prop="ieDate" label="进出口日期">
              <XdoDatePicker type="date" v-model="frmData.ieDate" disabled style="width: 100%;" transfer></XdoDatePicker>
            </FormItem>
            <XdoFormItem prop="ieFlag" label="进出口标志">
              <xdo-select v-model="frmData.ieFlag" :options="this.importExportManage.I_E_MARK_MAP"
                          :optionLabelRender="pcodeRender" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="dutyFlag" label="退补税标志">
              <xdo-select v-model="frmData.dutyFlag" :options="this.importExportManage.isDutyMap"
                          :optionLabelRender="pcodeRender" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="delayMark" label="滞报滞纳标志">
              <xdo-select v-model="frmData.delayMark" :options="this.importExportManage.isDutyMap"
                          :optionLabelRender="pcodeRender" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="generationTime" label="税单生成时间">
              <XdoDatePicker type="date" v-model="frmData.generationTime" disabled style="width: 100%;" transfer></XdoDatePicker>
            </XdoFormItem>
            <XdoFormItem prop="bankName" label="支付银行">
              <XdoIInput type="text" v-model="frmData.bankName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="payAcct" label="支付银行账户">
              <XdoIInput type="text" v-model="frmData.payAcct" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="payType" label="支付方式">
              <xdo-select v-model="frmData.payType" :options="this.taxRateManage.PAY_TYPE_MAP"
                          :optionLabelRender="pcodeRender" disabled></xdo-select>
            </XdoFormItem>
            <XdoFormItem prop="payDate" label="支付时间">
              <XdoDatePicker type="date" v-model="frmData.payDate" disabled style="width: 100%;" transfer></XdoDatePicker>
            </XdoFormItem>
            <XdoFormItem prop="taxDate" label="银行扣款时间">
              <XdoDatePicker type="date" v-model="frmData.taxDate" disabled style="width: 100%;" transfer></XdoDatePicker>
            </XdoFormItem>
            <XdoFormItem prop="paymentAmount" label="支付金额">
              <XdoIInput type="text" v-model="frmData.paymentAmount" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="dutyValue" label="完税总额">
              <XdoIInput type="text" v-model="frmData.dutyValue" disabled></XdoIInput>
            </XdoFormItem>
<!--            <XdoFormItem prop="sumTaxMark" label="汇总征税标志">-->
<!--              <XdoIInput type="text" v-model="frmData.sumTaxMark" disabled></XdoIInput>-->
<!--            </XdoFormItem>-->
            <XdoFormItem prop="operator" label="操作员账号">
              <XdoIInput type="text" v-model="frmData.operator" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="limitDate" label="缴款期限">
              <XdoDatePicker type="date" v-model="frmData.limitDate" disabled style="width: 100%;" transfer></XdoDatePicker>
            </XdoFormItem>
            <XdoFormItem prop="incomename" label="收款银行名称">
              <XdoIInput type="text" v-model="frmData.incomename" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="incomesys" label="收入系统">
              <XdoIInput type="text" v-model="frmData.incomesys" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="exchequername" label="财政部名称">
              <XdoIInput type="text" v-model="frmData.exchequername" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="payBkCode" label="支付银行编码">
              <XdoIInput type="text" v-model="frmData.payBkCode" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="payOpBkName" label="付款开户行名称">
              <XdoIInput type="text" v-model="frmData.payOpBkName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="payOpBkCode" label="付款开户行行号">
              <XdoIInput type="text" v-model="frmData.payOpBkCode" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="delayMark" label="延期标志">
              <XdoIInput type="text" v-model="frmData.delayMark" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="subjectCode" label="税费种类编码">
              <XdoIInput type="text" v-model="frmData.subjectCode" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="subjectName" label="税费种类名称">
              <XdoIInput type="text" v-model="frmData.subjectName" disabled></XdoIInput>
            </XdoFormItem>
            <XdoFormItem prop="sumTaxMark" label="汇总征税标志">
              <XdoIInput type="text" v-model="frmData.sumTaxMark" disabled></XdoIInput>
            </XdoFormItem>
          </XdoForm>
        </div>
        <XdoButton type="text" @click="backToList" style="float: right; padding: 8px 12px 0 0;">
          <XdoIcon type="ios-undo" size="22" style="color: green;" />
        </XdoButton>
      </Panel>
    </Collapse>
    <XdoCard :bordered="false" title="税单表体信息" class="ieLogisticsTrackingCard">
      <TaxActualBodyList :parent-config="parentConfig" :head-show="collapseShow"></TaxActualBodyList>
    </XdoCard>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'
  import TaxActualBodyList from './tax-actual-body-list'
  import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
  import { editStatus, taxRateManage, entryManage, importExportManage } from '@/view/cs-common'

  export default {
    name: 'taxActualEdit',
    mixins: [commEdit],
    components: {
      TaxActualBodyList
    },
    data() {
      return {
        ajaxUrl: {},
        cmbSource: {},
        rulesHeader: {},
        entryManage: entryManage,
        collapseName: ['taxActual'],
        taxRateManage: taxRateManage,
        importExportManage: importExportManage
      }
    },
    computed: {
      tradeCodeName() {
        return this.getCodeName(this.frmData.tradeCode, this.frmData.tradeName)
      },
      ownerCodeName() {
        return this.getCodeName(this.frmData.ownerCode, this.frmData.ownerName)
      },
      agentCodeName() {
        return this.getCodeName(this.frmData.agentCode, this.frmData.agentName)
      },
      parentConfig() {
        return {
          editData: this.editConfig.editData,
          editStatus: this.editConfig.editStatus,
          taxVouNo: this.editConfig.editData.taxVouNo
        }
      },
      collapseShow() {
        let me = this
        return me.collapseName.includes('taxActual')
      }
    },
    methods: {
      getCodeName(code, name) {
        if (isNullOrEmpty(code)) {
          if (isNullOrEmpty(name)) {
            return ''
          } else {
            return name
          }
        } else {
          if (isNullOrEmpty(name)) {
            return code
          } else {
            return code + ' ' + name
          }
        }
      },
      /**
       * 自定义初始化值
       */
      getDefaultData() {
        return {
          entryId: '',
          taxVouNo: '',
          taxId: null,
          taxType: '',
          emsListNo: '',
          ddate: '',
          emsNo: '',
          declPort: '',
          iePort: '',
          tradeCode: '',
          tradeName: '',
          ownerCode: '',
          ownerName: '',
          agentCode: '',
          agentName: '',
          tradeMode: '',
          cutMode: '',
          invoiceNo: '',
          billNo: '',
          contrNo: '',
          gname: '',
          masterCustoms: '',
          ieDate: '',
          ieFlag: '',
          dutyFlag: '',
          delayMark: '',
          generationTime: '',
          bankName: '',
          payAcct: '',
          payType: '',
          payDate: '',
          taxDate: '',
          paymentAmount: null,
          dutyValue: null,
          operator: '',
          limitDate: '',
          incomename: '',
          incomesys: '',
          exchequername: '',
          downloadStatus: '',

          payBkCode: '',
          payOpBkName: '',
          payOpBkCode: '',
          payeeName: '',
          subjectCode: '',
          subjectName: '',
          sumTaxMark: ''
        }
      },
      /**
       * 返回列表界面
       */
      backToList() {
        let me = this
        me.editBack({
          editData: {},
          showList: true,
          editStatus: editStatus.SHOW
        })
      },
      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      editBack(backObj) {
        let me = this
        me.$emit('onEditBack', backObj)
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-collapse-header {
    color: black;
    font-weight: bold;
  }

  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  /deep/ .ieLogisticsTrackingCard .ivu-card-extra {
    top: 3px !important;
  }
</style>
