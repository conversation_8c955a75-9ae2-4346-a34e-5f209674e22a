<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields">
          </DynamicForm>
        </div>
      </XdoCard>
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="table" class="dc-table" v-if="grdShow" checkboxSelection :height="dynamicHeight"
                     :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data" :components="components"
                     :overlayLoadingTemplate="overlayLoadingTemplate" :overlayNoRowsTemplate="overlayNoRowsTemplate"
                     @selectionChanged="handleSelectionChange"></xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TaxActualEdit v-if="!showList" :edit-config="editConfig" :in-source="cmbSource" @onEditBack="editBack"></TaxActualEdit>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <dc-Loading-pop :show.sync="loadingShow" :title="loadingTitle" :options="options"></dc-Loading-pop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { taxActualListNew } from './js/taxActualListNew'

  export default {
    name: 'taxActualList',
    mixins: [taxActualListNew],
    data() {
      return {
        cmbSource: {},
        listConfig: {
          exportTitle: '进口提单表头--测试'
        },
        ajaxUrl: {
          dataScraping: csAPI.taxRateManage.actualTax.imports.dataScraping,
          selectAllPaged: csAPI.taxRateManage.actualTax.imports.selectAllPaged,
          checklistPrintUrl: csAPI.taxRateManage.actualTax.imports.checklistPrintUrl
        }
      }
    },
    created() {
      let me = this
      // 备案号
      me.$http.post(csAPI.csProductClassify.bonded.getZtythEmsListNo).then(res => {
        let emsNoDataArr = []
        for (let item of res.data.data) {
          if (!isNullOrEmpty(item.emsNo)) {
            emsNoDataArr.push({
              value: item.emsNo,
              label: item.emsNo
            })
          }
        }
        me.$set(me.cmbSource, 'emsNo', emsNoDataArr)
      }).catch(() => {
        me.$set(me.cmbSource, 'emsNo', [])
      }).finally(() => {
        me.searchFieldsReLoad('emsNo')
      })
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
