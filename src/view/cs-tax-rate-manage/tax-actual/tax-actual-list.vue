<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <XdoBreadCrumb show-icon>
          <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
          <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
        </XdoBreadCrumb>
        <div v-show="showSearch">
          <div class="separateLine"></div>
          <TaxActualSearch ref="headSearch"></TaxActualSearch>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
<!--          <div>-->
<!--            <iframe :src="`http://pmc.51haitun.cn/websocket/Default.html?userName=${this.pmcLogin.userName}&tradeCode=${this.pmcLogin.tradeCode}`" style="display:none;"></iframe>-->
<!--          </div>-->
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight" disable
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TaxActualEdit v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></TaxActualEdit>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="realTotalColumns" class="height:500px"></TableColumnSetup>
<!--    <PmcLoginPop :show.sync="pmcLogin.showLoginPop" @onConfirm="doPmcLogin"></PmcLoginPop>-->
    <dc-file-preview-pop :show.sync="filePreview.show" :file-data="filePreview.fileData" neglect-sid download-post
                         :customize-url="filePreview.pdfUrl" :customize-download-url="filePreview.downloadUrl"></dc-file-preview-pop>
    <dc-Loading-pop :show.sync="loadingShow" :title="loadingTitle" :options="options"></dc-Loading-pop>
    <XdoModal width="600" mask title="下载情况说明" v-model="downloadInfoShow" :footer-hide="true" :mask-closable="false">
      <XdoTable :columns="columns1" :data="msg"></XdoTable>
    </XdoModal>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import TaxActualEdit from './tax-actual-edit'
  import TaxActualSearch from './tax-actual-search'
  import { isDate, formatDate } from '@/libs/datetime'
  import { taxActualList } from '../js/tax-actual/taxActualList'
  import { blobSaveFile, getHttpHeaderFileName, isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'taxActualList',
    components: {
      TaxActualEdit,
      TaxActualSearch
    },
    mixins: [taxActualList],
    data() {
      return {
        msg: [],
        options: {
          showMsg: '',
          loading: false
        },
        columns1: [{
          key: 'entryId',
          title: '报关单号'
        }, {
          key: 'msg',
          title: '下载信息'
        }],
        loadingShow: false,
        downloadInfoShow: false,
        loadingTitle: '税单采集提醒',   // Ikey卡登陆
        filePreview: {
          pdfUrl: '',
          show: false,
          fileData: [],
          downloadUrl: ''
        },
        pmcLogin: {
          password: '',
          isLogin: false,
          showLoginPop: false,
          userName: this.$store.state.user.userNo,       // 'test', //
          tradeCode: this.$store.state.user.company,     // '88888888', //
          tradeName: this.$store.state.user.companyName  // '测试企业', //
        },
        ajaxUrl: {
          preview: csAPI.taxRateManage.actualTax.imports.preview,
          getPreview: csAPI.taxRateManage.actualTax.imports.getPreview,
          dataScraping: csAPI.taxRateManage.actualTax.imports.dataScraping,
          selectAllPaged: csAPI.taxRateManage.actualTax.imports.selectAllPaged,
          downloadAppendix: csAPI.taxRateManage.actualTax.imports.downloadAppendix,
          checklistPrintUrl: csAPI.taxRateManage.actualTax.imports.checklistPrintUrl,
          downloadDutyFormUrl: csAPI.taxRateManage.actualTax.imports.downloadDutyFormUrl
        }
      }
    },
    methods: {
      /**
       * 下载校验
       */
      scrapingValid() {
        let me = this,
          valid = true,
          today = formatDate(new Date(), 'yyyy-MM-dd'),
          toToday = new Date(today + ' 23:23:59').getTime(),
          fromToday = new Date(today + ' 00:00:01').getTime()
        me.gridConfig.selectRows.forEach(row => {
          if (['2', '下载成功'].includes(row['downloadStatus'])) {
            if (!['S'].includes(row['transStatus'])) {
              if (!isNullOrEmpty(row['lastDownloadTime']) && isDate(row['lastDownloadTime'])) {
                let downloadTime = new Date(row['lastDownloadTime']).getTime()
                if (downloadTime > fromToday && downloadTime < toToday) {
                  valid = false
                }
              }
            }
          }
        })
        if (!valid) {
          me.$Message.success('当前报关单当天已下载过一次税单信息，请明日再次下载!')
        }
        return valid
      },
      /**
       * 数据爬取
       */
      handleDataScraping() {
        let me = this
        if (me.checkRowSelected('下载税金')) {
          if (me.scrapingValid()) {
            // if (me.pmcLogin.isLogin === true) {
            me.loadingPopShow('正在进行税单采集申请, 请稍后')
            me.doDataScraping()
            // } else {
            //   me.$set(me.pmcLogin, 'showLoginPop', true)
            // }
          }
        }
      },
      /**
       * 单行下载
       * @param param
       */
      handleDownloadDutyForm(param) {
        let me = this
        me.$http.post(me.ajaxUrl.downloadDutyFormUrl, param).then(res => {
          if (!isNullOrEmpty(res.data.message)) {
            me.$Message.success(res.data.message)
          }
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          me.setToolbarLoading('data-scraping', false)
          me.loadingPopClear()
        })
      },
      loadingPopShow(message) {
        let me = this
        me.$set(me, 'loadingShow', true)
        me.$set(me.options, 'loading', true)
        me.$set(me.options, 'showMsg', message)
      },
      loadingPopClear() {
        let me = this
        me.$set(me, 'loadingShow', false)
        me.$set(me.options, 'showMsg', '')
        me.$set(me.options, 'loading', false)
      },
      // /**
      //  * Ikey卡登陆并爬取数据
      //  * @param password
      //  */
      // doPmcLogin(password) {
      //   let me = this
      //   me.setToolbarLoading('data-scraping', true)
      //
      //   me.loadingPopShow('正在登陆中, 请稍后')
      //
      //   let registerUrl = '/datacenter/api/IKeyConfigUpload'
      //   me.$http.post(registerUrl, {
      //     'InsertUser': 'GW',
      //     'UserName': me.pmcLogin.userName,
      //     'TradeName': me.pmcLogin.tradeName,
      //     'TradeCode': me.pmcLogin.tradeCode
      //   }).then(resR => {
      //     if (resR.data.ok === 'true') {
      //       let loginUrl = '/datacenter/api/SingleWindow?userName='
      //       me.$http.get(loginUrl + me.pmcLogin.userName + '&tradeCode=' + me.pmcLogin.tradeCode + '&cardPwd=' + password).then(res => {
      //         let data = JSON.parse(res.data)
      //         if (data._args.Result) {
      //           // me.$Message.success('登录成功!')
      //           me.$set(me.options, 'showMsg', '登录成功, 正在下载税单, 请稍后!')
      //           me.$set(me.pmcLogin, 'isLogin', true)
      //           me.doDataScraping()
      //         } else {
      //           if (Array.isArray(data._args.Error) && data._args.Error.length > 0) {
      //             me.$Message.success(data._args.Error[0])
      //           } else {
      //             me.$Message.success('登录失败!')
      //           }
      //           // me.$Message.success(data._args.Data)
      //           me.$set(me.pmcLogin, 'isLogin', false)
      //           me.setToolbarLoading('data-scraping', false)
      //           me.loadingPopClear()
      //         }
      //       }).catch(() => {
      //         me.$Message.error('卡登录失败，请检查IC卡是否插入正确!')
      //         me.$set(me.pmcLogin, 'isLogin', false)
      //         me.setToolbarLoading('data-scraping', false)
      //         me.loadingPopClear()
      //       })
      //     } else {
      //       me.loadingPopClear()
      //       me.$Message.error(resR.data.ok.errorCode)
      //       me.setToolbarLoading('data-scraping', false)
      //     }
      //   }).catch(() => {
      //     me.loadingPopClear()
      //     me.setToolbarLoading('data-scraping', false)
      //     me.$Message.success('登录失败!')
      //   })
      // },
      /**
       * 执行数据抓取作业
       */
      doDataScraping() {
        let me = this,
          data = me.gridConfig.selectRows.map(item => {
            return item.entryId
          })
        me.$http.post(me.ajaxUrl.dataScraping, {
          entryIds: data
        }).then(res => {
          if (!isNullOrEmpty(res.data.message)) {
            me.$Message.success(res.data.message)
          } else if (null !== res.data.data) {
            this.downloadInfoShow = true
            this.msg = res.data.data
          }
          me.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          // me.setToolbarLoading('data-scraping', false)
          me.loadingPopClear()
        })
      },
      /**
       * 附件预览
       * @param row
       * @param type 0:核对单, 1:缴款书
       */
      pdfPreview(row, type) {
        let me = this
        me.$set(me.filePreview, 'pdfUrl', me.ajaxUrl.preview + '/' + type + '/' + row.taxVouNo)
        me.$set(me.filePreview, 'downloadUrl', me.ajaxUrl.downloadAppendix + '/' + type + '/' + row.taxVouNo)
        me.$http.post(me.ajaxUrl.getPreview + '/' + type + '/' + row.taxVouNo).then(res => {
          let filename = ''
          if (type === '0') {
            filename = res.data.data['checkFileName']
          } else {
            filename = res.data.data['payFileName']
          }
          // 预览
          me.$set(me.filePreview, 'show', true)
          me.$set(me.filePreview, 'fileData', [{
            sid: res.data.data.sid,
            originFileName: filename
          }])
        }).catch(() => {
        })
      },
      /**
       * 附件下载
       * @param row
       * @param type 0:核对单, 1:缴款书
       */
      fileDownload(row, type) {
        let me = this,
          downloadUrl = me.ajaxUrl.downloadAppendix + '/' + type + '/' + row.taxVouNo
        me.$http.post(downloadUrl, null, {
          responseType: 'blob'
        }).then(res => {
          const filename = getHttpHeaderFileName(res.headers)
          const blob = new Blob([res.data], { type: `application/pdf` })
          blobSaveFile(blob, filename)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
