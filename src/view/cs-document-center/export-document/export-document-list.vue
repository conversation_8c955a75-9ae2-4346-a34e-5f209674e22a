<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DocumentSearch ref="headSearch" :iemark="iemark" :ems-no="currEmsNo"></DocumentSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions">
          <template v-slot:batch-export>
            <ExportAsync name="批量下载" :param="taskInfo" :click="onExportClick" :columns="cmbSource.acmpType" :customBaseUri="customBaseUri" />
          </template>
        </xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <XdoModal v-model="showModal" title="预览" fullscreen
              :footer-hide="true" :mask-closable="false">
      <DocumentPreview v-if="showModal" :pdfData="pdfData"></DocumentPreview>
    </XdoModal>
    <BatchDownloadPop :show.sync="batchDownload.show" ie-mark="E" :sel-rows="gridConfig.selectRows"></BatchDownloadPop>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getColumnsByConfig } from '@/common'
  import { documentList } from '../js/documentList'
  import DocumentPreview from '../components/documentPreview'
  import { columnsConfigE, columns } from '../js/documentColumns'

  export default {
    name: 'exportDocumentList',
    components: {DocumentPreview},
    mixins: [documentList, columns],
    data() {
      return {
        iemark: 'E',
        gridConfig: {
          exportTitle: '出口文档'
        },
        taskInfo: {
          enableSharding: false,     // 启用分片
          taskCode: 'E_DOC_CENTER'   // 添加任务使用的 taskCode
        },
        ajaxUrl: {
          exportUrl: csAPI.documentCenter.docE.exportUrl,
          getAcmpType: csAPI.documentCenter.comm.getAcmpType,
          selectAllPaged: csAPI.documentCenter.docE.selectAllPaged,
          checkSyncEntry: csAPI.documentCenter.comm.checkSyncEntry,
          syncEntryAttached: csAPI.documentCenter.comm.syncEntryAttached
        }
      }
    },
    created: function () {
      let me = this
      let grdCols = getColumnsByConfig(me.totalColumns, columnsConfigE)
      for (let col of grdCols) {
        if (col.key === 'supplierName') {
          col.title = '境外收货人'
        } else if (col.key === 'iedate') {
          col.title = '出口日期'
        } else if (col.key === 'overseasShipperName') {
          col.title = '境外收货人'
        }
      }
      me.baseFields = grdCols
      me.listId = me.$route.path + '/' + me.$options.name
      let columns = me.$bom3.showTableColumns(me.listId, me.baseFields)
      me.handleUpdateColumn(columns)
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
