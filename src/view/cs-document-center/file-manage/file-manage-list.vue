<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DynamicForm :model="searchConfig.model" :rules="searchConfig.rules" :fields="searchConfig.fields" labelWidth="110">
            </DynamicForm>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="listConfig.columns" :data="listConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"
                  @onCellEditingStarted="onCellEditingStarted" @onCellValueChanged="onCellValueChanged"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="baseFields"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
    <UniversalProgress :show.sync="uploadProgressShow" :completed="uploadCompleted" :failure="uploadFailure"></UniversalProgress>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { fileManageList } from '../js/file-manage/fileManageList'

  export default {
    name: 'fileManageList',
    mixins: [fileManageList],
    data() {
      return {
        defaultFields: [],
        listConfig: {
          exportTitle: '文件管理'
        },
        ajaxUrl: {
          // 附件相关
          fileDownload: csAPI.attachedInfo.get,
          getFileList: csAPI.attachedInfo.list,
          fileUpload: csAPI.attachedInfo.insert,
          fileDelete: csAPI.attachedInfo.delete,
          lock: csAPI.documentCenter.fileManage.lock,
          // 文档相关
          insert: csAPI.documentCenter.fileManage.insert,
          update: csAPI.documentCenter.fileManage.update,
          deleteUrl: csAPI.documentCenter.fileManage.delete,
          exportUrl: csAPI.documentCenter.fileManage.exportUrl,
          selectListType: csAPI.documentCenter.fileType.selectListType,
          selectAllPaged: csAPI.documentCenter.fileManage.selectAllPaged
        }
      }
    },
    methods: {
      sourceLoad() {
        let me = this
        me.$http.post(me.ajaxUrl.selectListType).then(res => {
          me.$set(me.cmbSource, 'documentType', res.data.data.map(item => {
            return {
              value: item['typeCode'],
              label: item['typeName']
            }
          }))
        }).catch(() => {
        }).finally(() => {
          me.searchFieldsReLoad('documentType')
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .ag-theme-balham .ag-cell {
    line-height: normal;
  }
</style>
