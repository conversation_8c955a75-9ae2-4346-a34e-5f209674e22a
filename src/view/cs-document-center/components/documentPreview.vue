<template>
  <section class="head">
    <XdoCard class="leftCard" bordered v-if="showPdf">
      <embed :src="pdfUrl" type="application/pdf" width="100%" :height="autoHeight">
    </XdoCard>
    <XdoCard class="rightCard">
      <XdoCard>
        <span>列表</span>
        <div>
          <ul>
            <li v-for=" item in this.pdfData" :key="item.sid">
              <a @click.prevent="getPreviewAttach(item)" :class="{excelName : !item.originFileName.endsWith('pdf')}">{{item.originFileName}}</a>
              <a @click.prevent="downLoad(item)" style="margin-left: 5px;">下载</a>
            </li>
          </ul>
        </div>
      </XdoCard>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { blobSaveFile } from '@/libs/util'

  export default {
    name: 'documentPreview',
    props: {
      pdfData: {
        type: Array,
        default: () => ({})
      }
    },
    data() {
      return {
        pdfUrl: '',
        pdfList: [],
        showPdf: false,
        autoHeight: document.body.clientHeight - (document.body.clientHeight / 5)
      }
    },
    created() {
      this.getData()
    },
    mounted() {
      this.getRecordPreview()
    },
    destroyed() {
      window.URL.revokeObjectURL(this.pdfUrl)
    },
    methods: {
      getBlob(base64, contentType, sliceSize) {
        contentType = contentType || ''
        sliceSize = sliceSize || 512
        let byteCharacters = atob(base64)
        let byteArrays = []
        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          let slice = byteCharacters.slice(offset, offset + sliceSize)
          let byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          let byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        return new Blob(byteArrays, {type: contentType})
      },
      //初始化数据
      getData() {
        let me = this
        me.pdfData.forEach(item => {
          me.pdfList.push(item.sid)
        })
      },
      getRecordPreview() {
        let me = this,
          sid
        if (me.pdfData.length > 0) {
          sid = me.pdfData[0].sid
        }
        if (sid && me.pdfData[0].originFileName.endsWith('pdf') === true) {
          me.showPdf = true
          me.$http.get(csAPI.documentCenter.getPdf + `/${sid}`).then(res => {
            const blob = me.getBlob(res.data, `application/pdf`)
            me.pdfUrl = window.URL.createObjectURL(blob)
          }).catch(() => {
          })
        }
      },
      getPreviewAttach(e) {
        let me = this
        if (e.originFileName.endsWith('pdf')) {
          me.$http.get(csAPI.documentCenter.getPdf + `/${e.sid}`).then(res => {
            me.showPdf = true
            const blob = me.getBlob(res.data, `application/pdf`)
            me.pdfUrl = window.URL.createObjectURL(blob)
          }).catch(() => {
          })
        }
      },
      downLoad(e) {
        let me = this
        me.$http.get(csAPI.documentCenter.getPdf + `/${e.sid}`).then(res => {
          const blob = me.getBlob(res.data, `application/vnd.ms-excel`)
          blobSaveFile(blob, e.originFileName)
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  .head {
    display: flex;
    justify-content: center;
  }

  .leftCard {
    flex: 1;
  }

  .rightCard {
    flex: 1;
  }

  ul {
    //display: flex;
    //flex-wrap: wrap;
  }

  ul li {
    margin-right: 10px;
    list-style-type: none;
  }

  .excelName {
    color: #bbbbbb;
    cursor: default;
    text-decoration: none;
  }
</style>
