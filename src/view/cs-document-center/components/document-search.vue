<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange"></dc-dateRange>
      <XdoFormItem prop="insertUser" label="制单员">
        <XdoIInput type="text" v-model="searchParam.insertUser"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange :label="iEDateLabel" @onDateRangeChanged="handleIEDateChange"></dc-dateRange>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo" :maxlength="30"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="declareName" label="申报单位">
        <XdoIInput type="text" v-model="searchParam.declareName"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="acmpType" label="单据类型">
        <div style="display: grid; grid-template-columns: auto 66px;">
          <xdo-select v-model="searchParam.acmpType" :options="importExportManage.DOCUMENT_TYPE_MAP"
                      :optionLabelRender="pcodeRender"></xdo-select>
          <xdo-select v-model="searchParam.hasAcmp" :options="importExportManage.HAS_DOCUMENT_MAP"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </div>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="syncEntryStatus" label="报关单已同步">
        <xdo-select v-model="searchParam.syncEntryStatus" :options="cmbSource.syncEntryStatus"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="报关单申报日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="batchNo" label="批次号">
        <XdoIInput type="text" v-model="searchParam.batchNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { importExportManage } from '@/view/cs-common'

  export default {
    name: 'billOfLadingSearch',
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      emsNo: {
        type: String
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',
          entryNo: '',
          trafMode: '',
          tradeMode: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          insertUser: '',
          iedateFrom: '',
          iedateTo: '',
          invoiceNo: '',
          declareName: '',
          acmpType: '',
          hasAcmp: '',
          hawb: '',
          emsNo: this.emsNo,
          iemark: this.iemark,
          syncEntryStatus: '',
          declareDateFrom: '',
          declareDateTo: '',
          contrNo: '',
          batchNo: ''
        },
        cmbSource: {
          emsNoData: [],
          syncEntryStatus: [{
            value: '0',
            label: '否'
          }, {
            value: '1',
            label: '是'
          }]
        },
        importExportManage: importExportManage
      }
    },
    created: function () {
      // 备案号
      let me = this
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        me.cmbSource.emsNoData = res.data.data.map(item => {
          return {
            label: item.VALUE,
            value: item.VALUE
          }
        })
      }).catch(() => {
        me.cmbSource.emsNoData = []
      })
    },
    methods: {
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "insertTimeFrom", values[0])
          this.$set(this.searchParam, "insertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "insertTimeFrom", '')
          this.$set(this.searchParam, "insertTimeTo", '')
        }
      },
      handleIEDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "iedateFrom", values[0])
          this.$set(this.searchParam, "iedateTo", values[1])
        } else {
          this.$set(this.searchParam, "iedateFrom", '')
          this.$set(this.searchParam, "iedateTo", '')
        }
      },
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      }
    },
    computed: {
      iEDateLabel() {
        if (this.iemark === 'I') {
          return '进口日期'
        } else if (this.iemark === 'E') {
          return '出口日期'
        } else {
          return '进出口日期'
        }
      }
    }
  }
</script>

<style scoped>
</style>
