import pms from '@/libs/pms'
import { csAPI } from '@/api'
import DocumentSearch from '../components/document-search'
import BatchDownloadPop from '../components/batch-download-pop'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { dynamicExport } from '../../cs-common/dynamic-export/dynamicExport'

export const documentList = {
  components: {
    DocumentSearch,
    BatchDownloadPop
  },
  mixins: [commList, pms, dynamicExport],
  data() {
    return {
      listId: '',
      actions: [],
      pdfData: [],
      cmbSource: {
        acmpType: []
      },
      currEmsNo: '',     // this.$store.getters[`${namespace}/defaultEmsNo`]
      // 查询条件行数
      searchLines: 5,
      baseFields: [],
      showModal: false,
      batchDownload: {
        show: false,
        selRows: []
      },
      listSetupShow: false,
      toolbarEventMap: {
        'export': this.handleDownload,
        'attach-sync': this.handleAttachSync,
        'setting': this.handleTableColumnSetup,
        'batch-export': this.handleBatchDownload
      }
    }
  },
  created: function () {
    let me = this
    me.$http.post(me.ajaxUrl.getAcmpType).then(res => {
      let result = []
      if (res.data.data) {
        Object.keys(res.data.data).forEach(key => {
          if (res.data.data.hasOwnProperty(key)) {
            result.push({
              key: key,
              value: res.data.data[key]
            })
          }
        })
      }
      me.$set(me.cmbSource, 'acmpType', result)
    }).catch(() => {
      me.$set(me.cmbSource, 'acmpType', [])
    })
  },
  mounted: function () {
    let me = this
    me.loadFunctions().then()
  },
  computed: {
    extendParams() {
      let me = this
      return {
        ieMark: me.iemark,
        emsListNos: me.gridConfig.selectRows.map(row => {
          return row.emsListNo
        })
      }
    }
  },
  methods: {
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 自定义列修改
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.$set(me, 'listSetupShow', false)
      me.$set(me.gridConfig, 'gridColumns', [{
        key: 'selection',
        type: 'selection'
      }, ...columns])
      me.$set(me.gridConfig, 'exportColumns', columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      }))
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 批量下载
     */
    handleBatchDownload() {
      let me = this
      if (me.checkRowSelected('批量下载')) {
        me.$set(me.batchDownload, 'show', true)
      }
    },
    /**
     * 批量下载
     * @param params
     */
    doBatchDownload(params) {
      let me = this
      me.$set(me.batchDownload, 'show', false)
      console.info(params)
    },
    /**
     * 同步报关单附件
     */
    handleAttachSync() {
      let me = this
      if (me.checkRowSelected('同步报关单附件')) {
        let params = me.gridConfig.selectRows.map(row => {
          return {
            sid: row['erpSid'],
            entryNo: row.entryNo
          }
        })
        me.setToolbarLoading('attach-sync', true)
        me.$http.post(me.ajaxUrl.checkSyncEntry + '/' + me.iemark, params).then(res => {
          if (res.data.data === 'confirm') {
            me.$Modal.confirm({
              title: '提醒',
              okText: '继续',
              loading: true,
              cancelText: '取消',
              content: '存在已同步数据是否继续?',
              onOk: () => {
                me.$http.post(me.ajaxUrl.syncEntryAttached + '/' + me.iemark, params).then(resI => {
                  me.$Message.success(resI.data.message)
                  me.handleSearchSubmit()
                }).catch(() => {
                }).finally(() => {
                  me.setToolbarLoading('attach-sync')
                })
                setTimeout(() => {
                  me.$Modal.remove()
                }, 150)
              },
              onCancel: () => {
                me.setToolbarLoading('attach-sync')
              }
            })
          } else {
            me.$http.post(me.ajaxUrl.syncEntryAttached + '/' + me.iemark, params).then(res2 => {
              me.$Message.success(res2.data.message)
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('attach-sync')
            })
          }
        }).catch(() => {
          me.setToolbarLoading('attach-sync')
        })
      }
    },
    /**
     * 批量下载
     */
    onExportClick() {
      let me = this,
        exportTitle = '',
        today = (new Date()).toJSON().substr(0, 10).replace(/[-T]/g, '')
      if (me.listConfig) {
        exportTitle = me.listConfig.exportTitle
      }
      if (me.gridConfig) {
        exportTitle = me.gridConfig.exportTitle
      }
      me.$set(me.taskInfo, 'taskName', exportTitle)
      me.$set(me.taskInfo, 'fileName', exportTitle + '_' + today + '.zip')
      me.$set(me.taskInfo, 'exportParamJson', me.extendParams)
    },
    /**
     * 预览
     * @param emsListNo
     * @param attachType
     */
    handleView(emsListNo, attachType) {
      let me = this,
        url
      if (me.iemark === 'I') {
        url = csAPI.documentCenter.docI.getBill
      } else if (me.iemark === 'E') {
        url = csAPI.documentCenter.docE.getBill
      }
      me.$http.post(url, {
        emsListNo: emsListNo,
        acmpType: attachType
      }).then(res => {
        me.pdfData = res.data.data
        me.showModal = true
      }).catch(() => {
      })
    }
  }
}
