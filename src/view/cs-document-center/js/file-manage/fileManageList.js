import { util } from '@/libs'
import { isNullOrEmpty } from '@/libs/util'
import { baseListConfig } from '@/mixin/generic/baseListConfig'
import { baseSearchConfig } from '@/mixin/generic/baseSearchConfig'
import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
import UniversalProgress from '@/components/universal-progress/universal-progress'

export const fileManageList = {
  name: 'fileManageList',
  components: {
    UniversalProgress
  },
  mixins: [baseSearchConfig, baseListConfig],
  data() {
    let params = this.getParams()
    let fields = this.getFields()
    return {
      autoCreate: false,
      baseParams: [
        ...params
      ],
      baseFields: [
        ...fields
      ],
      toolbarEventMap: {
        'add': this.templateAdd,
        'delete': this.handleDelete,
        'lock': this.handleLock,
        'unlock': this.handleUnlock,
        'export': this.handleDownload
      },
      fileData: [],
      currFile: null,
      cmbSource: {
        documentType: [],
        documentTypeObj: {}
      },
      valueDisable: false,
      fileUploading: false,
      uploadFailure: false,
      uploadCompleted: false,
      uploadProgressShow: false
    }
  },
  created: function () {
    let me = this
    me.sourceLoad()
    me.handleUpdateColumn(me.baseFields)
  },
  methods: {
    /**
     * 获取默认值(文件)
     * @param headId
     * @returns {{billSerialNo: string, acmpType: string, acmpFormat: string, businessSid, acmpNo: string, businessType: string}}
     */
    getDefaultUploadData(headId) {
      return {
        acmpNo: '',
        acmpType: '',
        acmpFormat: '',
        billSerialNo: '',
        businessType: 'FM',
        businessSid: headId
      }
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return deepClone(this.searchConfig.model)
    },
    /**
     * 更新自定义选择
     * @param columns
     */
    handleUpdateColumn(columns) {
      let me = this
      me.listConfig.columns = columns
    },
    /**
     * 查询条件字段
     * @returns {({type: string, title: string, key: string}|{title: string, key: string}|{title: string, key: string}|{range: boolean, title: string, key: string})[]}
     */
    getParams() {
      return [{
        type: 'select',
        title: '文档类型',
        key: 'documentType'
      }, {
        title: '文档名称',
        key: 'documentName'
      }, {
        title: '文档序号',
        key: 'documentNo'
      }, {
        range: true,
        title: '录入日期',
        key: 'insertTime'
      }]
    },
    /**
     * 查询结果列字段
     * @returns {({type: string}|{width: number, tooltip: boolean, align: string, title: string, key: string}|{width: number, title: string, align: string, render: (function(*, *): (*)), key: string}|{autoHeight: boolean, width: number, align: string, title: string, render: (function(*, *): VNode), key: string}|{editable: boolean, cellEditorParams: (function(): {maxLength: string}), width: number, tooltip: boolean, cellEditor: string, align: string, title: string, key: string})[]}
     */
    getFields() {
      let me = this
      return [{
        type: 'selection'
      }, {
        width: 128,
        tooltip: true,
        align: 'center',
        title: '文档序号',
        key: 'documentNo'
      }, {
        width: 60,
        title: '状态',
        key: 'status',
        align: 'center',
        render: (h, params) => {
          let status = params.row.status
          if (status === '1') {
            return h('div', {}, '已锁定')
          } else {
            return h('div', {}, '正常')
          }
        }
      }, {
        width: 360,
        align: 'center',
        autoHeight: true,
        title: '文档类型',
        key: 'documentType',
        render: (h, params) => {
          let sid = params.row.sid,
            status = params.row.status,
            docType = params.row['documentType'],
            options = me.cmbSource.documentType.map(item => {
              return h('Option', {
                props: {
                  value: item.value
                }
              }, item.value + ' ' + item.label)
            })
          return h('div', [h('Select', {
            props: {
              transfer: true,
              value: docType,
              clearable: true,
              filterable: true,
              disabled: (status === '1')
            },
            style: {
              width: '100%',
              paddingTop: '2px'
            },
            on: {
              input: (val) => {
                let data = deepClone(params.row)
                data['documentType'] = val
                if (isNullOrEmpty(sid)) {
                  me.rowInsert(data)
                } else {
                  me.rowUpdate(sid, data)
                }
              }
            }
          }, options)])
        }
        // width: 180,
        // tooltip: true,
        // editable: true,
        // align: 'center',
        // title: '文档类型',
        // key: 'documentType',
        // cellEditor: 'agSelectCellEditor',
        // cellEditorParams: function () {
        //   return {
        //     values: Object.keys(me.cmbSource.documentTypeObj)
        //   }
        // }
      }, {
        width: 180,
        tooltip: true,
        editable: true,
        align: 'center',
        title: '文档名称',
        key: 'documentName',
        cellEditorParams: () => {
          return {
            maxLength: '100'
          }
        },
        cellEditor: 'agTextCellEditor'
      }, {
        width: 360,
        key: 'sid',
        title: '附件',
        align: 'center',
        autoHeight: true,
        render: (h, params) => {
          let me = this,
            sid = params.row.sid,
            status = params.row.status,
            attach = params.row['attachedDtoList'] || []
          if (isNullOrEmpty(sid)) {
            return h('span', '')
          }
          let uploadData = me.getDefaultUploadData(sid),
            ctrlUpdate = h('Upload', {
              style: {
                paddingRight: '12px'
              },
              props: {
                multiple: true,
                data: uploadData,
                'show-upload-list': false,
                disabled: (status === '1'),
                'on-error': me.handleOnError,
                action: me.ajaxUrl.fileUpload,
                'on-success': me.handleOnSuccess,
                'before-upload': me.handleBeforeUpload,
                headers: {
                  Authorization: 'Bearer ' + me.$store.state.token
                }
              }
            }, [h('strong', {
              style: {
                color: 'blue',
                fontSize: '14px',
                cursor: 'pointer'
              }
            }, ['[文件上传]'])]),
            fileForDownloads = []
          let fileIndex = 0
          attach.forEach(fileInfo => {
            fileForDownloads.push(h('li', {}, [
              h('span', {}, [
                h('Icon', {
                  props: {
                    title: '删除',
                    type: 'md-close'
                  },
                  on: {
                    click: () => {
                      if (status === '1') {
                        me.$Message.warning('锁定的数据不能删除!')
                      } else {
                        me.onDeleteFile(fileInfo.sid, fileIndex)
                      }
                    }
                  }
                }, []),
                h('a', {
                  on: {
                    click: () => {
                      me.downloadFile(fileInfo.sid)
                    }
                  }
                }, [fileInfo.originFileName])
              ])
            ]))
            fileIndex++
          })
          let fileList = h('ul', {}, fileForDownloads)
          return h('div', {
            style: {
              marginTop: '2px',
              textAlign: 'left',
              marginBottom: '2px'
            }
          }, [
            ctrlUpdate,
            fileList
          ])
        }
      }, {
        width: 380,
        tooltip: true,
        editable: true,
        key: 'address',
        align: 'center',
        title: '存放地点',
        cellEditorParams: () => {
          return {
            maxLength: '100'
          }
        },
        cellEditor: 'agTextCellEditor'
      }, {
        width: 380,
        key: 'note',
        title: '备注',
        tooltip: true,
        editable: true,
        align: 'center',
        cellEditorParams: () => {
          return {
            maxLength: '100'
          }
        },
        cellEditor: 'agTextCellEditor'
      }, {
        width: 160,
        title: '录入人',
        align: 'center',
        key: 'userName'
      }, {
        width: 120,
        align: 'center',
        title: '录入日期',
        key: 'insertTime',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      }]
    },
    /**
     * 单元格编辑起始事件
     * @param e
     */
    onCellEditingStarted(e) {
      let me = this
      if (e.data.status === '1') {
        if (e.api) {
          me.$set(me, 'valueDisable', true)
          e.api.stopEditing()
        }
      }
    },
    /**
     * 单元格值变更事件
     * @param e
     */
    onCellValueChanged(e) {
      let me = this,
        data = e.data,
        sid = data.sid,
        note = data.note,
        address = data.address,
        field = e.column['colId'],
        documentType = data.documentType,
        documentName = data.documentName
      if (me.valueDisable) {
        me.$set(me, 'valueDisable', false)
        return
      }
      if (!isNullOrEmpty(documentType)) {
        documentType = documentType.trim()
        if (documentType.length > 20) {
          me.$Message.warning('文档类型长度不能超过20位字节长度(一个汉字2位字节长度)!')
          if ('documentType' === field) {
            e.data['documentType'] = e.oldValue
          }
          return
        }
      }
      if (!isNullOrEmpty(documentName)) {
        documentName = documentName.trim()
        if (documentName.length > 100) {
          me.$Message.warning('文档名称长度不能超过100位字节长度(一个汉字2位字节长度)!')
          if ('documentName' === field) {
            e.data['documentName'] = e.oldValue
          }
          return
        }
      }
      if (!isNullOrEmpty(address)) {
        address = address.trim()
        if (address.length > 100) {
          me.$Message.warning('存放地址长度不能超过100位字节长度(一个汉字2位字节长度)!')
          if ('address' === field) {
            e.data['address'] = e.oldValue
          }
          return
        }
      }
      if (!isNullOrEmpty(note)) {
        note = note.trim()
        if (note.length > 100) {
          me.$Message.warning('备注长度不能超过100位字节长度(一个汉字2位字节长度)!')
          if ('note' === field) {
            e.data['note'] = e.oldValue
          }
          return
        }
      }
      if (isNullOrEmpty(sid)) {
        me.rowInsert(data)
      } else {
        me.rowUpdate(sid, data)
      }
    },
    /**
     * 新增
     */
    templateAdd() {
      let me = this
      me.listConfig.data = [{
        sid: '',
        note: '',
        address: '',
        insertUser: '',
        insertTime: '',
        documentNo: '',
        documentType: '',
        documentName: ''
      }, ...me.listConfig.data]
    },
    /**
     * 行新增
     * @param rowData
     */
    rowInsert(rowData) {
      let me = this
      me.$http.post(me.ajaxUrl.insert, rowData).then(() => {
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    },
    /**
     * 行更新
     * @param pk
     * @param rowData
     */
    rowUpdate(pk, rowData) {
      let me = this
      me.$http.put(me.ajaxUrl.update + '/' + pk, rowData).then(() => {
      }).catch(() => {
      }).finally(() => {
        me.getList()
      })
    },
    /**
     * 锁定
     */
    handleLock() {
      let me = this
      if (me.checkRowSelected('锁定')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确认',
          cancelText: '取消',
          content: '确认锁定所选项吗',
          onOk: () => {
            me.setToolbarLoading('lock', true)
            let params = me.getSelectedParams()
            me.$http.post(`${me.ajaxUrl.lock}/${params}/1`).then(() => {
              me.$Message.success('锁定成功!')
              me.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              me.setToolbarLoading('lock', false)
            })
          }
        })
      }
    },
    /**
     * 解锁
     */
    handleUnlock() {
      let me = this
      if (me.checkRowSelected('解锁')) {
        if (me.customCheck(me.listConfig.selectRows, '解锁')) {
          me.$Modal.confirm({
            title: '提醒',
            okText: '确定',
            cancelText: '取消',
            content: '确认解锁所选项吗',
            onOk: () => {
              me.setToolbarLoading('unlock', true)
              let params = me.getSelectedParams()
              me.$http.post(`${me.ajaxUrl.lock}/${params}/0`).then(() => {
                me.$Message.success('解锁成功!')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setToolbarLoading('unlock')
              })
            }
          })
        }
      }
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    /**
     * 上传之前回调方法
     */
    handleBeforeUpload() {
      let me = this
      me.$set(me, 'uploadFailure', false)
      me.$set(me, 'uploadCompleted', false)
      me.$set(me, 'uploadProgressShow', true)
    },
    /**
     * 上传错误回调方法
     */
    handleOnError() {
      let me = this
      me.$nextTick(() => {
        me.$set(me, 'uploadFailure', true)
      })
    },
    /**
     * 上传附件成功后回调
     * @param response
     */
    handleOnSuccess(response/*, file*/) {
      let me = this
      if (response.success) {
        me.$set(me, 'uploadCompleted', true)
        me.getList()
      } else {
        me.$nextTick(() => {
          me.$set(me, 'uploadFailure', true)
        })
        me.$Message.error(response.message)
      }
    },
    /**
     * 文件删除
     * @param fileSid
     */
    onDeleteFile(fileSid/*, index*/) {
      let me = this
      me.$Modal.confirm({
        title: '提醒',
        okText: '确定',
        cancelText: '取消',
        content: '您确定要删除此文件吗?',
        onOk: () => {
          me.$http.delete(`${me.ajaxUrl.fileDelete}/${fileSid}`).then(() => {
            me.$Message.success('删除成功!')
            me.getList()
          }).catch(() => {
          })
        }
      })
    },
    /**
     * 文件下载
     * @param sId
     */
    downloadFile(sId) {
      let me = this
      me.$http.get(me.ajaxUrl.fileDownload + '/' + sId, {
        responseType: 'blob'
      }).then(res => {
        const name = util.getHttpHeaderFileName(res.headers)
        const blob = new Blob([res.data], {type: 'application/octet-stream'})
        util.blobSaveFile(blob, name)
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this,
        noSids = me.listConfig.selectRows.filter(row => {
          return isNullOrEmpty(row.sid)
        }),
        lockedRows = me.listConfig.selectRows.filter(row => {
          return row.status === '1'
        })
      if (Array.isArray(noSids) && noSids.length > 0) {
        me.$Message.warning('未保存的数据无法执行删除操作,请重新选择!')
        return
      }
      if (Array.isArray(lockedRows) && lockedRows.length > 0) {
        me.$Message.warning('已锁定的数据无法执行删除操作,请重新选择!')
        return
      }
      me.doDelete(me.ajaxUrl.deleteUrl, me.actions.findIndex(it => it.command === 'delete'))
    }
  },
  computed: {
    /**
     * 导出列头信息
     * @returns {{value: *, key: *}[]}
     */
    exportHeader() {
      return this.listConfig.columns.filter(column => {
        return !['selection', 'operation', 'sid'].includes(column.key) && !isNullOrEmpty(column.key)
      }).map(theCol => {
        return {
          key: theCol.key,
          value: theCol.title
        }
      })
    }
  }
}
