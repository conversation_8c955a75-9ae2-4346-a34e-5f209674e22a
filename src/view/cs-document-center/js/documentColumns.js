import { isNullOrEmpty } from '@/libs/util'
import { baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  ...baseColumnsExport
  , 'batchNo'
  , 'emsListNo'
  , 'entryNo'
  , 'emsNo'
  , 'tradeMode'
  , 'trafMode'
  , 'iedate'
  , 'supplierName'
  , 'invoiceNo'
  , 'contrNo'
  , 'declareCodeCustoms'
  , 'userName'
  , 'insertTime'
  , 'note'
  // 以下为随附单据
  , 'emsListNoCnt'
  , 'entryNoCnt'
  , 'invoiceNoCnt'
  , 'invoiceBoxCnt'
  , 'reviewCnt'
  , 'invBoxBillCnt'
  , 'taxCnt'
  , 'statementCnt'
  , 'situationCnt'
  , 'otherCnt'
  , 'invoiceNoCnt'
  , 'pactCodeCnt'
  , 'orderCnt'
  , 'originCnt'

  , 'classificationCnt'
  , 'arrivalCnt'
  , 'pictureCnt'
  , 'declarationCnt'
  , 'inspectionCnt'
  , 'drawingCnt'
  , 'freightlistCnt'
  , 'quotationCnt'
  , 'voucherCnt'
  , 'attorneyCnt'
  , 'syncEntryStatus'
  , 'declareDate'
  , 'hawb'
  , 'overseasShipperName'
]

const columnsConfig = [
  ...commColumns
]

const excelColumnsConfig = [
  ...commColumns
]

const columnsConfigI = [
  ...columnsConfig
]

const excelColumnsConfigI = [
  ...excelColumnsConfig
]

const columnsConfigE = [
  ...columnsConfig
]

const excelColumnsConfigE = [
  ...excelColumnsConfig
]

const columns = {
  mixins: [baseColumns],
  data() {
    return {
      totalColumns: [
        {
          title: '批次号',
          width: 180,
          align: 'center',
          key: 'batchNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '单据内部编号',
          width: 180,
          align: 'center',
          key: 'emsListNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '报关单号',
          width: 180,
          align: 'center',
          key: 'entryNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '备案号',
          key: 'emsNo',
          width: 120,
          ellipsis: true,
          tooltip: true,
          align: 'center'
        },
        {
          title: '监管方式',
          key: 'tradeMode',
          width: 130,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.trade)
          }
        },
        {
          title: '运输方式',
          key: 'trafMode',
          width: 110,
          ellipsis: true,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
            return this.cmbShowRender(h, params, [], this.pcode.transf)
          }
        },
        {
          title: '进出口日期',
          minWidth: 100,
          align: 'center',
          key: 'iedate'
        },
        {
          title: '供应商',
          minWidth: 120,
          align: 'center',
          key: 'supplierName',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '发票号',
          minWidth: 120,
          align: 'center',
          key: 'invoiceNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '合同协议号',
          minWidth: 120,
          align: 'center',
          key: 'contrNo',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '申报单位',
          minWidth: 160,
          align: 'center',
          key: 'declareCodeCustoms',
          render: (h, params) => {
            let result = ''
            if (!isNullOrEmpty(params.row['declareCodeCustoms'])) {
              result = params.row['declareCodeCustoms']
            }
            if (!isNullOrEmpty(result)) {
              result = result + ' '
            }
            if (!isNullOrEmpty(params.row['declareName'])) {
              result += params.row['declareName']
            }
            return this.toolTipRender(h, result)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          minWidth: 120,
          key: 'declareDate',
          title: '报关单申报日期'
        },
        {
          width: 150,
          key: 'hawb',
          title: '提运单号'
        },
        {
          width: 186,
          title: '境外收发货人',
          key: 'overseasShipperName'
        },
        {
          title: '制单员',
          minWidth: 120,
          align: 'center',
          key: 'userName',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '制单日期',
          minWidth: 100,
          align: 'center',
          key: 'insertTime'
        },
        {
          title: '备注',
          minWidth: 200,
          align: 'center',
          key: 'note',
          ellipsis: true,
          tooltip: true
        },
        // 以下为随附单据
        {
          title: '提运单(随附单据)',
          width: 160,
          align: 'center',
          key: 'emsListNoCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'emsListNo')
          }
        },
        {
          title: '报关单',
          width: 80,
          align: 'center',
          key: 'entryNoCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'entryNo')
          }
        },
        {
          title: '合同',
          width: 80,
          align: 'center',
          key: 'pactCodeCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'pactCode')
          }
        },
        {
          title: '发票箱单',
          width: 80,
          align: 'center',
          key: 'invoiceNoCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'invoiceNo')
          }
        },
        {
          title: '发票&箱单',
          width: 100,
          align: 'center',
          key: 'invoiceBoxCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'invoiceBox')
          }
        },
        {
          title: '复核单',
          width: 80,
          align: 'center',
          key: 'reviewCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'review')
          }
        },
        {
          title: '发票&箱单&提单',
          width: 160,
          align: 'center',
          key: 'invBoxBillCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'invBoxBill')
          }
        },
        {
          title: '税单',
          width: 80,
          align: 'center',
          key: 'taxCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'tax')
          }
        },
        {
          title: '对账单',
          width: 80,
          align: 'center',
          key: 'statementCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'statement')
          }
        },
        {
          title: '订单',
          width: 80,
          align: 'center',
          key: 'orderCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'orderNo')
          }
        },
        {
          title: '原产地证',
          width: 90,
          align: 'center',
          key: 'originCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'origin')
          }
        },
        {
          title: '情况说明',
          width: 90,
          align: 'center',
          key: 'situationCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'situation')
          }
        },
        {
          title: '其他',
          width: 80,
          align: 'center',
          key: 'otherCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'other')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '归类证书',
          key: 'classificationCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'Classification')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '到货通知',
          key: 'arrivalCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'arrival')
          }
        },
        {
          width: 80,
          title: '图片',
          align: 'center',
          key: 'pictureCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'picture')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '申报资料',
          key: 'declarationCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'Declaration')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '商检证书',
          key: 'inspectionCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'inspection')
          }
        },
        {
          width: 80,
          title: '图纸',
          align: 'center',
          key: 'drawingCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'drawing')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '运费清单',
          key: 'freightlistCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'freightlist')
          }
        },
        {
          width: 100,
          title: '报价单',
          align: 'center',
          key: 'quotationCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'quotation')
          }
        },
        {
          width: 100,
          align: 'center',
          key: 'voucherCnt',
          title: '出入库凭证',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'voucher')
          }
        },
        {
          width: 100,
          align: 'center',
          title: '出口委托书',
          key: 'attorneyCnt',
          render: (h, params) => {
            return this.showAttachRender(h, params, 'attorney')
          }
        },
        {
          width: 120,
          title: '报关单已同步',
          key: 'syncEntryStatus'
        }
      ]
    }
  },
  methods: {
    showAttachRender(h, params, attachType) {
      if (isNullOrEmpty(params.row[params.column.key])) {
        return h('span', '')
      }
      return h('a', {
        props: {
          size: 'small',
          type: 'primary'
        },
        style: {
          color: '',
          cursor: 'hand'
        },
        on: {
          click: () => {
            this.handleView(params.row.emsListNo, attachType)
          }
        }
      }, params.row[params.column.key])
    }
  }
}

export {
  columnsConfigI,
  columnsConfigE,
  excelColumnsConfigI,
  excelColumnsConfigE,
  columns
}
