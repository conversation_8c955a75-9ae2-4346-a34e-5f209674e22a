<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100"
             inline>
      <XdoFormItem prop="bondMark" label="保完税标志">
        <xdo-select v-model="searchParam.bondMark" :options="this.customDec.showDiff.bondMark"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="清单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="entryStatus" label="报关单状态">
        <xdo-select v-model="searchParam.entryStatus" :options="this.cmbSource.entryStatusData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbSource.emsNoData"></xdo-select>
      </XdoFormItem>
      <DcDateRange :label="ieDateLabel" @onDateRangeChanged="handiEDateChange"></DcDateRange>
      <DcDateRange label="申报日期" @onDateRangeChanged="handdDateChange"></DcDateRange>


      <XdoFormItem prop="entryPort" :label="iePortLabel">
        <xdo-select v-model="searchParam.entryPort" :asyncOptions="pcodeList"
                    meta="CIQ_ENTY_PORT" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList"
                    :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList"
                    :meta="pcode.transf" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="declareCode" label="申报单位">
        <xdo-select v-model="searchParam.declareCode" :options="this.cmbSource.declareData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <DcDateRange label="制单日期" @onDateRangeChanged="handleValidDateChange" :values="ieDefaultDates"></DcDateRange>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'I'" prop="entryType" label="报关单类型">
        <xdo-select v-model="searchParam.entryType" :options="this.cmbSource.entryTypeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="isUpload" label="草单上传">
        <xdo-select v-model="searchParam.isUpload" :options="this.customDec.showDiff.isUpload"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>

      <XdoFormItem prop="dataSource" label="数据来源">
        <xdo-select v-model="searchParam.dataSource" :options="this.customDec.showDiff.dataSource"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { detailGeneralMethod } from '../showDiff/js/detailGeneralMethod'
  import  {customDec}  from '@/view/cs-knorr/common/comm/constant'
  import DcDateRange from '@/view/cs-knorr/common/components/dc-date-range/DcDateRange'

  export default {
    name: 'DecShowDiffSearch',
    mixins: [detailGeneralMethod],
    components:{
      DcDateRange,
    },
    props: {
      iemark: { //进出口标记
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        ajaxUrl: {
          getSupplierCodeUrl: ''
        },
        searchParam: {
          bondMark: '',
          emsListNo: '',
          entryNo: '',
          sendApiStatus: '',
          entryStatus: '',
          emsNo: '',
          iEDateFrom: '',
          iEDateTo: '',
          dDateFrom: '',
          dDateTo: '',
          entryPort: '',
          tradeMode: '',
          trafMode: '',
          hawb: '',
          declareCode: '',
          overseasShipper: '',
          currAll: '',
          erpInsertTimeFrom: '',
          erpInsertTimeTo: '',
          supplierCode: '',
          clientCode: '',
          contrNo: '',
          entryType: '',
          isUpload: ''
        },
        cmbSource: {
          emsNoData: [],
          entryStatusData: [],
          declareData: [],
          supplierCodeList: [],
          sendApiStatusData: [{
            value: '-1', label: '未发送'
          }, {
            value: '0', label: '发送失败'
          }, {
            value: '1', label: '发送成功'
          }],
          entryTypeData: customDec.showDiff.entryType
        },
        customDec: customDec
      }
    },
    created() {
      let me = this
      // 备案号
      me.getEmsNoList((req) => {
        if (!req.data) {
          return []
        }
        let emsNoDataArr = []
        for (let item of req.data) {
          if (!isNullOrEmpty(item.emsNo)) {
            emsNoDataArr.push({
              value: item.emsNo,
              label: item.emsNo
            })
          }
        }
        me.cmbSource.emsNoData = emsNoDataArr
      })

      // 供应商/客户
      if (me.iemark === 'I') {
        me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csApiKnorr.showDiff.PRD)
      } else if (me.iemark === 'E') {
        me.$set(me.ajaxUrl, 'getSupplierCodeUrl', csApiKnorr.showDiff.CLI)
      }
      me.$http.post(me.ajaxUrl.getSupplierCodeUrl).then(res => {
        me.cmbSource.supplierCodeList = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.supplierCodeList = []
      })

      // 申报单位
      me.$http.post(csApiKnorr.showDiff.CUT).then(res => {
        me.cmbSource.declareData = res.data.data.map((item) => {
          return {
            label: item.LABEL,
            value: item.CODE
          }
        })
      }).catch(() => {
        me.cmbSource.declareData = []
      })

      // 报关单状态
      me.$http.post(csApiKnorr.showDiff.getParamValues + '/ENTRY_STATUS').then(res => {
        me.cmbSource.entryStatusData = res.data.data.map((item) => {
          return {
            label: item.value,
            value: item.key
          }
        })
      }).catch(() => {
        me.cmbSource.entryStatusData = []
      })
    },
    mounted: function() {
      // this.$set(me.searchParam, 'emsNo', me.$store.getters[`${namespace}/selectedManual`])
    },
    methods: {
      /**
       * 申报日期范围
       * @param values
       */
      handdDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'dDateFrom', values[0])
          this.$set(this.searchParam, 'dDateTo', values[1])
        } else {
          this.$set(this.searchParam, 'dDateFrom', '')
          this.$set(this.searchParam, 'dDateTo', '')
        }
      },
      /**
       * 申报日期范围
       * @param values
       */
      handiEDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'iEDateFrom', values[0])
          this.$set(this.searchParam, 'iEDateTo', values[1])
        } else {
          this.$set(this.searchParam, 'iEDateFrom', '')
          this.$set(this.searchParam, 'iEDateTo', '')
        }
      },
      handleValidDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, 'erpInsertTimeFrom', values[0])
          this.$set(this.searchParam, 'erpInsertTimeTo', values[1])
        } else {
          this.$set(this.searchParam, 'erpInsertTimeFrom', '')
          this.$set(this.searchParam, 'erpInsertTimeTo', '')
        }
      }
    },
    computed: {
      ieDateLabel() {
        if (this.iemark === 'I') {
          return '进口日期'
        } else if (this.iemark === 'E') {
          return '出口日期'
        } else {
          return '进出口日期'
        }
      },
      iePortLabel() {
        if (this.iemark === 'I') {
          return '入境口岸'
        } else if (this.iemark === 'E') {
          return '离境口岸'
        } else {
          return '出入境口岸'
        }
      },
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style scoped>
</style>
