import { columnRender } from '@/view/cs-knorr/common/comm'
import  {customDec}  from '@/view/cs-knorr/common/comm/constant'


const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      // {
      //   title: '发送状态',
      //   key: 'sendApiStatus',
      //   align: 'center',
      //   width: 100
      // },

      {
        width: 150,
        key: 'result',
        title: '比对结果',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.result)
        }
      },
      {
        width: 150,
        key: 'isUpload',
        title: '草单上传',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.isUpload)
        }
      },
      {
        width: 150,
        key: 'approvalStatus',
        title: '审批状态',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.approvalStatus)
        }
      },
      {
        width: 150,
        key: 'dataSource',
        title: '数据来源',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.dataSource)
        }
      },
      {
        width: 200,
        key: 'approvalNote',
        title: '审批意见',
        // render: (h, params) => {
        //   return this.cmbShowRender(h, params, customDec.showDiff.approvalStatus)
        // }
      },

      {
        width: 150,
        key: 'sendStatus',
        title: '发送状态',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.sendStatus)
        }
      },




      {
        title: '报关单状态',
        key: 'entryStatusName',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        // render: (h, params) => {
        //         //   return this.cmbShowRender(h, params, customDec.showDiff.entryType)
        //         // }
      },
      {
        title: '保完税标志',
        width: 100,
        align: 'center',
        key: 'bondMark',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.bondMark)
        }
      },
      {
        title: '制单日期',
        key: 'erpInsertTime',
        width: 90,
        align: 'center',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },
      {
        title: '清单内部编号',
        key: 'emsListNo',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '报关单统一编号',
        key: 'seqNo',
        width: 130,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '报关单号',
        key: 'entryNo',
        width: 200,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '中文品名',
        minWidth: 120,
        align: 'center',
        key: 'gname',
        ellipsis: true,
        tooltip: true
      },
      {
        title: '报关单申报日期',
        width: 120,
        align: 'center',
        key: 'entryDeclareDate',
        render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }
      },
      {
        title: '备案号',
        key: 'emsNo',
        width: 140,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '监管方式',
        key: 'tradeMode',
        width: 150,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.trade)
        }
      },
      {
        title: '运输方式',
        key: 'trafMode',
        width: 110,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transf)
        }
      },
      {
        title: '成交方式',
        key: 'transMode',
        width: 70,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.transac)
        }
      },
      {
        title: '申报单位',
        key: 'declareName',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '申报地海关',
        key: 'masterCustoms',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      },
      {
        title: '境外收货人',
        key: 'overseasShipper',
        width: 250,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render:(h,params)=>{
          return h('span', params.row.overseasShipper||'' + ' '+ (params.row.overseasShipperName||''))
        }
      },
      {
        title: '供应商',
        width: 150,
        align: 'center',
        key: 'supplierName',
        ellipsis: true,
        tooltip: true
      },
      // {
      //   title: '客户',
      //   width: 150,
      //   align: 'center',
      //   key: 'clientName',
      //   ellipsis: true,
      //   tooltip: true
      // },
      {
        title: '运抵国（地区）',
        key: 'tradeCountry',
        width: 180,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.country)
        }
      },
      {
        title: '离境关别',
        key: 'ieport',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.customs_rel)
        }
      },
      {
        title: '合同协议号',
        key: 'contrNo',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '许可证号',
        key: 'licenseNo',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '包装种类',
        key: 'wrapType',
        width: 100,
        ellipsis: true,
        tooltip: true,
        align: 'center',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], this.pcode.wrap)
        }
      },
      {
        title: '件数',
        key: 'packNum',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '总净重',
        key: 'netWt',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '总毛重',
        key: 'grossWt',
        width: 80,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '运费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'feeMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap )
            }
          },
          {
            title: '费率',
            key: 'feeRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'feeCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr)
            }
          }
        ]
      },
      {
        title: '保费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'insurMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap)
            }
          },
          {
            title: '费率',
            key: 'insurRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'insurCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr)
            }
          }
        ]
      },
      {
        title: '杂费',
        align: 'center',
        children: [
          {
            title: '类型',
            key: 'otherMark',
            width: 80,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, customDec.showDiff.feeTypeMap)
            }
          },
          {
            title: '费率',
            key: 'otherRate',
            width: 80,
            ellipsis: true,
            tooltip: true,
            align: 'center'
          },
          {
            title: '币制',
            key: 'otherCurr',
            width: 150,
            ellipsis: true,
            tooltip: true,
            align: 'center',
            render: (h, params) => {
              return this.cmbShowRender(h, params, [], this.pcode.curr)
            }
          }
        ]
      },
      {
        title: '总量',
        key: 'qtyAll',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '总价',
        key: 'decTotalAll',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '币制',
        key: 'currAll',
        width: 120,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '发票号',
        key: 'invoiceNo',
        width: 160,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        title: '备注',
        key: 'note',
        width: 90,
        ellipsis: true,
        tooltip: true,
        align: 'center'
      },
      {
        width: 150,
        key: 'entryType',
        title: '报关单类型',
        render: (h, params) => {
          return this.cmbShowRender(h, params, customDec.showDiff.entryType)
        }
      },


    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],
    }
  },

}
export {
  columns
}

