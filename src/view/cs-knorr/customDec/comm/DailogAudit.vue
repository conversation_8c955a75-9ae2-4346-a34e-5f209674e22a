<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="500"
    title="审核意见">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm class="dc-form dc-form-2 xdo-enter-form" ref="form" :show-message="false" :model="selectForm"
             :rules="rulesHeader"
             :label-width="80">
      <XdoFormItem label="审核意见" class="dc-merge-1-3" prop="approvalNote">
        <XdoIInput type="textarea" v-model="selectForm.approvalNote"  :maxlength="100" ></XdoIInput>
      </XdoFormItem>
      <XdoFormItem class="dc-merge-1-3" style="text-align: right;padding-top: 5px">
        <XdoButton type="success" icon="ios-cloud-upload" :loading="trueLoading" @click="handleConfirm">确定
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>


<script>
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'

  export default {
    name: 'DailogAudit.vue', // 报关单比对 弹出页面
    props: {
      show: { type: Boolean, required: true },
      iemark: { type: String, default: 'I' },   //I进口审核  E出口审核
      sid: { type: String, required: true },  //审核单据的sid
      approvalStatus: { type: String, required: true }

    },
    data() {
      return {
        trueLoading: false,
        selectForm: {
          approvalNote: ''//审核意见
        },
        ajaxUrl: {
          iAudit: csApiKnorr.showDiff.iAudit,    //进口审核接口
          eAudit: csApiKnorr.showDiff.eAudit    //出口审核接口
        },
        rulesHeader: {
          approvalNote: [{ required: true, message: '不能为空', trigger: 'blur' }]
        }
      }
    },

    watch: {
      show(val) {
        if (val) {
          if (this.approvalStatus === '1') {
            this.selectForm.approvalNote = '审批通过'
          }
          if (this.approvalStatus === '-1') {

            this.selectForm.approvalNote = '审批退回'
          }
        }
      }
    },


    methods: {
      /**
       * 确定
       */
      handleConfirm() {

        this.$refs.form.validate().then(isValid => {
          if (isValid) {
            let url = this.iemark === 'I' ? this.ajaxUrl.iAudit : this.ajaxUrl.eAudit
            let postData = Object.assign(this.selectForm, { sid: this.sid, approvalStatus: this.approvalStatus })
            this.trueLoading = true
            this.$http.post(url, postData).then(() => {
              this.$Message.success('审核完成!')
              this.$emit('update:show', false)
              this.$emit('onConfirm', this.selectForm)
            }).catch(() => {
            }).finally(() => {
              this.trueLoading = false
            })
          }
        })
      }
      ,
      /**
       * 关闭
       */
      handleClose() {
        this.$emit('update:show', false)
      }
    }
  }
</script>

