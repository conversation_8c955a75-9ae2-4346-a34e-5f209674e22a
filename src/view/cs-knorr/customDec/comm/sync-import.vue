<template>
  <XdoModal width="600" mask v-model="show" title="数据导入"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header">
      <XdoForm :label-width="100" class="model-form">
        <XdoRow style="overflow: hidden;">
          <XdoCol span="20">
            <XdoFormItem :label="uploadProps.filename">
              <div v-if="uploadProps.file">
                <XdoIInput type="text" readonly v-model="uploadProps.file.name"></XdoIInput>
              </div>
              <div v-else>
                <XdoIInput type="text" readonly></XdoIInput>
              </div>
            </XdoFormItem>
          </XdoCol>
          <XdoCol span="4">
            <Upload ref="upload" style="padding: 0;"
                    :headers="requestHeader" :show-upload-list="false"
                    :format="['xls','xlsx']"
                    :action="uploadProps.uploadUrl"
                    :data="uploadOption"
                    :before-upload="handleBeforeUpload"
                    :on-success="handleSuccess">
              <XdoButton icon="ios-cloud-upload-outline">请选择</XdoButton>
            </Upload>
          </XdoCol>
        </XdoRow>
      </XdoForm>
    </div>
    <div class="xdo-enter-action action dc-form-1" style="text-align: center; margin-bottom: 6px;">
      <div>
        <XdoButton type="primary" @click="upload" :loading="importLoading" style="width: 100px;"> 导入 </XdoButton>
      </div>
    </div>
  </XdoModal>
</template>

<script>
  import { excelExport } from '@/api'

  export default {
    data() {
      return {
        uploadProps: {
          filename: '',
          file: null,
          uploadUrl: '',
          downloadUrl: ''
        },
        // uploadOption: {},
        importLoading: false,
        downloadLoading: false,
        requestHeader: {
          Authorization: 'Bearer ' + this.$store.state.token
        }
      }
    },
    watch: {
      show(value) {
        if (value) {
          this.assignment()
        }
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      },
      uploadConfig: {
        type: Object,
        require: true,
        default: () => ({
          filename: '',
          uploadUrl: '',
          importType: '',
          downloadUrl: ''
        })
      },
      uploadOption: {
        type: Object,
        require: true,
        default: () => ({
          emsListNo: '',
        })
      }
    },
    mounted() {
      let me = this
      me.assignment()
    },
    methods: {
      assignment() {
        let me = this
        me.uploadProps = Object.assign({}, me.uploadConfig)
      },
      valid() {
        let me = this
        if (me.uploadProps.file) {
          if (me.uploadProps.file.size > 5120000) {
            me.$Message.warning('文件 ' + me.uploadProps.file.name + ' 太大，不能超过 5M。')
            return false
          }
          /*let fileType = this.uploadProps.file.type
          let typeOne = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          let typeTwo = 'application/vnd.ms-excel'
          if (typeOne !== fileType && typeTwo !== fileType) {
            this.$Message.warning('文件 ' + this.uploadProps.file.name + ' 格式不正确，请上传 xls 或 xlsx 格式的Excel文件。')
            return false
          }*/
          return true
        } else {
          me.$Message.warning('校验前请先导入文件')
        }
        return false
      },
      handleBeforeUpload(file) {
        let me = this
        me.$set(me.uploadProps, 'file', file)
        me.valid()
        return false
      },
      upload() {
        let me = this
        if (me.valid() === true) {
          me.$set(me, 'importLoading', true)
          me.$refs.upload.post(me.uploadProps.file)
        }
      },
      handleSuccess(response) {
        let me = this
        // 因为上传过程为实例，这里模拟添加 url
        me.importLoading = false
        if (response.success === true) {
          me.$Message.success('导入成功!')
          me.$emit('import:success')
          me.handleClose()
        } else {
          me.$Message.warning(response.message)
        }
      },
      handleTemp() {
        let me = this
        me.downloadLoading = true
        excelExport(me.uploadConfig.downloadUrl, {
          name: me.uploadConfig.filename
        }, 'get').finally(() => {
          me.downloadLoading = false
        })
      },
      handleClose() {
        let me = this
        me.importLoading = false
        me.downloadLoading = false
        me.uploadProps.file = null
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }

  .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    border-color: #57a3f3;
    background-color: #57a3f3;
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }
</style>
