<template>
  <span>
    <Poptip v-if="diffData.otherHead" titile="差异信息" word-wrap transfer padding="5px" :placement="placement">
      <div slot="content">
        <table>
          <tr>
            <td>本地数据：</td>
            <td :key="index" v-for="(item,index) in diffKesArry" style="padding: 5px; border-bottom: #bbbbbb 1px solid ">
             <span :key="indexd" v-for="(itemD,indexd) in Array.from(String(oldData[item]||''))">
               <span
                 :class="{'span-title-error': String(oldData[item])[indexd] != String(otherData[item]||'')[indexd]}">{{itemD}}</span>
            </span>
            </td>
          </tr>
          <tr>
              <td>返回数据：</td>
            <td :key="index" v-for="(item,index) in diffKesArry" style="padding: 5px ;">
            <span :key="indexd" v-for="(itemD,indexd) in Array.from(String(otherData[item]||''))">
               <span
                 :class="{'span-title-error': String(otherData[item])[indexd] != String(oldData[item]||'')[indexd]}">{{itemD}}</span>
            </span>
            </td>
          </tr>
        </table>
      </div>
      <span :class="{'span-title':true,'span-title-body':isBody,'span-title-error':isDiff}"><slot></slot></span>
    </Poptip>
    <span v-else :class="{'span-title':true,'span-title-body':isBody,}">
      <slot></slot>
    </span>
  </span>
</template>

<script>
  export default {
    name: 'SpanDiff', //比对 组件
    props: {
      // 判断是否是表体   字体不用 加粗
      isBody: { type: Boolean, default: false },
      //提示信息出现的位置
      placement: { type: String, default: 'top' },
      //原始数据
      oldData: { type: Object },
      otherData: { type: Object },

      //比对数据
      diffData: {
        type: Object,
        required: true,
        default: () => ({
          head: {},
          list: [],
          otherHead: {},
          otherList: []
        })
      },
      /**
       * a,b,c 需要比对字段的key
       */
      diffKes: {
        type: String, required: true,
        default: () => ('')
      }
    },
    data() {
      return {
        diffKesArry: []
      }
    },
    created() {
      this.diffKesArry = this.diffKes.split(',')
      // console.log(this.otherData)
    },
    computed: {
      /**
       * 是否有差异
       * @returns {boolean} true : 有  false 没有
       */
      isDiff() {
        let result = false
        this.diffKesArry.forEach(item => {
            if (this.oldData[item] !== this.otherData[item]) {
              result = true
            }
          }
        )
        return result
      },
      /**
       * 2 字体蓝色，本地数据无，返回数据有。
       */
      isExcess(){
        let result = false
        this.diffKesArry.forEach(item => {
            if (this.oldData[item] !== this.otherData[item]) {
              if (!this.oldData[item] && this.otherData[item]){
                result = true
              }
            }
          }
        )
        return result
      }
    }
  }
</script>

<style scoped>
  .span-title {
    padding-right: 3px;
    font-weight: bold;
    font-size: 13px
  }

  .span-title-body {
    font-weight: 200;
  }

  .span-title-error {
    color: red;
    font-size: 13px;
    font-weight: bold;
  }



</style>
