<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <DecShowDiffSearch ref="headSearch" iemark="I"></DecShowDiffSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" :current="pageParam.page" :page-size='pageParam.limit' :total="pageParam.dataTotal"
                   show-total show-sizer
                   :page-size-opts='pageSizeOpts' @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <!--    报关单比对页面-->
    <DecShowDiff v-if="!showList" :propDiffData="diffData" @backList="this.backToList"></DecShowDiff>

    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>

    <DailogAudit  :show.sync="isShowDailogAudit"  :approvalStatus="approvalStatus" iemark="I"  :sid="sid"  @onConfirm="handleconfirmAudit"></DailogAudit>

    <SyncImport :show.sync="importShow" :uploadConfig="impData" :uploadOption = "param" @import:success="onAfterImport"></SyncImport>
  </section>
</template>

<script>

  import DecShowDiff from './DecShowDiff'
  import { mainJS } from './js/decShowDiffMain'
  import DailogAudit from '../comm/DailogAudit'
  import SyncImport from '../comm/sync-import'
  import { columns } from './js/decShowDiffColunmns'
  import DecShowDiffSearch from './DecShowDiffSearch'

  export default {
    name: 'DecShowDiffMain',
    moduleName: '进口报关单比对',
    components: {
      DecShowDiffSearch,
      DecShowDiff,
      DailogAudit,
      SyncImport
    },
    mixins: [columns, mainJS],
    data() {
      return {
        importShow: false,
        // 导入参数
        impData: {
          importType: '',
          filename: '',
          uploadUrl: ''
        },
        param: {
            emsListNo: ''
        }
      }
    },
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>

