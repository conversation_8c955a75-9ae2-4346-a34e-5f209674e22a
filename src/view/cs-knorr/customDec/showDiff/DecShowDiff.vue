<template>
  <Card :bordered="false">
    <p slot="title"> 报关单比对</p>
    <Icon type="ios-backspace" size="25" style="cursor:pointer;" color="#2d8cf0" slot="extra" @click="this.backList"/>
    <div style="padding:2px; font-family:STsong; ">
      <div style="width: 100%;line-height: 30px; text-align: center">
        <h2>中华人民共和国海关{{isIemarkI?'进口':'出口'}}货物报关单</h2>
      </div>
      <div class="" style="font-size: 12px; margin: 10px 0 3px 3px ">
        <p style="color:orangered;font-size: 14px; font-weight: bold">tips: 1 字体红色，有差异。 2 表体行字体蓝色，本地数据无，返回数据有。 3 字体黑色，无差异。</p>
      </div>
      <div class="wrapper" style="font-size: 12px; margin: 10px 0 3px 3px ">
        <div class="one">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="preEntryNo" placement="top-start">
            预录入编号：
          </SpanDiff>
          {{diffData.head.preEntryNo }}
        </div>
        <div class="two">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="entryNo,masterCustomsName">
            海关编号：
          </SpanDiff>
          {{diffData.head.entryNo}}({{diffData.head.masterCustomsName}})
        </div>
        <div class="three" style="text-align: left; font-size: 15px; font-weight: bold">
              {{liangBuShenBao}}
        </div>
      </div>
      <!--      表头-->
      <div style="border-top: black 1px solid; border-right: black 1px solid">
        <div class="rowHead">
          <div style="display: flex; justify-self: left">
            <div style="width: 28%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          placement="top-start"
                          diffKes="tradeCode,tradeName">
                  {{isIemarkI?'境内收货人':'境内发货人' }}
                </SpanDiff>
                ({{diffData.head.tradeCode}})
              </div>
              <div>
                {{diffData.head.tradeName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="ieport,ieportName">
                  {{isIemarkI?'进境关别':'出境关别' }}
                </SpanDiff>
                ({{diffData.head.ieport}})
              </div>
              <div>
                {{diffData.head.ieportName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="iedate">
                  {{isIemarkI?'进口日期':'出口日期' }}
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.iedate}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">

              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="ddate">
                  申报日期
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.ddate}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="emsNo">
                  备案号
                </SpanDiff>

              </div>
              <div>
                {{diffData.head.emsNo }}
              </div>
            </div>
          </div>
        </div>
        <div class="rowHead">
          <div style="display: flex; justify-self: left">
            <div style="width: 28%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          placement="top-start"
                          diffKes="overseasShipperName">
                  {{isIemarkI?'境外发货人':'境外收货人' }}
                </SpanDiff>
                <!--                ({{diffData.head.overseasShipper }})-->
              </div>
              <div>
                {{diffData.head.overseasShipperName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="trafMode,trafModeName">
                  运输方式
                </SpanDiff>
                ({{diffData.head.trafMode}})
              </div>
              <div>
                {{diffData.head.trafModeName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="trafName,voyageNo">
                  运输工具名称及航次号
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.trafName}}{{diffData.head.voyageNo?'/'+diffData.head.voyageNo:''}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">

              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="hawb">
                  提运单号
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.hawb}}
              </div>
            </div>
            <div v-if="isIemarkI" style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="warehouse">
                  货物存放地点
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.warehouse}}
              </div>
            </div>
            <div v-else style="width: 18%;border-bottom: black 1px solid">

            </div>

          </div>
        </div>
        <div class="rowHead">
          <div style="display: flex; justify-self: left">
            <div style="width: 28%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          placement="top-start"
                          diffKes="ownerCode,ownerName">
                  {{isIemarkI?'消费使用单位':'生产销售单位'}}
                </SpanDiff>

                ({{diffData.head.ownerCode}})
              </div>
              <div>
                {{diffData.head.ownerName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="tradeMode,tradeModeName">
                  监管方式
                </SpanDiff>
                ({{diffData.head.tradeMode}})
              </div>
              <div>
                {{diffData.head.tradeModeName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="cutMode,cutModeName">
                  征免性质
                </SpanDiff>
                ({{diffData.head.cutMode}})
              </div>
              <div>
                {{diffData.head.cutModeName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="licenseNo">
                  许可证号
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.licenseNo}}
              </div>
            </div>
            <div v-if="isIemarkI" style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="despPort,despPortName">
                  启运港
                </SpanDiff>
                ({{diffData.head.despPort}})
              </div>
              <div>
                {{diffData.head.despPortName}}
              </div>
            </div>
            <div v-else style="width: 18%;border-bottom: black 1px solid">

            </div>
          </div>
        </div>
        <div class="rowHead">
          <div style="display: flex; justify-self: left">
            <div style="width: 28%" class="borderMe">
              <div>

                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          placement="top-start"
                          diffKes="contrNo">
                  合同协议号
                </SpanDiff>
              </div>
              <div>
                {{diffData.head.contrNo}}
              </div>


            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="tradeNation,tradeNationName">
                  贸易国(地区)
                </SpanDiff>
                ({{diffData.head.tradeNation}})
              </div>
              <div>
                {{diffData.head.tradeNationName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="tradeCountry,tradeCountryName">
                  {{isIemarkI?'启运国(地区)':'运抵国(地区)'}}
                </SpanDiff>
                ({{diffData.head.tradeCountry}})
              </div>
              <div>
                {{diffData.head.tradeCountryName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="destPort,destPortName">
                  {{isIemarkI?'经停港':'指运港'}}
                </SpanDiff>
                ({{diffData.head.destPort}})
              </div>
              <div>
                {{diffData.head.destPortName}}
              </div>
            </div>
            <div style="width: 18%" class="borderMe">
              <div>
                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          diffKes="entryPort,entryPortName">
                  {{isIemarkI?'入境口岸':'离境口岸'}}
                </SpanDiff>
                ({{diffData.head.entryPort}})
              </div>
              <div>
                {{diffData.head.entryPortName}}
              </div>
            </div>
          </div>
        </div>
        <div class="rowHead">
          <div style="display: flex; justify-self: left">
            <div style="width: 28%" class="borderMe">
              <div>

                <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                          placement="top-start"
                          diffKes="wrapType,wrapTypeName">
                  包装种类
                </SpanDiff>
                ({{diffData.head.wrapType}})
              </div>
              <div>
                {{diffData.head.wrapTypeName}}
              </div>


            </div>
            <div style="width: 18% ;display: flex; justify-self: left" class="borderMe">
              <div style="width: 40% ;border-right: black 1px solid">
                <div>

                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="packNum">
                    件数
                  </SpanDiff>
                </div>
                <div>
                  {{diffData.head.packNum}}
                </div>
              </div>
              <div style="width: 60%;padding-left: 3px ">
                <div>

                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="grossWt">
                    毛重(千克)
                  </SpanDiff>
                </div>
                <div>
                  {{diffData.head.grossWt}}
                </div>
              </div>

            </div>
            <div style="width: 18% ;display: flex; justify-self: left" class="borderMe">
              <div style="width: 40% ;border-right: black 1px solid">
                <div>
                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="netWt">
                    净重(千克)
                  </SpanDiff>
                </div>
                <div>
                  {{diffData.head.netWt}}
                </div>
              </div>
              <div style="width: 60%;padding-left: 3px ">
                <div>
                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="transMode,transModeName">
                    成交方式
                  </SpanDiff>

                  ({{diffData.head.transMode}})
                </div>
                <div>
                  {{diffData.head.transModeName}}
                </div>
              </div>

            </div>
            <div style="width: 36%;display: flex; justify-self: left" class="borderMe">

              <div style="width: 33.3% ;border-right: black 1px solid">
                <div>
                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="feeCurr,feeRate,feeMark">
                    运费
                  </SpanDiff>

                </div>
                <div>
                  {{(`${diffData.head.feeCurr||''}/${diffData.head.feeRate||''}/${diffData.head.feeMark||''}`)==='//'?'':(`${diffData.head.feeCurr||''}/${diffData.head.feeRate||''}/${diffData.head.feeMark||''}`)}}
                  <!--币制/金额/类型-->
                </div>
              </div>
              <div style="width: 33.3% ;border-right: black 1px solid;padding-left: 3px ">
                <div>
                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="insurCurr,insurRate,insurMark">
                    保费
                  </SpanDiff>
                </div>
                <div>
                  {{(`${diffData.head.insurCurr||''}/${diffData.head.insurRate||''}/${diffData.head.insurMark||''}`)==='//'?'':(`${diffData.head.insurCurr||''}/${diffData.head.insurRate||''}/${diffData.head.insurMark||''}`)}}
                  <!--                  000/率/类型-->
                </div>
              </div>
              <div style="width: 33.3% ;padding-left: 3px ">
                <div>

                  <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                            diffKes="otherCurr,otherRate,otherMark">
                    杂费
                  </SpanDiff>
                </div>
                <div>
                  {{(`${diffData.head.otherCurr||''}/${diffData.head.otherRate||''}/${diffData.head.otherMark||''}`)==='//'?'':(`${diffData.head.otherCurr||''}/${diffData.head.otherRate||''}/${diffData.head.otherMark||''}`)}}
                </div>
              </div>
            </div>
          </div>
        </div>


        <div class="rowHead borderMe" style="text-align: left">
          <div>

            <span class="span-title-me">随附单证及编号</span>
          </div>
          <div>

          </div>
        </div>
        <div class="rowHead borderMe" style="text-align: left">
          <div>
            <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                      placement="top-start"
                      diffKes="markNo">
              标记唛码及备注
            </SpanDiff>
          </div>
          <div>
            {{diffData.head.markNo}}
          </div>
        </div>
      </div>
      <!--      表体 head-->
      <div class="wrapperBody borderMe" style="border-right: black 1px solid;">
        <div class="wrapperBody1">
          <span class="span-title-me">项号</span>
        </div>
        <div class="wrapperBody2">
          <span class="span-title-me"> 商品编号</span>
        </div>
        <div class="wrapperBody3">
          <span class="span-title-me">商品名称及规格型号</span>
        </div>
        <div class="wrapperBody4">
          <span class="span-title-me">数量及单位</span>
        </div>
        <div class="wrapperBody5">
          <span class="span-title-me"> 单价/总价/币制</span>
        </div>
        <div class="wrapperBody6">
          <span class="span-title-me">原产国(地区)</span>
        </div>
        <div class="wrapperBody7">
          <span class="span-title-me"> 最终目的国(地区)</span>
        </div>
        <div class="wrapperBody8">
          <span class="span-title-me"> {{isIemarkI?'境内目的地':'境内货源地'}}</span>
        </div>
        <div class="wrapperBody9">
          <span class="span-title-me">征免</span>
        </div>
      </div>
      <!--      表体 content-->
      <div :key="item.sid" v-for="(item,index) in diffData.list" class="wrapperBodyContent borderMe"
           style="border-right: black 1px solid; border-bottom-style: dotted ">
        <div class="wrapperBodyContent1">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true" placement="top-start"
                    :otherData=" diffData.otherList[index]||[]" diffKes="serialNo">
            {{item.serialNo}}
          </SpanDiff>
        </div>
        <div class="wrapperBodyContent2">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="codeTS">
            {{item.codeTS}}
          </SpanDiff>
        </div>
        <div class="wrapperBodyContent3">

          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="gname">
            {{item.gname}}
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent3-2 bubble:after">

          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="gmodel">
            {{item.gmodel}}
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent4">

          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="qty1,unit1Name">
            {{item.qty1}}{{item.unit1Name}}
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent4-2">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="qty2,unit2Name">
            {{item.qty2}}{{item.unit2Name}}
          </SpanDiff>

        </div>
        <div class="wrapperBodyContent4-3">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="qty,unitName">
            {{item.qty}} {{item.unitName}}
          </SpanDiff>
        </div>
        <div class="wrapperBodyContent5">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="decPrice">
            {{item.decPrice}}
          </SpanDiff>
        </div>
        <div class="wrapperBodyContent5-2">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="decTotal">
            {{item.decTotal}}
          </SpanDiff>

        </div>
        <div class="wrapperBodyContent5-3">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="currName">
            {{item.currName}}
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent6">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="originCountryName">
            {{item.originCountryName}}
          </SpanDiff>

        </div>
        <div class="wrapperBodyContent6-2">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="originCountry">
            ({{item.originCountry}})
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent7">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="destinationCountryName">
            {{item.destinationCountryName}}
          </SpanDiff>


        </div>
        <div class="wrapperBodyContent7-2">
          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]" diffKes="destinationCountry">
            ({{item.destinationCountry}})
          </SpanDiff>

        </div>
        <div class="wrapperBodyContent8">

          <SpanDiff :diffData="diffData" :oldData="item" :isBody="true"
                    :otherData="diffData.otherList[index]||[]"
                    diffKes="districtCode,destCode,districtName,destName">
            ({{item.districtCode}}{{item.destCode?'/'+item.destCode:''}}){{item.districtName}}{{item.destName?'/'+item.destName:''}}
          </SpanDiff>

        </div>
        <div class="wrapperBodyContent9">
          <div>
            <SpanDiff :diffData="diffData" :oldData="item" :isBody="true" placement="top-end"
                      :otherData="diffData.otherList[index]||[]" diffKes="dutyModeName">
              {{item.dutyModeName}}
            </SpanDiff>
          </div>
          <div>
            <SpanDiff :diffData="diffData" :oldData="item" :isBody="true" placement="top-end"
                      :otherData="diffData.otherList[index]||[]" diffKes="dutyMode">
              ({{item.dutyMode}})
            </SpanDiff>
          </div>
        </div>
      </div>

      <!--      表体 content 多余的-->
      <div :key="item.sid" v-for="(item) in otherListMore" class="wrapperBodyContent borderMe"
           style="border-right: black 1px solid; border-bottom-style: dotted;color: #389DE9 ">
        <div class="wrapperBodyContent1">
          <span>
            {{item.gno}}
           </span>
        </div>
        <div class="wrapperBodyContent2">
           <span>
            {{item.codeTS}}
           </span>
        </div>
        <div class="wrapperBodyContent3">
          {{item.gname}}
        </div>
        <div class="wrapperBodyContent3-2 bubble:after">
          <span>
             {{item.gmodel}}
          </span>
        </div>
        <div class="wrapperBodyContent4">
          {{item.qty1}}{{item.unit1Name}}
        </div>
        <div class="wrapperBodyContent4-2">
          {{item.qty2}}{{item.unit2Name}}
        </div>
        <div class="wrapperBodyContent4-3">
          {{item.qty}} {{item.unitName}}
        </div>
        <div class="wrapperBodyContent5">
          {{item.decPrice}}
        </div>
        <div class="wrapperBodyContent5-2">
          {{item.decTotal}}
        </div>
        <div class="wrapperBodyContent5-3">
          {{item.currName}}
        </div>
        <div class="wrapperBodyContent6">
          {{item.originCountryName}}
        </div>
        <div class="wrapperBodyContent6-2">
          ({{item.originCountry}})
        </div>
        <div class="wrapperBodyContent7">
          {{item.destinationCountryName}}
        </div>
        <div class="wrapperBodyContent7-2">
          ({{item.destinationCountry}})
        </div>
        <div class="wrapperBodyContent8">
          ({{item.districtCode}}{{item.destCode?'/'+item.destCode:''}})
          {{item.districtName}}{{item.destName?'/'+item.destName:''}}
        </div>
        <div class="wrapperBodyContent9">
          <div>
            {{item.dutyModeName}}
          </div>
          <div>
            ({{item.dutyMode}})
          </div>
        </div>
      </div>

      <!--价格说明-->
      <div style="display:flex;justify-self:left;border-right:black 1px solid;text-align: center" class="borderMe">
        <div style="width: 25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="promiseItems1">
            特殊关系确认：
          </SpanDiff>

          {{diffData.head.promiseItems1}}
        </div>
        <div style="width:  25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="promiseItems2">
            价格影响确认：
          </SpanDiff>
          {{diffData.head.promiseItems2}}
        </div>
        <div style="width: 25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="promiseItems3">
            支付特许权使用费确认：
          </SpanDiff>
          {{diffData.head.promiseItems3}}
        </div>
        <div style="width: 25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="promiseItems4">
            公式定价确认：
          </SpanDiff>
          {{diffData.head.promiseItems4}}
        </div>
        <div style="width: 25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="promiseItems5">
            暂定价格确认：
          </SpanDiff>
          {{diffData.head.promiseItems5}}
        </div>
        <div style="width: 25%">
          <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                    diffKes="cusRemarkAuto">
            自报自缴：
          </SpanDiff>
          {{diffData.head.cusRemarkAuto==='1'?'是':'否'}}
        </div>
      </div>
      <!--结尾-->
      <div style="display:flex;justify-self:left;border-right:black 1px solid; line-height: 35px; " class="borderMe">
        <div style="width:70% ;border-right: black 1px solid">
          <div style="display: flex;justify-self: left;">
            <div style="width: 20%">
              <span class="span-title-me">报关人员</span>
            </div>
            <div style="width: 20%">
              <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                        diffKes="typistNo">
                报关人员证号
              </SpanDiff>
              {{diffData.head.typistNo}}
            </div>
            <div style="width: 20%">
              <span class="span-title-me">电话</span>
            </div>
            <div style="width: 40%;text-align: right;padding-right: 2px">
              <span class="span-title-me"> 兹申明对以上内容承担如实申报 、依法纳税之法律责任</span>
            </div>
          </div>
          <div style="display: flex;justify-self: left; padding-right: 2px ">
            <div style="width: 70%">

              <SpanDiff :diffData="diffData" :oldData="diffData.head" :otherData="diffData.otherHead"
                        placement="top-start"
                        diffKes="declareCode,declareName">
                申报单位
              </SpanDiff>
              ({{diffData.head.declareCode}}){{diffData.head.declareName}}
            </div>
            <div style="width: 30%; text-align: right"><span class="span-title-me">申报单位(签章)</span></div>
          </div>
        </div>
        <div style="width:30%;padding-left: 2px">
          <span class="span-title-me"> 海关批注及签章</span>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>

  import SpanDiff from './SpanDiff'

  export default {
    name: 'DecShowDiff',
    components: {
      SpanDiff
    },
    props: {
      //比对数据
      propDiffData: {
        type: Object,
        required: true,
        default: () => ({
          head: {},
          list: [],
          otherHead: {},
          otherList: []
        })
      }
    },
    data() {
      return {

        diffData: this.propDiffData,
        otherListMore: []
      }
    },
    computed: {
      //两步申报 非保税 进口 的时候 展示涉证.......信息
      liangBuShenBao : function (){
          if (this.diffData.head.entryType==='X' && this.diffData.head.bondMark==='1'){
              return  `两步申报模式:${this.diffData.head.cardMark==='1'?'涉证':'非证'}、 ${this.diffData.head.checkMark==='1'?'涉检':'非检'}、 ${this.diffData.head.taxMark==='1'?'涉税':'非税'} `;
          }else{
            return  '';
          }
        //
      },

      /**
       * 是否是 进口 true 进口    false 出口
       * @returns {boolean}
       */
      isIemarkI: function() {
        return this.diffData.head.iemark === 'I'
      }
    },
    created() {
      this.handlePadingData()
    },
    mounted() {
    },
    methods: {

      /**
       * 价格说明处理
       * */
      handleGetpromiseItemsName(promiseItems) {

        if (promiseItems == '0') {
          return '否'
        }
        if (promiseItems == '1') {
          return '是'
        }
        if (promiseItems == '9') {
          return '空'
        }
        return promiseItems

      },
      /**
       *
       */
      handlePadingData() {
        this.diffData.otherList = this.diffData.otherList || []
        if (this.diffData.head && this.diffData.head.promiseItems) {
          this.diffData.head.promiseItems1 = this.handleGetpromiseItemsName(this.diffData.head.promiseItems[0])
          this.diffData.head.promiseItems2 = this.handleGetpromiseItemsName(this.diffData.head.promiseItems[1])
          this.diffData.head.promiseItems3 = this.handleGetpromiseItemsName(this.diffData.head.promiseItems[2])
          this.diffData.head.promiseItems4 = this.handleGetpromiseItemsName(this.diffData.head.promiseItems[3])
          this.diffData.head.promiseItems5 = this.handleGetpromiseItemsName(this.diffData.head.promiseItems[4])
        } else {
          this.diffData.head.promiseItems1 = '空'
          this.diffData.head.promiseItems2 = '空'
          this.diffData.head.promiseItems3 = '空'
          this.diffData.head.promiseItems4 = '空'
          this.diffData.head.promiseItems5 = '空'
        }

        if (!this.diffData.otherHead) {
          return
        }


        //处理表头的  价格说明
        if (this.diffData.otherHead.promiseItems) {
          this.diffData.otherHead.promiseItems1 = this.handleGetpromiseItemsName(this.diffData.otherHead.promiseItems[0])
          this.diffData.otherHead.promiseItems2 = this.handleGetpromiseItemsName(this.diffData.otherHead.promiseItems[1])
          this.diffData.otherHead.promiseItems3 = this.handleGetpromiseItemsName(this.diffData.otherHead.promiseItems[2])
          this.diffData.otherHead.promiseItems4 = this.handleGetpromiseItemsName(this.diffData.otherHead.promiseItems[3])
          this.diffData.otherHead.promiseItems5 = this.handleGetpromiseItemsName(this.diffData.otherHead.promiseItems[4])
        } else {
          this.diffData.otherHead.promiseItems1 = '空'
          this.diffData.otherHead.promiseItems2 = '空'
          this.diffData.otherHead.promiseItems3 = '空'
          this.diffData.otherHead.promiseItems4 = '空'
          this.diffData.otherHead.promiseItems5 = '空'
        }

        /**
         * 找出返回的数据  多出来的数据
         */
        if (this.diffData.otherList && this.diffData.otherList.length > 0) {
          let list = this.diffData.list || []
          let numAdd = this.diffData.otherList.length - list.length
          if (numAdd > 0) {
            this.otherListMore = [...this.otherListMore, ...this.diffData.otherList]
            this.otherListMore = this.otherListMore.filter((item, index) => index >list.length-1 )
          }
        }

      },
      backList() {
        this.$emit('backList', true)
      }
    }
  }
</script>

<style scoped>


  .span-title-me {
    padding-right: 3px;
    font-weight: bold;
    font-size: 13px;
  }

  .borderMe {
    border-left: black solid 1px;
    border-bottom: black solid 1px;
    text-align: left;
    padding-left: 2px;
  }

  .bubble:after {
    content: '';
    position: absolute;
    left: 100%;
    bottom: 0px;
    width: 16px;
    height: 16px;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-top-width: 10px;
    border-top-color: currentColor;
    border-radius: 0 32px 0 0;
    color: #dddddd;
  }

  .wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    grid-auto-rows: minmax(33.3%, auto);
  }

  .one {
    grid-column: 1;
    grid-row: 1;
  }

  .two {
    grid-column: 2;
    grid-row: 1;
  }

  .three {
    grid-column: 3;
    grid-row: 1;
  }

  .rowHead {
    width: 100%;
    line-height: 25px;
    text-align: center;
    /*border: black solid 1px;*/
  }

  /*表体 head*/
  .wrapperBody {
    display: grid;
    grid-template-columns: repeat(260, 1fr);
    text-align: center;
  }

  .wrapperBody1 {
    grid-column: 1/7;
    grid-row: 1;
  }

  .wrapperBody2 {
    grid-column: 8/24;
    grid-row: 1;
  }

  .wrapperBody3 {
    grid-column: 25/103;
    grid-row: 1;
  }

  .wrapperBody4 {
    grid-column: 104/127;
    grid-row: 1;
  }

  .wrapperBody5 {
    grid-column: 128/155;
    grid-row: 1;
  }

  .wrapperBody6 {
    grid-column: 156/179;
    grid-row: 1;
  }

  .wrapperBody7 {
    grid-column: 180/204;
    grid-row: 1;
  }

  .wrapperBody8 {
    grid-column: 205/244;
    grid-row: 1;
  }

  .wrapperBody9 {
    grid-column: 245/260;
    grid-row: 1;
  }

  /*表体 内容*/
  .wrapperBodyContent {
    display: grid;
    grid-template-columns: repeat(260, 1fr);
    text-align: center;
    grid-auto-rows: minmax(20px, auto);
  }

  .wrapperBodyContent1 {
    grid-column: 1/7;
    grid-row: 1;
    text-align: left;
  }

  .wrapperBodyContent2 {
    grid-column: 8/24;
    grid-row: 1;
    /*禁止换行 start*/
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /*禁止换行 end*/
  }

  .wrapperBodyContent3 {
    grid-column: 25/103;
    grid-row: 1;
    text-align: left;
    /*禁止换行 start*/
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /*禁止换行 end*/
  }

  .wrapperBodyContent3-2 {
    grid-column: 25/103;
    grid-row: 2/4;
    text-align: left;
  }


  .wrapperBodyContent4 {
    grid-column: 104/127;
    grid-row: 1;
    text-align: right;
  }

  .wrapperBodyContent4-2 {
    grid-column: 104/127;
    grid-row: 2;
    text-align: right;
  }

  .wrapperBodyContent4-3 {
    grid-column: 104/127;
    grid-row: 3;
    text-align: right;
  }


  .wrapperBodyContent5 {
    grid-column: 128/155;
    grid-row: 1;
    text-align: right;
  }

  .wrapperBodyContent5-2 {
    grid-column: 128/155;
    grid-row: 2;
    text-align: right;
  }

  .wrapperBodyContent5-3 {
    grid-column: 128/155;
    grid-row: 3;
    text-align: right;
  }


  .wrapperBodyContent6 {
    grid-column: 156/179;
    grid-row: 1;
    text-align: right;

  }

  .wrapperBodyContent6-2 {
    grid-column: 156/179;
    grid-row: 2;
    text-align: right;
  }


  .wrapperBodyContent7 {
    grid-column: 180/204;
    grid-row: 1;
    text-align: right;
  }

  .wrapperBodyContent7-2 {
    grid-column: 180/204;
    grid-row: 2;
    text-align: right;
  }


  .wrapperBodyContent8 {
    grid-column: 205/244;
    grid-row: 1/4;
    text-align: right;
  }

  .wrapperBodyContent9 {
    grid-column: 245/260;
    grid-row: 1/4;
    text-align: right;
  }


</style>
