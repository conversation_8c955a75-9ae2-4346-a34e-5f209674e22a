import { csApiKnorr } from '@/view/cs-knorr/common/comm'



export const detailGeneralMethod = {
  methods: {
    /**
     * 获取 备案号
     * @param callback
     */
    getEmsNoList(callback) {
      this.$http.post(csApiKnorr.showDiff.getZtythEmsListNo).then(res => {
        callback(res.data)
      }).catch(() => {
        callback({data: []})
      })
    },
    /**
     * 获取 内部备案号
     * @param callback
     */
    getCopEmsNoList(callback) {
      this.$http.post(csApiKnorr.showDiff.getZtythCopEmsNo).then(res => {
        callback(res.data)
      })
    }
  }
}
