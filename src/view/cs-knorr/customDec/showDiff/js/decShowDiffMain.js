import pms from '@/libs/pms'
import { csApiKnorr } from '@/view/cs-knorr/common/comm'
import { commColumnsCustom } from '@/view/cs-knorr/common/comm'
import { operationRenderer } from '@/view/cs-knorr/common/comm/operation_renderer'
import { dynamicImport } from '@/view/cs-knorr/common/dynamic-import/dynamicImport'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  data() {
    return {
      // 查询条件行数
      searchLines: 6,
      // 审核相关
      sid: '',
      diffData: {},
      approvalStatus: '',
      isShowDailogAudit: false,
      gridConfig: {
        exportTitle: '报关比对'
      },
      toolbarEventMap: {
        'diff': this.handleDiff,       // '比对信息'
        'pass': this.handlePassAudit,   //通过
        'back': this.handleBackAudit    //退回
      },
      ajaxUrl: {
        diff: csApiKnorr.showDiff.diff,
        selectAllPaged: csApiKnorr.showDiff.selectAllPaged
      },
    }
  },
  methods: {
    /**
     * 审核通过
     */
    handlePassAudit() {
      let me = this
      if (me.checkRowSelected('审核通过', true)) {
        console.log(me.gridConfig.selectRows[0])
        me.sid = me.gridConfig.selectRows[0].sid
        me.approvalStatus = '1'
        me.isShowDailogAudit = true
      }
    },
    /**
     * 审核通过 / 退回 完成后的回调 事件
     */
    handleconfirmAudit() {
      this.handleSearchSubmit()
    },
    /**
     * 审核退回
     */
    handleBackAudit() {
      if (this.checkRowSelected('审核退回', true)) {
        this.sid = this.gridConfig.selectRows[0].sid
        this.approvalStatus = '-1'
        this.isShowDailogAudit = true
      }
    },
    /*
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let diff = this.actions.filter(item => {
        return item.command === 'diff'
      })
      //判断是否存在修改按钮  若存在 就增加 查看修改列
      let bascol = []
      if (Array.isArray(diff) && diff.length > 0) {
        let upload = this.actions.filter(item => {
          return item.command === 'upload'
        })
        if (Array.isArray(upload) && upload.length > 0) {
          bascol = [
            {
              title: '操作',
              fixed: 'left',
              width: 120,
              align: 'center',
              key: 'operation',
              cellRendererFramework: operationRenderer(this, [{
                title: '比对',
                handle: 'handleDiffInline',
                marginRight: '15px'
              },{
                title: '上传',
                handle: 'handleUploadExcel',
                marginRight: '0'
              }])
            }
          ]
        } else {
          bascol = [{
            width: 120,
            title: '操作',
            fixed: 'left',
            align: 'center',
            key: 'operation',
            cellRendererFramework: operationRenderer(this, [{
              title: '比对',
              handle: 'handleDiffInline',
              marginRight: '0'
            }])
          }]
        }

      } else {
        return []
      }

      // table上面的上传按钮不显示
      this.actions = this.actions.map(item => {
        if (item.command === 'upload') {
          item.needed = false
        }
        return item
      })


      this.gridConfig.gridColumns = []
      this.$nextTick(function () {
        this.gridConfig.gridColumns = [...bascol, ...columns]
      })
      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.tableloading = true
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.gridConfig.data = res.data.data.map(item => {
            return Object.assign({result: item.result}, item.head)
          })
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
          me.tableloading = false
        })
      })
    },
    /**
     * 报关单比对 tool
     */
    handleDiff() {
      if (this.checkRowSelected('比对', true)) {
        this.handleDiffInline(this.gridConfig.selectRows[0])
      }
    },
    /**
     * 报关单比对 行内
     */
    handleDiffInline(selectRow) {
      this.$http.get(`${this.ajaxUrl.diff}/${selectRow.sid}`).then((res) => {
        //
        // res.data.data.head.preEntryNo =222222222
        // res.data.data.head.feeMark =2
        // res.data.data.head.feeRate =1
        // res.data.data.head.feeCurr =14
        // res.data.data.otherHead =Object.assign({},res.data.data.head,{preEntryNo:'123123'}) //,feeMark:'1',feeRate:'2',feeCurr:'502'
        // res.data.data.otherList =[]//[... res.data.data.list.map(item=>Object.assign({},item)) ,... res.data.data.list.map(item=>Object.assign({},item))]
        // res.data.data.otherList[1].codeTS = '22222222'
        // res.data.data.otherList[0].codeTS = '111111'
        //
        this.diffData = res.data.data
        this.showList = false
      }).catch(() => {
      }).finally(() => {
      })
    },
    /**
     * 上传报关单草单
     */
    handleUploadExcel(selectRow) {
      this.importShow = true

      this.$set(this, 'impData', {
        importType: 'entry',
        filename: '报关单草单',
        uploadUrl: csApiKnorr.showDiff.import + `/I`
      })

      this.$set(this, 'param', {
        emsListNo: selectRow.emsListNo
      })
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      let me = this
      me.$set(me, 'importShow', false)
      me.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
