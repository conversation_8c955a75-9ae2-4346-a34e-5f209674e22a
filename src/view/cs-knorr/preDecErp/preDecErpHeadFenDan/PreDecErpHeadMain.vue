<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <PreDecErpHeadSearch ref="headSearch"></PreDecErpHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <PreDecErpHeadTab  v-if="!showList" @onEditBack="editBack"  :editConfig="editConfig"></PreDecErpHeadTab>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId" :columns="totalColumns" class="height:500px"
                      @updateColumns="handleUpdateColumn"></TableColumnSetup>
    <DailogSendBack :show.sync="isShowReturn" @onConfirm="handleReturnConfirm"></DailogSendBack>
    <DailogSend :show.sync="isShowSend" ref="DailogSend" @onConfirm="handleSendConfirm"></DailogSend>
  </section>
</template>

<script>
  import DailogSend from './DailogSend'
  import DailogSendBack from './DailogSendBack'
  import { mainJS } from './js/preDecErpHeadMain'
  import PreDecErpHeadTab from './PreDecErpHeadTab'
  import { columns } from './js/preDecErpHeadColumns'
  import PreDecErpHeadSearch from './PreDecErpHeadSearch'
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'

  export default {
    name: 'PreDecErpHeadMain',
    moduleName: '预报单表头',
    components: {
      DailogSend,
      DailogSendBack,
      PreDecErpHeadTab,
      PreDecErpHeadSearch
    },
    mixins: [columns, mainJS],
    data() {
      return {
        comboxData: interfaceImportManagementConst
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>

