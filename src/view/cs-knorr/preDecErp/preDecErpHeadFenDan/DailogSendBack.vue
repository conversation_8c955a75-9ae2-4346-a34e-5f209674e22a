<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="600"
    title="退回原因">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm class="" ref="form" :show-message="false" :model="frmData"
             :label-width="10">
      <XdoFormItem label="">
        <Input v-model="frmData.note" type="textarea" :rows="4" placeholder="请填写退回原因" />
      </XdoFormItem>
      <XdoFormItem class="" style="text-align: right">
        <XdoButton type="success" icon="ios-cloud-upload" @click="handleConfirm">确定
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>
<script>
  export default {
    name: 'DailogSendBack', //退回
    props: {
      show: { type: Boolean, required: true }
    },
    data() {
      return {
        frmData: {
          note: '', // 退回原因
        }
      }
    },
    methods: {
      /**
       * 确定
       */
      handleConfirm() {
        this.$emit('onConfirm', this.frmData)
      },
      /**
       * 关闭
       */
      handleClose() {
        this.$emit('update:show', false)
      }
    }
  }
</script>
<style scoped>

  .dc-form-2{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    grid-column-gap: 2px
  }

</style>
