<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="600"
    title="关务分单">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm class="" ref="form" :show-message="false" :model="frmData"
             :label-width="80">
      <XdoFormItem label="报关行">
        <xdo-select v-model="frmData.customerCode"
                    :options="customerCodeData"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem class="" style="text-align: right">
        <XdoButton  :loading="isLoading"   type="success" icon="ios-cloud-upload" @click="handleConfirm">确定
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>
<script>
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'

  export default {
    name: 'DailogSend', //分单
    props: {
      show: { type: Boolean, required: true }
    },
    data() {
      return {
        frmData: {
          customerCode: '' // 退回原因
        },
        customerCodeData: [],
        isLoading:false,
      }
    },
    created() {
      /**
       * 获取 报关行
       */
      this.$http.post(csApiKnorr.comomApiInfo.baseCompany.selectComboxByCode + '/CUT').then(res => {
        if (res.data.data) {
          this.customerCodeData = res.data.data.filter(x => x.CODE).map(
            (item) => {
              return { value: item.CODE, label: item.LABEL }
            }
          )
        }
      }).catch(() => {
        this.customerCodeData = []
      }).finally(() => {
      })
    },
    methods: {
      /**
       * 确定
       */
      handleConfirm() {
        this.$emit('onConfirm', this.frmData)
      },
      /**
       * 关闭
       */
      handleClose() {
        this.$emit('update:show', false)
      }
    }
  }
</script>
<style scoped>

  .dc-form-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 2px
  }

</style>
