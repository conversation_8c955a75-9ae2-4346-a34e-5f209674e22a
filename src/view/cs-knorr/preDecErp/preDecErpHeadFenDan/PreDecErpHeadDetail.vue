<template>
  <section v-focus>
    <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader"
             label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="预报单表头">
        <div class="dc-form" style="padding-right: 10px">
          <XdoFormItem prop="preEmsListNo" label="预报单编号">
            <XdoIInput type="text" v-model="frmData.preEmsListNo" disabled :maxlength="20"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="forwardName" label="货运代理">
            <XdoIInput type="text" v-model="frmData.forwardName" :disabled="showDisableMe" :clearable="!showDisableMe"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gwUser" label="关务人员">
            <XdoIInput type="text" v-model="frmData.gwUser" :disabled="showDisableMe" :clearable="!showDisableMe" :maxlength="30"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="sa" label="SA">
            <XdoIInput type="text" v-model="frmData.sa" :disabled="showDisableMe" :clearable="!showDisableMe" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="overseasShipper" label="境外发货人">
            <xdo-select v-model="frmData.overseasShipper" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :options="overseasShipperData" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="trafMode" label="运输方式">
            <xdo-select v-model="frmData.trafMode" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="TRANSF" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="mawb" label="主运单号">
            <XdoIInput type="text" v-model="frmData.mawb"
                       :disabled="showDisableMe" :clearable="!showDisableMe" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="hawb" label="分运单号">
            <XdoIInput type="text" v-model="frmData.hawb"
                       :disabled="showDisableMe" :clearable="!showDisableMe" :maxlength="100"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="wrapType" label="包装信息">
            <xdo-select v-model="frmData.wrapType" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="WRAP" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="packNum" label="件数">
            <xdo-input type="text" v-model="frmData.packNum" decimal notConvertNumber
                       int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="weight" label="计费重量">
            <xdo-input type="text" v-model="frmData.weight" decimal notConvertNumber
                       int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="volume" label="体积">
            <xdo-input type="text" v-model="frmData.volume" decimal notConvertNumber
                       int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="grossWt" label="毛重">
            <xdo-input type="text" v-model="frmData.grossWt" decimal notConvertNumber
                       int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="netWt" label="净重">
            <xdo-input type="text" v-model="frmData.netWt" decimal notConvertNumber
                       int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
          </XdoFormItem>
          <XdoFormItem prop="customerCode" label="报关行">
            <xdo-select v-model="frmData.customerCode" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :options="customerCodeData" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="deliveryDate" label="提货日期">
            <XdoDatePicker type="date" :value="frmData.deliveryDate" transfer
                           :disabled="showDisableMe" :clearable="!showDisableMe"
                           @on-change="frmData.deliveryDate=$event">
            </XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="etdDate" label="ETD日期">
            <XdoDatePicker type="date" :value="frmData.etdDate" transfer
                           :disabled="showDisableMe" :clearable="!showDisableMe"
                           @on-change="frmData.etdDate=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="etaDate" label="ETA日期">
            <XdoDatePicker type="date" :value="frmData.etaDate" transfer
                           :disabled="showDisableMe" :clearable="!showDisableMe"
                           @on-change="frmData.etaDate=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="ataDate" label="ATA日期">
            <XdoDatePicker type="date" :value="frmData.ataDate" transfer
                           :disabled="showDisableMe" :clearable="!showDisableMe"
                           @on-change="frmData.ataDate=$event"></XdoDatePicker>
          </XdoFormItem>
          <XdoFormItem prop="tradeCountry" label="启运国">
            <xdo-select v-model="frmData.tradeCountry" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="COUNTRY_OUTDATED" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="feeRate" label="运费">
            <div style="display:flex;justify-self:left">
              <div style="width: 50%">
                <xdo-input type="text" v-model="frmData.feeRate" decimal notConvertNumber
                           int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
              </div>
              <div style="width: 50%">
                <XdoFormItem>
                  <xdo-select v-model="frmData.feeCurr" :disabled="showDisableMe" :clearable="!showDisableMe"
                              :asyncOptions="pcodeList" meta="CURR_OUTDATED" :optionLabelRender="pcodeRender"></xdo-select>
                </XdoFormItem>
              </div>
            </div>
          </XdoFormItem>
          <XdoFormItem prop="otherRate" label="杂费">
            <div style="display:flex;justify-self:left">
              <div style="width: 50%">
                <xdo-input type="text" v-model="frmData.otherRate" decimal notConvertNumber
                           int-length="14" precision="5" :disabled="showDisableMe" :clearable="!showDisableMe"></xdo-input>
              </div>
              <div style="width: 50%">
                <XdoFormItem>
                  <xdo-select v-model="frmData.otherCurr" :disabled="showDisableMe" :clearable="!showDisableMe"
                              :asyncOptions="pcodeList" meta="CURR_OUTDATED" :optionLabelRender="pcodeRender">
                  </xdo-select>
                </XdoFormItem>
              </div>
            </div>
          </XdoFormItem>
          <XdoFormItem prop="despPort" label="启运港">
            <xdo-select v-model="frmData.despPort" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="PORT_LIN" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="destPort" label="经停港">
            <xdo-select v-model="frmData.destPort" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="PORT_LIN" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="entryPort" label="入境口岸">
            <xdo-select v-model="frmData.entryPort" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :asyncOptions="pcodeList" meta="CIQ_ENTY_PORT" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="containerLcl" label="整箱/拼箱">
            <xdo-select v-model="frmData.containerLcl" :disabled="showDisableMe" :clearable="!showDisableMe"
                        :options="comboxDataMe.preDecErpHead.containerLclHead" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="status" label="单据状态">
            <xdo-select v-model="frmData.status" disabled
                        :options="comboxDataMe.preDecErpHead.status" :optionLabelRender="pcodeRender">
            </xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="insertTime" label="创建时间">
            <XdoIInput type="text" v-model="frmData.insertTime" disabled></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注" class="dc-merge-1-3">
            <XdoIInput type="text" v-model="frmData.note"
                       :disabled="showDisableMe" :clearable="!showDisableMe" :maxlength="512"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="集装箱信息">
        <div style="padding: 2px 2px 2px 30px;">
          <table id="tableMe" class="tableMe">
            <thead>
            <tr>
              <th style="width: 60px;">序号</th>
              <th style="width: 180px;">集装箱规格型号</th>
              <th style="width: 200px;">集装箱号</th>
              <th style="width: 150px;">拼箱标识</th>
              <th style="width: 150px;">自重</th>
            </tr>
            </thead>
            <tbody>
            <tr :key="index" v-for="(item,index) in frmData.containerList">
              <td>
                <span>
                  {{ index + 1 }}
                </span>
              </td>
              <td>
                <Select v-model="item.containerModel" transfer filterable :disabled="showDisableMe"
                        :clearable="!showDisableMe">
                  <Option v-for="itemp in comboxDataMe.preDecErpHead.containerModel" :value="itemp.value"
                          :key="itemp.value">{{itemp.value + ' ' + itemp.label}}
                  </Option>
                </Select>
              </td>
              <td>
                <Select v-model="item.containerLcl" transfer filterable :disabled="showDisableMe"
                        :clearable="!showDisableMe">
                  <Option v-for="itemp in comboxDataMe.preDecErpHead.containerLclBody" :value="itemp.value"
                          :key="itemp.value">{{itemp.value + ' ' + itemp.label}}
                  </Option>
                </Select>
              </td>
              <td>
                <xdo-input type="text" v-model="item.containerWt" decimal notConvertNumber
                           int-length="14" precision="5" :disabled="showDisableMe"
                           :clearable="!showDisableMe"></xdo-input>
              </td>
              <td>
                <Input v-model="item.containerMd" :disabled="showDisableMe" :clearable="!showDisableMe"/>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button style="margin-left: 5px;" v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>
      </template>
    </div>
    <DailogSendBack :show.sync="isShowReturn" @onConfirm="handleReturnConfirm"></DailogSendBack>
    <DailogSend ref="DailogSend" :show.sync="isShowSend" @onConfirm="handleSendConfirm"></DailogSend>
  </section>
</template>

<script>
  import DailogSend from './DailogSend'
  import DailogSendBack from './DailogSendBack'
  import { detailJS } from './js/preDecErpHeadDetail'
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'

  export default {
    name: 'PreDecErpHeadDetail',
    mixins: [detailJS],
    components: {
      DailogSend,
      DailogSendBack
    },
    data() {
      return {
        rulesHeader: {},
        isShowSend: false,
        isShowReturn: false,
        formName: 'dataForm',
        customerCodeData: [],       // 报关行
        overseasShipperData: [],    // 境外发货人
        comboxDataMe: interfaceImportManagementConst,
        ajaxUrl: {
          get: csApiKnorr.interfaceImportManagement.preDecErpHead.get,
          send: csApiKnorr.interfaceImportManagement.preDecErpHead.send,
          return: csApiKnorr.interfaceImportManagement.preDecErpHead.return,
          insert: csApiKnorr.interfaceImportManagement.preDecErpHead.insert,
          update: csApiKnorr.interfaceImportManagement.preDecErpHead.update
        }
      }
    },
    created() {
      /**
       * 获取  报关行 和 供应商的信息
       */
      this.$http.post(csApiKnorr.comomApiInfo.baseCompany.selectComboxByCode + '/CUT,PRD').then(res => {
        if (res.data.data) {
          //报关行
          this.customerCodeData = res.data.data.filter(x => x.CODE && x.TYPE === 'CUT').map(
            (item) => {
              return { value: item.CODE, label: item.LABEL }
            }
          )
          //境外发货人  供应商
          this.overseasShipperData = res.data.data.filter(x => x.VALUE && x.TYPE === 'PRD').map(
            (item) => {
              return { value: item.VALUE, label: item.LABEL }
            }
          )
        }
      }).catch(() => {
        this.customerCodeData = []
        this.overseasShipperData = []
      }).finally(() => {
      })
    },
    methods: {

      /**
       * 加载后台数据
       * */
      loadBackendData(editConfig) {
        this.$http.get(`${this.ajaxUrl.get}/${editConfig.editData.sid}`).then((res) => {
          this.$set(this, 'frmData', res.data.data)
        }).catch(() => {
        }).finally(() => {
        })
      },

      /**
       * 删除
       * */
      delJiZhuangXiang(index) {
        this.frmData.containerList.splice(index, 1)

      },

      /**
       * 新增集装箱信息
       */
      addJiZhuangXiang() {
        let data = [
          {
            containerMd: '', //集装箱号
            containerModel: '' //集装箱型号
          }
        ]
        this.frmData.containerList = [...this.frmData.containerList, ...data]
      },
      /**
       * 退回
       */
      handleReturn() {
        this.isShowReturn = true
      },
      /**
       * 退回 确定
       * @param frmData
       */
      handleReturnConfirm(frmData) {


        this.$http.post(`${this.ajaxUrl.return}/${this.frmData.sid}`, frmData).then(() => {
          this.frmData.status = '-1'
          this.isShowReturn = false
          this.$Message.success('退回完成!')
        }).catch(() => {
        }).finally(() => {

        })
      },
      /**
       * 关务分单
       */
      handleSend() {
        if (this.frmData.status === '-1') {
          this.$Message.warning('已退回，无法操作!')
          return
        }
        if (this.frmData.status === '2') {
          this.$Message.warning('已分单，无法操作!')
          return
        }
        if (this.frmData.status === '3') {
          this.$Message.warning('预报完结，无法操作!')
          return
        }
        this.isShowSend = true
      },
      /*
       *关务分单  确定
       * @param frmData
       */
      handleSendConfirm(frmData) {
        this.$refs.DailogSend.isLoading=true
        this.$http.post(`${this.ajaxUrl.send}/${this.frmData.sid}`, frmData).then(() => {
          this.frmData.status = '2'
          this.frmData.customerCode = frmData.customerCode
          this.isShowSend = false
          this.$Message.success('分单完成!')
        }).catch(() => {
        }).finally(() => {
          this.$refs.DailogSend.isLoading=false
        })
      }
    },
    computed: {
      /**
       * 输入组件是否可输
       * @returns {boolean}
       */
      showDisableMe() {
        return true
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard.ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard.ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  .tableMe {
    border-spacing: 0;
    border-collapse: collapse;
  }

  #tableMe th {
    height: 20px;
    font-size: 11px;
    line-height: 25px;
    border: #e8eaec solid 1px;
    background-color: #f5f7f7;
  }

  #tableMe td {
    height: 20px;
    font-size: 13px;
    padding: 5px 10px;
    line-height: 25px;
    text-align: center;
    border: #e8eaec solid 1px;
  }
</style>












