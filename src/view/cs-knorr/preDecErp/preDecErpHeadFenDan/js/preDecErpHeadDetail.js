import { commEdit } from '@/view/cs-knorr/common/comm'
import { editStatus } from '@/view/cs-knorr/common/comm/constant'


export const detailJS = {
  mixins: [commEdit],
  data() {
    return {
      buttons: [
        {
          type: 'success',
          disabled: false,
          loading: false,
          needed: true,
          click: this.handleSend,
          icon: 'dc-btn-save',
          label: '关务分单'
        },
        {
          type: 'warning',
          disabled: false,
          loading: false,
          needed: true,
          click: this.handleReturn,
          icon: 'dc-btn-save',
          label: '退回'
        },
        {
          type: 'primary',
          disabled: false,
          loading: false,
          needed: true,
          click: this.handleBack,
          icon: 'dc-btn-cancel',
          label: '返回'
        }
      ]
    }
  },
  watch: {
    'frmData.status': {
      immediate: true,
      handler: function(val) {
          this.buttons[0].needed = val==='1'
          this.buttons[1].needed = val==='1'
      }
    }
  },
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      this.doSave((res) => {
        this.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        preEmsListNo: '',//预报单编号
        gwUser: '',//关务人员
        sa: '',//SA
        overseasShipper: '',//境外发货人
        trafMode: '',//运输方式
        mawb: '',//主运单号
        hawb: '',//分运单号
        wrapType: '',//包装信息
        packNum: '',//件数
        weight: '',//计费重量
        volume: '',//体积
        grossWt: '',//毛重
        netWt: '',//净重
        customerCode: '',//报关行
        deliveryDate: '',//提货日期
        etdDate: '',//ETD日期
        etaDate: '',//ETA日期
        ataDate: '',//ATA日期
        tradeCountry: '',//启运国
        feeRate: '',//运费
        feeCurr: '',//运费币制
        otherCurr: '',//杂费币制
        otherRate: '',//杂费
        despPort: '',//启运港
        destPort: '',//经停港
        entryPort: '',//入境口岸
        status: '',//状态 0 暂存、-1 退回、1 预报单提交、2 关务分单、3 接收委托、8 预报单完结
        entrustStatus: '',//受托标记 0 未受托, 1 已受托 默认未受托
        entrustDate: '',//受托时间
        note: '',//备注
        insertUserName: '',//货运代理
        insertTime: '',//创建时间
        containerList:[
          // {
          //   containerMd:'', //集装箱号
          //   containerModel:'' //集装箱型号
          // }
        ],
        containerLcl:'',//整箱/拼箱
        forwardName:'',
        forwardCode:'',

      }
    }

  }


}






