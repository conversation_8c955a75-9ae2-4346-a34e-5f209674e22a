import pms from '@/libs/pms'
import { csApiKnorr } from '@/view/cs-knorr/common/comm'
import { commColumnsCustom } from '@/view/cs-knorr/common/comm'
import { dynamicImport } from '@/view/cs-knorr/common/dynamic-import/dynamicImport'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  data() {
    return {
      // 查询条件行数
      searchLines: 5,
      isShowSend: false,
      isShowReturn: false,
      isEditRowIn: ['-1', '0'],             // 可编辑的状态    退回 暂存     当前行的状态    status
      gridConfig: {
        exportTitle: '预报单表头'
      },
      toolbarEventMap: {
        'send': this.handleSend,            // 分担
        'return': this.handleReturn,        // 退回
        'export': this.handleDownload,
        'inforFill': this.handleInforFill   // 信息补充
      },
      ajaxUrl: {
        send: csApiKnorr.interfaceImportManagement.preDecErpHead.send,
        return: csApiKnorr.interfaceImportManagement.preDecErpHead.return,
        inforFill: csApiKnorr.interfaceImportManagement.preDecErpHead.inforFill,
        exportUrl: csApiKnorr.interfaceImportManagement.preDecErpHead.exportUrl2,
        selectAllPaged: csApiKnorr.interfaceImportManagement.preDecErpHead.selectAllPagedFenDan
      }
    }
  },
  methods: {
    /**
     * 退回
     */
    handleReturn() {
      if (this.checkRowSelected('退回')) {
        let count = this.gridConfig.selectRows.filter(x => x.status === '-1')
        if (count && count.length > 0) {
          this.$Message.warning('存在已退回的数据，无法操作!')
          return
        }

        let count1 = this.gridConfig.selectRows.filter(x => x.status === '2')
        if (count1 && count1.length > 0) {
          this.$Message.warning('存在已分单的数据，无法操作!')
          return
        }
        let count2 = this.gridConfig.selectRows.filter(x => x.status === '3')
        if (count2 && count2.length > 0) {
          this.$Message.warning('存在预报完结的数据，无法操作!')
          return
        }
        // { value: '-1', label: '退回' },
        // { value: '0', label: '暂存' },
        // { value: '1', label: '待分单' },
        // { value: '2', label: '已分单' },
        // { value: '3', label: '预报完结' }
        this.isShowReturn = true
      }
    },
    /**
     * 退回 确定
     * @param frmData
     */
    handleReturnConfirm(frmData) {
      let sids = this.getSelectedParams()
      this.setButtonLoading('return', true)
      this.$http.post(`${this.ajaxUrl.return}/${sids}`, frmData).then(() => {
        this.isShowReturn = false
        this.$Message.success('退回完成!')
        this.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        this.setButtonLoading('return', false)
      })
    },
    /**
     * 关务分单
     */
    handleSend() {
      if (this.checkRowSelected('分单')) {

        let count1 = this.gridConfig.selectRows.filter(x => x.status === '-1')
        if (count1 && count1.length > 0) {
          this.$Message.warning('存在已退回的数据，无法操作!')
          return
        }

        let count = this.gridConfig.selectRows.filter(x => x.status === '2')
        if (count && count.length > 0) {
          this.$Message.warning('存在已分单的数据，无法操作!')
          return
        }
        let count2 = this.gridConfig.selectRows.filter(x => x.status === '3')
        if (count2 && count2.length > 0) {
          this.$Message.warning('存在预报完结的数据，无法操作!')
          return
        }

        // { value: '-1', label: '退回' },
        // { value: '0', label: '暂存' },
        // { value: '1', label: '待分单' },
        // { value: '2', label: '已分单' },
        // { value: '3', label: '预报完结' }
        this.isShowSend = true
      }
    },
    /*
     *关务分单  确定
     * @param frmData
     */
    handleSendConfirm(frmData) {
      let sids = this.getSelectedParams()
      this.setButtonLoading('send', true)
      this.$refs.DailogSend.isLoading = true
      this.$http.post(`${this.ajaxUrl.send}/${sids}`, frmData).then(() => {
        this.isShowSend = false
        this.$Message.success('分单完成!')
        this.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        this.$refs.DailogSend.isLoading = false
        this.setButtonLoading('send', false)
      })
    },
    /**
     * 导出
     */
    handleDownload() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
