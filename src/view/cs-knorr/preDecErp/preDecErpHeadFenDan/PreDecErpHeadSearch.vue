<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100"
             inline>
      <XdoFormItem prop="preEmsListNo" label="预报单编号">
        <XdoIInput type="text" v-model="searchParam.preEmsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="mawb" label="主运单号">
        <XdoIInput type="text" v-model="searchParam.mawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="hawb" label="分运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gwUser" label="关务人员">
        <XdoIInput type="text" v-model="searchParam.gwUser"></XdoIInput>
      </XdoFormItem>
<!--      <DcDateRange label="受托日期" @onDateRangeChanged="handleDateChange"></DcDateRange>-->

      <XdoFormItem prop="overseasShipper" label="境外发货人">
        <xdo-select v-model="searchParam.overseasShipper"
                    :options="this.overseasShipperData"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>

      <XdoFormItem prop="customerCode" label="报关行">
        <xdo-select v-model="searchParam.customerCode"
                    :options="this.customerCodeData"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>


      <XdoFormItem prop="forwardCode" label="货运代理">
        <xdo-select v-model="searchParam.forwardCode"
                    :options="this.forwardCodeData"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode"
                    :asyncOptions="pcodeList"
                    meta='TRANSF'
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryPort" label="入境口岸">
        <xdo-select v-model="searchParam.entryPort"
                    :asyncOptions="pcodeList"
                    meta='CIQ_ENTY_PORT'
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeCountry" label="启运国">
        <xdo-select v-model="searchParam.tradeCountry"
                    :asyncOptions="pcodeList"
                    meta='COUNTRY_OUTDATED'
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="despPort" label="启运港">
        <xdo-select v-model="searchParam.despPort"
                    :asyncOptions="pcodeList"
                    meta='PORT_LIN'
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="sa" label="SA">
        <XdoIInput type="text" v-model="searchParam.sa"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="单据状态">
        <xdo-select v-model="searchParam.status"
                    :options="this.comboxData.preDecErpHead.status"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <DcDateRange label="提交分单日期" @onDateRangeChanged="handleDateChangeSubmitDate" :values="ieDefaultDates"></DcDateRange>
    </XdoForm>
  </section>
</template>

<script>
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'
  import DcDateRange from '@/view/cs-knorr/common/components/dc-date-range/DcDateRange'


  export default {
    name: 'PreDecErpHeadSearch',
    components: { DcDateRange },
    data() {
      return {
        comboxData: interfaceImportManagementConst,
        searchParam: {
          preEmsListNo: '',//预报单编号
          gwUser: '',//关务人员
          sa: '',//SA
          overseasShipper: '',//境外发货人
          trafMode: '',//运输方式
          mawb: '',//主运单号
          hawb: '',//分运单号
          wrapType: '',//包装信息
          packNum: '',//件数
          weight: '',//计费重量
          volume: '',//体积
          grossWt: '',//毛重
          netWt: '',//净重
          customerCode: '',//报关行
          deliveryDate: '',//提货日期
          etdDate: '',//ETD日期
          etaDate: '',//ETA日期
          ataDate: '',//ATA日期
          tradeCountry: '',//启运国
          feeRate: '',//运费
          feeCurr: '',//运费币制
          otherCurr: '',//杂费币制
          otherRate: '',//杂费
          despPort: '',//启运港
          destPort: '',//经停港
          entryPort: '',//入境口岸
          status: '',//状态 0 暂存、-1 退回、1 预报单提交、2 关务分单、3 接收委托、8 预报单完结
          entrustStatus: '',//受托标记 0 未受托, 1 已受托 默认未受托
          entrustDate: '',//受托时间
          entrustDateFrom: '', //受托时间
          entrustDateTo: '',//受托时间
          note: '',//备注
          insertUserName: '',//创建人名称
          insertTime: '',//创建时间
          forwardCode: '', //货运代理
          submitDateFrom: '', //提交分单时间
          submitDateTo: '',//提交分单时间
        },
        customerCodeData: [],
        overseasShipperData: [],
        forwardCodeData: []

      }
    },
    created() {
      this.$http.post(csApiKnorr.comomApiInfo.baseCompany.selectComboxByCode + '/CUT,PRD,FOD').then(res => {
        if (res.data.data) {
          //报关行
          this.customerCodeData = res.data.data.filter(x => x.CODE && x.TYPE === 'CUT').map(
            (item) => {
              return { value: item.CODE, label: item.LABEL }
            }
          )
          //境外发货人  供应商
          this.overseasShipperData = res.data.data.filter(x => x.VALUE && x.TYPE === 'PRD').map(
            (item) => {
              return { value: item.VALUE, label: item.LABEL }
            }
          )
          // 货运代理 货代
          this.forwardCodeData = res.data.data.filter(x => x.CODE && x.TYPE === 'FOD').map(
            (item) => {
              return { value: item.CODE, label: item.LABEL }
            }
          )
        }
      }).catch(() => {
        this.customerCodeData = []
        this.overseasShipperData = []
        this.forwardCodeData = []
      }).finally(() => {
      })
    },
    methods: {
      /**
       * 受托日期
       * @param values
       */
      handleDateChange(values) {
        this.searchParam.entrustDateFrom = values[0]
        this.searchParam.entrustDateTo = values[1]
      },
      /**
       * 提交分单日期
       */
      handleDateChangeSubmitDate(values){
        this.searchParam.submitDateFrom = values[0]
        this.searchParam.submitDateTo = values[1]
      },

    },
    computed: {
      /**
       * 提交分单的默认 时间段  一个月
       * @returns {string[]}
       */
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    }

  }
</script>
<style scoped>
</style>














