<template>
  <XdoModal
    v-model="show"
    :mask-closable="false"
    :closable="false"
    :footer-hide="true"
    :mask="true"
    width="600"
    title="信息补充">
    <slot></slot>
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm class="dc-form dc-form-2 xdo-enter-form" ref="form" :show-message="false" :model="frmData"
             :label-width="80">
      <XdoFormItem label="ATA日期">
        <XdoDatePicker type="date" :value="frmData.ataDate" transfer
                       @on-change="frmData.ataDate=$event"></XdoDatePicker>
      </XdoFormItem>
      <XdoFormItem label="包装信息">
        <xdo-select v-model="frmData.wrapType"
                    :asyncOptions="pcodeList"
                    meta='WRAP'
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>

      <XdoFormItem class="dc-merge-1-3" style="text-align: right">
        <XdoButton type="success" icon="ios-cloud-upload" @click="handleConfirm">保存
        </XdoButton>
        <XdoButton type="error" icon="ios-close" style="margin-left:5px" @click="handleClose">关闭</XdoButton>
      </XdoFormItem>
    </XdoForm>
  </XdoModal>
</template>


<script>
  export default {
    name: 'DailogInforFill', //信息补充
    props: {
      show: { type: Boolean, required: true }
    },
    data() {
      return {
        frmData: {
          ataDate: '', //ATA日期
          wrapType: '' //包装信息
        }
      }
    },
    methods: {
      /**
       * 确定
       */
      handleConfirm() {

        this.$emit('onConfirm', this.frmData,this)
      },
      /**
       * 关闭
       */
      handleClose() {
        this.$emit('update:show', false)
      }
    },
    watch:{
      show(val){
        if (val){
          this.frmData.ataDate=''
          this.frmData.wrapType=''
        }
      }
    }
  }
</script>
<style scoped>

  .dc-form-2{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    grid-column-gap: 2px
  }

</style>
