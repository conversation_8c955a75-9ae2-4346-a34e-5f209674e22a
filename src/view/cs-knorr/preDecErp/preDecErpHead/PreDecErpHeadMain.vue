<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <PreDecErpHeadSearch ref="headSearch"></PreDecErpHeadSearch>
          </div>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <div class="action" ref="area_actions">
          <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
        </div>
      </XdoCard>
      <XdoCard :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <PreDecErpHeadTab v-if="!showList" @onEditBack="editBack" :editConfig="editConfig"></PreDecErpHeadTab>
    <TableColumnSetup v-model="showtableColumnSetup" :resId="tableId"
                      @updateColumns="handleUpdateColumn" :columns="totalColumns"
                      class="height:500px"></TableColumnSetup>
    <DailogInforFill :show.sync="isShowInforFill" @onConfirm="handleComfirmInfoFill"></DailogInforFill>
    <DcImport v-model="importConfig.show" :config="importConfig" :startRow="importConfig.startRow"
              @importSuccess="handleInforFillImportSuccess"></DcImport>
  </section>
</template>

<script>
  import { mainJS } from './js/preDecErpHeadMain'
  import DailogInforFill from './DailogInforFill'
  import PreDecErpHeadTab from './PreDecErpHeadTab'
  import { columns } from './js/preDecErpHeadColumns'
  import PreDecErpHeadSearch from './PreDecErpHeadSearch'
  import DcImport from '@/view/cs-knorr/common/components/dc-import/DcImport'
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'

  export default {
    name: 'PreDecErpHeadMain',
    moduleName: '预报单表头',
    components: {
      DcImport,
      DailogInforFill,
      PreDecErpHeadTab,
      PreDecErpHeadSearch
    },
    mixins: [columns, mainJS],
    data() {
      return {
        comboxData: interfaceImportManagementConst
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
