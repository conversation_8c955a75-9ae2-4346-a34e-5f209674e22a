<template>
  <XdoTabs v-model="tabName" :animated="false" type="card" class="dc-tab" name="Tab" @on-click="tabClick">
    <TabPane name="head" label="预报单录入" tab="Tab">
      <PreDecErpHeadDetail v-if="tabs.head" :editConfig="editConfig" @onEditBack="handleEditBack"></PreDecErpHeadDetail>
    </TabPane>
    <!--    maintainStatus：已生成-->
    <TabPane name="list" label="文档信息" tab="Tab" v-if="editConfig.editData.sid">
      <AcmpInfoListCustom title="文档信息"
                          :sid="editConfig.editData.sid"
                          :showAction="!showDisable"
                          :just-view="showDisable"
                          businessType="yuBaoDanLuRu"></AcmpInfoListCustom>
    </TabPane>
    <TabPane name="log" label="审批情况" tab="Tab" v-if="editConfig.editData.sid">
      <xdo-ag-grid ref="ref_agGridLog"
                   :columns="gridConfigLog.gridColumns"
                   :data="gridConfigLog.data"
                   :height="680">
      </xdo-ag-grid>
    </TabPane>
    <template v-slot:extra>
      <XdoButton type="text" @click="backToList">
        <XdoIcon type="ios-undo" size="22" style="color: green"/>
      </XdoButton>
    </template>
  </XdoTabs>
</template>

<script>

  import { editStatus } from '@/view/cs-knorr/common/comm/constant'
  import AcmpInfoListCustom from '@/view/cs-knorr/common/components/AcmpInfoListCustom'
  import PreDecErpHeadDetail from './PreDecErpHeadDetail'
  import { columnRender } from '@/view/cs-knorr/common/comm'
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'

  export default {
    name: 'PreDecErpHeadTab.vue',
    moduleName: '预报单-表头-详细页面',
    mixins:[columnRender],
    components: {
      AcmpInfoListCustom,
      PreDecErpHeadDetail
    },
    props: {
      editConfig: {
        type: Object,
        default: () => ({
          editData: {},
          editStatus: editStatus.SHOW
        })
      }
    },
    data() {
      return {
        tabName: 'head',
        tabs: {
          head: true,
          list: false
        },
        //日志
        gridConfigLog: {
          gridColumns: [
            { title: '节点', width: 150, key: 'status',
              render: (h, params) => {
                return this.cmbShowRender(h, params, interfaceImportManagementConst.preDecErpHead.status)
              }
            },
            { title: '操作人', width: 150, key: 'insertUserName' },
            { title: '时间', width: 150, key: 'insertTime' },
            { title: '审核意见', width: 300, key: 'note' }],
          data: []
        }

      }
    },
    created() {

    },
    methods: {

      handleEditBack(backObj) {
        this.$emit('onEditBack', backObj)
      },


      /**
       * 供编辑界面传回信息调用
       * @param backObj
       */
      backToList() {
        this.$emit('onEditBack', {
          showList: true,
          editStatus: editStatus.SHOW,
          editData: {}
        })
      },

      tabClick(tableName) {
        this.tabs[tableName] = true
        if( tableName==='log'){
          const sid = this.editConfig.editData.sid
          this.$http.get(`${csApiKnorr.interfaceImportManagement.preDecErpHead.approveGet}/${sid}`).then((res) => {
            this.gridConfigLog.data = res.data.data
          }).catch(() => {
          }).finally(() => {
          })


        }

      }
    },
    computed: {
      isShowQuoConfirmDetail: function() {
        return this.tabName === 'head'
      },
      showDisable: function() {
        return this.editConfig.editStatus !== editStatus.EDIT
      }
    }

  }
</script>
