import pms from '@/libs/pms'
import { csApiKnorr } from '@/view/cs-knorr/common/comm'
import { commColumnsCustom } from '@/view/cs-knorr/common/comm'
import { editStatus } from '@/view/cs-knorr/common/comm/constant'
import { dynamicImport } from '@/view/cs-knorr/common/dynamic-import/dynamicImport'

export const mainJS = {
  mixins: [commColumnsCustom, pms, dynamicImport],
  data() {
    return {
      // 查询条件行数
      searchLines: 2,
      ajaxUrl: {
        delete: csApiKnorr.interfaceImportManagement.preDecErpHead.delete,
        exportUrl: csApiKnorr.interfaceImportManagement.preDecErpHead.exportUrl,
        selectAllPaged: csApiKnorr.interfaceImportManagement.preDecErpHead.selectAllPaged,
        copy: csApiKnorr.interfaceImportManagement.preDecErpHead.copy,
        takeYuBaoDan: csApiKnorr.interfaceImportManagement.preDecErpHead.takeYuBaoDan,
        inforFill: csApiKnorr.interfaceImportManagement.preDecErpHead.inforFill
      },
      gridConfig: {
        exportTitle: '预报单表头'
      },
      toolbarEventMap: {
        'add': this.handleAdd,           // '新增'
        'edit': this.handleEdit,         // '编辑'
        'delete': this.handleDelete,     // '删除'
        'export': this.handleDownload,   // '导出'
        'import': this.handleImport,     // '导入'
        'copy': this.handleCopy,   //复制
        'takeYuBaoDan': this.handleTakeYuBaoDan, //提交预报单
        'inforFill': this.handleInforFill, // 信息补充
        'inforFillImport': this.handleInforFillImport // 信息补充导入
      },
      isShowInforFill: false,
      importConfig: {
        show: false,
        startRow: 5,
        tplName: '信息补充导入',
        url: csApiKnorr.interfaceImportManagement.preDecErpHead.inforFillImport,
        tplUrl: csApiKnorr.interfaceImportManagement.preDecErpHead.exportSupplement + '/tpl',
        correctUrl: csApiKnorr.interfaceImportManagement.preDecErpHead.inforFillImport,
        errorUrl: csApiKnorr.interfaceImportManagement.preDecErpHead.exportSupplement
      },
      isEditRowIn: ['-1', '0'] //可编辑的状态    退回 暂存     当前行的状态    status
    }
  },
  mounted: function () {
  },
  methods: {
    /**
     * 编辑事件  重写 comm
     * @param row
     */
    handleEditByRow(row) {
      if (!this.isEditRowIn.includes(row.status)) {
        this.$Message.warning('当前单据状态不支持编辑!')
        return
      }
      this.editConfig.editStatus = editStatus.EDIT
      this.editConfig.editData = row
      this.showList = false
    },
    /**
     * 信息补充导入
     */
    handleInforFillImport() {
      this.importConfig.show = true
    },
    /**
     * 信息补充导入 完成后 事件
     */
    handleInforFillImportSuccess() {
      this.isShowInforFill = false
      this.handleSearchSubmit()
    },
    /**
     * 信息补充
     */
    handleInforFill() {
      if (this.checkRowSelected('信息补充', true)) {
        if (this.gridConfig.selectRows[0].status === '3') {
          this.$Message.warning('预报已完结，无法操作')
          return
        }
        // { value: '-1', label: '退回' },
        // { value: '0', label: '暂存' },
        // { value: '1', label: '待分单' },
        // { value: '2', label: '已分单' },
        // { value: '3', label: '预报完结' }
        this.isShowInforFill = true
      }
    },
    /**
     * 信息补充提交
     */
    handleComfirmInfoFill(frmData, me) {
      const sid = this.gridConfig.selectRows[0].sid
      this.setButtonLoading('inforFill', true)
      this.$http.put(`${this.ajaxUrl.inforFill}/${sid}`, frmData, {noIntercept: true}).then((res) => {
        if (res.data.success) {
          this.$Message.success('保存成功!')
          this.isShowInforFill = false
          this.handleSearchSubmit()
        } else {
          me.$Message.warning(res.data.message)
        }
      }).catch(() => {
      }).finally(() => {
        this.setButtonLoading('inforFill', false)
      })
    },
    /**
     * 提交预报单
     */
    handleTakeYuBaoDan() {
      if (this.checkRowSelected('提交预报单')) {
        this.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '如果提交,生成的预报单将不能删除!',
          onOk: () => {
            let params = this.getSelectedParams()
            this.setButtonLoading('takeYuBaoDan', true)
            this.$http.post(`${this.ajaxUrl.takeYuBaoDan}/${params}`).then(() => {
              this.$Message.success('提交成功!')
              this.handleSearchSubmit()
            }).catch(() => {
            }).finally(() => {
              this.setButtonLoading('takeYuBaoDan', false)
            })
          }
        })
      }
    },
    /**
     * 复制
     */
    handleCopy() {
      if (this.checkRowSelected('复制', true)) {
        let params = this.gridConfig.selectRows[0].sid
        this.setButtonLoading('copy', true)
        this.$http.post(`${this.ajaxUrl.copy}/${params}`).then(() => {
          this.$Message.success('复制成功!')
          this.handleSearchSubmit()
        }).catch(() => {
        }).finally(() => {
          this.setButtonLoading('copy', false)
        })
      }
    },
    /**
     * 弹出导入窗体
     */
    handleImport() {
      this.modelImportShow = true
    },
    /**
     * 导入成功后事件
     */
    onAfterImport() {
      this.modelImportShow = false
      this.getList()
    },
    /**
     * 删除
     */
    handleDelete() {
      this.doDelete(this.ajaxUrl.delete, this.actions.findIndex(it => it.command === 'delete'))
    },
    /**
     * 导出
     */
    handleDownload() {
      this.doExport(this.ajaxUrl.exportUrl, this.actions.findIndex(it => it.command === 'export'))
    }
  }
}
