import { columnRender } from '@/view/cs-knorr/common/comm'
import { csApiKnorr } from '@/view/cs-knorr/common/comm'

const columns = {
  mixins: [columnRender],
  data() {
    let totalColumnsBase = [
      { title: '预报单编号', width: 150, key: 'preEmsListNo' },
      {
        title: '单据状态', width: 150, key: 'status',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.comboxData.preDecErpHead.status)
        }
      },
      { title: '货运代理', width: 150, key: 'forwardName',

        // render: (h, params) => {
        //   return this.cmbShowRender(h, params, this.forwardCodeData)
        // }
      },
      { title: '关务人员', width: 150, key: 'gwUser' },
      { title: 'SA', width: 150, key: 'sa' },
      { title: '境外发货人', width: 250, key: 'overseasShipper',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.overseasShipperData)
        }
      },
      {
        title: '运输方式', width: 150, key: 'trafMode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'TRANSF')
        }
      },
      { title: '主运单号', width: 150, key: 'mawb' },
      { title: '分运单号', width: 150, key: 'hawb' },
      {
        title: '包装信息', width: 150, key: 'wrapType',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'WRAP')
        }
      },
      { title: '件数', width: 150, key: 'packNum' },
      { title: '计费重量', width: 150, key: 'weight' },
      { title: '体积', width: 150, key: 'volume' },
      { title: '毛重', width: 150, key: 'grossWt' },
      { title: '净重', width: 150, key: 'netWt' },
      {
        title: '报关行', width: 250, key: 'customerCode',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.customerCodeData)
        }
      },

      { title: '提货日期', width: 150, key: 'deliveryDate'
        ,render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }

      },
      { title: 'ETD日期', width: 150, key: 'etdDate'
        ,render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }

      },
      { title: 'ETA日期', width: 150, key: 'etaDate'
        ,render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }

      },
      { title: 'ATA日期', width: 150, key: 'ataDate'
        ,render: (h, params) => {
          return this.dateTimeShowRender(h, params)
        }

      },
      {
        title: '启运国', width: 150, key: 'tradeCountry',//
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'COUNTRY_OUTDATED')
        }
      },
      { title: '运费', width: 150, key: 'feeRate' },
      {
        title: '运费币制', width: 150, key: 'feeCurr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }
      },
      {
        title: '杂费币制', width: 150, key: 'otherCurr',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CURR_OUTDATED')
        }
      },
      { title: '杂费', width: 150, key: 'otherRate' },
      {
        title: '启运港', width: 150, key: 'despPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'PORT_LIN')
        }

      },
      {
        title: '经停港', width: 150, key: 'destPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'PORT_LIN')
        }
      },
      {
        title: '入境口岸', width: 150, key: 'entryPort',
        render: (h, params) => {
          return this.cmbShowRender(h, params, [], 'CIQ_ENTY_PORT')
        }
      },
      {
        title: '整箱/拼箱', width: 150, key: 'containerLcl',
        render: (h, params) => {
          return this.cmbShowRender(h, params, this.comboxData.preDecErpHead.containerLclHead)
        }
      },

      // {
      //   title: '受托标记', width: 150, key: 'entrustStatus',
      //   render: (h, params) => {
      //     return this.cmbShowRender(h, params, this.comboxData.preDecErpHead.entrustStatus)
      //   }
      // },
      // { title: '受托时间', width: 150, key: 'entrustDate' },
      { title: '创建时间', width: 150, key: 'insertTime' },
      { title: '备注', width: 150, key: 'note' },
      { title: '最终审核意见', width: 200, key: 'approveNote' },



    ]
    return {
      totalColumns: [
        ...totalColumnsBase
      ],
      customerCodeData: [],
      overseasShipperData:[],
      // forwardCodeData:[],
    }
  },
  created() {
    /**
     *  获取  报关行 和 供应商的信息
     */
    this.$http.post(csApiKnorr.comomApiInfo.baseCompany.selectComboxByCode + '/CUT,PRD').then(res => {
      if (res.data.data) {
        //报关行
        this.customerCodeData = res.data.data.filter(x => x.CODE && x.TYPE === 'CUT').map(
          (item) => {
            return { value: item.CODE, label: item.LABEL }
          }
        )
        //境外发货人  供应商
        this.overseasShipperData = res.data.data.filter(x => x.VALUE && x.TYPE === 'PRD').map(
          (item) => {
            return { value: item.VALUE, label: item.LABEL }
          }
        )
        // //货运代理   货代
        // this.forwardCodeData = res.data.data.filter(x => x.CODE && x.TYPE === 'FOD').map(
        //   (item) => {
        //     return { value: item.CODE, label: item.LABEL }
        //   }
        // )
      }
    }).catch(() => {
      this.customerCodeData = []
      this.overseasShipperData = []
    }).finally(() => {
    })
  }
}
export {
  columns
}

