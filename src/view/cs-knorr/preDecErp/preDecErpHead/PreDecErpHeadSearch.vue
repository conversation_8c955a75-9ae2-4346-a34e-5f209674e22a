<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="100"
             inline>
      <XdoFormItem prop="preEmsListNo" label="预报单编号">
        <XdoIInput type="text" v-model="searchParam.preEmsListNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="mawb" label="主运单号">
        <XdoIInput type="text" v-model="searchParam.mawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="hawb" label="分运单号">
        <XdoIInput type="text" v-model="searchParam.hawb"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="gwUser" label="关务人员">
        <XdoIInput type="text" v-model="searchParam.gwUser"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="status" label="单据状态">
        <xdo-select v-model="searchParam.status"
                    :options="this.comboxData.preDecErpHead.status"
                    :optionLabelRender="pcodeRender">
        </xdo-select>
      </XdoFormItem>
      <DcDateRange label="创建日期" @onDateRangeChanged="handleDateChange" :values="ieDefaultDates"></DcDateRange>

    </XdoForm>
  </section>
</template>

<script>
  import { interfaceImportManagementConst } from '@/view/cs-knorr/common/comm/constant'
  import DcDateRange from '@/view/cs-knorr/common/components/dc-date-range/DcDateRange'

  export default {
    name: 'PreDecErpHeadSearch',
    components: {
      DcDateRange
    },
    data() {
      return {
        comboxData: interfaceImportManagementConst,
        searchParam:
          {
            preEmsListNo: '',//预报单编号
            gwUser: '',//关务人员
            sa: '',//SA
            overseasShipper: '',//境外发货人
            trafMode: '',//运输方式
            mawb: '',//主运单号
            hawb: '',//分运单号
            wrapType: '',//包装信息
            packNum: '',//件数
            weight: '',//计费重量
            volume: '',//体积
            grossWt: '',//毛重
            netWt: '',//净重
            customerCode: '',//报关行
            deliveryDate: '',//提货日期
            etdDate: '',//ETD日期
            etaDate: '',//ETA日期
            ataDate: '',//ATA日期
            tradeCountry: '',//启运国
            feeRate: '',//运费
            feeCurr: '',//运费币制
            otherCurr: '',//杂费币制
            otherRate: '',//杂费
            despPort: '',//启运港
            destPort: '',//经停港
            entryPort: '',//入境口岸
            status: '',//状态 0 暂存、-1 退回、1 预报单提交、2 关务分单、3 接收委托、8 预报单完结
            entrustStatus: '',//受托标记 0 未受托, 1 已受托 默认未受托
            entrustDate: '',//受托时间
            note: '',//备注
            insertUserName: '',//创建人名称
            insertTime: '',//创建时间
            insertTimeFrom: '',  //预报日期
            insertTimeTo: ''    //预报日期
          }
      }
    },
    methods: {
      /**
       * 预报日期
       * @param values
       */
      handleDateChange(values) {
        this.searchParam.insertTimeFrom = values[0]
        this.searchParam.insertTimeTo = values[1]
      }

    },
    computed:{
      /**
       * 预报日期默认值
       * @returns {string[]}
       */
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString()
        return [dateFrom, dateTo]
      },
    }

  }
</script>
<style scoped>
</style>














