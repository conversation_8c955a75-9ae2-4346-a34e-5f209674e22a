<template>
  <XdoFormItem :class="className" :label="label" style="padding-right: 0 !important;">
    <span v-if="slotClass !== ''" slot="label" :class="slotClass">{{label}}</span>
    <div class="rangeContainer">
      <XdoFormItem prop="dateFrom" class="side">
        <XdoDatePicker type="date" :options="fromOptions" :disabled="disabled" @on-change="fromDateChange" placeholder="请选择开始时间" v-model="dateFrom"></XdoDatePicker>
      </XdoFormItem>
      <div class="middleStyle">-</div>
      <XdoFormItem prop="dateTo" class="side">
        <XdoDatePicker type="date" :options="toOptions" :disabled="disabled" @on-change="toDateChange" placeholder="请选择结束时间" v-model="dateTo"></XdoDatePicker>
      </XdoFormItem>
    </div>
  </XdoFormItem>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'DcDateRange',
    data() {
      return {
        dateFrom: '',
        dateTo: '',
        fromOptions: {}, //开始日期设置
        toOptions: {}, //结束日期设置
      }
    },
    props: {
      label: {type: String, default: () => ('日期范围')},
      className: {type: String, default: () => ('')},
      fieldNames: {type: Array, default: () => ([])},
      disabled: {type: Boolean, default: () => (false)},
      slotClass: {type: String, default: () => ('')},
      values: {type: Array, default: () => (['', ''])}
    },
    watch: {
      dateFrom: function (val) {
        this.$emit('onDateRangeChanged', [val, this.dateTo], this.realFields)
      },
      dateTo: function (val) {
        this.$emit('onDateRangeChanged', [this.dateFrom, val], this.realFields)
      },
      values: {
        immediate: true,
        handler: function (values) {
          if (Array.isArray(values)) {
            let me = this, startDate = '', endDate = ''
            if (values.length > 0) {
              try {
                if (!isNullOrEmpty(values[0])) {
                  startDate = (new Date(values[0])).format('yyyy-MM-dd')
                }
              } catch (e) {
                startDate = ''
                console.info('起始时间错误: ' + e.message)
              }
              me.$set(me, 'dateFrom', startDate)
            }
            if (values.length > 1) {
              try {
                if (!isNullOrEmpty(values[1])) {
                  endDate = (new Date(values[1])).format('yyyy-MM-dd')
                }
              } catch (e) {
                endDate = ''
                console.info('截止时间错误: ' + e.message)
              }
              me.$set(me, 'dateTo', endDate)
            }
          }
        }
      }
    },
    methods: {
      fromDateChange: function (e) { //设置开始时间
        this.dateFrom = e
        // let startTime = this.dateFrom ? new Date(this.dateFrom).valueOf() : ''
        let startTime = ''
        if (this.dateFrom) {
          let tmpDate = new Date(this.dateFrom)
          startTime = tmpDate.setDate(tmpDate.getDate() - 1).valueOf()
        }
        this.toOptions = {
          disabledDate: date => {
            if (startTime === '') {
              return ''
            }
            return date && (date.valueOf() < startTime)
          }
        }
      },
      toDateChange: function (e) { //设置结束时间
        this.dateTo = e
        // let endTime = this.dateTo ? new Date(this.dateTo).valueOf() - 24 * 60 * 60 * 1000 : ''
        let endTime = ''
        if (this.dateTo) {
          let tmpDate = new Date(this.dateTo)
          endTime = tmpDate.setDate(tmpDate.getDate()).valueOf()
        }
        this.fromOptions = {
          disabledDate: date => {
            if (endTime === '') {
              return ''
            }
            return date && date.valueOf() > endTime
          }
        }
      }
    },
    computed: {
      realFields() {
        let dateFrom = 'dateFrom'
        let dateTo = 'dateTo'
        if (Array.isArray(this.fieldNames) && this.fieldNames.length === 2) {
          if (isNullOrEmpty(this.fieldNames[0]) !== true) {
            dateFrom = this.fieldNames[0]
          } else {
            console.error('未设置开始时间字段')
          }
          if (isNullOrEmpty(this.fieldNames[1]) !== true) {
            dateTo = this.fieldNames[1]
          } else {
            console.error('未设置截止时间字段')
          }
        }
        return [dateFrom, dateTo]
      }
    }
  }
</script>

<style lang="less" scoped>
  .preRequired:before {
    content: '*';
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: 'SimSun';
    font-size: 12px;
    color: blue
  }

  .rangeContainer {
    margin: 0;
    padding: 0;
    display: flex;
    display: -o-flex;
    display: -ms-flex;
    display: -moz-flex;
    display: -webkit-flex;
  }

  .side {
    flex: 1;
    margin: 0 !important;
    padding: 0 !important;
  }

  .middleStyle {
    width: 20px;
    padding-top: 2px;
    text-align: center;
  }
</style>
