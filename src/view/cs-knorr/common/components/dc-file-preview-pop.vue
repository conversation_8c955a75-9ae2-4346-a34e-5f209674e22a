<template>
  <XdoModal mask v-model="show" title="文件预览" fullscreen
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close-circle-outline" size="22"></XdoIcon>
    </a>
    <div class="head">
      <XdoCard class="leftCard" bordered :style="`width: ${leftWidth}px`">
        <embed v-if="isPdf" :src="fileUrl" type="application/pdf" width="100%" :height="autoHeight">
<!--        <XdoButton v-if="isExcel" type="text" size="large" icon="ios-print-outline" @click="onPrint"></XdoButton>-->
<!--        <div v-if="isExcel" v-html="excelToHtml()" :style="`height: ${autoHeight}px`" class="excelDiv"></div>-->
      </XdoCard>
      <XdoCard class="rightCard" :style="`width: ${rightWidth}px`">
        <span>列表</span>
        <div>
          <ul>
            <li v-for="item in fileData" :key="item.sid">
              <a @click.prevent="getPreviewAttach(item)" style="cursor: pointer"
                 :class="{excelName : !item['originFileName'].endsWith('pdf')}">{{item['originFileName']}}</a>
              <a @click.prevent="downLoad(item)" style="margin-left: 5px">下载</a>
            </li>
          </ul>
        </div>
      </XdoCard>
    </div>
  </XdoModal>
</template>

<script>
  import XLSX from 'xlsx'
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'
  import { isNullOrEmpty, blobSaveFile } from '@/libs/util'

  export default {
    name: 'dcFilePreviewPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      fileData: {
        type: Array,
        default: () => ([])
      },
      customizeUrl: {
        type: String,
        default: () => ('')
      }
    },
    data() {
      return {
        fileId: '',
        fileUrl: '',
        fileList: [],
        isPdf: false,
        isExcel: false,
        autoHeight: 800,
        fileContent: '',
        fileShow: false,
        leftWidth: 400,
        rightWidth: 200
      }
    },
    computed: {
      /**
       * 获取pdf文件的url
       */
      getPdfUrl() {
        let me = this
        if (isNullOrEmpty(me.customizeUrl)) {
          return csApiKnorr.comomApiInfo.attachedInfo.getPdf
        }
        return me.customizeUrl
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          if (show) {
            me.getData()
            me.getRecordPreview()
            me.$set(me, 'leftWidth', ((window.innerWidth * 2) / 3))
            me.$set(me, 'rightWidth', ((window.innerWidth * 1) / 5))
            me.$set(me, 'autoHeight', ((window.innerHeight * 4) / 5))
          } else {
            me.$set(me, 'fileId', '')
            me.$set(me, 'fileUrl', '')
            me.$set(me, 'fileList', [])
            me.$set(me, 'isPdf', false)
            me.$set(me, 'isExcel', false)
            me.$set(me, 'fileShow', false)
            me.$set(me, 'fileContent', '')
          }
        }
      }
    },
    methods: {
      /**
       * 初始化数据
       */
      getData() {
        let me = this
        me.fileData.forEach(item => {
          me.fileList.push(item.sid)
        })
      },
      /**
       * 获取文件
       * @param base64
       * @param contentType
       * @param sliceSize
       */
      getBlob(base64, contentType, sliceSize) {
        contentType = contentType || ''
        sliceSize = sliceSize || 512

        let byteCharacters = atob(base64)
        let byteArrays = []

        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          let slice = byteCharacters.slice(offset, offset + sliceSize)

          let byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }

          let byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }

        return new Blob(byteArrays, {type: contentType})
      },
      /**
       * 预览第一个文件
       */
      getRecordPreview() {
        let me = this
        if (me.fileData.length > 0) {
          me.getPreviewAttach(me.fileData[0])
        }
      },
      /**
       * 预览文件
       */
      getPreviewAttach(file) {
        let me = this,
          fileType = '',
          fileName = file['originFileName']
        me.$set(me, 'isPdf', false)
        me.$set(me, 'isExcel', false)
        me.$set(me, 'fileShow', false)
        if (!isNullOrEmpty(file.sid) && !isNullOrEmpty(fileName)) {
          if (fileName.endsWith('pdf')) {
            fileType = 'application/pdf'
          } else if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
            fileType = 'application/vnd.ms-excel'
          }
          if (!isNullOrEmpty(fileType)) {
            me.$http.get(me.getPdfUrl + `/${file.sid}`).then(res => {
              if (res.status === 200) {
                me.$set(me, 'fileShow', true)
                if (fileName.endsWith('pdf')) {
                  me.$set(me, 'isPdf', true)
                } else if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
                  me.$set(me, 'isExcel', true)
                }
                const blob = me.getBlob(res.data, fileType)
                me.$set(me, 'fileId', file.sid)
                me.fileUrl = window.URL.createObjectURL(blob)
              }
            }, () => {
            })
          }
        }
      },
      excelToHtml() {
        let me = this
        me.$http.get(me.fileUrl, {
          responseType: 'blob'
        }).then(response => {
          let reader = new FileReader()
          reader.onload = e => {
            // 预处理
            let binary = '',
              buf = new Uint8Array(e.target.result),
              length = buf.byteLength
            for (let i = 0; i < length; i++) {
              binary += String.fromCharCode(buf[i])
            }
            const wb = XLSX.read(binary, {type: 'binary'})
            console.log('wb', wb)
            // 抓取第一个sheet
            let wsName = wb.SheetNames[0],
              ws = wb.Sheets[wsName]
            me.$set(me, 'fileContent', XLSX.utils.sheet_to_html(ws))
          }
          reader.readAsArrayBuffer(response.data)
        }).catch(err => {
          console.log(err)
        })
        return me.fileContent
      },
      downLoad(file) {
        let me = this,
          fileType = '',
          fileName = file['originFileName']
        if (!isNullOrEmpty(file.sid) && !isNullOrEmpty(fileName)) {
          if (fileName.endsWith('pdf')) {
            fileType = 'application/pdf'
          } else if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
            fileType = 'application/vnd.ms-excel'
          }
          me.$http.get(me.getPdfUrl + `/${file.sid}`).then(res => {
            if (res.status === 200) {
              const blob = me.getBlob(res.data, fileType)
              blobSaveFile(blob, fileName)
            }
          }, () => {
          })
        }
      },
      // onPrint() {
      //   let me = this
      //   if (!isNullOrEmpty(me.fileId)) {
      //     me.$http.get(me.getPdfUrl + `/${me.fileId}`).then(res => {
      //       if (res.status === 200) {
      //         let printWin = window.open('打印窗口', '_blank')
      //         printWin.document.write(res.data)
      //         printWin.document.close()
      //         printWin.print()
      //         printWin.close()
      //       }
      //     }, () => {
      //     })
      //   }
      // },
      /**
       * 关闭预览界面
       */
      handleClose() {
        this.$emit('update:show', false)
      }
    },
    destroyed() {
      let me = this
      window.URL.revokeObjectURL(me.fileUrl)
    }
  }
</script>

<style lang="less" scoped>
  .head {
    display: flex;
    justify-content: center;
  }
  .leftCard {
    flex: 2;
  }
  .rightCard {
    flex: 1;
  }
  ul {
    //display: flex;
    //flex-wrap: wrap;
  }
  ul li {
    list-style-type: none;
    margin-right: 10px;
  }
  .excelName {
    color: #bbbbbb;
    text-decoration: none;
    cursor: default;
  }

  /deep/ .excelDiv {
    width: 100%;
    overflow: auto;
    border: 2px solid #bdc3c7;
  }

  /deep/ .excelDiv table {
    border-spacing: 0;
    border: 1px solid #bdc3c7;
    border-collapse: collapse;
  }

  /deep/ .excelDiv table td {
    padding: 0 5px;
    white-space: nowrap;
    border: 1px solid #bdc3c7;
  }

  /deep/ .ivu-modal-body {
    top: 31px;
  }
</style>
