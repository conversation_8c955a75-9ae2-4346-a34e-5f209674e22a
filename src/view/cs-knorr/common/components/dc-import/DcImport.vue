<template>
  <XdoModal v-model="value" title="数据导入" :footer-hide="true" :closable="false" :mask-closable="false">
    <XdoUpload type="drag" :before-upload="uploadHook" :action="config.url" :data="updateData()" :headers="headers" :on-success="importSuccess" :on-error="importError">
      <div style="padding: 20px 0">
        <Icon type="ios-cloud-upload" size="24" style="color: #3399ff"></Icon>
        <p style="color: #ed4014">请在设置好导入参数后单击选择文件或者拖动文件到此处</p>
      </div>
    </XdoUpload>
    <div class="dc-import-content">
      <XdoForm class="dc-import-form" ref="form" :show-message="false" :model="importForm" :rules="rules" :label-width="120">
        <div class="dc-import-option dc-margin-bottom">
          <XdoFormItem label="导入起始行" required prop="startRow">
            <Input v-model="importForm.startRow" type="text" :maxlength="9" style="width:50px"></Input>
          </XdoFormItem>
          <XdoFormItem label="正确数据直接导入" style="margin-left:20px" prop="directImport">
            <i-switch v-model="importForm.directImport" style="margin-top: 6px"></i-switch>
          </XdoFormItem>
        </div>
        <XdoFormItem class="dc-import-operation" :label-width="40">
          <Button type="success" icon="ios-cloud-upload" @click="handleImportCorrectData" :disabled="importCorrectDisabled">{{correctTitle}}</Button>
          <Button type="error" icon="ios-cloud-download" style="margin-left:30px" @click="handleExportErrorData" :loading="exportLoading" :disabled="importErrorDisabled">{{errorTitle}}</Button>
        </XdoFormItem>
        <slot name="note"><div class="import-error-message">{{errorMessage}}</div></slot>
      </XdoForm>
      <div class="dc-import-button">
        <Button v-show="showTemplateDownload" type="info" icon="ios-cloud-download" :loading="downloadTplLoading"  class="dc-margin-left" @click="handleDownload">模板下载</Button>
        <Button type="info" icon="ios-close" style="margin-top: 20px" class="dc-margin-left" @click="handleClose">页面关闭</Button>
      </div>
    </div>
  </XdoModal>
</template>
<script>
import { excelExport } from '@/api'
export default {
  name: 'DcImport',
  componentName: 'DcImport',
  props: {
    value: { type: Boolean, default: false },
    bizParam: { type: Object },
    startRow: { type: Number, default: 5 },
    config: {
      type: Object,
      default: () => { return {
        tplName: '',
        url: '',
        tplUrl: '',
        errorUrl: '',
        correctUrl: ''
      }}
    }
  },
  data () {
    return {
      importForm: {
        startRow: this.startRow,
        headRow: 0,
        directImport: true,
        bizParam: ''
      },
      rules: {
        startRow: [{required: true, pattern: /^\d+$/, message: '导入起始行必须为整数', trigger: 'blur'}]
      },
      importResult: {
        correctSize: 0,
        errorSize: 0,
        correctKey: '',
        errorKey: ''
      },
      correctTitle: '导入正确数据',
      errorTitle: '导出错误数据',
      exportLoading: false,
      downloadTplLoading: false,
      errorMessage: ''
    }
  },
  mounted () {

  },
  methods: {
    handleDownload () {
      this.downloadTplLoading = true
      excelExport(`${this.config.tplUrl}?name=${this.config.tplName}`, 'GET')
      .finally(() => {
        this.downloadTplLoading = false
      })
    },
    handleClose () {
      this.$emit('input', false)
    },
    handleExportErrorData () {
      this.exportLoading = true
      excelExport(`${this.config.errorUrl}/${this.importResult.errorKey}`, {name: '错误数据'}).finally(() => {
        this.exportLoading = false
      })
    },
    handleImportCorrectData () {
      this.$http.post(`${this.config.correctUrl}/${this.importResult.correctKey}`).then(res => {
        this.$Message.success(`入库成功${res.data.data}条`)
        this.$set(this.importResult, 'correctKey', '')
        this.$emit('importSuccess', this.importResult)
      }, () => {})
    },
    uploadHook () {
      this.errorMessage = ''
      return true;
    },
    updateData () {
      if (this.bizParam) {
        this.importForm.bizParam = JSON.stringify(this.bizParam)
      }
      return this.importForm;
    },
    importSuccess (res) {
      if (res.success) {
        Object.assign(this.importResult, res.data)
        this.correctTitle = `导入正确数据 (${this.importResult.correctSize})`
        this.errorTitle = `导出错误数据 (${this.importResult.errorSize})`
      } else {
        this.errorMessage = res.message
      }
      this.$emit('importSuccess', this.importResult)
    },
    importError (error) {
      this.$Message.warning(error)
    }
  },
  computed: {
    importCorrectDisabled () {
      if (!this.config.correctUrl) {
        return true
      }

      if (this.importForm.directImport) {
        return true
      }

      if (!this.importResult.correctKey) {
        return true
      }

      return false;
    },
    importErrorDisabled () {
       if (!this.config.errorUrl) {
        return true
      }

      if (!this.importResult.errorKey) {
        return true
      }

      return false
    },
    headers () {
      return {
         Authorization: `Bearer ${this.$store.state.token}`
      }
    },
    showTemplateDownload () {
      return !!this.config.tplUrl
    }
  }
}
</script>
<style lang="css" scoped>
.dc-import-content {
  display: flex;
  justify-content: space-between;
}

.dc-import-option {
  display: flex;
  justify-content: flex-start;
}

.dc-import-operation {
  display: flex;
  justify-content: flex-start;
}

.dc-import-button {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.import-error-message {
  margin-top: 10px;
  color: #ff6600;
}

</style>
