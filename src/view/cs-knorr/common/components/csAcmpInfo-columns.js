
import { columnRender } from '@/view/cs-knorr/common/comm'

import { getKeyValue, isNullOrEmpty } from '@/libs/util'

export default {
  mixins:[columnRender],
  data() {
    return {
      acmpColumns: {
        editColumns: [{
          title: '文件名',
          ellipsis: true,
          tooltip: true,
          align: 'left',
          render: (h, { row }) => {
            return h('a', {
              on: {
                click: () => {
                  this.downloadFile(row.sid)
                }
              }
            }, row.originFileName)
          }
        },
          {
            width: 250,
            title: '单证类型',
            align: 'center',
            key: 'acmpType',
            render: (h, params) => {
              return this.cmbShowRender(h, params, this.comboxData_acmpType)
            }
          },
          {
            width: 120,
            title: '文件扩展名',
            align: 'center',
            render: (h, { row }) => {
              return h('span', {
                on: {
                  // click: () => {
                  //   this.downloadFile(row.sid)
                  // }
                }
              }, row.originFileName.substring(row.originFileName.lastIndexOf('.') + 1))
            }
          },

          // {
          //   width: 200,
          //   title: '说明',
          //   align: 'center',
          //   key: 'note'
          // },
          {
          width: 110,
          title: '上传时间',
          align: 'center',
          key: 'insertTime'
        }, {
          width: 80,
          title: '操作',
          key: 'action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.removeAttach(params)
                  }
                }
              }, '删除')
            ])
          }
        }],
        viewColumns: [{
          title: '文件名',
          ellipsis: true,
          tooltip: true,
          align: 'left',
          render: (h, { row }) => {
            return h('a', {
              on: {
                click: () => {
                  this.downloadFile(row.sid)
                }
              }
            }, row.originFileName)
          }
        },
          {
            width: 250,
            title: '单证类型',
            align: 'center',
            key: 'acmpType',
            render: (h, params) => {
              return this.cmbShowRender(h, params, this.comboxData_acmpType)
            }
          },
          {
            width: 120,
            title: '文件扩展名',
            align: 'center',
            render: (h, { row }) => {
              return h('span', {
                on: {
                  // click: () => {
                  //   this.downloadFile(row.sid)
                  // }
                  //fileName.substring(fileName.lastIndexOf('.') + 1)
                }
              }, row.originFileName.substring(row.originFileName.lastIndexOf('.') + 1))
            }
          },


          // {
          //   width: 200,
          //   title: '说明',
          //   align: 'center',
          //   key: 'note'
          // },

          {
            width: 110,
            title: '上传时间',
            align: 'center',
            key: 'insertTime'
          }]
      },

    }
  },
  methods:{

    cmbShowRender(h, params, cmbSource, pCodeKey, toolTip, toUpperCase) {
      let cmbVal = params.row[params.column.key]
      if (isNullOrEmpty(cmbVal)) {
        cmbVal = ''
      } else if (!isNullOrEmpty(pCodeKey)) {
        if (toUpperCase === true) {
          cmbVal = getKeyValue(this.pcodeGet(pCodeKey, cmbVal.toUpperCase()), cmbVal.toUpperCase(),false)
        } else {
          cmbVal = getKeyValue(this.pcodeGet(pCodeKey, cmbVal), cmbVal,false)
        }
      } else {
        cmbVal = getKeyValue(cmbSource, cmbVal,false)
      }
      if (params.column.tooltip === true) {
        return this.toolTipRender(h, cmbVal, toolTip)
      } else {
        return h('span', cmbVal)
      }
    },
  }

}
