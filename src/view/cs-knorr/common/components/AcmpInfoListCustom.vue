<template>
  <section class="xdo-enter-root" style="padding-bottom: 3px;">
    <p style="font-weight: bold; padding: 7px 22px 3px 22px; border-bottom: #dcdee2 solid 1px; margin-bottom: 0;">
      {{title}}
    </p>
    <div v-show="showAction" style="padding: 0; margin: 0;" class="xdo-enter-root" v-focus>
      <XdoForm ref="attachForm" :show-message="false" :model="attachForm">
        <table class="frmTable">
          <tr>
            <td style="padding-left: 6px">
              <div style="display:flex;justify-self: left">
                <div style="width: 100px; text-align: right ">单证类型:</div>
                <div style="width: 100%">
                  <!--                  <XdoIInput type="text" v-model="editForm.acmpType" ></XdoIInput>-->

                  <XdoFormItem>
                    <xdo-select v-model="editForm.acmpType"
                                :options="comboxData_acmpType"
                                style="width: 100%"

                    ></xdo-select>
                  </XdoFormItem>
                </div>
              </div>
            </td>
            <td>
              <XdoIInput type="text" disabled :value="fileName"></XdoIInput>
              <input ref="file" v-if="showFileInput" @change="fileChanged" type="file" style="display: none;"/>
            </td>
            <td style="width: 40px;">
              <Button @click="handleSelectFile">浏览</Button>
            </td>
            <td style="width: 40px;">
              <XdoButton type="warning" @click="handleSaveAttach">上传</XdoButton>
            </td>
          </tr>
          <!--          <tr>-->
          <!--            <td colspan="4">-->
          <!--              <div style="display:flex;justify-self: left">-->
          <!--                <div style="width: 100px; text-align: right ">说明:</div>-->
          <!--&lt;!&ndash;                <div style="width: 100%">&ndash;&gt;-->
          <!--&lt;!&ndash;                  <XdoIInput type="text" v-model="editForm.note" style="width: 100%"></XdoIInput>&ndash;&gt;-->
          <!--&lt;!&ndash;                </div>&ndash;&gt;-->
          <!--              </div>-->
          <!--            </td>-->
          <!--          </tr>-->

        </table>

      </XdoForm>
    </div>
    <XdoTable :height="tableHeight" size="small" stripe :columns="grdColumns" :data="dataAcmpSource" border
              style="margin-top: 2px;"></XdoTable>
    <dcFilePreviewPop :show.sync="filePreview.show" :file-data="filePreview.fileData"></dcFilePreviewPop>
  </section>
</template>

<script>
  import { csApiKnorr } from '@/view/cs-knorr/common/comm'
  import * as  util from '@/libs/util'
  import acmpColumns from './csAcmpInfo-columns'
  import dcFilePreviewPop from './dc-file-preview-pop'


  export default {
    name: 'AcmpInfoListCustom',
    components: {
      dcFilePreviewPop
    },
    mixins: [acmpColumns],
    props: {
      sid: {
        type: String,
        require: true
      },
      title: {
        type: String,
        default: '商品资料'
      },
      businessType: {
        type: String,
        default: () => ('2')
      },
      showAction: {
        type: Boolean,
        require: true
      },
      justView: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: '-'
      }
    },
    data() {
      return {
        comboxData_acmpType: [],
        fileName: '',
        attachForm: {},
        selectedFile: null,
        editForm: {
          businessType: this.businessType,  // 单证类型：必填，字符型，长度1位（2-手册、3-报核、4－清单、5－质疑
          acmpNo: '',
          acmpFormat: '',
          acmpType: '',
          billSerialNo: '',
          businessSid: '',
          note: ''
        },
        dataAcmpSource: [],
        grdColumns: [],
        showFileInput: true,
        filePreview: {
          show: false,
          fileData: []
        }
      }
    },
    watch: {
      sid: function(val) {
        this.editForm.businessSid = val
        this.loadData()
      },
      justView: {
        immediate: true,
        handler: function(justView) {
          if (justView === true) {
            this.$set(this, 'grdColumns', this.acmpColumns.viewColumns)
          } else {
            this.$set(this, 'grdColumns', this.acmpColumns.editColumns)
          }
        }
      }
    },
    mounted() {
      if (this.sid) {
        this.editForm.businessSid = this.sid
        this.loadData()
      }

      this.handleGetAcmpType()
    },
    methods: {
      /**
       * 获取文件类型
       */
      handleGetAcmpType() {
        this.$http.post(csApiKnorr.comomApiInfo.attachedInfo.getAcmpType).then(res => {
          this.comboxData_acmpType = JSON.parse(res.data.data)
        }).catch(() => {
        }).finally(() => {
        })
      },


      loadData() {
        let me = this
        me.$set(me, 'showFileInput', false)
        me.$http.post(csApiKnorr.comomApiInfo.attachedInfo.list + '/' + me.sid).then(res => {
          me.dataAcmpSource = res.data.data
        }).catch(() => {
        }).finally(() => {
          me.$set(me, 'showFileInput', true)
        })
      }
      ,
      fileChanged(e) {
        let me = this
        if (e.target.files[0] === undefined)
          return
        me.selectedFile = e.target.files[0]
        if (me.selectedFile) {
          me.fileName = me.selectedFile.name
        } else {
          me.fileName = ''
        }
      }
      ,
      handleSelectFile() {
        if (this.$refs['file']) {
          const event = new MouseEvent('click')
          this.$refs['file'].dispatchEvent(event)
        }
      }
      ,
      handleSaveAttach() {
        let me = this
        if (!me.selectedFile) {
          me.$Message.error('请选择上传的文件')
          return
        }
        const fd = new FormData()
        fd.append('file', me.selectedFile)
        for (let val in me.editForm) {
          if (me.editForm.hasOwnProperty(val)) {
            fd.append(val, me.editForm[val])
          }
        }
        me.$http.post(csApiKnorr.comomApiInfo.attachedInfo.insertAll, fd, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }).then(() => {
          me.loadData()
          me.$Message.success('上传成功')
          me.$refs['attachForm'].resetFields()
          me.fileName = ''
          me.selectedFile = null
        }).catch(() => {
        })
      }
      ,
      removeAttach(params) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此文件吗?',
          onOk: () => {
            const sids = params.row.sid
            me.$http.delete(`${csApiKnorr.comomApiInfo.attachedInfo.delete}/${sids}`).then(() => {
              me.$Message.success('删除成功！')
              me.loadData()
              me.$refs['attachForm'].resetFields()
              me.fileName = ''
              me.selectedFile = null
            }).catch(() => {
            })
          }
        })
      }
      ,
      downloadFile(sysId) {
        let me = this
        me.$http.get(`${csApiKnorr.comomApiInfo.attachedInfo.get}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          // 下载
          const name = util.getHttpHeaderFileName(res.headers)
          // const blob = new Blob([res.data], {type: 'application/octet-stream'})
          // util.blobSaveFile(blob, name)

          // 预览
          me.$set(me.filePreview, 'show', true)
          me.$set(me.filePreview, 'fileData', [{
            sid: sysId,
            originFileName: name
          }])
        })
      }
    },
    computed: {
      tableHeight() {
        if (typeof this.height === 'number') {
          if (this.showAction) {
            return this.height - 28
          } else {
            return this.height
          }
        }
        return '-'
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-table-tip {
    min-height: 100px;
  }

  .frmTable {
    display: table;
    border-collapse: separate;
    border-spacing: 1px;
    border-color: grey;
    margin: 0;
    padding: 1px 0;
    width: 100%;
    line-height: 25px;
  }

  /*.frmTable tr, .frmTable td {*/
  /*  margin: 0;*/
  /*  padding: 0;*/
  /*  width: 100%;*/
  /*}*/
</style>
