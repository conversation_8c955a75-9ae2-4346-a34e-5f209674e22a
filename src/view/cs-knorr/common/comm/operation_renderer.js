const defaultOperation = [
  {title: '编辑', handle: 'handleEditByRow'},
  {title: '查看', handle: 'handleViewByRow', marginRight: '0'}
]

const createChildComponent = (h, item, vm, params) => {
  return h('a', {
    style: {
      marginRight: item.marginRight ? item.marginRight : '15px'
    },
    on: {
      click: () => {
        if (vm && vm.hasOwnProperty(item.handle)) {
          vm[item.handle](params.data)
        }
      },
    },
  }, item.title)
}

export const operationRenderer = (vm, meta = defaultOperation) => {
  return majesty.Vue.extend({
    render(h) {
      const children = meta.map(it => createChildComponent(h, it, vm, this.params))
      return h('div', children)
    }
  })
}

//lp 2020年2月13日
const createChildComponentOwner = (h, item, vm, params) => {
  return h('a', {
    style: {
      marginRight: item.marginRight ? item.marginRight : '15px'
    },
    on: {
      click: () => {
        if (vm && vm.hasOwnProperty(item.handle)) {
          vm[item.handle](params,item.param)
        }
      },
    },
  }, item.title==''?params.value:item.title)  //lp 2020年2月13日
}

export const operationRendererOwner = (vm, meta = defaultOperation) => {
  return majesty.Vue.extend({
    render(h) {
      const children = meta.map(it => createChildComponentOwner(h, it, vm, this.params))  //lp 2020年2月13日
      return h('div', children)
    }
  })
}
