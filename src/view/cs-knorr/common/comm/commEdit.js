import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from './constant'

export const commEdit = {
  props: {
    editConfig: {
      type: Object,
      default: () => ({
        editData: {},
        editStatus: editStatus.SHOW
      })
    }
  },
  data () {
    let theData = this.getDefaultData()
    return {
      frmData: {
        ...theData
      },
      buttons: [],
      ajaxUrl: {
        insert: '',
        update: ''
      },
      formName: '',
      successMsg: {
        insert: '新增成功',
        update: '修改成功'
      }
    }
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val.editStatus === editStatus.ADD) {
          this.$set(this, 'frmData', this.getDefaultData())
        } else {
          let theData = Object.assign(this.getDefaultData(), (val.editData || {}))
          if (typeof this.loadBackendData === 'function') {
            this.loadBackendData(val)
          }else {

            this.$set(this, 'frmData', theData)
          }
        }
        if (!isNullOrEmpty(this.editConfig.headId)) {
          this.frmData.headId = this.editConfig.headId
        }
      }
    }
  },
  methods: {
    /**
     * 获取默认值
     * @returns {{}}
     */
    getDefaultData () {
      return {}
    },
    /**
     * 设置保存按钮加载样式(可外部覆盖)
     * @param loading
     */
    setBtnSaveLoading (loading) {
      console.info('当前加载样式为:' + loading)
    },
    /**
     * 执行保存操作(包括新增、修改)
     * @param callback
     */
    doSave (callback) {
      let me = this
      if (isNullOrEmpty(me.formName)) {
        console.error('未设置form的名称：this.formName')
        return
      }
      const data = Object.assign({}, me.frmData)
      me.$refs[me.formName].validate().then(isValid => {
        if (isValid) {
          me.doSaveByFormData(data, callback)
        }
      })
    },
    /**
     * 添加扩展参数
     */
    getExtendQueryString(){
      return ''
    },
    /**
     * 根据需提交的数据及回调函数执行保存操作
     * @param formData
     * @param callback
     */
    doSaveByFormData (formData, callback) {
      let me = this
      if (typeof formData === "object" && Object.keys(formData).length > 0) {
        if (me.editConfig.editStatus === editStatus.ADD) {
          if (!isNullOrEmpty(me.ajaxUrl.insert)) {
            me.setBtnSaveLoading(true)
            let extendQueryString = me.getExtendQueryString()
            me.$http.post(`${me.ajaxUrl.insert}${extendQueryString}`, formData).then(res => {
              if (typeof callback === 'function') {
                callback.call(me, res)
              } else {
                me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
              }
              me.$Message.success(me.successMsg.insert + '!')
            }).catch(() => {
            }).finally(() => {
              me.setBtnSaveLoading(false)
            })
          } else {
            console.error('未设置新增接口: this.ajaxUrl.insert')
          }
        } else if (me.editConfig.editStatus === editStatus.EDIT) {
          if (!isNullOrEmpty(me.ajaxUrl.update)) {
            me.setBtnSaveLoading(true)
            let extendQueryString = me.getExtendQueryString()
            me.$http.put(`${me.ajaxUrl.update}/${formData.sid}${extendQueryString}`, formData).then(res => {
              if (typeof callback === 'function') {
                callback.call(me, res)
              } else {
                me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
              }
              me.$Message.success(me.successMsg.update + '!')
            }).catch(() => {
            }).finally(() => {
              me.setBtnSaveLoading(false)
            })
          } else {
            console.error('未设置修改接口: this.ajaxUrl.update')
          }
        }
      } else {
        console.error('未设置有效的form数据: formData')
      }
    },
    /**
     * 调用列表界面方法并将当前编辑界面信息传给列表界面
     * @param showList
     * @param editStatus
     * @param data
     */
    refreshIncomingData (showList, editStatus, data) {
      this.$emit('onEditBack', {
        showList: showList,
        editStatus: editStatus,
        editData: data
      })
    },
    /**
     * 返回主界面
     */
    handleBack () {
      this.refreshIncomingData(true, editStatus.SHOW, this.getDefaultData())
    }
  },
  computed: {
    /**
     * 输入组件是否可输
     * @returns {boolean}
     */
    showDisable () {
      return !(this.editConfig.editStatus === editStatus.ADD || this.editConfig.editStatus === editStatus.EDIT)
    }
  }
}
