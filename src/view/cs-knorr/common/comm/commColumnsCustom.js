import pms from '@/libs/pms'
import { commList } from './commList'
import { operationRenderer } from './operation_renderer'


// 自定义列导出功能通用JS
export const commColumnsCustom = {
  mixins: [commList, pms],
  data() {
    return {
      tableId: '',
      tableShow: true,
      showtableColumnSetup: false,
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      toolbarEventMap: {
        'setting': this.handleTableColumnSetup
      },
      checkboxSelection: true,
      isShowSetting: true, //是否显示自定义配置列 按钮
      isAutoSizeAllColumns: false,//查询完成后事件 根据列的内容 调整列宽 必须设置 ref 值  ref_agGrid
      p_group: 'default',
      isShowOp: true  //在没有编辑权限的时候  是否显示查看按钮 =操作列

    }
  },
  mounted: function() {
    this.loadFunctions(this.p_group).then(() => {
      if (typeof this.loadFunctionsAfterOne === 'function') {
        this.loadFunctionsAfterOne()
      }

      this.setShowFields(this.totalColumns)
      this.actionLoad()
    })

  },
  methods: {
    actionLoad() {
      if (this.isShowSetting) {
        this.actions.push({
          ...this.actionsComm,
          icon: 'ios-cog',
          label: '自定义配置',
          command: 'setting',
          key: 'xdo-btn-setting'
        })
      }
      if (typeof this.loadOtherActions === 'function') {
        this.loadOtherActions()
      }
    },
    /**
     * 设置列表显示字段
     * @param totalColumns
     * @param defaultColumns
     */
    setShowFields(totalColumns, defaultColumns) {

      let me = this
      me.tableId = me.$route.path + '/' + me.$options.name
      let columns = []
      if (Array.isArray(defaultColumns) && defaultColumns.length > 0) {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns, defaultColumns)
      } else {
        columns = me.$bom3.showTableColumns(me.tableId, totalColumns)
      }


      me.handleUpdateColumn(columns)
    },
    /**
     * 弹出列表设置窗口
     */
    handleTableColumnSetup() {
      this.showtableColumnSetup = true
    },
    /**
     * 保存列表设置
     * @param columns
     */
    handleUpdateColumn(columns) {
      let edit = this.actions.filter(item => {
        return item.command === 'edit'
      })
      //判断是否存在修改按钮  若存在 就增加 查看修改列
      let bascol = []
      if (Array.isArray(edit) && edit.length > 0) {
        bascol = this.getDefaultColumnsC()
      } else {
        bascol = this.isShowOp ? [
          {
            title: '操作',
            fixed: 'left',
            width: 90,
            align: 'center',
            key: 'operation',
            cellRendererFramework: operationRenderer(this, [{
              title: '查看',
              handle: 'handleViewByRow',
              marginRight: '0'
            }])
          }
        ] : []

      }
      this.gridConfig.gridColumns = []
      this.$nextTick(function() {
        this.gridConfig.gridColumns = [...bascol, ...columns]
      })

      this.gridConfig.exportColumns = columns.map(col => {
        return {
          key: col.key,
          value: col.title
        }
      })
    },
    /**
     * 获取操作列
     *
     * @returns {{cellRendererFramework: *, width: number, fixed: string, title: string, align: string, key: string}[]}
     */
    getDefaultColumnsC() {
      return [
        {
          title: '操作',
          fixed: 'left',
          width: 120,
          align: 'center',
          key: 'operation',
          cellRendererFramework: operationRenderer(this)
        }
      ]
    },

    /**
     * AG 选中行变化事件
     * @param params
     */
    handleSelectionChanged(params) {
      this.handleSelectionChange(params.api.getSelectedRows())
    },
    /**
     * 查询完成后事件 根据列的内容 调整列宽
     */
    afterSearch() {
      // if (this.isAutoSizeAllColumns) {
      //   // console.log(this.gridConfig.gridColumns)
      //   if (this.$refs.ref_agGrid){ //必须在grid属性上配置
      //     let a =Object.assign([],this.gridConfig.gridColumns)
      //     //console.log(this.$refs.ref_agGrid.gridOptions.columnApi)
      //     this.$refs.ref_agGrid.gridOptions.columnApi.autoSizeColumns(a.map(item=>item.key).filter(x=>x!='checkboxSelection'&& x!='operation'),false)
      //   }
      // }
    },
    /**
     *查询 enter 键 出发查询事件
     * @param e
     */
    handleSearchSubmitForm(e) {
      if (e.key === 'Enter') {
        this.handleSearchSubmit()
      }
    }

  }
}
