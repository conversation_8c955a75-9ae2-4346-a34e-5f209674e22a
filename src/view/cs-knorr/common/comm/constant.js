export const editStatus = {
  // 新增
  ADD: `add`,
  // 修改
  EDIT: `edit`,
  // 只读
  SHOW: `show`
}

export const pageParam = {
  page: 1,
  limit: 10,
  sort: ''
}

/**
 * 进口管理
 */
export const interfaceImportManagementConst = {
  /**
   * 预报单录入
   */
  preDecErpHead: {

    //受托标记
    entrustStatus: [
      { value: '0', label: '未委托' },
      { value: '1', label: '已委托' }
    ],
    //单据状态
    status: [
      { value: '-1', label: '退回' },
      { value: '0', label: '暂存' },
      { value: '1', label: '待分单' },
      { value: '2', label: '已分单' },
      { value: '3', label: '预报完结' }
    ],
    //集装箱型号
    containerModel: [
      { value: '11', label: '普通2*标准箱(L)' },
      { value: '12', label: '冷藏2*标准箱(L)' },
      { value: '13', label: '罐式2*标准箱(L)' },
      { value: '21', label: '普通标准箱(S)' },
      { value: '22', label: '冷藏标准箱(S)' },
      { value: '23', label: '罐式标准箱(S)' },
      { value: '31', label: '其他标准箱(S)' },
      { value: '32', label: '其他2*标准箱(L)' }
    ],
    //装箱/拼箱  0整箱；拼箱
    containerLclHead: [
      { value: '0', label: '整箱' },
      { value: '1', label: '拼箱' }
    ],
    // 拼箱标识  0：否；1：是
    containerLclBody: [
      { value: '0', label: '否' },
      { value: '1', label: '是' }

    ],
    acmpType: [
      { value: '1', label: '提单' },
      { value: '2', label: 'charge list' },
      { value: '3', label: '报关单' },
      { value: '4', label: '税单' },
      { value: '5', label: '商检证书' },
      { value: '6', label: '出口委托书' },
      { value: '7', label: '到货通知' },
      { value: '8', label: '申报资料' },
      { value: '9', label: '归类证书' },
      { value: '10', label: '商业发票' },
      { value: '11', label: '商业装箱单' },
      { value: '12', label: '形式发票装箱单' },
      { value: '13', label: '出入库凭证' },
      { value: '14', label: '其它单证' },
      { value: '15', label: '图片' },
      { value: '16', label: '图纸' }
    ]
  }

}

/**
 *
 * @type {{selectAllPaged: string}}
 */
export const customDec = {
  /**
   * 报关单比对
   */
  showDiff: {
    feeTypeMap: [{value: '1', label: '率'}, {value: '2', label: '单价'}, {value: '3', label: '总价'}],
    /**
     * 报完税标记
     */
    bondMark: [{
      value: '0',
      label: '保税'
    }, {
      value: '1',
      label: '非保税'
    }],
    result: [{
      value: '-1',
      label: '第三方数据缺失'
    }, {
      value: '0',
      label: '结果一致'
    }, {
      value: '1',
      label: '结果不同'
    }],
    dataSource:[{
      value: '0',
      label: '报关行'
    }, {
      value: '1',
      label: '导入'
    }, {
      value: '2',
      label: 'OCR识别'
    }],
    /**
     *报关单类型
     */
    entryType: [
      {value: '1', label: '进口报关单'},
      {value: '2', label: '出口报关单'},
      {value: '3', label: '进境备案清单'},
      {value: '4', label: '出境备案清单'},
      {value: '5', label: '进境两单一审备案清单'},
      {value: '6', label: '出境两单一审备案清单'},
      {value: 'B', label: '转关提前进境备案清单'},
      {value: 'C', label: '转关提前出境备案清单'},
      {value: 'e', label: '进口两步申报一次录入报关单'},
      {value: 'F', label: '出口二次转关'},
      {value: 'G', label: '进口提前/工厂验放报关单'},
      {value: 'H', label: '出口提前/工厂验放报关单'},
      {value: 'I', label: '进口提前/暂时进口报关单'},
      {value: 'J', label: '出口提前/暂时出口报关单'},
      {value: 'K', label: '进口提前/中欧班列报关单'},
      {value: 'L', label: '出口提前/中欧班列报关单'},
      {value: 'M', label: '出口提前/市场采购报关单'},
      {value: 'N', label: '出口提前/空运联程报关单'},
      {value: 'O', label: '进口提前/工厂验放备案清单'},
      {value: 'P', label: '出口提前/工厂验放备案清单'},
      {value: 'Q', label: '进口提前/暂时进口备案清单'},
      {value: 'X', label: '进口两步申报报关单'},
      {value: 'Y', label: '进口两步申报备案清单'},
    ],



    //审批状态 (0 待审批, 1 审批通过, -1 审批拒绝)
    approvalStatus:[
      {value:'0',label:'待审批'},
      {value:'1',label:'审批通过'},
      {value:'-1',label:'审批退回'}
    ],
    // 发送状态
    sendStatus:[
      {value:'0',label:'待发送'},
      {value:'1',label:'发送成功'},
      {value:'-1',label:'发送失败'}
    ],
    // 发送状态
    isUpload:[
      {value:'0',label:'未上传'},
      {value:'1',label:'已上传'},
    ]




  }


}






