//克罗尔定制
const baseUriKn = '/gwstd/api'

const baseUri = '/gwstd/api'
/**
 * 进口管理
 * @type {{selectAllPaged: string}}
 */
export const interfaceImportManagement = {
  /**
   * 预报单录入
   */
  preDecErpHead: {
    selectAllPaged: `${baseUriKn}/v1/preDecErpHead/list`, //分页查询接口
    get: `${baseUriKn}/v1/preDecErpHead`,
    insert: `${baseUriKn}/v1/preDecErpHead`,
    update: `${baseUriKn}/v1/preDecErpHead`,
    delete: `${baseUriKn}/v1/preDecErpHead`,
    copy: `${baseUriKn}/v1/preDecErpHead/copy`,
    takeYuBaoDan: `${baseUriKn}/v1/preDecErpHead/submit`,
    inforFill: `${baseUriKn}/v1/preDecErpHead/supplement`,
    inforFillImport: `${baseUriKn}/v1/preDecErpHead/import/supplement`,
    exportSupplement: `${baseUriKn}/v1/preDecErpHead/export/supplement`,
    approveGet: `${baseUriKn}/v1/preDecErpHead/approve`, //审批情况

    selectAllPagedFenDan: `${baseUriKn}/v1/preDecErpHead/gwlist`, //分页查询接口   分单单独
    send: `${baseUriKn}/v1/preDecErpHead/distribute`, //分单
    return: `${baseUriKn}/v1/preDecErpHead/back`, //退回
    exportUrl: `${baseUriKn}/v1/preDecErpHead/exportList`,

    exportUrl2: `${baseUriKn}/v1/preDecErpHead/gwExportList`,    // 预报分单导出
  }
}

/*
 * 关务标准版接口 报关单比对 v1/entry/diff
 */
export  const  showDiff = {
  //进口
  selectAllPaged: `${baseUri}/v1/decIEntry/diff/list`, //分页查询接口
  diff: `${baseUri}/v1/decIEntry/diff`,   //sid  //比对

  //出口
  selectAllPagedE: `${baseUri}/v1/decEEntry/diff/list`, //分页查询接口
  diffE: `${baseUri}/v1/decEEntry/diff`,   //sid  //比对

///v1/entryThird/E/approval

  // 导入草单数据
  import:`${baseUri}/v1/entryThird/import`,
  //进口审核
  iAudit:`${baseUri}/v1/entryThird/I/approval`,
  //出口审核
  eAudit:`${baseUri}/v1/entryThird/E/approval`,  // selectAllPaged: `${baseUri}/v1/entry/diff`, //详情
  getZtythEmsListNo: `${baseUri}/v1/ztythEmsImgexg/getZtythEmsNo`,//
  getZtythCopEmsNo: `${baseUri}/v1/ztythEmsImgexg/getZtythCopEmsNo`,//

  PRD: `${baseUri}/v1/biClientInformation/selectComboxByCode/PRD`,   // 供应商
  CLI: `${baseUri}/v1/biClientInformation/selectComboxByCode/CLI`,   // 客户
  CUT: `${baseUri}/v1/biClientInformation/selectComboxByCode/CUT`,   // 报关行
  getParamValues: `${baseUri}/v1/biCustomerParams/getParamValues`
}

/*
 * 关务标准版接口
 */
export const comomApiInfo = {
  /**
   * 获取 货代 报关行
   */
  baseCompany: {
    //下拉数据源     /FOD,CUT
    selectComboxByCode: `${baseUri}/v1/biClientInformation/selectComboxByCode`
  },
  /**
   * 随附单据
   */
  attachedInfo: {
    insert: `${baseUri}/v1/attached`,
    insertAll: `${baseUri}/v1/attached/insertAll`,
    // list: `${baseUri}/v1/attached/list`,   ///business/{sid}
    list: `${baseUri}/v1/attached/business`,   ///business/{sid}
    delete: `${baseUri}/v1/attached`,
    get: `${baseUri}/v1/attached`,
    getPreAcmpInfo: `${baseUri}/v1/attached/getPreAcmpInfo`,
    getPdf: `${baseUri}/v1/decErpEHeadN/getAttachFile`,

    getAcmpType:`${baseUri}/v1/attached/selectedList`,
    business: `${baseUri}/v1/attached/business`,
  }
}


