import { editStatus } from './constant'
import { columnRender } from './columnRender'
import { isNumber, getKeyValue, isNullOrEmpty} from '@/libs/util'

// 導出基礎列表字段
const baseColumnsExport = ['sid']
// 僅可單行操作列表基礎字段
const baseColumnsSingleOperate = ['operation', 'sid']
// 支持多行選擇列表基礎字段
const baseColumnsMultiSelection = ['selection', 'sid']
// 支持多選及單行操作列表基礎字段
const baseColumnsShow = ['selection', 'operation', 'sid']

/**
 * 通用列信息
 * @type {{}}
 */
const baseColumns = {
  mixins: [ columnRender ],
  methods: {
    /**
     * 获取默认列【勾选列及操作列】
     * @returns {*[]}
     */
    getDefaultColumns () {
      return [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, {
        width: 88,
        title: '操作',
        fixed: 'left',
        align: 'center',
        key: 'operation',
        render: (h, params) => {
          return h('div', [
            h('a', {
              props: {
                size: 'small',
                type: 'primary'
              },
              style: {
                display: this.operationEditShow()
              },
              on: {
                click: () => {
                  this.handleEditByRow(params.row)
                }
              }
            }, '编辑'),
            h('a', {
              props: {
                type: 'primary'
              },
              style: {
                marginLeft: (this.operationEditShow() === '' ? '15px' : '0')
              },
              on: {
                click: () => {
                  this.handleViewByRow(params.row)
                }
              }
            }, '查看')
          ])
        }
      }]
    }
  }
}

/**
 * 通用行编辑列
 * @type {{}}
 */
const baseEditColumns = {
  mixins: [ columnRender ],
  data () {
    return {
      rowSaveLoading: false,
      successMsg: {
        insert: '新增成功',
        update: '修改成功'
      }
    }
  },
  methods: {
    /**
     * 获取默认列【勾选列及操作列】
     * @returns {*[]}
     */
    getDefaultColumns() {
      let me = this
      return [{
        width: 36,
        // fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, {
        width: 100,
        title: '操作',
        // fixed: 'left',
        align: 'center',
        key: 'operation',
        render: (h, {row, index}) => {
          if (row.$rowStatus === editStatus.ADD || row.$rowStatus === editStatus.EDIT) {
            return [
              h('Button', {
                props: {
                  size: 'small',
                  type: 'success',
                  loading: this.rowSaveLoading
                },
                on: {
                  click: () => {
                    me.saveEditRow(row, index)
                  }
                }
              }, '保存'),
              h('Button', {
                props: {
                  size: 'small',
                  type: 'error'
                },
                style: {
                  marginLeft: '6px'
                },
                on: {
                  click: () => {
                    me.abandonRowEdit(row, index)
                  }
                }
              }, '取消')
            ]
          } else {
            return h('Button', {
              props: {
                size: 'small'
              },
              style: {
                display: this.operationEditShow()
              },
              on: {
                click: () => {
                  me.setRowEdit(index)
                }
              }
            }, '编辑')
          }
        }
      }]
    },
    /**
     * 新增一行数据
     * @param defaultData
     */
    addEmptyRow(defaultData) {
      let me = this
      let theDefaultData = Object.assign({
        sid: '',
        $originData: {},
        $rowStatus: editStatus.ADD
      }, defaultData || {})
      let newDatas = []
      newDatas.push(theDefaultData)
      for (let row of me.gridConfig.data) {
        newDatas.push(JSON.parse(JSON.stringify(row)))
      }
      me.$set(me.pageParam, 'dataTotal', me.pageParam.dataTotal + 1)
      me.$set(me.gridConfig, 'data', newDatas)
    },
    /**
     * 设置行可编辑
     * @param index
     */
    setRowEdit(index) {
      if (index > -1 && index < this.gridConfig.data.length) {
        let originData = JSON.parse(JSON.stringify(this.gridConfig.data[index]))
        delete originData.$rowStatus
        delete originData.$originData
        this.$set(this.gridConfig.data[index], '$originData', originData)
        this.$set(this.gridConfig.data[index], '$rowStatus', editStatus.EDIT)
        this.gridConfig.selectRows = []
      }
    },
    /**
     * 行编辑保存后执行
     * @param me
     */
    baseAfterRowSave(me) {
      if (typeof me.afterRowSave === 'function') {
        me.afterRowSave.call(me)
      }
    },
    /**
     * 数据保存
     * @param row
     * @param index
     */
    saveEditRow(row, index) {
      let me = this
      let submitRow = JSON.parse(JSON.stringify(row))
      delete submitRow.$rowStatus
      delete submitRow.$originData
      if (row.$rowStatus === editStatus.ADD) {
        if (isNullOrEmpty(me.ajaxUrl.insert)) {
          console.error('未设置新增接口: this.ajaxUrl.insert')
          me.$Message.warning('未设置新增接口: this.ajaxUrl.insert!')
        } else {
          me.$set(me, 'rowSaveLoading', true)
          me.$http.post(me.ajaxUrl.insert, submitRow).then(res => {
            me.$set(me.gridConfig.data[index], 'sid', res.data.data.sid)
            me.$set(me.gridConfig.data[index], '$rowStatus', editStatus.SHOW)
            me.$set(me.gridConfig.data[index], '$originData', {})
            me.baseAfterRowSave(me)
            me.$Message.success(me.successMsg.insert + '!')
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'rowSaveLoading', false)
            try {
              let fn = eval(this.handleSearchSubmit)
              if(typeof(fn) === 'function'){
                fn()
              }
            }catch(e){
              console.error(e)
            }
          })
        }
      } else if (row.$rowStatus === editStatus.EDIT) {
        if (isNullOrEmpty(me.ajaxUrl.update)) {
          console.error('未设置更新接口: this.ajaxUrl.update')
          me.$Message.warning('未设置更新接口: this.ajaxUrl.update!')
        } else {
          me.$set(me, 'rowSaveLoading', true)
          me.$http.put(`${me.ajaxUrl.update}/${row.sid}`, submitRow).then(() => {
            me.$set(me.gridConfig.data[index], '$rowStatus', editStatus.SHOW)
            me.$set(me.gridConfig.data[index], '$originData', {})
            me.baseAfterRowSave(me)
            me.$Message.success(me.successMsg.update + '!')
          }).catch(() => {
          }).finally(() => {
            me.$set(me, 'rowSaveLoading', false)
          })
        }
      } else {
        console.error('需保存的编辑信息有误!')
        this.$Message.warning('需保存的编辑信息有误!')
      }
    },
    /**
     * 放弃修改/新增
     * @param row
     * @param index
     */
    abandonRowEdit(row, index) {
      let me = this
      if (row.$rowStatus === editStatus.ADD) {
        me.gridConfig.data.splice(index, 1)
        me.$set(me.pageParam, 'dataTotal', me.pageParam.dataTotal - 1)
      } else {
        me.$set(me.gridConfig.data[index], '$rowStatus', editStatus.SHOW)
        let originData = me.gridConfig.data[index].$originData || {}
        for (let field in originData) {
          me.$set(me.gridConfig.data[index], field, originData[field])
        }
        me.$set(me.gridConfig.data[index], '$originData', {})
      }
    },
    /**
     * 是否开启编辑模式
     * @param row
     * @param isPrimaryKey
     * @returns {boolean}
     */
    openEditMode (row, isPrimaryKey) {
      if (isPrimaryKey !== true) {
        isPrimaryKey = false
      }
      if (row.$rowStatus === editStatus.ADD) {
        return true
      } else if (row.$rowStatus === editStatus.EDIT && !isPrimaryKey) {
        return true
      } else {
        return false
      }
    },
    /**
     * 文本框通用渲染方法(可编辑)
     * @param h
     * @param params
     * @param maxLength
     * @param isPrimaryKey
     * @param toolTip
     * @returns {*}
     */
    inputRender (h, params, maxLength, isPrimaryKey, toolTip) {
      let row = params.row
      let index = params.index
      let field = params.column.key
      let scope = this
      let inputVal = row[field]
      if (scope.openEditMode(row, isPrimaryKey)) {
        let props = {
          clearable: true,
          value: inputVal
        }
        if (isNumber(maxLength)) {
          props['maxlength'] = maxLength
        }
        return h('div', [h('Input', {
          props: props,
          on: {
            input: (val) => {
              scope.$set(scope.gridConfig.data[index], field, val)
            }
          }
        })])
      } else {
        if (params.column.tooltip === true) {
          return scope.toolTipRender(h, inputVal, toolTip)
        } else {
          return h('span', inputVal)
        }
      }
    },
    /**
     * 日期选择通用渲染方法(可编辑)
     * @param h
     * @param params
     * @param type
     * @param format
     * @param isPrimaryKey
     * @returns {*}
     */
    dateRender (h, params, type, format, isPrimaryKey) {
      let row = params.row
      let index = params.index
      let field = params.column.key
      let scope = this
      let inputVal = row[field]
      if (scope.openEditMode(row, isPrimaryKey)) {
        if (isNullOrEmpty(type)) {
          type = 'date'
        }
        if (isNullOrEmpty(format)) {
          format = 'yyyy-MM-dd'
        }
        return h('div', [h('DatePicker', {
          props: {
            type: type,
            format: format,
            transfer: true,
            value: inputVal
          },
          on: {
            input: (val) => {
              scope.$set(scope.gridConfig.data[index], field, val)
            }
          }
        })])
      } else {
        return scope.dateTimeShowRender(h, params, format)
      }
    },
    /**
     * 数字输入通用渲染方法(可编辑)
     * @param h
     * @param params
     * @param intLen: 整数位数
     * @param precision: 精度
     * @param negative: 是否允许负值
     * @param isPrimaryKey
     * @param toolTip
     * @returns {*}
     */
    numberRender (h, params, intLen, precision, negative, isPrimaryKey, toolTip) {
      let row = params.row
      let index = params.index
      let field = params.column.key
      let scope = this
      let inputVal = row[field]
      if (scope.openEditMode(row, isPrimaryKey)) {
        let min = 0
        let max = 9999999999
        if (isNumber(intLen)) {
          max = 9
          let digits = 10
          for (let i = 1; i < intLen; i++) {
            max += digits * 9
            digits = digits * 10
          }
        }
        if (negative === true) {
          min = 0 - max
        }
        if (!isNumber(precision)) {
          precision = 0
        }
        if (isNumber(inputVal)) {
          inputVal = parseFloat(parseFloat(inputVal).toFixed(precision))
        } else {
          inputVal = 0
        }
        return h('div', [h('InputNumber', {
          props: {
            min: min,
            max: max,
            value: inputVal,
            precision: precision,
            'active-change': false
          },
          style: {
            width: '100%'
          },
          on: {
            input: (val) => {
              scope.$set(scope.gridConfig.data[index], field, val)
            }
          }
        })])
      } else {
        if (params.column.tooltip === true) {
          return scope.toolTipRender(h, inputVal, toolTip)
        } else {
          return h('span', inputVal)
        }
      }
    },
    /**
     * 下拉框默认渲染方法【可编辑单元格】
     * @param h
     * @param params
     * @param cmbSource
     * @param pCodeKey【如PCode为数据源则为PCode的key】
     * @param isPrimaryKey
     * @param toolTip
     * @returns {*}
     */
    cmbRender (h, params, cmbSource, pCodeKey, isPrimaryKey, toolTip) {
      let row = params.row
      let index = params.index
      let field = params.column.key
      let scope = this
      let cmbVal = row[field]
      if (scope.openEditMode(row, isPrimaryKey)) {
        let options = cmbSource.map(item => {
          return h('Option', {
            props: {
              value: item.value
            }
          }, (item.value + ' ' + item.label))
        })
        return h('div', [h('Select', {
          props: {
            transfer: true,
            clearable: true,
            filterable: true,
            value: cmbVal
          },
          style: {
            paddingTop: '2px'
          },
          on: {
            input: (val) => {
              scope.$set(scope.gridConfig.data[index], field, val)
            }
          }
        }, options)])
      } else {
        if (!isNullOrEmpty(pCodeKey)) {
          cmbVal = getKeyValue(scope.pcodeGet(pCodeKey, cmbVal), cmbVal)
        } else {
          cmbVal = getKeyValue(cmbSource, cmbVal)
        }
        if (params.column.tooltip === true) {
          return scope.toolTipRender(h, cmbVal, toolTip)
        } else {
          return h('span', cmbVal)
        }
      }
    }
  }
}

export {
  baseColumnsExport,
  baseColumnsSingleOperate,
  baseColumnsMultiSelection,
  baseColumnsShow,
  baseColumns,
  baseEditColumns
}
