import { excelExport } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import { editStatus } from './constant'

export const commList = {
  data () {
    return {
      tableloading: false,
      timer: null,
      // 列表界面是否显示
      showList: true,
      // 操作按钮
      actions: [],
      // 列表相关配置
      gridConfig: {
        data: [],
        selectRows: [],
        gridColumns: [],
        exportColumns: [],
        exportTitle: '导出文件名称'
      },
      // 分页相关
      pageParam: {
        page: 1,
        limit: 20,
        dataTotal: -1
      },
      pageSizeOpts: [10, 20, 50, 100],
      // 传入编辑界面的信息
      editConfig: {
        headId: '',
        editData: {},
        editStatus: editStatus.SHOW
      },
      // 调用后台相关URL
      ajaxUrl: {},
      // 是否显示查询条件
      showSearch: false,
      // 查询条件行数
      searchLines: 3,
      // 是否有按钮行
      hasActions: true,
      // 是否有分页行
      hasPager: true,
      // 是否有子Tab组件
      hasChildTabs: false,
      // (列表高度)偏移值
      OffsetHeight: 0,
      // 初始化即查询
      initSearch: true
    }
  },
  mounted: function() {
    let me = this
    me.showSearch = false
    if (me.initSearch) {
      me.$nextTick(() => {
        if (typeof me.beforeFirstSearch === 'function') {
          me.beforeFirstSearch.call(me)
        }
        me.handleSearchSubmit()
      })
    }
  },
  watch: {
    tableloading: {
      handler: function (loading) {
        let me = this
        if (loading) {
          if (me.$refs['table'] && typeof me.$refs['table'].showLoadingOverlay === 'function') {
            me.$refs['table'].showLoadingOverlay()
          }
        } else {
          if (me.$refs['table']) {
            if (Array.isArray(me.gridConfig.data) && me.gridConfig.data.length > 0) {
              if (me.$refs['table'] && typeof me.$refs['table'].hideOverlay === 'function') {
                me.$refs['table'].hideOverlay()
              }
            } else {
              if (me.$refs['table'] && typeof me.$refs['table'].showNoRowsOverlay === 'function') {
                me.$refs['table'].showNoRowsOverlay()
              }
            }
          }
        }
      }
    }
  },
  methods: {
    beforeFirstSearch(){},
    /**
     * 显示/隐藏查询条件
     */
    handleShowSearch() {
      this.showSearch = !this.showSearch
    },
    /**
     * 执行数据查询
     */
    getList() {
      if (isNullOrEmpty(this.ajaxUrl.selectAllPaged)) {
        console.error('查询api不能为空!')
        return
      }
      this.doSearch(this.ajaxUrl.selectAllPaged)
    },
    debounce(fn, delay) {
      let me = this
      return function () {
        clearTimeout(me.timer)
        let call = !me.timer
        if (call) {
          fn.call(this)
        }
        me.timer = setTimeout(function () {
          me.timer = false
        }, delay)
      }
    },
    /**
     * 点击查询按钮
     */
    handleSearchSubmit() {
      this.pageParam.page = 1
      this.debounce(this.getList, 2000)()
      //this.getList()
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      this.editConfig.editData = {}
      this.gridConfig.selectRows = []
      this.editConfig.editStatus = editStatus.ADD
      this.showList = false
    },
    /**
     * 列表中点击数据编辑
     * @param row
     */
    handleEditByRow(row) {
      if (this.customCheck([row], '编辑')) {
        this.editConfig.editStatus = editStatus.EDIT
        this.editConfig.editData = row
        this.showList = false
      }
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      this.editConfig.editStatus = editStatus.SHOW
      this.editConfig.editData = row
      this.showList = false
    },
    /**
     * 是否存在被选中的行
     * @param opTitle (操作标签)
     * @param isSingle (是否有且仅有一条)
     */
    checkRowSelected(opTitle, isSingle) {
      if (isSingle !== true) {
        isSingle = false
      }
      if (isNullOrEmpty(opTitle)) {
        opTitle = '操作'
      }
      if (this.gridConfig.selectRows.length === 0) {
        this.$Message.warning('未选择数据, 请选择您要' + opTitle + '的数据!')
        return false
      } else if (isSingle === true && this.gridConfig.selectRows.length > 1) {
        this.$Message.warning('一次仅能' + opTitle + '一条数据！')
        return false
      } else {
        return true
      }
    },
    /**
     * 自定义编辑、删除检查(可外部覆盖)
     * @param selRows 选中的行数组
     * @param opTitle (操作标签)
     * @returns {boolean}
     */
    customCheck(selRows, opTitle) {
      console.info('执行了自定义【' + opTitle + '】检查')
      return true
    },
    /**
     * 点击编辑按钮执行
     */
    handleEdit() {
      if (this.checkRowSelected('编辑', true)) {
        this.handleEditByRow(this.gridConfig.selectRows[0])
      }
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      // console.info('查询成功后执行的操作')
    },
    /**
     * 查询失败后执行的操作
     */
    afterSearchFailure() {
      // console.info('查询失败后执行的操作')
    },
    /**
     * 查询执行完成后的默认操作
     */
    afterSearch() {
      // console.info('查询执行完成后的默认操作')
    },
    /**
     * 执行查询
     * @param searchUrl
     */
    doSearch(searchUrl) {
      let me = this
      me.$nextTick(() => {
        me.tableloading = true
        let params = me.getSearchParams()
        me.$http.post(searchUrl, params, {
          params: {
            ...me.pageParam
          }
        }).then(res => {
          me.gridConfig.data = res.data.data
          me.pageParam.page = res.data.pageIndex
          me.pageParam.dataTotal = res.data.total
          me.afterSearchSuccess()
        }).catch(() => {
          me.afterSearchFailure()
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.afterSearch()
          me.tableloading = false
        })
      })
    },
    /**
     * 設置按鈕是否進入加載狀態
     * @param indexOrKey
     * @param flag
     */
    setButtonLoading(indexOrKey, flag) {
      let me = this
      if (typeof indexOrKey === 'number' && indexOrKey > -1 && indexOrKey < me.actions.length) {
        me.actions[indexOrKey].loading = flag
      } else if (typeof indexOrKey === 'string' && !isNullOrEmpty(indexOrKey)) {
        if (typeof me.setToolbarLoading === 'function') {
          me.setToolbarLoading(indexOrKey, flag)
        }
      }
    },
    /**
     * 执行导出
     * @param exportUrl
     * @param btnIndexOrKey
     * @param finallyFun
     */
    doExport(exportUrl, btnIndexOrKey, finallyFun) {
      let me = this
      me.$nextTick(() => {
        me.setButtonLoading(btnIndexOrKey, true)
        let params = me.getSearchParams()
        excelExport(exportUrl, {
          exportColumns: params,
          name: me.gridConfig.exportTitle,
          header: me.gridConfig.exportColumns
        }).finally(() => {
          me.setButtonLoading(btnIndexOrKey, false)
          if (typeof finallyFun === 'function') {
            finallyFun.call(me)
          }
        })
      })
    },
    /**
     * 获取选中数据的参数组(可外部覆盖)
     */
    getSelectedParams() {
      return this.gridConfig.selectRows.map(item => {
        return item.sid
      })
    },
    /**
     * 执行删除
     * @param delUrl
     * @param btnIndexOrKey
     */
    doDelete(delUrl, btnIndexOrKey) {
      let me = this
      if (me.checkRowSelected('删除')) {
        if (me.customCheck(me.gridConfig.selectRows, '删除')) {
          me.$Modal.confirm({
            title: '提醒',
            content: '确认删除所选项吗',
            okText: '删除',
            cancelText: '取消',
            onOk: () => {
              me.setButtonLoading(btnIndexOrKey, true)
              let params = me.getSelectedParams()
              me.$http.delete(`${delUrl}/${params}`).then(() => {
                me.$Message.success('删除成功！')
                me.handleSearchSubmit()
              }).catch(() => {
              }).finally(() => {
                me.setButtonLoading(btnIndexOrKey, false)
              })
            }
          })
        }
      }
    },
    /**
     * 行选中或取消选中
     * @param selectRows
     */
    handleSelectionChange(selectRows) {
      this.gridConfig.selectRows = selectRows
    },
    /**
     * 页次切换
     * @param page
     */
    pageChange(page) {
      this.pageParam.page = page
      this.getList()
    },
    /**
     * 切换每页记录数
     * @param pageSize
     */
    pageSizeChange(pageSize) {
      this.pageParam.limit = pageSize
      if (this.pageParam.page === 1) {
        this.getList()
      }
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      this.showList = backObj.showList
      if (this.showList) {
        this.getList()
      }
      this.editConfig.editData = backObj.editData
      this.editConfig.editStatus = backObj.editStatus
    },
    /**
     * 返回列表
     */
    backToList() {
      this.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 設置列表中編輯按鈕是否顯示
     * @returns {string}
     */
    operationEditShow() {
      return ''
    },
    /**
     * 用于AgGrid单元格
     * @param options
     */
    onAgCellOperation(options) {
      if (options.methods === 'handleEditByRow') {
        this.handleEditByRow(options.params)
      } else if (options.methods === 'handleViewByRow') {
        this.handleViewByRow(options.params)
      }
    }
  },
  computed: {
    /**
     * 列表高度
     * 按钮行: 28px (需要添加2px==>与上方空隙)
     * 底部行: 28px
     * 分页行: 28px
     * @returns {number}
     */
    dynamicHeight () {
      // tab头高度: 42px
      let tabHeight = 42
      // 當存在子Tab組件時去除此子Tab頭高度: 38px
      let childTabHeight = 0
      if (this.hasChildTabs) {
        childTabHeight = 38
      }
      // 麵包屑标签行高度: 28px (包含查询按钮)
      let breadCrumbHeight = 28
      // 底部信息欄高度: 28px
      let bottomToolBarHeight = 28
      // 得出基礎高度
      let hiddenHeight = window.innerHeight - tabHeight - childTabHeight - breadCrumbHeight - bottomToolBarHeight
      // 去除按鈕行高度: 28px(需要添加2px: 与上下之間的空隙)
      if (this.hasActions) {
        hiddenHeight = hiddenHeight - 30
      }
      // 去除分頁信息行高度: 28px
      if (this.hasPager) {
        hiddenHeight = hiddenHeight - 28
      }
      // 減去偏移量(某些特殊情況使用)
      hiddenHeight = hiddenHeight - this.OffsetHeight - 2
      // 當查詢條件塊打開時候去除其高度
      if (this.showSearch === true) {
        // 與上方間隙高度
        let gapHeight = 6
        // 分行高度
        let lineHeight = 28
        return hiddenHeight - gapHeight - lineHeight * this.searchLines
      }
      return hiddenHeight
    }
  }
}
