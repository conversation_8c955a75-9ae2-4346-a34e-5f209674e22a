import { isNullOrEmpty } from '@/libs/util'

export const dynamicImport = {
  // name: 'dynamicImport',
  methods: {
    getCommImportConfig(taskCode, param = {}, extendKey = '') {
      let me = this,
        token = me.$store.state.token,
        userNo = me.$store.state.user.userNo,
        tradeCode = me.$store.state.user.company,
        importRoot = '/CsxdoImportBackend/api'
      if (me.$config.cs && !isNullOrEmpty(me.$config.cs.importRoot)) {
        importRoot = me.$config.cs.importRoot
      }
      return {
        systemCode: 'CS-STIC',      // 系统编码
        httpApi: me.$http,     // http_api接口对象
        taskCode: taskCode,    // 模块标识
        baseUri: importRoot,   // 导入api的基路径
        extendKey: extendKey,  // 扩展主键
        tradeCode: tradeCode,  // 企业编码
        businessParam: {       // 自定义业务参数
          ...param,
          token: token,
          insertUser: userNo
        }
      }
    }
  }
}
