<template>
  <section style="padding-top: 68px;">
    <Card class="outer" :style="`background-color: ${this.backgroundColor}`">
      <Layout style="background-color: transparent;">
        <Sider style="background-color: transparent; padding: 38px 3px 10px 2px; min-width: 110px; max-width: 110px;">
          <table class="layoutTable">
            <tr>
              <td>
                <span style="font-weight: bold; font-size: x-large;">{{name}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span>{{department}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span>{{position}}</span>
              </td>
            </tr>
          </table>
        </Sider>
        <Content style="background-color: transparent; padding: 6px 0 10px 0;">
          <table class="layoutTable">
            <tr>
              <td>
                <span style="font-weight: bold; font-size: large; white-space: nowrap;">{{company}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="white-space: nowrap;">{{address}}</span>
              </td>
            </tr>
            <tr>
              <td>
                邮编: <span>{{postCode}}</span>
              </td>
            </tr>
            <tr>
              <td>
                客服热线: <span>{{hotLine}}</span>
              </td>
            </tr>
            <tr>
              <td>
                手机: <span>{{tel}}</span>
              </td>
            </tr>
            <tr>
              <td>
                邮箱: <span>{{email}}</span>
              </td>
            </tr>
            <tr>
              <td>
                网址: <span>{{url}}</span>
              </td>
            </tr>
          </table>
        </Content>
      </Layout>
    </Card>
  </section>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'businessCard',
    props: {
      cardInfo: {
        type: Object,
        default: () => ({
          name: '',       // 姓名
          department: '', // 部门
          position: '',   // 职务
          company: '',    // 公司名称
          address: '',    // 地址
          postCode: '',   // 邮编
          hotLine: '',    // 客服热线
          tel: '',        // 手机号码
          email: '',      // 邮箱
          url: ''         // 公司网址
        })
      },
      backgroundColor: {
        type: String,
        default: () => ('#D6DCE5')
      }
    },
    data() {
      return {
        name: '',       // 姓名
        department: '', // 部门
        position: '',   // 职务
        company: '',    // 公司名称
        address: '',    // 地址
        postCode: '',   // 邮编
        hotLine: '',    // 客服热线
        tel: '',        // 手机号码
        email: '',      // 邮箱
        url: ''         // 公司网址
      }
    },
    watch: {
      cardInfo: {
        deep: true,
        immediate: true,
        handler: function (cardInfo) {
          this.loadData(cardInfo)
        }
      }
    },
    methods: {
      loadData(data) {
        let me = this
        me.$set(me, 'name', '')       // 姓名
        me.$set(me, 'department', '') // 部门
        me.$set(me, 'position', '')   // 职务
        me.$set(me, 'company', '')    // 公司名称
        me.$set(me, 'address', '')    // 地址
        me.$set(me, 'postCode', '')   // 邮编
        me.$set(me, 'hotLine', '')    // 客服热线
        me.$set(me, 'tel', '')        // 手机号码
        me.$set(me, 'email', '')      // 邮箱
        me.$set(me, 'url', '')        // 公司网址
        if (data) {
          if (data.hasOwnProperty('name') && !isNullOrEmpty(data['name'])) {
            me.$set(me, 'name', data['name'].trim())               // 姓名
          }
          if (data.hasOwnProperty('department') && !isNullOrEmpty(data['department'])) {
            me.$set(me, 'department', data['department'].trim())   // 部门
          }
          if (data.hasOwnProperty('position') && !isNullOrEmpty(data['position'])) {
            me.$set(me, 'position', data['position'].trim())       // 职务
          }
          if (data.hasOwnProperty('company') && !isNullOrEmpty(data['company'])) {
            me.$set(me, 'company', data['company'].trim())         // 公司名称
          }
          if (data.hasOwnProperty('address') && !isNullOrEmpty(data['address'])) {
            me.$set(me, 'address', data['address'].trim())         // 地址
          }
          if (data.hasOwnProperty('postCode') && !isNullOrEmpty(data['postCode'])) {
            me.$set(me, 'postCode', data['postCode'].trim())       // 邮编
          }
          if (data.hasOwnProperty('hotLine') && !isNullOrEmpty(data['hotLine'])) {
            me.$set(me, 'hotLine', data['hotLine'].trim())         // 客服热线
          }
          if (data.hasOwnProperty('tel') && !isNullOrEmpty(data['tel'])) {
            me.$set(me, 'tel', data['tel'].trim())                 // 手机号码
          }
          if (data.hasOwnProperty('email') && !isNullOrEmpty(data['email'])) {
            me.$set(me, 'email', data['email'].trim())             // 邮箱
          }
          if (data.hasOwnProperty('url') && !isNullOrEmpty(data['url'])) {
            me.$set(me, 'url', data['url'].trim())                 // 公司网址
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card {
    border-radius: 28px !important;
  }

  /deep/ .ivu-card-head {
    padding: 7px 16px 5px 16px !important;
  }

  /deep/ .ivu-card-extra {
    z-index: 1000;
  }

  .outer {
    /*height: 186px;*/
    margin: 5px 10px;
    padding: 5px 10px;
  }

  .layoutTable, .layoutTable tr, .layoutTable tr td {
    margin: 0;
    padding: 0;
  }
</style>
