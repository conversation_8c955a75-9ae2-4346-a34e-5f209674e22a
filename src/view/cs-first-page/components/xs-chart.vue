<template>
  <section style="padding: 26px 0">
    <table cellpadding="0" cellspacing="0" border="0" style="width: 100%">
      <tr style="margin: 0; padding: 0">
        <td style="margin: 0; padding: 0">
          <Xsc ref="xsc" style="float:left;" :charts="charts" :options="options"></Xsc>
        </td>
        <td style="margin: 0; padding: 0; vertical-align: top">
          <a href="#" slot="extra" @click.prevent="refresh" style="position: relative; right: 164px; top: 20px; z-index: 1000">
            <Icon type="ios-refresh-circle" size="22" color="#70800b"></Icon>
          </a>
        </td>
      </tr>
    </table>
  </section>
</template>

<script>
  export default {
    name: 'xsChart',
    props: {
      title: {
        type: String,
        default: () => ('')
      },
      chartData: {
        type: Object,
        default: () => ({
          legendData: [],
          xAxisData: [],
          validData: []
        })
      },
      theme: {
        type: String,
        default: () => ('light'),
        validate: function (value) {
          return ['light', 'infographic', 'macarons', 'shine', 'walden', 'wonderlan', 'westeros', 'roma'].includes(value)
        }
      },
      xAxisName: {
        type: String,
        default: () => ('month')
      },
      chartType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['barLabelRotation', 'lineMulti'].includes(value)
        }
      }
    },
    data() {
      let me = this,
        height = 320,
        width = 812, // 1064,
        xAxis = me.getXAxis(),
        yAxis = me.getYAxis()
      return {
        charts: [{
          id: '',
          type: 'eCharts',
          chart: '',
          config: {
            box: {
              x: 0,
              y: 0,
              width: width,
              height: height,
              zIndex: 100
            },
            theme: 'wonderlan',
            options: {
              title: {
                text: '',
                show: true,
                x: 'center',
                subtext: '',
                textStyle: {
                  fontSize: 18,
                  lineHeight: 18,
                  color: '#389de9',
                  fontWeight: 'bold',
                  fontStyle: 'normal'
                },
                subtextStyle: {
                  fontSize: 12,
                  lineHeight: 12,
                  fontStyle: 'normal',
                  fontWeight: 'normal'
                }
              },
              tooltip: {
                show: true,
                formatter: '',
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                }
              },
              legend: {
                data: [],
                show: true,
                x: 'center',
                y: 'bottom',
                textStyle: {}
              },
              calculable: true,
              xAxis: {
                ...xAxis,
                data: [],
              },
              yAxis: yAxis,
              series: []
            },
            data: {
              loop: true,
              source: [],
              interval: 30,
              coordinate: 'rightAngle'
            }
          }
        }],
        options: {
          name: '',
          baseUrl: '',
          width: width,
          height: height,
          theme: 'light',
          backgroundSize: null,
          backgroundColor: null,
          backgroundImage: null,
          backgroundRepeat: null
          // baseUrl: 'http://devCollect.isaacxu.com'
        },
        themes: ['light', 'infographic', 'macarons', 'shine', 'walden', 'wonderlan']
      }
    },
    watch: {
      chartData: {
        deep: true,
        immediate: true,
        handler: function (chartData) {
          let me = this,
            seriesAndData = {
              series: [],
              source: []
            }
          me.charts[0].chart = me.chartType
          me.charts[0].config.theme = me.theme
          me.charts[0].config.options.title.text = me.title
          me.charts[0].config.options.legend.data = chartData.legendData
          me.charts[0].config.options.xAxis.data = chartData.xAxisData
          if (chartData.legendData.length > 0) {
            seriesAndData = me.getSeriesAndData()
          }
          me.charts[0].config.options.series = seriesAndData.series
          me.charts[0].config.data.source = seriesAndData.source
          me.options.name = me.title
          if (me.$refs['xsc']) {
            me.$refs['xsc'].update()
          }
        }
      }
    },
    methods: {
      getXAxis() {
        let me = this
        if (me.chartType === 'barLabelRotation') {
          return {
            show: true,
            min: null,
            max: 'dataMax',
            type: 'category',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {}
            },
            axisLabel: {
              fontSize: 12,
              interval: 'auto'
            }
          }
        } else if (me.chartType === 'lineMulti') {
          return {
            min: null,
            max: null,
            show: true,
            type: 'category',
            axisLine: {
              lineStyle: {}
            },
            axisLabel: {
              fontSize: 12,
              interval: 'auto'
            }
          }
        } else {
          return {}
        }
      },
      getYAxis() {
        let me = this,
          comm = {
            min: null,
            show: true,
            type: 'value',
            axisLine: {
              lineStyle: {}
            },
            splitLine: {
              lineStyle: {}
            },
            axisLabel: {
              fontSize: 10,
              interval: 'auto'
            }
          }
        if (me.chartType === 'barLabelRotation') {
          return {
            max: 'dataMax',
            interval: null,
            ...comm
          }
        } else if (me.chartType === 'lineMulti') {
          return {
            max: null,
            ...comm
          }
        } else {
          return {}
        }
      },
      getSeriesLabel() {
        let me = this,
          comm = {
            fontSize: 12,
            position: 'top',
            formatter: '{c}',
            color: 'rgba(0,0,0,1)'
          }
        if (me.chartType === 'barLabelRotation') {
          return {
            ...comm,
            show: true,
            distance: 15,
            align: 'center',
            verticalAlign: 'middle'
          }
        } else if (me.chartType === 'lineMulti') {
          return {
            ...comm,
            fontWeight: 'normal'
          }
        } else {
          return {}
        }
      },
      getSeriesAndData() {
        let me = this,
          data = [],
          series = [],
          dataComm = {
            type: 2,
            db: '',
            sql: '',
            y: 'value',
            x: me.xAxisName,
            xto: 'xAxis/data'
          },
          dataIndex = 0,
          seriesLabel = me.getSeriesLabel()
        me.chartData.validData.forEach(item => {
          if (me.chartType === 'barLabelRotation') {
            series.push({
              barGap: 0.5,
              data: data,
              type: 'bar',
              itemStyle: {},
              name: item.name,
              label: seriesLabel
            })
            data.push({
              ...dataComm,
              s: item.name,
              json: item.json,
              yto: 'series/' + String(dataIndex) + '/data',
              sto: ['legend/data/' + String(dataIndex), 'series/' + String(dataIndex) + '/name']
            })
            dataIndex++
          } else if (me.chartType === 'lineMulti') {
            series.push({
              data: data,
              type: 'line',
              itemStyle: {},
              name: item.name,
              label: seriesLabel
            })
            data.push({
              ...dataComm,
              s: item.name,
              json: item.json,
              yto: 'series/' + String(dataIndex) + '/data',
              sto: ['legend/data/' + String(dataIndex), 'series/' + String(dataIndex) + '/name']
            })
            dataIndex++
          }
        })
        return {
          source: data,
          series: series
        }
      },
      refresh() {
        let me = this
        me.$emit('doReload')
      }
    }
  }
</script>

<style lang="less" scoped>

</style>
