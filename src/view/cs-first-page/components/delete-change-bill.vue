<template>
  <section>
    <Card class="outer" :style="`background-color: ${this.backgroundColor}`">
      <Layout style="background-color: transparent; padding: 4px 0">
        <Sider style="background-color: transparent; padding: 8px 3px 3px 2px; min-width: 48px; max-width: 48px">
          <table class="layoutTable">
            <tr>
              <td style="padding-left: 0; color: #DCD4D4">
                <div class="verticalText">{{title}}</div>
              </td>
            </tr>
          </table>
        </Sider>
        <Content style="background-color: transparent; padding: 0 0 0 18px; border-left: 1px solid #E8EAEC">
          <table class="layoutTable">
            <tr>
              <td style="padding: 2px 0 0 12px; font-weight: bold; font-size: 16px;">
                报关单删改单记录
              </td>
              <td style="width: 5px;">
                <a href="#" slot="extra" class="itemA" @click.prevent="doDataLoad">
                  <Icon type="ios-refresh-circle" size="22" color="#D6DCE5"></Icon>
                </a>
              </td>
            </tr>
            <tr>
              <td style="padding: 0 0 3px 12px; color: #E8EAEC" colspan="2">
                <Icon v-if="loading.deleteChangeShow" type="ios-loading" class="ivu-icon-ios-loading" /><span v-if="!loading.deleteChangeShow">{{billData.deleteChangeCount}}</span>
              </td>
            </tr>
            <tr>
              <td style="padding: 2px 0 0 12px; font-weight: bold; font-size: 16px; border-top: 1px solid #E8EAEC" colspan="2">
                被记录报差错统计
              </td>
            </tr>
            <tr>
              <td style="padding: 0 0 3px 12px; color: #E8EAEC" colspan="2">
                <Icon v-if="loading.diffShow" type="ios-loading" class="ivu-icon-ios-loading" /><span v-if="!loading.diffShow">{{billData.diffCount}}</span>
              </td>
            </tr>
            <tr>
              <td style="padding: 2px 0 0 12px; font-weight: bold; font-size: 16px; border-top: 1px solid #E8EAEC" colspan="2">
                报关单查验记录
              </td>
            </tr>
            <tr>
              <td style="padding: 0 0 0 12px; color: #E8EAEC" colspan="2">
                <Icon v-if="loading.checkRecordShow" type="ios-loading" class="ivu-icon-ios-loading" /><span v-if="!loading.checkRecordShow">{{billData.checkRecordCount}}</span>
              </td>
            </tr>
          </table>
        </Content>
      </Layout>
    </Card>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNumber } from '../../../libs/util'

  export default {
    name: 'contactInfo',
    props: {
      title: {
        type: String,
        default: () => ('')
      },
      backgroundColor: {
        type: String,
        default: () => ('#D6DCE5')
      }
    },
    data() {
      return {
        billData: {
          diffCount: 0,
          checkRecordCount: 0,
          deleteChangeCount: 0
        },
        loading: {
          diffShow: false,
          checkRecordShow: false,
          deleteChangeShow: false
        },
        ajaxUrl: {
          getEntryCheckCount: csAPI.firstPage.deleteChange.getEntryCheckCount
        }
      }
    },
    created: function () {
      let me = this
      me.doDataLoad()
    },
    methods: {
      /**
       * 数据加载
       */
      doDataLoad() {
        let me = this
        // 删单
        me.$set(me.loading, 'deleteChangeShow', true)
        me.$http.post(me.ajaxUrl.getEntryCheckCount + '?businessType=DEL').then(res => {
          if (isNumber(res.data.data)) {
            me.$set(me.billData, 'deleteChangeCount', Number(res.data.data))
          } else {
            me.$set(me.billData, 'deleteChangeCount', 0)
          }
        }).catch(() => {
        }).finally(() => {
          me.$set(me.loading, 'deleteChangeShow', false)
        })
        // 查验
        me.$set(me.loading, 'checkRecordShow', true)
        me.$http.post(me.ajaxUrl.getEntryCheckCount + '?businessType=CHK').then(res => {
          if (isNumber(res.data.data)) {
            me.$set(me.billData, 'checkRecordCount', Number(res.data.data))
          } else {
            me.$set(me.billData, 'checkRecordCount', 0)
          }
        }).catch(() => {
        }).finally(() => {
          me.$set(me.loading, 'checkRecordShow', false)
        })
        // 错误
        me.$set(me.loading, 'diffShow', true)
        me.$http.post(me.ajaxUrl.getEntryCheckCount + '?businessType=ERR').then(res => {
          if (isNumber(res.data.data)) {
            me.$set(me.billData, 'diffCount', Number(res.data.data))
          } else {
            me.$set(me.billData, 'diffCount', 0)
          }
        }).catch(() => {
        }).finally(() => {
          me.$set(me.loading, 'diffShow', false)
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card {
    border-radius: 28px !important;
  }

  /deep/ .ivu-card-extra {
    z-index: 1000;
  }

  .outer {
    margin: 5px 10px;
    padding: 5px 10px;
  }

  .layoutTable {
    width: 100%;
  }

  .layoutTable, .layoutTable tr, .layoutTable tr td {
    margin: 0;
    padding: 0;
  }

  .verticalText {
    width: 15px;
    margin: 0 auto;
    font-size: 20px;
    font-weight: bold;
    line-height: 24px;
    word-wrap: break-word;
  }

  /deep/ .ivu-icon-ios-loading {
    animation: rotating 1.2s linear infinite;
    -webkit-animation: rotating 1.2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0)
    }
    to {
      transform: rotate(360deg)
    }
  }
</style>
