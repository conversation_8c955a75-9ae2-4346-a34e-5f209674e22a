<template>
  <section>
    <div class="dc-form-4 firstPageDiv">
      <ListInfo :listData="firstData" title="待办事项" funType="OYC" @doReload="getAuditList"></ListInfo>
      <ListInfo :listData="secondData" title="待办事项" funType="IE" @doReload="getAuditList"></ListInfo>
      <ListInfo :listData="warringData" title="预警事项" funType="WAR" @doReload="getWarringEmailConfig"></ListInfo>
      <ListInfo :listData="notice" title="综合信息" fun-type="URL" background-color="#F0F3F5"></ListInfo>
      <XsChart class="dc-merge-1-3" title="进出口票数统计" theme="westeros" chart-type="barLabelRotation"
               :chartData="chartData.Histogram" @doReload="getIEQuantity"></XsChart>
      <XsChart class="dc-merge-3-5" title="进出口金额统计" theme="roma" chart-type="lineMulti"
               :chartData="chartData.lineData" @doReload="getIEAmount"></XsChart>
      <DeleteChangeBill class="dc-merge-1-3" title="差错查验统计" background-color="#516B91"></DeleteChangeBill>
      <ContactInfo v-if="contactInfoShow" :class="contactCss" title="商务咨询" :company="contactUs.company" :link-data="contactUs.linkData" background-color="#59C4E6"></ContactInfo>
      <div v-show="instructor.guideType" class="instructor">
        <div class="verticalText" @click="instructorClick">操作指导</div>
        <div :class="instructorCss">
          <table>
            <tr>
              <td>
                <span>指导人员: {{instructor.guidePerson}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span>联系电话: {{instructor.guidePhone}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span>邮箱地址: {{instructor.guideEmail}}</span>
              </td>
            </tr>
            <tr>
              <td>
                <span>微 信 号: {{instructor.guideWeChat}}</span>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import XsChart from './components/xs-chart'
  import ListInfo from './components/list-info'
  import ContactInfo from './components/contact-info'
  import DeleteChangeBill from './components/delete-change-bill'

  export default {
    name: 'home',
    components: {
      XsChart,
      ListInfo,
      ContactInfo,
      DeleteChangeBill
    },
    data() {
      return {
        menuCodesStr: '',
        listData: {
          auditData: [],
          warringData: []
        },
        notice: [{
          orderId: 0,
          auditCount: 0,
          auditType: 'CUSTOMS_RULES'
        }],
        chartData: {
          Histogram: {
            legendData: [],
            xAxisData: [],
            validData: []
          },
          lineData: {
            legendData: [],
            xAxisData: [],
            validData: []
          }
        },
        contactUs: {
          linkData: [],
          company: 'dcEs'
          // company: 'dcJet'
          // company: 'jm2Yun'
        },
        instructor: {
          show: false,
          guideType: false,
          guidePerson: '',
          guidePhone: '',
          guideEmail: '',
          guideWeChat: ''
        },
        ajaxUrl: {
          getAuditList: csAPI.firstPage.audit.selectAllPaged,
          getWarringList: csAPI.firstPage.warning.selectAllPaged,
          getDecErpAmount: csAPI.firstPage.decErp.getDecErpAmount,
          getDecErpQuantity: csAPI.firstPage.decErp.getDecErpQuantity,
          getInstructorList: csAPI.firstPage.instructor.selectAllPaged,
          getEntryCheckCount: csAPI.firstPage.deleteChange.getEntryCheckCount
        }
      }
    },
    created: function() {
      let me = this
      me.pageInit()
    },
    mounted: function() {
      let me = this
      window.majesty.Vue.llk.subscribe(namespace, 'setMintenanceCompanySuccess', () => {
        me.pageInit()
      })
    },
    computed: {
      firstData() {
        return this.listData.auditData.filter(item => {
          return ['O', 'Y', 'C'].includes(item.auditType)
        })
      },
      secondData() {
        return this.listData.auditData.filter(item => {
          return ['I', 'E'].includes(item.auditType)
        })
      },
      warringData() {
        return this.listData.warringData
      },
      contactCss() {
        if (this.contactUs.company === 'dcJet') {
          return 'dc-merge-3-5'
        }
        return 'dc-merge-3-5'
      },
      instructorCss() {
        if (this.instructor.show) {
          return 'instructorShow'
        }
        return 'instructorHide'
      },
      contactInfoShow() {
        let me = this
        if (['37022601QX'].includes(me.$store.state.user.company)) {
          return false
        }
        return true
      }
    },
    methods: {
      /**
       * 页面初始化
       */
      pageInit() {
        let me = this,
          currUrl = window.location.href.toUpperCase()
        if (window.location.ancestorOrigins.length > 0) {
          currUrl = window.location.ancestorOrigins[0].toUpperCase()
          if (currUrl.indexOf('.EPORTYUN.') > -1) {
            me.$set(me.contactUs, 'company', 'jm2Yun')
          } else if (currUrl.indexOf('QUANHAI.51HAITUN.CN') > -1) {
            me.$set(me.contactUs, 'company', 'quanHai')
          }
        } else if (currUrl.indexOf('ICMS.51HAITUN.CN') > -1 || currUrl.indexOf('GCMS.51HAITUN.CN') > -1) {
          me.$set(me.contactUs, 'company', 'dcEs')
        } else if (currUrl.indexOf('CLOUD.51HAITUN.CN') > -1 || currUrl.indexOf('YUN.51HAITUN.CN') > -1 || currUrl.indexOf('CTYUN.51HAITUN.CN') > -1) {
          me.$set(me.contactUs, 'company', 'dcJet')
        } else if (currUrl.indexOf('QUANHAI.51HAITUN.CN') > -1) {
          me.$set(me.contactUs, 'company', 'quanHai')
        }
        me.getAuditList()
        me.getWarringEmailConfig()
        me.getIEQuantity()
        me.getIEAmount()
        me.getLinkData()
        me.loadInstructor()
      },
      /**
       * 获取配置的预警菜单key值
       */
      getWarringEmailConfig() {
        let me = this,
          menuCodes = [],
          menus = me.$store.state.user.menu
        if (Array.isArray(menus)) {
          menus.forEach(item => {
            if (item.code === 'CS-STIC') {
              item.children.forEach(item => {
                if (item.code === 'CS-EARLYWARNING') {
                  item.children.forEach(item => {
                    if (!menuCodes.includes(item.code)) {
                      menuCodes.push(item.code)
                    }
                  })
                }
              })
            }
          })
          me.$set(me, 'menuCodesStr', menuCodes.toString())
        }
        me.getWarringList()
      },
      /**
       * 获取待办事项
       */
      getAuditList() {
        let me = this
        me.$http.post(me.ajaxUrl.getAuditList).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me.listData, 'auditData', res.data.data)
          } else {
            me.$set(me.listData, 'auditData', [])
          }
        }).catch(() => {
          me.$set(me.listData, 'auditData', [])
        })
      },
      /**
       * 获取预警信息
       */
      getWarringList() {
        let me = this
        me.$http.post(me.ajaxUrl.getWarringList + '/' + me.menuCodesStr).then(res => {
          me.$set(me.listData, 'warringData', res.data.data)
        }).catch(() => {
        })
      },
      /**
       * 处理图表数据
       * @param chartData
       */
      dealWithChartData(chartData) {
        let me = this,
          legendData = [],
          xAxisData = [],
          validData = []
        if (Array.isArray(chartData)) {
          chartData.forEach(item => {
            if (Array.isArray(item.data)) {
              legendData.push(item.name)
              let yData = []
              item.data.forEach(iData => {
                yData.push(iData.value)
                if (!xAxisData.includes(iData[me.xAxisName])) {
                  xAxisData.push(iData[me.xAxisName])
                }
              })
              validData.push({
                name: item.name,
                data: yData,
                json: item.data//JSON.stringify(item.data)
              })
            }
          })
        }
        return {
          xAxisData: xAxisData,
          validData: validData,
          legendData: legendData
        }
      },
      /**
       * 获取进出口数量
       */
      getIEQuantity() {
        let me = this
        me.$http.post(me.ajaxUrl.getDecErpQuantity).then(res => {
          let resData = res.data.data,
            dataArr = []
          if (resData) {
            if (resData.hasOwnProperty('ivalue')) {
              dataArr.push(resData['ivalue'])
            }
            if (resData.hasOwnProperty('evalue')) {
              dataArr.push(resData['evalue'])
            }
          }
          let chartData = me.dealWithChartData(dataArr)
          me.$set(me.chartData.Histogram, 'legendData', chartData.legendData)
          me.$set(me.chartData.Histogram, 'xAxisData', chartData.xAxisData)
          me.$set(me.chartData.Histogram, 'validData', chartData.validData)
        }).catch(() => {
        })
      },
      /**
       * 获取进出口金额
       */
      getIEAmount() {
        let me = this
        me.$http.post(me.ajaxUrl.getDecErpAmount).then(res => {
          let resData = res.data.data,
            dataArr = []
          if (resData) {
            if (resData.hasOwnProperty('ivalue')) {
              dataArr.push(resData['ivalue'])
            }
            if (resData.hasOwnProperty('evalue')) {
              dataArr.push(resData['evalue'])
            }
          }
          let chartData = me.dealWithChartData(dataArr)
          me.$set(me.chartData.lineData, 'legendData', chartData.legendData)
          me.$set(me.chartData.lineData, 'xAxisData', chartData.xAxisData)
          me.$set(me.chartData.lineData, 'validData', chartData.validData)
        }).catch(() => {
        })
      },
      /**
       * 获取联系人信息
       */
      getLinkData() {
        let me = this,
          linkData = []
        if (me.contactUs.company === 'dcEs') {
          linkData.push({
            orderId: 0,
            customerHotLine: '************',
            customerEmail: '<EMAIL>',
            address: '苏州工业园区国际科技园二期',
            subAddress: 'B304 & B302 金鸡湖大道1355号'
          })
        } else if (me.contactUs.company === 'dcJet') {
          linkData.push({
            lineId: 1,
            items: [{
              orderId: 0,
              name: '于海霞',
              email: '<EMAIL>',
              tel: '13912797106 / 0512-66081860'
            }, {
              orderId: 1,
              name: '崔嘉',
              email: '<EMAIL>',
              tel: '15862462260 / 0512-66081855'
            }]
          })
          linkData.push({
            lineId: 2,
            items: [{
              orderId: 2,
              name: '沈岩',
              email: '<EMAIL>',
              tel: '15995579963 / 0512-67523285'
            }, {
              orderId: 3,
              name: '赵月萍',
              email: '<EMAIL>',
              tel: '15951882160 / 0512-66081877'
            }]
          })
        } else if (me.contactUs.company === 'jm2Yun') {
          linkData.push({
            orderId: 0,
            subAddress: '',
            customerHotLine: '400-6099-456',
            customerEmail: '<EMAIL>',
            address: '宁波市鄞州区惊驾路668号银晨国际1号楼19楼'
          })
        } else if (me.contactUs.company === 'quanHai') {
          linkData.push({
            orderId: 0,
            subAddress: '',
            customerEmail: '',
            address: '济南市市中区万寿路12号',
            customerHotLine: '0531-68696483'
          })
        }
        me.$set(me.contactUs, 'linkData', linkData)
      },
      /**
       * 加载操作指导信息
       */
      loadInstructor() {
        let me = this
        me.$http.post(me.ajaxUrl.getInstructorList, {}).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$nextTick(() => {
              let theModel = res.data.data[0]
              if (theModel.guideType === '1') {
                me.$set(me.instructor, 'guideType', true)
              } else {
                me.$set(me.instructor, 'guideType', false)
              }
              me.$set(me.instructor, 'guidePerson', theModel.guidePerson)
              me.$set(me.instructor, 'guidePhone', theModel.guidePhone)
              me.$set(me.instructor, 'guideEmail', theModel.guideEmail)
              me.$set(me.instructor, 'guideWeChat', theModel.guideWeChat)
            })
          }
        }).catch(() => {
          me.$set(me.instructor, 'guideType', false)
          me.$set(me.instructor, 'guidePerson', '')
          me.$set(me.instructor, 'guidePhone', '')
          me.$set(me.instructor, 'guideEmail', '')
          me.$set(me.instructor, 'guideWeChat', '')
        })
      },
      /**
       * 显示/隐藏操作指导信息
       */
      instructorClick() {
        let me = this
        me.$set(me.instructor, 'show', !me.instructor.show)
      }
    }
  }
</script>

<style lang="less" scoped>
  .firstPageDiv {
    overflow: auto;
    overflow-x: hidden;
    padding: 18px 16px;
    background-color: white;
  }

  .dc-form-4 {
    grid-column-gap: 30px;
  }

  .dc-form-5 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(5, 1fr);
  }
  .dc-form-5 > div {
    grid-column: 1/5;
  }

  .instructor {
    top:50%;
    right:0;
    z-index: 10000;
    margin-top:-150px;
    margin-left:-300px;
    position: absolute;
  }

  .instructorShow {
    display: block;
  }
  .instructorHide {
    display: none;
  }
  .instructorShow, .instructorHide {
    float: right;
    background-color: #FBE5D6;
    padding: 12px 20px 11px 23px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .verticalText {
    width: 23px;
    float: right;
    margin: 0 auto;
    cursor: pointer;
    font-size: 16px;
    padding: 6px 3px;
    font-weight: bold;
    line-height: 26px;
    word-wrap: break-word;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    background-image: linear-gradient(#F0F3F5, #2ED);
    background-image: -o-linear-gradient(#F0F3F5, #2ED);
    background-image: -moz-linear-gradient(#F0F3F5, #2ED);
    background-image: -webkit-linear-gradient(#F0F3F5, #2ED);
  }
</style>
