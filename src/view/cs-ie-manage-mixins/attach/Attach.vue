<template>
  <section>
    <XdoCard :bordered="false">
      <div class="form-title-wrapper">
        <p><span>单据内部编号</span></p>
      </div>
      <div class="form-title-body-wrapper">
        <RadioGroup type="button" v-model="emsListNo">
          <Radio :label="edit.editData.emsListNo"></Radio>
        </RadioGroup>
      </div>
      <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns"
             :data="gridConfig.data" stripe border>
        <template slot-scope="{ row }" slot="attachNoSlot">
          <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
            <span v-if="row.attachNoList.length > 1 && attachNoListItem.attachNo !== ''">{{++index}}、</span>{{ attachNoListItem.attachNo }}
          </div>
        </template>
        <template slot-scope="{ row }" slot="attachDataSlot">
          <!--标准格式-->
          <template v-if="row.attachType !==  attachTypeEnum.invoiceNo">
            <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
              <div class="filesList">
                <span>附件<span v-if="row.attachNoList.length > 1 && attachNoListItem.attachNo !== ''">{{++index}}</span>：</span>
                <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                        :show-upload-list="false"
                        :before-upload="handleBeforeUpload"
                        :on-error="handleOnError"
                        :on-success="handleOnSuccess"
                        :title='"共" + attachNoListItem.attachFiles.length + "个文件"'
                        v-show="buttonIsShow">
                  <strong>
                    <a icon="md-cloud-upload" long @click.prevent="onUpAttach(row.attachType, attachNoListItem.attachNo)">
                      <XdoIcon type="ios-loop-strong"></XdoIcon>
                      [上传]
                    </a>
                  </strong>
                </Upload>
                <ul>
                  <li v-for="(attachFileItem, index) of attachNoListItem.attachFiles" :key="attachFileItem.fileSid">
                  <span :title="attachFileItem.fileName">
                    <XdoIcon title="删除" type="md-close" @click="onDeleteFile(attachFileItem.fileSid,index,row.attachType,attachNoListItem.attachNo)" v-show="buttonIsShow"/>
                    <a @click.prevent="downloadFile(attachFileItem.fileSid)">{{attachFileItem.fileName}}</a>
                  </span>
                  </li>
                </ul>
              </div>
            </div>
          </template>

          <!--嵌套格式 发票箱单箱单-->
          <template v-else>
            <div v-for="(attachNoListItem, index) of row.attachNoList" :key="index">
              <div class="filesList">
                <span>附件<span v-if="row.attachNoList.length > 1 && attachNoListItem.attachNo !== ''">{{++index}}</span>：</span>
                <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                        :show-upload-list="false"
                        :before-upload="handleBeforeUpload"
                        :on-error="handleOnError"
                        :on-success="handleOnSuccess" :title='"共" + attachNoListItem.invoiceNoFiles.length + "个文件"'
                        v-show="buttonIsShow">
                  <strong>
                    <a icon="md-cloud-upload" long @click.prevent="onUpAttach(row.attachType, attachNoListItem.attachNo)">
                      <XdoIcon type="ios-loop-strong"></XdoIcon>
                      [上传发票]
                    </a>
                  </strong>
                </Upload>
                <Upload :action="uploadFileConfig.action" :headers="uploadFileConfig.headers" :data="uploadFileConfig.data"
                        :show-upload-list="false"
                        :before-upload="handleBeforeUpload"
                        :on-error="handleOnError"
                        :on-success="handleOnSuccess" :title='"共" + attachNoListItem.boxNoFiles.length + "个文件"'
                        v-show="buttonIsShow">
                  <strong>
                    <a icon="md-cloud-upload" long @click.prevent="onUpAttach(attachTypeEnum.boxNo, attachNoListItem.attachNo)">
                      <XdoIcon type="ios-loop-strong"></XdoIcon>
                      [上传箱单]
                    </a>
                  </strong>
                </Upload>
                <div>
                  <span v-if="attachNoListItem.invoiceNoFiles.length > 0">发票：</span>
                  <ul>
                    <li v-for="(attachFileItem, index) of attachNoListItem.invoiceNoFiles" :key="attachFileItem.fileSid">
                    <span :title="attachFileItem.fileName">
                      <XdoIcon title="删除" type="md-close" @click="onDeleteFile(attachFileItem.fileSid,index,row.attachType,attachNoListItem.attachNo)" v-show="buttonIsShow"/>
                      <a @click.prevent="downloadFile(attachFileItem.fileSid)">{{attachFileItem.fileName}}</a>
                    </span>
                    </li>
                  </ul>
                </div>
                <div>
                  <span v-if="attachNoListItem.invoiceNoFiles.length > 0">箱单：</span>
                  <ul>
                    <li v-for="(attachFileItem,index) of attachNoListItem.boxNoFiles" :key="attachFileItem.fileSid">
                        <span :title="attachFileItem.fileName">
                          <XdoIcon title="删除" type="md-close" @click="onDeleteFile(attachFileItem.fileSid,index,attachTypeEnum.boxNo,attachNoListItem.attachNo)" v-show="buttonIsShow"/>
                          <a @click.prevent="downloadFile(attachFileItem.fileSid)">{{attachFileItem.fileName}}</a>
                        </span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </template>
        </template>
      </XdoTable>
    </XdoCard>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { getHttpHeaderFileName, blobSaveFile } from '@/libs/util'

  export default {
    name: 'Attach',
    props: {
      headId: {
        type: String,
        default: ''
      },
      edit: {
        type: Object,
      },
      aeoShow: {
        type: Boolean,
        default: false
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        emsListNo: this.edit.editData.emsListNo,
        attachTypeEnum: {
          emsListNo: 'emsListNo',
          attachType: 'pactCode',
          invoiceNo: 'invoiceNo',
          boxNo: 'boxNo',
          entryNo: 'entryNo',
          other: 'other',
          invoiceBox: 'invoiceBox',
          review: 'review',
          invBoxBill: 'invBoxBill',
          tax: 'tax',
          statement: 'statement',
          situation: 'situation',
          orderNo: 'orderNo',
          origin: 'origin'
        },
        normalFiles: ['emsListNo', 'pactCode', 'entryNo', 'invoiceBox', 'review', 'invBoxBill', 'tax', 'statement', 'situation', 'other', 'orderNo', 'origin'],
        gridConfig: {
          gridColumns: [{
            title: '单证类型',
            width: 120,
            align: 'center',
            key: 'attachTypeName'
          }, {
            title: '单证号',
            width: 86,
            align: 'center',
            slot: 'attachNoSlot'
          }, {
            title: '文件',
            align: 'left',
            slot: 'attachDataSlot'
          }],
          data: [{
            attachTypeName: '预录入单',
            attachType: 'emsListNo',
            attachNoList: []
          }, {
            attachTypeName: '合同',
            attachType: 'pactCode',
            attachNoList: []
          }, {
            attachTypeName: '发票箱单',
            attachType: 'invoiceNo',
            attachNoList: []
          }, {
            attachTypeName: '报关单',
            attachType: 'entryNo',
            attachNoList: []
          }, {
            attachTypeName: '发票&箱单',
            attachType: 'invoiceBox',
            attachNoList: []
          }, {
            attachTypeName: '复核单',
            attachType: 'review',
            attachNoList: []
          }, {
            attachTypeName: '发票&箱单&提单',
            attachType: 'invBoxBill',
            attachNoList: []
          }, {
            attachTypeName: '税单',
            attachType: 'tax',
            attachNoList: []
          }, {
            attachTypeName: '对账单',
            attachType: 'statement',
            attachNoList: []
          }, {
            attachTypeName: '情况说明',
            attachType: 'situation',
            attachNoList: []
          }, {
            attachTypeName: '其他',
            attachType: 'other',
            attachNoList: []
          }, {
            attachTypeName: '订单',
            attachType: 'orderNo',
            attachNoList: []
          }, {
            attachTypeName: '原产地证',
            attachType: 'origin',
            attachNoList: []
          }]
        },
        ajaxUrl: {
          upload: csAPI.attachedInfo.insert,
          download: csAPI.attachedInfo.get,
          delete: csAPI.attachedInfo.delete,
          getAttachList: csAPI.attachedInfo.list
        },
        uploadInfo: {
          acmpNo: '',
          acmpType: ''
        }
      }
    },
    mounted() {
      let me = this
      me.$nextTick(() => {
        me.loadAttach()
      })
    },
    methods: {
      /**
       * 获取随附单证数据（不含附件信息）
       */
      loadAttach() {
        let me = this
        me.$http.get(`${csAPI.attachedInfo.getPreAcmpInfo}/${me.headId}/${me.iemark}`).then(res => {
          let attachData = res.data.data
          /*
            * 其它栏位是杂项，不存在单证号，但可直接上传附件
            * 使用预录入单号作为其它项的“单证号”
            * */
          attachData = Object.assign(attachData, {
            other: [''],
            pactCode: [''],
            invoiceNo: [''],
            entryNo: [''],
            invoiceBox: [''],
            review: [''],
            invBoxBill: [''],
            tax: [''],
            statement: [''],
            situation: [''],
            orderNo: [''],
            origin: ['']
          })
          if (Array.isArray(attachData.emsListNo) && attachData.emsListNo.length === 0) {
            attachData['emsListNo'] = ['']
          }
          // 初始化 单证信息
          me.gridConfig.data.map(item => {
            attachData[item.attachType].forEach(value => {
              if (item.attachType === me.attachTypeEnum.invoiceNo) {
                item.attachNoList.push({
                  attachNo: value,
                  invoiceNoFiles: [],
                  boxNoFiles: []
                })
              } else {
                item.attachNoList.push({
                  attachNo: value,
                  attachFiles: []
                })
              }
            })
          })
          // 加载文件
          me.loadAttachFiles();
        }).catch(() => {
        })
      },
      /**
       * 加载文件
       */
      loadAttachFiles() {
        let me = this
        // 加载对应的文件信息
        let attachType = Object.values(me.attachTypeEnum).join(',')
        me.$http.post(me.ajaxUrl.getAttachList, {
          businessSid: me.headId,
          businessType: (me.iemark + 'M'),
          acmpType: attachType
        }).then(res => {
          let attachDataFiles = res.data.data
          // 初始化 单证对应的文件信息
          me.gridConfig.data.forEach(grdDataItem => {
            grdDataItem.attachNoList.map(attachNoListItem => {
              // 根据 随附单据号&类型筛选 符合条件的附件
              let files = null
              if (me.normalFiles.indexOf(grdDataItem.attachType) > -1) {
                files = attachDataFiles.filter(item => item.acmpType === grdDataItem.attachType).map(item => {
                  return {fileSid: item.sid, fileName: item.originFileName}
                })
              } else {
                // files = attachDataFiles.filter(item => item.acmpType === grdDataItem.attachType
                //   && attachNoListItem.attachNo === item.acmpNo).map(item => {
                //   return {fileSid: item.sid, fileName: item.originFileName}
                // })
              }
              // 发票箱单 单独处理
              if (grdDataItem.attachType === me.attachTypeEnum.invoiceNo) {
                files = attachDataFiles.filter(item => item.acmpType === me.attachTypeEnum.invoiceNo).map(item => {
                  return {fileSid: item.sid, fileName: item.originFileName}
                })
                me.$set(attachNoListItem, 'invoiceNoFiles', files)
                //箱号
                // let boxFiles = attachDataFiles.filter(item => item.acmpType === me.attachTypeEnum.boxNo
                //   && attachNoListItem.attachNo === item.acmpNo).map(item => {
                //   return {fileSid: item.sid, fileName: item.originFileName}
                // })
                let boxFiles = attachDataFiles.filter(item => item.acmpType === me.attachTypeEnum.boxNo).map(item => {
                  return {fileSid: item.sid, fileName: item.originFileName}
                })
                me.$set(attachNoListItem, 'boxNoFiles', boxFiles)
              } else {
                me.$set(attachNoListItem, 'attachFiles', files)
              }
            })
          })
        }).catch(() => {
        })
      },
      /**
       * 文件删除
       */
      onDeleteFile(fileSid, index, attachType, attachNo) {
        let me = this
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '您确定要删除此附件吗?',
          onOk: () => {
            me.$http.delete(`${me.ajaxUrl.delete}/${fileSid}`).then(() => {
              me.$Message.success('删除成功!')
              me.gridConfig.data.filter(item => item.attachType === attachType
                || (attachType === me.attachTypeEnum.boxNo && item.attachType === me.attachTypeEnum.invoiceNo)).map(item => {
                item.attachNoList.filter(attachNoItem => attachNoItem.attachNo === attachNo).map(attachNoItem => {
                  if (attachType === me.attachTypeEnum.invoiceNo) {
                    attachNoItem.invoiceNoFiles.splice(index, 1)
                  } else if (attachType === me.attachTypeEnum.boxNo) {
                    attachNoItem.boxNoFiles.splice(index, 1)
                  } else {
                    attachNoItem.attachFiles.splice(index, 1)
                  }
                })
              })
            }).catch(() => {
            })
          }
        })
      },
      /**
       * 文件上传
       */
      onUpAttach(attachType, attachNo) {
        this.uploadInfo.acmpType = attachType
        this.uploadInfo.acmpNo = attachNo
      },
      /**
       * 上传之前回调方法
       */
      handleBeforeUpload() {
      },
      /**
       * 上传错误回调方法
       */
      handleOnError() {
      },
      /*
      * 上传附件成功后，回调
      * */
      handleOnSuccess(response, file) {
        let me = this
        if (response.success) {
          let attachDataList = {
            fileSid: response.data.sid,
            fileName: file.name
          }
          me.gridConfig.data.filter(item => item.attachType === response.data.acmpType
            || (item.attachType === me.attachTypeEnum.invoiceNo && response.data.acmpType === me.attachTypeEnum.boxNo)).map(item => {
            item.attachNoList.filter(attachNoItem => attachNoItem.attachNo === response.data.acmpNo).map(attachNoItem => {
              if (response.data.acmpType === me.attachTypeEnum.invoiceNo) {
                attachNoItem.invoiceNoFiles.push(attachDataList)
              } else if (response.data.acmpType === me.attachTypeEnum.boxNo) {
                attachNoItem.boxNoFiles.push(attachDataList)
              } else {
                attachNoItem.attachFiles.push(attachDataList)
              }
            })
          })
        } else {
          me.$Message.error(response.message)
        }
      },
      /**
       * 附件下载
       * @param sysId
       */
      downloadFile(sysId) {
        let me = this
        me.$http.get(`${me.ajaxUrl.download}/${sysId}`, {
          responseType: 'blob'
        }).then(res => {
          const name = getHttpHeaderFileName(res.headers)
          const blob = new Blob([res.data], {type: 'application/octet-stream'})
          blobSaveFile(blob, name)
        }).catch(() => {
        })
      }
    },
    computed: {
      // 上传按钮是否显示
      buttonIsShow() {
        return !this.aeoShow
      },
      uploadFileConfig() {
        let me = this
        return {
          action: me.ajaxUrl.upload,
          headers: {
            Authorization: 'Bearer ' + me.$store.state.token
          },
          data: {
            acmpFormat: '',
            billSerialNo: '',
            businessSid: me.headId,
            acmpNo: me.uploadInfo.acmpNo,
            businessType: (me.iemark + 'M'),
            acmpType: me.uploadInfo.acmpType
          }
        }
      }
    }
  }
</script>

<style scoped>
  tr.ivu-table-row-hover td {
    background-color: snow;
  }

  .ivu-icon {
    cursor: pointer;
  }

  .ivu-upload {
    width: 80px;
    display: inline-block;
  }

  .filesList {
    margin-top: 8px;
    text-align: left;
    margin-bottom: 8px;
  }

  .filesList strong a {
    font-size: 14px;
  }

  ul li {
    color: #999;
    list-style: none;
    margin-left: 15px;
    display: inline-block;
  }

  .form-title-wrapper {
    line-height: 1px;
    padding: 5px 10px !important;
    background-color: #dcdee2 !important;
  }

  .form-title-wrapper p {
    width: 100%;
    height: 20px;
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
  }

  .form-title-wrapper p span {
    vertical-align: middle;
  }

  .form-title-body-wrapper {
    padding: 8px 8px 2px 8px;
  }

  .form-title-body-wrapper .ivu-form-item {
    margin-bottom: 3px;
  }
</style>
