import { csAPI } from '@/api'
import { namespace } from '@/project'
import { auditTrailDetail } from '../../cs-aeoManage/base/auditTrailDetail'
import { transModeRules } from '@/view/cs-ie-manage/js/comm/transModeRules'
import FeeCascader from '@/view/cs-ie-manage-mixins/components/fee-cascader'
import { isNullOrEmpty, isNumber, ArrayToLocaleLowerCase } from '@/libs/util'
import { regulatoryRules } from '@/view/cs-ie-manage/js/comm/regulatoryRules'
import decErpHeadCheckPop from '@/view/cs-ie-manage/components/dec-erp-head-check-pop'
import { editStatus, SelectTemplate, TemplateConfirm, importExportManage } from '@/view/cs-common'
import WrapTypeSelectPop from '@/view/cs-ie-manage/components/wrap-type-select/wrap-type-select-pop'
import promiseItemsComponent from '@/view/cs-ie-manage/components/promise-items/promise-items-component'

export const decErpHeadEdit = {
  components: {
    FeeCascader,
    SelectTemplate,
    TemplateConfirm,
    WrapTypeSelectPop,
    decErpHeadCheckPop,
    promiseItemsComponent
  },
  props: {
    aeoShow: {
      type: Boolean,
      default: false
    },
    editConfig: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [regulatoryRules, transModeRules, auditTrailDetail],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    let defaultData = this.getDefaultData()
    return {
      timer: null,
      headerData: {
        ...defaultData
      },
      rulesHeader: {
        emsListNo: [
          {required: true, message: '不能为空!', trigger: 'blur'},
          {max: 32, message: '长度不能超过32位字节(汉字占2位)!', trigger: 'blur'}
        ],
        // mergeType: [{ required: true, message: '不能为空!', trigger: 'blur' }],

        feeMark: [{required: false, message: '不能为空!', trigger: 'blur'}],
        feeRate: [{type: 'number', required: false, message: '不能为空!', trigger: 'blur'}],
        feeCurr: [{required: false, message: '不能为空!', trigger: 'blur'}],

        insurMark: [{required: false, message: '不能为空!', trigger: 'blur'}],
        insurRate: [{type: 'number', required: false, message: '不能为空!', trigger: 'blur'}],
        insurCurr: [{required: false, message: '不能为空!', trigger: 'blur'}],

        otherMark: [],
        otherRate: [{type: 'number', required: false, message: '不能为空!', trigger: 'blur'}],
        otherCurr: [{required: false, message: '不能为空!', trigger: 'blur'}],

        tradeMode: [{required: false, message: '不能为空!', trigger: 'blur'}],
        wrapType: [{required: false, message: '不能为空!', trigger: 'blur'}],
        packNum: [{required: false, message: '不能为空!', trigger: 'blur'}],
        netWt: [{required: false, message: '不能为空!', trigger: 'blur'}],
        grossWt: [{required: false, message: '不能为空!', trigger: 'blur'}],
        transMode: [{required: false, message: '不能为空!', trigger: 'blur'}],
        trafName: [{required: false, message: '不能为空!', trigger: 'blur'}],
        tradeNation: [{required: false, message: '不能为空!', trigger: 'blur'}],
        tradeTerms: [{required: false, message: '不能为空!', trigger: 'blur'}],
        invoiceNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
        contrNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
        forwardCode: [{required: false, message: '不能为空!', trigger: 'blur'}],
        iedate: [],
        billListType: [{required: true, message: '不能为空!', trigger: 'blur'}],
        ownerCreditCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
        ownerName: [{required: true, message: '不能为空!', trigger: 'blur'}],
        agentCode: [{required: true, message: '不能为空!', trigger: 'blur'}],
        agentName: [{required: true, message: '不能为空!', trigger: 'blur'}]
      },
      buttons: [
        {...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'},
        {
          label: '返回',
          loading: false,
          type: 'primary',
          disabled: false,
          icon: 'dc-btn-cancel',
          needed: !this.aeoShow,
          click: this.handleBack
        },
        {...btnComm, click: this.handleSaveTemplate, icon: 'dc-btn-saveTemplate', label: '保存模板'}
      ],
      promiseItemsList: [],
      editDisable: false,
      showDisable: false,
      feeDisabled: false,
      insurDisabled: false,
      cutModeDisabled: false,
      tempConfirmShow: false,
      showSelectTemplate: false,
      // 是否从模板中加载
      isLoadFromTemplate: false,
      // 是否从编辑中加载
      isLoadEdit: false,
      cmbDataSource: {
        cutData: [],
        cutDataValueCode: {},
        emsNoList: [],
        ownerCode: [],
        forwardCodeList: [],
        tradeTermNewList: [],
        overseasShipperList: [],
        feeMarkList: importExportManage.feeTypeMap,
        entryTypeData: importExportManage.entryType,
        billTypeList: importExportManage.billTypeMap,
        dclcusMarkData: importExportManage.dclcusMark,
        dclcusTypeData: importExportManage.dclcusType,
        containerList: importExportManage.containerType,
        tradeTermList: importExportManage.tradeTermList,
        mergeTypeList: importExportManage.mergeTypeMapFull
      },
      firstWatch: true,
      ajaxUrl: {
        insert: '',
        update: ''
      },
      importExportManage: importExportManage,
      // 是否【单据内部编号】自动生成
      isEmsListNoAuto: false,
      moduleName: '',
      statistics: {
        sumQty: null,
        sumDecTotal: null,
        curr: '',
        currName: ''
      },
      promiseItems: {
        pi1Im: false,
        pi2Im: false,
        pi3Im: false,
        pi4Im: false
      },
      promiseItemsOrgin: [],
      requiredFields: [
        {
          key: 'wrapType', ref: 'fiWrapType'
        }, {
          key: 'packNum', ref: 'fiPackNum'
        }, {
          key: 'netWt', ref: 'fiNetWt'
        }, {
          key: 'grossWt', ref: 'fiGrossWt'
        }, {
          key: 'feeMark', ref: 'feeMark'
        }, {
          key: 'insurMark', ref: 'insurMark'
        }, {
          key: 'transMode', ref: 'transMode'
        }, {
          key: 'trafName', ref: 'fiTrafName'
        }, {
          key: 'tradeNation', ref: 'fiTradeNation'
        }, {
          key: 'tradeTerms', ref: 'fiTradeTerms'
        }, {
          key: 'invoiceNo', ref: 'fiInvoiceNo'
        }, {
          key: 'contrNo', ref: 'fiContrNo'
        }, {
          key: 'forwardCode', ref: 'fiForwardCode'
        }, {
          key: 'iedate', ref: 'fiIEDate'
        }, {
          key: 'districtCode', ref: 'fiDistrictCode'
        }, {
          key: 'overseasShipper', ref: 'fiOverseasShipper'
        }, {
          key: 'ieport', ref: 'fiIeport'
        }, {
          key: 'trafMode', ref: 'fiTrafMode'
        }, {
          key: 'tradeCountry', ref: 'fiTradeCountry'
        }],
      requiredBlueFields: {
        wrapType: false,
        packNum: false,
        netWt: false,
        grossWt: false,
        feeMark: false,
        insurMark: false,
        transMode: false,
        trafName: false,
        tradeNation: false,
        tradeTerms: false,
        invoiceNo: false,
        contrNo: false,
        forwardCode: false,
        iedate: false,
        districtCode: false,
        overseasShipper: false,
        ieport: true,
        trafMode: true,
        tradeCountry: true
      },
      isDeclare: true,
      wrapType2PopShow: false,
      tradeModePreRequire: true,
      formDataName: 'headerData',
      masterCustomsRequired: false,
      headCheckPop: {
        show: false,
        errData: []
      }
    }
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function (config) {
        let me = this
        if (config && config.editStatus === editStatus.ADD) {
          me.resetFormData()
          me.$set(me.headerData, 'receiveCode', me.$store.state.user.company)
          me.$set(me.headerData, 'receiveName', me.$store.state.user.companyName)
          me.$set(me.headerData, 'agentCode', me.$store.state.user.company)
          me.agentCodeEnter()

          me.editDisable = false
          me.showDisable = false
          me.feeDisabled = false
          me.insurDisabled = false
          me.cutModeDisabled = false
        } else if (config && config.editStatus === editStatus.EDIT) {
          me.isLoadEdit = true
          me.headerData = {...config.editData}
          if (!isNullOrEmpty(me.headerData.promiseItems)) {
            me.promiseItemsList = me.headerData.promiseItems.split(",")
          }
          me.setFeeRules('fee', me.headerData.feeMark)
          me.setFeeRules('insur', me.headerData.insurMark)
          me.setFeeRules('other', me.headerData.otherMark)

          me.editDisable = true
          me.showDisable = false
          // 锁定时不能编辑
          if (config.editData.status === '1') {
            me.showDisable = true
            me.buttons[0].needed = false
            me.buttons[2].needed = false
          }

          me.feeDisabled = false
          me.insurDisabled = false
          me.cutModeDisabled = false

          me.tradeModeChange(config.editData.tradeMode)
          setTimeout(() => {
            me.isLoadEdit = false
            me.headerData.cutMode = config.editData.cutMode
          }, 500)
        } else if (config && config.editStatus === editStatus.SHOW) {
          me.showDisable = true

          me.feeDisabled = true
          me.insurDisabled = true
          me.cutModeDisabled = true

          me.buttons[0].needed = false
          me.buttons[2].needed = false

          me.isLoadEdit = true
          me.headerData = {...config.editData}
          if (!isNullOrEmpty(me.headerData.promiseItems)) {
            me.promiseItemsList = me.headerData.promiseItems.split(",")
          }
          me.editDisable = true
          setTimeout(() => {
            me.isLoadEdit = false
            me.headerData.cutMode = config.editData.cutMode
          }, 500)
        } else {
          console.error('缺失编辑信息!')
        }
      }
    },
    'headerData.feeMark': function (val) {
      this.setFeeRules('fee', val)
    },
    'headerData.insurMark': function (val) {
      this.setFeeRules('insur', val)
    },
    'headerData.otherMark': function (val) {
      this.setFeeRules('other', val)
    },
    'headerData.transMode': {
      immediate: true,
      handler: function (val) {
        this.transactionChange(val)
      }
    },
    'headerData.declareCodeCustoms': {
      handler: function (declareCodeCustoms) {
        let me = this
        if (!isNullOrEmpty(declareCodeCustoms)) {
          let declareCode = me.cmbDataSource.cutDataValueCode[declareCodeCustoms]
          if (!isNullOrEmpty(declareCode)) {
            me.$set(me.headerData, 'declareCode', declareCode)
          } else {
            me.$set(me.headerData, 'declareCode', '')
          }
          me.declareCodeEnter()
        }
      }
    },
    'headerData.agentCodeCustoms': {
      handler: function (agentCodeCustoms) {
        let me = this
        if (!isNullOrEmpty(agentCodeCustoms)) {
          let agentCode = me.cmbDataSource.cutDataValueCode[agentCodeCustoms]
          if (!isNullOrEmpty(agentCode)) {
            me.$set(me.headerData, 'agentCode', agentCode)
          } else {
            me.$set(me.headerData, 'agentCode', '')
          }
          me.agentCodeEnter()
        }
      }
    },
    'headerData.emsNo': {
      deep: true,
      handler: function (emsNo) {
        let me = this
        me.cutModeGMarkCtrl(emsNo, me.headerData.tradeMode)
        let data = (me.configRequiredField || '').split(',')
        me.$set(me, 'masterCustomsRequired', false)
        if (Array.isArray(data) && data.includes('masterCustoms')) {
          if (!isNullOrEmpty(emsNo)) {
            me.$set(me, 'masterCustomsRequired', true)
          }
        }
      }
    },
    isEmsListNoAuto: {
      immediate: true,
      handler: function (flag) {
        let me = this
        me.rulesHeader.emsListNo[0].required = flag !== true
        me.$nextTick(() => {
          if (me.$refs['fiEmsListNo']) {
            me.$refs['fiEmsListNo'].setRules()
          }
        })
      }
    },
    // 'statistics.curr': {
    'headerData.curr': {
      immediate: true,
      handler: function (curr) {
        let me = this,
          currName = me.pcodeGet(me.pcode.curr_outdated, curr)
        if (!isNullOrEmpty(currName)) {
          me.$set(me.statistics, 'currName', curr + ' ' + currName)
        } else {
          me.$set(me.statistics, 'currName', '')
        }
      }
    },
    promiseItemsList: {
      immediate: true,
      handler: function (piArr) {
        let me = this
        if (Array.isArray(piArr) && piArr.indexOf('9') > -1) {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi1Im', true)
          })
        } else {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi1Im', false)
          })
        }
        if (Array.isArray(piArr) && piArr.indexOf('8') > -1) {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi2Im', true)
          })
        } else {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi2Im', false)
          })
        }
        if (Array.isArray(piArr) && piArr.indexOf('7') > -1) {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi3Im', true)
          })
        } else {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi3Im', false)
          })
        }
        if (Array.isArray(piArr) && piArr.indexOf('6') > -1) {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi4Im', true)
          })
        } else {
          me.$nextTick(() => {
            me.$set(me.promiseItems, 'pi4Im', false)
          })
        }
        me.$nextTick(() => {
          me.$set(me, 'promiseItemsOrgin', piArr)
        })
      }
    },
    // 以下為通關業務配置相關內容
    'configRequiredField': {
      immediate: true,
      handler: function (requiredField) {
        let me = this
        me.onTransactionChangeBase(null, requiredField)
      }
    },
    'configData.emsNo': {
      handler: function (emsNo) {
        let me = this
        if (me.editConfig.editStatus === editStatus.ADD) {
          me.$set(me.headerData, 'emsNo', emsNo)
        }
      }
    },
    'configData.tradeMode': {
      handler: function (tradeMode) {
        let me = this
        if (me.editConfig.editStatus === editStatus.ADD) {
          me.$set(me.headerData, 'tradeMode', tradeMode)
        }
      }
    },
    'headerData.tradeTerms': {
      immediate: true,
      handler: function (tradeTerms) {
        let me = this
        if (isNullOrEmpty(me.headerData.transMode)) {
          let ieMark = me.moduleName === 'LE' ? 'E' : 'I'
          let currTransMode = me.getTransModeByTradeTerms(tradeTerms, ieMark)
          me.$set(me.headerData, 'transMode', currTransMode)
        }
      }
    },
    'headerData.dclcusMark': {
      immediate: true,
      handler: function (dclcusMark) {
        let me = this
        me.$set(me, 'isDeclare', true)
        if (dclcusMark === '2') {
          me.$set(me, 'isDeclare', false)
          me.$set(me.headerData, 'entryType', '')
          me.$set(me.headerData, 'dclcusType', '')
          me.$set(me.headerData, 'declareCode', '')
          me.$set(me.headerData, 'declareName', '')
          me.$set(me.headerData, 'declareCreditCode', '')
          me.$set(me.headerData, 'declareCodeCustoms', '')
        }
      }
    },
    'headerData.mergeType': {
      immediate: true,
      handler: function (mergeType) {
        let me = this
        me.$nextTick(() => {
          if (mergeType === '1') {
            me.rulesHeader.tradeMode[0].required = true
            me.tradeModePreRequire = false
          } else {
            me.rulesHeader.tradeMode[0].required = false
            me.tradeModePreRequire = true
          }
          if (me.$refs['fiTradeMode']) {
            me.$refs['fiTradeMode'].setRules()
          }
        })
      }
    }
  },
  created: function () {
    let me = this
    me.cmbDataSource.tradeTermNewList = [...me.cmbDataSource.tradeTermList]
    // 货代
    me.$http.post(csAPI.ieParams.FOD).then(res => {
      me.cmbDataSource.forwardCodeList = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbDataSource.forwardCodeList = []
    })
    // 备案号
    me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
      me.cmbDataSource.emsNoList = res.data.data.map(item => {
        return {
          label: item.VALUE,
          value: item.VALUE
        }
      })
    }).catch(() => {
      me.cmbDataSource.emsNoList = []
    })
    // 报关行
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.cmbDataSource.cutData = ArrayToLocaleLowerCase(res.data.data)
      let valueCodeObj = {}
      for (let item of res.data.data) {
        valueCodeObj[item.VALUE] = item.CODE
      }
      me.cmbDataSource.cutDataValueCode = valueCodeObj
    }).catch(() => {
      me.cmbDataSource.cutData = []
      me.cmbDataSource.cutDataValueCode = {}
    })
    // 是否【单据内部编号】自动生成
    me.$http.get(csAPI.enterpriseParamsLib.customDocNo.judgeIsExistsRule + '/' + me.moduleName).then(res => {
      me.$set(me, 'isEmsListNoAuto', res.data.data)
    }).catch(() => {
      me.$set(me, 'isEmsListNoAuto', false)
    })

    // 消费使用/生产销售单位代码
    let ieMark = me.moduleName === 'LE' ? 'E' : 'I'
    me.$http.post(csAPI.csBaseInfo.productionUnit.selectList).then(res => {
      me.$set(me.cmbDataSource, 'ownerCode', res.data.data.filter(item => {
        return item.customerType === ieMark
      }).map(it => {
        return {
          code: it.ownerCode,
          label: it.ownerName,
          value: it.ownerCreditCode
        }
      }))
    }).catch(() => {
      me.$set(me.cmbDataSource, 'ownerCode', [])
    })
  },
  mounted: function () {
    let me = this
    me.loadBodySumInfo()
    if (me.editConfig.editStatus === editStatus.ADD) {
      me.$nextTick(() => {
        me.$set(me.headerData, 'ownerCreditCode', me.$store.state.user.socialCreditCode)
        me.onOwnerCodeEnter()
      })
    }
  },
  methods: {
    /**
     * 加载表体汇总信息
     */
    loadBodySumInfo() {
      let me = this
      if (!isNullOrEmpty(me.headerData.sid)) {
        me.$http.post(me.ajaxUrl.getSumListContent + '/' + me.headerData.sid).then(res => {
          me.$set(me.statistics, 'sumQty', res.data.data.middle)
          me.$set(me.statistics, 'sumDecTotal', res.data.data.right)
          me.$set(me.statistics, 'curr', res.data.data.left)
        }).catch(() => {
          me.$set(me.statistics, 'sumQty', null)
          me.$set(me.statistics, 'sumDecTotal', null)
          me.$set(me.statistics, 'curr', '')
        })
      } else {
        me.$set(me.statistics, 'sumQty', null)
        me.$set(me.statistics, 'sumDecTotal', null)
        me.$set(me.statistics, 'curr', '')
      }
    },
    /**
     * 打开选择模板
     */
    openSelectTemplate() {
      let me = this
      me.showSelectTemplate = true
      me.$refs.selTemplate.onSearch()
    },
    /**
     * 选择模板后操作
     * @param templateEntity
     */
    selectTemplateSuccess(templateEntity) {
      let me = this
      templateEntity.emsListNo = me.headerData.emsListNo
      me.isLoadFromTemplate = true
      let entity = me.headerData
      Object.keys(entity).forEach(property => {
        if (templateEntity.hasOwnProperty(property) && !['sid', 'bondMark'].includes(property)) {
          me.$set(entity, property, templateEntity[property])
        }
      })
      me.promiseItemsList = entity.promiseItems ? entity.promiseItems.split(',') : []
      me.headerData = entity
      me.$set(me.headerData, 'receiveCode', me.$store.state.user.company)
      me.$set(me.headerData, 'receiveName', me.$store.state.user.companyName)
      if (isNullOrEmpty(templateEntity['agentCode'])) {
        me.$set(me.headerData, 'agentCode', me.$store.state.user.company)
      }
      if (!isNullOrEmpty(me.configData.emsNo)&&me.frmData.bondMark!='1') {
        theData['emsNo'] = me.configData.emsNo
      }
      me.agentCodeEnter()
      me.$set(me.headerData, 'ownerCreditCode', me.$store.state.user.socialCreditCode)
      me.onOwnerCodeEnter()

      if (!isNullOrEmpty(me.headerData.declareCode)) {
        me.declareCodeEnter()
      }
      setTimeout(() => {
        me.isLoadFromTemplate = false
      }, 500)
    },
    /**
     * 选择模板关闭后操作
     */
    closeSelectTemplateModel() {
      this.showSelectTemplate = false
    },
    /**
     * 保存为模板
     */
    handleSaveTemplate() {
      this.tempConfirmShow = true
    },
    /**
     * 保存确认
     * @param templateName
     */
    afterConfirm(templateName) {
      let me = this
      me.tempConfirmShow = false
      if (templateName && templateName.trim().length > 0) {
        let entity = JSON.parse(JSON.stringify(me.headerData))
        entity.promiseItems = me.promiseItemsList.join(',')
        me.$refs.selTemplate.saveTemplate(entity, templateName, () => {
          me.$Message.success('模板保存成功')
        })
      }
    },
    /**
     * 显示其他包装种类选择弹出框
     */
    onWrapType2Search() {
      let me = this
      me.$set(me, 'wrapType2PopShow', true)
    },
    /**
     * 修改其他包装种类
     * @param wrapType
     */
    doChangeWrapType2(wrapType) {
      let me = this
      me.$set(me.headerData, 'wrapType2', wrapType)
      me.$set(me, 'wrapType2PopShow', false)
    },
    debounce(fn, delay) {
      let me = this
      return function () {
        clearTimeout(me.timer)
        let call = !me.timer
        if (call) {
          fn.call(this)
        }
        me.timer = setTimeout(function () {
          me.timer = false
        }, delay)
      }
    },
    /**
     * 申报单位代码获取名称
     */
    declareCodeEnter() {
      let me = this
      let queryCode = me.headerData.declareCode.trim()
      if (!queryCode || queryCode.length !== 10) {
        me.$set(me.headerData, "declareName", '')
        me.$set(me.headerData, "declareCreditCode", '')
        // me.$set(me.headerData, 'agentCode', '')
      } else {
        me.debounce(me.getDeclarationUnit, 2000)()
        // me.$set(me.headerData, 'agentCode', queryCode)
      }
      // me.agentCodeEnter()
    },
    getDeclarationUnit() {
      let me = this
      let queryCode = me.headerData.declareCode.trim()
      me.pcodeRemote(me.pcode.company, queryCode).then(res => {
        if (Array.isArray(res) && res.length > 0) {
          let theData = res[0]
          me.$set(me.headerData, "declareName", theData.NAME)
          me.$set(me.headerData, "declareCreditCode", theData.CREDIT_CODE)
        } else {
          me.$set(me.headerData, "declareName", '')
          me.$set(me.headerData, "declareCreditCode", '')
        }
      }).catch(() => {
        me.$set(me.headerData, "declareName", '')
        me.$set(me.headerData, "declareCreditCode", '')
      })
    },
    /**
     * 清单申报单位海关十位编码回车事件
     */
    agentCodeEnter() {
      let me = this,
        queryCode = me.headerData.agentCode.trim()
      if (!isNullOrEmpty(queryCode) && queryCode.length === 10) {
        me.pcodeRemote(me.pcode.company, queryCode).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            me.$set(me.headerData, 'agentName', res[0].NAME)
            me.$set(me.headerData, 'agentCreditCode', res[0].CREDIT_CODE)
          } else {
            me.$set(me.headerData, 'agentName', '')
            me.$set(me.headerData, 'agentCreditCode', '')
          }
        }).catch(() => {
          me.$set(me.headerData, 'agentName', '')
          me.$set(me.headerData, 'agentCreditCode', '')
        })
      } else {
        me.$set(me.headerData, 'agentName', '')
        me.$set(me.headerData, 'agentCreditCode', '')
      }
    },
    /**
     * 生产销售单位代码
     */
    onOwnerCodeEnter() {
      let me = this,
        ownerCreditCode = me.headerData.ownerCreditCode
      if (!isNullOrEmpty(ownerCreditCode)) {
        let kvs = me.cmbDataSource.ownerCode.filter(item => {
          return item.value === ownerCreditCode.trim()
        })
        if (Array.isArray(kvs) && kvs.length > 0) {
          me.$set(me.headerData, 'ownerName', kvs[0].label)
        } else {
          me.$set(me.headerData, 'ownerName', '')
        }
      } else {
        me.$set(me.headerData, 'ownerName', '')
      }
    },
    /**
     * 根据社会信用代码获取企业信息
     */
    declareCreditCodeEnter() {
      let me = this
      let declareCreditCode = me.headerData.declareCreditCode
      if (!isNullOrEmpty(declareCreditCode) && declareCreditCode.trim().length === 18) {
        me.pcodeRemote('COMPANY_BY_CREDITCODE', declareCreditCode.trim()).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            me.$set(me.headerData, "declareCode", res[0].CODE)
            me.$set(me.headerData, "declareName", res[0].NAME)
          } else {
            me.$set(me.headerData, "declareCode", '')
            me.$set(me.headerData, "declareName", '')
          }
        })
      } else {
        me.$set(me.headerData, "declareCode", '')
        me.$set(me.headerData, "declareName", '')
      }
    },
    /**
     * GMark控制
     * @param emsNo
     * @param tradeMode
     */
    cutModeGMarkCtrl(emsNo, tradeMode) {
      let me = this,
        ieMark = me.moduleName === 'LI' ? 'I' : 'E'
      let regulatoryRule = me.getRegulatoryRule(emsNo, tradeMode, ieMark)
      if (!isNullOrEmpty(regulatoryRule.cutMode)) {
        me.$set(me.headerData, 'cutMode', regulatoryRule.cutMode)
      }
      if (!me.showDisable) {
        me.$set(me, 'cutModeDisabled', regulatoryRule.cutModeDisable)
      }
    },
    /**
     * 监管方式change
     * @param value
     */
    tradeModeChange(value) {
      let me = this
      if (!me.showDisable) {
        me.$set(me, 'cutModeDisabled', false)
      }
      if (!me.isLoadFromTemplate) {
        me.$set(me.headerData, 'cutMode', '')
      }
      me.cutModeGMarkCtrl(me.headerData.emsNo, value)
    },
    /**
     * 费用输入初始化
     */
    feeInit(value) {
      let me = this
      if (me.firstWatch === true) {
        me.$set(me, 'firstWatch', false)
      } else {
        //清空运费、保费
        if (['1', '2', '3'].includes(value)) {
          if (!me.isLoadEdit) {
            me.$set(me.headerData, 'feeMark', '')
            me.$nextTick(() => {
              me.$set(me.headerData, 'feeRate', null)
              me.$set(me.headerData, 'feeCurr', '')
            })

            me.$set(me.headerData, 'insurMark', '')
            me.$nextTick(() => {
              me.$set(me.headerData, 'insurRate', null)
              me.$set(me.headerData, 'insurCurr', '')
            })
          }
        }
      }

      // 还原控件状态
      me.$set(me, 'feeDisabled', false)
      me.$set(me, 'insurDisabled', false)

      me.rulesHeader.feeMark[0].required = false
      me.rulesHeader.insurMark[0].required = false
    },
    /**
     * 运费变更方法
     * @param feeObj
     */
    onFeeDataChanged(feeObj) {
      this.$set(this.headerData, 'feeMark', feeObj['feeMark'])
      this.$set(this.headerData, 'feeRate', feeObj['feeRate'])
      this.$set(this.headerData, 'feeCurr', feeObj['feeCurr'])
    },
    /**
     * 保费变更方法
     * @param insurObj
     */
    onInsurDataChanged(insurObj) {
      this.$set(this.headerData, 'insurMark', insurObj['insurMark'])
      this.$set(this.headerData, 'insurRate', insurObj['insurRate'])
      this.$set(this.headerData, 'insurCurr', insurObj['insurCurr'])
    },
    /**
     * 杂费变更方法
     * @param otherObj
     */
    onOtherDataChanged(otherObj) {
      this.$set(this.headerData, 'otherMark', otherObj['otherMark'])
      this.$set(this.headerData, 'otherRate', otherObj['otherRate'])
      this.$set(this.headerData, 'otherCurr', otherObj['otherCurr'])
    },
    /**
     * 动态设置费用校验规则
     * @param type
     * @param val
     */
    setFeeRules(type, val) {
      let me = this
      me.$nextTick(() => {
        if (type === 'fee') {            // 运费
          me.rulesHeader.feeRate[0].required = false
          me.rulesHeader.feeCurr[0].required = false
          if (val === '1') {
            me.rulesHeader.feeRate[0].required = true
          } else if (val === '2' || val === '3') {
            me.rulesHeader.feeRate[0].required = true
            me.rulesHeader.feeCurr[0].required = true
          }
        } else if (type === 'insur') {   // 保费
          me.rulesHeader.insurRate[0].required = false
          me.rulesHeader.insurCurr[0].required = false
          if (val === '1') {
            me.rulesHeader.insurRate[0].required = true
          } else if (val === '2' || val === '3') {
            me.rulesHeader.insurRate[0].required = true
            me.rulesHeader.insurCurr[0].required = true
          }
        } else if (type === 'other') {   // 杂费
          me.rulesHeader.otherRate[0].required = false
          me.rulesHeader.otherCurr[0].required = false
          if (val === '1') {
            me.rulesHeader.otherRate[0].required = true
          } else if (val === '2' || val === '3') {
            me.rulesHeader.otherRate[0].required = true
            me.rulesHeader.otherCurr[0].required = true
          }
        }
      })
    },
    /**
     * 数据恢复初始化
     */
    resetFormData() {
      let me = this
      if (me.$refs['headerEditFrom']) {
        me.$refs['headerEditFrom'].resetFields()
      }
      me.headerData = me.getDefaultData()
    },
    /**
     * 境外收货人變更
     */
    overseasShipperChange() {
      let me = this,
        theItems = me.cmbDataSource.overseasShipperList.filter(item => {
          return item.value === me.headerData.overseasShipper
        })
      if (Array.isArray(theItems) && theItems.length > 0) {
        me.$set(me.headerData, 'overseasShipperName', theItems[0].label)
      } else {
        me.$set(me.headerData, 'overseasShipperName', '')
      }
    },
    /**
     * 保存
     */
    goToSave() {
      let me = this
      me.headerData.promiseItems = Array.isArray(me.promiseItemsList) && me.promiseItemsList.length > 0 ? me.promiseItemsList.join(',') : ''
      const data = Object.assign({}, me.headerData)
      if (me.editConfig.editStatus === editStatus.ADD) {
        me.buttons[0].loading = true
        me.$http.post(me.ajaxUrl.insert, data).then(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          me.$Message.success('新增成功!')
        }).catch(() => {
        }).finally(() => {
          me.buttons[0].loading = false
          me.$nextTick(() => {
            me.onTransactionChangeBase(me.headerData.transMode, me.configRequiredField)
          })
        })
      } else if (me.editConfig.editStatus === editStatus.EDIT) {
        me.buttons[0].loading = true
        me.$http.put(`${me.ajaxUrl.update}/${me.headerData.sid}`, data).then(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
          me.$Message.success('修改成功!')
          me.$emit('onAfterHeadSaved')
        }).catch(() => {
        }).finally(() => {
          me.buttons[0].loading = false
          me.$nextTick(() => {
            me.onTransactionChangeBase(me.headerData.transMode, me.configRequiredField)
          })
        })
      }
    },
    /**
     * 保存前數字轉換
     */
    convertNumbers() {
      let me = this
      if (me.headerData.packNum && isNumber(me.headerData.packNum)) {
        me.$set(me.headerData, 'packNum', me.headerData.packNum.toString())
      } else {
        me.$set(me.headerData, 'packNum', '')
      }
      if (me.headerData.netWt && isNumber(me.headerData.netWt)) {
        me.$set(me.headerData, 'netWt', me.headerData.netWt.toString())
      } else {
        me.$set(me.headerData, 'netWt', '')
      }
      if (me.headerData.grossWt && isNumber(me.headerData.grossWt)) {
        me.$set(me.headerData, 'grossWt', me.headerData.grossWt.toString())
      } else {
        me.$set(me.headerData, 'grossWt', '')
      }
    },
    /**
     * 校验分单号重复
     */
    uniqueCheck() {
      let me = this
      if (isNumber(me.headerData.netWt) && isNumber(me.headerData.grossWt)
        && parseFloat(me.headerData.netWt) > me.headerData.grossWt) {
        me.$set(me.headCheckPop, 'errData', ['总毛重小于总净重'])
        me.$set(me.headCheckPop, 'show', true)
      } else {
        me.doSaveContinue()
      }
    },
    /**
     * 继续保存
     */
    doSaveContinue() {
      let me = this
      me.goToSave()
      me.$set(me.headCheckPop, 'show', false)
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      // 对于一个控件绑两个值的情况进行赋值
      me.convertNumbers()
      if (typeof me.headerData.netWt === 'number') {
        me.$set(me.headerData, 'netWt', me.headerData.netWt.toString())
      }

      if (!isNullOrEmpty(me.headerData.agentCodeCustoms)) {
        let theAgents = me.cmbDataSource.cutData.filter(data => {
          return data.value === me.headerData.agentCodeCustoms
        })
        if (Array.isArray(theAgents) && theAgents.length > 0) {
          me.$set(me.headerData, 'agentNameCustoms', theAgents[0].label)
        }
      }

      if (!isNullOrEmpty(me.headerData.ownerCreditCode)) {
        let ownerCreditCode = me.headerData.ownerCreditCode.trim()
        let kvs = me.cmbDataSource.ownerCode.filter(item => {
          return item.value === ownerCreditCode
        })
        if (Array.isArray(kvs) && kvs.length > 0) {
          me.$set(me.headerData, 'ownerCode', kvs[0].code)
        } else {
          me.$set(me.headerData, 'ownerCode', '')
        }
      } else {
        me.$set(me.headerData, 'ownerCode', '')
      }

      me.$refs['headerEditFrom'].validate().then(isValid => {
        if (isValid) {
          me.uniqueCheck()
        }
      })
    },
    /**
     * 调用列表界面方法并将当前编辑界面信息传给列表界面
     * @param showList
     * @param editStatus
     * @param data
     */
    refreshIncomingData(showList, editStatus, data) {
      let me = this
      me.$emit('onEditBack', {
        editData: data,
        showList: showList,
        editStatus: editStatus
      })
    },
    /**
     * 关闭
     */
    handleBack() {
      let me = this
      me.refreshIncomingData(true, editStatus.SHOW, me.getDefaultData())
    },
    /**
     * 根据原值及新值获取实际值【价格说明】
     * @param newValues
     * @param trueVal
     * @param unChecked
     * @returns {*}
     */
    getResultVal(newValues, trueVal, unChecked) {
      let me = this,
        originVal
      if (me.promiseItemsOrgin.indexOf(trueVal) > -1) {
        originVal = trueVal
      } else if (me.promiseItemsOrgin.indexOf(unChecked) > -1) {
        originVal = unChecked
      } else {
        originVal = ''
      }
      let hasTrue = newValues.indexOf(trueVal) > -1
      if (isNullOrEmpty(originVal)) {
        if (hasTrue) {
          return trueVal
        }
        return ''
      } else if (originVal === trueVal) {
        if (hasTrue) {
          return trueVal
        } else {
          return unChecked
        }
      } else {
        if (hasTrue) {
          return ''
        } else {
          return unChecked
        }
      }
    },
    /**
     * 价格说明 值变更
     * @param checked
     */
    promiseItemsChange(checked) {
      let me = this,
        result = []
      let _1or9 = me.getResultVal(checked, '1', '9')
      if (!isNullOrEmpty(_1or9)) {
        result.push(_1or9)
      }
      let _2or8 = me.getResultVal(checked, '2', '8')
      if (!isNullOrEmpty(_2or8)) {
        result.push(_2or8)
      }
      let _3or7 = me.getResultVal(checked, '3', '7')
      if (!isNullOrEmpty(_3or7)) {
        result.push(_3or7)
      }
      let _4or6 = me.getResultVal(checked, '4', '6')
      if (!isNullOrEmpty(_4or6)) {
        result.push(_4or6)
      }
      me.$set(me, 'promiseItemsList', result)
    },
    /**
     * 成交方式变更事件(基础类)
     * @param transMode
     * @param requiredField
     */
    onTransactionChangeBase(transMode, requiredField) {
      // 数据准备
      let me = this
      if (isNullOrEmpty(transMode)) {
        transMode = me.headerData.transMode
      }
      if (isNullOrEmpty(requiredField)) {
        requiredField = me.configRequiredField
      }
      // 获取成交方式与运保费可输入关系
      me.setFeeDisableByTransMode(transMode)
      // 根据配置信息设置运费、保费是否必输(与成交方式一并启用)
      let reqFields = []
      if (!isNullOrEmpty(requiredField)) {
        reqFields = requiredField.split(',')
      }
      for (let field of me.requiredFields) {
        if (reqFields.includes(field.key)) {
          if ('feeMark' === field.key) {
            me.rulesHeader[field.key][0].required = !me.feeDisabled
          } else if ('insurMark' === field.key) {
            me.rulesHeader[field.key][0].required = !me.insurDisabled
          } else {
            if (me.requiredBlueFields.hasOwnProperty(field.key)) {
              me.$set(me.requiredBlueFields, field.key, true)
            }
          }
        } else {
          if (me.requiredBlueFields.hasOwnProperty(field.key)) {
            me.$set(me.requiredBlueFields, field.key, false)
          }
        }
        me.$nextTick(() => {
          if (me.$refs[field.ref]) {
            me.$refs[field.ref].setRules()
          }
        })
      }
    },
    /**
     * 获取成交方式与运保费可输入关系
     * @param transMode
     */
    setFeeDisableByTransMode(transMode) {
      let me = this
      if (me.editConfig && me.editConfig.editStatus === editStatus.SHOW) {
        me.$set(me, 'feeDisabled', true)
        me.$set(me, 'insurDisabled', true)
      } else {
        let ieMark = me.moduleName === 'LI' ? 'I' : 'E'
        let transModeRules = me.getFeeRulesByTransMode(transMode, ieMark)
        me.$set(me, 'feeDisabled', transModeRules.feeDisable)
        me.$set(me, 'insurDisabled', transModeRules.insuranceDisable)
      }
    }
  },
  computed: {
    /**
     * 运费是否只读
     * @returns {boolean|*}
     */
    transportDisabled() {
      let me = this
      if (me.showDisable) {
        return true
      }
      let transMode = me.headerData.transMode,
        ieMark = me.moduleName === 'LI' ? 'I' : 'E',
        transModeRules = me.getFeeRulesByTransMode(transMode, ieMark)
      return transModeRules.feeDisable
    },
    /**
     * 保费是否只读
     * @returns {boolean|*}
     */
    insuranceDisabled() {
      let me = this
      if (me.showDisable) {
        return true
      }
      let transMode = me.headerData.transMode,
        ieMark = me.moduleName === 'LI' ? 'I' : 'E',
        transModeRules = me.getFeeRulesByTransMode(transMode, ieMark)
      return transModeRules.insuranceDisable
    },
    emsListNoDisable() {
      if (this.isEmsListNoAuto === true) {
        return false
      }
      return this.isNew
    },
    declareDisable() {
      // if (this.isDeclare === false) {
      //   return true
      // }
      return this.showDisable
    },
    isNew() {
      if (this.editConfig.editStatus === editStatus.SHOW) {
        return false
      }
      if (this.headerData.hasOwnProperty('sid')) {
        if (!isNullOrEmpty(this.headerData.sid)) {
          return false
        }
      }
      return true
    },
    isAutoMerging() {
      return this.headerData.mergeType !== '1'
    },
    feeOptions() {
      return {
        mark: {
          field: 'feeMark',
          value: this.headerData.feeMark
        },
        rate: {
          field: 'feeRate',
          value: this.headerData.feeRate
        },
        curr: {
          field: 'feeCurr',
          value: this.headerData.feeCurr
        }
      }
    },
    insurOptions() {
      return {
        mark: {
          field: 'insurMark',
          value: this.headerData.insurMark
        },
        rate: {
          field: 'insurRate',
          value: this.headerData.insurRate
        },
        curr: {
          field: 'insurCurr',
          value: this.headerData.insurCurr
        }
      }
    },
    otherOptions() {
      return {
        mark: {
          field: 'otherMark',
          value: this.headerData.otherMark
        },
        rate: {
          field: 'otherRate',
          value: this.headerData.otherRate
        },
        curr: {
          field: 'otherCurr',
          value: this.headerData.otherCurr
        }
      }
    },
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    /**
     * 毛重、净重 精度
     * @returns {any}
     */
    precisionsConfig() {
      let me = this,
        config = {
          netWtI: 5,
          netWtE: 5,
          grossWtI: 5,
          grossWtE: 5,

          headVolumeDigitI: 5,      // 体积
          headVolumeDigitE: 5,      // 体积
          headDecTotalDigitI: 4,    // 总价
          headDecTotalDigitE: 4     // 总价
        }
      if (isNumber(me.configData.autoNetWtDigitI)) {
        config.netWtI = Number(me.configData.autoNetWtDigitI)
      }
      if (isNumber(me.configData.autoNetWtDigitE)) {
        config.netWtE = Number(me.configData.autoNetWtDigitE)
      }
      if (isNumber(me.configData.autoGrossWtDigitI)) {
        config.grossWtI = Number(me.configData.autoGrossWtDigitI)
      }
      if (isNumber(me.configData.autoGrossWtDigitE)) {
        config.grossWtE = Number(me.configData.autoGrossWtDigitE)
      }

      if (isNumber(me.configData.headVolumeDigitI)) {
        config.headVolumeDigitI = Number(me.configData.headVolumeDigitI)
      }
      if (isNumber(me.configData.headVolumeDigitE)) {
        config.headVolumeDigitE = Number(me.configData.headVolumeDigitE)
      }
      if (isNumber(me.configData.headDecTotalDigitI)) {
        config.headDecTotalDigitI = Number(me.configData.headDecTotalDigitI)
      }
      if (isNumber(me.configData.headDecTotalDigitE)) {
        config.headDecTotalDigitE = Number(me.configData.headDecTotalDigitE)
      }
      return config
    },
    netWtDigit() {
      let config = this.configData.precisionsConfig
      if (isNumber(config.netWtDigit)) {
        return config.netWtDigit
      }
      return 8
    },
    configRequiredField() {
      if (this.moduleName === 'LI') {
        return this.configData.reFieldI
      } else if (this.moduleName === 'LE') {
        return this.configData.reFieldE
      } else {
        return ''
      }
    },
    /**
     * 根据EmsNo及iEMark过滤后的监管方式
     * @returns {*}
     */
    filterTradeMode() {
      let ieMark = this.moduleName === 'LI' ? 'I' : 'E'
      return this.getTradeModeByEmsNo(this.headerData.emsNo, ieMark)
    },
    /**
     * 用于展示其他包装种类
     * @returns {string}
     */
    wrapType2View() {
      let result = []
      if (!isNullOrEmpty(this.headerData.wrapType2)) {
        let wrapTypes = this.headerData.wrapType2.split(',')
        if (Array.isArray(wrapTypes) && wrapTypes.length > 0) {
          wrapTypes.forEach(wrap => {
            if (!isNullOrEmpty(wrap)) {
              result.push(wrap + ' ' + this.pcodeGet('WRAP', wrap))
            }
          })
        }
      }
      return result.toString()
    },
    /**
     * 当【运输方式】为：(监管仓库，保税区，保税仓库，物流中心，物流园区，保税港区，出口加工区)
     * 且【监管方式】为：0255来料深加工、0654进料深加工、0657-进料余料结转、0258-来料余料结转时 时
     * 【关联备案号】必填， *蓝色星号*
     * @returns {boolean}
     */
    relEmsNoRequiredTips() {
      if (!isNullOrEmpty(this.headerData.emsNo)) {
        if (['0255', '0654', '0657', '0258'].includes(this.headerData.tradeMode)
          || ['1', '7', '8', 'W', 'X', 'Y', 'Z'].includes(this.headerData.trafMode)) {
          return true
        }
      }
      return false
    },
    clearanceDisable() {
      if (this.headerData.dclcusMark === '2') {
        return true
      }
      return this.showDisable
    }
  }
}
