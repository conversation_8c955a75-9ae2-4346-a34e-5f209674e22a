import carCabinetInfoEdit from '../car-cabinet-info-edit'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const carCabinetInfoList = {
  name: 'carCabinetInfoList',
  mixins: [columnRender, listDataProcessing],
  components: {
    carCabinetInfoEdit
  },
  props: {
    headId: {
      type: String,
      required: true
    },
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    fee: {
      type: Boolean,
      default: () => false
    },
    original: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      required: true,
      default: () => false
    }
  },
  data() {
    return {
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      pmsLevel: 'carCabinet',
      cmbSource: {
        cabinetTypeData: [],
        cabinetAttr: [{
          value: '1', label: '国内'
        }, {
          value: '2', label: '国际'
        }]
      },
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  watch: {
    disabled: {
      immediate: true,
      handler: function (disabled) {
        let me = this
        me.$nextTick(() => {
          me.$set(me.listConfig, 'operationColumnShow', !disabled)
          me.actionLoaded()
          me.loadListConfig()
        })
      }
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions = []
      if (!me.disabled) {
        me.actions = [{
          ...me.actionsComm,
          label: '新增',
          command: 'add',
          icon: 'ios-add',
          key: 'xdo-btn-add'
        }, {
          ...me.actionsComm,
          label: '编辑',
          command: 'edit',
          key: 'xdo-btn-edit',
          icon: 'ios-create-outline'
        }, {
          ...me.actionsComm,
          label: '删除',
          command: 'delete',
          key: 'xdo-btn-delete',
          icon: 'ios-trash-outline'
        }]
      }
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this
      return {
        headId: me.headId,
        ieMark: me.ieMark
      }
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      let me = this
      return [{
        width: 136,
        title: '国内/国际',
        key: 'cabinetAttr',
        cellRendererFramework: me.baseCellRenderer(function (h, params) {
          return me.cmbShowRender(h, params, me.cmbSource.cabinetAttr)
        })
      }, {
        width: 136,
        title: '车柜类型',
        key: 'cabinetType'
      }, {
        flex: 1,
        key: 'cabinetName',
        title: '车柜类型名称'
      }, {
        width: 136,
        title: '车柜数量',
        key: 'cabinetAmount'
      }]
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      me.$emit('setCurrData', me.listConfig.data)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
