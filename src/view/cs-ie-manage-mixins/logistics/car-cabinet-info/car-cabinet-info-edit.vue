<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { isNumber, isNullOrEmpty } from '@/libs/util'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'

  export default {
    name: 'carCabinetInfoEdit',
    mixins: [baseDetailConfig],
    props: {
      headId: {
        type: String,
        required: true
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      fee: {
        type: Boolean,
        default: () => false
      }
    },
    data() {
      return {
        ajaxUrl: {
          insert: '',
          update: ''
        },
        cmbSource: {
          cabinetType: [],
          cabinetAttr: [{
            value: '1', label: '国内'
          }, {
            value: '2', label: '国际'
          }]
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function () {
          let me = this
          if (me.showDisable) {
            me.buttons.forEach(button => {
              button.needed = ['cancel'].includes(button.key)
            })
          } else {
            me.buttons.forEach(button => {
              button.needed = ['save', 'cancel'].includes(button.key)
            })
          }
        }
      },
      fee: {
        immediate: true,
        handler: function (fee) {
          let me = this
          if (fee) {
            me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.carCabinetInfo.insertFee)
            me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.carCabinetInfo.updateFee)
          } else {
            me.$set(me.ajaxUrl, 'insert', csAPI.csImportExport.carCabinetInfo.insert)
            me.$set(me.ajaxUrl, 'update', csAPI.csImportExport.carCabinetInfo.update)
          }
        }
      }
    },
    methods: {
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded() {
        let me = this
        me.$set(me.detailConfig, 'model', me.editConfig.editData)
      },
      /**
       * 展示字段
       * @returns {({title: string, required: boolean, key: string}|{title: string, required: boolean, key: string}|{type: string, title: string, required: boolean, key: string}|{type: string, title: string, key: string, props: {intDigits: number, precision: number}})[]}
       */
      getFields() {
        let me = this
        return [{
          type: 'select',
          required: true,
          title: '国内/国际',
          key: 'cabinetAttr',
          on: {
            change: me.onCabinetAttrChange
          }
        }, {
          type: 'select',
          required: true,
          title: '车柜类型',
          key: 'cabinetType',
          on: {
            change: me.onCabinetTypeChange
          }
        }, {
          props: {
            disabled: true
          },
          required: true,
          key: 'cabinetName',
          title: '车柜类型名称'
        }, {
          props: {
            intDigits: 5,
            precision: 0
          },
          required: true,
          type: 'xdoInput',
          title: '车柜数量',
          key: 'cabinetAmount'
        }]
      },
      onCabinetAttrChange() {
        let me = this,
          realSource = [],
          type = me.detailConfig.model['cabinetAttr']
        if (['1', '2'].includes(type)) {
          realSource = me.inSource.cabinetTypeData.filter(item => {
            return item.type === type
          })
        }
        me.$set(me.cmbSource, 'cabinetType', realSource)
        let field = me.detailConfig.fields.find(p => p.key === 'cabinetType')
        if (field) {
          me.fieldOptimization(field)
        }
      },
      onCabinetTypeChange() {
        let me = this,
          currTitle = '',
          currValue = me.detailConfig.model['cabinetType']
        if (!isNullOrEmpty(currValue)) {
          let item = me.inSource.cabinetTypeData.find(p => p.value === currValue)
          if (item) {
            currTitle = item.label
          }
        }
        me.$set(me.detailConfig.model, 'cabinetName', currTitle)
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'headId', me.headId)
        me.$set(me.detailConfig.model, 'ieMark', me.ieMark)
        me.onCabinetTypeChange()
        if (isNumber(me.detailConfig.model['cabinetAmount'])) {
          me.$set(me.detailConfig.model, 'cabinetAmount', Number(me.detailConfig.model['cabinetAmount']))
        } else {
          me.$set(me.detailConfig.model, 'cabinetAmount', null)
        }
        let formData = deepClone(Object.assign({}, me.detailConfig.model))
        me.$refs['frmData'].validate().then(isValid => {
          if (isValid) {
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.setBtnSaveLoading('save', true)
              me.$http.post(me.ajaxUrl.insert, formData).then(res => {
                me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
                me.$Message.success(me.successMsg.insert + '!')
              }).catch(() => {
              }).finally(() => {
                me.setBtnSaveLoading('save', false)
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              me.setBtnSaveLoading('save', true)
              me.$http.put(me.ajaxUrl.update + '/' + formData.sid, formData).then(res => {
                me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
                me.$Message.success(me.successMsg.update + '!')
              }).catch(() => {
              }).finally(() => {
                me.setBtnSaveLoading('save', false)
              })
            }
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
