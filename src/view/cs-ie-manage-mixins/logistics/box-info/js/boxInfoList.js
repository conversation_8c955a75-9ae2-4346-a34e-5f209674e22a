import boxInfoEdit from '../box-info-edit'
import { listDataProcessing } from '@/mixin/generic/normal/listDataProcessing'
import { columnRender } from '@/view/cs-interim-verification/comm/columnRender'

export const boxInfoList = {
  name: 'boxInfoList',
  mixins: [columnRender, listDataProcessing],
  components: {
    boxInfoEdit
  },
  props: {
    headId: {
      type: String,
      required: true
    },
    ieMark: {
      type: String,
      required: true,
      validate: function (value) {
        return ['I', 'E'].includes(value)
      }
    },
    disabled: {
      type: Boolean,
      required: true,
      default: () => false
    }
  },
  data() {
    return {
      actionsComm: {
        needed: true,
        loading: false,
        disabled: false
      },
      pmsLevel: 'boxInfo',
      toolbarEventMap: {
        'add': this.handleAdd,
        'edit': this.handleEdit,
        'delete': this.handleDelete,
        'setting': this.handleTableColumnSetup
      }
    }
  },
  watch: {
    disabled: {
      immediate: true,
      handler: function (disabled) {
        let me = this
        me.$nextTick(() => {
          me.$set(me.listConfig, 'operationColumnShow', !disabled)
          me.actionLoaded()
          me.loadListConfig()
        })
      }
    }
  },
  methods: {
    actionLoaded() {
      let me = this
      me.actions = []
      if (!me.disabled) {
        me.actions = [{
          ...me.actionsComm,
          label: '新增',
          command: 'add',
          icon: 'ios-add',
          key: 'xdo-btn-add'
        }, {
          ...me.actionsComm,
          label: '编辑',
          command: 'edit',
          key: 'xdo-btn-edit',
          icon: 'ios-create-outline'
        }, {
          ...me.actionsComm,
          label: '删除',
          command: 'delete',
          key: 'xdo-btn-delete',
          icon: 'ios-trash-outline'
        }]
      }
    },
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let me = this
      return {
        headId: me.headId,
        ieMark: me.ieMark
      }
    },
    /**
     * 列表字段
     * @returns {*[]}
     */
    getFields() {
      return [{
        width: 136,
        title: '长(cm)',
        key: 'boxLength'
      }, {
        width: 136,
        title: '宽(cm)',
        key: 'boxWidth'
      }, {
        width: 136,
        title: '高(cm)',
        key: 'boxHeight'
      }, {
        width: 136,
        title: '件数',
        key: 'packNum'
      }, {
        width: 136,
        title: '体积重(kg)',
        key: 'volumeWeight'
      }]
    },
    /**
     * 查询成功后执行的操作
     */
    afterSearchSuccess() {
      let me = this
      me.$emit('setCurrData', me.listConfig.data)
    },
    /**
     * 设置可配置列字段
     */
    setConfigColumns() {
      let me = this
      me.$set(me.listConfig, 'settingColumns', me.getFields())
    },
    /**
     * 弹出自定义列设置界面
     */
    handleTableColumnSetup() {
      let me = this
      me.$set(me, 'listSetupShow', true)
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this
      me.doDelete(me.ajaxUrl.deleteUrl, 'delete')
    }
  }
}
