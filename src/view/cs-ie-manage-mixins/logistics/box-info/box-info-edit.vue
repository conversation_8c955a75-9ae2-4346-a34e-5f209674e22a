<template>
  <section>
    <div style="padding: 2px; width: 100%; background-color: white;">
      <DynamicForm ref="frmData" :disabled="showDisable" labelWidth="150"
                   :model="detailConfig.model" :rules="detailConfig.rules" :fields="detailConfig.fields">
      </DynamicForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { editStatus } from '@/view/cs-common'
  import { baseDetailConfig } from '@/mixin/generic/baseDetailConfig'
  import { deepClone } from '@/components/dynamic/jsx/utils/element-creater'
  import { isNumber, keepDecimal } from '@/libs/util'

  export default {
    name: 'boxInfoEdit',
    mixins: [baseDetailConfig],
    props: {
      headId: {
        type: String,
        required: true
      },
      ieMark: {
        type: String,
        required: true,
        validate: function(value) {
          return ['I', 'E'].includes(value)
        }
      },
      bulkWeightScale:{
        type:Number
      }
    },
    data() {
      return {
        ajaxUrl: {
          insert: csAPI.csImportExport.logisticsBoxInfo.insert,
          update: csAPI.csImportExport.logisticsBoxInfo.update
        }
      }
    },
    watch: {
      'editConfig.editStatus': {
        immediate: true,
        handler: function() {
          let me = this
          if (me.showDisable) {
            me.buttons.forEach(button => {
              button.needed = ['cancel'].includes(button.key)
            })
          } else {
            me.buttons.forEach(button => {
              button.needed = ['save', 'cancel'].includes(button.key)
            })
          }
        }
      }
    },
    methods: {
      /**
       * 数据加载完成后执行
       */
      afterModelLoaded() {
        let me = this
        me.$set(me.detailConfig, 'model', me.editConfig.editData)
      },
      /**
       * 展示字段
       * @returns {({title: string, required: boolean, key: string}|{title: string, required: boolean, key: string}|{type: string, title: string, required: boolean, key: string}|{type: string, title: string, key: string, props: {intDigits: number, precision: number}})[]}
       */
      getFields() {
        let me = this
        return [{
          key: 'cartonSize',
          title: '包装尺寸(cm)',                  // 每箱尺寸(长*宽*高) cm
          type: 'group_form_line',
          required:true,
          fields: [{
            title: '',
            key: 'boxLength',
            required:true,
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5
            },
            type: 'xdoInput',
            on: {
              enter: function() {
                me.contSizeCalculation()
              }
            }
          }, {
            title: '',
            key: 'boxWidth',
            required:true,
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5
            },
            type: 'xdoInput',
            on: {
              enter: function() {
                me.contSizeCalculation()
              }
            }
          }, {
            title: '',
            key: 'boxHeight',
            required:true,
            labelWidth: 0,
            props: {
              intDigits: 6,
              precision: 5
            },
            type: 'xdoInput',
            on: {
              enter: function() {
                me.contSizeCalculation()
              }
            }
          }]
        }, {
          type: 'xdoInput',
          required: true,
          title: '件数',
          key: 'packNum',
          on: {
            enter: function() {
              me.contSizeCalculation()
            }
          }
        }, {
          type: 'xdoInput',
          required: true,
          title: '体积重',
          key: 'volumeWeight',
          props: {
            intDigits: 8,
            precision: 5
          },
        }]
      },
      contSizeCalculation() {
        let me = this,
          boxLength = me.detailConfig.model['boxLength'],
          boxWidth = me.detailConfig.model['boxWidth'],
          boxHeight = me.detailConfig.model['boxHeight'],
          packNum = me.detailConfig.model['packNum']
        if (isNumber(boxLength) && isNumber(boxWidth) && isNumber(boxHeight) && isNumber(packNum)) {
          if (me.ieMark === 'I') {
            me.$set(me.detailConfig.model, 'volumeWeight', keepDecimal(boxLength * boxWidth * boxHeight * packNum / 5000, 5))
          } else if (me.ieMark === 'E' && isNumber(me.bulkWeightScale)) {
            me.$set(me.detailConfig.model, 'volumeWeight', keepDecimal(boxLength * boxWidth * boxHeight * packNum * me.bulkWeightScale / 5000, 5))
          }
        } else {
          me.$set(me.detailConfig.model, 'volumeWeight', '')
        }
      },
      /**
       * 数据保存
       */
      handleSave() {
        let me = this
        me.$set(me.detailConfig.model, 'headId', me.headId)
        me.$set(me.detailConfig.model, 'ieMark', me.ieMark)
        let formData = deepClone(Object.assign({}, me.detailConfig.model))
        me.$refs['frmData'].validate().then(isValid => {
          if (isValid) {
            if (me.editConfig.editStatus === editStatus.ADD) {
              me.setBtnSaveLoading('save', true)
              me.$http.post(me.ajaxUrl.insert, formData).then(res => {
                me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
                me.$Message.success(me.successMsg.insert + '!')
              }).catch(() => {
              }).finally(() => {
                me.setBtnSaveLoading('save', false)
              })
            } else if (me.editConfig.editStatus === editStatus.EDIT) {
              me.setBtnSaveLoading('save', true)
              me.$http.put(me.ajaxUrl.update + '/' + formData.sid, formData).then(res => {
                me.refreshIncomingData(true, editStatus.SHOW, res.data.data)
                me.$Message.success(me.successMsg.update + '!')
              }).catch(() => {
              }).finally(() => {
                me.setBtnSaveLoading('save', false)
              })
            }
          }
        })
      }
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-card-head {
    padding: 5px 10px !important;
  }

  .dc-form {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
