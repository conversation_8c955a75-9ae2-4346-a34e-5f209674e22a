<template>
  <section>
    <Collapse simple v-model="collapseName">
      <Panel name="carCabinet" :class="panelClass">
        装箱信息
        <div slot="content" style="border-top: 1px solid #e8eaec; padding: 0;">
          <div v-show="showList">
            <div v-if="!disabled" class="action" ref="area_actions">
              <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
            </div>
            <XdoCard :bordered="false">
              <xdo-ag-grid class="dc-table" ref="table" v-if="grdShow"
                           :checkboxSelection="listConfig.operationColumnShow" height="132"
                           :rowSelection="rowSelection" :columns="listConfig.columns" :data="listConfig.data"
                           :components="components"
                           :overlayLoadingTemplate="overlayLoadingTemplate"
                           :overlayNoRowsTemplate="overlayNoRowsTemplate"
                           @selectionChanged="handleSelectionChange"></xdo-ag-grid>
              <div ref="area_page">
                <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                         :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                         @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
              </div>
            </XdoCard>
          </div>
          <boxInfoEdit v-if="!showList" :edit-config="editConfig" :ie-mark="ieMark" :head-id="headId"
                       @onEditBack="editBack" :bulkWeightScale="bulkWeightScale"></boxInfoEdit>
        </div>
      </Panel>
    </Collapse>
    <TableColumnSetup v-model="listSetupShow" :resId="listId" :columns="listConfig.settingColumns"
                      @updateColumns="handleUpdateColumn" class="height:500px"></TableColumnSetup>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { boxInfoList } from './js/boxInfoList'

  export default {
    name: 'boxInfoList',
    mixins: [boxInfoList],
    data() {
      return {
        initSearch: false,
        collapseName: [''],
        ajaxUrl: {
          deleteUrl: csAPI.csImportExport.logisticsBoxInfo.delete,
          selectAllPaged: csAPI.csImportExport.logisticsBoxInfo.selectAllPaged,
          loadConfigUrl: csAPI.earlyWarning.manager.clearanceBusiness.loadConfigUrl
        },
        listConfig: {
          exportTitle: '装箱信息'
        },
        bulkWeightScale: null
      }
    },
    computed: {
      panelClass() {
        let me = this
        if (me.fee) {
          return ''
        }
        return 'form-title-wrapper'
      }
    },
    created: function() {
      let me = this
      me.handleSearchSubmit()
      // if (me.ieMark === 'E') {
        me.$http.post(me.ajaxUrl.loadConfigUrl).then(res => {
          if (Array.isArray(res.data.data) && res.data.data.length > 0) {
            me.$set(me, 'bulkWeightScale', res.data.data[0].bulkWeightScale)
          }
        }).catch(() => {
        })
        me.$nextTick(() => {
          if (!me.fee) {
            me.$set(me, 'collapseName', ['carCabinet'])
          }
          me.apiReset()
        })
      }
    // }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  /deep/ .ag-header-cell-label {
    justify-content: center;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  /deep/ .form-title-wrapper > .ivu-collapse-header {
    color: #17233d;
    font-size: 14px;
    font-weight: 700;
    background-color: #dcdee2 !important;
  }

  /deep/ .form-title-wrapper > .ivu-collapse-content {
    padding: 2px;
  }

  /deep/ .ivu-collapse-content > .ivu-collapse-content-box {
    padding: 2px 0;
  }
</style>
