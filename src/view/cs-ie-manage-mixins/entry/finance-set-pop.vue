<template>
  <XdoModal :width="modalWidth" mask :title="modalTitle" v-model="show"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleCancel">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header">
      <XdoForm ref="frmTemplate" class="dc-form dc-form-2" :model="frmData" :rules="templateRules"
               label-position="right" :label-width="120" inline>
        <XdoFormItem v-if="showFinanceNo" prop="financeNo" :label="fieldTitle" class="dc-merge-1-3">
          <XdoIInput type="text" v-model="frmData.financeNo" clearable :maxlength="255"></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </div>
    <div class="action" style="text-align: right;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.label">{{item.label}}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'financeSetPop',
    props: {
      show: {
        type: Boolean,
        require: true
      },
      showFinanceNo: {
        type: Boolean,
        default: false
      },
      sourceData: {
        type: String,
        default: ('')
      },
      ieMark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      let commBtn = {
        needed: true,
        loading: false,
        disabled: false
      }
      return {
        frmData: {
          financeNo: ''
        },
        templateRules: {},
        buttons: [{
          ...commBtn, label: '取消', type: 'default', icon: 'dc-btn-cancel', click: this.handleCancel
        }, {
          ...commBtn, label: '确定', type: 'primary', icon: 'dc-btn-confirm', click: this.handleConfirm
        }]
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          let me = this
          me.$set(me.frmData, 'financeNo', '')
          if (show) {
            if (!isNullOrEmpty(me.sourceData)) {
              me.$set(me.frmData, 'financeNo', me.sourceData.trim())
            }
          }
        }
      }
    },
    computed: {
      modalWidth() {
        let me = this
        if (me.showFinanceNo) {
          return 580
        }
        return 268
      },
      modalTitle() {
        let me = this
        if (me.showFinanceNo) {
          return '设置财务单据号'
        }
        return '财务取消'
      },
      fieldTitle() {
        let me = this
        if (me.ieMark === 'I') {
          return '财务单据号'
        }
        return '增值税普通发票号'
      }
    },
    methods: {
      handleCancel() {
        let me = this
        me.$emit('doFinance', '', false)
        me.$set(me.frmData, 'financeNo', '')
      },
      handleConfirm() {
        let me = this
        if (me.showFinanceNo) {
          me.$refs['frmTemplate'].validate().then(isValid => {
            if (isValid) {
              me.$emit('doFinance', me.frmData.financeNo, true)
              me.$set(me.frmData, 'financeNo', '')
            }
          })
        } else {
          me.$emit('doFinance', '', true)
          me.$set(me.frmData, 'financeNo', '')
        }
      }
    }
  }
</script>

<style lang="less">
  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }
</style>
