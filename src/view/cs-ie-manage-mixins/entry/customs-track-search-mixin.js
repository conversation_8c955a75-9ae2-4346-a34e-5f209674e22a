import { importExportManage } from '@/view/cs-common'

export default {
  data() {
    return {
      formResId: '',
      schema: {
        titleWidth: 100
      },
      showElements: [],
      searchParam: {
        goodsStatus: '',
        entryNo: '',
        emsListNoTo: '',
        tradeMode: '',
        trafMode: '',
        passDateFrom: '',
        passDateTo: '',
        beEntrustPerson: '',
        declareDateFrom: '',
        declareDateTo: '',
        declarePort: '',
        declarePerson: '',
        passFlag: '',
        deliveryFlag: '',
        checkFlag: '',
        dutyFlag: ''
      },
      elements: [{
        type: "select", key: "goodsStatus", title: '状态',
        props: {
          multiple: true,
          options: importExportManage.entryTrackStateMap
        }
      }, {
        type: "input", key: "entryNo", title: '报关单号'
      }, {
        type: "input", key: "emsListNo", title: '清单内部编号'
      }, {
        type: "pcode", key: "tradeMode", title: '监管方式',
        props: {
          meta: 'TRADE'
        }
      }, {
        type: "pcode", key: "trafMode", title: '运输方式',
        props: {
          meta: 'TRANSF'
        }
      }, {
        type: 'dateRange', key: 'passDate', title: '放行日期',
        fields: [{key: 'passDateFrom'}, {key: 'passDateTo'}]
      }, {
        type: "input", key: "beEntrustPerson", title: '受托人'
      }, {
        type: 'dateRange', key: 'declareDate', title: '放行日期',
        fields: [{key: 'declareDateFrom'}, {key: 'declareDateTo'}]
      }, {
        type: "pcode", key: "declarePort", title: '申报口岸',
        props: {
          meta: 'CUSTOMS_REL'
        }
      }, {
        type: "input", key: "declarePerson", title: '申报联系人'
      }, {
        type: "select", key: "passFlag", title: '是否放行',
        props: {
          options: importExportManage.isPassMap
        }
      }, {
        type: "select", key: "deliveryFlag", title: '是否送货',
        props: {
          options: importExportManage.isDeliveryMap
        }
      }, {
        type: "select", key: "checkFlag", title: '是否查验',
        props: {
          options: importExportManage.isCheckMap
        }
      }, {
        type: "select", key: "dutyFlag", title: '是否完税',
        props: {
          options: importExportManage.isDutyMap
        }
      }]
    }
  },
  mounted: function () {
    this.formResId = this.$route.fullPath
  }
}
