import pms from '@/libs/pms'
import { csAPI } from '@/api'
import DcImport from '@/components/dc-import'
import { ArrayToLocaleLowerCase } from '@/libs/util'
import { editStatus, importExportManage } from '@/view/cs-common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import financeSetPop from '@/view/cs-ie-manage-mixins/entry/finance-set-pop'
import CustomsTrackSearch from '@/view/cs-ie-manage-mixins/entry/customs-track-search'
import customsSubmitVerifyPop from '@/view/cs-ie-manage-mixins/entry/customs-submit-verify-pop'

export const customsTrackList = {
  components: {
    DcImport,
    financeSetPop,
    CustomsTrackSearch,
    customsSubmitVerifyPop
  },
  mixins: [pms, commList],
  data() {
    return {
      actions: [],
      // 查询条件行数
      searchLines: 7,
      showFormSetup: false,
      editConfig: {
        editData: {
          headId: '',
          emsListNo: '',
          selectedEntrys: []
        }
      },
      batchUpdateShow: false,
      financeConfig: {
        source: '',
        show: false,
        financeConfirm: ''
      },
      toolbarEventMap: {
        'edit': this.handleEdit,
        'export': this.handleDownload,
        'finance-affirm': this.handleFinanceAffirm,     // 财务确认
        'finance-recall': this.handleFinanceRecall,     // 采购取消
        'purchase-affirm': this.handlePurchaseAffirm,   // 采购确认
        'purchase-recall': this.handlePurchaseRecall    // 采购取消
      },
      cmbDataSource: {
        cutData: [],
        dutyTypeList: importExportManage.dutyTypeMap,
        complete: importExportManage.COMPLETE_SCHEDULE_MAP
      },
      submitVerifyConfig: {
        data: [],
        show: false,
        financeNo: '',
        loadingKey: '',
        selectedSids: [],
        confirmValue: ''
      }
    }
  },
  created: function () {
    let me = this
    // 报关行
    me.$http.post(csAPI.ieParams.CUT).then(res => {
      me.cmbDataSource.cutData = ArrayToLocaleLowerCase(res.data.data)
    }).catch(() => {
      me.cmbDataSource.cutData = []
    })
  },
  mounted() {
    let me = this
    me.loadFunctions().then(() => {
      if (typeof me.loadExtendActions === 'function') {
        me.loadExtendActions()
      }
    })
  },
  methods: {
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      let params = Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}))
      if (Array.isArray(params.goodsStatus)) {
        params.goodsStatus = params.goodsStatus.toString()
      }
      return params
    },
    /**
     * 点击编辑按钮执行
     */
    handleEdit() {
      let me = this
      if (me.checkRowSelected('编辑')) {
        me.handleEditByRow(me.gridConfig.selectRows)
      }
    },
    /**
     * 列表中点击数据编辑
     * @param selRows
     */
    handleEditByRow(selRows) {
      let me = this
      if ((Array.isArray(selRows) && selRows.length === 1) || !Array.isArray(selRows)) {
        let theRow
        if (Array.isArray(selRows)) {
          theRow = selRows[0]
        } else {
          theRow = selRows
        }
        me.editConfig.editStatus = editStatus.EDIT
        me.editConfig.editData.headId = theRow.sid
        me.editConfig.editData.emsListNo = theRow.emsListNo
        me.editConfig.editData.selectedEntrys = [theRow]
        me.showList = false
      } else {
        let selectedEntryAry = []
        let existsEmsListNoArr = []
        for (let item of selRows) {
          selectedEntryAry.push(item)
          if (item.status === '1') {
            existsEmsListNoArr.push(item.emsListNo)
          }
        }
        if (selectedEntryAry.length > 1 && existsEmsListNoArr.length > 0) {
          me.$Modal.confirm({
            title: '提示',
            content: '清单编号为：【' + existsEmsListNoArr.join(',') + '】 的报关追踪记录已有数据,是否覆盖?',
            onOk() {
              me.showList = false
              me.editConfig.editData.headId = ''
              me.editConfig.editData.emsListNo = ''
              me.editConfig.editStatus = editStatus.EDIT
              me.editConfig.editData.selectedEntrys = selectedEntryAry
            }
          })
        } else {
          me.showList = false
          me.editConfig.editData.headId = ''
          me.editConfig.editData.emsListNo = ''
          me.editConfig.editStatus = editStatus.EDIT
          me.editConfig.editData.selectedEntrys = selectedEntryAry
        }
      }
    },
    /**
     * 列表中点击数据展示
     * @param row
     */
    handleViewByRow(row) {
      let me = this
      me.editConfig.editStatus = editStatus.SHOW
      me.editConfig.editData.headId = row.sid
      me.editConfig.editData.emsListNo = row.emsListNo
      me.editConfig.editData.selectedEntrys = [row]
      me.showList = false
    },
    handleDownload() {
      let me = this
      // me.beforeDownload('0')
      me.doExport(me.ajaxUrl.exportUrl + '/0', me.actions.findIndex(it => it.command === 'export'))
    },
    handleShowSet() {
      let me = this
      me.showFormSetup = true
    },
    /**
     * 下载修改数据
     */
    downLoadModify() {
      let me = this
      me.beforeDownload('1')
      me.doExport(me.ajaxUrl.exportUrl + '/1', me.actions.findIndex(it => it.command === 'batchUpdate'))
    },
    /**
     * 上传批量修改数据
     */
    uploadModify() {
      let me = this
      me.$set(me, 'batchUpdateShow', true)
    },
    afterImport() {
      let me = this
      me.$set(me, 'batchUpdateShow', false)
      me.getList()
    },
    /**
     * 设置报关达成
     * @param type
     */
    doSetSchedule(type) {
      let me = this
      if (me.checkRowSelected('修改完成进度')) {
        me.$Modal.confirm({
          title: '提醒',
          okText: '确定',
          cancelText: '取消',
          content: '确认要为所选项设置报关达成吗?',
          onOk: () => {
            let params = me.getSelectedParams()
            me.$http.post(me.ajaxUrl.setCompleteSchedule + '/' + params + '/' + type).then(() => {
              me.$Message.success('设置成功!')
              me.getList()
            }).catch(() => {
            })
          }
        })
      }
    },
    /**
     * 执行采购确认
     * @param selectedSids
     * @param purchaseConfirm
     * @param btnKey
     */
    doPurchase(selectedSids, purchaseConfirm, btnKey) {
      let me = this
      me.$http.post(me.ajaxUrl.purchase + '/' + selectedSids, {
        purchaseConfirm: purchaseConfirm
      }).then(() => {
        if (purchaseConfirm === '0') {
          me.$Message.success('采购取消成功!')
        } else {
          me.$Message.success('采购确认成功!')
        }
        me.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading(btnKey)
      })
    },
    /**
     * 采购确认
     */
    handlePurchaseAffirm() {
      let me = this,
        confirmCheckUrl = me.ajaxUrl.confirmCheck
      if (me.checkRowSelected('采购确认')) {
        let selectedSids = me.getSelectedParams()
        confirmCheckUrl += '/' + selectedSids
        if (me.iEMark === 'I') {
          confirmCheckUrl += '/3'
        }
        me.setToolbarLoading('purchase-affirm', true)
        me.$http.post(confirmCheckUrl, {
          purchaseConfirm: '1'
        }, {
          noIntercept: true
        }).then(res => {
          if (res.data.success === true) {
            me.doPurchase(selectedSids, '1', 'purchase-affirm')
          } else {
            me.$set(me.submitVerifyConfig, 'confirmValue', '1')
            me.$set(me.submitVerifyConfig, 'data', res.data.data)
            me.$set(me.submitVerifyConfig, 'selectedSids', selectedSids)
            me.$set(me.submitVerifyConfig, 'loadingKey', 'purchase-affirm')
            me.$set(me.submitVerifyConfig, 'show', true)
          }
        }).catch(() => {
          me.setToolbarLoading('purchase-affirm')
        })
      }
    },
    /**
     * 采购取消
     */
    handlePurchaseRecall() {
      let me = this//,
      // confirmCheckUrl = me.ajaxUrl.confirmCheck
      if (me.checkRowSelected('采购取消')) {
        let selectedSids = me.getSelectedParams()
        // confirmCheckUrl += '/' + selectedSids
        // if (me.iEMark === 'I') {
        //   confirmCheckUrl += '/3'
        // }
        me.setToolbarLoading('purchase-recall', true)
        // me.$http.post(confirmCheckUrl, {
        //   purchaseConfirm: '0'
        // }, {
        //   noIntercept: true
        // }).then(res => {
        //   if (res.data.success === true) {
        me.doPurchase(selectedSids, '0', 'purchase-recall')
        //   } else {
        //     me.$set(me.submitVerifyConfig, 'confirmValue', '0')
        //     me.$set(me.submitVerifyConfig, 'data', res.data.data)
        //     me.$set(me.submitVerifyConfig, 'selectedSids', selectedSids)
        //     me.$set(me.submitVerifyConfig, 'loadingKey', 'purchase-recall')
        //     me.$set(me.submitVerifyConfig, 'show', true)
        //   }
        // }).catch(() => {
        //   me.setToolbarLoading('purchase-recall')
        // })
      }
    },
    /**
     * 执行财务确认
     * @param selectedSids
     * @param financeNo
     * @param btnKey
     */
    doFinance(selectedSids, financeNo, btnKey) {
      let me = this,
        message = '',
        params = {
          financeConfirm: me.financeConfig.financeConfirm
        }
      if (me.financeConfig.financeConfirm === '1') {
        message = '确认'
      } else {
        message = '取消'
      }
      if (me.iEMark === 'I') {
        params['financeNo'] = financeNo
      } else {
        params['vatNo'] = financeNo
      }
      me.$http.post(me.ajaxUrl.finance + '/' + selectedSids, params).then(() => {
        me.$Message.success('财务' + message + '成功!')
        me.handleSearchSubmit()
      }).catch(() => {
      }).finally(() => {
        me.setToolbarLoading(btnKey)
      })
    },
    goToFinance(financeNo, flag) {
      let me = this,
        btnKey = 'finance-affirm',
        selectedSids = me.getSelectedParams()
      me.$set(me.financeConfig, 'show', false)
      if (flag) {
        if (me.financeConfig.financeConfirm === '0') {
          btnKey = 'finance-recall'
          me.doFinance(selectedSids, financeNo, btnKey)
          return
        }
        let confirmCheckUrl = me.ajaxUrl.confirmCheck
        confirmCheckUrl += '/' + selectedSids
        if (me.iEMark === 'I') {
          confirmCheckUrl += '/4'
        }
        me.setToolbarLoading(btnKey, true)
        me.$http.post(confirmCheckUrl, {
          financeConfirm: me.financeConfig.financeConfirm
        }, {
          noIntercept: true
        }).then(res => {
          if (res.data.success === true) {
            me.doFinance(selectedSids, financeNo, btnKey)
          } else {
            me.$set(me.submitVerifyConfig, 'loadingKey', btnKey)
            me.$set(me.submitVerifyConfig, 'data', res.data.data)
            me.$set(me.submitVerifyConfig, 'financeNo', financeNo)
            me.$set(me.submitVerifyConfig, 'selectedSids', selectedSids)
            me.$set(me.submitVerifyConfig, 'show', true)
          }
        }).catch(() => {
          me.setToolbarLoading(btnKey)
        })
      }
    },
    /**
     * 是否继续执行原操作
     * @param isContinue
     */
    onSubmitContinue(isContinue) {
      let me = this
      me.$set(me.submitVerifyConfig, 'show', false)
      if (isContinue) {
        if (['finance-affirm', 'finance-recall'].includes(me.submitVerifyConfig.loadingKey)) {  // 财务
          me.doFinance(me.submitVerifyConfig.selectedSids, me.submitVerifyConfig.financeNo, me.submitVerifyConfig.loadingKey)
        } else {  // 采购
          me.doPurchase(me.submitVerifyConfig.selectedSids, me.submitVerifyConfig.confirmValue, me.submitVerifyConfig.loadingKey)
        }
      } else {
        me.setToolbarLoading(me.submitVerifyConfig.loadingKey)
      }
    },
    /**
     * 财务确认
     */
    handleFinanceAffirm() {
      let me = this,
        field = ''
      if (me.checkRowSelected('财务确认')) {
        me.$set(me.financeConfig, 'financeConfirm', '1')
        if (me.iEMark === 'I') {
          field = 'financeNo'
        } else {
          field = 'vatNo'
        }
        let sameRows = me.gridConfig.selectRows.filter(item => item[field] === me.gridConfig.selectRows[0][field])
        if (sameRows.length === me.gridConfig.selectRows.length) {
          me.$set(me.financeConfig, 'source', me.gridConfig.selectRows[0][field])
        } else {
          me.$set(me.financeConfig, 'source', '')
        }
        me.$set(me.financeConfig, 'show', true)
      }
    },
    /**
     * 财务取消
     */
    handleFinanceRecall() {
      let me = this
      if (me.checkRowSelected('财务取消')) {
        me.$set(me.financeConfig, 'financeConfirm', '0')
        me.$set(me.financeConfig, 'source', '')
        me.$set(me.financeConfig, 'show', true)
      }
    }
  },
  computed: {
    aeoShow() {
      return this.editConfig.editStatus === editStatus.SHOW
    },
    updateConfig() {
      let me = this
      return {
        startRow: 5,
        params: {},
        config: {
          tplUrl: '',
          errorUrl: me.ajaxUrl.batchUpdate + '/export',
          url: me.ajaxUrl.batchUpdate + '/import/update',
          correctUrl: me.ajaxUrl.batchUpdate + '/import/correct'
        }
      }
    },
    showFinanceNo() {
      let me = this
      return me.financeConfig.financeConfirm === '1'
    }
  }
}
