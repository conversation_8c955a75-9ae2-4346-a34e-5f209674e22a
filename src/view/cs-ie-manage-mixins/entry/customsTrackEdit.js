import { editStatus, importExportManage } from '@/view/cs-common'
import SimpleAttachment from '@/view/cs-common/components/SimpleAttachment'

export const customsTrackEdit = {
  name: 'customsTrackEdit',
  components: {
    SimpleAttachment
  },
  props: {
    // 提单表头ID的字符串
    headId: {
      type: String,
      default: ''
    },
    // 报关追踪信息
    selectedEntrys: {
      type: Array,
      default: () => ([])
    },
    aeoShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let defaultData = this.getDefaultData()
    return {
      // 报关追踪单表头
      headerData: {
        ...defaultData
      },
      // 当前清单编号
      currentBillNo: '',
      // 是否显示数据
      initData: true,
      // 报关追踪单列表
      customsTrackList: [],
      billNoDisabled: false,
      buttons: [{
        label: '保存',
        loading: false,
        type: 'primary',
        disabled: false,
        icon: 'dc-btn-save',
        needed: !this.aeoShow,
        click: this.handleSave
      }, {
        needed: true,
        label: '返回',
        loading: false,
        type: 'primary',
        disabled: false,
        icon: 'dc-btn-cancel',
        click: this.handleBack
      }],
      rulesHeader: {},
      cmbDataSource: {
        dutyTypeList: importExportManage.dutyTypeMap,
        entryTrackStateList: importExportManage.entryTrackStateMap
      },
      attachTypeWSSD: importExportManage.attachType.报关追踪完税税单,
      attachTypeSJCY: importExportManage.attachType.报关追踪商检查验单,
      attachTypeHGCY: importExportManage.attachType.报关追踪海关查验单
    }
  },
  mounted() {
    this.customsTrackDataLoad()
  },
  methods: {
    customsTrackDataLoad() {
      let me = this
      if (me.selectedEntrys.length === 0) {
        me.initData = true
        me.loadCustomsTrackListData()
      } else {
        me.billNoDisabled = true
        me.customsTrackList = me.selectedEntrys.map(item => {
          return {sid: item.sid, emsListNo: item.emsListNo}
        })
        me.initData = false
        if (me.selectedEntrys.length === 1) {
          me.initData = true
          me.currentBillNo = me.selectedEntrys[0].emsListNo
          me.loadCustomsTrackData(me.selectedEntrys[0].sid)
        }
      }
    },
    /**
     * 获取默认值
     * @returns {{}}
     */
    getDefaultData() {
      return {}
    },
    handleChangeBill(selectedVal) {
      if (selectedVal) {
        let me = this,
          currBill = me.customsTrackList.filter(item => {
            return item.emsListNo === selectedVal
          })
        if (currBill instanceof Array && currBill.length > 0) {
          me.loadCustomsTrackData(currBill[0].sid)
        }
      }
    },
    /**
     * 返回列表界面
     */
    handleBack() {
      let me = this
      me.$emit('onEditBack', {
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    }
  },
  computed: {
    showUpload() {
      return this.initData && this.currentBillNo !== ''
    },
    showDisable() {
      return this.aeoShow
    }
  }
}
