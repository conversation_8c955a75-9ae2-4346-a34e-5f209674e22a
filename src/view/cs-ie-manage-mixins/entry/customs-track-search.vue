<template>
  <!-- 不再使用 -->
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="goodsStatus" label="状态">
        <xdo-select v-model="searchParam.goodsStatus" :options="this.cmbDataSource.statusList" multiple="true"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="emsListNoTo" label="清单内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNoTo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <dc-dateRange label="放行日期" @onDateRangeChanged="handlePassDateChange"></dc-dateRange>
      <XdoFormItem prop="beEntrustPerson" label="受托人">
        <XdoIInput type="text" v-model="searchParam.beEntrustPerson"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="报关单申报日期" @onDateRangeChanged="handleDeclareDateChange"></dc-dateRange>
      <XdoFormItem prop="declarePort" label="申报口岸">
        <xdo-select v-model="searchParam.declarePort" :asyncOptions="pcodeList" :meta="pcode.customs_rel" :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declarePerson" label="申报联系人">
        <XdoIInput type="text" v-model="searchParam.declarePerson"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="passFlag" label="是否放行">
        <xdo-select v-model="searchParam.passFlag" :options="this.cmbDataSource.isPass"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="deliveryFlag" label="是否送货">
        <xdo-select v-model="searchParam.deliveryFlag" :options="this.cmbDataSource.isDelivery"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="checkFlag" label="是否查验">
        <xdo-select v-model="searchParam.checkFlag" :options="this.cmbDataSource.isCheck"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="dutyFlag" label="是否完税" v-if="showDutyFlag">
        <xdo-select v-model="searchParam.dutyFlag" :options="this.cmbDataSource.isDuty"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="declareCodeCustoms" label="报关行">
        <XdoIInput type="text" v-model="searchParam.declareCodeCustoms"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo"></XdoIInput>
      </XdoFormItem>
      <dc-dateRange v-if="showDutyFlag" label="完税日期" @onDateRangeChanged="handleDutyDateChange"></dc-dateRange>
      <dc-dateRange v-if="showDutyFlag" label="税金请款日期" @onDateRangeChanged="handlePaymentDateChange"></dc-dateRange>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange  label="制单日期" @onDateRangeChanged="handleHeadInsertTimeChange"></dc-dateRange>
      <XdoFormItem prop="complete" label="报关达成">
        <xdo-select v-model="searchParam.complete" :options="cmbDataSource.complete"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'I'" prop="purchaseConfirm" label="采购确认">
        <xdo-select v-model="searchParam.purchaseConfirm" :options="cmbDataSource.purchaseConfirm"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="financeConfirm" label="财务确认">
        <xdo-select v-model="searchParam.financeConfirm" :options="cmbDataSource.financeConfirm"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'I'" prop="financeNo" label="财务单据号">
        <XdoIInput type="text" v-model="searchParam.financeNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'E'" prop="vatNo" label="增值税普通发票号">
        <XdoIInput type="text" v-model="searchParam.vatNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo"></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="inspectionNo" label="报检单号">
        <XdoIInput type="text" v-model="searchParam.inspectionNo"></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { ArrayToLocaleLowerCase } from '@/libs/util'
  import { importExportManage } from '@/view/cs-common'

  export default {
    name: 'customsTrackSearch',
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      showSearch: {
        type: Boolean,
        default: () => ({})
      }
    },
    data() {
      return {
        searchParam: {
          goodsStatus: '',
          emsNo: '',
          entryNo: '',
          emsListNoTo: '',
          tradeMode: '',
          trafMode: '',
          passDateFrom: '',
          passDateTo: '',
          beEntrustPerson: '',
          declareDateFrom: '',
          declareDateTo: '',
          declarePort: '',
          declarePerson: '',
          passFlag: '',
          deliveryFlag: '',
          checkFlag: '',
          dutyFlag: '',
          declareCodeCustoms: '',
          contrNo: '',
          dutyDateFrom: '',
          dutyDateTo: '',
          paymentDateFrom: '',
          paymentDateTo: '',
          hawb: '',
          headInsertTime: '',
          complete: '',
          purchaseConfirm: '',
          financeConfirm: '',
          financeNo: '',
          vatNo: '',
          inspectionNo:''
        },
        cmbDataSource: {
          cutData: [],
          emsNoList: [],
          isDuty: importExportManage.isDutyMap,
          isPass: importExportManage.isPassMap,
          isCheck: importExportManage.isCheckMap,
          isDelivery: importExportManage.isDeliveryMap,
          statusList: importExportManage.entryTrackStateMap,
          complete: importExportManage.COMPLETE_SCHEDULE_MAP,
          purchaseConfirm: [{value: '0', label: '否'}, {value: '1', label: '是'}],
          financeConfirm: [{value: '0', label: '否'}, {value: '1', label: '是'}]
        }
      }
    },
    created: function () {
      let me = this
      if (me.iemark === 'I') {
        me.$set(me.cmbDataSource, 'statusList', importExportManage.entryTrackStateMapI)
      } else {
        me.$set(me.cmbDataSource, 'statusList', importExportManage.entryTrackStateMap)
      }
      me.getEmsNo()
    },
    watch: {
      showSearch(newVal) {
        if (newVal) {
          let me = this
          // 报关行
          me.$http.post(csAPI.ieParams.CUT).then(res => {
            me.cmbDataSource.cutData = ArrayToLocaleLowerCase(res.data.data)
          }).catch(() => {
            me.cmbDataSource.cutData = []
          })
        }
      }
    },
    computed: {
      showDutyFlag() {
        return this.iemark === 'I'
      }
    },
    methods: {
      /**
       * 获取备案号
       */
      getEmsNo() {
        let me = this
        me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
          let tmpArr = []
          for (let item of res.data.data) {
            tmpArr.push({
              value: item.VALUE,
              label: item.VALUE
            })
          }
          me.cmbDataSource.emsNoList = tmpArr
        })
      },
      handlePassDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "passDateFrom", values[0])
          this.$set(this.searchParam, "passDateTo", values[1])
        } else {
          this.$set(this.searchParam, "passDateFrom", '')
          this.$set(this.searchParam, "passDateTo", '')
        }
      },
      handleDeclareDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "declareDateFrom", values[0])
          this.$set(this.searchParam, "declareDateTo", values[1])
        } else {
          this.$set(this.searchParam, "declareDateFrom", '')
          this.$set(this.searchParam, "declareDateTo", '')
        }
      },
      /**
       * 完税日期
       * @param values
       */
      handleDutyDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "dutyDateFrom", values[0])
          this.$set(this.searchParam, "dutyDateTo", values[1])
        } else {
          this.$set(this.searchParam, "dutyDateFrom", '')
          this.$set(this.searchParam, "dutyDateTo", '')
        }
      },
      /**
       * 税金请款日期
       * @param values
       */
      handlePaymentDateChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "paymentDateFrom", values[0])
          this.$set(this.searchParam, "paymentDateTo", values[1])
        } else {
          this.$set(this.searchParam, "paymentDateFrom", '')
          this.$set(this.searchParam, "paymentDateTo", '')
        }
      },
      /**
       * 制单日期
       * @param values
       */
      handleHeadInsertTimeChange(values) {
        if (values instanceof Array && values.length === 2) {
          this.$set(this.searchParam, "headInsertTimeFrom", values[0])
          this.$set(this.searchParam, "headInsertTimeTo", values[1])
        } else {
          this.$set(this.searchParam, "headInsertTimeFrom", '')
          this.$set(this.searchParam, "headInsertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
  /deep/ .dc-select-selection {
    height: 24px;
    overflow: hidden;
  }

  /deep/ .dc-select-multiple .dc-select-input {
    height: 22px;
  }
</style>
