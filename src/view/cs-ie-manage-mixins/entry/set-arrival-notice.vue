<template>
  <XdoModal v-model="show" mask width="500" title="选择到货通知人"
            :mask-closable="false" :closable="false" :footer-hide="true">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoForm class="dc-import-form" ref="form" :show-message="false" :model="headerData" :label-width="86">
      <XdoFormItem prop="userNames" label="到货通知人">
        <CheckboxGroup v-model="headerData.userNames" class="dc-form" style="padding-top: 0;">
          <XdoCheckbox :label="item.key" v-bind:key="item.key" v-for="item in userNameArr">{{item.value}}</XdoCheckbox>
        </CheckboxGroup>
      </XdoFormItem>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <XdoButton type="success" icon="ios-cloud-upload" class="dc-import-margin-left" @click="handleConfirm">确定</XdoButton>
      <XdoButton type="error" icon="ios-close" class="dc-import-margin-left" @click="handleClose">关闭</XdoButton>
    </div>
  </XdoModal>
</template>

<script>
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'

  export default {
    name: 'setArrivalNotice',
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    data() {
      return {
        headerData: {
          userNames: []
        },
        userNameArr: []
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (val) {
          if (val) {
            let me = this
            me.$set(me.headerData, 'userNames', [])
          }
        }
      }
    },
    created() {
      let me = this
      me.$http.post(csAPI.earlyWarning.manager.warringEmails.head.getArrivalNoticer).then(res => {
        me.$set(me, 'userNameArr', res.data.data.map(item => {
          return {
            key: item['USER_NAME'],
            value: item['USER_NAME']
          }
        }))
      }).catch(() => {
        me.$set(me, 'userNameArr', [])
      })
    },
    methods: {
      handleConfirm() {
        let me = this
        if (isNullOrEmpty(me.headerData.userNames)) {
          me.$Message.warning('请选择到货通知人')
          return false
        }
        me.$emit('onConfirm', me.headerData.userNames)
        me.handleClose()
      },
      handleClose() {
        let me = this
        me.$emit('update:show', false)
      }
    }
  }
</script>

<style scoped>
  .action button {
    margin-right: 6px;
  }
</style>
