import pms from '@/libs/pms'
import { editStatus, AeoInfoList } from '@/view/cs-common'
import Bill from '@/view/cs-ie-manage-mixins/bill/bill-head'
import Cert from '@/view/cs-ie-manage/dec-erp-cert/dec-erp-cert.vue'
import Attach from '@/view/cs-ie-manage/attached-document/attached-document'
import { auditTrailProcessing } from '../../cs-aeoManage/base/auditTrailProcessing'
import { dynamicTabs } from '../../cs-ie-manage/dec-erp-head/dynamic/js/dynamicTabs'
import packingMaintainList from '@/view/cs-ie-manage/packing-maintain/packing-maintain-list'
import DecAttachedDocumentsList from '../../cs-ie-manage/dec-attached-documents/dec-attached-documents-list'

export const decErpHeadTabs = {
  components: {
    Cert,
    Bill,
    Attach,
    AeoInfoList,
    packingMaintainList,
    DecAttachedDocumentsList
  },
  mixins: [pms, auditTrailProcessing, dynamicTabs],
  props: {
    ieMark: {
      type: String,
      required: true
    },
    editConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabName: 'headTab',
      tabs: {
        headTab: true,
        bodyTab: false,
        certTab: false,
        packingTab: false,
        packingMaintainTab: false,
        billTab: false,
        logisticsTab: false,
        entryTab: false,
        attachTab: false,
        aeoTab: false,
      },
      parentConfig: {
        editData: {},
        editStatus: editStatus.SHOW
      },
      actions: [],
      headData: {},
      showBody: false,
      collapsed: true,
      isHeadSaved: false,     // 是否点击了表头保存按钮
      aeoShowTitle: false,
      isEntryTabShow: false,
      isLogisticsTabShow: false
    }
  },
  created: function() {
    let me = this
    me.loadFunctions('tabs').then()
  },
  watch: {
    editConfig: {
      deep: true,
      immediate: true,
      handler: function(config) {
        let me = this
        if (config && config.editStatus === editStatus.ADD) {
          me.headData = {
            sid: '',
            emsNo: '',
            gmark: '',
            bondMark: '',
            billType: '',
            emsListNo: '',
            tradeMode: '',
            districtCode: '',
            districtPostCode: ''
          }
          me.$set(me.parentConfig, 'editStatus', config.editStatus)
          me.showBody = false
        } else if (config && config.editStatus === editStatus.EDIT) {
          me.headData = config.editData
          me.parentConfig.editData = config.editData
          me.$set(me.parentConfig, 'editStatus', config.editStatus)
          me.showBody = true
        } else if (config && config.editStatus === editStatus.SHOW) {
          me.headData = config.editData
          me.parentConfig.editData = config.editData
          me.$set(me.parentConfig, 'editStatus', config.editStatus)
          me.showBody = true
        }
      }
    },
    tabName(value) {
      let me = this
      me.tabs[value] = true
      if (value === 'headTab') {
        if (me.dynamicHeadTabShow) {
          me.reloadDynamicHeadTab()
        } else {
          me.$nextTick(() => {
            if (me.$refs.head) {
              me.$refs.head.reCaculateWt()
              me.$refs.head.loadBodySumInfo()
              me.$refs.head.setFeeDisableByTransMode(me.headData.transMode)
              me.$emit('onDetailReload', me.editConfig.editData.sid, me.editConfig.editStatus)
            }
          })
        }
      } else if (value === 'billTab') {
        me.$nextTick(() => {
          if (me.$refs.bill) {
            me.$refs.bill.initFrm()
          }
        })
      } else if (value === 'bodyTab') {
        me.$nextTick(() => {
          if (me.$refs.body) {
            if (me.isHeadSaved) {
              me.$refs.body.backToList()
              me.$refs.body.billTypeChange(me.headData.billType)
              me.$set(me, 'isHeadSaved', false)
            }
            me.$refs.body.handleSearchSubmit()
          }
        })
      } else if (value === 'attachTab') {
        me.$nextTick(() => {
          if (me.$refs.attachInfo) {
            me.$refs.attachInfo.loadAttach()
          }
        })
      } else if (value === 'logisticsTab') {
        if (me.isLogisticsTabShow !== true) {
          me.$set(me, 'isLogisticsTabShow', true)
        } else {
          me.$nextTick(() => {
            if (me.$refs.logistics) {
              me.$refs.logistics.loadLogisticsData()
            }
          })
        }
      } else if (value === 'entryTab') {
        if (me.isEntryTabShow !== true) {
          me.$set(me, 'isEntryTabShow', true)
        } else {
          me.$nextTick(() => {
            if (me.$refs.entryTrack) {
              me.$refs.entryTrack.customsTrackDataLoad()
            }
          })
        }
      } else if (value === 'certTab') {
        me.$nextTick(() => {
          if (me.$refs.cert) {
            me.$refs.cert.getUseData()
          }
        })
      }
    }
  },
  methods: {
    afterHeadSaved() {
      let me = this
      me.$set(me, 'isHeadSaved', true)
    },
    /**
     * 返回列表界面
     */
    backToList() {
      this.editBack({
        editData: {},
        showList: true,
        editStatus: editStatus.SHOW
      })
    },
    /**
     * 供编辑界面传回信息调用
     * @param backObj
     */
    editBack(backObj) {
      this.$emit('onEditBack', backObj)
    },
    /**
     * 刷新清单信息
     */
    refreshBill() {
      this.headData.apprStatus = '9'
      if (this.$refs.bill) {
        this.$refs.bill.initFrm()
      }
    },
    getHeadId() {
      let me = this
      return me.editConfig.editData.sid
    },
    /**
     * 隐藏/显示右侧Slider
     * @param e
     */
    handleRightSliderClick(e) {
      let me = this
      me.$set(me, 'collapsed', !me.collapsed)
      if (me.collapsed) {
        e.target.style.transform = 'rotate(90deg)'
        e.target.style['-webkit-transform'] = 'rotate(90deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 10px'
        }
      } else {
        e.target.style.transform = 'rotate(0deg)'
        e.target.style['-webkit-transform'] = 'rotate(0deg)'
        if (me.$refs['attachTitle']) {
          me.$refs['attachTitle'].style.padding = '3px 22px'
        }
      }
    }
  }
}
