<template>
  <section>
    <XdoForm ref="formInline" class="dc-form dc-form-3" :model="searchParam" label-position="right" :label-width="120" inline>
      <XdoFormItem prop="apprStatus" label="状态">
        <xdo-select v-model="searchParam.apprStatus" :options="this.cmbDataSource.apprStatusList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsListNo" label="单据内部编号">
        <XdoIInput type="text" v-model="searchParam.emsListNo" clearable></XdoIInput>
      </XdoFormItem>
      <dc-dateRange label="制单日期" @onDateRangeChanged="handleValidDateChange" :values="ieDefaultDates"></dc-dateRange>
      <XdoFormItem prop="hawb" label="提运单号">
        <XdoIInput type="text" v-model="searchParam.hawb" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafName" label="运输工具名称">
        <XdoIInput type="text" v-model="searchParam.trafName" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="trafMode" label="运输方式">
        <xdo-select v-model="searchParam.trafMode" :asyncOptions="pcodeList" :meta="pcode.transf"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="tradeMode" label="监管方式">
        <xdo-select v-model="searchParam.tradeMode" :asyncOptions="pcodeList" :meta="pcode.trade"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryPort" :label="entryPortLabel">
        <xdo-select v-model="searchParam.entryPort" :asyncOptions="pcodeList" meta="CIQ_ENTY_PORT"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="emsNo" label="备案号">
        <xdo-select v-model="searchParam.emsNo" :options="this.cmbDataSource.emsNoList"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="attach" label="随附单据">
        <xdo-select v-model="searchParam.attach" :options="this.productClassify.ATTACH_SELECT"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="insertUserParam" label="制单员">
        <XdoIInput type="text" v-model="searchParam.insertUserParam" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="invoiceNo" label="发票号">
        <XdoIInput type="text" v-model="searchParam.invoiceNo" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="declareName" label="报关单申报单位">
        <XdoIInput type="text" v-model="searchParam.declareName" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="overseasShipper" :label="overseasShipperLabel">
        <xdo-select v-model="searchParam.overseasShipper" :options="this.cmbDataSource.overseasShipperList"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
      <XdoFormItem prop="entryNo" label="报关单号">
        <XdoIInput type="text" v-model="searchParam.entryNo" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="apprUserParam" label="内审员">
        <XdoIInput type="text" v-model="searchParam.apprUserParam" :maxlength="30" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="contrNo" label="合同协议号">
        <XdoIInput type="text" v-model="searchParam.contrNo" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="remark" label="内部备注">
        <XdoIInput type="text" v-model="searchParam.remark" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="note" label="备注">
        <XdoIInput type="text" v-model="searchParam.note" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem v-if="iemark === 'I'" prop="entryType" label="报关单类型">
        <xdo-select v-model="searchParam.entryType" :options="this.cmbDataSource.entryTypeData"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { namespace } from '@/project'
  import { isNullOrEmpty, ArrayToLocaleLowerCase } from '@/libs/util'
  import { productClassify, importExportManage } from '@/view/cs-common'

  export default {
    name: 'DecErpHeadSearch',
    props: {
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      }
    },
    data() {
      return {
        searchParam: {
          emsListNo: '',
          entryPort: '',
          tradeMode: '',
          trafMode: '',
          insertTimeFrom: '',
          insertTimeTo: '',
          hawb: '',
          trafName: '',
          grossWt: '',
          packNum: '',
          insertUserParam: '',
          apprStatus: '',
          emsNo: '',
          attach: '',
          volume: '',
          invoiceNo: '',
          declareName: '',
          decType: '0',
          overseasShipper: '',
          entryNo: '',
          apprUserParam: '',
          contrNo: '',
          remark: '',
          entryType: '',
          note: ''
        },
        cmbDataSource: {
          emsNoList: [],
          // apprUserParamData: [],
          overseasShipperList: [],
          entryTypeData: importExportManage.entryType,
          apprStatusList: importExportManage.auditStatusMap
        },
        productClassify: productClassify
      }
    },
    created: function () {
      // 备案号
      let me = this
      me.$http.post(csAPI.csProductClassify.bonded.getEmsNoSelect).then(res => {
        let tmpArr = []
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
        }
        me.cmbDataSource.emsNoList = tmpArr
      })
      // 境外收/发货人
      let overseasShipperUrl = ''
      if (me.iemark === 'I') {
        overseasShipperUrl = csAPI.ieParams.PRD
      } else if (me.iemark === 'E') {
        overseasShipperUrl = csAPI.ieParams.CLI
      }
      if (!isNullOrEmpty(overseasShipperUrl)) {
        me.$http.post(overseasShipperUrl).then(res => {
          me.cmbDataSource.overseasShipperList = [{label: 'NO', value: 'NO'}, ...ArrayToLocaleLowerCase(res.data.data)]
        }).catch(() => {
          me.cmbDataSource.overseasShipperList = []
        })
      }
      // // 内审人
      // me.$http.post(csAPI.aeoManage.comm.getapprUserParam).then(res => {
      //   me.cmbDataSource.apprUserParamData = res.data.data.map(item => {
      //     return {
      //       label: item.APPR_USER,
      //       value: item.APPR_USER
      //     }
      //   })
      // }).catch(() => {
      //   me.cmbDataSource.apprUserParamData = []
      // })
    },
    mounted: function () {
      let me = this
      me.$set(me.searchParam, 'emsNo', me.$store.getters[`${namespace}/selectedManual`])
    },
    computed: {
      entryPortLabel() {
        if (this.iemark === 'I') {
          return '入境口岸'
        } else if (this.iemark === 'E') {
          return '离境口岸'
        } else {
          return '未设置进出口标志'
        }
      },
      overseasShipperLabel() {
        if (this.iemark === 'I') {
          return '境外发货人'
        } else if (this.iemark === 'E') {
          return '境外收货人'
        } else {
          return '境外收/发货人'
        }
      },
      ieDefaultDates() {
        let today = new Date(),
          dateTo = today.toLocaleDateString(),
          dateFrom = new Date(today.setMonth(today.getMonth() - 3)).toLocaleDateString()
        return [dateFrom, dateTo]
      }
    },
    methods: {
      handleValidDateChange(values) {
        let me = this
        if (values instanceof Array && values.length === 2) {
          me.$set(me.searchParam, "insertTimeFrom", values[0])
          me.$set(me.searchParam, "insertTimeTo", values[1])
        } else {
          me.$set(me.searchParam, "insertTimeFrom", '')
          me.$set(me.searchParam, "insertTimeTo", '')
        }
      }
    }
  }
</script>

<style scoped>
</style>
