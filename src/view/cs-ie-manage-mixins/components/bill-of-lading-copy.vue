<template>
  <XdoModal width="420" mask v-model="show" title="提单复制"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleCancel">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <div class="header">
      <XdoForm ref="popForm" class="dc-form dc-form-3 xdo-enter-form" :model="modelData" :rules="rules" label-position="right" :label-width="100">
        <XdoFormItem prop="emsListNo" class="dc-merge-1-4" label="单据内部编号">
          <XdoIInput type="text" :maxlength="32" v-model="modelData.emsListNo" placeholder="请输入新的单据内部编号..."></XdoIInput>
        </XdoFormItem>
      </XdoForm>
    </div>
    <div class="xdo-enter-action action" style="text-align: right; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    data() {
      return {
        modelData: {
          emsListNo: ''
        },
        rules: {
          emsListNo: [{required: true, message: '单据内部编号不能为空!', trigger: 'blur'}]
        },
        buttons: [{
          label: '返回',
          loading: false,
          type: 'default',
          disabled: false,
          icon: 'dc-btn-cancel',
          needed: !this.aeoShow,
          click: this.handleCancel
        }, {
          label: '保存',
          needed: true,
          loading: false,
          disabled: false,
          type: 'primary',
          icon: 'dc-btn-save',
          click: this.handleOk
        }]
      }
    },
    props: {
      show: {
        type: Boolean,
        require: true
      }
    },
    methods: {
      handleOk() {
        let me = this
        me.$refs['popForm'].validate().then(isValid => {
          if (isValid) {
            me.$emit('popCopy:show', me.modelData.emsListNo)
            me.$set(me.modelData, 'emsListNo', '')
          }
        })
      },
      handleCancel() {
        let me = this
        me.$set(me.modelData, 'emsListNo', '')
        me.$emit('popCopy:show', '')
        me.$refs['popForm'].resetFields()
      }
    }
  }
</script>

<style lang="less" scoped>
</style>
