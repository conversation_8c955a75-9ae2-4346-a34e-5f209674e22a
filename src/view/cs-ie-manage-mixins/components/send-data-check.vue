<template>
  <XdoModal v-model="show" title="预警提醒"
            :footer-hide="true" :mask-closable="false" :width="600">
    <DcAgGrid ref="table" :columns="errgridColumns" :data="gridConfig.errdata" :height="300"></DcAgGrid>
    <div ref="area_page" style="text-align: right; padding-right: 20px; height: 38px; overflow: hidden;">
      共 {{gridConfig.errDataTotal}} 条
    </div>
    <div style="text-align: center;">
      <XdoButton type="primary" class="dc-margin-right" @click="continueProduct" :disabled="isContinue">继续</XdoButton>
      <XdoButton type="primary" class="dc-margin-right" @click="backPage">取消</XdoButton>
    </div>
  </XdoModal>
</template>

<script>
  export default {
    props: {
      show: {
        type: Boolean,
        required: true
      },
      isContinue: {
        type: Boolean,
        required: true
      },
      gridConfig: {
        type: Object,
        required: () => ({})
      }
    },
    data() {
      return {
        errgridColumns: [{
          width: 120,
          key: 'dataMark',
          title: '类型标记',
          render: (h, params) => {
            if (params.row.dataMark === 'head') {
              if (params.row.warningMark === '2') {
                return h('span', {
                  style: {
                    color: 'red'
                  }
                }, '表头')
              } else {
                return h('span', {}, '表头')
              }
            } else if (params.row.dataMark === 'list') {
              if (params.row.warningMark === '2') {
                return h('span', {
                  style: {
                    color: 'red'
                  }
                }, '表体')
              } else {
                return h('span', {}, '表体')
              }
            }
          }
        }, {
          width: 200,
          title: '预警信息',
          key: 'warningMsg',
          render: (h, params) => {
            if (params.row.warningMark === '2') {
              return h('span', {
                style: {
                  color: 'red'
                }
              }, params.row.warningMsg)
            } else {
              return h('span', {}, params.row.warningMsg)
            }
          }
        }]
      }
    },
    methods: {
      continueProduct() {
        let me = this
        me.$emit('onCheckBack', {
          isContinue: true,
          showDialog: false
        })
      },
      backPage() {
        let me = this
        me.$emit('onCheckBack', {
          showDialog: false,
          isContinue: false
        })
      }
    }
  }
</script>
