import { csAPI } from '@/api'
import { namespace } from '@/project'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'
import DcNumberInput from '@/components/dc-number-input/dc-number-input'
import { auditTrailDetail } from '../../cs-aeoManage/base/auditTrailDetail'
import { regulatoryRules } from '@/view/cs-ie-manage/js/comm/regulatoryRules'
import AreaPostCascader from '@/view/cs-ie-manage-mixins/components/area-post-cascader'
import { editStatus, MerchElement, importExportManage, taxExemptionEquipment } from '@/view/cs-common'
import { isNullOrEmpty, isNumber, keepDecimal, numberRangeValid, ArrayToLocaleLowerCase } from '@/libs/util'

export const decErpBodyEdit = {
  mixins: [commEdit, regulatoryRules, auditTrailDetail],
  components: {
    MerchElement,
    DcNumberInput,
    AreaPostCascader
  },
  props: {
    aeoShow: {
      type: Boolean,
      default: false
    },
    headData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      status: '',
      timer: null,
      tyShow: false,
      isMounted: false,
      currModelString: '',
      rulesHeader: {
        bondMark: [{required: true, message: '不能为空', trigger: 'blur'}],
        gmark: [{required: true, message: '不能为空', trigger: 'blur'}],
        gname: [{required: true, message: '不能为空', trigger: 'blur'}],
        gno: [{required: false, type: 'number', message: '不是有效的数字!', trigger: 'blur'}],
        emsNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
        copGNo: [{required: false, message: '不能为空!', trigger: 'blur'}],
        // netWt: [{ type: "number", message: '不是有效的数字!' }],
        // grossWt: [{ type: "number", message: '不是有效的数字!' }],
        // volume: [{ type: "number", message: '不是有效的数字!' }],
        tradeMode: [{required: true, message: '不能为空', trigger: 'blur'}],
        codeTS: [{required: true, message: '不能为空', trigger: 'blur'}],
        gmodel: [{required: true, message: '不能为空', trigger: 'blur'}],
        unit: [{required: true, message: '不能为空'}],
        unit1: [{required: true, message: '不能为空!', trigger: 'blur'}],
        curr: [{required: true, message: '不能为空'}],
        qty: [{type: "number", required: true, message: '不能为空', trigger: 'blur'}],
        decTotal: [{/*type: "number", */required: true, message: '不能为空', trigger: 'blur'}],
        decPrice: [{/*type: "number", */required: true, message: '不能为空', trigger: 'blur'}],
        exgVersion: [{required: false, message: '不能为空!', trigger: 'blur'}],
        entryGNo: [{
          required: false, type: "number", message: '不能为空!', trigger: 'blur'
        }, {
          validator: numberRangeValid, min: 1, max: 50
        }],
        dutyMode: [{required: true, message: '不能为空'}],
        qty1: [{required: false, type: 'number', message: '不能为空!', trigger: 'blur'}],
        qty2: [{required: false, type: 'number', message: '不能为空', trigger: 'blur'}],
        packNum: [{required: false, type: 'number', message: '不能为空!', trigger: 'blur'}],
        grossWt: [{required: false, message: '不能为空!', trigger: 'blur'}],
        billGNo: [{required: false, type: 'number', message: '不能为空', trigger: 'blur'}],
        originCountry: [{required: true, message: '不能为空', trigger: 'blur'}],
        destinationCountry: [{required: true, message: '不能为空', trigger: 'blur'}],
        districtCode: [{required: false, message: '不能为空!', trigger: 'blur'}],
        districtPostCode: [{required: false, message: '不能为空!', trigger: 'blur'}],
        netWt: [{required: false, message: '不能为空!', trigger: 'blur'}]
      },
      buttons: [
        {...btnComm, click: this.handleSave, icon: 'dc-btn-save', label: '保存'},
        {...btnComm, click: this.handleSaveContinue, icon: 'dc-btn-savecontinue', label: '保存继续'},
        {...btnComm, click: this.handleSaveClose, icon: 'dc-btn-saveclose', label: '保存关闭'},
        {...btnComm, click: this.handleBack, icon: 'dc-btn-cancel', label: '返回'}
      ],
      cmbSource: {
        emsNoList: [],
        emsCopEmsNo: {},
        facGNoSource: [],
        supplierCodeList: [],
        gmarkList: importExportManage.gmarkMap,
        bondMarkList: importExportManage.bondedFlagMap
      },
      cutModeDisabled: false,
      dutyModeDisabled: false,
      emsNoChangedByUser: false,
      netWTFromFacNo: null,
      gMarkDisable: false,
      disabledBillGNo: false,
      onAuditChangeEvent: 'onBodyAuditChanged',
      taxExemptionEquipment: taxExemptionEquipment
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function (val) {
        let me = this
        if (val === editStatus.SHOW) {
          me.buttons[0].needed = false
          me.buttons[1].needed = false
          me.buttons[2].needed = false
          me.$set(me, 'cutModeDisabled', true)
        } else {
          me.buttons[0].needed = true
          me.buttons[1].needed = true
          me.buttons[2].needed = true
          me.$set(me, 'cutModeDisabled', false)
        }
      }
    },
    'editConfig.headData.mergeType': {
      immediate: true,
      handler: function (val) {
        let me = this
        if (val === '1') {
          if (me.editConfig.editStatus === editStatus.ADD) {
            me.$set(me.frmData, 'emsNo', me.editConfig.headData.emsNo)
            me.$set(me.frmData, 'tradeMode', me.editConfig.headData.tradeMode)
          }
          me.rulesHeader.entryGNo[0].required = true
        } else {
          me.rulesHeader.entryGNo[0].required = false
        }
        me.$nextTick(() => {
          if (me.$refs['fiEntryGNo']) {
            me.$refs['fiEntryGNo'].setRules()
          }
        })
      }
    },
    'editConfig.headData.billType': {
      immediate: true,
      handler: function (billType) {
        this.billTypeChange(billType)
      }
    },
    'frmData.unit': {
      handler: function (val) {
        if (!isNullOrEmpty(val)) {
          this.qtyReady('unit')
        }
      }
    },
    'frmData.unit1': {
      handler: function (val) {
        if (!isNullOrEmpty(val)) {
          this.qtyReady('unit1')
        }
        if (this.$refs.fiQty1) {
          this.$refs.fiQty1.setRules()
        }
      }
    },
    'frmData.unit2': {
      handler: function (val) {
        if (!isNullOrEmpty(val)) {
          this.qtyReady('unit2')
        }
        this.bodyRequiredCtrl()
      }
    },
    'frmData.qty2': {
      handler: function (val) {
        if (isNumber(val) && typeof val === 'number') {
          this.$set(this.frmData, 'qty2', val.toString())
        }
      }
    },
    iemark: {
      immediate: true,
      handler: function () {
        this.bodyRequiredCtrl()
      }
    },
    'frmData.bondMark': {
      handler: function (bondMark) {
        let me = this
        me.bondMarkChanged(bondMark)
        me.exgVersionCtrl()
        me.setFieldsRequired(bondMark)
        me.bodyRequiredCtrl()
        me.loadFacGNoData()
        me.$set(me.frmData, 'cutMode', '')
      }
    },
    'frmData.gmark': {
      handler: function () {
        this.resetMaterialInfo()
        this.emsNoChange()
        this.exgVersionCtrl()
        this.loadFacGNoData()
      }
    },
    'frmData.emsNo': {
      handler: function () {
        if (this.frmData.bondMark === '0') {
          this.$set(this.frmData, 'tradeMode', '')
          this.$set(this, 'emsNoChangedByUser', true)
          this.resetMaterialInfo()
          this.emsNoChange()
          this.tradeModeChange(this.frmData.tradeMode)
        }
        this.loadFacGNoData()
      }
    },
    'frmData.facGNo': {
      handler: function (facGNo) {
        let me = this
        if (isNullOrEmpty(facGNo)) {
          me.resetMaterialInfo()
        } else {
          me.$http.post(me.ajaxUrl.getIeInfo, {
            bondedFlag: me.frmData.bondMark,     // 保税标记
            copGNo: me.frmData.copGNo,           // 备案料号
            emsNo: me.frmData.emsNo,             // 备案号
            gmark: me.frmData.gmark,             // 物料类型
            facGNo: facGNo ? facGNo : '',        // 企业料号
            iemark: me.iemark,
            headId: me.editConfig.headId
          }).then(res => {
            me.$set(me.frmData, 'gno', res.data.data.serialNo)
            if (me.frmData.copGNo !== res.data.data.copGNo) {
              me.$set(me.frmData, 'copGNo', res.data.data.copGNo)
            }
            if (me.frmData.facGNo !== res.data.data.facGNo) {
              me.$set(me.frmData, 'facGNo', res.data.data.facGNo)
            }
            me.$set(me.frmData, 'gname', res.data.data.gname)
            me.$set(me.frmData, 'codeTS', res.data.data.codeTS)

            me.$set(me.frmData, 'gmodel', res.data.data.gmodel)
            me.$set(me.frmData, 'unit', res.data.data.unit)

            me.$set(me.frmData, 'unit1', res.data.data.unit1)
            me.$set(me.frmData, 'unit2', res.data.data.unit2)
            me.$set(me.frmData, 'copGName', res.data.data.copGName)
            me.$set(me.frmData, 'copGModel', res.data.data.copGModel)
            me.$set(me.frmData, 'itemNo', res.data.data.itemNo)
            me.$set(me.frmData, 'costCenter', res.data.data.costCenter)
            me.$set(me.frmData, 'ciqNo', res.data.data.ciqNo)

            if (isNumber(res.data.data.netWt)) {
              me.$set(me, 'netWTFromFacNo', parseFloat(res.data.data.netWt))
            } else if (!isNullOrEmpty(res.data.data.netWt) && isNumber('0' + res.data.data.netWt)) {
              me.$set(me, 'netWTFromFacNo', parseFloat('0' + res.data.data.netWt))
            } else {
              me.$set(me, 'netWTFromFacNo', null)
            }
            me.$set(me.frmData, 'singleWeight', me.netWTFromFacNo)
            me.emsNoChange()
            if (isNullOrEmpty(me.frmData.exgVersion)) {
              me.$set(me.frmData, 'exgVersion', res.data.data.exgVersion)
            }
            if (me.iemark === 'I' && me.configData.matDataI === '1') {
              me.$set(me.frmData, 'decPrice', res.data.data.decPrice)
              me.$set(me.frmData, 'curr', res.data.data.curr)
              if (!['4400', '4600'].includes(me.editConfig.headData.tradeMode)) {
                me.$set(me.frmData, 'originCountry', res.data.data.country)
              }
            } else if (me.iemark === 'E' && me.configData.matDataE === '1') {
              me.$set(me.frmData, 'decPrice', res.data.data.decPrice)
              me.$set(me.frmData, 'curr', res.data.data.curr)
              me.$set(me.frmData, 'destinationCountry', res.data.data.country)
            }
          }).catch(() => {
            me.resetMaterialInfo()
          })
        }
      }
    },
    'frmData.tradeMode': {
      handler: function (tradeMode) {
        this.$set(this.frmData, 'facGNo', '')
        this.tradeModeChange(tradeMode)
        this.loadFacGNoData()
      }
    },
    /**
     * 以下為通關業務配置相關內容
     */
    'configData.listRequiredField': {
      immediate: true,
      handler: function () {
        this.bodyRequiredCtrl()
      }
    }
  },
  created() {
    this.loadEmsNo()
    this.loadSupplierCodeList()
  },
  mounted: function () {
    let me = this
    me.$nextTick(() => {
      me.exgVersionCtrl()
      if (me.frmData.bondMark === '0') {
        me.cmbSource.gmarkList = importExportManage.gmarkMap.filter((val) => {
          return ['E', 'I'].indexOf(val.value) > -1
        })
      } else {
        me.cmbSource.gmarkList = importExportManage.gmarkMap
      }
      if (me.editConfig.editStatus === editStatus.EDIT) {
        me.tradeModeChange(me.frmData.tradeMode, true)
      }
      me.status = me.editConfig.editStatus
      me.$set(me, 'isMounted', true)
      me.loadFacGNoData()
      if (me.headData.dclcusMark === '2') {
        me.$set(me.frmData, 'bondMark', '0')
      }
    })
    me.setFieldsRequired(me.frmData.bondMark)
    console.info('billType: ' + me.editConfig.headData.billType)
  },
  computed: {

    /**
     * 申报规格型号   禁用
     * @returns {boolean}
     */
    canEdit_gmodel(){
      if  (this.iemark ==='I' )  {
        return  (this.configData.canEditBodyIBond ||'').split(',').includes('0')
      }
      if  (this.iemark ==='E' )  {
        return  (this.configData.canEditBodyEBond||'').split(',').includes('0')
      }
      return  false
    },

    /**
     * 料号申报要素   禁用
     * @returns {boolean}
     */
    canEdit_copGModel(){
      if  (this.iemark ==='I' )  {
        return  (this.configData.canEditBodyIBond ||'').split(',').includes('1')
      }

      if  (this.iemark ==='E')  {
        return  (this.configData.canEditBodyEBond||'').split(',').includes('1')
      }

      return  false
    },


    bondMarkDisable() {
      let me = this
      if (me.headData.dclcusMark === '2') {
        return true
      }
      if (me.editConfig.headData.mergeType === '1') {
        return true
      }
      return me.showDisable
    },
    isDisableExgVersion() {
      if (!isNullOrEmpty(this.frmData.emsNo) && (this.frmData.emsNo.startsWith('B') || this.frmData.emsNo.startsWith('C'))) {
        return true
      }
      return !(this.frmData.gmark === importExportManage.gmark.成品 && this.frmData.bondMark === '0')
    },
    gmarkDisable() {
      if (this.showDisable) {
        return true
      } else {
        return this.editConfig.headData.mergeType === '1'
      }
    },
    emsNoRealDisable() {
      if (this.showDisable) {
        return true
      } else if (this.frmData.bondMark !== '0') {
        return true
      } else {
        return this.editConfig.headData.mergeType === '1'
      }
    },
    tradeModeDisable() {
      if (this.editConfig.headData.mergeType === '1') {
        return true
      }
      return this.showDisable
    },
    unitDisable() {
      if (this.showDisable) {
        return true
      }
      return !['0844', '0845', '5200', '0400', '0864', '0865'].includes(this.frmData.tradeMode)
    },
    entryGNoDisable() {
      if (this.editConfig.headData.mergeType !== '1') {
        return true
      }
      return this.showDisable
    },
    showQtyDisable() {
      if (this.showDisable) {
        return true
      }
      return isNullOrEmpty(this.frmData.unit2)
    },
    facGNoOptions() {
      let me = this
      if (me.editConfig.editStatus !== editStatus.ADD && me.emsNoChangedByUser === false) {
        me.$set(me.frmData, 'facGNo', me.editConfig.editData.facGNo)
      }
      return {
        limit: 20,
        label: 'value',
        value: 'value',
        fieldName: 'facGNo',
        ajaxUrl: me.ajaxUrl.getFacGNolist,
        params: {
          bondedFlag: me.frmData.bondMark,     // 保税标记
          // copGNo: me.frmData.copGNo,        // 备案料号
          emsNo: me.frmData.emsNo,             // 备案号
          gmark: me.frmData.gmark,             // 物料类型
          tradeMode: me.frmData.tradeMode      // 监管方式
        }
      }
    },
    doQuery() {
      if (isNullOrEmpty(this.frmData.bondMark)) {
        return false
      } else if (isNullOrEmpty(this.frmData.gmark)) {
        return false
      }
      return true
    },
    areaOptions() {
      return {
        area: {
          field: 'districtCode',
          value: this.frmData.districtCode
        },
        post: {
          field: 'districtPostCode',
          value: this.frmData.districtPostCode
        }
      }
    },
    /**
     * 规范申报展示信息
     * @returns {number}
     */
    usedCountGModel() {
      let bytesCount = 0
      let strGModel = this.frmData.gmodel
      if (isNullOrEmpty(strGModel)) {
        return bytesCount
      }
      let chars = ''
      for (let i = 0; i < strGModel.length; i++) {
        chars = strGModel.charAt(i)
        /* eslint-disable no-control-regex */
        if (/^[\u0000-\u00ff]$/.test(chars)) {
          bytesCount += 1
        } else {
          bytesCount += 2
        }
      }
      return bytesCount
    },
    /**
     * 料号申报要素展示信息
     * @returns {number}
     */
    usedCountCopGModel() {
      let bytesCount = 0
      let strGModel = this.frmData.copGModel
      if (isNullOrEmpty(strGModel)) {
        return bytesCount
      }
      let chars = ''
      for (let i = 0; i < strGModel.length; i++) {
        chars = strGModel.charAt(i)
        /* eslint-disable no-control-regex */
        if (/^[\u0000-\u00ff]$/.test(chars)) {
          bytesCount += 1
        } else {
          bytesCount += 2
        }
      }
      return bytesCount
    },
    meModelString() {
      if (this.currModelString === 'gmodel') {
        return this.frmData.gmodel
      } else if (this.currModelString === 'copGModel') {
        return isNullOrEmpty(this.frmData.copGModel) ? '' : this.frmData.copGModel
      } else {
        return this.frmData.gmodel
      }
    },
    isEdit() {
      return this.editConfig.editStatus === editStatus.EDIT || this.editConfig.editStatus === editStatus.ADD
    },
    /**
     * 根据EmsNo及iEMark过滤后的监管方式
     * @returns {*}
     */
    filterTradeMode() {
      return this.getTradeModeByEmsNo(this.frmData.emsNo, this.iemark)
    },
    billGNoDisabled() {
      if (this.disabledBillGNo) {
        return true
      }
      return this.showDisable
    },
    configData() {
      return this.$store.state[`${namespace}`].clearanceBusinessSetting
    },
    configRequiredField() {
      if (this.iemark === 'I') {
        // if (this.frmData.bondMark === '0') {
        return this.configData.listRequiredField
        // } else if (this.frmData.bondMark === '1') {
        //   return this.configData.listReFieldINoBond
        // }
      } else if (this.iemark === 'E') {
        // if (this.frmData.bondMark === '0') {
        return this.configData.listReFieldE
        // } else if (this.frmData.bondMark === '1') {
        //   return this.configData.listReFieldENoBond
        // }
      }
      return ''
    },
    precisionsConfig() {
      let me = this,
        result = {
          qtyDigit: 5,
          qty1Digit: 5,
          qty2Digit: 5,
          netWtDigit: 8,

          volumeI: 5,
          volumeE: 5,
          decTotalI: 4,
          decTotalE: 4
        }

      if (isNumber(me.configData.precisionsConfig.qtyDigit)) {
        result.qtyDigit = me.configData.precisionsConfig.qtyDigit
      }
      if (isNumber(me.configData.precisionsConfig.qty1Digit)) {
        result.qty1Digit = me.configData.precisionsConfig.qty1Digit
      }
      if (isNumber(me.configData.precisionsConfig.qty2Digit)) {
        result.qty2Digit = me.configData.precisionsConfig.qty2Digit
      }
      if (isNumber(me.configData.precisionsConfig.netWtDigit)) {
        result.netWtDigit = me.configData.precisionsConfig.netWtDigit
      }

      if (isNumber(me.configData.volumeDigitI)) {
        result.volumeI = me.configData.volumeDigitI
      }
      if (isNumber(me.configData.volumeDigitE)) {
        result.volumeE = me.configData.volumeDigitE
      }
      if (isNumber(me.configData.decTotalDigitI)) {
        result.decTotalI = me.configData.decTotalDigitI
      }
      if (isNumber(me.configData.decTotalDigitE)) {
        result.decTotalE = me.configData.decTotalDigitE
      }
      return result
    }
  },
  methods: {
    loadFacGNoData() {
      let me = this,
        params = {
          emsNo: me.frmData.emsNo,             // 备案号
          gmark: me.frmData.gmark,             // 物料类型
          bondedFlag: me.frmData.bondMark,     // 保税标记
          tradeMode: me.frmData.tradeMode      // 监管方式
        }
      if (me.isMounted === true) {
        me.$http.post(me.ajaxUrl.getFacGNolist, params).then(res => {
          me.$set(me.cmbSource, 'facGNoSource', res.data.data.map(item => {
            return {
              value: item,
              label: item
            }
          }))
        }).catch(() => {
          me.$set(me.cmbSource, 'facGNoSource', [])
        })
      }
    },
    setFieldsRequired(bondMark) {
      let me = this
      me.rulesHeader.destinationCountry[0].required = bondMark === '0'
      me.$nextTick(() => {
        if (me.$refs.fiDestinationCountry) {
          me.$refs.fiDestinationCountry.setRules()
        }
      })
    },
    /**
     * 备案号
     */
    loadEmsNo() {
      let me = this
      // 备案号
      me.$http.post(me.ajaxUrl.getEmsNoSelect).then(res => {
        let tmpArr = []
        let tmpEmsCopEmsNo = {}
        for (let item of res.data.data) {
          tmpArr.push({
            label: item.VALUE,
            value: item.VALUE
          })
          if (!isNullOrEmpty(item['COP_EMS_NO']) && !isNullOrEmpty(item.VALUE) && typeof tmpEmsCopEmsNo[item.VALUE] === 'undefined') {
            tmpEmsCopEmsNo[item.VALUE] = item.COP_EMS_NO
          }
        }
        me.cmbSource.emsNoList = tmpArr
        me.cmbSource.emsCopEmsNo = tmpEmsCopEmsNo
      })
    },
    /**
     * 客户/供应商
     */
    loadSupplierCodeList() {
      let me = this
      me.$http.post(me.ajaxUrl.getSupplierCode).then(res => {
        me.cmbSource.supplierCodeList = ArrayToLocaleLowerCase(res.data.data)
      }).catch(() => {
        me.cmbSource.supplierCodeList = []
      })
    },
    bondMarkChanged(bondMark) {
      if (bondMark === '0') {
        this.cmbSource.gmarkList = importExportManage.gmarkMap.filter((val) => {
          return ['E', 'I'].indexOf(val.value) > -1
        })
        this.rulesHeader.gno[0].required = true
        this.rulesHeader.emsNo[0].required = true
        this.rulesHeader.copGNo[0].required = true
        this.$set(this.frmData, 'emsNo', this.editConfig.headData.emsNo)
      } else {
        this.cmbSource.gmarkList = importExportManage.gmarkMap
        this.rulesHeader.gno[0].required = false
        this.rulesHeader.emsNo[0].required = false
        this.rulesHeader.copGNo[0].required = false
        this.$set(this.frmData, 'emsNo', '')
      }
      this.resetMaterialInfo()
    },
    /**
     * 设置单耗版本号是否必输
     */
    exgVersionCtrl() {
      this.rulesHeader.exgVersion[0].required = !this.isDisableExgVersion
      this.$refs.exgVersion.setRules()
    },
    /**
     * 重置初始化物料信息
     */
    resetMaterialInfo() {
      let me = this
      me.$set(me.frmData, 'gno', null)
      me.$set(me.frmData, 'copGNo', '')
      me.$set(me.frmData, 'facGNo', '')

      me.$set(me.frmData, 'gname', '')
      me.$set(me.frmData, 'codeTS', '')
      me.$set(me.frmData, 'gmodel', '')
      me.$set(me.frmData, 'unit', '')
      me.$set(me.frmData, 'unit1', '')
      me.$set(me.frmData, 'unit2', '')
      me.$set(me.frmData, 'copGName', '')
      me.$set(me.frmData, 'copGModel', '')
      me.$set(me.frmData, 'exgVersion', '')
      me.$set(me.frmData, 'ciqNo', '')
      if (me.iemark === 'I' && me.configData.matDataI === '1') {
        me.$set(me.frmData, 'decPrice', '')
        me.$set(me.frmData, 'curr', '')
        me.$set(me.frmData, 'originCountry', '')
      } else if (me.iemark === 'E' && me.configData.matDataE === '1') {
        me.$set(me.frmData, 'decPrice', '')
        me.$set(me.frmData, 'curr', '')
        me.$set(me.frmData, 'destinationCountry', '')
      }
    },
    /**
     * 备案号变更
     */
    emsNoChange() {
      if (this.frmData.bondMark === '0' && !isNullOrEmpty(this.frmData.emsNo)) {
        //BC手册 + 物理类型是成品，版本号默认0
        if (this.frmData.emsNo.startsWith('B') || this.frmData.emsNo.startsWith('C')) {
          this.frmData.exgVersion = ''
        } else if (this.frmData.emsNo.startsWith('E')) {
          this.frmData.exgVersion = ''
        }
      }
    },
    /**
     * 监管方式变更
     * @param value
     * @param isInit
     */
    tradeModeChange(value, isInit) {
      let me = this
      if (!me.showDisable) {
        me.$set(me, 'cutModeDisabled', false)
      }
      // 同表头新规则
      let regulatoryRule = me.getRegulatoryRule(me.frmData.emsNo, value, me.iemark)
      if (!isNullOrEmpty(regulatoryRule.gMark)) {
        me.$set(me.frmData, 'gmark', regulatoryRule.gMark)
      }
      me.$set(me, 'gMarkDisable', regulatoryRule.gMarkDisable)
      // if (!isNullOrEmpty(regulatoryRule.cutMode)) {
      //   me.$set(me.frmData, 'cutMode', regulatoryRule.cutMode)
      // }
      if (!me.showDisable) {
        me.$set(me, 'cutModeDisabled', regulatoryRule.cutModeDisable)
      }
      // 表体扩展规则
      let bodyRule = me.getDefaultByRule(value, me.iemark)
      if (isInit !== true) {
        if (!me.gMarkDisable) {
          me.$set(me.frmData, 'gmark', bodyRule.gMark)
        }
        me.$set(me.frmData, 'originCountry', bodyRule.originCountry)
        if (isNullOrEmpty(me.frmData.destinationCountry)) {
          me.$set(me.frmData, 'destinationCountry', bodyRule.destinationCountry)
        }
        me.$set(me.frmData, 'dutyMode', bodyRule.dutyMode)
      }
      me.$set(me, 'dutyModeDisabled', bodyRule.dutyModeDisabled)
    },
    /**
     * 弹出规格型号
     */
    handleAddtype() {
      this.currModelString = 'gmodel'
      this.tyShow = true
    },
    handleAddtypeCn() {
      this.currModelString = 'copGModel'
      this.tyShow = true
    },
    handleGModelChange(val) {
      if (this.currModelString === 'gmodel') {
        this.$set(this.frmData, 'gmodel', val)
      } else if (this.currModelString === 'copGModel') {
        this.$set(this.frmData, 'copGModel', val)
      } else {
        this.$set(this.frmData, 'gmodel', val)
      }
    },
    /**
     * 设置保存按钮加载样式
     * @param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
      this.buttons[1].loading = loading
      this.buttons[2].loading = loading
      console.info('btnLoading: ' + loading)
    },
    /**
     * 根据单价计算总价
     */
    calcTotal() {
      let me = this
      if (isNumber(me.frmData.decPrice) && isNumber(me.frmData.qty)) {
        let price = Number(me.frmData.decPrice)
        let qty = Number(me.frmData.qty)
        if (me.iemark === 'I') {
          me.$set(me.frmData, 'decTotal', keepDecimal(price * qty, me.precisionsConfig.decTotalI))
        } else {
          me.$set(me.frmData, 'decTotal', keepDecimal(price * qty, me.precisionsConfig.decTotalE))
        }
      }
    },
    /**
     * 根据总价计算单价
     */
    calcPrice() {
      let me = this
      if (isNumber(me.frmData.decTotal) && isNumber(me.frmData.qty)) {
        let total = Number(me.frmData.decTotal)
        let qty = Number(me.frmData.qty)
        me.$set(me.frmData, 'decPrice', keepDecimal(total / qty, 5))
      }
    },
    /**
     * 根据序号设置法定数量的默认值
     * @param qtyIndex
     */
    qtyAutoFill(qtyIndex) {
      if (qtyIndex !== 1 && qtyIndex !== 2) {
        return
      }
      let qtyField = 'qty' + qtyIndex.toString()
      let unitField = 'unit' + qtyIndex.toString()
      let destField = 'dest' + qtyIndex.toString()
      let me = this
      if (!isNullOrEmpty(me.frmData.unit) && !isNullOrEmpty(me.frmData[unitField])) {
        if (isNumber(me.frmData.qty)) {
          const rateParams = {
            //sid: me.frmData.sid,
            bondMark: me.frmData.bondMark,
            gmark: me.frmData.gmark,
            emsNo: me.frmData.emsNo,
            facGNo: me.frmData.facGNo,
            origin: me.frmData.unit
          }
          rateParams[destField] = me.frmData[unitField]
          if (me.frmData.unit === me.frmData[unitField]) {
            if (qtyIndex === 1) {
              me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty, me.precisionsConfig.qty1Digit))
            } else {
              me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty, me.precisionsConfig.qty2Digit))
            }
            return
          }
          me.$http.post(csAPI.csProductClassify.bonded.getFactorRate, rateParams).then(res => {
            if (res.data.data) {
              if (qtyIndex === 1) {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data, me.precisionsConfig.qty1Digit))
              } else {
                me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data, me.precisionsConfig.qty2Digit))
              }
              return
            }
            const params = {
              origin: me.frmData.unit,
              dest: me.frmData[unitField],
              type: 'UNIT'
            }
            me.$http.post(csAPI.ieParams.getTransformInfo, params).then(res => {
              if (res.data.data && !isNullOrEmpty(res.data.data.rate)) {
                if (qtyIndex === 1) {
                  me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data.rate, me.precisionsConfig.qty1Digit))
                } else {
                  me.$set(me.frmData, qtyField, keepDecimal(me.frmData.qty * res.data.data.rate, me.precisionsConfig.qty2Digit))
                }
              } else {
                if (me.frmData[unitField] === '035' && isNumber(me.frmData.netWt)) {
                  if (qtyIndex === 1) {
                    me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty1Digit))
                  } else {
                    me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty2Digit))
                  }
                }
              }
            })
          }).catch(() => {
          })
        } else {
          if (me.frmData[unitField] === '035' && isNumber(me.frmData.netWt)) {
            if (qtyIndex === 1) {
              me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty1Digit))
            } else {
              me.$set(me.frmData, qtyField, keepDecimal(me.frmData.netWt, me.precisionsConfig.qty2Digit))
            }
          }
        }
      }

      /*if (!isNumber(this.frmData[qtyField])) {
        let unitField = 'unit' + qtyIndex.toString()
        if (this.frmData[unitField] === '035' && isNumber(this.frmData.netWt)) {
          this.$set(this.frmData, qtyField, this.frmData.netWt)
        } else if (this.frmData.unit === '007' && this.frmData[unitField] === '026' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty / 2)
        } else if (this.frmData.unit === '007' && this.frmData[unitField] === '054' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty / 1000)
        } else if (this.frmData.unit === '036' && this.frmData[unitField] === '035' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty / 1000)
        } else if (!isNullOrEmpty(this.frmData.unit) && this.frmData.unit === this.frmData[unitField] && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty)
        }
        else if (this.frmData.unit === '026' && this.frmData[unitField] === '007' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty * 2)
        } else if (this.frmData.unit === '054' && this.frmData[unitField] === '007' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty * 1000)
        } else if (this.frmData.unit === '035' && this.frmData[unitField] === '036' && isNumber(this.frmData.qty)) {
          this.$set(this.frmData, qtyField, this.frmData.qty * 1000)
        }
      }*/
    },
    /**
     * 数据准备
     * 通用规则：申报单位          法一(二)单位          法一(二)数量取值
     *           相同                相同                 申报数量
     *                               035                  净重
     *          007 个              026 对             申报数量 / 2
     *          007 个              054 千个           申报数量 / 1000
     *          036 克              035 千克           申报数量 / 1000
     *          026 对              007 个             申报数量 * 2
     *          054 千个            007 个             申报数量 * 1000
     *          035 千克            036 克             申报数量 * 1000
     */
    qtyReady(fieldName) {
      if (isNullOrEmpty(fieldName)) {
        this.qtyAutoFill(1)
        this.qtyAutoFill(2)
      } else {
        if (fieldName === 'unit') {
          this.qtyAutoFill(1)
          this.qtyAutoFill(2)
        } else if (fieldName === 'unit1') {
          this.qtyAutoFill(1)
        } else if (fieldName === 'unit2') {
          this.qtyAutoFill(2)
        } else if (fieldName === 'netWt') {
          this.qtyAutoFill(1)
          this.qtyAutoFill(2)
        } else if (fieldName === 'qty') {
          this.qtyAutoFill(1)
          this.qtyAutoFill(2)
        }
      }
    },
    debounce(fn, delay) {
      let me = this
      return function () {
        clearTimeout(me.timer)
        let call = !me.timer
        if (call) {
          fn.apply(this, ['qty'])
        }
        me.timer = setTimeout(function () {
          me.timer = false
        }, delay)
      }
    },
    onQtyEnter() {
      let me = this
      if (isNumber(me.frmData.qty)) {
        me.debounce(me.qtyReady, 2000)()
        //me.qtyReady('qty')
        if (isNumber(me.frmData.singleWeight)) {
          if (!isNumber(me.frmData.netWt)) {
            me.$set(me.frmData, 'netWt', keepDecimal(me.frmData.singleWeight * me.frmData.qty, me.precisionsConfig.netWtDigit)+'')
            // me.$set(me.frmData, 'netWt', keepDecimal(me.netWTFromFacNo * me.frmData.qty, 5))
          }
        }
      }
    },
    onNetWtEnter() {
      if (isNumber(this.frmData.netWt)) {
        this.qtyReady('netWt')
      }
    },
    /**
     * 境内货源地/境内目的地
     * @param areaObj
     */
    onAreaDataChanged(areaObj) {
      this.$set(this.frmData, 'districtCode', areaObj['districtCode'])
      this.$set(this.frmData, 'districtPostCode', areaObj['districtPostCode'])
    },
    onConfirmRoyaltiesNoEnter() {
      let me = this,
        royaltiesNo = me.frmData.confirmRoyaltiesNo
      if (!isNullOrEmpty(royaltiesNo)) {
        me.$http.post(me.ajaxUrl.checkRoyalityNo, {
          iemark: me.iemark,
          royalityNo: royaltiesNo
        }).then(res => {
          if (res.data.data === true) {
            me.$set(me.frmData, 'confirmRoyalties', '1')
          } else {
            me.$set(me.frmData, 'confirmRoyalties', '')//'0')
          }
        }, () => {
        })
      } else {
        me.$set(me.frmData, 'confirmRoyalties', '')
      }
    },
    /**
     * 保存前处理【客户/供应商】及copEmsNo值
     */
    onBeforeSave() {
      let me = this
      let theItems = me.cmbSource.supplierCodeList.filter(item => {
        return item.value === me.frmData.supplierCode
      })
      if (Array.isArray(theItems) && theItems.length > 0) {
        me.$set(me.frmData, 'supplierName', theItems[0].label)
      } else {
        me.$set(me.frmData, 'supplierName', '')
      }
      if (me.frmData.bondMark === '0' && !isNullOrEmpty(me.frmData.emsNo)) {
        me.$set(me.frmData, 'copEmsNo', me.cmbSource.emsCopEmsNo[me.frmData.emsNo])
      } else {
        me.$set(me.frmData, 'copEmsNo', '')
      }
      // 特殊处理数量、单价、总价
      if (isNumber(me.frmData.qty)) {
        me.$set(me.frmData, 'qty', keepDecimal(me.frmData.qty, me.precisionsConfig.qtyDigit))
      }
      if (isNumber(me.frmData.qty1)) {
        me.$set(me.frmData, 'qty1', Number(me.frmData.qty1))
      }
      if (isNumber(me.frmData.qty2)) {
        me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
      }
      if (isNumber(me.frmData.decTotal)) {
        me.$set(me.frmData, 'decTotal', me.frmData.decTotal.toString())
      }
      if (isNumber(me.frmData.decPrice)) {
        me.$set(me.frmData, 'decPrice', me.frmData.decPrice.toString())
      }
      // if (isNumber(me.frmData.qty2)) {
      //   me.$set(me.frmData, 'qty2', me.frmData.qty2.toString())
      // }
    },
    onValidQty1() {
      let me = this
      return me.frmData.bondMark === '0' && isNullOrEmpty(me.frmData.qty1)
    },
    codeTSEnter() {
      let me = this
      // 商品编码带出计量单位
      let val = me.frmData.codeTS.trim()
      if (val.length === 10) {
        me.pcodeRemote(me.pcode.complex, val).then(res => {
          if (Array.isArray(res) && res.length > 0) {
            me.$set(me.frmData, 'unit1', res[0]['UNIT_1'])
            me.$set(me.frmData, 'unit2', res[0]['UNIT_2'])
          } else {
            me.resetUnit()
          }
        })
      } else if (val.length > 0) {
        me.resetUnit()
      }
    },
    resetUnit() {
      this.$Message.warning('商品编码不存在')
      this.$set(this.frmData, 'unit1', '')
      this.$set(this.frmData, 'unit2', '')
    },
    saveConfirm(funSave) {
      let me = this
      me.onBeforeSave()
      me.$refs['bodyEditFrom'].validate().then(isValid => {
        if (isValid) {
          me.setBtnSaveLoading(true)
          me.$http.post(me.ajaxUrl.checkDecTotal, me.frmData).then(res => {
            if (res.data.message === '0') {
              if (isNumber(me.frmData.qty2)) {
                me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
              }
              funSave.call(me)
            } else {
              me.setBtnSaveLoading(false)
              me.$Modal.confirm({
                title: '提醒',
                okText: '确定',
                cancelText: '取消',
                content: '数量*单价≠总价，是否保存',
                loading: true,
                onOk: () => {
                  if (isNumber(me.frmData.qty2)) {
                    me.$set(me.frmData, 'qty2', Number(me.frmData.qty2))
                  }
                  funSave.call(me)
                  setTimeout(() => {
                    me.$Modal.remove()
                  }, 150)
                }
              })
            }
          })
        }
      })
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.saveConfirm(() => {
        me.doSave(res => {
          me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
        })
      })
    },
    /**
     * 保存继续
     */
    handleSaveContinue() {
      let me = this
      me.saveConfirm(() => {
        me.doSave(() => {
          me.refreshIncomingData(false, editStatus.ADD, me.getDefaultData())
        })
      })
    },
    /**
     * 保存关闭
     */
    handleSaveClose() {
      let me = this
      me.saveConfirm(() => {
        me.doSave(() => {
          me.refreshIncomingData(true, editStatus.SHOW, me.getDefaultData())
        })
      })
    },
    /**
     * 表头清单归并类型变更
     */
    billTypeChange(billType) {
      let me = this
      if (billType === '4') {
        me.rulesHeader.billGNo[0].required = true
        me.disabledBillGNo = false
      } else {
        me.rulesHeader.billGNo[0].required = false
        me.disabledBillGNo = true
      }
      me.$nextTick(() => {
        if (me.$refs['fiBillGNo']) {
          me.$refs['fiBillGNo'].setRules()
        }
      })
    },
    /**
     * 判断法一法二数量是否必填
     */
    bodyRequiredCtrl() {
      let me = this,
        reqFields = [],
        requires = me.configRequiredField
      if (!isNullOrEmpty(requires)) {
        reqFields = requires.split(',')
      }
      me.rulesHeader.qty1[0].required = false
      me.rulesHeader.qty2[0].required = false

      me.rulesHeader.netWt[0].required = false

      me.rulesHeader.grossWt[0].required = false
      me.rulesHeader.packNum[0].required = false

      if (me.iemark === 'I') {
        me.rulesHeader.districtCode[0].required = false
        me.rulesHeader.districtPostCode[0].required = false
      }
      if (me.frmData.bondMark === '0') {
        me.rulesHeader.qty1[0].required = true
        if (me.iemark === 'I') {
          if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
            me.rulesHeader.qty2[0].required = true
          }
        } else {
          if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
            me.rulesHeader.qty2[0].required = true
          }
        }
      } else if (me.frmData.bondMark === '1') {
        if (me.iemark === 'I') {
          if (reqFields.includes('qty1')) {
            me.rulesHeader.qty1[0].required = true
          }
          if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
            me.rulesHeader.qty2[0].required = true
          }
        } else {
          if (reqFields.includes('qty1')) {
            me.rulesHeader.qty1[0].required = true
          }
          if (reqFields.includes('qty2') && !isNullOrEmpty(me.frmData.unit2)) {
            me.rulesHeader.qty2[0].required = true
          }
        }
      }

      if (reqFields.includes('grossWt')) {
        me.rulesHeader.grossWt[0].required = true
      }
      if (reqFields.includes('packNum')) {
        me.rulesHeader.packNum[0].required = true
      }

      if (me.iemark === 'I') {
        if (reqFields.includes('districtCode')) {
          me.rulesHeader.districtCode[0].required = true
          me.rulesHeader.districtPostCode[0].required = true
        }
      }
      if (reqFields.includes('netWt')) {
        me.rulesHeader.netWt[0].required = true
      }
      me.$nextTick(() => {
        if (me.$refs.fiQty1) {
          me.$refs.fiQty1.setRules()
        }
        if (me.$refs.fiQty2) {
          me.$refs.fiQty2.setRules()
        }
        if (me.$refs.fiDistrictCode) {
          me.$refs.fiDistrictCode.setRules()
        }
        if (me.$refs.fiGrossWt) {
          me.$refs.fiGrossWt.setRules()
        }
        if (me.$refs.fiPackNum) {
          me.$refs.fiPackNum.setRules()
        }
      })
    },
    /**
     * 根据企业编号修改信息内容(用于表体)
     * @param message
     * @returns {*}
     */
    setMsgByFacGNo(message) {
      let me = this
      if (!isNullOrEmpty(message) && !isNullOrEmpty(me.frmData.facGNo)) {
        return '企业料号: ' + me.frmData.facGNo + ' 存在问题: ' + message
      }
      return message
    }
  }
}
