<template>
  <XdoModal width="1024" mask v-model="show" title="数据提取"
            :closable="false" :footer-hide="true" :mask-closable="false">
    <a class="ivu-modal-close" @click="handleClose">
      <XdoIcon type="ios-close" style="font-size: 21px; color: #389DE9; line-height: 1px;"></XdoIcon>
    </a>
    <XdoCard :bordered="false">
      <div ref="area_head">
        <XdoBreadCrumb show-icon>
          <div class="dc-form-2">
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <Upload ref="upload" style="padding: 0;" class="dc-margin-right"
                    :headers="requestHeader" :show-upload-list="false"
                    :format="['xls','xlsx']" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    :action="ajaxUrl.uploadUrl" :data="uploadOption"
                    :before-upload="handleBeforeUpload" :on-success="handleSuccess">
              <XdoButton type="primary" icon="ios-cloud-upload-outline">导入条件</XdoButton>
            </Upload>
          </div>
        </XdoBreadCrumb>
      </div>
      <div ref="area_search">
        <div class="separateLine"></div>
        <XdoForm :label-width="72" class="dc-form dc-form-3" inline style="padding-top: 0;">
          <XdoFormItem prop="linkedNo" label="提取单号" style="padding: 0;">
            <xdo-select v-model="searchParam.linkedNo" :options="cmbSource.emsListNoData"></xdo-select>
            <!-- mixer -->
          </XdoFormItem>
          <XdoFormItem prop="linkedNo2" label="" class="dc-merge-2-4" :label-width="0">
            <XdoIInput type="text" v-model="searchParam.linkedNo2" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="bondMark" label="保完税标识">
            <xdo-select v-model="searchParam.bondMark" :options="this.importExportManage.bondedFlagMap4Erp"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="facGNo" label="企业料号">
            <XdoIInput type="text" v-model="searchParam.facGNo" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="copGNo" label="备案料号">
            <XdoIInput type="text" v-model="searchParam.copGNo" clearable></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="gmark" label="物料类型">
            <xdo-select v-model="searchParam.gmark" :options="this.importExportManage.gmarkMap"
                        :optionLabelRender="pcodeRender"></xdo-select>
          </XdoFormItem>
          <XdoFormItem prop="costCenter" label="成本中心">
            <XdoIInput type="text" v-model="searchParam.costCenter" clearable></XdoIInput>
          </XdoFormItem>
          <DcDateRange label="发票日期" @onDateRangeChanged="handleDateChange" v-if="isExitBond"></DcDateRange>
          <XdoFormItem prop="remark1" label="remark1" v-if="isExitBond">
            <XdoIInput type="text" v-model="searchParam.remark1"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="remark2" label="remark2" v-if="isExitBond">
            <XdoIInput type="text" v-model="searchParam.remark2"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="decPriceFlag" label="单价">
            <XdoCheckbox v-model="decPriceFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
          </XdoFormItem>
          <XdoFormItem prop="netWtFlag" label="净重">
            <XdoCheckbox v-model="netWtFlag" style="padding-top: 4px;">0或空</XdoCheckbox>
          </XdoFormItem>
        </XdoForm>
      </div>
    </XdoCard>
    <div class="action" ref="area_actions">
      <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
    </div>
    <div class="content">
      <DcAgGrid ref="table" v-show="!isConfirm" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="360"
                @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
      <div v-if="!isConfirm" class="page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                 :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                 @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
      </div>
      <DcAgGrid ref="table" v-show="isConfirm" :columns="grdErrConfig.gridColumns" :data="grdErrConfig.data" :height="360"></DcAgGrid>
      <div v-if="isConfirm" class="page">
        <XdoPage class="dc-page" show-total show-sizer :page-size-opts="grdErrConfig.pageSizeOpts"
                 :current="grdErrConfig.page" :page-size="grdErrConfig.limit" :total="grdErrConfig.dataTotal"
                 @on-change="pageErrChange" @on-page-size-change="pageSizeErrChange"/>
      </div>
    </div>
  </XdoModal>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import excel from '@/libs/excel_old'
  import { isNullOrEmpty, getKeyValue } from '@/libs/util'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { interimVerification, importExportManage } from '@/view/cs-common'
  import { columnsConfig, columnsErrConfig, excelColumnsConfig, columns } from './erpExtractColumns'
  import DcDateRange from '@/components/dc-date-range/dc-date-range'

  export default {
    name: 'erpExtract',
    components: {
      DcDateRange
    },
    mixins: [pms, commList, columns],
    props: {
      show: {
        type: Boolean,
        require: true
      },
      headId: {
        type: String,
        require: true
      },
      emsListNo: {
        type: String,
        require: true
      },
      iemark: {
        type: String,
        required: true,
        validate: function (value) {
          return ['I', 'E'].includes(value)
        }
      },
      bondMark:{
        type:String,
        validdate:function(value){
          return['','0','1'].includes(value)
        }
      }
    },
    data() {
      return {
        netWtFlag: false,
        decPriceFlag: false,
        searchParam: {
          linkedNo: '',
          linkedNo2: '',
          bondMark: '',
          facGNo: '',
          copGNo: '',
          netWt: null,
          invoiceDate: '',
          decPrice: null,
          gmark: '',
          costCenter: '',
          invoiceDateFrom:'',
          invoiceDateTo:'',
          remark1:'',
          remark2:''
        },
        toolbarEventMap: {
          'export': this.handleDownload,
          'extractByQuery': this.handleConfirm,
          'extractByChoose': this.handleExtractByChoose
        },
        actions: [],
        actionsComm: {
          needed: true,
          loading: false,
          disabled: false
        },
        cmbSource: {
          emsListNoData: []
        },
        ajaxUrl: {
          uploadUrl: csAPI.csImportExport.decErpIListN.importLinkedNo,
          getEmsListNo: '',
          selectAllPaged: '',
          extractConfirm: '',
          extractErpDataByChoose: ''
        },
        interimVerification: interimVerification,
        gmarkList: importExportManage.gmarkMap,
        importExportManage: importExportManage,
        isConfirm: false,
        grdErrConfig: {
          gridColumns: [],

          fullData: [],
          pagerData: {},
          page: 1,
          limit: 20,
          dataTotal: -1,
          data: [],

          exportTitle: '数据提取错误数据',
          exportColumns: [],
          pageSizeOpts: [10, 20, 50, 100]
        },
        requestHeader: {
          Authorization: 'Bearer ' + this.$store.state.token
        },
        uploadOption: {},
        uploadProps: {
          file: null
        }
      }
    },
    watch: {
      show: {
        immediate: true,
        handler: function (show) {
          if (show) {
            this.handleSearchSubmit()
          }
        }
      },
      emsListNo: {
        immediate: true,
        handler: function (hid) {
          let me = this
          if (isNullOrEmpty(hid)) {
            me.$set(me.cmbSource, 'emsListNoData', [])
          } else {
            me.$nextTick(() => {
              me.loadEmsListNoData()
            })
          }
        }
      },
      iemark: {
        immediate: true,
        handler: function (mark) {
          if (mark === 'I') {
            this.$set(this.ajaxUrl, 'getEmsListNo', csAPI.sapErp.extract.imports.getEmsListNo)
            this.$set(this.ajaxUrl, 'selectAllPaged', csAPI.sapErp.extract.imports.selectAllPaged)
            this.$set(this.ajaxUrl, 'extractConfirm', csAPI.sapErp.extract.imports.extractConfirm)
            this.$set(this.ajaxUrl, 'extractErpDataByChoose', csAPI.sapErp.extract.imports.checkLinkedNoBySid)
          } else if (mark === 'E') {
            this.$set(this.ajaxUrl, 'getEmsListNo', csAPI.sapErp.extract.exports.getEmsListNo)
            this.$set(this.ajaxUrl, 'selectAllPaged', csAPI.sapErp.extract.exports.selectAllPaged)
            this.$set(this.ajaxUrl, 'extractConfirm', csAPI.sapErp.extract.exports.extractConfirm)
            this.$set(this.ajaxUrl, 'extractErpDataByChoose', csAPI.sapErp.extract.exports.checkLinkedNoBySid)
          }
        }
      },
      'grdErrConfig.fullData': {
        handler: function () {
          this.errDataAnalysis()
        }
      },
      'grdErrConfig.limit': {
        handler: function () {
          this.errDataAnalysis()
        }
      },
      netWtFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'netWt', 1)
          } else {
            this.$set(this.searchParam, 'netWt', null)
          }
        }
      },
      decPriceFlag: {
        immediate: true,
        handler: function (flag) {
          if (flag) {
            this.$set(this.searchParam, 'decPrice', 1)
          } else {
            this.$set(this.searchParam, 'decPrice', null)
          }
        }
      }
    },

    computed:{
      isExitBond() {
        return this.iemark === 'E' && (this.bondMark === '0'|| this.bondMark === '1'||this.bondMark!=='')
      }
    },

    mounted() {
      let me = this
      let columns = getColumnsByConfig(me.totalColumns, columnsConfig)
      if(this.iemark === 'I'){
        columns = columns.filter(x=>x.key!=='invoiceDate')
      }

      me.gridConfig.gridColumns = [{
        width: 36,
        fixed: 'left',
        align: 'center',
        key: 'selection',
        type: 'selection'
      }, ...columns]

      let errColumns = getColumnsByConfig(me.errColumns, columnsErrConfig)
      if(this.iemark === 'I'){
        errColumns = errColumns.filter(x=>x.key!=='invoiceDate')
      }
      me.grdErrConfig.gridColumns = getColumnsByConfig(errColumns, columnsErrConfig)
      me.grdErrConfig.exportColumns = getExcelColumnsByConfig(errColumns, excelColumnsConfig)
      me.loadFunctions().then(() => {
        if (typeof me.actionLoad === "function") {
          me.actionLoad()
        }
      })
    },
    methods: {
      actionLoad() {
        let me = this
        me.actions = []
        me.actions.push({
          ...me.actionsComm,
          label: '提取勾选',
          key: 'xdo-btn-edit',
          icon: 'ios-checkmark',
          command: 'extractByChoose'
        }, {
          ...me.actionsComm,
          label: '全部提取',
          icon: 'md-done-all',
          key: 'xdo-btn-upload',
          command: 'extractByQuery'
        }, {
          ...me.actionsComm,
          command: 'export',
          label: '导出错误数据',
          key: 'xdo-btn-download',
          icon: 'ios-cloud-download-outline'
        })
      },
      /**
       * 抓取所以可用的关联编号
       */
      loadEmsListNoData() {
        let me = this
        me.$http.post(me.ajaxUrl.getEmsListNo).then(res => {
          let emsListNoList = res.data.data || []
          me.$set(me.cmbSource, 'emsListNoData', emsListNoList.sort())
        }).catch(() => {
          me.$set(me.cmbSource, 'emsListNoData', [])
        }).finally(() => {
          me.handleSearchSubmit()
        })
      },
      filterMethod(value, option) {
        if (isNullOrEmpty(value)) {
          value = ''
        }
        if (typeof value !== 'string') {
          value = value.toString()
        }
        let opVal = option
        if (isNullOrEmpty(opVal)) {
          opVal = ''
        }
        if (typeof opVal !== 'string') {
          opVal = opVal.toString()
        }
        return opVal.toUpperCase().indexOf(value.toUpperCase()) !== -1
      },
      /**
       * 获取查询条件
       */
      getSearchParams() {
        return this.searchParam
      },

      /**
       * 点击查询按钮
       */
      handleSearchSubmit() {
        this.isConfirm = false
        this.pageParam.page = 1
        this.getList()
        this.setToolbarProperty('export', 'disabled', true)
        // 清空错误数据
        this.grdErrDataClear()
      },
      /**
       * 错误数据清空
       */
      grdErrDataClear() {
        this.grdErrConfig.page = 1
        this.grdErrConfig.data = []
        this.grdErrConfig.dataTotal = 0
        this.grdErrConfig.fullData = []
        this.grdErrConfig.pagerData = {}

        this.isConfirm = false
      },
      /**
       * 根据勾选提取
       */
      handleExtractByChoose() {
        let me = this
        if (me.checkRowSelected('提取勾选')) {
          me.setButtonLoading('extractByChoose', true)
          let url = me.ajaxUrl.extractErpDataByChoose
          if (!isNullOrEmpty(me.headId)) {
            url += '/' + me.headId
          }
          let params = me.getSelectedParams()
          me.$http.post(url + '/' + params, {noIntercept: true}).then(res => {
            if (res && res.data) {
              if (res.data.success === true) {
                if (res.data.data && Array.isArray(res.data.data)) {
                  me.setToolbarProperty('export', 'disabled', (res.data.data.length < 1))

                  me.grdErrConfig.page = 1
                  me.grdErrConfig.dataTotal = res.data.data.length
                  me.grdErrConfig.fullData = res.data.data
                  me.isConfirm = true
                  me.$Message.success(res.data.message)
                } else {
                  me.$Message.success(res.data.message)
                  me.handleClose()
                }
              } else {
                me.grdErrDataClear()
                if (!isNullOrEmpty(res.data.message)) {
                  me.$Message.error(res.data.message)
                } else {
                  me.$Message.error('数据提取失败!')
                }
              }
            } else {
              me.grdErrDataClear()
              if (res && !isNullOrEmpty(res.message)) {
                me.$Message.success(res.message)
              } else {
                me.$Message.success('数据提取失败!')
              }
            }
          }).catch(() => {
          }).finally(() => {
            me.setButtonLoading('extractByChoose', false)
          })
        }
      },
      /**
       * 根据查询条件提取
       */
      handleConfirm() {
        let me = this
        me.setToolbarLoading('extractByQuery', true)
        let confirmData = Object.assign({
          headId: me.headId
        }, me.searchParam)
        me.$http.post(me.ajaxUrl.extractConfirm, confirmData, {noIntercept: true}).then(res => {
          if (res && res.data) {
            if (res.data.success === true) {
              if (res.data.data && Array.isArray(res.data.data)) {
                me.setToolbarProperty('export', 'disabled', (res.data.data.length < 1))

                me.grdErrConfig.page = 1
                me.grdErrConfig.dataTotal = res.data.data.length
                me.grdErrConfig.fullData = res.data.data
                me.isConfirm = true
                me.$Message.success(res.data.message)
              } else {
                me.$Message.success(res.data.message)
                me.handleClose()
              }
            } else {
              me.grdErrDataClear()
              if (!isNullOrEmpty(res.data.message)) {
                me.$Message.error(res.data.message)
              } else {
                me.$Message.error('数据提取失败!')
              }
            }
          } else {
            me.grdErrDataClear()
            if (res && !isNullOrEmpty(res.message)) {
              me.$Message.success(res.message)
            } else {
              me.$Message.success('数据提取失败!')
            }
          }
        }).catch(() => {
        }).finally(() => {
          me.gridConfig.selectRows = []
          me.setToolbarLoading('extractByQuery', false)
        })
      },
      errDataAnalysis() {
        this.$set(this.grdErrConfig, 'pagerData', {1: []})

        let fullData = this.grdErrConfig.fullData || []
        let pageSize = this.grdErrConfig.limit
        let pageIndex = 1
        let dataIndex = 0

        for (let data of fullData) {
          if (dataIndex < pageSize) {
            dataIndex++
            this.grdErrConfig.pagerData[pageIndex].push(data)
          } else {
            dataIndex = 1
            pageIndex++
            this.grdErrConfig.pagerData[pageIndex] = [data]
          }
        }

        this.$set(this.grdErrConfig, 'data', this.grdErrConfig.pagerData[1])
      },
      pageErrChange(page) {
        this.grdErrConfig.page = page
        this.$set(this.grdErrConfig, 'data', this.grdErrConfig.pagerData[this.grdErrConfig.page])
      },
      pageSizeErrChange(pageSize) {
        this.grdErrConfig.limit = pageSize
        this.$set(this.grdErrConfig, 'data', this.grdErrConfig.pagerData[1])
      },
      /**
       * 处理导出数据
       */
      dealWithExportData() {
        let resultData = []
        let tmpData
        for (let data of this.grdErrConfig.fullData) {
          if (data) {
            tmpData = JSON.parse(JSON.stringify(data))
            if (data.hasOwnProperty('bondMark')) {
              tmpData['bondMark'] = getKeyValue(this.interimVerification.BONDED_FLAG_MAP, data['bondMark'])
            }
            if (data.hasOwnProperty('gmark')) {
              tmpData['gmark'] = getKeyValue(this.gmarkList, data['gmark'])
            }
            if (data.hasOwnProperty('curr')) {
              tmpData['curr'] = getKeyValue(this.pcodeGet(this.pcode.curr_outdated, data['curr']), data['curr'])
            }
            if (data.hasOwnProperty('country')) {
              tmpData['country'] = getKeyValue(this.pcodeGet(this.pcode.country_outdated, data['country']), data['country'])
            }
            if (data.hasOwnProperty('dutyMode')) {
              tmpData['dutyMode'] = getKeyValue(this.pcodeGet(this.pcode.levymode, data['dutyMode']), data['dutyMode'])
            }
            resultData.push(tmpData)
          }
        }
        return resultData
      },
      /**
       * 错误数据导出
       */
      handleDownload() {
        if (this.grdErrConfig.fullData.length > 0) {
          let exportData = this.dealWithExportData()
          const params = {
            key: this.grdErrConfig.exportColumns.map(item => {
              if (item.key) {
                return item.key
              }
            }),
            title: this.grdErrConfig.exportColumns.map(item => {
              if (item.value) {
                return item.value
              }
            }),
            autoWidth: true,
            data: exportData,
            filename: this.grdErrConfig.exportTitle
          }
          excel.export_array_to_excel(params)
        } else {
          this.$Message.warning('没有错误数据可供导出!')
        }
      },
      /**
       * 关闭
       */
      handleClose() {
        this.$emit('update:show', false)
        this.$emit('import:success', false)
      },
      rowClassName() {
        return 'myRow'
      },
      // 导入查询条件
      valid() {
        if (this.uploadProps.file) {
          if (this.uploadProps.file.size > 5120000) {
            this.$Message.warning('文件 ' + this.uploadProps.file.name + ' 太大，不能超过 5M。')
            return false
          }
          let fileType = this.uploadProps.file.type
          let typeOne = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          let typeTwo = 'application/vnd.ms-excel'
          if (typeOne !== fileType && typeTwo !== fileType) {
            this.$Message.warning('文件 ' + this.uploadProps.file.name + ' 格式不正确，请上传 xls 或 xlsx 格式的Excel文件。')
            return false
          }
          return true
        } else {
          this.$Message.warning('校验前请先导入文件')
        }
        return false
      },
      handleBeforeUpload(file) {
        this.$set(this.uploadProps, 'file', file)
        return this.valid()
      },
      handleSuccess(response) {
        let me = this
        if (response.success === true) {
          me.$set(me.searchParam, 'linkedNo2', response.data)
          me.handleSearchSubmit()
        } else {
          me.$Message.warning(response.message)
        }
      },
      handleDateChange(values) {
        this.searchParam.invoiceDateFrom = values[0]
        this.searchParam.invoiceDateTo = values[1]
      }
    }
  }
</script>

<style lang="less" scoped>
  .buttonCol {
    padding-top: 0;
  }

  .ivu-form-item-content {
    white-space: nowrap !important;
  }

  .myRow td {
    height: 32px !important;
  }

  .ivu-tabs-bar .ivu-tabs-nav-right button:hover {
    background-color: #57a3f3;
    border-color: #57a3f3;
  }

  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }

  .action {
    padding: 0 !important;
  }

  .dc-form-2 {
    display: grid;
    grid-column-gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }

  .dc-form-2 > div {
    grid-column: 1/2;
  }
</style>
