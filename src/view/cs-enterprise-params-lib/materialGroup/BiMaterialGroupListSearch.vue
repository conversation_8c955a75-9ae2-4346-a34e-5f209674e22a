<template>
  <section>
    <XdoForm ref='formInline' class='dc-form' :model='searchParams' label-position='right' :label-width='100' inline>
      <XdoFormItem prop='matGroup' label='物料群组'>
        <XdoIInput type='text' v-model='searchParams.matGroup'></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop='matGroupName' label='物料群组说明'>
        <XdoIInput type='text' v-model='searchParams.matGroupName'></XdoIInput>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
export default {
  name: 'BiMaterialGroupListSearch',
  data() {
    return {
      searchParams: {
        matGroup: '',
        matGroupName: ''
      },
      searchLines: 2
    }
  }
}
</script>

<style scoped>
</style>
