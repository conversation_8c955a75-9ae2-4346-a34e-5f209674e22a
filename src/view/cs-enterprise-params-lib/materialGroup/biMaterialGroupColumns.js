import { baseColumnsShow, baseColumnsExport, baseColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
   'sid'
  , 'matGroup'
  , 'matGroupName'
  , 'note'
]
const columnsConfig = [
  ...baseColumnsShow
  , ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport
  , ...commColumns
]

const columns = {
  mixins:[baseColumns],
  data() {
    let baseFields = this.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          title: '物料群组',
          align: 'center',
          key: 'matGroup'
        },
        {
          title: '物料群组说明',
          align: 'center',
          key: 'matGroupName'
        },
        {
          title: '备注',
          align: 'center',
          key: 'note'
        }
      ]
    }
  }
}
export {
  columns,
  excelColumnsConfig,
  columnsConfig
}
