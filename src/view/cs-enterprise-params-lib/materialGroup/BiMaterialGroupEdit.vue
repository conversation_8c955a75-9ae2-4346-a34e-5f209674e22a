<template>
  <section class="xdo-enter-root" v-focus>
    <XdoForm ref="dataForm" class="dc-form dc-form-3 xdo-enter-form" :model="frmData" :rules="rulesHeader" label-position="right" :label-width="150">
      <XdoCard :bordered="false" class="dc-merge-1-4 ieLogisticsTrackingCard" title="基础信息">
        <div class="dc-form dc-form-3" style="padding-right: 10px;">
          <XdoFormItem prop="matGroup" label="物料群组">
            <XdoIInput type="text" v-model="frmData.matGroup" :maxlength="10" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="matGroupName" label="物料群组说明">
            <XdoIInput type="text" v-model="frmData.matGroupName" :maxlength="50" :disabled="showDisable"></XdoIInput>
          </XdoFormItem>
          <XdoFormItem prop="note" label="备注">
            <XdoIInput type="text" v-model="frmData.note" :disabled="showDisable" :maxlength="255"></XdoIInput>
          </XdoFormItem>
        </div>
      </XdoCard>
    </XdoForm>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}</Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
import { csAPI } from '@/api'
import { materialEdit } from '../materialGroup/js/materialEdit'

export default {
  name: 'BiMaterialGroupEdit',
  mixins: [materialEdit],
  data() {
    return {
      ajaxUrl: {
        insert: csAPI.enterpriseParamsLib.material.insert,
        update: csAPI.enterpriseParamsLib.material.update
      },
      rulesHeader: {
        matGroup: [{required: true, message: '不能为空!', trigger: 'blur'}]
      }
    }
  },
  methods: {
    getDefaultData() {
      return {
        sid: '',
        matGroup: '',
        matGroupName: '',
        note: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ieLogisticsTrackingCard .ivu-card-head {
  padding: 5px 10px !important;
}

.ieLogisticsTrackingCard .ivu-card-body {
  padding: 8px 8px 2px 8px;
}
</style>
