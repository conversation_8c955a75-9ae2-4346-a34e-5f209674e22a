<template>
  <section>
    <div v-show="showList" ref="billBase">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <BiMaterialGroupListSearch ref="listSearch"></BiMaterialGroupListSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <DcAgGrid ref="table" :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                  @onAgCellOperation="onAgCellOperation" @on-selection-change="handleSelectionChange"></DcAgGrid>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
    <BiMaterialGroupEdit v-if="!showList" :editConfig="editConfig" :parentConfig="parentConfig"
                                @onEditBack="editBack"></BiMaterialGroupEdit>
  </section>
</template>
<script>

import { csAPI } from '@/api'
import { material } from '../materialGroup/js/material'
import BiMaterialGroupListSearch from '../materialGroup/BiMaterialGroupListSearch'
import BiMaterialGroupEdit from '../materialGroup/BiMaterialGroupEdit'
import { getColumnsByConfig } from '@/common'
import { columns, columnsConfig } from '../materialGroup/biMaterialGroupColumns'

export default {
  name: 'biMaterialGroupList',
  components: {
    BiMaterialGroupListSearch,
    BiMaterialGroupEdit
  },
  mixins: [material,columns],
  props: {
    parentConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data(){
    return {
      searchLines: 2,
      ajaxUrl: {
        insert: csAPI.enterpriseParamsLib.material.insert,
        update: csAPI.enterpriseParamsLib.material.update,
        delete: csAPI.enterpriseParamsLib.material.delete,
        selectAllPaged: csAPI.enterpriseParamsLib.material.selectAllPaged
      }
    }
  },
  mounted: function () {
    let me = this
    me.gridConfig.gridColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
  },
  methods:{
    /**
     * 获取查询条件(可外部覆盖)
     */
    getSearchParams() {
      return Object.assign({}, (this.$refs.listSearch ? this.$refs.listSearch.searchParams : {}))
    }
  }
}
</script>
<style lang='less' scoped>
</style>
