import pms from '@/libs/pms'
import { isNullOrEmpty } from '@/libs/util'
import ParamRoyaltySearch from '../param-royalty-search'
import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { importExportManage, taxExemptionEquipment } from '@/view/cs-common'
import { columnsConfig, excelColumnsConfig, columns } from './paramRoyaltyColumns'

export const paramList = {
  components: {
    ParamRoyaltySearch
  },
  mixins: [commList, columns, pms],
  data() {
    return {
      toolbarEventMap: {
        'add': this.rowAdd,
        'edit': this.rowEdit,
        'delete': this.handleDelete,
        'export': this.handleExport
      },
      // 查询条件行数
      searchLines: 1,
      cmbSource: {
        customParamCodeData: []
      },
      importExportManage: importExportManage,
      taxExemptionEquipment: taxExemptionEquipment
    }
  },
  mounted: function () {
    this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
    this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
    this.loadFunctions().then()
  },
  methods: {
    rowAdd() {
      this.addEmptyRow({
        iemark: '',
        royalityNo: '',
        royalityFee: '',
        note: '',
        insertUser: this.$store.state.user.userNo,
        insertTime: (new Date()).toLocaleDateString()
      })
    },
    rowEdit() {
      if (this.checkRowSelected('编辑', true)) {
        let theRow = this.gridConfig.selectRows[0]
        this.gridConfig.selectRows = []
        let index = -1
        let theInd = -1
        for (let row of this.gridConfig.data) {
          theInd++
          if (row.sid === theRow.sid) {
            index = theInd
          }
        }
        if (index > -1) {
          this.setRowEdit(index)
        } else {
          console.error('当前选中的数据存在问题!')
        }
      }
    },
    handleDelete() {
      let me = this,
        newRows = me.gridConfig.selectRows.filter(item => {
          return isNullOrEmpty(item.sid)
        })
      if (Array.isArray(newRows) && newRows.length > 0) {
        me.$Message.warning('仅可删除已保存的数据!')
        return
      }
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleExport() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    }
  }
}
