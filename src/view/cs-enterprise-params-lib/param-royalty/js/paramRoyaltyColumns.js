import { baseColumnsShow, baseColumnsExport, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'iemark'
  , 'royalityNo'
  , 'royalityFee'
  , 'note'
  , 'insertUser'
  , 'insertTime'
  ,'userName'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [baseEditColumns],
  data() {
    let me = this
    let baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 120,
          key: 'iemark',
          align: 'center',
          title: '进出口类型',
          render: (h, params) => {
            return me.cmbRender(h, params, me.importExportManage.I_E_MARK_MAP)
          }
        },
        {
          width: 220,
          align: 'center',
          key: 'royalityNo',
          title: '特许权关联号',
          render: (h, params) => {
            return me.inputRender(h, params, 100)
          }
        },
        {
          key: 'note',
          title: '备注',
          minWidth: 220,
          align: 'center',
          render: (h, params) => {
            return me.inputRender(h, params, 500)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          width: 160,
          title: '录入人',
          align: 'center',
          key: 'userName',
          ellipsis: true,
          tooltip: true
        },
        {
          width: 100,
          align: 'center',
          title: '录入日期',
          key: 'insertTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
