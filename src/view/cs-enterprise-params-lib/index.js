import { namespace } from '@/project'
import paramPortList from './param-port/param-port-list'
import RoyaltyList from './param-royalty/param-royalty-list'
import biMaterialGroupList from './materialGroup/BiMaterialGroupList'
import EnterpriseParamsLibUnitList from './param-unit/param-unit-list'
import EnterpriseParamsLibCurrList from './param-curr/param-curr-list'
import EnterpriseParamsLibGMarkList from './param-gMark/param-gMark-list'
import AvoidParamsList from './param-avoid-params/param-avoid-params-list'
import EnterpriseParamsLibCountryList from './param-country/param-country-list'
import EnterpriseParamsLibBondMarkList from './param-bondMark/param-bondMark-list'
import logisticsAttributesList from './logistics-attributes/logistics-attributes-list'
import EnterpriseparamCustomDocNoList from './param-custom-doc-no/param-custom-doc-no-list'
import EnterpriseparamImportMethodList from './param-import-method/param-import-method-list'
import EnterpriseparamExportMethodList from './param-export-method/param-export-method-list'
import EnterpriseParamsLibvehicleUsageList from './param-vehicleUsage/param-vehicleUsage-list'
import paramGoodsAttributesList from './param-goods-attributes/param-goods-attributes-list'
import BiPremiumRate from './param-premiumRate/param-premiumRate-list'

export default [
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibUnitList',
    name: 'paramUnitList',
    meta: {
      icon: 'ios-document',
      title: '计量单位'
    },
    component: EnterpriseParamsLibUnitList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibCurrList',
    name: 'paramCurrList',
    meta: {
      icon: 'ios-document',
      title: '币制'
    },
    component: EnterpriseParamsLibCurrList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibCountryList',
    name: 'paramCountryList',
    meta: {
      icon: 'ios-document',
      title: '国别'
    },
    component: EnterpriseParamsLibCountryList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibBondMarkList',
    name: 'paramBondMarkList',
    meta: {
      icon: 'ios-document',
      title: '保完税标识'
    },
    component: EnterpriseParamsLibBondMarkList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibGMarkList',
    name: 'paramGMarkList',
    meta: {
      icon: 'ios-document',
      title: '物料类型标识'
    },
    component: EnterpriseParamsLibGMarkList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamCustomDocNoList',
    name: 'paramCustomDocNoList',
    meta: {
      icon: 'ios-document',
      title: '自定义单据编号'
    },
    component: EnterpriseparamCustomDocNoList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseparamImportMethodList',
    name: 'paramImportMethodList',
    meta: {
      icon: 'ios-document',
      title: '进口方式'
    },
    component: EnterpriseparamImportMethodList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseparamExportMethodList',
    name: 'paramExportMethodList',
    meta: {
      icon: 'ios-document',
      title: '出口方式'
    },
    component: EnterpriseparamExportMethodList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/paramRoyaltyList',
    name: 'paramRoyaltyList',
    meta: {
      icon: 'ios-document',
      title: '特许权使用费'
    },
    component: RoyaltyList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/paramAvoidParamsList',
    name: 'paramAvoidParamsList',
    meta: {
      icon: 'ios-document',
      title: '免表参数'
    },
    component: AvoidParamsList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/EnterpriseParamsLibvehicleUsageList',
    name: 'EnterpriseParamsLibvehicleUsageList',
    meta: {
      icon: 'ios-document',
      title: '车柜类型'
    },
    component: EnterpriseParamsLibvehicleUsageList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/paramGoodsAttributesList',
    name: 'paramGoodsAttributesList',
    meta: {
      icon: 'ios-document',
      title: '货物属性'
    },
    component: paramGoodsAttributesList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/logisticsAttributesList',
    name: 'logisticsAttributesList',
    meta: {
      title: '物流属性',
      icon: 'ios-document'
    },
    component: logisticsAttributesList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/paramPortList',
    name: 'paramPortList',
    meta: {
      title: '港口',
      icon: 'ios-document'
    },
    component: paramPortList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/biMaterialGroup',
    name: 'biMaterialGroupList',
    meta: {
      title: '港口',
      icon: 'ios-document'
    },
    component: biMaterialGroupList
  },
  {
    path: '/' + namespace + '/enterpriseParamsLib/biPremiumRate',
    name: 'biPremiumRate',
    meta: {
      icon: 'ios-document',
      title: '保费率'
    },
    component: BiPremiumRate
  }
]

