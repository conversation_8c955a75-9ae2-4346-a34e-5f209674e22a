import pms from '@/libs/pms'
import { csAPI } from '@/api'
import { isNullOrEmpty } from '@/libs/util'
import ParamSearch from '../components/param-search'
import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
import { commList } from '@/view/cs-interim-verification/comm/commList'
import { columnsConfig, excelColumnsConfig, columns } from '../js/paramColumns'
import { productClassify, deepProcessing, taxExemptionEquipment } from '@/view/cs-common'

export const paramList = {
  components: {
    ParamSearch
  },
  mixins: [commList, columns, pms],
  data() {
    return {
      // 查询条件行数
      searchLines: 1,
      cmbSource: {
        customParamCodeData: []
      },
      toolbarEventMap: {
        'add': this.rowAdd,
        'edit': this.rowEdit,
        'delete': this.handleDelete,
        'export': this.handleExport
      },
      deepProcessing: deepProcessing,
      productClassify: productClassify,
      taxExemptionEquipment: taxExemptionEquipment,
      ajaxUrl: {
        insert: csAPI.enterpriseParamsLib.comm.insert,
        update: csAPI.enterpriseParamsLib.comm.update,
        delete: csAPI.enterpriseParamsLib.comm.delete,
        exportUrl: csAPI.enterpriseParamsLib.comm.exportUrl,
        getByParamType: csAPI.enterpriseParamsLib.comm.getByParamType,
        selectAllPaged: csAPI.enterpriseParamsLib.comm.selectAllPaged
      }
    }
  },
  /**
   * 监视
   */
  watch: {
    metaCode: {
      immediate: true,
      handler: function (metaCode) {
        let me = this
        if (metaCode !== null) {
          me.pcodeList(metaCode).then(res => {
            me.$set(me.cmbSource, 'customParamCodeData', res)
          }).catch(() => {
          })
        }
      }
    },
    optionCode: {
      immediate: true,
      handler: function (optionCode) {
        let me = this
        if (optionCode !== null) {
          me.$set(me.cmbSource, 'customParamCodeData', optionCode)
        }
      }
    }
  },
  mounted: function () {
    let me = this
    let tmpColumns = getColumnsByConfig(me.totalColumns, columnsConfig)
    for (let col of tmpColumns) {
      if (col.key === 'paramsCode') {
        col.title = me.paramsCodeLabel
      } else if (col.key === 'customParamCode') {
        col.title = me.customParamCodeLabel
      }
    }
    let tmpExportColumns = getExcelColumnsByConfig(me.totalColumns, excelColumnsConfig)
    for (let col of tmpExportColumns) {
      if (col.key === 'paramsCode') {
        col.title = me.paramsCodeLabel
      } else if (col.key === 'customParamCode') {
        col.title = me.customParamCodeLabel
      }
    }
    me.gridConfig.gridColumns = me.columnsSort(tmpColumns)
    me.gridConfig.exportColumns = me.exportColsSort(tmpExportColumns)
    me.loadFunctions().then()
  },
  methods: {
    columnsSort(columns) {
      return columns
    },
    exportColsSort(columns) {
      return columns
    },
    rowAdd() {
      let me = this,
        newData = {
          note: '',
          paramsCode: '',
          customParamCode: '',
          paramsType: me.paramsType,
          insertUser: me.$store.state.user.userNo,
          insertTime: (new Date()).toLocaleDateString()
        }
      me.addEmptyRow(newData)
    },
    /**
     * 编辑
     */
    rowEdit() {
      let me = this
      if (me.checkRowSelected('编辑', true)) {
        let theRow = me.gridConfig.selectRows[0]
        me.gridConfig.selectRows = []
        let index = -1
        let theInd = -1
        for (let row of me.gridConfig.data) {
          theInd++
          if (row.sid === theRow.sid) {
            index = theInd
          }
        }
        if (index > -1) {
          me.setRowEdit(index)
        } else {
          console.error('当前选中的数据存在问题!')
        }
      }
    },
    /**
     * 删除
     */
    handleDelete() {
      let me = this,
        newRows = me.gridConfig.selectRows.filter(item => {
          return isNullOrEmpty(item.sid)
        })
      if (Array.isArray(newRows) && newRows.length > 0) {
        me.$Message.warning('仅可删除已保存的数据!')
        return
      }
      me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
    },
    handleExport() {
      let me = this
      me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
    },
    getSearchParams() {
      return Object.assign({}, (this.$refs.headSearch ? this.$refs.headSearch.searchParam : {}), {inType: '1'})
    }
  },
  /**
   * 计算
   */
  computed: {
    metaCode() {
      if (['UNIT', 'INV_UNIT'].includes(this.paramsType)) {
        return this.pcode.unit
      } else if (['INV_CURR', 'CURR'].includes(this.paramsType)) {
        return this.pcode.curr_outdated
      } else if (['INV_STATE', 'COUNTRY'].includes(this.paramsType)) {
        return this.pcode.country_outdated
      } else if (['INV_TRAF'].includes(this.paramsType)) {
        return this.pcode.transf
      } else if (['INV_WRAP'].includes(this.paramsType)) {
        return this.pcode.wrap
      } else {
        return ''
      }
    },
    optionCode() {
      if (this.paramsType === 'BONDMARK') {
        return this.productClassify.BONDED_FLAG_SELECT
      } else if (this.paramsType === 'GMARK') {
        return this.productClassify.GMARK_SELECT
      } else if (this.paramsType === 'IN_WAY') {
        return this.deepProcessing.IMPORT_METHOD_MAP
      } else if (this.paramsType === 'OUT_WAY') {
        return this.deepProcessing.EXPORT_METHOD_MAP
      } else if (this.paramsType === 'ROYALTY') {
        return this.taxExemptionEquipment.ROYALTY_MAP
      } else if (this.paramsType === 'AVOID_PARAMS') {
        return this.taxExemptionEquipment.AVOID_PARAMS_MAP
      } else {
        return ''
      }
    }
  }
}
