<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="paramsCode" :label="paramsCodeLabel">
        <XdoIInput type="text" v-model="searchParam.paramsCode" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="customParamCode" :label="customParamCodeLabel">
        <xdo-select v-if="isPcode" v-model="searchParam.customParamCode" :meta="metaCode"
                    :asyncOptions="pcodeList" :optionLabelRender="pcodeRender"></xdo-select>
        <xdo-select v-if="!isPcode" v-model="searchParam.customParamCode" :options="optionCode"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  import { productClassify, deepProcessing, taxExemptionEquipment } from '@/view/cs-common'

  export default {
    name: 'paramSearch',
    props: {
      paramsType: {
        type: String,
        required: true,
        validate: function (value) {
          return ['UNIT', 'CURR', 'COUNTRY', 'BONDMARK', 'GMARK', 'IN_WAY', 'OUT_WAY', 'ROYALTY', 'AVOID_PARAMS',
            'INV_UNIT', 'INV_CURR', 'INV_STATE', 'INV_TRAF', 'INV_WRAP'].includes(value)
        }
      },
      isPcode: {
        type: Boolean,
        default: true,
        required: false
      }
    },
    data() {
      return {
        searchParam: {
          paramsCode: '',
          customParamCode: '',
          paramsType: this.paramsType
        },
        deepProcessing: deepProcessing,
        productClassify: productClassify,
        taxExemptionEquipment: taxExemptionEquipment
      }
    },
    computed: {
      paramsCodeLabel() {
        let me = this
        if (me.paramsType === 'UNIT') {
          return '企业计量单位'
        } else if (me.paramsType === 'CURR') {
          return '企业币制'
        } else if (me.paramsType === 'COUNTRY') {
          return '企业国别'
        } else if (me.paramsType === 'BONDMARK') {
          return '企业保完税标识'
        } else if (me.paramsType === 'GMARK') {
          return '企业物料类型'
        } else if (me.paramsType === 'IN_WAY') {
          return '进口方式'
        } else if (me.paramsType === 'OUT_WAY') {
          return '出口方式'
        } else if (me.paramsType === 'ROYALTY') {
          return '机种型号'
        } else if (me.paramsType === 'AVOID_PARAMS') {
          return '参数代码'
        } else if (me.paramsType === 'INV_UNIT') {
          return '发票计量单位'
        } else if (me.paramsType === 'INV_CURR') {
          return '发票币制'
        } else if (me.paramsType === 'INV_STATE') {
          return '发票国别'
        } else if (me.paramsType === 'INV_TRAF') {
          return '发票运输方式'
        } else if (me.paramsType === 'INV_WRAP') {
          return '发票包装种类'
        } else {
          return '企业参数类型'
        }
      },
      customParamCodeLabel() {
        let me = this
        if (['UNIT', 'INV_UNIT'].includes(me.paramsType)) {
          return '海关计量单位'
        } else if (['CURR', 'INV_CURR'].includes(me.paramsType)) {
          return '海关币制'
        } else if (['COUNTRY', 'INV_STATE'].includes(me.paramsType)) {
          return '海关国别'
        } else if (me.paramsType === 'BONDMARK') {
          return '海关保完税标识'
        } else if (me.paramsType === 'GMARK') {
          return '海关物料类型'
        } else if (me.paramsType === 'IN_WAY') {
          return '转换后进口方式'
        } else if (me.paramsType === 'OUT_WAY') {
          return '转换后出口方式'
        } else if (me.paramsType === 'ROYALTY') {
          return '特许权使用费'
        } else if (me.paramsType === 'AVOID_PARAMS') {
          return '参数类型'
        } else if (me.paramsType === 'INV_TRAF') {
          return '运输方式'
        } else if (me.paramsType === 'INV_WRAP') {
          return '包装种类'
        } else {
          return '海关参数类型'
        }
      },
      metaCode() {
        let me = this
        if (['UNIT', 'INV_UNIT'].includes(me.paramsType)) {
          return me.pcode.unit
        } else if (['CURR', 'INV_CURR'].includes(me.paramsType)) {
          return me.pcode.curr_outdated
        } else if (['COUNTRY', 'INV_STATE'].includes(me.paramsType)) {
          return me.pcode.country_outdated
        } else if (['INV_TRAF'].includes(me.paramsType)) {
          return me.pcode.transf
        } else if (['INV_WRAP'].includes(me.paramsType)) {
          return me.pcode.wrap
        } else {
          return ''
        }
      },
      optionCode() {
        let me = this
        if (me.paramsType === 'BONDMARK') {
          return me.productClassify.BONDED_FLAG_SELECT
        } else if (me.paramsType === 'GMARK') {
          return me.productClassify.GMARK_SELECT
        } else if (me.paramsType === 'IN_WAY') {
          return me.deepProcessing.IMPORT_METHOD_MAP
        } else if (me.paramsType === 'OUT_WAY') {
          return me.deepProcessing.EXPORT_METHOD_MAP
        } else if (me.paramsType === 'ROYALTY') {
          return me.taxExemptionEquipment.ROYALTY_MAP
        } else if (me.paramsType === 'AVOID_PARAMS') {
          return me.taxExemptionEquipment.AVOID_PARAMS_MAP
        } else {
          return ''
        }
      }
    }
  }
</script>

<style scoped>
</style>
