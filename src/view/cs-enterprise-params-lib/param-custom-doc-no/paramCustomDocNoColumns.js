import { baseColumnsShow, baseColumnsExport, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'moduleName'
  , 'tradeMode'
  , 'fixedPrefix'
  , 'dateFmt'
  , 'seqNoDigits'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [ baseEditColumns ],
  data () {
    let me = this
    let baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 100,
          align: 'center',
          title: '模块名称',
          key: 'moduleName',
          render: (h, params) => {
            return me.cmbRender(h, params, me.enterpriseParamsLib.BUSINESS_MODULE_MAP, '', true)
          }
        },
        {
          width: 200,
          align: 'center',
          key: 'tradeMode',
          title: '监管方式',
          render: (h, params) => {
            return me.cmbRender(h, params, me.cmbSource.tradeModeData, '', true)
          },
          ellipsis: true,
          tooltip: true
        },
        {
          minWidth: 150,
          title: '固定值',
          align: 'center',
          key: 'fixedPrefix',
          render: (h, params) => {
            return me.inputRender(h, params, 30)
          }
        },
        {
          minWidth: 150,
          key: 'dateFmt',
          title: '日期值',
          align: 'center',
          render: (h, params) => {
            return me.cmbRender(h, params, me.enterpriseParamsLib.DATETIME_FORMAT_MAP)
          }
        },
        {
          minWidth: 100,
          align: 'center',
          title: '流水位数',
          key: 'seqNoDigits',
          render: (h, params) => {
            return me.numberRender(h, params, 1)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
