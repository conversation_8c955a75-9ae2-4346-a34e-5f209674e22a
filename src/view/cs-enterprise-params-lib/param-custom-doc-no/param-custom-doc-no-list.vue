<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <ParamSearch ref="headSearch" :params-type="paramsType"></ParamSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="gridConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import { isNullOrEmpty } from '@/libs/util'
  import { enterpriseParamsLib } from '@/view/cs-common'
  import ParamSearch from './param-custom-doc-no-search'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { getColumnsByConfig, getExcelColumnsByConfig } from '@/common'
  import { columnsConfig, excelColumnsConfig, columns } from './paramCustomDocNoColumns'

  export default {
    name: 'paramCustomDocNoList',
    components: {
      ParamSearch
    },
    mixins: [commList, pms, columns],
    data() {
      return {
        ajaxUrl: {
          insert: csAPI.enterpriseParamsLib.customDocNo.insert,
          update: csAPI.enterpriseParamsLib.customDocNo.update,
          delete: csAPI.enterpriseParamsLib.customDocNo.delete,
          selectAllPaged: csAPI.enterpriseParamsLib.customDocNo.selectAllPaged
        },
        toolbarEventMap: {
          'add': this.rowAdd,
          'edit': this.rowEdit,
          'delete': this.handleDelete
        },
        // 查询条件行数
        searchLines: 1,
        cmbSource: {
          tradeModeData: []
        },
        enterpriseParamsLib: enterpriseParamsLib,
        gridConfig: {
          exportTitle: '自定义单据编号'
        }
      }
    },
    created: function () {
      let me = this
      me.pcodeList(me.pcode.trade).then(res => {
        let data = JSON.parse(JSON.stringify(res || {}))
        let allItems = data.filter(item => {
          return item.value === '0000'
        })
        if (Array.isArray(allItems) && allItems.length === 0) {
          data.splice(0, 0, {value: '0000', label: '不限'})
        }
        me.$set(me.cmbSource, 'tradeModeData', data)
      }).catch(() => {
        me.$set(me.cmbSource, 'tradeModeData', [])
      })
    },
    mounted: function () {
      this.gridConfig.gridColumns = getColumnsByConfig(this.totalColumns, columnsConfig)
      this.gridConfig.exportColumns = getExcelColumnsByConfig(this.totalColumns, excelColumnsConfig)
      this.loadFunctions().then()
    },
    methods: {
      rowAdd() {
        this.addEmptyRow({
          moduleName: '',
          tradeMode: '',
          fixedPrefix: '',
          dateFmt: '',
          seqNoDigits: null
        })
      },
      rowEdit() {
        if (this.checkRowSelected('编辑', true)) {
          let theRow = this.gridConfig.selectRows[0]
          let index = -1
          let theInd = -1
          for (let row of this.gridConfig.data) {
            theInd++
            if (row.sid === theRow.sid) {
              index = theInd
            }
          }
          if (index > -1) {
            this.setRowEdit(index)
          } else {
            console.error('当前选中的数据存在问题!')
          }
        }
      },
      handleDelete() {
        let me = this,
          newRows = me.gridConfig.selectRows.filter(item => {
            return isNullOrEmpty(item.sid)
          })
        if (Array.isArray(newRows) && newRows.length > 0) {
          me.$Message.warning('仅可删除已保存的数据!')
          return
        }
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
