
import { baseColumnsShow, baseColumnsExport, baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

const commColumns = [
  'paramsCode'
  , 'customParamCode'
  , 'note'
  , 'userName'
  , 'insertTime'
]

const columnsConfig = [
  ...baseColumnsShow,
  ...commColumns
]

const excelColumnsConfig = [
  ...baseColumnsExport,
  ...commColumns
]

const columns = {
  mixins: [baseEditColumns],
  data() {
    let me = this,
      baseFields = me.getDefaultColumns()
    return {
      totalColumns: [
        ...baseFields,
        {
          width: 220,
          align: 'center',
          title: '货物属性',
          key: 'paramsCode',
          render: (h, params) => {
            return me.inputRender(h, params, 30)
          }
        },
        {
          width: 200,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          title: '货物属性名称',
          key: 'customParamCode',
          render: (h, params) => {
            return me.inputRender(h, params, 30)
          }
        },
        {
          key: 'note',
          title: '备注',
          minWidth: 150,
          tooltip: true,
          ellipsis: true,
          align: 'center',
          render: (h, params) => {
            return me.inputRender(h, params)
          }
        },
        {
          width: 120,
          tooltip: true,
          ellipsis: true,
          title: '录入人',
          align: 'center',
          key: 'userName'
        },
        {
          width: 100,
          align: 'center',
          title: '录入日期',
          key: 'insertTime',
          render: (h, params) => {
            return me.dateTimeShowRender(h, params)
          }
        }
      ]
    }
  }
}

export {
  columnsConfig,
  excelColumnsConfig,
  columns
}
