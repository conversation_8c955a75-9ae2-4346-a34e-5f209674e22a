<template>
  <section>
    <XdoForm ref="formInline" class="dc-form" :model="searchParam" label-position="right" :label-width="100" inline>
      <XdoFormItem prop="cabinetType" label="车柜类型">
        <XdoIInput type="text" v-model="searchParam.cabinetType" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="cabinetName" label="车柜类型名称">
        <XdoIInput type="text" v-model="searchParam.cabinetName" clearable></XdoIInput>
      </XdoFormItem>
      <XdoFormItem prop="cabinetAttribute" label="国内/国际">
        <xdo-select v-model="searchParam.cabinetAttribute" :options="cmbSource.cabinetAttribute"
                    :optionLabelRender="pcodeRender"></xdo-select>
      </XdoFormItem>
    </XdoForm>
  </section>
</template>

<script>
  export default {
    name: 'paramVehicleUsageSearch',
    data() {
      return {
        searchParam: {
          cabinetType: '',
          cabinetName: '',
          cabinetAttribute: ''
        },
        cmbSource: {
          cabinetAttribute: [{
            value: '1', label: '国内段'
          }, {
            value: '2', label: '国际段'
          }]
        }
      }
    }
  }
</script>

<style scoped>
</style>
