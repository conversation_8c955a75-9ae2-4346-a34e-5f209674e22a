<template>
  <section>
    <div v-show="showList">
      <XdoCard :bordered="false">
        <div>
          <XdoBreadCrumb show-icon>
            <XdoButton type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</XdoButton>
            <XdoButton type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</XdoButton>
          </XdoBreadCrumb>
        </div>
        <div>
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <paramPortSearch ref="headSearch"></paramPortSearch>
          </div>
        </div>
      </XdoCard>
      <div class="action">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <XdoCard :bordered="false">
        <XdoTable class="dc-table" ref="table" :columns="grdConfig.gridColumns" :height="dynamicHeight"
                  :data="gridConfig.data" stripe border @on-selection-change="handleSelectionChange"></XdoTable>
        <div ref="area_page">
          <XdoPage class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                   :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                   @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </XdoCard>
    </div>
  </section>
</template>

<script>
  import { paramList } from '../js/paramList'
  import paramPortSearch from './param-port-search'
  import { baseEditColumns } from '@/view/cs-interim-verification/comm/baseColumns'

  export default {
    name: 'paramPortList',
    mixins: [paramList, baseEditColumns],
    components: {
      paramPortSearch
    },
    data() {
      let me = this,
        baseFields = me.getDefaultColumns()
      return {
        paramsType: 'PORT',
        cmbSource: {
          note: [],
          customParamCode: []
        },
        grdConfig: {
          gridColumns: [...baseFields, {
            width: 220,
            align: 'center',
            title: '企业港口',
            key: 'paramsCode',
            render: (h, params) => {
              return me.inputRender(h, params, 50)
            }
          }, {
            width: 238,
            tooltip: true,
            ellipsis: true,
            align: 'center',
            title: '海关港口',
            key: 'customParamCode',
            render: (h, params) => {
              return me.cmbRender(h, params, me.cmbSource.customParamCode)
            }
          }, {
            width: 220,
            key: 'note',
            tooltip: true,
            ellipsis: true,
            align: 'center',
            title: '海关国别',
            render: (h, params) => {
              return me.cmbRender(h, params, me.cmbSource.note)
            }
          }, {
            width: 160,
            tooltip: true,
            ellipsis: true,
            align: 'center',
            key: 'userName',
            title: '录入人'
          }, {
            width: 88,
            align: 'center',
            title: '录入日期',
            key: 'insertTime',
            render: (h, params) => {
              return me.dateTimeShowRender(h, params)
            }
          }],
          exportColumns: [{
            value: '企业港口',
            key: 'paramsCode'
          }, {
            value: '海关港口',
            key: 'customParamCode'
          }, {
            value: '海关国别',
            key: 'note'
          }, {
            value: '录入人',
            key: 'userName'
          }, {
            value: '录入日期',
            key: 'insertTime'
          }],
          exportTitle: '港口'
        }
      }
    },
    created() {
      let me = this
      me.pcodeList(me.pcode.country_outdated).then(res => {
        me.$set(me.cmbSource, 'note', res)
      }).catch(() => {
        me.$set(me.cmbSource, 'note', [])
      })
      me.pcodeList('PORT_LIN').then(res => {
        me.$set(me.cmbSource, 'customParamCode', res)
      }).catch(() => {
        me.$set(me.cmbSource, 'customParamCode', [])
      })
    },
    methods: {
      getSearchParams() {
        let me = this
        return Object.assign({}, (me.$refs.headSearch ? me.$refs.headSearch.searchParam : {}), {
          inType: '1',
          paramsType: me.paramsType
        })
      },
      handleExport() {
        let me = this
        me.gridConfig.exportColumns = me.grdConfig.exportColumns
        me.gridConfig.exportTitle='港口'
        me.doExport(me.ajaxUrl.exportUrl, me.actions.findIndex(it => it.command === 'export'))
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
