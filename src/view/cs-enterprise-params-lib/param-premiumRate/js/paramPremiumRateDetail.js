import { editStatus } from '@/view/cs-common/constant'
import { commEdit } from '@/view/cs-interim-verification/comm/commEdit'

export const detailJS = {
  mixins: [commEdit],
  data() {
    let btnComm = {
      needed: true,
      loading: false,
      type: 'primary',
      disabled: false
    }
    return {
      cmbSource: {
        status: [{
          value: '0', label: '停用'
        }, {
          value: '1', label: '启用'
        }]
      },
      buttons: [
        { ...btnComm, label: '保存', icon: 'dc-btn-save', click: this.handleSave },
        { ...btnComm, label: '保存返回', icon: 'dc-btn-save', click: this.handleSaveClose },
        { ...btnComm, label: '返回', icon: 'dc-btn-cancel', click: this.handleBack }
      ]
    }
  },
  watch: {
    'editConfig.editStatus': {
      immediate: true,
      handler: function(val) {
        this.buttons[0].needed = val !== editStatus.SHOW
        this.buttons[1].needed = val !== editStatus.SHOW
      }
    }
  },
  /**
   * 方法
   */
  methods: {
    /**
     * 设置保存按钮加载样式
     * param loading
     */
    setBtnSaveLoading(loading) {
      this.buttons[0].loading = loading
    },
    /**
     * 保存
     */
    handleSave() {
      let me = this
      me.doSave(res => {
        me.refreshIncomingData(false, editStatus.EDIT, res.data.data)
      })
    },
    /**
     * 保存返回
     */
    handleSaveClose() {
      let me = this
      me.doSave(() => {
        me.handleBack()
      })
    },
    /**
     * 获取默认值
     * returns
     */
    getDefaultData() {
      return {
        year: '',
        premiumRate: '',
        status: '1'
      }
    }
  }
}
