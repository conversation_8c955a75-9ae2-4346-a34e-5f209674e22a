<template>
  <section class="xdo-enter-root" v-focus>
    <XdoCard :bordered="false" title="" class="ieLogisticsTrackingCard">
      <XdoForm ref="dataForm" class="dc-form xdo-enter-form" :model="frmData" :rules="rulesHeader"
               label-position="right" :label-width="100">
        <XdoFormItem prop="year" label="年度">
          <XdoDatePicker type="year" placeholder="请选择日期" :value="frmData.year" transfer @on-change="frmData.year=$event"></XdoDatePicker>
<!--          <XdoDatePicker type="year" v-model="frmData.year"></XdoDatePicker>-->
        </XdoFormItem>
        <XdoFormItem prop="premiumRate" label="保费率">
          <xdo-input type="text" v-model="frmData.premiumRate" decimal
                     int-length="13" precision="5"></xdo-input>
        </XdoFormItem>
        <XdoFormItem prop="status" label="启用状态">
          <xdo-select v-model="frmData.status" :options="this.cmbSource.status"
                      :optionLabelRender="pcodeRender"></xdo-select>
        </XdoFormItem>
      </XdoForm>
    </XdoCard>
    <div class="xdo-enter-action action" style="text-align: center; margin-top: 10px;">
      <template v-for="item in buttons">
        <Button v-if="item.needed" :type="item.type" :disabled="item.disabled" :loading="item.loading"
                @click="item.click" :icon="item.icon" :key="item.icon">{{ item.label }}
        </Button>&nbsp;
      </template>
    </div>
  </section>
</template>

<script>
  import { csAPI } from '@/api'
  import { detailJS } from './js/paramPremiumRateDetail'

  export default {
    name: 'expenseAccountSettingEdit',
    mixins: [detailJS],
    data() {
      return {
        formName: 'dataForm',
        cmbSource: {
          status: [{
            value: '0', label: '停用'
          }, {
            value: '1', label: '启用'
          }]
        },
        ajaxUrl: {
          insert: csAPI.enterpriseParamsLib.premiumRate.insert,
          update: csAPI.enterpriseParamsLib.premiumRate.update
        },
        rulesHeader: {
          year: [{ required: true, message: '不能为空', trigger: 'blur' }],
          premiumRate: [{ required: true, message: '不能为空', trigger: 'blur', type: 'number' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }]
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ieLogisticsTrackingCard .ivu-card-head {
    padding: 5px 10px !important;
  }

  .ieLogisticsTrackingCard .ivu-card-body {
    padding: 8px 8px 2px 8px;
  }

  .dc-form-3 {
    grid-template-columns: repeat(3, minmax(100px, 1fr));
  }
</style>
