<template>
  <section>
    <div v-show="showList">
      <Card :bordered="false">
        <div ref="area_head">
          <XdoBreadCrumb show-icon>
            <Button type="primary" class="dc-margin-right" @click="handleSearchSubmit">查询</Button>
            <Button type="warning" class="dc-margin-right" @click="handleShowSearch">查询条件</Button>
          </XdoBreadCrumb>
        </div>
        <div ref="area_search">
          <div v-show="showSearch">
            <div class="separateLine"></div>
            <biPremiumRateSearch ref="headSearch"></biPremiumRateSearch>
          </div>
        </div>
      </Card>
      <div class="action" ref="area_actions">
        <xdo-toolbar @click="handleToolbarClick" :action-source="actions"></xdo-toolbar>
      </div>
      <Card :bordered="false">
        <xdo-ag-grid ref="ref_agGrid" :checkboxSelection="checkboxSelection" rowSelection="multiple"
                     :columns="gridConfig.gridColumns" :data="gridConfig.data" :height="dynamicHeight"
                     @selectionChanged="handleSelectionChanged">
        </xdo-ag-grid>
        <div ref="area_page">
          <Page class="dc-page" show-total show-sizer :page-size-opts="pageSizeOpts"
                :current="pageParam.page" :page-size="pageParam.limit" :total="pageParam.dataTotal"
                @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
        </div>
      </Card>
    </div>
    <paramPremiumRateDetail v-if="!showList" :editConfig="editConfig" @onEditBack="editBack">></paramPremiumRateDetail>
  </section>
</template>

<script>
  import pms from '@/libs/pms'
  import { csAPI } from '@/api'
  import biPremiumRateSearch from './param-premiumRate-search'
  import { commList } from '@/view/cs-interim-verification/comm/commList'
  import { columns } from './js/paramPremiumRateColumns'
  import paramPremiumRateDetail from './param-premiumRate-detail'
  import { commColumnsCustom } from '@/view/cs-interim-verification/comm/commColumnsCustom'

  export default {
    name: 'biPremiumRate',
    moduleName: '保费率',
    mixins: [commList, pms, columns, commColumnsCustom],
    components: {
      biPremiumRateSearch,
      paramPremiumRateDetail
    },
    data() {
      return {
        // 查询条件行数
        searchLines: 1,
        toolbarEventMap: {
          'add': this.handleAdd,
          'edit': this.handleEdit,
          'delete': this.handleDelete,
          'enable': this.handleEnable,
          'disable': this.handleDisable
        },
        ajaxUrl: {
          delete: csAPI.enterpriseParamsLib.premiumRate.delete,
          selectAllPaged: csAPI.enterpriseParamsLib.premiumRate.selectAllPaged,
          changeStatus: csAPI.enterpriseParamsLib.premiumRate.changeStatus
        },
        cmbSource: {
          status: [{
            value: '0', label: '停用'
          }, {
            value: '1', label: '启用'
          }]
        }
      }
    },
    methods: {
      handleDelete() {
        let me = this
        me.doDelete(me.ajaxUrl.delete, me.actions.findIndex(it => it.command === 'delete'))
      },
      handleEnable() {
        let me = this
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.error('请至少选择一条数据启用！')
          return
        } else {
          const sids = me.gridConfig.selectRows.map(item => {
            return item.sid
          })
          this.$http.post(this.ajaxUrl.changeStatus + '/' + sids + '/1')
            .then(() => {
              me.$Message.info('启用成功！')
              me.handleSearchSubmit()
            }, () => {
            })
        }
      },
      handleDisable() {
        let me = this
        if (me.gridConfig.selectRows.length === 0) {
          me.$Message.error('请至少选择一条数据停用！')
          return
        } else {
          const sids = me.gridConfig.selectRows.map(item => {
            return item.sid
          })
          this.$http.post(this.ajaxUrl.changeStatus + '/' + sids + '/0')
            .then(() => {
              me.$Message.info('停用成功！')
              me.handleSearchSubmit()
            }, () => {
            })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .ivu-form-item {
    margin-bottom: 5px;
  }

  .separateLine {
    height: 10px;
    border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
  }
</style>
